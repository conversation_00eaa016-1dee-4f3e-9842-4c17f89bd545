import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      userId: string
      name?: string | null
      email?: string | null
      image?: string | null
      role: string
      isMediator?: boolean
    }
  }

  interface User {
    id: string
    userId?: string
    email: string
    name: string
    role: string
    isMediator?: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string
    userId?: string
    role?: string
    isMediator?: boolean
  }
}
