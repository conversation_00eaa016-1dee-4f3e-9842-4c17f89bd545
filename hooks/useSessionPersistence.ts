'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'

interface SessionPersistenceState {
  isLoading: boolean
  isAuthenticated: boolean
  session: any
  error: string | null
}

export function useSessionPersistence() {
  const { data: session, status, update } = useSession()
  const [state, setState] = useState<SessionPersistenceState>({
    isLoading: true,
    isAuthenticated: false,
    session: null,
    error: null
  })

  useEffect(() => {
    const handleSessionCheck = async () => {
      try {
        // 检查会话状态
        if (status === 'loading') {
          setState(prev => ({ ...prev, isLoading: true }))
          return
        }

        if (status === 'authenticated' && session) {
          setState({
            isLoading: false,
            isAuthenticated: true,
            session,
            error: null
          })
          
          // 设置定期刷新会话
          const refreshInterval = setInterval(async () => {
            try {
              await update()
            } catch (error) {
              console.error('会话刷新失败:', error)
            }
          }, 5 * 60 * 1000) // 每5分钟刷新一次
          
          return () => clearInterval(refreshInterval)
        } else {
          setState({
            isLoading: false,
            isAuthenticated: false,
            session: null,
            error: status === 'unauthenticated' ? null : '会话验证失败'
          })
        }
      } catch (error) {
        console.error('会话检查失败:', error)
        setState({
          isLoading: false,
          isAuthenticated: false,
          session: null,
          error: '会话检查失败'
        })
      }
    }

    handleSessionCheck()
  }, [session, status, update])

  // 监听页面可见性变化，重新验证会话
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && state.isAuthenticated) {
        try {
          await update()
        } catch (error) {
          console.error('页面重新聚焦时会话刷新失败:', error)
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [state.isAuthenticated, update])

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'next-auth.session-token' || e.key?.startsWith('next-auth.')) {
        // 会话 token 发生变化，重新验证
        update()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [update])

  return {
    ...state,
    refreshSession: update
  }
}

// 会话持久性检查工具函数
export function checkSessionPersistence() {
  return new Promise((resolve) => {
    // 检查 cookie 中是否存在会话 token
    const cookies = document.cookie.split(';')
    const sessionCookie = cookies.find(cookie => 
      cookie.trim().startsWith('next-auth.session-token=')
    )
    
    if (sessionCookie) {
      // 验证会话是否有效
      fetch('/api/auth/session')
        .then(res => res.json())
        .then(session => {
          resolve({
            hasSessionCookie: true,
            isValidSession: !!session?.user,
            session
          })
        })
        .catch(() => {
          resolve({
            hasSessionCookie: true,
            isValidSession: false,
            session: null
          })
        })
    } else {
      resolve({
        hasSessionCookie: false,
        isValidSession: false,
        session: null
      })
    }
  })
}

// 强制刷新会话的工具函数
export async function forceRefreshSession() {
  try {
    const response = await fetch('/api/auth/session', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (response.ok) {
      const session = await response.json()
      return session
    } else {
      throw new Error('会话刷新失败')
    }
  } catch (error) {
    console.error('强制刷新会话失败:', error)
    throw error
  }
}
