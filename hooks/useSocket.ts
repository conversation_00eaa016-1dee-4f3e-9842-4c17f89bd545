import { useEffect, useState, useCallback, useRef } from 'react'
import { useSession } from 'next-auth/react'
import socketManager from '@/lib/socketClient'

interface Message {
  id: string
  content: string
  messageType: string
  status: string
  senderId: string
  receiverId: string
  orderId: string
  createdAt: string
  sender: {
    id: string
    name: string
    email: string
  }
  receiver: {
    id: string
    name: string
    email: string
  }
}

interface OnlineUser {
  userId: string
  lastActive: Date
}

interface TypingUser {
  userId: string
  user: {
    id: string
    name: string
    email: string
  }
  orderId: string
}

interface SystemNotification {
  type: string
  title: string
  message: string
  data?: any
}

export const useSocket = () => {
  const { data: session } = useSession()
  const [isConnected, setIsConnected] = useState(false)
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([])
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [notifications, setNotifications] = useState<SystemNotification[]>([])
  
  const messageCallbacks = useRef<((message: Message) => void)[]>([])
  const notificationCallbacks = useRef<((notification: SystemNotification) => void)[]>([])
  const typingCallbacks = useRef<((typing: TypingUser[]) => void)[]>([])

  // 连接WebSocket
  const connect = useCallback(async () => {
    if (!session?.user?.id || isConnected) return

    try {
      await socketManager.connect()
      setIsConnected(true)
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error)
      setIsConnected(false)
    }
  }, [session, isConnected])

  // 断开连接
  const disconnect = useCallback(() => {
    socketManager.disconnect()
    setIsConnected(false)
  }, [])

  // 发送消息
  const sendMessage = useCallback(async (orderId: string, content: string, messageType: string = 'TEXT') => {
    try {
      const message = await socketManager.sendMessage(orderId, content, messageType)
      return message
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }, [])

  // 打字状态管理
  const startTyping = useCallback((orderId: string) => {
    socketManager.startTyping(orderId)
  }, [])

  const stopTyping = useCallback((orderId: string) => {
    socketManager.stopTyping(orderId)
  }, [])

  // 标记消息为已读
  const markMessagesAsRead = useCallback((orderId: string) => {
    socketManager.markMessagesAsRead(orderId)
  }, [])

  // 获取在线用户
  const getOnlineUsers = useCallback(() => {
    socketManager.getOnlineUsers()
  }, [])

  // 添加消息监听器
  const onMessage = useCallback((callback: (message: Message) => void) => {
    messageCallbacks.current.push(callback)
    
    return () => {
      const index = messageCallbacks.current.indexOf(callback)
      if (index > -1) {
        messageCallbacks.current.splice(index, 1)
      }
    }
  }, [])

  // 添加通知监听器
  const onNotification = useCallback((callback: (notification: SystemNotification) => void) => {
    notificationCallbacks.current.push(callback)
    
    return () => {
      const index = notificationCallbacks.current.indexOf(callback)
      if (index > -1) {
        notificationCallbacks.current.splice(index, 1)
      }
    }
  }, [])

  // 添加打字状态监听器
  const onTyping = useCallback((callback: (typing: TypingUser[]) => void) => {
    typingCallbacks.current.push(callback)
    
    return () => {
      const index = typingCallbacks.current.indexOf(callback)
      if (index > -1) {
        typingCallbacks.current.splice(index, 1)
      }
    }
  }, [])

  // 检查用户是否在线
  const isUserOnline = useCallback((userId: string) => {
    return onlineUsers.some(user => user.userId === userId)
  }, [onlineUsers])

  // 获取用户最后活跃时间
  const getUserLastActive = useCallback((userId: string) => {
    const user = onlineUsers.find(u => u.userId === userId)
    return user?.lastActive || null
  }, [onlineUsers])

  useEffect(() => {
    if (session?.user?.id) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [session, connect, disconnect])

  useEffect(() => {
    if (!isConnected) return

    const socket = socketManager.getSocket()
    if (!socket) return

    // 监听连接状态
    const handleConnect = () => setIsConnected(true)
    const handleDisconnect = () => setIsConnected(false)

    // 监听新消息
    const handleNewMessage = (message: Message) => {
      setMessages(prev => [...prev, message])
      messageCallbacks.current.forEach(callback => callback(message))
    }

    // 监听在线用户更新
    const handleOnlineUsers = (users: OnlineUser[]) => {
      setOnlineUsers(users)
    }

    const handleUserOnline = (data: { userId: string, user: any }) => {
      setOnlineUsers(prev => [
        ...prev.filter(u => u.userId !== data.userId),
        { userId: data.userId, lastActive: new Date() }
      ])
    }

    const handleUserOffline = (data: { userId: string, lastActive: Date }) => {
      setOnlineUsers(prev => prev.filter(u => u.userId !== data.userId))
    }

    // 监听打字状态
    const handleUserTyping = (data: TypingUser) => {
      setTypingUsers(prev => {
        const filtered = prev.filter(u => u.userId !== data.userId || u.orderId !== data.orderId)
        return [...filtered, data]
      })
      typingCallbacks.current.forEach(callback => callback(typingUsers))
    }

    const handleUserStopTyping = (data: { userId: string, orderId: string }) => {
      setTypingUsers(prev => {
        const filtered = prev.filter(u => !(u.userId === data.userId && u.orderId === data.orderId))
        typingCallbacks.current.forEach(callback => callback(filtered))
        return filtered
      })
    }

    // 监听系统通知
    const handleSystemNotification = (notification: SystemNotification) => {
      setNotifications(prev => [...prev, notification])
      notificationCallbacks.current.forEach(callback => callback(notification))
    }

    // 绑定事件监听器
    socket.on('connect', handleConnect)
    socket.on('disconnect', handleDisconnect)
    socket.on('new_message', handleNewMessage)
    socket.on('online_users_list', handleOnlineUsers)
    socket.on('user_online', handleUserOnline)
    socket.on('user_offline', handleUserOffline)
    socket.on('user_typing', handleUserTyping)
    socket.on('user_stop_typing', handleUserStopTyping)
    socket.on('system_notification', handleSystemNotification)

    return () => {
      socket.off('connect', handleConnect)
      socket.off('disconnect', handleDisconnect)
      socket.off('new_message', handleNewMessage)
      socket.off('online_users_list', handleOnlineUsers)
      socket.off('user_online', handleUserOnline)
      socket.off('user_offline', handleUserOffline)
      socket.off('user_typing', handleUserTyping)
      socket.off('user_stop_typing', handleUserStopTyping)
      socket.off('system_notification', handleSystemNotification)
    }
  }, [isConnected, typingUsers])

  return {
    isConnected,
    onlineUsers,
    typingUsers,
    messages,
    notifications,
    connect,
    disconnect,
    sendMessage,
    startTyping,
    stopTyping,
    markMessagesAsRead,
    getOnlineUsers,
    onMessage,
    onNotification,
    onTyping,
    isUserOnline,
    getUserLastActive
  }
}
