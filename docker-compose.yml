services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: bitmarket-mysql
    restart: unless-stopped
    environment:
      # 数据库配置
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-bitmarket_root_2024}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-bitmarket}
      MYSQL_USER: ${MYSQL_USER:-bitmarket_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-bitmarket_pass_2024}
      # 字符集配置
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      # 数据持久化
      - mysql_data:/var/lib/mysql
      # 初始化脚本
      - ./mysql-init:/docker-entrypoint-initdb.d
      # MySQL 配置文件 (暂时禁用以排除配置问题)
      # - ./mysql-config/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-bitmarket_root_2024}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - bitmarket-network

  # Redis 缓存服务 (可选)
  redis:
    image: redis:7-alpine
    container_name: bitmarket-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-bitmarket_redis_2024}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - bitmarket-network
    profiles:
      - redis

volumes:
  mysql_data:
    driver: local
    name: bitmarket_mysql_data
  redis_data:
    driver: local
    name: bitmarket_redis_data

networks:
  bitmarket-network:
    driver: bridge
    name: bitmarket-network
