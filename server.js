// 加载环境变量
require('dotenv').config()

const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const { Server } = require('socket.io')
const jwt = require('jsonwebtoken')
const { PrismaClient } = require('@prisma/client')
const cluster = require('cluster')
const os = require('os')

const dev = process.env.NODE_ENV !== 'production'

// 集群配置（生产环境）
const numCPUs = os.cpus().length
const useCluster = !dev && process.env.USE_CLUSTER === 'true'

if (useCluster && cluster.isMaster) {
  console.log(`Master ${process.pid} is running`)

  // Fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork()
  }

  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`)
    cluster.fork()
  })
} else {
  // Worker process or development mode
  const app = next({ dev })
  const handle = app.getRequestHandler()

  // 优化的Prisma客户端配置
  const prisma = new PrismaClient({
    log: dev ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })

  // 在线用户管理（使用Map提升性能）
  const onlineUsers = new Map()

  // 打字状态管理
  const typingUsers = new Map()

  // 连接池管理
  const connectionPool = new Map()

  // 性能监控
  const performanceMetrics = {
    connections: 0,
    messages: 0,
    errors: 0,
    startTime: Date.now(),
  }

app.prepare().then(() => {
  const server = createServer((req, res) => {
    const parsedUrl = parse(req.url, true)
    handle(req, res, parsedUrl)
  })

  // 优化的Socket.IO配置
  const io = new Server(server, {
    cors: {
      origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true,
    },
    path: '/api/socket',
    // 性能优化配置
    transports: ['websocket', 'polling'],
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 10000,
    maxHttpBufferSize: 1e6, // 1MB
    // 连接限制
    connectionStateRecovery: {
      maxDisconnectionDuration: 2 * 60 * 1000, // 2分钟
      skipMiddlewares: true,
    },
  })

  // 如果配置了Redis，则使用Redis适配器
  if (process.env.REDIS_URL) {
    try {
      const { createAdapter } = require('@socket.io/redis-adapter')
      const { createClient } = require('redis')

      const pubClient = createClient({ url: process.env.REDIS_URL })
      const subClient = pubClient.duplicate()

      Promise.all([
        pubClient.connect(),
        subClient.connect()
      ]).then(() => {
        io.adapter(createAdapter(pubClient, subClient))
        console.log('✅ Redis adapter connected successfully')
      }).catch(err => {
        console.warn('⚠️ Redis adapter failed to connect, using default adapter:', err.message)
      })
    } catch (err) {
      console.warn('⚠️ Redis adapter not available, using default adapter:', err.message)
    }
  }

  // 身份验证中间件
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token
      if (!token) {
        return next(new Error('Authentication error'))
      }

      // 验证JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret')
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          name: true,
          email: true
        }
      })

      if (!user) {
        return next(new Error('User not found'))
      }

      socket.userId = user.id
      socket.user = user
      next()
    } catch (error) {
      console.error('Socket authentication error:', error)
      next(new Error('Authentication error'))
    }
  })

  io.on('connection', (socket) => {
    console.log(`User ${socket.user.name} connected: ${socket.id}`)

    // 更新性能指标
    performanceMetrics.connections++

    // 用户上线（优化内存使用）
    const userInfo = {
      socketId: socket.id,
      userId: socket.userId,
      lastActive: Date.now(), // 使用时间戳而不是Date对象
      rooms: new Set() // 跟踪用户加入的房间
    }

    onlineUsers.set(socket.userId, userInfo)
    connectionPool.set(socket.id, socket.userId)

    // 限制广播频率（防止广播风暴）
    if (onlineUsers.size % 10 === 0) { // 每10个连接广播一次
      socket.broadcast.emit('user_online', {
        userId: socket.userId,
        user: {
          id: socket.user.id,
          name: socket.user.name
        }
      })
    }

    // 房间加入逻辑（暂时不使用缓存）
    socket.on('join_order_rooms', async () => {
      try {
        const orders = await prisma.order.findMany({
          where: {
            OR: [
              { buyerId: socket.userId },
              { sellerId: socket.userId }
            ]
          },
          select: { id: true }
        })

        // 批量加入房间并跟踪
        const roomsToJoin = orders.map(order => `order_${order.id}`)
        roomsToJoin.forEach(room => {
          socket.join(room)
          userInfo.rooms.add(room)
        })

        console.log(`User ${socket.userId} joined ${roomsToJoin.length} rooms`)
      } catch (error) {
        console.error('Error joining order rooms:', error)
        socket.emit('error', { message: '加入房间失败' })
      }
    })

    // 优化的消息发送逻辑
    socket.on('send_message', async (data) => {
      const messageStart = performance.now()

      try {
        const { orderId, content, messageType = 'TEXT' } = data

        // 输入验证
        if (!orderId || !content || content.trim().length === 0) {
          socket.emit('error', { message: '消息内容不能为空' })
          return
        }

        if (content.length > 1000) {
          socket.emit('error', { message: '消息内容过长' })
          return
        }

        // 验证订单权限
        const order = await prisma.order.findFirst({
          where: {
            id: orderId,
            OR: [
              { buyerId: socket.userId },
              { sellerId: socket.userId }
            ]
          },
          select: { id: true, buyerId: true, sellerId: true }
        })

        if (!order) {
          socket.emit('error', { message: '无权限发送消息到此订单' })
          return
        }

        // 确定接收者
        const receiverId = order.buyerId === socket.userId ? order.sellerId : order.buyerId

        // 创建消息
        const message = await prisma.message.create({
          data: {
            orderId,
            content: content.trim(),
            messageType,
            senderId: socket.userId,
            receiverId,
            status: 'SENT'
          },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            receiver: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })

        // 发送给订单房间的所有用户
        io.to(`order_${orderId}`).emit('new_message', message)

        // 更新消息状态为已送达（如果接收者在线）
        const receiverOnline = onlineUsers.get(receiverId)
        if (receiverOnline) {
          await prisma.message.update({
            where: { id: message.id },
            data: { status: 'DELIVERED' }
          })

          // 通知消息状态更新
          io.to(`order_${orderId}`).emit('message_status_update', {
            messageId: message.id,
            status: 'DELIVERED'
          })
        }

      } catch (error) {
        console.error('Error sending message:', error)
        socket.emit('error', { message: '发送消息失败' })
      }
    })

    // 其他事件处理...
    // 打字状态、标记已读等

    // 用户断开连接
    socket.on('disconnect', () => {
      console.log(`User ${socket.user.name} disconnected: ${socket.id}`)
      
      // 延迟移除用户（防止短暂断线）
      setTimeout(() => {
        const currentUser = onlineUsers.get(socket.userId)
        if (currentUser && currentUser.socketId === socket.id) {
          onlineUsers.delete(socket.userId)
          
          // 广播用户离线状态
          socket.broadcast.emit('user_offline', {
            userId: socket.userId,
            lastActive: new Date()
          })
        }
      }, 5000) // 5秒后移除
    })
  })

  const PORT = process.env.PORT || 3000
  server.listen(PORT, (err) => {
    if (err) throw err
    console.log(`> Ready on http://localhost:${PORT}`)
  })
})
}
