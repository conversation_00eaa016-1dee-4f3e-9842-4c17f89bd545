-- BitMarket 商品分类和成色系统增强迁移
-- 扩展商品分类选项和成色系统

-- 1. 更新现有商品的分类，确保数据一致性
UPDATE `Product` SET `category` = 'ELECTRONICS' WHERE `category` = 'GENERAL' AND (`title` LIKE '%手机%' OR `title` LIKE '%电脑%' OR `title` LIKE '%数码%' OR `title` LIKE '%电子%');
UPDATE `Product` SET `category` = 'CLOTHING' WHERE `category` = 'GENERAL' AND (`title` LIKE '%衣服%' OR `title` LIKE '%鞋子%' OR `title` LIKE '%包包%' OR `title` LIKE '%配饰%');
UPDATE `Product` SET `category` = 'BOOKS' WHERE `category` = 'GENERAL' AND (`title` LIKE '%书%' OR `title` LIKE '%文具%' OR `title` LIKE '%笔记本%');
UPDATE `Product` SET `category` = 'HOME' WHERE `category` = 'GENERAL' AND (`title` LIKE '%家具%' OR `title` LIKE '%家居%' OR `title` LIKE '%厨具%');
UPDATE `Product` SET `category` = 'SPORTS' WHERE `category` = 'GENERAL' AND (`title` LIKE '%运动%' OR `title` LIKE '%健身%' OR `title` LIKE '%户外%');

-- 2. 为现有商品设置合理的默认成色
-- 保持现有的 condition 字段值不变，因为已经有合理的默认值

-- 3. 添加索引以优化查询性能（跳过，因为索引可能已存在）
-- 索引将通过 Prisma schema 自动管理

-- 4. 验证数据完整性
-- 检查是否有无效的分类值
SELECT COUNT(*) as invalid_categories FROM `Product` 
WHERE `category` NOT IN ('GENERAL', 'ELECTRONICS', 'CLOTHING', 'BOOKS', 'HOME', 'SPORTS', 'BEAUTY', 'VIRTUAL', 'AUTOMOTIVE', 'TOYS', 'HEALTH');

-- 检查是否有无效的成色值
SELECT COUNT(*) as invalid_conditions FROM `Product` 
WHERE `condition` NOT IN ('NEW', 'LIKE_NEW', 'GOOD', 'FAIR', 'POOR', 'NOT_APPLICABLE');

-- 5. 添加注释说明
ALTER TABLE `Product` COMMENT = '商品表 - 支持扩展的分类和成色系统';

-- 迁移完成（Prisma 会自动管理迁移记录）
