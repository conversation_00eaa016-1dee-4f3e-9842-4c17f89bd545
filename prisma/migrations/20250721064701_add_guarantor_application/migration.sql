-- CreateTable
CREATE TABLE `GuarantorApplication` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `reason` TEXT NOT NULL,
    `experience` TEXT NULL,
    `creditScoreAtApply` INTEGER NOT NULL,
    `depositBalanceAtApply` DOUBLE NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `reviewedBy` VARCHAR(191) NULL,
    `reviewNotes` TEXT NULL,
    `reviewedAt` DATETIME(3) NULL,
    `metadata` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `GuarantorApplication_userId_status_idx`(`userId`, `status`),
    INDEX `GuarantorApplication_status_createdAt_idx`(`status`, `createdAt`),
    INDEX `GuarantorApplication_reviewedBy_reviewedAt_idx`(`reviewedBy`, `reviewedAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `GuarantorApplication` ADD CONSTRAINT `GuarantorApplication_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GuarantorApplication` ADD CONSTRAINT `GuarantorApplication_reviewedBy_fkey` FOREIGN KEY (`reviewedBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
