/*
  Warnings:

  - A unique constraint covering the columns `[batchId]` on the table `BatchSettlement` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[date]` on the table `FundPoolStats` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `updatedAt` to the `BatchSettlement` table without a default value. This is not possible if the table is not empty.
  - Added the required column `date` to the `FundPoolStats` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `SecurityLog` DROP FOREIGN KEY `SecurityLog_userId_fkey`;

-- AlterTable
ALTER TABLE `BatchSettlement` ADD COLUMN `batchId` VARCHAR(191) NULL,
    ADD COLUMN `completedAt` DATETIME(3) NULL,
    ADD COLUMN `failedAt` DATETIME(3) NULL,
    ADD COLUMN `operatorId` VARCHAR(191) NULL,
    ADD COLUMN `startedAt` DATETIME(3) NULL,
    ADD COLUMN `type` VARCHAR(191) NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NOT NULL;

-- AlterTable
ALTER TABLE `BlockchainTransaction` ADD COLUMN `metadata` JSON NULL,
    ADD COLUMN `type` VARCHAR(191) NOT NULL DEFAULT 'ESCROW_FUNDING',
    ADD COLUMN `verifiedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `EscrowDispute` ADD COLUMN `decisionDetails` JSON NULL,
    ADD COLUMN `finalDecision` VARCHAR(191) NULL,
    ADD COLUMN `votingEndsAt` DATETIME(3) NULL,
    ADD COLUMN `votingStartsAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `FundPoolStats` ADD COLUMN `averageBalance` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `date` DATE NOT NULL,
    ADD COLUMN `newDeposits` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `newWithdrawals` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `totalEarnings` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `totalWithdrawals` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `transactionCount` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `utilizationRate` DOUBLE NOT NULL DEFAULT 0,
    MODIFY `totalDeposits` DOUBLE NOT NULL DEFAULT 0,
    MODIFY `totalFrozen` DOUBLE NOT NULL DEFAULT 0,
    MODIFY `totalAvailable` DOUBLE NOT NULL DEFAULT 0,
    MODIFY `totalWithdrawn` DOUBLE NOT NULL DEFAULT 0,
    MODIFY `activeUsers` INTEGER NOT NULL DEFAULT 0,
    MODIFY `newUsers` INTEGER NOT NULL DEFAULT 0,
    MODIFY `dailyTransactions` INTEGER NOT NULL DEFAULT 0,
    MODIFY `dailyVolume` DOUBLE NOT NULL DEFAULT 0,
    MODIFY `avgTransactionSize` DOUBLE NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `Order` ADD COLUMN `shippedAt` DATETIME(3) NULL,
    ADD COLUMN `shippingProofImages` JSON NULL,
    ADD COLUMN `shippingProofText` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `SecurityLog` ADD COLUMN `details` JSON NULL,
    ADD COLUMN `success` BOOLEAN NOT NULL DEFAULT true,
    MODIFY `userId` VARCHAR(191) NULL,
    MODIFY `description` VARCHAR(191) NULL,
    MODIFY `status` VARCHAR(191) NULL,
    MODIFY `userAgent` TEXT NULL;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `availableBalance` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `balanceVersion` INTEGER NOT NULL DEFAULT 1,
    ADD COLUMN `frozenBalance` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `guaranteeLevel` VARCHAR(191) NOT NULL DEFAULT 'BRONZE',
    ADD COLUMN `guaranteedAmount` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `lastBalanceUpdate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    ADD COLUMN `totalEarnings` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `totalWithdrawals` DOUBLE NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE `BlacklistedWallet` (
    `id` VARCHAR(191) NOT NULL,
    `address` VARCHAR(191) NOT NULL,
    `reason` VARCHAR(191) NOT NULL,
    `addedBy` VARCHAR(191) NOT NULL,
    `addedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `notes` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `BlacklistedWallet_address_key`(`address`),
    INDEX `BlacklistedWallet_address_idx`(`address`),
    INDEX `BlacklistedWallet_isActive_idx`(`isActive`),
    INDEX `BlacklistedWallet_addedBy_idx`(`addedBy`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RiskAssessmentLog` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `riskLevel` VARCHAR(191) NOT NULL,
    `riskScore` DOUBLE NOT NULL,
    `riskFactors` JSON NOT NULL,
    `requiresApproval` BOOLEAN NOT NULL DEFAULT false,
    `requiresKYC` BOOLEAN NOT NULL DEFAULT false,
    `maxAllowedAmount` DOUBLE NULL,
    `recommendations` JSON NOT NULL,
    `transactionType` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `additionalData` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `RiskAssessmentLog_userId_idx`(`userId`),
    INDEX `RiskAssessmentLog_riskLevel_idx`(`riskLevel`),
    INDEX `RiskAssessmentLog_createdAt_idx`(`createdAt`),
    INDEX `RiskAssessmentLog_transactionType_idx`(`transactionType`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GuaranteeTransaction` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `balanceBefore` DOUBLE NOT NULL,
    `balanceAfter` DOUBLE NOT NULL,
    `frozenBefore` DOUBLE NOT NULL,
    `frozenAfter` DOUBLE NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `relatedType` VARCHAR(191) NULL,
    `relatedId` VARCHAR(191) NULL,
    `txHash` VARCHAR(191) NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'COMPLETED',
    `metadata` JSON NULL,
    `operatorId` VARCHAR(191) NULL,
    `batchId` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `GuaranteeTransaction_userId_createdAt_idx`(`userId`, `createdAt`),
    INDEX `GuaranteeTransaction_type_createdAt_idx`(`type`, `createdAt`),
    INDEX `GuaranteeTransaction_relatedType_relatedId_idx`(`relatedType`, `relatedId`),
    INDEX `GuaranteeTransaction_status_idx`(`status`),
    INDEX `GuaranteeTransaction_batchId_idx`(`batchId`),
    INDEX `GuaranteeTransaction_txHash_idx`(`txHash`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GuaranteeLevel` (
    `id` VARCHAR(191) NOT NULL,
    `level` VARCHAR(191) NOT NULL,
    `minBalance` DOUBLE NOT NULL,
    `maxDailyWithdrawal` DOUBLE NOT NULL,
    `withdrawalFeeRate` DOUBLE NOT NULL,
    `tradingFeeDiscount` DOUBLE NOT NULL,
    `priorityLevel` INTEGER NOT NULL DEFAULT 0,
    `benefits` JSON NOT NULL,
    `requirements` JSON NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `GuaranteeLevel_level_key`(`level`),
    INDEX `GuaranteeLevel_level_idx`(`level`),
    INDEX `GuaranteeLevel_minBalance_idx`(`minBalance`),
    INDEX `GuaranteeLevel_isActive_idx`(`isActive`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShippingCompany` (
    `id` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `website` VARCHAR(191) NULL,
    `trackingUrl` VARCHAR(191) NULL,
    `userId` VARCHAR(191) NOT NULL,
    `isGlobal` BOOLEAN NOT NULL DEFAULT false,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `ShippingCompany_userId_idx`(`userId`),
    INDEX `ShippingCompany_code_idx`(`code`),
    INDEX `ShippingCompany_isGlobal_idx`(`isGlobal`),
    INDEX `ShippingCompany_isActive_idx`(`isActive`),
    UNIQUE INDEX `ShippingCompany_code_userId_key`(`code`, `userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShippingIssue` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `sellerId` VARCHAR(191) NOT NULL,
    `issueType` VARCHAR(191) NOT NULL,
    `reason` VARCHAR(191) NOT NULL,
    `estimatedResolutionDate` DATETIME(3) NULL,
    `actualResolutionDate` DATETIME(3) NULL,
    `alternativeSolution` VARCHAR(191) NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'REPORTED',
    `resolution` VARCHAR(191) NULL,
    `reportedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `resolvedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `ShippingIssue_orderId_idx`(`orderId`),
    INDEX `ShippingIssue_sellerId_idx`(`sellerId`),
    INDEX `ShippingIssue_issueType_idx`(`issueType`),
    INDEX `ShippingIssue_status_idx`(`status`),
    INDEX `ShippingIssue_reportedAt_idx`(`reportedAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShippingTemplate` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `shippingCompany` VARCHAR(191) NOT NULL,
    `estimatedDeliveryDays` INTEGER NULL,
    `shippingNotes` VARCHAR(191) NULL,
    `isDefault` BOOLEAN NOT NULL DEFAULT false,
    `userId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `ShippingTemplate_userId_idx`(`userId`),
    INDEX `ShippingTemplate_isDefault_idx`(`isDefault`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShippingSecurityLog` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `action` VARCHAR(191) NOT NULL,
    `details` VARCHAR(191) NULL,
    `ipAddress` VARCHAR(191) NULL,
    `userAgent` VARCHAR(191) NULL,
    `riskLevel` VARCHAR(191) NOT NULL DEFAULT 'LOW',
    `timestamp` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `ShippingSecurityLog_orderId_idx`(`orderId`),
    INDEX `ShippingSecurityLog_userId_idx`(`userId`),
    INDEX `ShippingSecurityLog_action_idx`(`action`),
    INDEX `ShippingSecurityLog_riskLevel_idx`(`riskLevel`),
    INDEX `ShippingSecurityLog_timestamp_idx`(`timestamp`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AfterSalesRequest` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `buyerId` VARCHAR(191) NOT NULL,
    `sellerId` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `reason` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `images` JSON NULL,
    `requestedAmount` DOUBLE NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `sellerResponse` VARCHAR(191) NULL,
    `sellerResponseAt` DATETIME(3) NULL,
    `processedAt` DATETIME(3) NULL,
    `completedAt` DATETIME(3) NULL,
    `refundAmount` DOUBLE NULL,
    `refundMethod` VARCHAR(191) NULL,
    `refundTxHash` VARCHAR(191) NULL,
    `refundedAt` DATETIME(3) NULL,
    `exchangeProductId` VARCHAR(191) NULL,
    `exchangeTrackingNumber` VARCHAR(191) NULL,
    `repairInstructions` VARCHAR(191) NULL,
    `adminNotes` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `AfterSalesRequest_orderId_idx`(`orderId`),
    INDEX `AfterSalesRequest_buyerId_idx`(`buyerId`),
    INDEX `AfterSalesRequest_sellerId_idx`(`sellerId`),
    INDEX `AfterSalesRequest_type_idx`(`type`),
    INDEX `AfterSalesRequest_status_idx`(`status`),
    INDEX `AfterSalesRequest_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AfterSalesMessage` (
    `id` VARCHAR(191) NOT NULL,
    `afterSalesId` VARCHAR(191) NOT NULL,
    `senderId` VARCHAR(191) NOT NULL,
    `content` VARCHAR(191) NOT NULL,
    `images` JSON NULL,
    `messageType` VARCHAR(191) NOT NULL DEFAULT 'TEXT',
    `isRead` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `AfterSalesMessage_afterSalesId_idx`(`afterSalesId`),
    INDEX `AfterSalesMessage_senderId_idx`(`senderId`),
    INDEX `AfterSalesMessage_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `BatchSettlement_batchId_key` ON `BatchSettlement`(`batchId`);

-- CreateIndex
CREATE INDEX `BatchSettlement_type_idx` ON `BatchSettlement`(`type`);

-- CreateIndex
CREATE INDEX `BatchSettlement_batchId_idx` ON `BatchSettlement`(`batchId`);

-- CreateIndex
CREATE INDEX `BlockchainTransaction_type_idx` ON `BlockchainTransaction`(`type`);

-- CreateIndex
CREATE INDEX `BlockchainTransaction_verifiedAt_idx` ON `BlockchainTransaction`(`verifiedAt`);

-- CreateIndex
CREATE UNIQUE INDEX `FundPoolStats_date_key` ON `FundPoolStats`(`date`);

-- CreateIndex
CREATE INDEX `FundPoolStats_date_idx` ON `FundPoolStats`(`date`);

-- AddForeignKey
ALTER TABLE `BatchSettlement` ADD CONSTRAINT `BatchSettlement_operatorId_fkey` FOREIGN KEY (`operatorId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SecurityLog` ADD CONSTRAINT `SecurityLog_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BlacklistedWallet` ADD CONSTRAINT `BlacklistedWallet_addedBy_fkey` FOREIGN KEY (`addedBy`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RiskAssessmentLog` ADD CONSTRAINT `RiskAssessmentLog_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GuaranteeTransaction` ADD CONSTRAINT `GuaranteeTransaction_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GuaranteeTransaction` ADD CONSTRAINT `GuaranteeTransaction_operatorId_fkey` FOREIGN KEY (`operatorId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShippingCompany` ADD CONSTRAINT `ShippingCompany_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShippingIssue` ADD CONSTRAINT `ShippingIssue_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShippingIssue` ADD CONSTRAINT `ShippingIssue_sellerId_fkey` FOREIGN KEY (`sellerId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShippingTemplate` ADD CONSTRAINT `ShippingTemplate_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShippingSecurityLog` ADD CONSTRAINT `ShippingSecurityLog_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShippingSecurityLog` ADD CONSTRAINT `ShippingSecurityLog_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AfterSalesRequest` ADD CONSTRAINT `AfterSalesRequest_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AfterSalesRequest` ADD CONSTRAINT `AfterSalesRequest_buyerId_fkey` FOREIGN KEY (`buyerId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AfterSalesRequest` ADD CONSTRAINT `AfterSalesRequest_sellerId_fkey` FOREIGN KEY (`sellerId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AfterSalesRequest` ADD CONSTRAINT `AfterSalesRequest_exchangeProductId_fkey` FOREIGN KEY (`exchangeProductId`) REFERENCES `Product`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AfterSalesMessage` ADD CONSTRAINT `AfterSalesMessage_afterSalesId_fkey` FOREIGN KEY (`afterSalesId`) REFERENCES `AfterSalesRequest`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AfterSalesMessage` ADD CONSTRAINT `AfterSalesMessage_senderId_fkey` FOREIGN KEY (`senderId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
