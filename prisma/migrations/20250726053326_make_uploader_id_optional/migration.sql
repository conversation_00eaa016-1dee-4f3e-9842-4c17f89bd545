-- DropF<PERSON>ign<PERSON>ey
ALTER TABLE `HelpMediaFile` DROP FOREIGN KEY `HelpMediaFile_uploaderId_fkey`;

-- AlterTable
ALTER TABLE `HelpMediaFile` MODIFY `uploaderId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `Order` ADD COLUMN `disputeReason` VARCHAR(191) NULL,
    ADD COLUMN `disputeReportedAt` DATETIME(3) NULL,
    ADD COLUMN `disputeResolvedAt` DATETIME(3) NULL,
    ADD COLUMN `disputeStatus` VARCHAR(191) NOT NULL DEFAULT 'NONE',
    ADD COLUMN `escrowFee` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `escrowFeeRate` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `escrowNotes` VARCHAR(191) NULL,
    ADD COLUMN `mediatorId` VARCHAR(191) NULL,
    ADD COLUMN `useEscrow` BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `bnbWalletVerified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `bnbWalletVerifiedAt` DATETIME(3) NULL,
    ADD COLUMN `isMediator` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `mediatorDeposit` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `mediatorExperience` VARCHAR(191) NULL,
    ADD COLUMN `mediatorFeeRate` DOUBLE NULL,
    ADD COLUMN `mediatorIntroduction` VARCHAR(191) NULL,
    ADD COLUMN `mediatorLastActiveAt` DATETIME(3) NULL,
    ADD COLUMN `mediatorReputation` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `mediatorStatus` VARCHAR(191) NOT NULL DEFAULT 'INACTIVE',
    ADD COLUMN `mediatorSuccessRate` DOUBLE NOT NULL DEFAULT 0,
    ADD COLUMN `mediatorTotalOrders` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `mediatorVerifiedAt` DATETIME(3) NULL,
    ADD COLUMN `walletVerified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `walletVerifiedAt` DATETIME(3) NULL;

-- CreateTable
CREATE TABLE `EscrowTransaction` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `fromAddress` VARCHAR(191) NULL,
    `toAddress` VARCHAR(191) NULL,
    `txHash` VARCHAR(191) NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `description` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `confirmedAt` DATETIME(3) NULL,

    INDEX `EscrowTransaction_orderId_idx`(`orderId`),
    INDEX `EscrowTransaction_txHash_idx`(`txHash`),
    INDEX `EscrowTransaction_status_idx`(`status`),
    INDEX `EscrowTransaction_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ArbitrationCase` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `reporterId` VARCHAR(191) NOT NULL,
    `reporterType` VARCHAR(191) NOT NULL,
    `reason` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `evidence` JSON NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `resolution` VARCHAR(191) NULL,
    `votingDeadline` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `resolvedAt` DATETIME(3) NULL,

    INDEX `ArbitrationCase_orderId_idx`(`orderId`),
    INDEX `ArbitrationCase_reporterId_idx`(`reporterId`),
    INDEX `ArbitrationCase_status_idx`(`status`),
    INDEX `ArbitrationCase_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ArbitrationVote` (
    `id` VARCHAR(191) NOT NULL,
    `caseId` VARCHAR(191) NULL,
    `disputeId` VARCHAR(191) NULL,
    `voterId` VARCHAR(191) NOT NULL,
    `decision` VARCHAR(191) NOT NULL,
    `reasoning` VARCHAR(191) NULL,
    `voteWeight` INTEGER NOT NULL DEFAULT 1,
    `votedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `ArbitrationVote_caseId_idx`(`caseId`),
    INDEX `ArbitrationVote_disputeId_idx`(`disputeId`),
    INDEX `ArbitrationVote_voterId_idx`(`voterId`),
    INDEX `ArbitrationVote_createdAt_idx`(`createdAt`),
    UNIQUE INDEX `ArbitrationVote_caseId_voterId_key`(`caseId`, `voterId`),
    UNIQUE INDEX `ArbitrationVote_disputeId_voterId_key`(`disputeId`, `voterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RewardCoupon` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NULL,
    `type` VARCHAR(191) NOT NULL,
    `value` DOUBLE NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'ACTIVE',
    `expiresAt` DATETIME(3) NOT NULL,
    `usedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `batchId` VARCHAR(191) NULL,
    `createdById` VARCHAR(191) NULL,
    `discountType` VARCHAR(191) NOT NULL DEFAULT 'FIXED',
    `distributionType` VARCHAR(191) NOT NULL DEFAULT 'TARGETED',
    `maxDiscount` DOUBLE NULL,
    `maxUses` INTEGER NOT NULL DEFAULT 1,
    `minAmount` DOUBLE NULL,
    `terms` VARCHAR(191) NULL,
    `title` VARCHAR(191) NULL,
    `usedCount` INTEGER NOT NULL DEFAULT 0,

    INDEX `RewardCoupon_userId_idx`(`userId`),
    INDEX `RewardCoupon_status_idx`(`status`),
    INDEX `RewardCoupon_expiresAt_idx`(`expiresAt`),
    INDEX `RewardCoupon_createdAt_idx`(`createdAt`),
    INDEX `RewardCoupon_distributionType_idx`(`distributionType`),
    INDEX `RewardCoupon_type_idx`(`type`),
    INDEX `RewardCoupon_createdById_fkey`(`createdById`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `MediatorVerification` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `walletAddress` VARCHAR(191) NOT NULL,
    `verificationMethod` VARCHAR(191) NOT NULL,
    `verificationData` JSON NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `verifiedBy` VARCHAR(191) NULL,
    `verifiedAt` DATETIME(3) NULL,
    `rejectionReason` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `MediatorVerification_userId_idx`(`userId`),
    INDEX `MediatorVerification_status_idx`(`status`),
    INDEX `MediatorVerification_createdAt_idx`(`createdAt`),
    INDEX `MediatorVerification_verifiedBy_fkey`(`verifiedBy`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DisputeReport` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `reporterId` VARCHAR(191) NOT NULL,
    `disputeType` VARCHAR(191) NOT NULL,
    `description` TEXT NOT NULL,
    `evidence` JSON NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `priority` VARCHAR(191) NOT NULL DEFAULT 'MEDIUM',
    `assignedToId` VARCHAR(191) NULL,
    `adminResponse` TEXT NULL,
    `resolutionNotes` TEXT NULL,
    `reportedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `resolvedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `DisputeReport_orderId_idx`(`orderId`),
    INDEX `DisputeReport_reporterId_idx`(`reporterId`),
    INDEX `DisputeReport_status_idx`(`status`),
    INDEX `DisputeReport_priority_idx`(`priority`),
    INDEX `DisputeReport_assignedToId_idx`(`assignedToId`),
    INDEX `DisputeReport_reportedAt_idx`(`reportedAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GiftCardProduct` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `productType` VARCHAR(191) NOT NULL,
    `faceValue` DOUBLE NOT NULL,
    `salePrice` DOUBLE NOT NULL,
    `stock` INTEGER NOT NULL DEFAULT 0,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `features` JSON NULL,
    `terms` VARCHAR(191) NULL,
    `validDays` INTEGER NOT NULL DEFAULT 365,
    `supportedPayments` JSON NULL,
    `createdById` VARCHAR(191) NOT NULL,
    `updatedById` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `GiftCardProduct_productType_idx`(`productType`),
    INDEX `GiftCardProduct_isActive_idx`(`isActive`),
    INDEX `GiftCardProduct_createdAt_idx`(`createdAt`),
    INDEX `GiftCardProduct_createdById_fkey`(`createdById`),
    INDEX `GiftCardProduct_updatedById_fkey`(`updatedById`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GiftCardOrder` (
    `id` VARCHAR(191) NOT NULL,
    `orderNumber` VARCHAR(191) NOT NULL,
    `productId` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `unitPrice` DOUBLE NOT NULL,
    `totalAmount` DOUBLE NOT NULL,
    `paymentMethod` VARCHAR(191) NOT NULL,
    `paymentStatus` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `paymentTxHash` VARCHAR(191) NULL,
    `deliveryMethod` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `paidAt` DATETIME(3) NULL,
    `completedAt` DATETIME(3) NULL,
    `cancelledAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `GiftCardOrder_orderNumber_key`(`orderNumber`),
    INDEX `GiftCardOrder_orderNumber_idx`(`orderNumber`),
    INDEX `GiftCardOrder_userId_idx`(`userId`),
    INDEX `GiftCardOrder_productId_idx`(`productId`),
    INDEX `GiftCardOrder_paymentStatus_idx`(`paymentStatus`),
    INDEX `GiftCardOrder_status_idx`(`status`),
    INDEX `GiftCardOrder_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GiftCard` (
    `id` VARCHAR(191) NOT NULL,
    `cardCode` VARCHAR(191) NOT NULL,
    `faceValue` DOUBLE NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'GENERATED',
    `validUntil` DATETIME(3) NOT NULL,
    `soldAt` DATETIME(3) NULL,
    `soldToId` VARCHAR(191) NULL,
    `saleOrderId` VARCHAR(191) NULL,
    `redeemedAt` DATETIME(3) NULL,
    `redeemedById` VARCHAR(191) NULL,
    `redeemedValue` DOUBLE NULL,
    `createdById` VARCHAR(191) NOT NULL,
    `batchId` VARCHAR(191) NULL,
    `notes` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `assignedAt` DATETIME(3) NULL,
    `assignedById` VARCHAR(191) NULL,
    `assignedToId` VARCHAR(191) NULL,
    `orderId` VARCHAR(191) NULL,
    `productId` VARCHAR(191) NULL,

    UNIQUE INDEX `GiftCard_cardCode_key`(`cardCode`),
    INDEX `GiftCard_cardCode_idx`(`cardCode`),
    INDEX `GiftCard_status_idx`(`status`),
    INDEX `GiftCard_validUntil_idx`(`validUntil`),
    INDEX `GiftCard_soldAt_idx`(`soldAt`),
    INDEX `GiftCard_redeemedAt_idx`(`redeemedAt`),
    INDEX `GiftCard_batchId_idx`(`batchId`),
    INDEX `GiftCard_productId_idx`(`productId`),
    INDEX `GiftCard_orderId_idx`(`orderId`),
    INDEX `GiftCard_assignedById_fkey`(`assignedById`),
    INDEX `GiftCard_assignedToId_fkey`(`assignedToId`),
    INDEX `GiftCard_createdById_fkey`(`createdById`),
    INDEX `GiftCard_redeemedById_fkey`(`redeemedById`),
    INDEX `GiftCard_saleOrderId_fkey`(`saleOrderId`),
    INDEX `GiftCard_soldToId_fkey`(`soldToId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GiftCardTransaction` (
    `id` VARCHAR(191) NOT NULL,
    `giftCardId` VARCHAR(191) NOT NULL,
    `transactionType` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `userId` VARCHAR(191) NULL,
    `orderId` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `metadata` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `GiftCardTransaction_giftCardId_idx`(`giftCardId`),
    INDEX `GiftCardTransaction_transactionType_idx`(`transactionType`),
    INDEX `GiftCardTransaction_userId_idx`(`userId`),
    INDEX `GiftCardTransaction_createdAt_idx`(`createdAt`),
    INDEX `GiftCardTransaction_orderId_fkey`(`orderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RedemptionCode` (
    `id` VARCHAR(191) NOT NULL,
    `codeValue` VARCHAR(191) NULL,
    `codeType` VARCHAR(191) NOT NULL,
    `rewardType` VARCHAR(191) NOT NULL,
    `rewardValue` DOUBLE NOT NULL,
    `rewardUnit` VARCHAR(191) NOT NULL DEFAULT 'USDT',
    `distributionType` VARCHAR(191) NOT NULL,
    `targetUserId` VARCHAR(191) NULL,
    `batchId` VARCHAR(191) NULL,
    `maxUses` INTEGER NOT NULL DEFAULT 1,
    `usedCount` INTEGER NOT NULL DEFAULT 0,
    `validFrom` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `validUntil` DATETIME(3) NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'ACTIVE',
    `firstUsedAt` DATETIME(3) NULL,
    `lastUsedAt` DATETIME(3) NULL,
    `createdById` VARCHAR(191) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `terms` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `RedemptionCode_codeValue_key`(`codeValue`),
    INDEX `RedemptionCode_codeValue_idx`(`codeValue`),
    INDEX `RedemptionCode_codeType_idx`(`codeType`),
    INDEX `RedemptionCode_rewardType_idx`(`rewardType`),
    INDEX `RedemptionCode_status_idx`(`status`),
    INDEX `RedemptionCode_validFrom_idx`(`validFrom`),
    INDEX `RedemptionCode_validUntil_idx`(`validUntil`),
    INDEX `RedemptionCode_targetUserId_idx`(`targetUserId`),
    INDEX `RedemptionCode_batchId_idx`(`batchId`),
    INDEX `RedemptionCode_createdById_fkey`(`createdById`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RedemptionTransaction` (
    `id` VARCHAR(191) NOT NULL,
    `redemptionCodeId` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `transactionType` VARCHAR(191) NOT NULL,
    `rewardValue` DOUBLE NOT NULL,
    `rewardUnit` VARCHAR(191) NOT NULL,
    `usageContext` VARCHAR(191) NULL,
    `orderId` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `metadata` JSON NULL,
    `ipAddress` VARCHAR(191) NULL,
    `userAgent` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `RedemptionTransaction_redemptionCodeId_idx`(`redemptionCodeId`),
    INDEX `RedemptionTransaction_userId_idx`(`userId`),
    INDEX `RedemptionTransaction_transactionType_idx`(`transactionType`),
    INDEX `RedemptionTransaction_createdAt_idx`(`createdAt`),
    INDEX `RedemptionTransaction_orderId_fkey`(`orderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SystemSetting` (
    `id` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `key` VARCHAR(191) NOT NULL,
    `value` JSON NOT NULL,
    `description` VARCHAR(191) NULL,
    `dataType` VARCHAR(191) NOT NULL DEFAULT 'string',
    `isPublic` BOOLEAN NOT NULL DEFAULT false,
    `isEditable` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `createdBy` VARCHAR(191) NULL,
    `updatedBy` VARCHAR(191) NULL,

    INDEX `SystemSetting_category_idx`(`category`),
    INDEX `SystemSetting_isPublic_idx`(`isPublic`),
    INDEX `SystemSetting_createdAt_idx`(`createdAt`),
    UNIQUE INDEX `SystemSetting_category_key_key`(`category`, `key`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SearchKeyword` (
    `id` VARCHAR(191) NOT NULL,
    `keyword` VARCHAR(191) NOT NULL,
    `searchCount` INTEGER NOT NULL DEFAULT 1,
    `resultCount` INTEGER NOT NULL DEFAULT 0,
    `isHot` BOOLEAN NOT NULL DEFAULT false,
    `category` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `SearchKeyword_keyword_key`(`keyword`),
    INDEX `SearchKeyword_searchCount_idx`(`searchCount`),
    INDEX `SearchKeyword_isHot_idx`(`isHot`),
    INDEX `SearchKeyword_category_idx`(`category`),
    INDEX `SearchKeyword_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Notification` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `message` TEXT NOT NULL,
    `data` JSON NULL,
    `isRead` BOOLEAN NOT NULL DEFAULT false,
    `readAt` DATETIME(3) NULL,
    `relatedId` VARCHAR(191) NULL,
    `relatedType` VARCHAR(191) NULL,
    `priority` VARCHAR(191) NOT NULL DEFAULT 'NORMAL',
    `expiresAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `Notification_userId_isRead_idx`(`userId`, `isRead`),
    INDEX `Notification_type_idx`(`type`),
    INDEX `Notification_priority_idx`(`priority`),
    INDEX `Notification_createdAt_idx`(`createdAt`),
    INDEX `Notification_relatedId_relatedType_idx`(`relatedId`, `relatedType`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `MediatorCommittee` (
    `id` VARCHAR(191) NOT NULL,
    `mediatorId` VARCHAR(191) NOT NULL,
    `role` VARCHAR(191) NOT NULL DEFAULT 'MEMBER',
    `appointedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `appointedBy` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'ACTIVE',
    `votingPower` INTEGER NOT NULL DEFAULT 1,
    `specializations` JSON NULL,
    `notes` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `MediatorCommittee_mediatorId_idx`(`mediatorId`),
    INDEX `MediatorCommittee_status_idx`(`status`),
    INDEX `MediatorCommittee_appointedBy_fkey`(`appointedBy`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SystemConfig` (
    `id` VARCHAR(191) NOT NULL,
    `key` VARCHAR(191) NOT NULL,
    `value` JSON NOT NULL,
    `description` VARCHAR(191) NULL,
    `category` VARCHAR(191) NOT NULL DEFAULT 'GENERAL',
    `isPublic` BOOLEAN NOT NULL DEFAULT false,
    `updatedBy` VARCHAR(191) NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `SystemConfig_key_key`(`key`),
    INDEX `SystemConfig_category_idx`(`category`),
    INDEX `SystemConfig_isPublic_idx`(`isPublic`),
    INDEX `SystemConfig_updatedBy_fkey`(`updatedBy`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EscrowOrder` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `mediatorId` VARCHAR(191) NOT NULL,
    `buyerId` VARCHAR(191) NOT NULL,
    `sellerId` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `mediatorFee` DOUBLE NOT NULL,
    `platformFee` DOUBLE NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `bnbTransactionHash` VARCHAR(191) NULL,
    `mediatorWalletAddress` VARCHAR(191) NOT NULL,
    `fundedAt` DATETIME(3) NULL,
    `shippedAt` DATETIME(3) NULL,
    `deliveredAt` DATETIME(3) NULL,
    `completedAt` DATETIME(3) NULL,
    `disputedAt` DATETIME(3) NULL,
    `cancelledAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `EscrowOrder_orderId_key`(`orderId`),
    INDEX `EscrowOrder_mediatorId_idx`(`mediatorId`),
    INDEX `EscrowOrder_buyerId_idx`(`buyerId`),
    INDEX `EscrowOrder_sellerId_idx`(`sellerId`),
    INDEX `EscrowOrder_status_idx`(`status`),
    INDEX `EscrowOrder_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EscrowChatRoom` (
    `id` VARCHAR(191) NOT NULL,
    `escrowOrderId` VARCHAR(191) NOT NULL,
    `roomCode` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `EscrowChatRoom_escrowOrderId_key`(`escrowOrderId`),
    UNIQUE INDEX `EscrowChatRoom_roomCode_key`(`roomCode`),
    INDEX `EscrowChatRoom_roomCode_idx`(`roomCode`),
    INDEX `EscrowChatRoom_isActive_idx`(`isActive`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EscrowDispute` (
    `id` VARCHAR(191) NOT NULL,
    `escrowOrderId` VARCHAR(191) NOT NULL,
    `reporterId` VARCHAR(191) NOT NULL,
    `reportedId` VARCHAR(191) NOT NULL,
    `reason` VARCHAR(191) NOT NULL,
    `description` TEXT NOT NULL,
    `evidence` JSON NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `priority` VARCHAR(191) NOT NULL DEFAULT 'MEDIUM',
    `adminAssigned` VARCHAR(191) NULL,
    `resolution` TEXT NULL,
    `resolvedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `EscrowDispute_escrowOrderId_idx`(`escrowOrderId`),
    INDEX `EscrowDispute_reporterId_idx`(`reporterId`),
    INDEX `EscrowDispute_status_idx`(`status`),
    INDEX `EscrowDispute_priority_idx`(`priority`),
    INDEX `EscrowDispute_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `MediatorApplication` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `bnbWalletAddress` VARCHAR(191) NOT NULL,
    `depositAmount` DOUBLE NOT NULL,
    `feeRate` DOUBLE NOT NULL,
    `experience` TEXT NOT NULL,
    `introduction` TEXT NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `reviewedBy` VARCHAR(191) NULL,
    `reviewNotes` TEXT NULL,
    `approvedAt` DATETIME(3) NULL,
    `rejectedAt` DATETIME(3) NULL,
    `suspendedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `MediatorApplication_userId_key`(`userId`),
    INDEX `MediatorApplication_userId_idx`(`userId`),
    INDEX `MediatorApplication_status_idx`(`status`),
    INDEX `MediatorApplication_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WithdrawalVoucher` (
    `id` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `description` VARCHAR(191) NULL,
    `validUntil` DATETIME(3) NOT NULL,
    `isUsed` BOOLEAN NOT NULL DEFAULT false,
    `usedBy` VARCHAR(191) NULL,
    `usedAt` DATETIME(3) NULL,
    `issuedBy` VARCHAR(191) NOT NULL,
    `issuedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `WithdrawalVoucher_code_key`(`code`),
    INDEX `WithdrawalVoucher_code_idx`(`code`),
    INDEX `WithdrawalVoucher_isUsed_idx`(`isUsed`),
    INDEX `WithdrawalVoucher_validUntil_idx`(`validUntil`),
    INDEX `WithdrawalVoucher_usedBy_idx`(`usedBy`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `MediatorReward` (
    `id` VARCHAR(191) NOT NULL,
    `mediatorId` VARCHAR(191) NOT NULL,
    `rewardType` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NULL,
    `voucherId` VARCHAR(191) NULL,
    `description` VARCHAR(191) NOT NULL,
    `earnedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `claimedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `MediatorReward_mediatorId_idx`(`mediatorId`),
    INDEX `MediatorReward_rewardType_idx`(`rewardType`),
    INDEX `MediatorReward_earnedAt_idx`(`earnedAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BlockchainTransaction` (
    `id` VARCHAR(191) NOT NULL,
    `txHash` VARCHAR(191) NOT NULL,
    `network` VARCHAR(191) NOT NULL DEFAULT 'BNB_CHAIN',
    `fromAddress` VARCHAR(191) NOT NULL,
    `toAddress` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `tokenSymbol` VARCHAR(191) NOT NULL DEFAULT 'USDT',
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `blockNumber` INTEGER NULL,
    `gasUsed` DOUBLE NULL,
    `gasFee` DOUBLE NULL,
    `confirmations` INTEGER NOT NULL DEFAULT 0,
    `relatedOrderId` VARCHAR(191) NULL,
    `relatedEscrowId` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `BlockchainTransaction_txHash_key`(`txHash`),
    INDEX `BlockchainTransaction_txHash_idx`(`txHash`),
    INDEX `BlockchainTransaction_network_idx`(`network`),
    INDEX `BlockchainTransaction_status_idx`(`status`),
    INDEX `BlockchainTransaction_relatedOrderId_idx`(`relatedOrderId`),
    INDEX `BlockchainTransaction_relatedEscrowId_idx`(`relatedEscrowId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Order_mediatorId_fkey` ON `Order`(`mediatorId`);

-- AddForeignKey
ALTER TABLE `Order` ADD CONSTRAINT `Order_mediatorId_fkey` FOREIGN KEY (`mediatorId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `HelpMediaFile` ADD CONSTRAINT `HelpMediaFile_uploaderId_fkey` FOREIGN KEY (`uploaderId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowTransaction` ADD CONSTRAINT `EscrowTransaction_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ArbitrationCase` ADD CONSTRAINT `ArbitrationCase_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ArbitrationCase` ADD CONSTRAINT `ArbitrationCase_reporterId_fkey` FOREIGN KEY (`reporterId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ArbitrationVote` ADD CONSTRAINT `ArbitrationVote_caseId_fkey` FOREIGN KEY (`caseId`) REFERENCES `ArbitrationCase`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ArbitrationVote` ADD CONSTRAINT `ArbitrationVote_disputeId_fkey` FOREIGN KEY (`disputeId`) REFERENCES `EscrowDispute`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ArbitrationVote` ADD CONSTRAINT `ArbitrationVote_voterId_fkey` FOREIGN KEY (`voterId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RewardCoupon` ADD CONSTRAINT `RewardCoupon_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RewardCoupon` ADD CONSTRAINT `RewardCoupon_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorVerification` ADD CONSTRAINT `MediatorVerification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorVerification` ADD CONSTRAINT `MediatorVerification_verifiedBy_fkey` FOREIGN KEY (`verifiedBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `DisputeReport` ADD CONSTRAINT `DisputeReport_assignedToId_fkey` FOREIGN KEY (`assignedToId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `DisputeReport` ADD CONSTRAINT `DisputeReport_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `DisputeReport` ADD CONSTRAINT `DisputeReport_reporterId_fkey` FOREIGN KEY (`reporterId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardProduct` ADD CONSTRAINT `GiftCardProduct_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardProduct` ADD CONSTRAINT `GiftCardProduct_updatedById_fkey` FOREIGN KEY (`updatedById`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardOrder` ADD CONSTRAINT `GiftCardOrder_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `GiftCardProduct`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardOrder` ADD CONSTRAINT `GiftCardOrder_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_assignedById_fkey` FOREIGN KEY (`assignedById`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_assignedToId_fkey` FOREIGN KEY (`assignedToId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `GiftCardOrder`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `GiftCardProduct`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_redeemedById_fkey` FOREIGN KEY (`redeemedById`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_saleOrderId_fkey` FOREIGN KEY (`saleOrderId`) REFERENCES `Order`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCard` ADD CONSTRAINT `GiftCard_soldToId_fkey` FOREIGN KEY (`soldToId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardTransaction` ADD CONSTRAINT `GiftCardTransaction_giftCardId_fkey` FOREIGN KEY (`giftCardId`) REFERENCES `GiftCard`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardTransaction` ADD CONSTRAINT `GiftCardTransaction_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GiftCardTransaction` ADD CONSTRAINT `GiftCardTransaction_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RedemptionCode` ADD CONSTRAINT `RedemptionCode_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RedemptionCode` ADD CONSTRAINT `RedemptionCode_targetUserId_fkey` FOREIGN KEY (`targetUserId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RedemptionTransaction` ADD CONSTRAINT `RedemptionTransaction_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RedemptionTransaction` ADD CONSTRAINT `RedemptionTransaction_redemptionCodeId_fkey` FOREIGN KEY (`redemptionCodeId`) REFERENCES `RedemptionCode`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RedemptionTransaction` ADD CONSTRAINT `RedemptionTransaction_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SystemSetting` ADD CONSTRAINT `SystemSetting_createdBy_fkey` FOREIGN KEY (`createdBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SystemSetting` ADD CONSTRAINT `SystemSetting_updatedBy_fkey` FOREIGN KEY (`updatedBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Notification` ADD CONSTRAINT `Notification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorCommittee` ADD CONSTRAINT `MediatorCommittee_appointedBy_fkey` FOREIGN KEY (`appointedBy`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorCommittee` ADD CONSTRAINT `MediatorCommittee_mediatorId_fkey` FOREIGN KEY (`mediatorId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SystemConfig` ADD CONSTRAINT `SystemConfig_updatedBy_fkey` FOREIGN KEY (`updatedBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowOrder` ADD CONSTRAINT `EscrowOrder_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowOrder` ADD CONSTRAINT `EscrowOrder_mediatorId_fkey` FOREIGN KEY (`mediatorId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowOrder` ADD CONSTRAINT `EscrowOrder_buyerId_fkey` FOREIGN KEY (`buyerId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowOrder` ADD CONSTRAINT `EscrowOrder_sellerId_fkey` FOREIGN KEY (`sellerId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowChatRoom` ADD CONSTRAINT `EscrowChatRoom_escrowOrderId_fkey` FOREIGN KEY (`escrowOrderId`) REFERENCES `EscrowOrder`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowDispute` ADD CONSTRAINT `EscrowDispute_escrowOrderId_fkey` FOREIGN KEY (`escrowOrderId`) REFERENCES `EscrowOrder`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowDispute` ADD CONSTRAINT `EscrowDispute_reporterId_fkey` FOREIGN KEY (`reporterId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowDispute` ADD CONSTRAINT `EscrowDispute_reportedId_fkey` FOREIGN KEY (`reportedId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowDispute` ADD CONSTRAINT `EscrowDispute_adminAssigned_fkey` FOREIGN KEY (`adminAssigned`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorApplication` ADD CONSTRAINT `MediatorApplication_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorApplication` ADD CONSTRAINT `MediatorApplication_reviewedBy_fkey` FOREIGN KEY (`reviewedBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WithdrawalVoucher` ADD CONSTRAINT `WithdrawalVoucher_usedBy_fkey` FOREIGN KEY (`usedBy`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WithdrawalVoucher` ADD CONSTRAINT `WithdrawalVoucher_issuedBy_fkey` FOREIGN KEY (`issuedBy`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorReward` ADD CONSTRAINT `MediatorReward_mediatorId_fkey` FOREIGN KEY (`mediatorId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MediatorReward` ADD CONSTRAINT `MediatorReward_voucherId_fkey` FOREIGN KEY (`voucherId`) REFERENCES `WithdrawalVoucher`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BlockchainTransaction` ADD CONSTRAINT `BlockchainTransaction_relatedOrderId_fkey` FOREIGN KEY (`relatedOrderId`) REFERENCES `Order`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BlockchainTransaction` ADD CONSTRAINT `BlockchainTransaction_relatedEscrowId_fkey` FOREIGN KEY (`relatedEscrowId`) REFERENCES `EscrowOrder`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
