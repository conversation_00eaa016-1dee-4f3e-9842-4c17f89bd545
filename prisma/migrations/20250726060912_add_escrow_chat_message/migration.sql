-- CreateTable
CREATE TABLE `EscrowChatMessage` (
    `id` VARCHAR(191) NOT NULL,
    `content` VARCHAR(191) NOT NULL,
    `messageType` VARCHAR(191) NOT NULL DEFAULT 'TEXT',
    `status` VARCHAR(191) NOT NULL DEFAULT 'SENT',
    `fileUrl` VARCHAR(191) NULL,
    `fileName` VARCHAR(191) NULL,
    `fileSize` INTEGER NULL,
    `fileMimeType` VARCHAR(191) NULL,
    `fileMetadata` JSON NULL,
    `chatRoomId` VARCHAR(191) NOT NULL,
    `senderId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `EscrowChatMessage_chatRoomId_createdAt_idx`(`chatRoomId`, `createdAt`),
    INDEX `EscrowChatMessage_senderId_idx`(`senderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `EscrowChatMessage` ADD CONSTRAINT `EscrowChatMessage_chatRoomId_fkey` FOREIGN KEY (`chatRoomId`) REFERENCES `EscrowChatRoom`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EscrowChatMessage` ADD CONSTRAINT `EscrowChatMessage_senderId_fkey` FOREIGN KEY (`senderId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
