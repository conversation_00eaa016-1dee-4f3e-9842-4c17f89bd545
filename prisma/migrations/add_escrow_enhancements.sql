-- 中间人托管服务系统数据库扩展
-- 添加新的表和字段以支持完整的中间人托管功能

-- 1. 扩展用户表，添加更多中间人相关字段
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "bnbWalletAddress" TEXT;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "bnbWalletVerified" BOOLEAN DEFAULT false;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "bnbWalletVerifiedAt" TIMESTAMP(3);
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "mediatorExperience" TEXT;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "mediatorIntroduction" TEXT;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "mediatorTotalOrders" INTEGER DEFAULT 0;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "mediatorSuccessRate" FLOAT DEFAULT 0;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "mediatorLastActiveAt" TIMESTAMP(3);

-- 2. 扩展订单表，添加更多托管相关字段
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "useEscrow" BOOLEAN DEFAULT false;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "escrowFee" FLOAT DEFAULT 0;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "escrowFeeRate" FLOAT DEFAULT 0;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "escrowNotes" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "disputeReason" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "disputeStatus" TEXT DEFAULT 'NONE'; -- NONE, REPORTED, UNDER_REVIEW, RESOLVED
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "disputeReportedAt" TIMESTAMP(3);
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "disputeResolvedAt" TIMESTAMP(3);

-- 3. 创建中间人认证记录表
CREATE TABLE IF NOT EXISTS "MediatorVerification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "walletAddress" TEXT NOT NULL,
    "verificationMethod" TEXT NOT NULL, -- SIGNATURE, TRANSACTION, MANUAL
    "verificationData" JSONB,
    "status" TEXT NOT NULL DEFAULT 'PENDING', -- PENDING, VERIFIED, REJECTED
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MediatorVerification_pkey" PRIMARY KEY ("id")
);

-- 4. 创建托管聊天室表
CREATE TABLE IF NOT EXISTS "EscrowChatRoom" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "participants" TEXT[], -- 参与者用户ID数组
    "roomType" TEXT NOT NULL DEFAULT 'ESCROW', -- ESCROW, ARBITRATION
    "status" TEXT NOT NULL DEFAULT 'ACTIVE', -- ACTIVE, ARCHIVED, CLOSED
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EscrowChatRoom_pkey" PRIMARY KEY ("id")
);

-- 5. 创建争议举报表
CREATE TABLE IF NOT EXISTS "DisputeReport" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "reporterId" TEXT NOT NULL,
    "reportedUserId" TEXT NOT NULL,
    "category" TEXT NOT NULL, -- PAYMENT_ISSUE, SHIPPING_ISSUE, PRODUCT_ISSUE, FRAUD, OTHER
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "evidence" JSONB, -- 证据文件和截图
    "chatExport" JSONB, -- 聊天记录导出
    "status" TEXT NOT NULL DEFAULT 'PENDING', -- PENDING, UNDER_REVIEW, RESOLVED, REJECTED
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, URGENT
    "assignedTo" TEXT, -- 分配给的管理员ID
    "resolution" TEXT,
    "resolvedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DisputeReport_pkey" PRIMARY KEY ("id")
);

-- 6. 创建中间人奖励记录表
CREATE TABLE IF NOT EXISTS "MediatorReward" (
    "id" TEXT NOT NULL,
    "mediatorId" TEXT NOT NULL,
    "caseId" TEXT, -- 仲裁案例ID（如果是仲裁奖励）
    "orderId" TEXT, -- 订单ID（如果是订单完成奖励）
    "rewardType" TEXT NOT NULL, -- ARBITRATION_PARTICIPATION, ORDER_COMPLETION, MONTHLY_BONUS
    "rewardAmount" FLOAT NOT NULL,
    "couponId" TEXT, -- 奖励券ID
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING', -- PENDING, ISSUED, EXPIRED
    "issuedAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MediatorReward_pkey" PRIMARY KEY ("id")
);

-- 7. 创建中间人委员会表
CREATE TABLE IF NOT EXISTS "MediatorCommittee" (
    "id" TEXT NOT NULL,
    "mediatorId" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'MEMBER', -- MEMBER, SENIOR, CHAIRMAN
    "appointedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "appointedBy" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, RETIRED
    "votingPower" INTEGER NOT NULL DEFAULT 1,
    "specializations" TEXT[], -- 专业领域
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MediatorCommittee_pkey" PRIMARY KEY ("id")
);

-- 8. 创建系统配置表（用于管理各种参数）
CREATE TABLE IF NOT EXISTS "SystemConfig" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" JSONB NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL DEFAULT 'GENERAL', -- GENERAL, ESCROW, MEDIATOR, REWARD
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "updatedBy" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SystemConfig_pkey" PRIMARY KEY ("id")
);

-- 添加外键约束
ALTER TABLE "MediatorVerification" ADD CONSTRAINT "MediatorVerification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "MediatorVerification" ADD CONSTRAINT "MediatorVerification_verifiedBy_fkey" FOREIGN KEY ("verifiedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "EscrowChatRoom" ADD CONSTRAINT "EscrowChatRoom_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "DisputeReport" ADD CONSTRAINT "DisputeReport_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DisputeReport" ADD CONSTRAINT "DisputeReport_reporterId_fkey" FOREIGN KEY ("reporterId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DisputeReport" ADD CONSTRAINT "DisputeReport_reportedUserId_fkey" FOREIGN KEY ("reportedUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DisputeReport" ADD CONSTRAINT "DisputeReport_assignedTo_fkey" FOREIGN KEY ("assignedTo") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "MediatorReward" ADD CONSTRAINT "MediatorReward_mediatorId_fkey" FOREIGN KEY ("mediatorId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "MediatorReward" ADD CONSTRAINT "MediatorReward_caseId_fkey" FOREIGN KEY ("caseId") REFERENCES "ArbitrationCase"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "MediatorReward" ADD CONSTRAINT "MediatorReward_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "MediatorReward" ADD CONSTRAINT "MediatorReward_couponId_fkey" FOREIGN KEY ("couponId") REFERENCES "RewardCoupon"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "MediatorCommittee" ADD CONSTRAINT "MediatorCommittee_mediatorId_fkey" FOREIGN KEY ("mediatorId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "MediatorCommittee" ADD CONSTRAINT "MediatorCommittee_appointedBy_fkey" FOREIGN KEY ("appointedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- 添加索引
CREATE INDEX IF NOT EXISTS "MediatorVerification_userId_idx" ON "MediatorVerification"("userId");
CREATE INDEX IF NOT EXISTS "MediatorVerification_status_idx" ON "MediatorVerification"("status");
CREATE INDEX IF NOT EXISTS "MediatorVerification_createdAt_idx" ON "MediatorVerification"("createdAt");

CREATE INDEX IF NOT EXISTS "EscrowChatRoom_orderId_idx" ON "EscrowChatRoom"("orderId");
CREATE INDEX IF NOT EXISTS "EscrowChatRoom_status_idx" ON "EscrowChatRoom"("status");

CREATE INDEX IF NOT EXISTS "DisputeReport_orderId_idx" ON "DisputeReport"("orderId");
CREATE INDEX IF NOT EXISTS "DisputeReport_reporterId_idx" ON "DisputeReport"("reporterId");
CREATE INDEX IF NOT EXISTS "DisputeReport_status_idx" ON "DisputeReport"("status");
CREATE INDEX IF NOT EXISTS "DisputeReport_createdAt_idx" ON "DisputeReport"("createdAt");

CREATE INDEX IF NOT EXISTS "MediatorReward_mediatorId_idx" ON "MediatorReward"("mediatorId");
CREATE INDEX IF NOT EXISTS "MediatorReward_rewardType_idx" ON "MediatorReward"("rewardType");
CREATE INDEX IF NOT EXISTS "MediatorReward_status_idx" ON "MediatorReward"("status");

CREATE INDEX IF NOT EXISTS "MediatorCommittee_mediatorId_idx" ON "MediatorCommittee"("mediatorId");
CREATE INDEX IF NOT EXISTS "MediatorCommittee_status_idx" ON "MediatorCommittee"("status");

CREATE UNIQUE INDEX IF NOT EXISTS "SystemConfig_key_idx" ON "SystemConfig"("key");

-- 插入默认系统配置
INSERT INTO "SystemConfig" ("id", "key", "value", "description", "category", "isPublic") VALUES
('cfg_escrow_min_amount', 'escrow_min_amount', '100', '启用托管服务的最低订单金额(USDT)', 'ESCROW', true),
('cfg_mediator_min_fee', 'mediator_min_fee_rate', '0.01', '中间人最低手续费率', 'MEDIATOR', true),
('cfg_mediator_max_fee', 'mediator_max_fee_rate', '0.30', '中间人最高手续费率', 'MEDIATOR', true),
('cfg_platform_fee_rate', 'platform_fee_rate', '0.30', '平台从中间人手续费中抽取的比例', 'ESCROW', false),
('cfg_reward_coupon_amount', 'reward_coupon_amount', '10', '中间人仲裁奖励券金额(USDT)', 'REWARD', false),
('cfg_reward_coupon_validity', 'reward_coupon_validity_days', '7', '奖励券有效期(天)', 'REWARD', false),
('cfg_max_monthly_rewards', 'max_monthly_rewards_per_mediator', '1', '每个中间人每月最多获得的奖励券数量', 'REWARD', false)
ON CONFLICT ("key") DO NOTHING;
