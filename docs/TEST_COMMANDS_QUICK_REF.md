# 测试命令快速参考卡片

## 🚀 常用测试命令

### 基础命令
```bash
npm test                    # 运行所有测试
npm run test:watch         # 监视模式
npm run test:coverage      # 生成覆盖率报告
npm run test:ci            # CI模式运行
```

### 分类测试
```bash
npm run test:unit          # 单元测试
npm run test:integration   # 集成测试
npm run test:e2e           # 端到端测试
npm run test:performance   # 性能测试
npm run test:all           # 所有测试套件
```

### 环境管理
```bash
npm run test:setup         # 设置测试环境
npm run test:cleanup       # 清理测试环境
npm run test:fix           # 修复测试问题
```

## 🔧 Vitest 直接命令

### 运行特定测试
```bash
npx vitest test/basic.test.ts              # 单个文件
npx vitest test/api/                       # 整个目录
npx vitest --grep "商品API"                # 按名称过滤
npx vitest --changed                       # 只运行变更的测试
```

### 调试选项
```bash
npx vitest --reporter=verbose              # 详细输出
npx vitest --bail=1                        # 失败时停止
npx vitest --testTimeout=30000             # 设置超时
npx vitest --threads --maxThreads=1        # 单线程模式
```

### 覆盖率选项
```bash
npx vitest --coverage                      # 生成覆盖率
npx vitest --coverage --reporter=html      # HTML格式
npx vitest --coverage.thresholds.lines=80  # 设置阈值
```

## 🐛 故障排除

### 常见错误及快速修复

#### Mock 错误
```bash
# 错误: Cannot read properties of undefined (reading 'mockResolvedValue')
npm run test:fix           # 运行自动修复脚本
```

#### API 测试失败 (400/500)
```bash
# 检查测试数据完整性
npm run test:fix
# 或手动检查 test/api/*.test.ts 中的测试数据
```

#### 数据库连接错误
```bash
export NODE_ENV=test
export DATABASE_URL=file:./test.db
npx prisma migrate reset --force
npm run test:setup
```

#### 性能测试失败
```bash
# 清理并重新运行
npm run test:cleanup
npm run test:setup
npm run test:performance
```

### 环境重置
```bash
# 完全重置测试环境
npm run cache:clear
rm -rf node_modules/.cache .next test.db*
npm install
npx prisma migrate reset --force
npm run test:setup
```

## 📊 测试报告

### 查看报告
```bash
# 覆盖率报告
open coverage/index.html

# 测试结果报告
open test-results/report.html

# JSON 结果
cat test-results/results.json
```

### 报告位置
```
test-results/
├── results.json       # JSON格式结果
├── report.html        # HTML测试报告
└── fix-report.json    # 修复报告

coverage/
├── index.html         # 覆盖率主报告
├── lcov.info         # LCOV格式
└── coverage-final.json # JSON格式覆盖率
```

## 🎯 测试最佳实践

### 编写测试
```typescript
// 基本结构
describe('功能模块', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该执行预期行为', () => {
    // Arrange (准备)
    const input = 'test'
    
    // Act (执行)
    const result = functionToTest(input)
    
    // Assert (断言)
    expect(result).toBe('expected')
  })
})
```

### 调试技巧
```typescript
// 添加调试输出
console.log('调试信息:', data)

// 监视函数调用
const spy = vi.spyOn(module, 'function')
expect(spy).toHaveBeenCalledWith(args)

// 检查 Mock 状态
expect(vi.isMockFunction(mockFunction)).toBe(true)
```

## 📋 测试检查清单

### 运行测试前
- [ ] 设置正确的环境变量
- [ ] 清理旧的测试数据
- [ ] 确保依赖已安装

### 编写测试时
- [ ] 使用描述性的测试名称
- [ ] 每个测试只验证一个行为
- [ ] 正确设置和清理 Mock
- [ ] 处理异步操作

### 测试失败时
- [ ] 检查错误信息
- [ ] 验证测试数据
- [ ] 确认 Mock 设置
- [ ] 运行修复脚本

## 🔗 相关文档

- [完整测试指南](./TESTING_GUIDE.md)
- [Vitest 官方文档](https://vitest.dev/)
- [Testing Library 文档](https://testing-library.com/)

## 💡 快速提示

### 性能优化
```bash
# 并行运行测试
npx vitest --threads --maxThreads=4

# 只运行失败的测试
npx vitest --changed

# 跳过慢速测试
npx vitest --exclude="**/performance/**"
```

### 开发工作流
```bash
# 开发时的推荐工作流
npm run test:watch &          # 后台运行监视模式
# 编写代码...
npm run test:coverage         # 检查覆盖率
npm run lint                  # 代码检查
npm run build                 # 构建验证
```

### CI/CD 流程
```bash
# 完整的CI流程
npm run type:check &&
npm run lint &&
npm run test:ci &&
npm run build
```

---

**记住**: 如果遇到测试问题，首先运行 `npm run test:fix` 尝试自动修复！
