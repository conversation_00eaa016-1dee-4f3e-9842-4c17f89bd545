# 地图集成配置指南

## 概述

BitMarket平台集成了高德地图API，为用户提供直观的地理位置可视化功能。本指南将帮助您完成地图功能的配置和部署。

## 🗺️ 支持的地图服务

### 1. 高德地图 (推荐)
- **优势**: 国内服务稳定、数据准确、API丰富
- **适用**: 中国大陆地区用户
- **组件**: `AMapView.tsx`

### 2. 模拟地图
- **优势**: 无需API密钥、开发测试友好
- **适用**: 开发环境、演示环境
- **组件**: `MapView.tsx`

## 🔑 API密钥配置

### 获取高德地图API密钥

1. **注册开发者账号**
   - 访问 [高德开放平台](https://console.amap.com/)
   - 注册并完成实名认证

2. **创建应用**
   - 进入控制台 → 应用管理 → 我的应用
   - 点击"创建新应用"
   - 填写应用信息

3. **添加Key**
   - 选择应用 → 添加Key
   - 服务平台选择"Web端(JS API)"
   - 设置域名白名单（如：localhost:3000, yourdomain.com）

4. **获取密钥**
   - 复制生成的API Key

### 环境变量配置

在项目根目录的 `.env.local` 文件中添加：

```bash
# 高德地图API密钥
NEXT_PUBLIC_AMAP_API_KEY=your_amap_api_key_here

# 可选：百度地图API密钥
NEXT_PUBLIC_BAIDU_MAP_API_KEY=your_baidu_api_key_here

# 可选：Google Maps API密钥
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
```

## 🛠️ 组件使用

### 基础地图组件

```tsx
import AMapView from '@/components/location/AMapView'

// 使用高德地图
<AMapView
  center={{ latitude: 39.9042, longitude: 116.4074 }}
  items={searchResults}
  searchRadius={5}
  onItemClick={handleItemClick}
/>
```

### 附近搜索组件

```tsx
import NearbySearch from '@/components/location/NearbySearch'

// 集成地图的搜索组件
<NearbySearch
  type="product"
  onResults={handleSearchResults}
  initialLocation={userLocation}
/>
```

### 位置选择器

```tsx
import LocationPicker from '@/components/location/LocationPicker'

// 位置设置组件
<LocationPicker
  initialLocation={currentLocation}
  onLocationChange={handleLocationChange}
  showPublicOption={true}
  showRadiusOption={true}
/>
```

## 🎨 地图样式配置

### 高德地图样式

```typescript
// 标准样式
mapStyle: 'amap://styles/normal'

// 卫星图
mapStyle: 'amap://styles/satellite'

// 暗色主题
mapStyle: 'amap://styles/dark'

// 清新蓝
mapStyle: 'amap://styles/fresh'
```

### 自定义标记图标

```typescript
const createCustomIcon = (type: string) => {
  return new window.AMap.Icon({
    image: 'data:image/svg+xml;base64,' + btoa(`
      <svg width="24" height="24" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" fill="#3b82f6"/>
        <text x="12" y="16" text-anchor="middle" fill="white">🛍️</text>
      </svg>
    `),
    size: new window.AMap.Size(24, 24)
  })
}
```

## 🔧 功能特性

### 1. 地理位置搜索
- 支持1-50km可调搜索半径
- 实时距离计算和排序
- 边界框优化查询性能

### 2. 交互式地图
- 缩放、平移、重新定位
- 点击标记查看详情
- 鼠标悬停快速预览
- 搜索范围可视化

### 3. 标记管理
- 商品标记（蓝色）
- 需求标记（绿色）
- 搜索中心标记（红色）
- 自定义图标和样式

### 4. 信息窗口
- 标题、价格、距离信息
- 自定义HTML内容
- 响应式设计

## 📱 响应式设计

### 移动端优化
```css
/* 移动端地图容器 */
@media (max-width: 768px) {
  .map-container {
    height: 300px;
    margin: 0 -16px;
  }
}

/* 平板端地图容器 */
@media (min-width: 769px) and (max-width: 1024px) {
  .map-container {
    height: 400px;
  }
}

/* 桌面端地图容器 */
@media (min-width: 1025px) {
  .map-container {
    height: 500px;
  }
}
```

## 🚀 部署配置

### 域名白名单设置

在高德地图控制台中设置允许的域名：

**开发环境**:
- `localhost:3000`
- `127.0.0.1:3000`

**生产环境**:
- `yourdomain.com`
- `www.yourdomain.com`
- `*.yourdomain.com` (支持子域名)

### HTTPS要求

生产环境必须使用HTTPS协议：
- 地理定位API要求HTTPS
- 高德地图在HTTPS下性能更好
- 确保SSL证书有效

## 🔍 故障排除

### 常见问题

1. **地图不显示**
   - 检查API密钥是否正确
   - 确认域名在白名单中
   - 查看浏览器控制台错误

2. **定位失败**
   - 确保使用HTTPS协议
   - 检查浏览器定位权限
   - 验证API密钥权限

3. **标记不显示**
   - 检查坐标格式（经度在前，纬度在后）
   - 确认坐标范围合理
   - 验证数据格式

### 调试工具

```typescript
// 开启地图调试模式
const map = new window.AMap.Map('container', {
  zoom: 13,
  center: [116.4074, 39.9042],
  debug: true // 开启调试
})

// 监听地图事件
map.on('complete', () => {
  console.log('地图加载完成')
})

map.on('click', (e) => {
  console.log('点击位置:', e.lnglat.getLng(), e.lnglat.getLat())
})
```

## 📊 性能优化

### 1. 懒加载
```typescript
// 动态加载地图API
const loadMapAPI = async () => {
  if (window.AMap) return
  
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${apiKey}`
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  })
}
```

### 2. 标记聚合
```typescript
// 大量标记时使用聚合
import AMapLoader from '@amap/amap-jsapi-loader'

const cluster = new window.AMap.MarkerCluster(map, markers, {
  gridSize: 80,
  maxZoom: 14
})
```

### 3. 缓存优化
```typescript
// 缓存地图实例
const mapCache = new Map()

const getMapInstance = (containerId: string) => {
  if (mapCache.has(containerId)) {
    return mapCache.get(containerId)
  }
  
  const map = new window.AMap.Map(containerId, options)
  mapCache.set(containerId, map)
  return map
}
```

## 🔐 安全考虑

### API密钥保护
- 使用域名白名单限制访问
- 定期轮换API密钥
- 监控API使用量
- 设置合理的调用限制

### 数据隐私
- 用户位置数据加密存储
- 实现位置脱敏显示
- 提供位置权限控制
- 遵守相关隐私法规

## 📈 监控和分析

### 使用统计
```typescript
// 地图使用统计
const trackMapUsage = (action: string, data?: any) => {
  // 发送统计数据到分析服务
  analytics.track('map_usage', {
    action,
    timestamp: Date.now(),
    ...data
  })
}

// 使用示例
trackMapUsage('map_loaded')
trackMapUsage('marker_clicked', { itemId: 'xxx' })
trackMapUsage('search_performed', { radius: 5, results: 10 })
```

### 性能监控
```typescript
// 地图性能监控
const measureMapPerformance = () => {
  const startTime = performance.now()
  
  map.on('complete', () => {
    const loadTime = performance.now() - startTime
    console.log(`地图加载时间: ${loadTime}ms`)
    
    // 发送性能数据
    analytics.track('map_performance', {
      loadTime,
      markerCount: markers.length
    })
  })
}
```

---

**配置完成后，您的BitMarket平台将拥有强大的地理位置可视化功能，为用户提供直观的附近搜索体验！**
