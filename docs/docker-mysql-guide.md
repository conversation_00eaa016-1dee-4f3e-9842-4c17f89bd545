# Docker MySQL 使用指南

## 🎉 设置完成状态

✅ **Docker MySQL 已成功配置并运行！**

## 📊 当前配置信息

| 配置项 | 值 | 说明 |
|--------|-----|------|
| **数据库类型** | MySQL 8.0 | 在Docker容器中运行 |
| **容器名称** | bitmarket-mysql | Docker容器标识 |
| **主机地址** | localhost | 本地访问 |
| **端口号** | 3306 | MySQL默认端口 |
| **用户名** | root | 管理员用户 |
| **密码** | password | 默认密码 |
| **数据库名** | bitmarket | 项目数据库 |
| **数据持久化** | Docker Volume | 数据不会丢失 |

## 🐳 Docker 优势

### ✅ 优点
- **零安装**: 无需在Windows上安装MySQL服务器
- **环境隔离**: 不影响系统其他部分
- **一键启停**: 快速启动和停止数据库
- **版本控制**: 可以轻松切换MySQL版本
- **数据持久化**: 容器删除后数据仍然保留
- **易于备份**: 可以轻松备份和恢复

### 📋 文件结构
```
bitmarket/
├── docker-compose.yml          # Docker配置文件
├── mysql-init/                 # MySQL初始化脚本
│   └── 01-init.sql            # 数据库初始化SQL
├── scripts/
│   └── docker-mysql-setup.js  # Docker管理脚本
└── prisma/
    └── migrations/             # 数据库迁移文件
```

## 🛠️ 管理命令

### 基本操作

```bash
# 启动MySQL容器
node scripts/docker-mysql-setup.js start

# 停止MySQL容器
node scripts/docker-mysql-setup.js stop

# 查看容器状态
node scripts/docker-mysql-setup.js status

# 查看MySQL日志
node scripts/docker-mysql-setup.js logs

# 测试数据库连接
node scripts/docker-mysql-setup.js test
```

### 高级操作

```bash
# 连接到MySQL命令行
node scripts/docker-mysql-setup.js connect

# 重启容器
docker-compose restart

# 查看容器详细信息
docker inspect bitmarket-mysql

# 进入容器Shell
docker exec -it bitmarket-mysql bash
```

## 🔧 Docker Compose 配置

**文件**: `docker-compose.yml`
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: bitmarket-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: bitmarket
      MYSQL_USER: bitmarket_user
      MYSQL_PASSWORD: bitmarket_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

volumes:
  mysql_data:
    driver: local
```

## 📊 数据库管理

### 连接信息

```bash
# 应用程序连接 (已配置)
DATABASE_URL="mysql://root:password@localhost:3306/bitmarket"

# 命令行连接
mysql -h localhost -P 3306 -u root -p bitmarket

# Docker内部连接
docker exec -it bitmarket-mysql mysql -u root -p bitmarket
```

### 数据持久化

- **数据卷**: `mysql_data` (Docker管理的持久化存储)
- **位置**: Docker内部管理，数据不会因容器删除而丢失
- **备份**: 可以通过Docker命令备份整个数据卷

### 初始化脚本

**文件**: `mysql-init/01-init.sql`
```sql
-- BitMarket 数据库初始化脚本
CREATE DATABASE IF NOT EXISTS bitmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE bitmarket;
SELECT 'BitMarket 数据库初始化完成' AS message;
```

## 🚀 日常使用工作流

### 开发启动流程

1. **启动Docker Desktop** (如果未运行)
2. **启动MySQL容器**:
   ```bash
   node scripts/docker-mysql-setup.js start
   ```
3. **启动应用**:
   ```bash
   npm run dev
   ```

### 开发结束流程

1. **停止应用** (Ctrl+C)
2. **停止MySQL容器** (可选):
   ```bash
   node scripts/docker-mysql-setup.js stop
   ```

### 数据库操作

```bash
# 查看数据库状态
node scripts/database-config.js show

# 运行数据库迁移
npx prisma migrate dev

# 查看数据库数据
npx prisma studio

# 重置数据库
npx prisma migrate reset
```

## 🔍 故障排除

### 常见问题

#### 1. 容器启动失败

**检查Docker状态**:
```bash
docker --version
docker ps
```

**解决方案**:
- 确保Docker Desktop正在运行
- 检查端口3306是否被占用
- 重启Docker Desktop

#### 2. 连接被拒绝

**检查容器状态**:
```bash
node scripts/docker-mysql-setup.js status
node scripts/docker-mysql-setup.js logs
```

**解决方案**:
- 等待MySQL完全启动 (约30秒)
- 检查容器健康状态
- 重启容器

#### 3. 数据丢失

**检查数据卷**:
```bash
docker volume ls
docker volume inspect bitmarket_mysql_data
```

**解决方案**:
- 数据存储在Docker卷中，不会因容器删除而丢失
- 如需恢复，检查数据卷完整性

### 端口冲突解决

如果3306端口被占用：

1. **修改docker-compose.yml**:
   ```yaml
   ports:
     - "3307:3306"  # 使用3307端口
   ```

2. **更新环境变量**:
   ```bash
   DATABASE_URL="mysql://root:password@localhost:3307/bitmarket"
   ```

## 📦 备份和恢复

### 数据备份

```bash
# 备份数据库
docker exec bitmarket-mysql mysqldump -u root -ppassword bitmarket > backup.sql

# 备份数据卷
docker run --rm -v bitmarket_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql-backup.tar.gz -C /data .
```

### 数据恢复

```bash
# 恢复数据库
docker exec -i bitmarket-mysql mysql -u root -ppassword bitmarket < backup.sql

# 恢复数据卷
docker run --rm -v bitmarket_mysql_data:/data -v $(pwd):/backup alpine tar xzf /backup/mysql-backup.tar.gz -C /data
```

## 🔧 高级配置

### 自定义MySQL配置

创建 `mysql-config/my.cnf`:
```ini
[mysqld]
max_connections = 200
innodb_buffer_pool_size = 256M
```

更新 `docker-compose.yml`:
```yaml
volumes:
  - ./mysql-config:/etc/mysql/conf.d
```

### 多环境支持

```bash
# 开发环境
docker-compose -f docker-compose.dev.yml up -d

# 测试环境
docker-compose -f docker-compose.test.yml up -d
```

## 📈 性能监控

### 查看MySQL状态

```sql
-- 连接到MySQL
docker exec -it bitmarket-mysql mysql -u root -p

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看数据库大小
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'bitmarket'
GROUP BY table_schema;
```

### 容器资源使用

```bash
# 查看容器资源使用
docker stats bitmarket-mysql

# 查看容器日志
docker logs bitmarket-mysql --tail 100 -f
```

## 🎯 总结

✅ **Docker MySQL 已成功配置并运行**
- 数据库类型: MySQL 8.0
- 连接地址: localhost:3306
- 容器名称: bitmarket-mysql
- 数据持久化: 已启用
- 管理工具: 已就绪

**下一步**: 你现在可以正常使用BitMarket应用，所有数据将存储在Docker MySQL容器中！

---

**快速参考**:
- 启动: `node scripts/docker-mysql-setup.js start`
- 停止: `node scripts/docker-mysql-setup.js stop`
- 状态: `node scripts/docker-mysql-setup.js status`
- 测试: `node scripts/docker-mysql-setup.js test`
