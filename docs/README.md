# BitMarket 文档中心

欢迎来到BitMarket项目的文档中心！这里包含了所有Node.js操作、脚本使用和系统管理的详细文档。

## 📚 文档导航

### 🚀 快速开始
- **[快速参考卡片](quick-reference.md)** - 常用命令和操作速查
- **[Node.js操作指南](node-operations-guide.md)** - 完整的操作文档
- **[脚本详细文档](scripts-documentation.md)** - 所有脚本的详细说明

### 📋 文档概览

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [quick-reference.md](quick-reference.md) | 快速参考和常用命令 | 所有开发者 |
| [node-operations-guide.md](node-operations-guide.md) | 完整操作指南和最佳实践 | 开发者、运维人员 |
| [scripts-documentation.md](scripts-documentation.md) | 脚本功能和使用说明 | 开发者、测试人员 |

---

## 🎯 根据需求选择文档

### 我是新手开发者
👉 建议阅读顺序：
1. [快速参考卡片](quick-reference.md) - 了解基本命令
2. [Node.js操作指南](node-operations-guide.md) - 学习完整流程
3. [脚本详细文档](scripts-documentation.md) - 掌握脚本使用

### 我需要快速解决问题
👉 直接查看：
- [快速参考卡片](quick-reference.md) - 故障排除速查
- [Node.js操作指南](node-operations-guide.md) - 紧急情况处理

### 我要了解脚本功能
👉 重点阅读：
- [脚本详细文档](scripts-documentation.md) - 所有脚本说明
- [快速参考卡片](quick-reference.md) - 常用脚本命令

### 我是运维人员
👉 关注内容：
- [Node.js操作指南](node-operations-guide.md) - 生产环境部署
- [快速参考卡片](quick-reference.md) - 监控和维护

---

## 🛠️ 核心功能概览

### 充值管理系统
BitMarket的充值管理系统支持多种支付方式，并为管理员提供了完善的审核界面：

#### 支持的支付方式
- **币安支付 (BINANCE_PAY)**: 显示PIN码和订单号
- **BNB链支付**: 显示交易hash和BSCScan链接
- **USDT支付**: 显示交易hash和TronScan链接

#### 管理功能
- 充值申请审核
- 用户保证金管理
- 提现申请处理
- 操作历史记录

### 数据管理工具
提供了完整的数据管理脚本：

#### 测试数据管理
```bash
# 创建完整测试数据
node scripts/create-complete-test-deposits.js

# 清理测试数据
node scripts/clean-deposit-records.js test
```

#### 生产数据管理
```bash
# 查看数据状态
node scripts/clean-deposit-records.js

# 数据库管理
npx prisma studio
```

---

## 🚀 快速开始指南

### 1. 环境准备
```bash
# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 初始化数据库
npx prisma migrate dev
npx prisma generate
```

### 2. 启动开发环境
```bash
# 启动开发服务器
npm run dev

# 创建测试数据
node scripts/create-complete-test-deposits.js
```

### 3. 访问管理后台
- 地址: http://localhost:3000/admin/deposits
- 功能: 查看充值申请和用户保证金

### 4. 测试完成后清理
```bash
# 清理测试数据
node scripts/clean-deposit-records.js test
```

---

## 📊 项目结构

### 重要目录
```
bitmarket-v1.1.1-emergency-security-fix/
├── app/                    # Next.js应用代码
│   ├── admin/             # 管理后台
│   └── api/               # API接口
├── docs/                  # 文档目录
│   ├── README.md          # 文档导航（本文件）
│   ├── quick-reference.md # 快速参考
│   ├── node-operations-guide.md # 操作指南
│   └── scripts-documentation.md # 脚本文档
├── prisma/                # 数据库相关
│   ├── schema.prisma      # 数据库模型
│   └── migrations/        # 数据库迁移
└── scripts/               # 管理脚本
    ├── create-complete-test-deposits.js
    ├── clean-deposit-records.js
    └── create-test-deposits.js
```

### 核心文件
- `app/admin/deposits/page.tsx` - 充值管理页面
- `app/api/admin/deposits/route.ts` - 充值API接口
- `scripts/clean-deposit-records.js` - 数据清理工具

---

## 🔧 开发工具

### 数据库工具
- **Prisma Studio**: `npx prisma studio` - 可视化数据库管理
- **数据库迁移**: `npx prisma migrate dev` - 应用数据库变更

### 调试工具
- **开发服务器**: `npm run dev` - 热重载开发环境
- **构建检查**: `npm run build` - 生产环境构建测试

### 脚本工具
- **测试数据**: `node scripts/create-complete-test-deposits.js`
- **数据清理**: `node scripts/clean-deposit-records.js`
- **状态查看**: `node scripts/clean-deposit-records.js`

---

## 📞 获取帮助

### 文档问题
如果文档有不清楚的地方，请：
1. 查看相关的详细文档
2. 检查快速参考卡片
3. 查看脚本的帮助信息

### 技术问题
如果遇到技术问题，请：
1. 查看故障排除部分
2. 检查错误日志
3. 验证环境配置

### 联系方式
- 开发团队: <EMAIL>
- 技术支持: <EMAIL>
- 文档反馈: <EMAIL>

---

## 📝 文档维护

### 更新记录
- **v1.0** (2025-07-21): 初始版本，包含完整的操作文档
- 充值管理系统功能完善
- 多支付方式支持
- 完整的脚本工具集

### 贡献指南
如果您想改进文档：
1. 确保信息准确性
2. 保持格式一致性
3. 添加实际使用示例
4. 更新相关的快速参考

---

## 🎉 功能亮点

### ✅ 已实现功能
- **多支付方式支持**: 币安支付、BNB链、USDT等
- **智能信息显示**: 根据支付方式显示相应信息
- **完整的管理界面**: 展开式详情、批准/拒绝操作
- **数据管理工具**: 创建、清理、查看测试数据
- **用户过滤**: 自动过滤已删除用户
- **安全操作**: 完善的权限验证和操作日志

### 🔮 技术特色
- **现代化架构**: Next.js + Prisma + TypeScript
- **响应式设计**: 适配不同屏幕尺寸
- **实时更新**: 数据变更即时反映
- **错误处理**: 完善的错误捕获和用户提示
- **性能优化**: 高效的数据查询和渲染

---

*BitMarket文档中心 v1.0 | 最后更新: 2025年7月21日*
