# BitMarket Node.js 操作文档

## 📋 目录
- [环境准备](#环境准备)
- [数据库操作](#数据库操作)
- [测试数据管理](#测试数据管理)
- [充值记录管理](#充值记录管理)
- [用户管理](#用户管理)
- [开发调试](#开发调试)
- [故障排除](#故障排除)

---

## 🚀 环境准备

### 启动开发服务器
```bash
# 启动Next.js开发服务器
npm run dev

# 或者使用yarn
yarn dev

# 服务器将在 http://localhost:3000 启动
```

### 环境变量检查
```bash
# 检查.env文件是否存在
ls -la .env

# 查看环境变量（注意：不要在生产环境中执行）
cat .env
```

---

## 🗄️ 数据库操作

### Prisma 数据库管理

#### 生成Prisma客户端
```bash
# 生成Prisma客户端
npx prisma generate
```

#### 数据库迁移
```bash
# 创建新的迁移
npx prisma migrate dev --name migration_name

# 应用迁移到生产环境
npx prisma migrate deploy

# 重置数据库（开发环境）
npx prisma migrate reset
```

#### 数据库查看和管理
```bash
# 打开Prisma Studio（数据库可视化工具）
npx prisma studio

# 查看数据库状态
npx prisma migrate status

# 查看数据库结构
npx prisma db pull
```

---

## 🧪 测试数据管理

### 创建测试用户
```bash
# 创建基础测试用户
node scripts/create-test-users.js

# 创建管理员用户
node scripts/create-admin-user.js
```

### 创建测试充值记录

#### 基础测试数据
```bash
# 创建基础充值测试数据
node scripts/create-test-deposits.js
```

#### 完整测试数据（包含PIN码和交易hash）
```bash
# 创建包含完整信息的测试充值记录
node scripts/create-complete-test-deposits.js
```

### 测试数据特点
- **币安支付记录**: 包含PIN码和订单号
- **BNB链记录**: 包含交易hash和区块链信息
- **USDT记录**: 包含交易hash和TRC20网络信息
- **用户关联**: 自动关联到现有测试用户

---

## 🧹 充值记录管理

### 查看充值记录状态
```bash
# 查看当前充值记录统计
node scripts/clean-deposit-records.js
```

### 删除充值记录

#### 删除所有充值记录
```bash
# ⚠️ 危险操作：删除所有充值记录
node scripts/clean-deposit-records.js all
```

#### 精确删除选项
```bash
# 删除测试充值记录（包含"测试"关键词）
node scripts/clean-deposit-records.js test

# 删除待处理的充值记录
node scripts/clean-deposit-records.js pending

# 删除今天创建的充值记录
node scripts/clean-deposit-records.js today

# 删除特定用户的充值记录
node scripts/clean-deposit-records.<NAME_EMAIL>
```

### 充值记录清理工具功能
- **安全确认**: 显示详细统计和最近记录
- **多种选项**: 支持不同粒度的清理
- **操作日志**: 清晰的前后对比
- **错误处理**: 完善的错误处理机制

---

## 👥 用户管理

### 查看用户信息
```bash
# 查看所有用户（如果有相关脚本）
node scripts/list-users.js

# 查看管理员用户
node scripts/list-admin-users.js
```

### 用户数据操作
```bash
# 重置用户密码（如果有相关脚本）
node scripts/reset-user-password.js <EMAIL>

# 删除测试用户（如果有相关脚本）
node scripts/clean-test-users.js
```

---

## 🔧 开发调试

### 日志查看
```bash
# 查看开发服务器日志
# 日志会直接显示在运行 npm run dev 的终端中

# 如果使用PM2管理进程
pm2 logs bitmarket

# 查看错误日志
pm2 logs bitmarket --err
```

### 代码检查
```bash
# 运行ESLint检查
npm run lint

# 运行类型检查（如果使用TypeScript）
npm run type-check

# 运行测试（如果有测试套件）
npm test
```

### 构建和部署
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 检查构建结果
npm run analyze
```

---

## 🛠️ 故障排除

### 常见问题解决

#### 数据库连接问题
```bash
# 检查数据库连接
node -e "const { PrismaClient } = require('@prisma/client'); const prisma = new PrismaClient(); prisma.\$connect().then(() => console.log('✅ 数据库连接成功')).catch(e => console.error('❌ 数据库连接失败:', e))"

# 重新生成Prisma客户端
npx prisma generate
```

#### 依赖问题
```bash
# 清理node_modules并重新安装
rm -rf node_modules package-lock.json
npm install

# 或使用yarn
rm -rf node_modules yarn.lock
yarn install
```

#### 端口占用问题
```bash
# 查看端口占用情况
netstat -tulpn | grep :3000

# 杀死占用端口的进程
kill -9 $(lsof -t -i:3000)

# 使用不同端口启动
PORT=3001 npm run dev
```

#### 缓存清理
```bash
# 清理Next.js缓存
rm -rf .next

# 清理npm缓存
npm cache clean --force

# 重新构建
npm run build
```

---

## 📊 监控和维护

### 性能监控
```bash
# 查看内存使用情况
node --inspect scripts/memory-usage.js

# 性能分析
node --prof scripts/performance-test.js
```

### 数据备份
```bash
# 导出数据库（如果有备份脚本）
node scripts/backup-database.js

# 导入数据库
node scripts/restore-database.js backup-file.sql
```

---

## 🔐 安全操作

### 敏感数据处理
```bash
# 生成新的JWT密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# 加密敏感配置
node scripts/encrypt-config.js

# 验证环境变量
node scripts/verify-env.js
```

### 权限检查
```bash
# 检查管理员权限
node scripts/check-admin-permissions.js

# 验证用户角色
node scripts/verify-user-roles.js
```

---

## 📝 脚本文件说明

### 已创建的脚本文件
- `scripts/create-test-deposits.js` - 创建基础测试充值数据
- `scripts/create-complete-test-deposits.js` - 创建完整测试充值数据
- `scripts/clean-deposit-records.js` - 充值记录清理工具

### 脚本执行权限
```bash
# 确保脚本有执行权限
chmod +x scripts/*.js

# 检查Node.js版本兼容性
node --version
npm --version
```

---

## ⚡ 快速参考

### 常用命令组合
```bash
# 完整重置开发环境
npm run dev:reset

# 创建完整测试环境
npm run setup:test

# 清理所有测试数据
npm run clean:test

# 生产环境部署
npm run deploy:prod
```

### 环境变量模板
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

---

## 📞 支持和帮助

### 获取帮助
- 查看脚本帮助: `node scripts/script-name.js --help`
- 查看详细日志: 在脚本中添加 `--verbose` 参数
- 调试模式: 使用 `DEBUG=* node script-name.js`

### 联系信息
- 开发团队: <EMAIL>
- 技术支持: <EMAIL>
- 文档更新: <EMAIL>

---

## 🎯 实际操作示例

### 完整的开发流程示例

#### 1. 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd bitmarket-v1.1.1-emergency-security-fix

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入正确的数据库连接信息

# 初始化数据库
npx prisma migrate dev
npx prisma generate
```

#### 2. 创建测试环境
```bash
# 启动开发服务器
npm run dev

# 新开终端，创建测试数据
node scripts/create-test-users.js
node scripts/create-complete-test-deposits.js

# 访问管理后台
# http://localhost:3000/admin/deposits
```

#### 3. 测试功能验证
```bash
# 查看创建的测试数据
node scripts/clean-deposit-records.js

# 在浏览器中测试：
# 1. 登录管理后台
# 2. 查看充值申请列表
# 3. 点击详情查看不同支付方式的信息
# 4. 测试批准/拒绝功能
```

#### 4. 清理测试数据
```bash
# 清理所有测试充值记录
node scripts/clean-deposit-records.js all

# 或者只清理测试数据
node scripts/clean-deposit-records.js test
```

### 生产环境部署示例

#### 1. 构建和部署
```bash
# 构建生产版本
npm run build

# 运行生产环境检查
npm run start

# 使用PM2管理进程（推荐）
npm install -g pm2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 2. 数据库迁移
```bash
# 生产环境数据库迁移
npx prisma migrate deploy

# 验证迁移结果
npx prisma migrate status
```

---

## 📚 脚本详细说明

### create-test-deposits.js
**功能**: 创建基础测试充值数据
**用法**: `node scripts/create-test-deposits.js`
**输出**: 创建5条基础充值记录
**特点**:
- 包含不同支付方式（USDT、BNB、BINANCE_PAY）
- 自动关联到现有用户
- 生成基础的订单信息

### create-complete-test-deposits.js
**功能**: 创建包含完整信息的测试充值数据
**用法**: `node scripts/create-complete-test-deposits.js`
**输出**: 创建5条完整充值记录
**特点**:
- 币安支付：包含PIN码和订单号
- BNB链：包含完整交易hash
- USDT：包含TRC20网络交易hash
- 包含详细的metadata信息

### clean-deposit-records.js
**功能**: 充值记录清理工具
**用法**:
```bash
# 查看状态
node scripts/clean-deposit-records.js

# 执行清理
node scripts/clean-deposit-records.js [action] [params]
```
**参数说明**:
- `all`: 删除所有充值记录
- `test`: 删除测试充值记录
- `pending`: 删除待处理充值记录
- `today`: 删除今天创建的充值记录
- `user <email>`: 删除特定用户的充值记录

---

## 🔍 调试技巧

### 数据库调试
```bash
# 直接查询数据库
npx prisma studio

# 使用Prisma客户端调试
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.depositRecord.findMany().then(console.log);
"

# 查看SQL查询日志
# 在 .env 中添加: DATABASE_URL="...?logging=true"
```

### API调试
```bash
# 测试API端点
curl -X GET http://localhost:3000/api/admin/deposits?type=deposits

# 使用Node.js测试
node -e "
fetch('http://localhost:3000/api/admin/deposits?type=deposits')
  .then(r => r.json())
  .then(console.log);
"
```

### 前端调试
```bash
# 启动开发服务器并打开调试
DEBUG=* npm run dev

# 检查构建错误
npm run build 2>&1 | grep -i error

# 分析包大小
npm run analyze
```

---

## ⚠️ 注意事项和最佳实践

### 安全注意事项
1. **生产环境**:
   - 永远不要在生产环境运行测试数据脚本
   - 定期备份数据库
   - 使用强密码和安全的环境变量

2. **开发环境**:
   - 定期清理测试数据
   - 不要提交包含敏感信息的文件
   - 使用不同的数据库实例

### 性能最佳实践
1. **数据库操作**:
   - 批量操作时使用事务
   - 定期清理无用数据
   - 监控查询性能

2. **脚本执行**:
   - 大量数据操作时显示进度
   - 实现错误重试机制
   - 记录操作日志

### 代码维护
1. **脚本管理**:
   - 为每个脚本添加详细注释
   - 实现参数验证
   - 提供帮助信息

2. **版本控制**:
   - 记录重要操作的版本
   - 保持文档同步更新
   - 使用语义化版本号

---

## 🚨 紧急情况处理

### 数据恢复
```bash
# 如果误删数据，立即停止所有操作
pm2 stop all

# 从备份恢复（如果有）
node scripts/restore-from-backup.js

# 检查数据完整性
node scripts/verify-data-integrity.js
```

### 服务器问题
```bash
# 检查服务器状态
pm2 status

# 重启服务
pm2 restart all

# 查看错误日志
pm2 logs --err

# 内存不足时的处理
pm2 reload all
```

### 数据库问题
```bash
# 检查数据库连接
npx prisma db pull

# 修复数据库连接
npx prisma migrate reset --force

# 重新应用迁移
npx prisma migrate deploy
```

---

*最后更新: 2025年7月21日*
*版本: v1.1.1*
*维护者: BitMarket开发团队*
