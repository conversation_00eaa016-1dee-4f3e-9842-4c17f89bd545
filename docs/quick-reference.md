# BitMarket Node.js 快速参考卡片

## 🚀 常用命令速查

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 数据库管理
npx prisma studio
npx prisma generate
npx prisma migrate dev
```

### 测试数据管理
```bash
# 创建测试数据
node scripts/create-complete-test-deposits.js

# 查看数据状态
node scripts/clean-deposit-records.js

# 清理测试数据
node scripts/clean-deposit-records.js test
node scripts/clean-deposit-records.js all
```

---

## 📊 充值记录管理

### 查看状态
```bash
# 显示所有充值记录统计
node scripts/clean-deposit-records.js
```

### 清理选项
| 命令 | 功能 | 风险级别 |
|------|------|----------|
| `node scripts/clean-deposit-records.js test` | 删除测试数据 | 🟢 低 |
| `node scripts/clean-deposit-records.js pending` | 删除待处理记录 | 🟡 中 |
| `node scripts/clean-deposit-records.js today` | 删除今天的记录 | 🟡 中 |
| `node scripts/clean-deposit-records.js all` | 删除所有记录 | 🔴 高 |

---

## 🛠️ 故障排除速查

### 数据库问题
```bash
# 连接测试
node -e "const { PrismaClient } = require('@prisma/client'); const prisma = new PrismaClient(); prisma.\$connect().then(() => console.log('✅ OK')).catch(e => console.error('❌ Error:', e))"

# 重置数据库
npx prisma migrate reset
npx prisma generate
```

### 端口占用
```bash
# 查看端口占用
netstat -tulpn | grep :3000

# 杀死进程
kill -9 $(lsof -t -i:3000)

# 使用其他端口
PORT=3001 npm run dev
```

### 依赖问题
```bash
# 清理重装
rm -rf node_modules package-lock.json
npm install

# 清理缓存
npm cache clean --force
rm -rf .next
```

---

## 📁 重要文件位置

### 脚本文件
- `scripts/create-complete-test-deposits.js` - 创建完整测试数据
- `scripts/clean-deposit-records.js` - 充值记录清理工具
- `scripts/create-test-deposits.js` - 创建基础测试数据

### 配置文件
- `.env` - 环境变量配置
- `prisma/schema.prisma` - 数据库模型
- `package.json` - 项目依赖和脚本

### 核心代码
- `app/admin/deposits/page.tsx` - 管理后台充值页面
- `app/api/admin/deposits/route.ts` - 充值API接口
- `prisma/migrations/` - 数据库迁移文件

---

## 🎯 测试流程

### 完整测试流程
1. **启动服务**: `npm run dev`
2. **创建数据**: `node scripts/create-complete-test-deposits.js`
3. **访问后台**: http://localhost:3000/admin/deposits
4. **测试功能**: 点击详情查看不同支付方式
5. **清理数据**: `node scripts/clean-deposit-records.js test`

### 验证要点
- ✅ 币安支付显示PIN码和订单号
- ✅ BNB链显示交易hash和BSCScan链接
- ✅ USDT显示交易hash和TronScan链接
- ✅ 详情展开/收起功能正常
- ✅ 批准/拒绝按钮可用

---

## 🔐 安全检查清单

### 开发环境
- [ ] `.env`文件不包含生产数据库信息
- [ ] 测试数据定期清理
- [ ] 不提交敏感信息到版本控制

### 生产环境
- [ ] 使用强密码和安全连接
- [ ] 定期备份数据库
- [ ] 监控异常操作
- [ ] 限制管理员权限

---

## 📞 紧急联系

### 开发问题
- 查看详细文档: `docs/node-operations-guide.md`
- 检查错误日志: 开发服务器终端输出
- 数据库问题: `npx prisma studio`

### 生产问题
- 立即停止服务: `pm2 stop all`
- 查看日志: `pm2 logs --err`
- 数据恢复: 联系数据库管理员

---

## 💡 小贴士

### 提高效率
- 使用别名简化常用命令
- 设置IDE快捷键
- 保存常用代码片段

### 避免错误
- 操作前先查看数据状态
- 重要操作前备份数据
- 在开发环境充分测试

### 最佳实践
- 定期更新依赖
- 保持代码整洁
- 及时更新文档

---

*快速参考 v1.0 | 2025年7月21日*
