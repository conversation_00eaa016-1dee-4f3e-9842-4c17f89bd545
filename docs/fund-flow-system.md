# 统一资金流动管理系统

## 概述

本系统实现了平台所有资金流动的统一管理机制，基于"冻结 → 状态确认 → 划扣"的核心模式，支持商品购买、提现、平台消费、担保等多种业务场景。

## 核心机制

### 1. 统一流程模型

所有资金操作都遵循以下统一流程：

```
用户发起操作 → 检查余额 → 冻结资金 → 等待状态确认 → 执行划扣 → 完成交易
```

### 2. 状态流转

- **FROZEN**: 资金已冻结，等待状态确认
- **CONFIRMED**: 状态已确认，可以进行划扣  
- **SETTLED**: 已完成划扣
- **CANCELLED**: 已取消，资金解冻

### 3. 业务场景支持

- **PURCHASE**: 商品购买
- **WITHDRAWAL**: 提现
- **CONSUMPTION**: 平台消费
- **GUARANTEE**: 担保

## 数据模型

### FundFreeze (资金冻结记录)

```sql
CREATE TABLE FundFreeze (
  id            TEXT PRIMARY KEY,
  userId        TEXT NOT NULL,           -- 用户ID
  amount        REAL NOT NULL,           -- 冻结金额
  purpose       TEXT NOT NULL,           -- 冻结目的
  relatedId     TEXT,                    -- 关联业务ID
  status        TEXT DEFAULT 'FROZEN',   -- 状态
  toUserId      TEXT,                    -- 划扣目标用户
  platformFee   REAL DEFAULT 0,          -- 平台手续费
  actualAmount  REAL,                    -- 实际划扣金额
  createdAt     DATETIME DEFAULT NOW(),
  confirmedAt   DATETIME,
  settledAt     DATETIME,
  cancelledAt   DATETIME,
  metadata      JSON,                    -- 扩展信息
  notes         TEXT                     -- 备注
);
```

### Withdrawal (提现记录)

```sql
CREATE TABLE Withdrawal (
  id            TEXT PRIMARY KEY,
  userId        TEXT NOT NULL,
  amount        REAL NOT NULL,
  walletAddress TEXT NOT NULL,
  withdrawalFee REAL DEFAULT 0,
  actualAmount  REAL NOT NULL,
  status        TEXT DEFAULT 'PENDING',
  reviewedBy    TEXT,
  processedBy   TEXT,
  txHash        TEXT,
  createdAt     DATETIME DEFAULT NOW()
);
```

## 核心API

### 资金流动服务 (lib/fund-flow.ts)

```typescript
// 冻结资金
async function freezeFunds(params: FreezeFundsParams): Promise<string>

// 确认状态
async function confirmStatus(freezeId: string, confirmedBy?: string): Promise<void>

// 执行划扣
async function settleFunds(params: SettleFundsParams): Promise<void>

// 取消冻结
async function cancelFreeze(freezeId: string, reason?: string): Promise<void>

// 查询可用余额
async function getAvailableBalance(userId: string): Promise<number>
```

### 业务适配器

#### 商品购买适配器 (lib/adapters/purchase-adapter.ts)

```typescript
// 启动购买流程
async function initiatePurchase(params: PurchaseParams): Promise<PurchaseFlowResult>

// 确认支付
async function confirmPurchasePayment(orderId: string): Promise<PurchaseFlowResult>

// 确认收货
async function confirmPurchaseDelivery(orderId: string): Promise<PurchaseFlowResult>

// 取消购买
async function cancelPurchase(orderId: string, reason: string): Promise<PurchaseFlowResult>
```

#### 提现适配器 (lib/adapters/withdrawal-adapter.ts)

```typescript
// 创建提现申请
async function createWithdrawal(params: WithdrawalParams): Promise<WithdrawalResult>

// 审核提现
async function reviewWithdrawal(withdrawalId: string, approved: boolean): Promise<WithdrawalResult>

// 处理提现
async function processWithdrawal(withdrawalId: string, txHash: string): Promise<WithdrawalResult>
```

## REST API 接口

### 用户资金管理

```
GET  /api/funds/balance          # 查询资金余额
POST /api/funds/balance          # 充值担保金
GET  /api/funds/withdrawal       # 查询提现记录
POST /api/funds/withdrawal       # 创建提现申请
DELETE /api/funds/withdrawal     # 取消提现申请
```

### 管理员接口

```
GET  /api/admin/withdrawals      # 查询待审核提现
POST /api/admin/withdrawals      # 审核/处理提现
```

## 业务场景示例

### 1. 商品购买流程

```typescript
// 1. 用户点击购买
const result = await initiatePurchase({
  userId: 'user123',
  orderId: 'order456', 
  totalAmount: 105,
  sellerId: 'seller789'
})

// 2. 用户完成支付
await confirmPurchasePayment('order456')

// 3. 用户确认收货
await confirmPurchaseDelivery('order456', 0.05) // 5% 平台费
```

### 2. 提现流程

```typescript
// 1. 用户申请提现
const result = await createWithdrawal({
  userId: 'user123',
  amount: 100,
  walletAddress: '0x...'
})

// 2. 管理员审核
await reviewWithdrawal(result.withdrawalId, true, 'admin123')

// 3. 管理员处理转账
await processWithdrawal(result.withdrawalId, 'txhash123', 'admin123')
```

## 安全特性

### 1. 事务保证
- 所有资金操作都在数据库事务中执行
- 确保数据一致性和原子性

### 2. 余额验证
- 冻结前检查用户可用余额
- 防止超额冻结

### 3. 状态控制
- 严格的状态流转控制
- 防止非法状态变更

### 4. 审计跟踪
- 完整的操作日志记录
- 支持资金流向追踪

## 监控和管理

### 1. 资金统计
- 按用途分类统计
- 实时余额监控
- 异常交易告警

### 2. 管理界面
- 用户资金概览
- 冻结记录查询
- 提现审核管理

### 3. 报表功能
- 资金流动报表
- 平台费用统计
- 用户行为分析

## 测试验证

系统包含完整的测试用例：

```bash
# 运行资金流动测试
node scripts/test-fund-flow.js
```

测试覆盖：
- ✅ 用户创建和余额管理
- ✅ 商品购买完整流程
- ✅ 提现申请和冻结
- ✅ 余额查询和统计

## 扩展性

### 1. 新业务场景
- 通过适配器模式轻松添加新场景
- 复用核心资金流动逻辑

### 2. 自定义规则
- 支持业务特定的费率设置
- 灵活的状态确认机制

### 3. 第三方集成
- 标准化的API接口
- 支持外部系统集成

## 部署说明

### 1. 数据库迁移
```bash
npx prisma migrate deploy
```

### 2. 环境配置
- 确保数据库连接正常
- 配置相关环境变量

### 3. 权限设置
- 管理员角色配置
- API访问权限控制

---

该系统为平台提供了统一、安全、可扩展的资金管理能力，大大简化了各种业务场景的资金处理逻辑。
