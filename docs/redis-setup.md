# Redis 配置指南

## 🚀 当前状态

您的应用目前使用**内存缓存**作为回退方案，这意味着：
- ✅ 应用正常运行，无Redis连接错误
- ✅ 缓存功能正常工作（使用内存）
- ⚠️ 缓存数据在应用重启后会丢失
- ⚠️ 无法在多个应用实例间共享缓存

## 📋 Redis 安装和配置（可选）

如果您想获得更好的缓存性能，可以安装和配置Redis：

### Windows 安装

#### 方法1：使用 Windows Subsystem for Linux (WSL)
```bash
# 安装WSL2
wsl --install

# 在WSL中安装Redis
sudo apt update
sudo apt install redis-server

# 启动Redis
sudo service redis-server start

# 测试Redis
redis-cli ping
```

#### 方法2：使用 Docker
```bash
# 安装Docker Desktop
# 然后运行Redis容器
docker run -d --name redis -p 6379:6379 redis:latest

# 测试连接
docker exec -it redis redis-cli ping
```

#### 方法3：使用 Redis for Windows
```bash
# 下载并安装 Redis for Windows
# https://github.com/microsoftarchive/redis/releases

# 或使用 Chocolatey
choco install redis-64
```

### 配置环境变量

安装Redis后，在您的 `.env` 文件中添加：

```env
# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379
```

### 验证Redis连接

```bash
# 运行健康检查
npm run health:check

# 或者测试缓存功能
npm run cache:clear --stats
```

## 🔄 切换回Redis缓存

如果您已经安装了Redis，可以通过以下步骤切换回Redis缓存：

### 1. 更新导入语句

将以下文件中的导入从 `cache-fallback` 改回 `cache`：

```javascript
// 从
import { cache } from '@/lib/cache-fallback'

// 改为
import { cache } from '@/lib/cache'
```

需要更新的文件：
- `app/api/products/route.ts`
- `lib/api-cache-middleware.ts`
- `app/api/health/route.ts`
- `scripts/performance-setup.js`
- `scripts/health-check.js`
- `scripts/clear-cache.js`

### 2. 重启应用

```bash
npm run dev:optimized
```

## 🔧 Redis 配置优化

### 基础配置
```env
# 基础Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password_here
REDIS_DB=0
```

### 高级配置
```env
# 连接池配置
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100
REDIS_CONNECT_TIMEOUT=10000

# 性能优化
REDIS_LAZY_CONNECT=true
REDIS_KEEP_ALIVE=true
REDIS_FAMILY=4
```

### 生产环境配置
```env
# 生产环境Redis配置
REDIS_URL=redis://username:password@hostname:port/database
REDIS_TLS=true
REDIS_CLUSTER=true
```

## 📊 性能对比

| 特性 | 内存缓存 | Redis缓存 |
|------|----------|-----------|
| 性能 | 极快 | 快 |
| 持久化 | ❌ | ✅ |
| 多实例共享 | ❌ | ✅ |
| 内存使用 | 应用内存 | 独立进程 |
| 集群支持 | ❌ | ✅ |
| 数据类型 | 基础 | 丰富 |

## 🛠️ 故障排除

### 常见问题

#### 1. Redis连接失败
```bash
# 检查Redis是否运行
redis-cli ping

# 检查端口是否开放
netstat -an | findstr :6379

# 重启Redis服务
sudo service redis-server restart
```

#### 2. 权限问题
```bash
# 检查Redis配置
redis-cli config get "*"

# 设置密码（如果需要）
redis-cli config set requirepass yourpassword
```

#### 3. 内存不足
```bash
# 检查Redis内存使用
redis-cli info memory

# 设置最大内存
redis-cli config set maxmemory 256mb
redis-cli config set maxmemory-policy allkeys-lru
```

### 监控命令

```bash
# 监控Redis状态
redis-cli monitor

# 查看连接信息
redis-cli client list

# 查看键统计
redis-cli info keyspace

# 查看性能统计
redis-cli info stats
```

## 🎯 推荐配置

### 开发环境
- 使用内存缓存（当前配置）
- 简单快速，无需额外设置

### 测试环境
- 使用本地Redis
- 测试缓存功能和性能

### 生产环境
- 使用Redis集群
- 配置持久化和备份
- 启用监控和告警

## 📚 相关文档

- [Redis官方文档](https://redis.io/documentation)
- [ioredis客户端文档](https://github.com/luin/ioredis)
- [Docker Redis镜像](https://hub.docker.com/_/redis)

---

**注意**: 当前应用使用内存缓存完全正常工作，Redis是可选的性能优化。
