# 快速启动指南

## 🚀 快速开始

### 1. 环境配置

确保您的 `.env` 文件包含以下必需的环境变量：

```env
# 数据库配置
DATABASE_URL="file:./prisma/dev.db"

# NextAuth 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# JWT 密钥
JWT_SECRET="your-jwt-secret-key-here"

# 开发环境
NODE_ENV="development"
```

### 2. 安装依赖

```bash
npm install
```

### 3. 数据库设置

```bash
# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移（如果需要）
npx prisma db push
```

### 4. 启动应用

#### 基础启动（推荐用于开发）
```bash
npm run dev
```

#### 优化启动（包含性能监控）
```bash
npm run dev:optimized
```

#### 生产环境启动
```bash
npm run build
npm run start
```

## 🔧 故障排除

### 常见问题

#### 1. 模块找不到错误
如果遇到 `Cannot find module` 错误：

```bash
# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 2. 数据库连接错误
确保数据库文件存在：

```bash
# 检查数据库文件
ls -la prisma/dev.db

# 如果不存在，创建数据库
npx prisma db push
```

#### 3. TypeScript 错误
运行类型检查：

```bash
npm run type:check
```

#### 4. 环境变量问题
检查 `.env` 文件是否存在且配置正确：

```bash
# 复制示例文件
cp .env.example .env

# 编辑环境变量
nano .env
```

### 性能优化（可选）

#### 启用 Redis 缓存
```env
# 添加到 .env 文件
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_URL="redis://localhost:6379"
```

#### 启用集群模式（生产环境）
```env
USE_CLUSTER="true"
```

## 📋 开发工具

### 有用的命令

```bash
# 类型检查
npm run type:check

# 健康检查
npm run health:check

# 数据库优化分析
npm run db:optimize

# 验证认证修复
npm run verify:auth-fix

# 缓存管理
npm run cache:clear --stats
```

### 开发服务器选项

```bash
# 标准开发模式
npm run dev

# 使用 Turbopack（更快的编译）
npm run dev:next

# 带性能监控的开发模式
npm run dev:optimized

# 快速开发模式（实验性HTTPS）
npm run dev:fast
```

## 🌐 访问应用

启动成功后，访问：
- **应用主页**: http://localhost:3000
- **健康检查**: http://localhost:3000/api/health
- **API文档**: http://localhost:3000/api

## 📚 更多文档

- [性能优化指南](./performance-optimization.md)
- [TypeScript修复报告](./typescript-fixes.md)
- [数据库优化报告](./database-optimization-report.md)

## 🆘 获取帮助

如果遇到问题：

1. 检查控制台错误信息
2. 运行健康检查：`npm run health:check`
3. 查看日志文件：`logs/` 目录
4. 参考故障排除部分

---

**提示**: 首次启动建议使用 `npm run dev` 进行基础开发，等熟悉后再启用性能优化功能。
