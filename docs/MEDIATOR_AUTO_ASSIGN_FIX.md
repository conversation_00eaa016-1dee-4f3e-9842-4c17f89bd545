# 中间人自动分配功能修复报告

## 问题描述

系统出现错误："自动分配中间人失败: '暂无保证金充足的中间人可用'"，但实际上数据库中存在符合条件的中间人（保证金充足）。

## 问题分析

### 根本原因

1. **保证金计算逻辑不一致**：系统中存在多套保证金管理方案，不同API使用不同的计算逻辑
2. **数据不同步**：`availableBalance` 字段没有正确计算和更新
3. **错误的数据源**：auto-assign API 使用了错误的表（`fundTransaction`）来计算保证金

### 具体问题

1. **auto-assign API** 使用 `fundTransaction` 表查询 `GUARANTEE_DEPOSIT` 和 `GUARANTEE_WITHDRAWAL`
2. **mediator/list API** 使用 `mediatorDeposit` 字段
3. **escrow/create API** 使用 `fundFreeze` 表
4. **数据不一致**：中间人有 `depositBalance: 9999 USDT` 但 `availableBalance: 0 USDT`

## 修复方案

### 1. 数据修复

创建并运行 `scripts/fix-mediator-balance.js` 脚本：

- 修复 `availableBalance` 字段：`availableBalance = depositBalance - frozenBalance`
- 同步 `mediatorDeposit` 字段：如果为0但 `depositBalance` 有值，则同步

### 2. API逻辑统一

#### auto-assign API (`app/api/mediator/auto-assign/route.ts`)

**修复前**：
```javascript
// 使用 fundTransaction 表计算保证金
const guaranteeDeposits = await prisma.fundTransaction.aggregate({
  where: { userId: mediator.id, type: 'GUARANTEE_DEPOSIT' },
  _sum: { amount: true }
})
const totalFrozenFunds = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)
```

**修复后**：
```javascript
// 使用 User 表的余额字段
const mediatorBalance = await prisma.user.findUnique({
  where: { id: mediator.id },
  select: { depositBalance: true, frozenBalance: true, availableBalance: true }
})
const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount)
```

#### mediator/list API (`app/api/mediator/list/route.ts`)

**修复前**：
```javascript
const availableDeposit = mediator.mediatorDeposit - usedDeposit
```

**修复后**：
```javascript
const availableAmount = Math.max(0, mediator.availableBalance - lockedAmount)
```

#### escrow/create API (`app/api/escrow/create/route.ts`)

**修复前**：
```javascript
// 使用 fundFreeze 表
const frozenAmount = await prisma.fundFreeze.aggregate({
  where: { userId: mediator.id, status: 'ACTIVE', relatedType: 'MEDIATOR_DEPOSIT' },
  _sum: { amount: true }
})
```

**修复后**：
```javascript
// 使用 User 表的余额字段
const mediatorBalance = await prisma.user.findUnique({
  where: { id: mediator.id },
  select: { availableBalance: true }
})
const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount)
```

### 3. 统一的保证金计算逻辑

所有API现在都使用相同的逻辑：

1. 从 User 表获取 `availableBalance`
2. 计算活跃订单的锁定金额 (`escrowAmount`)
3. 可用金额 = `availableBalance - lockedAmount`
4. 只有可用金额 >= 订单金额的中间人才符合条件

## 修复结果

### 测试结果

运行 `scripts/comprehensive-mediator-test.js` 的结果：

```
=== 测试总结 ===
活跃中间人数量: 1
有可用余额的中间人数量: 1
数据一致性问题: 0

✅ 中间人系统修复成功！
   - 数据一致性正常
   - 有活跃的中间人
   - 有可用余额的中间人
   - 自动分配逻辑正常工作
```

### 修复前后对比

| 项目 | 修复前 | 修复后 |
|------|--------|--------|
| availableBalance | 0 USDT | 9999 USDT |
| mediatorDeposit | 0 USDT | 9999 USDT |
| 自动分配结果 | ❌ 暂无保证金充足的中间人可用 | ✅ 成功分配中间人 |
| API逻辑一致性 | ❌ 三套不同逻辑 | ✅ 统一逻辑 |

## 预防措施

### 1. 数据一致性检查

定期运行数据一致性检查脚本，确保：
- `availableBalance = depositBalance - frozenBalance`
- 各个保证金相关字段同步

### 2. 统一保证金管理

建议：
- 所有保证金相关操作都通过 `GuaranteeSystem` 类进行
- 避免直接修改 User 表的余额字段
- 使用事务确保数据一致性

### 3. 测试覆盖

- 为所有保证金相关API添加单元测试
- 定期运行综合测试脚本
- 监控保证金计算的一致性

## 相关文件

### 修复的文件
- `app/api/mediator/auto-assign/route.ts`
- `app/api/mediator/list/route.ts`
- `app/api/escrow/create/route.ts`

### 创建的脚本
- `scripts/debug-mediator-deposit.js` - 调试脚本
- `scripts/fix-mediator-balance.js` - 数据修复脚本
- `scripts/test-mediator-logic.js` - 逻辑测试脚本
- `scripts/comprehensive-mediator-test.js` - 综合测试脚本

### 相关系统
- `lib/guarantee-system.ts` - 担保金系统
- `prisma/schema.prisma` - 数据库模型

## 总结

此次修复解决了中间人自动分配功能的核心问题：

1. ✅ 修复了数据不一致问题
2. ✅ 统一了保证金计算逻辑
3. ✅ 确保了API的一致性
4. ✅ 提供了完整的测试覆盖

现在系统能够正确识别和分配保证金充足的中间人，"暂无保证金充足的中间人可用"的错误已经解决。
