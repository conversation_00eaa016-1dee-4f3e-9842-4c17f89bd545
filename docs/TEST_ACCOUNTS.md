# 🧪 BitMarket 测试账号信息

## 📋 账号概览

为了便于开发和测试，我们已经创建了3个测试账号，包括1个管理员账号和2个普通用户账号。

## 👑 管理员账号

### 系统管理员
- **邮箱**: `<EMAIL>`
- **密码**: `admin123456`
- **用户ID**: `admin-001`
- **角色**: ADMIN
- **信用分数**: 100
- **保证金余额**: 10,000 USDT
- **信用等级**: DIAMOND
- **信用积分**: 1,000
- **担保人资格**: 是
- **账号状态**: 激活

**权限说明**:
- 可以访问管理后台
- 可以管理所有用户
- 可以审核交易和争议
- 可以调整用户信用分数
- 可以管理系统设置

## 👤 普通用户账号

### 用户1 - 张三
- **邮箱**: `zhang<PERSON>@example.com`
- **密码**: `user123456`
- **用户ID**: `user-001`
- **角色**: USER
- **信用分数**: 85
- **保证金余额**: 1,000 USDT
- **信用等级**: GOLD
- **信用积分**: 500
- **所在地区**: 北京市 朝阳区
- **担保人资格**: 否
- **账号状态**: 激活

### 用户2 - 李四
- **邮箱**: `<EMAIL>`
- **密码**: `user123456`
- **用户ID**: `user-002`
- **角色**: USER
- **信用分数**: 75
- **保证金余额**: 500 USDT
- **信用等级**: SILVER
- **信用积分**: 300
- **所在地区**: 上海市 浦东新区
- **担保人资格**: 否
- **账号状态**: 激活

## 🔧 账号管理

### 创建测试账号
```bash
# 创建测试账号
npm run test:accounts

# 重置数据库并创建测试账号
npm run test:accounts:reset
```

### 验证账号
```bash
# 查看数据库中的用户
docker-compose exec mysql mysql -u bitmarket_user -pbitmarket_pass_2024 bitmarket -e "SELECT userId, email, role, creditScore FROM User;"

# 查看信用记录
docker-compose exec mysql mysql -u bitmarket_user -pbitmarket_pass_2024 bitmarket -e "SELECT * FROM CreditHistory;"
```

## 🎯 测试场景

### 管理员测试场景
1. **登录管理后台**
   - 使用管理员账号登录
   - 验证管理面板访问权限
   - 测试用户管理功能

2. **用户管理**
   - 查看用户列表
   - 编辑用户信息
   - 调整用户信用分数
   - 冻结/解冻用户账号

3. **交易管理**
   - 查看所有交易
   - 处理争议
   - 审核交易

### 用户测试场景
1. **账号功能**
   - 用户注册/登录
   - 个人信息管理
   - 密码修改
   - 邮箱验证

2. **交易功能**
   - 发布买单/卖单
   - 搜索和筛选订单
   - 发起交易
   - 完成交易流程

3. **信用系统**
   - 查看信用分数
   - 信用记录查询
   - 信用等级权益

4. **保证金系统**
   - 充值保证金
   - 提取保证金
   - 保证金冻结/解冻

## 🔒 安全注意事项

### 开发环境
- ✅ 所有账号仅用于开发和测试
- ✅ 密码为明文，便于测试
- ✅ 邮箱已预设为验证状态
- ✅ 信用分数已初始化

### 生产环境
- ⚠️ **必须删除所有测试账号**
- ⚠️ **必须使用强密码**
- ⚠️ **必须启用邮箱验证流程**
- ⚠️ **必须重新配置管理员账号**

## 📊 账号数据结构

### 用户表字段
```sql
- id: 主键ID
- userId: 用户唯一标识
- name: 用户姓名
- email: 邮箱地址
- password: 加密密码
- role: 用户角色 (ADMIN/USER)
- creditScore: 信用分数
- depositBalance: 保证金余额
- creditLevel: 信用等级
- creditPoints: 信用积分
- city: 城市
- district: 区域
- isGuarantor: 是否为担保人
- status: 账号状态
- emailVerified: 邮箱验证时间
- createdAt: 创建时间
- updatedAt: 更新时间
```

### 信用记录表字段
```sql
- id: 主键ID
- userId: 用户ID
- changeType: 变更类型
- changeScore: 变更分数
- reason: 变更原因
- beforeScore: 变更前分数
- afterScore: 变更后分数
- metadata: 元数据
- createdAt: 创建时间
```

## 🚀 快速开始

### 1. 启动服务
```bash
# 启动 Docker 数据库
npm run docker:start

# 启动应用程序
npm run dev
```

### 2. 创建测试账号
```bash
npm run test:accounts
```

### 3. 访问应用
- **应用地址**: http://localhost:3000
- **管理员登录**: <EMAIL> / admin123456
- **用户登录**: <EMAIL> / user123456

## 🔗 相关文档

- [Docker 环境设置](./DOCKER_SETUP_GUIDE.md)
- [开发环境搭建](./deployment/development-setup.md)
- [API 文档](./api-documentation.md)
- [数据库设计](./database-schema.md)

---

*最后更新: 2025-07-25*  
*维护团队: BitMarket Development Team*
