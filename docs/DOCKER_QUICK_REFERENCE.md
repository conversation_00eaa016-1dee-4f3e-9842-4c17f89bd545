# 🐳 BitMarket Docker 快速参考

## 🚀 快速开始 (3 步)

```bash
# 1. 启动 Docker Desktop
# 2. 设置数据库环境
npm run docker:setup

# 3. 配置并启动应用
cp .env.docker .env.local && npm run dev
```

## 📝 常用命令

### 🔧 环境设置
```bash
npm run docker:setup          # 设置 MySQL 数据库
npm run docker:setup:redis    # 设置 MySQL + Redis
npm run docker:test           # 验证配置
```

### 🎮 服务管理
```bash
npm run docker:start          # 启动服务
npm run docker:stop           # 停止服务
npm run docker:restart        # 重启服务
npm run docker:status         # 查看状态
npm run docker:logs           # 查看日志
```

### 🗃️ 数据库操作
```bash
npm run docker:shell          # 进入 MySQL
npm run docker:migrate        # 运行迁移
npm run docker:backup         # 备份数据
npm run docker:reset          # 重置数据库
```

### 🧹 维护
```bash
npm run docker:clean          # 清理资源
```

## 🔧 配置信息

### 数据库连接
```env
DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"
```

### 服务端口
- **MySQL**: 3306
- **Redis**: 6379 (可选)
- **应用**: 3000

## 🚨 故障排除

### Docker daemon 未运行
```bash
# 启动 Docker Desktop 应用程序
```

### 端口冲突
```bash
# 检查端口占用
lsof -i :3306

# 修改 .env.docker 中的端口
MYSQL_PORT=3307
```

### 权限问题
```bash
# 添加用户到 docker 组
sudo usermod -aG docker $USER
newgrp docker
```

## 📚 完整文档

- [完整设置指南](./DOCKER_SETUP_GUIDE.md)
- [技术文档](./deployment/docker-database-guide.md)
- [配置报告](../test-results/DOCKER_CONFIGURATION_REPORT.md)

---

*快速参考 | BitMarket v1.3.2*
