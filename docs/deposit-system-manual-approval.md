# 充值系统 - 管理员手动确认流程

## 概述

本系统实现了一个需要管理员手动确认的充值流程，确保资金安全和合规性。用户提交充值申请后，需要管理员确认到账才能添加保证金。

## 系统架构

### 1. 用户充值流程

1. **提交申请**: 用户在 `/deposit` 页面选择支付方式并提交充值申请
2. **获取支付信息**: 系统返回相应的支付信息（钱包地址、银行账户等）
3. **完成支付**: 用户按照支付信息完成转账
4. **等待确认**: 申请状态为 `PENDING_APPROVAL`，等待管理员确认
5. **余额更新**: 管理员确认后，保证金自动添加到用户账户

### 2. 管理员审核流程

1. **查看申请**: 在 `/admin/deposits` 页面查看待审核的充值申请
2. **验证到账**: 根据支付方式验证资金是否到账
3. **确认操作**: 点击"确认到账"或"拒绝申请"
4. **自动处理**: 系统自动更新用户余额并记录操作历史

## 支持的支付方式

### 1. 链上转账 (chain)
- **货币**: USDT-TRC20
- **钱包地址**: TYQEuRHZxZvKJT8PnM8aCdNKhgeVLSbPAU
- **确认方式**: 管理员通过区块链浏览器验证交易

### 2. 币安支付 (binance_qr)
- **方式**: 扫码支付
- **货币**: USDT
- **确认方式**: 管理员通过币安后台验证订单

### 3. 银行转账 (bank_transfer)
- **银行**: 中国银行
- **账户**: ****************
- **户名**: BitMarket平台
- **货币**: CNY
- **确认方式**: 管理员通过银行流水验证

### 4. 人工处理 (manual)
- **方式**: 联系客服
- **确认方式**: 线下确认

## 数据库结构

### DepositRecord 表
```sql
- id: 充值记录ID
- userId: 用户ID
- amount: 充值金额
- originalAmount: 原始金额
- method: 支付方式
- status: 状态 (PENDING_APPROVAL, COMPLETED, REJECTED)
- txHash: 交易哈希（可选）
- notes: 备注信息
- metadata: 元数据（包含支付信息）
- createdAt: 创建时间
- updatedAt: 更新时间
```

### 状态说明
- `PENDING_APPROVAL`: 等待管理员确认
- `COMPLETED`: 已完成，余额已更新
- `REJECTED`: 已拒绝

## API 接口

### 用户接口

#### POST /api/funds/deposit
提交充值申请

**请求参数:**
```json
{
  "amount": 100,
  "method": "chain",
  "notes": "充值备注",
  "requireManualApproval": true
}
```

**响应:**
```json
{
  "success": true,
  "message": "充值申请已提交，请按照支付信息完成转账，管理员确认到账后将自动添加保证金",
  "depositId": "deposit_id",
  "amount": 100,
  "method": "chain",
  "status": "PENDING_APPROVAL",
  "walletAddress": "TYQEuRHZxZvKJT8PnM8aCdNKhgeVLSbPAU",
  "network": "TRC20",
  "currency": "USDT"
}
```

#### GET /api/funds/deposit
获取充值记录

### 管理员接口

#### GET /api/admin/deposits
获取充值申请列表

#### POST /api/admin/deposits/[id]/approve
审核充值申请

**请求参数:**
```json
{
  "action": "approve", // 或 "reject"
  "notes": "确认到账"
}
```

## 前端页面

### 用户页面 (/deposit)
- 支付方式选择
- 充值金额输入
- 支付信息显示
- 历史记录查看

### 管理员页面 (/admin/deposits)
- 充值申请列表
- 审核操作界面
- 操作历史记录

## 安全特性

1. **双重确认**: 用户提交 + 管理员确认
2. **操作记录**: 所有操作都有详细日志
3. **状态追踪**: 完整的状态流转记录
4. **权限控制**: 只有管理员可以确认充值

## 使用说明

### 用户操作
1. 访问 `/deposit` 页面
2. 选择"充值"标签页
3. 选择支付方式
4. 输入充值金额
5. 提交申请并按照提示完成支付
6. 在"历史记录"标签页查看申请状态

### 管理员操作
1. 访问 `/admin/deposits` 页面
2. 选择"充值申请"标签页
3. 查看待处理的申请
4. 验证资金到账情况
5. 点击"确认到账"或"拒绝申请"

## 注意事项

1. 充值申请提交后状态为 `PENDING_APPROVAL`
2. 管理员确认后状态变为 `COMPLETED`，用户余额自动更新
3. 拒绝的申请状态为 `REJECTED`，不会更新余额
4. 所有操作都会记录在 `DepositOperation` 表中
5. 建议定期检查待处理的申请，及时处理用户充值

## 扩展功能

系统支持以下扩展：
- 添加新的支付方式
- 自动化验证（如API对接）
- 批量处理功能
- 通知系统集成
- 风控规则配置
