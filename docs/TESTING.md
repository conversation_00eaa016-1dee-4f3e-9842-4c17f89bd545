# BitMarket 测试文档

## 概述

BitMarket项目采用全面的测试策略，包括单元测试、集成测试、端到端测试和性能测试，确保系统的可靠性、安全性和性能。

## 测试架构

### 测试类型

1. **单元测试** - 测试独立的函数和组件
2. **集成测试** - 测试API端点和数据库交互
3. **端到端测试** - 测试完整的用户流程
4. **性能测试** - 测试系统在高负载下的表现

### 技术栈

- **测试框架**: Vitest
- **测试工具**: @testing-library/react, @testing-library/jest-dom
- **模拟工具**: Vitest mocks, @faker-js/faker
- **覆盖率**: @vitest/coverage-v8
- **CI/CD**: GitHub Actions

## 快速开始

### 安装依赖

```bash
npm install
```

### 设置测试环境

```bash
npm run test:setup
```

### 运行测试

```bash
# 运行所有测试
npm run test:all

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 运行性能测试
npm run test:performance

# 监视模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

## 测试结构

```
test/
├── setup.ts                 # 测试环境设置
├── db-mock.ts              # 数据库模拟
├── test-utils.ts           # 测试工具函数
├── factories/              # 测试数据工厂
│   └── data-factory.ts
├── api/                    # API测试
│   ├── auth.test.ts
│   ├── products.test.ts
│   ├── orders.test.ts
│   ├── messages.test.ts
│   └── upload.test.ts
├── e2e/                    # 端到端测试
│   ├── user-flow.test.ts
│   ├── admin-functions.test.ts
│   └── business-flow.test.ts
└── performance/            # 性能测试
    └── concurrent.test.ts
```

## API测试

### 认证API测试

测试用户注册、登录、Socket认证等功能：

```typescript
describe('认证API测试', () => {
  it('应该成功注册新用户', async () => {
    // 测试用户注册流程
  })
  
  it('应该拒绝重复邮箱注册', async () => {
    // 测试重复注册验证
  })
})
```

### 商品API测试

测试商品的CRUD操作、搜索、筛选等功能：

```typescript
describe('商品API测试', () => {
  it('应该返回已审核通过的商品列表', async () => {
    // 测试商品列表获取
  })
  
  it('应该支持搜索功能', async () => {
    // 测试商品搜索
  })
})
```

### 订单API测试

测试订单创建、支付、状态更新等功能：

```typescript
describe('订单API测试', () => {
  it('应该成功创建订单', async () => {
    // 测试订单创建
  })
  
  it('应该处理支付流程', async () => {
    // 测试支付处理
  })
})
```

## 端到端测试

### 用户流程测试

测试完整的用户交互流程：

```typescript
describe('用户功能端到端测试', () => {
  it('应该完成完整的注册流程', async () => {
    // 测试用户注册到登录的完整流程
  })
  
  it('应该能够浏览和购买商品', async () => {
    // 测试商品浏览到购买的完整流程
  })
})
```

### 管理员功能测试

测试管理员权限和功能：

```typescript
describe('管理员功能测试', () => {
  it('应该能够审核商品', async () => {
    // 测试商品审核流程
  })
  
  it('应该能够管理用户', async () => {
    // 测试用户管理功能
  })
})
```

## 性能测试

### 并发测试

测试系统在高并发情况下的表现：

```typescript
describe('性能和并发测试', () => {
  it('应该处理大量并发商品查询', async () => {
    // 测试并发查询性能
  })
  
  it('应该处理并发订单创建', async () => {
    // 测试并发订单创建
  })
})
```

## 测试数据

### 使用数据工厂

项目提供了完整的测试数据工厂：

```typescript
import { UserFactory, ProductFactory, OrderFactory } from '@/test/factories/data-factory'

// 创建测试用户
const buyer = UserFactory.createBuyer()
const seller = UserFactory.createSeller()
const admin = UserFactory.createAdmin()

// 创建测试商品
const product = ProductFactory.createAvailable()
const pendingProduct = ProductFactory.createPending()

// 创建测试订单
const order = OrderFactory.createPending()
const paidOrder = OrderFactory.createPaid()
```

### 批量数据生成

```typescript
// 生成完整的测试场景
const scenario = DataGenerator.generateCompleteUserScenario()

// 生成市场数据
const marketData = DataGenerator.generateMarketplaceData(50, 200)
```

## 模拟和Mock

### 数据库模拟

```typescript
import { mockPrisma } from '@/test/db-mock'

// 模拟数据库查询
mockPrisma.user.findUnique.mockResolvedValue(mockUser)
mockPrisma.product.findMany.mockResolvedValue(mockProducts)
```

### API模拟

```typescript
import { createMockRequest, createMockSession } from '@/test/test-utils'

// 创建模拟请求
const request = createMockRequest('POST', '/api/products', productData)

// 创建模拟会话
const session = createMockSession(mockUser)
```

## 覆盖率要求

项目设置了以下覆盖率阈值：

- **分支覆盖率**: 80%
- **函数覆盖率**: 80%
- **行覆盖率**: 80%
- **语句覆盖率**: 80%

## CI/CD集成

### GitHub Actions

项目配置了完整的CI/CD流程：

1. **代码检查** - ESLint和TypeScript类型检查
2. **单元测试** - 运行所有单元测试
3. **集成测试** - 测试API端点
4. **端到端测试** - 测试完整用户流程
5. **性能测试** - 定期运行性能测试
6. **安全扫描** - npm audit和Snyk扫描
7. **覆盖率报告** - 上传到Codecov

### 测试环境

- **Node.js版本**: 18.x, 20.x
- **数据库**: SQLite (测试环境)
- **缓存**: 内存模拟
- **文件存储**: 本地模拟

## 最佳实践

### 测试编写原则

1. **AAA模式** - Arrange, Act, Assert
2. **单一职责** - 每个测试只验证一个功能点
3. **独立性** - 测试之间不应相互依赖
4. **可读性** - 测试名称应清晰描述测试内容
5. **可维护性** - 使用工厂和工具函数减少重复代码

### 命名规范

```typescript
describe('功能模块名称', () => {
  describe('具体功能', () => {
    it('应该在特定条件下产生预期结果', async () => {
      // 测试实现
    })
  })
})
```

### 错误处理测试

```typescript
it('应该处理数据库连接错误', async () => {
  mockPrisma.user.findUnique.mockRejectedValue(new Error('Database error'))
  
  const response = await request
  expect(response.status).toBe(500)
  expect(response.json()).resolves.toHaveProperty('error')
})
```

## 故障排除

### 常见问题

1. **测试超时** - 增加测试超时时间或优化测试逻辑
2. **Mock未生效** - 检查Mock的导入顺序和配置
3. **数据库连接失败** - 确保测试数据库配置正确
4. **覆盖率不足** - 添加缺失的测试用例

### 调试技巧

```typescript
// 使用console.log调试
it('调试测试', async () => {
  console.log('Request data:', requestData)
  const response = await makeRequest()
  console.log('Response:', await response.json())
})

// 使用vitest的调试功能
npm run test:watch -- --reporter=verbose
```

## 贡献指南

### 添加新测试

1. 在相应目录下创建测试文件
2. 使用现有的工具函数和工厂
3. 遵循命名规范和最佳实践
4. 确保测试覆盖率达标
5. 更新相关文档

### 测试审查清单

- [ ] 测试名称清晰描述功能
- [ ] 使用AAA模式组织测试
- [ ] 包含正常和异常情况
- [ ] Mock和断言正确
- [ ] 测试独立且可重复运行
- [ ] 覆盖率达到要求

## 参考资料

- [Vitest官方文档](https://vitest.dev/)
- [Testing Library文档](https://testing-library.com/)
- [Faker.js文档](https://fakerjs.dev/)
- [GitHub Actions文档](https://docs.github.com/en/actions)
