# 🏗️ 项目结构 | Project Structure

## 📁 目录结构详解 | Directory Structure

```
bitmarket/
├── 📁 app/                           # Next.js 15 App Router 目录
│   ├── 📁 (auth)/                   # 认证路由组
│   │   ├── 📄 signin/page.tsx       # 登录页面
│   │   └── 📄 signup/page.tsx       # 注册页面
│   ├── 📁 admin/                    # 管理后台
│   │   ├── 📄 users/page.tsx        # 用户管理
│   │   └── 📄 layout.tsx            # 管理后台布局
│   ├── 📁 api/                      # API 路由
│   │   ├── 📁 auth/                 # 认证相关 API
│   │   ├── 📁 admin/                # 管理员 API
│   │   ├── 📁 orders/               # 订单 API
│   │   ├── 📁 products/             # 商品 API
│   │   └── 📁 user/                 # 用户 API
│   ├── 📁 banner/                   # 封禁用户页面
│   ├── 📁 chat/                     # 聊天功能
│   ├── 📁 orders/                   # 订单管理
│   ├── 📁 products/                 # 商品管理
│   ├── 📁 profile/                  # 用户中心
│   ├── 📄 globals.css               # 全局样式
│   ├── 📄 layout.tsx                # 根布局
│   └── 📄 page.tsx                  # 首页
├── 📁 components/                   # 可复用组件
│   ├── 📄 Navbar.tsx                # 导航栏组件
│   ├── 📄 Footer.tsx                # 页脚组件
│   └── 📄 ui/                       # UI 基础组件
├── 📁 lib/                          # 工具库和配置
│   ├── 📄 auth.ts                   # NextAuth 配置
│   ├── 📄 prisma.ts                 # Prisma 客户端
│   ├── 📄 utils.ts                  # 工具函数
│   ├── 📄 cache.ts                  # 缓存管理
│   └── 📄 user-status.ts            # 用户状态管理
├── 📁 prisma/                       # 数据库相关
│   ├── 📄 schema.prisma             # 数据库模型
│   ├── 📁 migrations/               # 数据库迁移
│   └── 📄 seed.ts                   # 数据填充脚本
├── 📁 public/                       # 静态资源
│   ├── 📁 images/                   # 图片资源
│   ├── 📁 icons/                    # 图标文件
│   └── 📁 uploads/                  # 用户上传文件
├── 📁 scripts/                      # 脚本工具
│   ├── 📄 health-check.js           # 健康检查脚本
│   ├── 📄 performance-setup.js      # 性能优化脚本
│   └── 📄 optimize-database.js      # 数据库优化脚本
├── 📁 docs/                         # 项目文档
│   ├── 📄 quick-start.md            # 快速开始指南
│   ├── 📄 api-reference.md          # API 接口文档
│   └── 📄 deployment/               # 部署相关文档
├── 📁 types/                        # TypeScript 类型定义
│   └── 📄 next-auth.d.ts            # NextAuth 类型扩展
├── 📄 package.json                  # 项目依赖配置
├── 📄 tsconfig.json                 # TypeScript 配置
├── 📄 tailwind.config.js            # Tailwind CSS 配置
├── 📄 next.config.ts                # Next.js 配置
├── 📄 prisma.config.js              # Prisma 配置
└── 📄 README.md                     # 项目说明文档
```

## 🔧 核心文件说明 | Core Files

### 📄 app/layout.tsx
根布局文件，定义全局布局结构：
- 全局样式导入
- 字体配置
- 元数据设置
- 全局组件包装

### 📄 app/page.tsx
应用首页，包含：
- 商品展示
- 搜索功能
- 分类导航
- 用户引导

### 📄 lib/auth.ts
NextAuth.js 配置文件：
- 认证提供商配置
- 会话管理
- 回调函数
- 数据库适配器

### 📄 lib/prisma.ts
Prisma 客户端配置：
- 数据库连接
- 连接池管理
- 开发环境优化
- 错误处理

### 📄 prisma/schema.prisma
数据库模型定义：
- 用户模型
- 商品模型
- 订单模型
- 关系定义

## 🎯 组件架构 | Component Architecture

### 🧩 组件分层
```
components/
├── 📁 ui/                    # 基础 UI 组件
│   ├── Button.tsx            # 按钮组件
│   ├── Input.tsx             # 输入框组件
│   ├── Modal.tsx             # 模态框组件
│   └── Loading.tsx           # 加载组件
├── 📁 forms/                 # 表单组件
│   ├── LoginForm.tsx         # 登录表单
│   ├── ProductForm.tsx       # 商品表单
│   └── OrderForm.tsx         # 订单表单
├── 📁 layout/                # 布局组件
│   ├── Navbar.tsx            # 导航栏
│   ├── Sidebar.tsx           # 侧边栏
│   └── Footer.tsx            # 页脚
└── 📁 features/              # 功能组件
    ├── ProductList.tsx       # 商品列表
    ├── OrderHistory.tsx      # 订单历史
    └── ChatWindow.tsx        # 聊天窗口
```

### 🔄 状态管理
- **React Hooks**: 组件内部状态
- **Context API**: 全局状态共享
- **NextAuth Session**: 用户认证状态
- **SWR/React Query**: 服务端状态管理

## 🗃️ 数据库设计 | Database Design

### 📊 核心表结构
```sql
-- 用户表
User {
  id: String (Primary Key)
  email: String (Unique)
  name: String
  status: UserStatus
  creditScore: Int
  isGuarantor: Boolean
  depositBalance: Float
}

-- 商品表
Product {
  id: String (Primary Key)
  title: String
  description: String
  price: Float
  sellerId: String (Foreign Key)
  status: ProductStatus
}

-- 订单表
Order {
  id: String (Primary Key)
  buyerId: String (Foreign Key)
  sellerId: String (Foreign Key)
  productId: String (Foreign Key)
  status: OrderStatus
  totalAmount: Float
}
```

## 🔌 API 设计 | API Design

### 🛣️ 路由结构
```
/api/
├── auth/                     # 认证相关
│   ├── signin               # 登录
│   ├── signup               # 注册
│   └── signout              # 登出
├── user/                    # 用户相关
│   ├── profile              # 用户资料
│   ├── status               # 用户状态
│   └── settings             # 用户设置
├── products/                # 商品相关
│   ├── [id]                 # 商品详情
│   ├── create               # 创建商品
│   └── search               # 搜索商品
├── orders/                  # 订单相关
│   ├── [id]                 # 订单详情
│   ├── create               # 创建订单
│   └── history              # 订单历史
└── admin/                   # 管理员相关
    ├── users                # 用户管理
    ├── orders               # 订单管理
    └── analytics            # 数据分析
```

## 🎨 样式架构 | Styling Architecture

### 🎭 Tailwind CSS 配置
- **基础样式**: 颜色、字体、间距
- **组件样式**: 可复用的样式类
- **响应式设计**: 移动端适配
- **暗色模式**: 主题切换支持

### 🖼️ 设计系统
- **颜色规范**: 主色调、辅助色、状态色
- **字体系统**: 标题、正文、代码字体
- **间距系统**: 统一的边距和内边距
- **组件规范**: 按钮、表单、卡片等

## 🔧 开发工具配置 | Development Tools

### 📝 代码质量
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查
- **Husky**: Git 钩子管理

### 🧪 测试配置
- **Jest**: 单元测试框架
- **React Testing Library**: 组件测试
- **Cypress**: 端到端测试
- **MSW**: API 模拟测试

这个项目结构设计遵循了现代 Web 应用的最佳实践，确保代码的可维护性、可扩展性和开发效率。
