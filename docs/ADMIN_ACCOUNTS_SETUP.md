# 管理员账号设置指南

## 概述

本文档介绍了比特市场系统中管理员账号的创建和管理方法。系统已成功创建了多个管理员账号，并提供了多种创建方式。

## 已创建的管理员账号

### 1. 默认管理员账号
- **邮箱**: `<EMAIL>`
- **密码**: `123456`
- **姓名**: 系统管理员
- **用户ID**: `user-a76dnjmpnx02`
- **状态**: 活跃
- **信用分**: 100
- **创建时间**: 2025/7/29 10:14:23

### 2. 自定义管理员账号
- **邮箱**: `<EMAIL>`
- **密码**: `admin123456`
- **姓名**: 比特市场管理员
- **状态**: 活跃
- **信用分**: 30
- **创建时间**: 2025/7/29 08:12:00

## 管理员创建方法

### 方法一：使用默认脚本创建
```bash
node scripts/create-admin-account.js
```
- 创建默认管理员账号 (<EMAIL>)
- 自动生成用户ID
- 默认密码: 123456

### 方法二：使用自定义脚本创建
```bash
node scripts/create-custom-admin.js
```
- 交互式创建管理员账号
- 可自定义邮箱、密码和姓名
- 支持更新现有用户为管理员

### 方法三：使用命令行参数创建
```bash
node scripts/create-admin-user.js [邮箱] [密码]
```
- 快速创建管理员账号
- 支持命令行参数
- 默认使用 <EMAIL> 和 123456

### 方法四：通过Web界面创建
访问: `http://localhost:3000/admin/setup`
- 用户友好的Web界面
- 表单验证
- 实时状态反馈

### 方法五：通过API创建
```bash
curl -X POST http://localhost:3000/api/admin/create \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "管理员"
  }'
```

## 管理员权限验证

### 检查管理员账号
```bash
node scripts/check-admin-user.js
```

### 列出所有管理员
```bash
node scripts/list-admin-accounts.js
```

### 验证管理员功能
```bash
node scripts/verify-admin-account.js
```

## 登录信息

### 登录页面
- URL: `http://localhost:3000/auth/signin`
- 支持邮箱密码登录
- 自动识别管理员权限

### 管理后台
- URL: `http://localhost:3000/admin`
- 需要管理员权限访问
- 提供完整的管理功能

## 管理员功能

### 权限控制
- 用户管理
- 订单管理
- 系统统计
- 数据管理
- 创建其他管理员

### 安全特性
- 密码加密存储 (bcrypt)
- 会话管理 (NextAuth)
- 权限验证中间件
- 邮箱验证状态

## 系统统计

- **管理员总数**: 2
- **活跃管理员**: 2
- **已验证邮箱**: 2
- **总用户数**: 2
- **管理员占比**: 100%

## 安全建议

1. **修改默认密码**: 首次登录后立即修改默认密码
2. **使用强密码**: 密码至少6位，建议包含字母、数字和特殊字符
3. **定期检查**: 定期检查管理员账号状态和权限
4. **限制数量**: 根据需要控制管理员账号数量
5. **监控登录**: 监控管理员登录活动

## 故障排除

### 常见问题

1. **邮箱已存在**
   - 使用更新功能将现有用户设为管理员
   - 或选择不同的邮箱地址

2. **密码验证失败**
   - 检查密码长度（至少6位）
   - 确认密码输入正确

3. **权限验证失败**
   - 检查用户角色是否为 'ADMIN'
   - 验证数据库连接状态

4. **数据库连接问题**
   - 确认数据库服务正在运行
   - 检查数据库连接配置

### 重置管理员权限
```bash
node scripts/simple-admin-setup.js
```

## 开发者信息

- **创建时间**: 2025年7月29日
- **版本**: v1.3.0
- **数据库**: Prisma + MySQL
- **认证系统**: NextAuth.js
- **密码加密**: bcryptjs

## 相关文件

### 脚本文件
- `scripts/create-admin-account.js` - 默认管理员创建
- `scripts/create-custom-admin.js` - 自定义管理员创建
- `scripts/create-admin-user.js` - 命令行管理员创建
- `scripts/list-admin-accounts.js` - 管理员列表
- `scripts/check-admin-user.js` - 管理员检查
- `scripts/verify-admin-account.js` - 管理员验证

### API文件
- `app/api/admin/create/route.ts` - 管理员创建API
- `app/api/admin/check/route.ts` - 管理员权限检查

### 页面文件
- `app/admin/setup/page.tsx` - 管理员设置页面
- `app/auth/signin/page.tsx` - 登录页面

### 配置文件
- `lib/auth.ts` - 认证配置
- `lib/admin.ts` - 管理员权限检查
- `prisma/schema.prisma` - 数据库模型

---

**注意**: 请妥善保管管理员账号信息，确保系统安全。
