# BitMarket 测试命令快速参考

## 基础测试命令

### 运行测试
```bash
# 运行所有测试
npm test
npm run test

# 运行单元测试
npm run test:unit

# 运行集成测试  
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 运行性能测试
npm run test:performance

# 运行所有测试套件
npm run test:all
```

### 开发模式
```bash
# 监视模式 - 文件变化时自动重新运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage

# CI模式 - 适用于持续集成环境
npm run test:ci
```

### 环境管理
```bash
# 设置测试环境
npm run test:setup

# 清理测试环境
npm run test:cleanup
```

## Vitest 直接命令

### 基本用法
```bash
# 运行所有测试
npx vitest

# 运行特定文件
npx vitest test/basic.test.ts

# 运行特定目录
npx vitest test/api/

# 运行匹配模式的测试
npx vitest --grep "商品API"
```

### 监视模式
```bash
# 启动监视模式
npx vitest --watch

# 监视特定文件
npx vitest --watch test/api/products.test.ts

# 监视模式下运行覆盖率
npx vitest --watch --coverage
```

### 覆盖率选项
```bash
# 生成覆盖率报告
npx vitest --coverage

# 指定覆盖率报告格式
npx vitest --coverage --reporter=html
npx vitest --coverage --reporter=json
npx vitest --coverage --reporter=text

# 设置覆盖率阈值
npx vitest --coverage --coverage.thresholds.lines=80
```

### 调试选项
```bash
# 详细输出
npx vitest --reporter=verbose

# 显示测试执行时间
npx vitest --reporter=verbose --reporter=json

# 运行失败的测试时停止
npx vitest --bail=1

# 设置超时时间
npx vitest --testTimeout=30000
```

## 高级测试命令

### 并行执行
```bash
# 设置最大并发数
npx vitest --threads --maxThreads=4

# 单线程模式（调试时有用）
npx vitest --threads --maxThreads=1

# 禁用并行执行
npx vitest --no-threads
```

### 过滤测试
```bash
# 按文件名过滤
npx vitest products

# 按测试名称过滤
npx vitest --grep "应该创建商品"

# 排除特定测试
npx vitest --grep "^(?!.*性能测试).*$"

# 只运行失败的测试
npx vitest --changed
```

### 输出格式
```bash
# JSON 输出
npx vitest --reporter=json --outputFile=results.json

# JUnit XML 输出
npx vitest --reporter=junit --outputFile=results.xml

# 多种报告格式
npx vitest --reporter=verbose --reporter=json --reporter=html
```

## 专用测试脚本

### 账户测试
```bash
# 创建测试账户
npm run test:accounts

# 重置并创建测试账户
npm run test:accounts:reset

# 验证测试账户
npm run test:accounts:verify

# 测试登录功能
npm run test:login
```

### 功能测试
```bash
# 测试托管系统
npm run test:escrow-system

# 测试优化功能
npm run test:optimizations

# 测试下拉菜单修复
npm run test:dropdown-fix
```

### 数据库测试
```bash
# 测试数据库迁移
npm run safe-migrate

# 优化数据库
npm run db:optimize

# 健康检查
npm run health:check
```

## Docker 环境测试

### Docker 测试命令
```bash
# 设置 Docker 测试环境
npm run docker:setup

# 启动 Docker 服务
npm run docker:start

# 运行 Docker 配置测试
npm run docker:test

# 在 Docker 中运行迁移
npm run docker:migrate

# 查看 Docker 日志
npm run docker:logs
```

## 性能和监控

### 性能测试
```bash
# 性能设置
npm run perf:setup

# 性能监控
npm run perf:monitor

# 清理缓存
npm run cache:clear

# 类型检查
npm run type:check

# 监视模式类型检查
npm run type:check:watch
```

## 测试报告和结果

### 查看测试结果
```bash
# 测试结果位置
ls test-results/
# - results.json (JSON格式结果)
# - report.html (HTML报告)
# - index.html (覆盖率报告)

# 覆盖率报告位置
ls coverage/
# - index.html (主要覆盖率报告)
# - lcov.info (LCOV格式)
# - coverage-final.json (JSON格式)
```

### 打开报告
```bash
# 在浏览器中打开覆盖率报告
open coverage/index.html

# 在浏览器中打开测试报告
open test-results/report.html
```

## 故障排除命令

### 清理和重置
```bash
# 清理所有缓存
npm run cache:clear

# 杀死占用端口的进程
npm run kill:port

# 清理并重新启动开发服务器
npm run dev:clean

# 重置数据库
npx prisma migrate reset --force
```

### 调试命令
```bash
# 详细模式运行测试
npx vitest --reporter=verbose

# 运行单个测试文件进行调试
npx vitest test/api/products.test.ts --reporter=verbose

# 使用 Node.js 调试器
node --inspect-brk ./node_modules/.bin/vitest test/api/products.test.ts
```

## 环境变量

### 测试环境变量
```bash
# 设置测试环境
export NODE_ENV=test

# 设置测试数据库
export DATABASE_URL=file:./test.db

# 设置调试模式
export DEBUG=true

# 设置详细输出
export VERBOSE=true
```

## 常用组合命令

### 完整测试流程
```bash
# 清理 -> 设置 -> 运行测试 -> 生成报告
npm run test:cleanup && npm run test:setup && npm run test:all && npm run test:coverage
```

### 快速开发测试
```bash
# 监视模式运行单元测试
npm run test:unit -- --watch
```

### CI/CD 流程
```bash
# 完整的CI测试流程
npm run type:check && npm run lint && npm run test:ci && npm run build
```

## 测试文件位置

```
test/
├── api/                 # API集成测试
├── e2e/                 # 端到端测试
├── performance/         # 性能测试
├── factories/           # 测试数据工厂
├── setup.ts            # 测试设置
├── test-utils.ts       # 测试工具
└── basic.test.ts       # 基础测试

app/                    # 应用代码中的测试
├── **/*.test.ts        # 组件和页面测试
└── **/*.spec.ts        # 规格测试

lib/                    # 库函数测试
└── **/*.test.ts        # 工具函数测试

components/             # 组件测试
└── **/*.test.tsx       # React组件测试
```

## 快速参考

| 命令 | 描述 |
|------|------|
| `npm test` | 运行所有测试 |
| `npm run test:watch` | 监视模式 |
| `npm run test:coverage` | 覆盖率报告 |
| `npm run test:unit` | 单元测试 |
| `npm run test:integration` | 集成测试 |
| `npm run test:e2e` | 端到端测试 |
| `npm run test:performance` | 性能测试 |
| `npx vitest --help` | 查看所有选项 |
