# 🔌 API 接口文档 | API Reference

## 📋 接口概览 | API Overview

比特市场提供完整的 RESTful API，支持用户管理、商品交易、订单处理等核心功能。

BitMarket provides a complete RESTful API supporting user management, product trading, order processing, and other core features.

### 🔗 基础信息 | Base Information

- **Base URL**: `https://your-domain.com/api`
- **Authentication**: NextAuth.js Session-based
- **Content Type**: `application/json`
- **Rate Limiting**: 100 requests/minute per IP

## 🔐 认证系统 | Authentication

### POST /api/auth/signin
用户登录 | User Sign In

**请求体 | Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应 | Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "name": "用户名",
    "role": "USER"
  },
  "session": {
    "expires": "2025-02-18T00:00:00.000Z"
  }
}
```

### POST /api/auth/signup
用户注册 | User Sign Up

**请求体 | Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "新用户"
}
```

## 👤 用户管理 | User Management

### GET /api/user/profile
获取用户资料 | Get User Profile

**Headers:**
```
Authorization: Bearer <session-token>
```

**响应 | Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "name": "用户名",
    "avatar": "/uploads/avatar.jpg",
    "city": "北京",
    "binanceUid": "123456789",
    "creditScore": 85,
    "isGuarantor": false,
    "depositBalance": 1000.00,
    "status": "ACTIVE"
  }
}
```

### PUT /api/user/profile
更新用户资料 | Update User Profile

**请求体 | Request Body:**
```json
{
  "name": "新用户名",
  "city": "上海",
  "binanceUid": "987654321"
}
```

### GET /api/user/status
检查用户状态 | Check User Status

**响应 | Response:**
```json
{
  "success": true,
  "status": {
    "isActive": true,
    "isBanned": false,
    "isSuspended": false,
    "banInfo": null
  }
}
```

## 📦 商品管理 | Product Management

### GET /api/products
获取商品列表 | Get Products List

**查询参数 | Query Parameters:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 商品分类
- `search`: 搜索关键词
- `minPrice`: 最低价格
- `maxPrice`: 最高价格

**响应 | Response:**
```json
{
  "success": true,
  "products": [
    {
      "id": "product-123",
      "title": "iPhone 15 Pro",
      "description": "全新未拆封",
      "price": 8999.00,
      "images": ["/uploads/iphone1.jpg"],
      "category": "电子产品",
      "seller": {
        "id": "user-456",
        "name": "卖家名称",
        "creditScore": 92
      },
      "status": "AVAILABLE",
      "createdAt": "2025-01-18T10:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### POST /api/products
发布商品 | Create Product

**请求体 | Request Body:**
```json
{
  "title": "商品标题",
  "description": "商品描述",
  "price": 999.00,
  "category": "电子产品",
  "images": ["image1.jpg", "image2.jpg"],
  "condition": "NEW",
  "location": "北京"
}
```

### GET /api/products/[id]
获取商品详情 | Get Product Details

**响应 | Response:**
```json
{
  "success": true,
  "product": {
    "id": "product-123",
    "title": "iPhone 15 Pro",
    "description": "全新未拆封，原装正品",
    "price": 8999.00,
    "images": ["/uploads/iphone1.jpg"],
    "category": "电子产品",
    "condition": "NEW",
    "location": "北京",
    "seller": {
      "id": "user-456",
      "name": "卖家名称",
      "avatar": "/uploads/seller-avatar.jpg",
      "creditScore": 92,
      "isGuarantor": false
    },
    "status": "AVAILABLE",
    "views": 156,
    "favorites": 23,
    "createdAt": "2025-01-18T10:00:00.000Z"
  }
}
```

## 📋 订单管理 | Order Management

### POST /api/orders
创建订单 | Create Order

**请求体 | Request Body:**
```json
{
  "productId": "product-123",
  "quantity": 1,
  "shippingAddress": {
    "name": "收货人",
    "phone": "13800138000",
    "address": "北京市朝阳区xxx街道xxx号",
    "zipCode": "100000"
  },
  "paymentMethod": "binance_pay",
  "useGuarantor": true
}
```

### GET /api/orders
获取订单列表 | Get Orders List

**查询参数 | Query Parameters:**
- `type`: `buy` | `sell` (买入/卖出订单)
- `status`: 订单状态
- `page`: 页码
- `limit`: 每页数量

**响应 | Response:**
```json
{
  "success": true,
  "orders": [
    {
      "id": "order-123",
      "product": {
        "id": "product-123",
        "title": "iPhone 15 Pro",
        "price": 8999.00,
        "images": ["/uploads/iphone1.jpg"]
      },
      "buyer": {
        "id": "user-789",
        "name": "买家名称"
      },
      "seller": {
        "id": "user-456",
        "name": "卖家名称"
      },
      "quantity": 1,
      "totalAmount": 8999.00,
      "status": "PENDING_PAYMENT",
      "paymentMethod": "binance_pay",
      "guarantor": {
        "id": "user-guarantor",
        "name": "中间人名称"
      },
      "createdAt": "2025-01-18T12:00:00.000Z"
    }
  ]
}
```

### PUT /api/orders/[id]
更新订单状态 | Update Order Status

**请求体 | Request Body:**
```json
{
  "status": "PAID",
  "paymentProof": {
    "txHash": "0x123456789abcdef",
    "screenshot": "/uploads/payment-proof.jpg"
  }
}
```

## 🛡️ 管理员接口 | Admin APIs

### GET /api/admin/users
获取用户列表 | Get Users List (Admin Only)

**查询参数 | Query Parameters:**
- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索用户
- `role`: 用户角色
- `status`: 用户状态

**响应 | Response:**
```json
{
  "success": true,
  "users": [
    {
      "id": "user-123",
      "email": "<EMAIL>",
      "name": "用户名",
      "role": "USER",
      "status": "ACTIVE",
      "creditScore": 85,
      "isGuarantor": false,
      "bannedAt": null,
      "bannedUntil": null,
      "createdAt": "2025-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 500,
    "totalPages": 25
  }
}
```

### PATCH /api/admin/users
管理用户操作 | User Management Actions

**请求体 | Request Body:**
```json
{
  "userId": "user-123",
  "action": "banUser",
  "data": {
    "banReason": "违反平台规定",
    "banDuration": 7
  }
}
```

**支持的操作 | Supported Actions:**
- `banUser`: 封禁用户
- `unbanUser`: 解封用户
- `deleteUser`: 删除用户
- `updateRole`: 更新用户角色

## 🔍 错误处理 | Error Handling

### 错误响应格式 | Error Response Format

```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE",
  "details": {
    "field": "具体错误字段",
    "message": "详细错误描述"
  }
}
```

### 常见错误码 | Common Error Codes

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `VALIDATION_ERROR` | 400 | 参数验证失败 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

## 📊 状态码说明 | Status Codes

### 用户状态 | User Status
- `ACTIVE`: 正常状态
- `BANNED`: 被封禁
- `SUSPENDED`: 暂停使用
- `DELETED`: 已删除

### 商品状态 | Product Status
- `AVAILABLE`: 可购买
- `SOLD`: 已售出
- `REMOVED`: 已下架
- `PENDING`: 审核中

### 订单状态 | Order Status
- `PENDING_PAYMENT`: 待付款
- `PAID`: 已付款
- `SHIPPED`: 已发货
- `DELIVERED`: 已送达
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消
- `REFUNDED`: 已退款

## 🔧 开发工具 | Development Tools

### Postman Collection
我们提供了完整的 Postman 集合文件，包含所有 API 接口的示例请求。

### API 测试
```bash
# 健康检查
curl -X GET https://your-domain.com/api/health

# 获取商品列表
curl -X GET "https://your-domain.com/api/products?page=1&limit=10"
```

### SDK 支持
- JavaScript/TypeScript SDK
- Python SDK
- PHP SDK

更多详细信息请参考我们的 [API 开发者指南](./api-developer-guide.md)。
