# BitMarket 币安PIN验证支付系统

## 📋 系统概述

BitMarket平台实现了基于PIN验证的币安支付系统，通过唯一的PIN码确保转账的安全性和可追溯性。

## 🔧 核心功能

### 1. PIN码生成
- **格式**: 8位字母数字组合 (A-Z, 0-9)
- **唯一性**: 系统确保同一时间内PIN码唯一
- **有效期**: 30分钟
- **生成时机**: 订单创建时自动生成

### 2. 支付流程
1. 用户创建订单，系统自动生成PIN码
2. 用户扫描收款二维码
3. 在转账备注中填写PIN码
4. 完成转账后输入币安订单号
5. 系统验证PIN码和订单号的一致性

### 3. 验证机制
- **PIN码验证**: 确保用户输入的PIN码与订单匹配
- **订单号验证**: 验证币安订单号格式
- **时效验证**: 检查PIN码是否在有效期内
- **使用状态**: 防止PIN码重复使用

## 🛡️ 安全特性

### 1. 防护措施
- **尝试次数限制**: 最多5次验证尝试
- **时效控制**: PIN码30分钟后自动过期
- **一次性使用**: PIN码验证成功后立即失效
- **IP记录**: 记录所有验证尝试的IP地址

### 2. 日志记录
- 所有验证尝试都会记录到数据库
- 包含IP地址、用户代理、时间戳
- 支持审计和安全分析

## 📊 数据库结构

### Order表新增字段
```sql
paymentPin        String?     -- 支付PIN码
paymentPinExpiry  DateTime?   -- PIN码过期时间
paymentPinUsed    Boolean     -- PIN码是否已使用
verificationAttempts Int      -- 验证尝试次数
lastVerificationAt DateTime?  -- 最后验证时间
```

### PaymentPinVerification表
```sql
id                String   @id @default(cuid())
orderId           String   -- 关联订单ID
submittedPin      String   -- 用户提交的PIN
submittedOrderNumber String -- 用户提交的币安订单号
isValid           Boolean  -- 验证是否成功
verificationStatus String  -- 验证状态
failureReason     String?  -- 失败原因
ipAddress         String?  -- IP地址
userAgent         String?  -- 用户代理
verifiedAt        DateTime -- 验证时间
```

## 🔌 API接口

### 1. 验证PIN码
```
POST /api/orders/{orderId}/verify-pin
```

**请求体:**
```json
{
  "pin": "ABC12345",
  "orderNumber": "BINANCE123456789"
}
```

**响应:**
```json
{
  "success": true,
  "message": "验证成功，支付已提交等待确认",
  "status": "SUCCESS"
}
```

### 2. 重新生成PIN码
```
PUT /api/orders/{orderId}/verify-pin
```

**响应:**
```json
{
  "success": true,
  "message": "PIN码已重新生成",
  "pin": "XYZ98765"
}
```

### 3. 获取PIN状态
```
GET /api/orders/{orderId}/verify-pin
```

**响应:**
```json
{
  "hasPinCode": true,
  "pinExpiry": "2025-07-17T06:30:00.000Z",
  "pinUsed": false,
  "verificationAttempts": 0,
  "maxAttempts": 5,
  "canRegenerate": true
}
```

## 🎨 前端组件

### BinancePayment组件更新
- 显示PIN码提示信息
- 两步式验证流程
- 实时状态反馈
- 错误处理和重试机制

### 支付步骤
1. 显示收款二维码和PIN码
2. 用户完成转账
3. 切换到验证界面
4. 输入PIN码和币安订单号
5. 提交验证

## 🧪 测试指南

### 运行测试脚本
```bash
node scripts/test-pin-system.js
```

### 测试覆盖
- PIN码生成和唯一性
- 验证成功和失败场景
- 时效性检查
- 重复使用防护
- 尝试次数限制

## 📈 监控和维护

### 1. 性能监控
- PIN码生成时间
- 验证响应时间
- 数据库查询性能

### 2. 安全监控
- 异常验证尝试
- IP地址分析
- 失败率统计

### 3. 数据清理
- 定期清理过期的PIN码
- 归档验证记录
- 优化数据库性能

## 🔄 升级计划

### 短期优化
- 集成币安商户API进行实时验证
- 添加OCR识别转账截图
- 实现自动化对账功能

### 长期规划
- 支持多种加密货币
- 智能风控系统
- 机器学习反欺诈

## 🚨 故障排除

### 常见问题
1. **PIN码生成失败**: 检查数据库连接和唯一性约束
2. **验证超时**: 检查网络连接和API响应时间
3. **重复验证**: 确认PIN码使用状态正确更新

### 错误代码
- `SUCCESS`: 验证成功
- `FAILED`: 系统错误
- `EXPIRED`: PIN码已过期
- `INVALID_PIN`: PIN码不正确
- `INVALID_ORDER`: 订单号格式错误
- `TOO_MANY_ATTEMPTS`: 尝试次数过多

## 📞 技术支持

如遇技术问题，请联系开发团队并提供：
- 订单ID
- 错误信息
- 操作时间
- 用户IP地址
