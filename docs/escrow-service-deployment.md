# BitMarket 托管服务部署指南

## 概述

本文档详细说明了如何部署和配置 BitMarket 的区块链中间人托管服务系统。

## 系统要求

### 硬件要求
- **CPU**: 最少 4 核，推荐 8 核
- **内存**: 最少 8GB，推荐 16GB
- **存储**: 最少 100GB SSD
- **网络**: 稳定的互联网连接，带宽 ≥ 100Mbps

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 12+
- **Node.js**: v18.0.0+
- **MySQL**: v8.0+
- **Redis**: v6.0+
- **Nginx**: v1.18+ (生产环境)

## 环境配置

### 1. 环境变量设置

创建 `.env.local` 文件：

```bash
# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/bitmarket"

# NextAuth 配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# 区块链配置
BNB_CHAIN_RPC_URL="https://bsc-dataseed1.binance.org/"
PLATFORM_WALLET_PRIVATE_KEY="your-platform-wallet-private-key"
USDT_CONTRACT_ADDRESS="******************************************"

# Redis 配置
REDIS_URL="redis://localhost:6379"

# 邮件服务配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# 文件存储配置
UPLOAD_DIR="/var/uploads"
MAX_FILE_SIZE="10485760" # 10MB

# 安全配置
ENCRYPTION_KEY="your-32-character-encryption-key"
JWT_SECRET="your-jwt-secret"

# 第三方服务
BINANCE_PAY_API_KEY="your-binance-pay-api-key"
BINANCE_PAY_SECRET="your-binance-pay-secret"

# 监控和日志
LOG_LEVEL="info"
SENTRY_DSN="your-sentry-dsn"
```

### 2. 数据库初始化

```bash
# 安装依赖
npm install

# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 初始化基础数据
npx prisma db seed
```

### 3. Redis 配置

```bash
# 安装 Redis
sudo apt update
sudo apt install redis-server

# 配置 Redis
sudo nano /etc/redis/redis.conf

# 修改以下配置
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000

# 启动 Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 部署步骤

### 1. 克隆项目

```bash
git clone https://github.com/your-org/bitmarket.git
cd bitmarket
```

### 2. 安装依赖

```bash
npm install
```

### 3. 构建项目

```bash
npm run build
```

### 4. 启动服务

#### 开发环境
```bash
npm run dev
```

#### 生产环境
```bash
# 使用 PM2 管理进程
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save
pm2 startup
```

### 5. Nginx 配置

创建 `/etc/nginx/sites-available/bitmarket` 文件：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    # SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # 文件上传限制
    client_max_body_size 10M;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket 支持
    location /socket.io/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location /_next/static/ {
        alias /path/to/bitmarket/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 上传文件
    location /uploads/ {
        alias /var/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/bitmarket /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 区块链配置

### 1. 钱包设置

```bash
# 生成平台钱包（仅在安全环境中执行）
node scripts/generate-wallet.js

# 输出示例：
# Address: ******************************************
# Private Key: 0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890
```

### 2. 智能合约部署

```bash
# 编译合约
npx hardhat compile

# 部署到 BNB Chain 测试网
npx hardhat run scripts/deploy.js --network bsc-testnet

# 部署到 BNB Chain 主网
npx hardhat run scripts/deploy.js --network bsc-mainnet
```

### 3. 合约验证

```bash
# 验证合约源码
npx hardhat verify --network bsc-mainnet CONTRACT_ADDRESS "Constructor Argument 1" "Constructor Argument 2"
```

## 监控和日志

### 1. 应用监控

```bash
# 安装监控工具
npm install -g @sentry/cli

# 配置 Sentry
sentry-cli login
sentry-cli releases new VERSION
sentry-cli releases files VERSION upload-sourcemaps .next/static
```

### 2. 系统监控

```bash
# 安装 Prometheus 和 Grafana
docker-compose -f monitoring/docker-compose.yml up -d

# 访问 Grafana
# http://localhost:3001
# 默认用户名/密码: admin/admin
```

### 3. 日志管理

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/bitmarket

# 内容：
/var/log/bitmarket/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload bitmarket
    endscript
}
```

## 安全配置

### 1. 防火墙设置

```bash
# 配置 UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL 证书

```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 数据库安全

```bash
# MySQL 安全配置
sudo mysql_secure_installation

# 创建专用数据库用户
mysql -u root -p
CREATE USER 'bitmarket'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON bitmarket.* TO 'bitmarket'@'localhost';
FLUSH PRIVILEGES;
```

## 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
cat > /opt/backup/db-backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backup/mysql"
mkdir -p $BACKUP_DIR

mysqldump -u bitmarket -p'password' bitmarket > $BACKUP_DIR/bitmarket_$DATE.sql
gzip $BACKUP_DIR/bitmarket_$DATE.sql

# 保留最近 30 天的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x /opt/backup/db-backup.sh

# 添加到 crontab
echo "0 2 * * * /opt/backup/db-backup.sh" | crontab -
```

### 2. 文件备份

```bash
# 创建文件备份脚本
cat > /opt/backup/file-backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backup/files"
mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /var/uploads/
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /opt/bitmarket/ --exclude=node_modules --exclude=.next

# 保留最近 7 天的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/backup/file-backup.sh

# 添加到 crontab
echo "0 3 * * 0 /opt/backup/file-backup.sh" | crontab -
```

## 性能优化

### 1. 数据库优化

```sql
-- MySQL 配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;

-- 创建索引
CREATE INDEX idx_escrow_status ON EscrowOrder(status);
CREATE INDEX idx_escrow_created ON EscrowOrder(createdAt);
CREATE INDEX idx_user_mediator ON User(isMediator, mediatorStatus);
```

### 2. Redis 缓存

```javascript
// 缓存配置
const cacheConfig = {
  userSession: 3600, // 1小时
  escrowData: 1800,  // 30分钟
  riskAssessment: 600, // 10分钟
  mediatorList: 300   // 5分钟
}
```

### 3. CDN 配置

```bash
# 配置 CloudFlare 或其他 CDN
# 缓存规则：
# - 静态资源: 1年
# - API 响应: 不缓存
# - 页面: 1小时
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   sudo systemctl status mysql
   
   # 检查连接
   mysql -u bitmarket -p -h localhost
   ```

2. **Redis 连接失败**
   ```bash
   # 检查 Redis 状态
   sudo systemctl status redis-server
   
   # 测试连接
   redis-cli ping
   ```

3. **区块链 RPC 连接失败**
   ```bash
   # 测试 RPC 连接
   curl -X POST -H "Content-Type: application/json" \
     --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
     https://bsc-dataseed1.binance.org/
   ```

4. **文件上传失败**
   ```bash
   # 检查目录权限
   ls -la /var/uploads/
   sudo chown -R www-data:www-data /var/uploads/
   sudo chmod -R 755 /var/uploads/
   ```

### 日志查看

```bash
# 应用日志
pm2 logs bitmarket

# Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u bitmarket -f
```

## 维护计划

### 定期维护任务

1. **每日**
   - 检查系统状态
   - 查看错误日志
   - 验证备份完成

2. **每周**
   - 更新系统补丁
   - 清理临时文件
   - 检查磁盘空间

3. **每月**
   - 数据库优化
   - 性能分析
   - 安全审计

4. **每季度**
   - 依赖包更新
   - 安全扫描
   - 灾难恢复测试

## 联系支持

如果在部署过程中遇到问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **文档**: https://docs.bitmarket.com
- **GitHub**: https://github.com/bitmarket/issues
