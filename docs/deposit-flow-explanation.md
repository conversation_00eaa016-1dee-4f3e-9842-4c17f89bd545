# 充值流程说明

## 🔄 完整充值流程

### 第一步：用户发起充值
1. 用户在充值页面选择支付方式（链上转账/币安支付）
2. 输入充值金额
3. 点击"立即充值"按钮

### 第二步：系统生成支付信息
1. 系统生成唯一的PIN码（8位字符）
2. 根据支付方式提供相应信息：
   - **链上转账**: USDT-TRC20钱包地址
   - **币安支付**: 二维码图片链接
3. PIN码有效期30分钟
4. 充值记录状态：`PENDING`（等待PIN码验证）

### 第三步：用户完成支付
1. 用户根据提供的信息完成实际支付：
   - **链上转账**: 向指定地址转账USDT，获得交易哈希
   - **币安支付**: 扫描二维码支付，获得订单号
2. 用户保存支付凭证（交易哈希或订单号）

### 第四步：PIN码验证
1. 用户在PIN码验证窗口输入：
   - 系统提供的PIN码
   - 支付凭证（交易哈希或订单号）
2. 点击"验证并提交"
3. 系统验证PIN码和支付信息

### 第五步：提交管理员审核
1. PIN码验证成功后，充值记录状态变为：`PENDING_APPROVAL`（等待管理员确认）
2. 用户看到提示："充值申请已提交，请等待管理员确认到账"
3. 此时**不会**立即更新用户余额

### 第六步：管理员审核
1. 管理员在后台看到状态为"等待确认"的申请
2. 管理员根据支付凭证验证资金是否到账：
   - **链上转账**: 通过区块链浏览器查询交易哈希
   - **币安支付**: 通过币安后台查询订单号
3. 确认到账后点击"确认到账"按钮

### 第七步：完成充值
1. 管理员确认后，充值记录状态变为：`COMPLETED`（已完成）
2. 系统自动更新用户余额
3. 记录资金变动历史
4. 用户可以在历史记录中看到充值完成

## 🔐 安全机制

### PIN码验证
- 每次充值生成唯一PIN码
- PIN码有效期30分钟
- 防止重复提交和恶意攻击

### 双重确认
- 用户提交支付凭证
- 管理员验证实际到账
- 避免虚假充值

### 状态追踪
- `PENDING`: 等待PIN码验证
- `PENDING_APPROVAL`: 等待管理员确认
- `COMPLETED`: 充值完成
- `REJECTED`: 管理员拒绝

## 💡 关键特点

1. **用户体验**: 用户先看到支付信息，完成支付后再验证
2. **安全性**: PIN码验证 + 管理员确认双重保障
3. **可追溯**: 完整的状态流转和操作记录
4. **灵活性**: 支持多种支付方式
5. **真实性**: 基于真实支付凭证的验证

## 🚫 与直接申请的区别

**错误流程（之前的实现）**:
用户输入金额 → 直接提交申请 → 等待管理员确认

**正确流程（当前实现）**:
用户输入金额 → 获取支付信息 → 完成支付 → 提交PIN码和凭证 → 等待管理员确认

这样确保了用户确实完成了支付行为，而不是空手套白狼。
