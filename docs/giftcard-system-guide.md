# 礼品卡系统功能指南

## 🎯 系统概述

礼品卡系统支持管理员管理商品和用户购买礼品卡，提供多种发货方式选择，满足不同使用场景。

## 👨‍💼 管理员功能

### 商品管理
管理员可以在充值页面的"礼品卡"标签页中管理礼品卡商品：

#### 创建商品
- **基本信息**: 商品名称、描述、类型（礼品卡/充值卡）
- **价格设置**: 面值、售价、库存数量
- **有效期**: 设置礼品卡有效天数
- **支付方式**: 选择支持的支付方式（余额/币安支付/加密货币）
- **使用条款**: 设置使用条款和注意事项
- **状态控制**: 启用/禁用商品

#### 编辑商品
- 修改商品信息、价格、库存
- 调整有效期和支付方式
- 更新使用条款
- 启用/禁用商品

#### 删除商品
- 只能删除没有关联礼品卡或订单的商品
- 系统会检查关联数据防止误删

#### 商品统计
- 查看每个商品的礼品卡生成数量
- 查看订单数量统计
- 监控商品销售情况

### 权限控制
- 只有管理员角色可以访问商品管理功能
- 普通用户只能看到购买界面

## 👤 用户功能

### 购买礼品卡
用户可以在礼品卡页面购买各种面值的礼品卡：

#### 商品选择
- 查看可用的礼品卡商品
- 对比面值和售价
- 查看折扣优惠信息

#### 数量选择
- 支持购买1-10张礼品卡
- 实时计算总价和节省金额

#### 发货方式选择
用户可以选择三种发货方式：

1. **🎫 兑换码**
   - 生成兑换码，可分享给他人使用
   - 适合转赠他人或自己保存
   - 兑换码可在平台内使用

2. **🎁 礼品卡**
   - 生成实体礼品卡，可作为礼品赠送
   - 适合作为礼品送给朋友
   - 包装精美，具有纪念价值

3. **💰 直充账号**
   - 直接充值到购买者的账户余额
   - 立即到账，无需兑换步骤
   - 适合自己使用的快速充值

### 支付方式
- **余额支付**: 使用账户余额购买
- **币安支付**: 使用币安扫码支付
- **加密货币**: 使用其他加密货币支付

## 🛒 购买流程

### 完整购买流程
1. **选择商品**: 在礼品卡列表中选择心仪的商品
2. **点击购买**: 点击"立即兑换"按钮
3. **设置数量**: 选择购买数量（1-10张）
4. **选择发货方式**: 
   - 兑换码：生成可分享的兑换码
   - 礼品卡：生成实体礼品卡
   - 直充账号：直接充值到账户
5. **确认订单**: 查看总价和节省金额
6. **完成支付**: 使用选择的支付方式付款
7. **获得商品**: 根据发货方式获得相应的商品

### 不同发货方式的结果

#### 兑换码发货
```
购买成功！
兑换码: ABC123DEF456
有效期: 2024-12-31
面值: 50 USDT
```

#### 礼品卡发货
```
购买成功！
礼品卡号: GC789012345
有效期: 2024-12-31
面值: 50 USDT
```

#### 直充账号
```
购买成功！
已直充 50 USDT 到您的账户
当前余额: 1050 USDT
```

## 📊 测试商品

系统包含以下测试商品：

| 商品名称 | 面值 | 售价 | 折扣 | 库存 | 状态 |
|---------|------|------|------|------|------|
| USDT充值卡 - 10元 | 10 USDT | 9.5 USDT | 5% | 100 | 启用 |
| USDT充值卡 - 50元 | 50 USDT | 47.5 USDT | 5% | 50 | 启用 |
| USDT充值卡 - 100元 | 100 USDT | 92 USDT | 8% | 20 | 启用 |
| 平台礼品卡 - 25元 | 25 USDT | 24 USDT | 4% | 30 | 启用 |
| 限时特惠卡 - 200元 | 200 USDT | 180 USDT | 10% | 5 | 启用 |
| 测试商品 | 5 USDT | 5 USDT | 0% | 0 | 禁用 |

## 🧪 测试方法

### 管理员测试
1. **登录管理员账号**: `<EMAIL>` / `admin123`
2. **访问礼品卡页面**: http://localhost:3001/deposit → 礼品卡标签页
3. **查看管理功能**: 应该看到蓝色的"管理员功能"区域
4. **测试商品管理**:
   - 创建新商品
   - 编辑现有商品
   - 查看商品统计
   - 启用/禁用商品

### 用户测试
1. **登录用户账号**: `<EMAIL>` / `buyer123`
2. **访问礼品卡页面**: http://localhost:3001/deposit → 礼品卡标签页
3. **测试购买流程**:
   - 选择不同面值的礼品卡
   - 尝试不同的发货方式
   - 测试数量选择功能
   - 验证支付和到账

### 发货方式测试
1. **兑换码测试**: 选择兑换码发货，查看生成的兑换码
2. **礼品卡测试**: 选择礼品卡发货，查看礼品卡信息
3. **直充测试**: 选择直充账号，验证余额是否立即增加

## 🔧 技术特点

### 前端功能
- 响应式设计，支持移动端
- 实时价格计算和折扣显示
- 直观的发货方式选择界面
- 管理员和用户界面分离

### 后端功能
- 完整的商品管理API
- 支持多种发货方式处理
- 库存管理和订单跟踪
- 权限控制和安全验证

### 数据库设计
- 商品表：存储礼品卡商品信息
- 礼品卡表：存储生成的礼品卡
- 订单表：记录购买订单
- 用户表：管理用户余额

## 💡 使用建议

### 管理员建议
1. 定期检查商品库存，及时补充热门商品
2. 根据销售情况调整价格和折扣
3. 监控用户反馈，优化商品描述
4. 设置合理的有效期，平衡用户体验和风险

### 用户建议
1. 选择合适的发货方式：
   - 自用选择"直充账号"最方便
   - 送礼选择"礼品卡"最有仪式感
   - 分享选择"兑换码"最灵活
2. 关注折扣优惠，选择性价比高的商品
3. 注意有效期，及时使用购买的礼品卡

这个礼品卡系统为平台提供了完整的礼品卡销售和管理解决方案，支持多种使用场景，提升用户体验！
