# 🚀 BitMarket 托管服务快速开始指南

## 📋 概述

本指南将帮助您快速了解和使用 BitMarket 的区块链托管服务功能。托管服务为大金额交易（≥100 USDT）提供安全保障。

## 🎯 5分钟快速体验

### 1. 作为买家体验托管服务

#### 步骤 1: 选择支持托管的商品
1. 访问 `http://localhost:3000/products`
2. 选择价格 ≥ 100 USDT 的商品
3. 在商品详情页面查看托管服务选项

#### 步骤 2: 启用托管服务
1. 勾选"使用托管服务"
2. 从中间人列表中选择一个（查看费率和信誉）
3. 确认总费用（商品金额 + 托管费）
4. 点击"立即购买"

#### 步骤 3: 支付托管资金
1. 系统会显示支付信息
2. 使用 BNB Chain 钱包转账
3. 提交交易哈希确认支付
4. 等待中间人确认资金到账

#### 步骤 4: 跟踪订单
1. 访问 `/escrow/orders` 查看订单状态
2. 卖家发货后会收到通知
3. 收到商品后点击"确认收货"
4. 资金自动释放给卖家

### 2. 申请成为中间人

#### 步骤 1: 访问中间人控制台
1. 访问 `/mediator/dashboard`
2. 如果不是中间人，会显示注册表单

#### 步骤 2: 填写申请信息
1. **钱包地址**: 输入您的 BNB Chain 钱包地址
2. **服务费率**: 设置 1%-30% 的费率（建议从 5% 开始）
3. **保证金**: 设置保证金金额（最低 1000 USDT）
4. **个人介绍**: 简述您的背景和优势
5. **相关经验**: 描述电商或金融相关经验

#### 步骤 3: 提交申请
1. 点击"提交申请"
2. 等待管理员审核（1-3个工作日）
3. 审核通过后充值保证金
4. 账户激活后开始接受托管订单

### 3. 参与仲裁投票

#### 步骤 1: 查看待投票案例
1. 访问 `/arbitration/cases?role=mediator&status=VOTING`
2. 查看需要投票的争议案例

#### 步骤 2: 分析案例
1. 仔细阅读争议详情
2. 查看买家和卖家的证据
3. 了解订单和商品信息

#### 步骤 3: 投票
1. 选择投票选项：
   - **支持买家**: 认为应该退款
   - **支持卖家**: 认为应该释放资金
   - **中性**: 需要更多信息
2. 填写详细的投票理由
3. 提交投票

#### 步骤 4: 获得奖励
1. 投票后获得 10 USDT 免手续费提现券
2. 奖励券 7 天有效期
3. 每月最多获得 1 张奖励券

## 🔧 系统配置

### 环境要求
- Node.js 18+
- MySQL 8.0+
- BNB Chain 钱包

### 快速部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd bitmarket

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 4. 数据库迁移
npx prisma db push
npx prisma generate

# 5. 启动开发服务器
npm run dev
```

### 测试系统
```bash
# 运行托管系统测试
npm run test:escrow-system

# 运行所有测试
npm test
```

## 📊 关键页面和功能

### 用户页面
- **商品浏览**: `/products` - 查看支持托管的商品
- **托管订单**: `/escrow/orders` - 管理托管订单
- **争议举报**: `/radar` - 提交和查看争议
- **奖励券**: `/rewards/coupons` - 管理奖励券

### 中间人页面
- **控制台**: `/mediator/dashboard` - 中间人注册和管理
- **仲裁投票**: `/arbitration/cases` - 参与争议仲裁

### 管理员页面
- **用户管理**: `/admin/users` - 管理用户和中间人
- **争议处理**: `/admin/arbitration` - 处理复杂争议
- **系统配置**: `/admin/settings` - 配置托管参数

## 🔒 安全最佳实践

### 用户安全
1. **钱包安全**: 使用硬件钱包或安全的软件钱包
2. **私钥保护**: 永远不要分享私钥
3. **交易确认**: 仔细核对转账地址和金额
4. **证据保存**: 保存聊天记录和交易凭证

### 中间人安全
1. **保证金管理**: 定期检查保证金余额
2. **公正仲裁**: 基于事实进行客观投票
3. **及时响应**: 快速处理托管订单
4. **风险控制**: 不要承接超出能力的订单

### 系统安全
1. **权限控制**: 严格的用户权限验证
2. **输入验证**: 所有输入参数验证
3. **交易监控**: 实时监控资金流向
4. **审计日志**: 完整的操作记录

## 📈 经济模型

### 费用结构
```
托管订单总费用 = 商品金额 + 托管服务费
托管服务费 = 商品金额 × 中间人费率
平台费用 = 托管服务费 × 30%
中间人收益 = 托管服务费 × 70%
```

### 示例计算
```
商品金额: 1000 USDT
中间人费率: 5%
托管服务费: 1000 × 5% = 50 USDT
买家总支付: 1000 + 50 = 1050 USDT
平台费用: 50 × 30% = 15 USDT
中间人收益: 50 × 70% = 35 USDT
```

## 🎁 奖励机制

### 中间人奖励
- **仲裁参与**: 每次投票获得 10 USDT 免手续费提现券
- **月度限制**: 每月最多 1 张奖励券
- **有效期**: 7 天有效期
- **使用场景**: 提现时免手续费

### 信誉系统
- **成功率**: 完成订单数 / 总订单数
- **响应速度**: 平均处理时间
- **用户评价**: 买家和卖家评分
- **仲裁准确性**: 投票与最终结果一致性

## 🆘 常见问题

### Q: 什么情况下可以使用托管服务？
A: 商品金额 ≥ 100 USDT 的交易可以选择使用托管服务。

### Q: 中间人如何选择？
A: 建议选择信誉高、费率合理、响应快的中间人。

### Q: 争议如何解决？
A: 通过中间人委员会投票仲裁，多数决原则自动执行结果。

### Q: 资金安全如何保障？
A: 资金由中间人托管，只有在确认收货或仲裁决定后才会释放。

### Q: 手续费如何计算？
A: 托管服务费由中间人设定（1%-30%），平台从中抽取30%。

## 📞 技术支持

### 联系方式
- **在线客服**: 网站右下角聊天窗口
- **邮件支持**: <EMAIL>
- **技术文档**: `/docs/ESCROW_SYSTEM_GUIDE.md`

### 紧急情况
- **资金问题**: <EMAIL>
- **系统故障**: <EMAIL>
- **争议升级**: <EMAIL>

---

🎉 **恭喜！您已经了解了 BitMarket 托管服务的基本使用方法。开始您的安全交易之旅吧！**

*快速指南版本: v1.0.0*  
*最后更新: 2025-07-25*
