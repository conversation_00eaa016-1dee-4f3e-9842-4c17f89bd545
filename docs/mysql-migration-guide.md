# MySQL 数据库迁移指南

## 🎯 迁移状态

✅ **配置已完成**: 项目已成功切换到MySQL数据库配置

## 📊 迁移前后对比

| 项目 | SQLite (之前) | MySQL (现在) |
|------|---------------|--------------|
| **数据库类型** | 文件数据库 | 网络数据库 |
| **端口** | 无 | 3306 |
| **连接方式** | 文件访问 | TCP连接 |
| **并发支持** | 有限 | 优秀 |
| **生产适用** | 否 | 是 |
| **扩展性** | 有限 | 优秀 |

## 🔧 已完成的配置更改

### 1. 环境变量更新

**文件**: `.env`
```bash
# 旧配置 (已注释)
# DATABASE_URL="file:./dev.db"

# 新配置 (已启用)
DATABASE_URL="mysql://root:password@localhost:3306/bitmarket"
```

### 2. Prisma Schema 更新

**文件**: `prisma/schema.prisma`
```prisma
datasource db {
  provider = "mysql"  // 从 "sqlite" 改为 "mysql"
  url      = env("DATABASE_URL")
}
```

### 3. 依赖包安装

✅ **mysql2 驱动已安装**
```bash
npm install mysql2  # 已完成
```

✅ **Prisma 客户端已生成**
```bash
npx prisma generate  # 已完成
```

## 🗃️ MySQL 数据库设置

### 当前配置参数

| 参数 | 值 | 说明 |
|------|-----|------|
| **主机** | localhost | 本地MySQL服务器 |
| **端口** | 3306 | MySQL默认端口 |
| **用户名** | root | 管理员用户 |
| **密码** | password | 默认密码 |
| **数据库名** | bitmarket | 项目数据库 |

### 需要完成的步骤

#### 1. 安装MySQL服务器

**Windows**:
1. 下载 [MySQL Installer](https://dev.mysql.com/downloads/installer/)
2. 运行安装程序，选择 "Developer Default"
3. 设置 root 密码为 `password`
4. 启动 MySQL 服务

**macOS** (使用 Homebrew):
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

**Ubuntu/Debian**:
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### 2. 创建数据库

连接到MySQL并创建数据库：
```sql
mysql -u root -p

CREATE DATABASE bitmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
SHOW DATABASES;
EXIT;
```

#### 3. 运行数据库迁移

```bash
npx prisma migrate dev --name switch-to-mysql
```

#### 4. 测试连接

```bash
node scripts/setup-mysql.js test
```

## 🚀 快速设置命令

### 自动设置 (推荐)

```bash
# 一键完成所有设置
node scripts/setup-mysql.js setup
```

### 手动设置

```bash
# 1. 查看设置指南
node scripts/setup-mysql.js guide

# 2. 安装MySQL驱动 (已完成)
npm install mysql2

# 3. 生成Prisma客户端 (已完成)
npx prisma generate

# 4. 运行数据库迁移
npx prisma migrate dev

# 5. 测试连接
node scripts/setup-mysql.js test
```

## 📋 SQL命令参考

已生成MySQL设置命令文件: `setup-mysql.sql`

```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE bitmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户 (可选)
CREATE USER 'bitmarket_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON bitmarket.* TO 'bitmarket_user'@'localhost';
FLUSH PRIVILEGES;

-- 查看数据库
SHOW DATABASES;

-- 使用数据库
USE bitmarket;

-- 查看表
SHOW TABLES;
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 连接被拒绝 (ECONNREFUSED)

**原因**: MySQL服务未启动
**解决方案**:
```bash
# Windows
# 在服务管理器中启动 MySQL80 服务

# macOS
brew services start mysql

# Linux
sudo systemctl start mysql
```

#### 2. 访问被拒绝 (Access denied)

**原因**: 用户名或密码错误
**解决方案**:
1. 检查 `.env` 文件中的用户名和密码
2. 重置MySQL root密码
3. 确保用户有访问权限

#### 3. 数据库不存在 (Unknown database)

**原因**: 未创建 bitmarket 数据库
**解决方案**:
```sql
mysql -u root -p
CREATE DATABASE bitmarket;
```

#### 4. 驱动未安装

**原因**: mysql2 包未安装
**解决方案**:
```bash
npm install mysql2
```

## 📊 数据迁移

### 从SQLite迁移数据 (可选)

如果需要保留SQLite中的数据：

1. **导出SQLite数据**:
```bash
node scripts/database-manager.js backup
```

2. **运行MySQL迁移**:
```bash
npx prisma migrate dev
```

3. **手动导入重要数据** (如用户账户)

### 验证迁移结果

```bash
# 检查数据库配置
node scripts/database-config.js show

# 测试MySQL连接
node scripts/setup-mysql.js test

# 查看数据库信息
node scripts/database-manager.js info
```

## 🎉 迁移完成检查清单

- [x] ✅ 更新环境变量 (DATABASE_URL)
- [x] ✅ 更新Prisma schema (provider = "mysql")
- [x] ✅ 安装MySQL驱动 (mysql2)
- [x] ✅ 生成Prisma客户端
- [ ] ⏳ 安装MySQL服务器
- [ ] ⏳ 创建bitmarket数据库
- [ ] ⏳ 运行数据库迁移
- [ ] ⏳ 测试数据库连接

## 🔄 回滚到SQLite (如需要)

如果遇到问题需要回滚：

1. **恢复环境变量**:
```bash
# .env
DATABASE_URL="file:./dev.db"
```

2. **恢复Prisma schema**:
```prisma
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}
```

3. **重新生成客户端**:
```bash
npx prisma generate
```

## 📞 获取帮助

如果遇到问题：

1. **查看设置指南**: `node scripts/setup-mysql.js guide`
2. **测试连接**: `node scripts/setup-mysql.js test`
3. **检查MySQL服务状态**: `sudo systemctl status mysql`
4. **查看MySQL日志**: `sudo journalctl -u mysql`

---

**下一步**: 安装MySQL服务器并运行 `node scripts/setup-mysql.js setup` 完成自动配置！
