# 数据库端口配置指南

## 📊 当前配置状态

**BitMarket项目当前使用**: SQLite 数据库
- **类型**: 文件数据库
- **端口**: 不适用 (无需网络端口)
- **文件位置**: `prisma/dev.db`
- **文件大小**: ~612 KB
- **适用场景**: 开发环境

## 🔌 数据库端口对照表

| 数据库类型 | 默认端口 | 协议 | 用途 | 推荐环境 |
|------------|----------|------|------|----------|
| **SQLite** | 无端口 | 文件 | 轻量级数据库 | 开发/测试 |
| **PostgreSQL** | 5432 | TCP | 企业级数据库 | 生产环境 |
| **MySQL** | 3306 | TCP | 通用数据库 | 生产环境 |
| **Redis** | 6379 | TCP | 缓存数据库 | 缓存服务 |
| **MongoDB** | 27017 | TCP | 文档数据库 | NoSQL应用 |
| **Elasticsearch** | 9200 | HTTP | 搜索引擎 | 搜索服务 |

## 🗃️ 数据库类型详解

### 1. SQLite (当前使用)

```bash
# 当前配置
DATABASE_URL="file:./dev.db"
```

**特点**:
- ✅ 无需安装额外软件
- ✅ 零配置，开箱即用
- ✅ 轻量级，适合开发
- ❌ 不支持并发写入
- ❌ 不适合生产环境

**端口**: 不适用 (文件数据库)

### 2. PostgreSQL

```bash
# 配置示例
DATABASE_URL="postgresql://username:password@localhost:5432/bitmarket"
```

**特点**:
- ✅ 高性能，支持复杂查询
- ✅ 强大的扩展性
- ✅ 适合生产环境
- ❌ 需要额外安装和配置

**默认端口**: 5432
**自定义端口示例**: 5433, 5434

### 3. MySQL

```bash
# 配置示例
DATABASE_URL="mysql://username:password@localhost:3306/bitmarket"
```

**特点**:
- ✅ 广泛使用，社区支持好
- ✅ 性能良好
- ✅ 易于管理
- ❌ 某些高级功能有限

**默认端口**: 3306
**自定义端口示例**: 3307, 3308

### 4. Redis (缓存)

```bash
# 配置示例
REDIS_URL="redis://localhost:6379"
```

**特点**:
- ✅ 极高性能
- ✅ 支持多种数据结构
- ✅ 适合缓存和会话存储
- ❌ 数据存储在内存中

**默认端口**: 6379

## 🔧 端口配置管理

### 查看当前配置

```bash
# 使用配置工具
node scripts/database-config.js show

# 输出示例:
# 📊 当前数据库配置
# 数据库类型: SQLITE
# 连接URL: file:./dev.db
# 端口号: 不适用 (文件数据库)
```

### 切换数据库类型

```bash
# 交互式切换
node scripts/database-config.js

# 选择菜单:
# 1. 显示当前配置
# 2. 显示端口信息  
# 3. 切换数据库
# 4. 测试连接
```

### 测试数据库连接

```bash
# 测试当前配置
node scripts/database-config.js test
```

## 🚀 生产环境配置建议

### PostgreSQL 配置 (推荐)

```bash
# .env.local
DATABASE_URL="postgresql://bitmarket_user:secure_password@localhost:5432/bitmarket_prod"

# 或使用自定义端口
DATABASE_URL="postgresql://bitmarket_user:secure_password@localhost:5433/bitmarket_prod"
```

**安装 PostgreSQL**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS (使用 Homebrew)
brew install postgresql

# Windows
# 下载并安装 PostgreSQL 官方安装包
```

**创建数据库**:
```sql
-- 连接到 PostgreSQL
sudo -u postgres psql

-- 创建用户和数据库
CREATE USER bitmarket_user WITH PASSWORD 'secure_password';
CREATE DATABASE bitmarket_prod OWNER bitmarket_user;
GRANT ALL PRIVILEGES ON DATABASE bitmarket_prod TO bitmarket_user;
```

### MySQL 配置

```bash
# .env.local
DATABASE_URL="mysql://bitmarket_user:secure_password@localhost:3306/bitmarket_prod"
```

**安装 MySQL**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# macOS (使用 Homebrew)
brew install mysql

# Windows
# 下载并安装 MySQL 官方安装包
```

## 🔒 端口安全配置

### 防火墙设置

```bash
# Ubuntu/Debian - 允许 PostgreSQL
sudo ufw allow 5432/tcp

# Ubuntu/Debian - 允许 MySQL
sudo ufw allow 3306/tcp

# 只允许本地连接
sudo ufw allow from 127.0.0.1 to any port 5432
```

### 数据库安全配置

```bash
# PostgreSQL - 编辑配置文件
sudo nano /etc/postgresql/13/main/postgresql.conf

# 限制监听地址
listen_addresses = 'localhost'

# MySQL - 编辑配置文件
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# 限制绑定地址
bind-address = 127.0.0.1
```

## 🔍 端口故障排除

### 检查端口占用

```bash
# 检查端口是否被占用
netstat -tulpn | grep :5432  # PostgreSQL
netstat -tulpn | grep :3306  # MySQL
netstat -tulpn | grep :6379  # Redis

# Windows
netstat -an | findstr :5432
```

### 常见端口冲突解决

```bash
# 如果默认端口被占用，使用自定义端口

# PostgreSQL 自定义端口
DATABASE_URL="postgresql://user:pass@localhost:5433/db"

# MySQL 自定义端口  
DATABASE_URL="mysql://user:pass@localhost:3307/db"

# Redis 自定义端口
REDIS_URL="redis://localhost:6380"
```

### 连接测试

```bash
# PostgreSQL 连接测试
psql -h localhost -p 5432 -U username -d database_name

# MySQL 连接测试
mysql -h localhost -P 3306 -u username -p database_name

# Redis 连接测试
redis-cli -h localhost -p 6379 ping
```

## 📈 性能优化建议

### 连接池配置

```bash
# Prisma 连接池配置
DATABASE_URL="postgresql://user:pass@localhost:5432/db?connection_limit=20&pool_timeout=20"
```

### 端口绑定优化

```bash
# 生产环境 - 只绑定内网IP
DATABASE_URL="*****************************************/db"

# 开发环境 - 绑定本地
DATABASE_URL="postgresql://user:pass@127.0.0.1:5432/db"
```

## 🛠️ 快速命令参考

```bash
# 查看当前数据库配置
node scripts/database-config.js show

# 显示所有数据库端口信息
node scripts/database-config.js port

# 测试数据库连接
node scripts/database-config.js test

# 交互式配置管理
node scripts/database-config.js

# 启动 Prisma Studio (可视化管理)
npx prisma studio

# 应用数据库迁移
npx prisma migrate dev

# 重置数据库
npx prisma migrate reset
```

## 📞 获取帮助

如果遇到端口相关问题：

1. **检查配置**: `node scripts/database-config.js show`
2. **测试连接**: `node scripts/database-config.js test`
3. **查看端口**: `netstat -tulpn | grep :端口号`
4. **检查服务**: `sudo systemctl status postgresql`
5. **查看日志**: `sudo journalctl -u postgresql`

---

**总结**: BitMarket当前使用SQLite数据库，无需端口配置。如需切换到PostgreSQL或MySQL等网络数据库，请使用提供的配置工具进行切换。
