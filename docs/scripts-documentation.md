# BitMarket 脚本文档

## 📋 脚本总览

本文档详细说明了BitMarket项目中所有Node.js脚本的功能、用法和注意事项。

---

## 🧪 测试数据脚本

### create-test-deposits.js
**路径**: `scripts/create-test-deposits.js`
**功能**: 创建基础测试充值数据
**依赖**: Prisma Client, 现有用户数据

#### 用法
```bash
node scripts/create-test-deposits.js
```

#### 功能详情
- 创建5条基础充值记录
- 支持USDT、BNB、BINANCE_PAY三种支付方式
- 自动关联到现有用户
- 生成基础订单信息和备注

#### 输出示例
```
🔄 创建测试充值记录...
✅ 创建充值记录: cmdcx0py00001v92kbvllu07z
   用户: 系统管理员 (<EMAIL>)
   金额: 100 USDT
   支付方式: USDT
```

#### 注意事项
- 需要先有用户数据
- 会覆盖同名测试数据
- 仅用于开发环境

---

### create-complete-test-deposits.js
**路径**: `scripts/create-complete-test-deposits.js`
**功能**: 创建包含完整信息的测试充值数据
**依赖**: Prisma Client, 现有用户数据

#### 用法
```bash
node scripts/create-complete-test-deposits.js
```

#### 功能详情
- 创建5条完整充值记录
- **币安支付**: 包含PIN码和订单号
- **BNB链**: 包含完整交易hash和BSC网络信息
- **USDT**: 包含交易hash和TRC20网络信息
- 包含详细的metadata信息

#### 创建的数据类型
| 支付方式 | 数量 | 特殊字段 |
|----------|------|----------|
| BINANCE_PAY | 2条 | pinCode, paymentOrderId |
| BNB | 2条 | transactionHash, 网络信息 |
| USDT | 1条 | transactionHash, txHash |

#### 输出示例
```
✅ 创建充值记录: cmdcxbhiq0001v9s4cae0lhmw
   用户: 系统管理员 (<EMAIL>)
   金额: 500 USDT
   支付方式: BINANCE_PAY
   PIN码: 123456
   支付订单号: BINANCE_ORDER_1753091337840_001
```

#### 测试验证要点
- 币安支付记录应显示PIN码
- BNB链记录应显示交易hash
- USDT记录应显示TRC20信息
- 所有记录状态为PENDING

---

## 🧹 数据清理脚本

### clean-deposit-records.js
**路径**: `scripts/clean-deposit-records.js`
**功能**: 充值记录清理工具
**依赖**: Prisma Client

#### 基础用法
```bash
# 查看当前状态
node scripts/clean-deposit-records.js

# 执行清理操作
node scripts/clean-deposit-records.js [action] [params]
```

#### 清理选项详解

##### 1. 查看状态（无参数）
```bash
node scripts/clean-deposit-records.js
```
**功能**: 
- 显示充值记录统计
- 列出最近10条记录
- 提供操作指南

**输出信息**:
- 按状态分组的统计
- 总记录数和总金额
- 最近记录的详细信息

##### 2. 删除所有记录
```bash
node scripts/clean-deposit-records.js all
```
**功能**: 删除所有充值记录
**风险级别**: 🔴 高风险
**适用场景**: 完全重置系统

##### 3. 删除测试记录
```bash
node scripts/clean-deposit-records.js test
```
**功能**: 删除包含测试关键词的记录
**匹配条件**:
- 备注包含"测试"
- 备注包含"test"或"Test"
- 备注包含"需要核实"
- 备注包含"需要确认"

**风险级别**: 🟢 低风险
**推荐使用**: 开发环境清理

##### 4. 删除待处理记录
```bash
node scripts/clean-deposit-records.js pending
```
**功能**: 删除状态为PENDING的记录
**风险级别**: 🟡 中风险
**适用场景**: 清理未处理的申请

##### 5. 删除今日记录
```bash
node scripts/clean-deposit-records.js today
```
**功能**: 删除今天创建的记录
**风险级别**: 🟡 中风险
**适用场景**: 清理当日测试数据

##### 6. 删除特定用户记录
```bash
node scripts/clean-deposit-records.<NAME_EMAIL>
```
**功能**: 删除指定用户的所有充值记录
**参数**: 用户邮箱地址
**风险级别**: 🟡 中风险

#### 安全特性
- **操作前确认**: 显示详细统计信息
- **操作后验证**: 显示清理结果
- **错误处理**: 完善的错误捕获和提示
- **日志记录**: 详细的操作日志

#### 输出示例
```bash
📊 当前充值记录统计:
   PENDING: 17 笔, 总额 5873.01 USDT
   总计: 17 笔充值记录

🗑️  删除所有充值记录...
✅ 成功删除 17 条充值记录

📊 清理后的统计:
   无充值记录
   总计: 0 笔充值记录
```

---

## 🔧 脚本开发指南

### 脚本结构模板
```javascript
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function mainFunction() {
  try {
    console.log('🚀 脚本开始执行...')
    
    // 主要逻辑
    
    console.log('✅ 脚本执行完成')
  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  mainFunction()
}

module.exports = { mainFunction }
```

### 最佳实践
1. **错误处理**: 使用try-catch包装主要逻辑
2. **资源清理**: 确保Prisma连接正确关闭
3. **用户反馈**: 提供清晰的进度和结果信息
4. **参数验证**: 验证输入参数的有效性
5. **模块化**: 支持作为模块导入使用

### 日志规范
- 🚀 开始执行
- 🔄 进行中操作
- ✅ 成功完成
- ❌ 错误失败
- ⚠️ 警告信息
- 💡 提示信息

---

## 📊 脚本性能监控

### 执行时间监控
```javascript
const startTime = Date.now()
// 脚本逻辑
const endTime = Date.now()
console.log(`⏱️ 执行时间: ${endTime - startTime}ms`)
```

### 内存使用监控
```javascript
const used = process.memoryUsage()
console.log('💾 内存使用:')
for (let key in used) {
  console.log(`   ${key}: ${Math.round(used[key] / 1024 / 1024 * 100) / 100} MB`)
}
```

---

## 🚨 故障排除

### 常见错误及解决方案

#### 1. Prisma连接错误
**错误**: `PrismaClientInitializationError`
**解决**: 
```bash
npx prisma generate
npx prisma db pull
```

#### 2. 用户不存在错误
**错误**: 脚本找不到用户数据
**解决**: 先运行用户创建脚本

#### 3. 权限错误
**错误**: 数据库操作权限不足
**解决**: 检查数据库连接字符串和用户权限

#### 4. 内存不足
**错误**: 大量数据操作时内存溢出
**解决**: 
```bash
node --max-old-space-size=4096 scripts/script-name.js
```

---

## 📝 脚本维护

### 定期维护任务
- [ ] 检查脚本执行状态
- [ ] 更新依赖版本
- [ ] 优化性能瓶颈
- [ ] 更新文档说明

### 版本控制
- 重要修改时更新版本号
- 记录变更日志
- 保持向后兼容性

---

*脚本文档 v1.0 | 2025年7月21日*
