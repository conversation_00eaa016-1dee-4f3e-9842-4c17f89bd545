# BitMarket 测试指南

## 概述

BitMarket 使用 Vitest 作为主要测试框架，支持单元测试、集成测试、端到端测试和性能测试。

## 测试框架和工具

### 核心工具
- **Vitest**: 主要测试框架
- **@testing-library/react**: React 组件测试
- **@testing-library/jest-dom**: DOM 断言扩展
- **jsdom**: 浏览器环境模拟
- **@faker-js/faker**: 测试数据生成

### 测试类型
1. **单元测试**: 测试单个函数或组件
2. **集成测试**: 测试API端点和数据库交互
3. **端到端测试**: 测试完整的用户流程
4. **性能测试**: 测试系统性能和负载

## 快速开始

### 安装依赖
```bash
npm install
```

### 运行测试

#### 基本命令
```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 运行性能测试
npm run test:performance

# 运行所有测试套件
npm run test:all
```

#### 开发模式
```bash
# 监视模式运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage

# CI模式运行测试
npm run test:ci
```

#### 测试环境管理
```bash
# 设置测试环境
npm run test:setup

# 清理测试环境
npm run test:cleanup
```

## 测试配置

### Vitest 配置 (vitest.config.ts)
```typescript
export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
    include: [
      'test/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'app/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'lib/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'components/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
})
```

### 测试设置 (test/setup.ts)
- 全局变量配置
- Mock 设置
- 环境变量配置
- 测试工具初始化

## 编写测试

### 基本测试结构
```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'

describe('功能模块名称', () => {
  beforeEach(() => {
    // 每个测试前的设置
    vi.clearAllMocks()
  })

  it('应该执行预期的行为', () => {
    // 准备
    const input = 'test input'
    
    // 执行
    const result = functionToTest(input)
    
    // 断言
    expect(result).toBe('expected output')
  })
})
```

### API 测试示例
```typescript
import { describe, it, expect, vi } from 'vitest'
import { GET } from '@/app/api/products/route'
import { createMockRequest } from '@/test/test-utils'

describe('商品API', () => {
  it('应该返回商品列表', async () => {
    const request = createMockRequest('GET', '/api/products')
    const response = await GET(request)
    
    expect(response.status).toBe(200)
    
    const data = await response.json()
    expect(data).toHaveProperty('products')
    expect(Array.isArray(data.products)).toBe(true)
  })
})
```

### React 组件测试示例
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import Button from '@/components/Button'

describe('Button 组件', () => {
  it('应该渲染按钮文本', () => {
    render(<Button>点击我</Button>)
    
    expect(screen.getByText('点击我')).toBeInTheDocument()
  })

  it('应该处理点击事件', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>点击我</Button>)
    
    fireEvent.click(screen.getByText('点击我'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

## 测试工具和辅助函数

### 测试工具 (test/test-utils.ts)
```typescript
// 创建模拟请求
export function createMockRequest(method: string, url: string, body?: any): Request

// 创建模拟会话
export function createMockSession(user: any): Session

// 生成随机字符串
export function generateRandomString(length: number): string

// 模拟用户数据
export const mockUsers = {
  admin: { id: 'admin-id', role: 'ADMIN' },
  seller: { id: 'seller-id', role: 'USER' },
  buyer: { id: 'buyer-id', role: 'USER' }
}

// 模拟商品数据
export const mockProducts = {
  available: { id: 'product-id', status: 'AVAILABLE' },
  sold: { id: 'product-id-2', status: 'SOLD' }
}
```

### Mock 配置
项目已预配置以下 Mock:
- Next.js 路由和导航
- NextAuth 认证
- Prisma 数据库客户端
- Redis 缓存
- 文件系统操作
- 图像处理 (Sharp)
- Socket.io

## 测试最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 使用中文描述测试意图
- 遵循 "应该 + 预期行为" 的格式

### 2. 测试结构
- 使用 AAA 模式: Arrange (准备), Act (执行), Assert (断言)
- 每个测试只验证一个行为
- 保持测试简单和独立

### 3. Mock 使用
- 只 Mock 外部依赖
- 避免过度 Mock
- 使用有意义的 Mock 数据

### 4. 异步测试
```typescript
it('应该处理异步操作', async () => {
  const promise = asyncFunction()
  await expect(promise).resolves.toBe('expected result')
})
```

### 5. 错误测试
```typescript
it('应该处理错误情况', async () => {
  const invalidInput = null
  await expect(functionThatThrows(invalidInput)).rejects.toThrow('错误信息')
})
```

## 测试覆盖率

### 覆盖率目标
- 分支覆盖率: 80%
- 函数覆盖率: 80%
- 行覆盖率: 80%
- 语句覆盖率: 80%

### 查看覆盖率报告
```bash
npm run test:coverage
```
报告将生成在 `coverage/` 目录中，打开 `coverage/index.html` 查看详细报告。

## 持续集成

### CI 配置
```bash
npm run test:ci
```
此命令会:
- 运行所有测试
- 生成覆盖率报告
- 输出 JSON 格式的测试结果

### 测试报告
测试结果保存在 `test-results/` 目录:
- `results.json`: JSON 格式的测试结果
- `report.html`: HTML 格式的测试报告
- `index.html`: 覆盖率报告

## 故障排除

### 常见问题及解决方案

#### 1. Mock 相关错误

**错误**: `TypeError: Cannot read properties of undefined (reading 'mockResolvedValue')`

**原因**: Prisma Mock 设置问题，通常是因为 Mock 对象未正确初始化

**解决方案**:
```typescript
// 确保在测试文件中正确导入和设置 Mock
import { vi } from 'vitest'

// 在 beforeEach 中重置 Mock
beforeEach(() => {
  vi.clearAllMocks()

  // 重新设置 Mock 返回值
  mockPrisma.user.findUnique.mockResolvedValue(mockUser)
  mockPrisma.product.create.mockResolvedValue(mockProduct)
})
```

#### 2. API 测试返回 400/500 错误

**错误**: API 测试期望 200 但收到 400 或 500

**原因**:
- 请求数据验证失败 (400)
- 服务器内部错误 (500)
- Mock 数据不完整

**解决方案**:
```typescript
// 检查请求数据是否完整
const userData = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test User',
  // 确保包含所有必填字段
  binanceUid: '12345678',
  bnbWalletAddress: '******************************************'
}

// 添加调试信息
const response = await apiHandler(request)
if (response.status !== 200) {
  const errorData = await response.json()
  console.log('API Error:', errorData)
}
```

#### 3. 性能测试失败

**错误**: 统计数据不正确，如 `expected 0 to be 6`

**原因**:
- 异步操作未正确等待
- 统计计算逻辑错误
- Mock 数据问题

**解决方案**:
```typescript
// 确保异步操作完成
await Promise.all(tasks)

// 添加延迟确保统计更新
await new Promise(resolve => setTimeout(resolve, 100))

// 验证统计数据
const stats = executor.getStats()
console.log('Performance Stats:', stats)
```

#### 4. 数据库连接错误

**错误**: 无法连接到测试数据库

**解决方案**:
```bash
# 设置正确的环境变量
export NODE_ENV=test
export DATABASE_URL=file:./test.db

# 重置测试数据库
npx prisma migrate reset --force
npx prisma migrate deploy
```

#### 5. 文件上传测试失败

**错误**: 文件上传 Mock 不工作

**解决方案**:
```typescript
// Mock 文件系统操作
vi.mock('fs/promises', () => ({
  writeFile: vi.fn().mockResolvedValue(undefined),
  mkdir: vi.fn().mockResolvedValue(undefined)
}))

// 创建正确的 FormData
const formData = new FormData()
const file = new File(['test content'], 'test.jpg', {
  type: 'image/jpeg'
})
formData.append('file', file)
```

### 调试技巧

#### 1. 添加调试输出
```typescript
it('调试测试', async () => {
  console.log('测试数据:', testData)

  const response = await apiCall(testData)
  console.log('响应状态:', response.status)

  if (response.status !== 200) {
    const errorData = await response.json()
    console.log('错误详情:', errorData)
  }

  expect(response.status).toBe(200)
})
```

#### 2. 使用 vi.spyOn 监视函数调用
```typescript
const spy = vi.spyOn(module, 'functionName')
await functionCall()
expect(spy).toHaveBeenCalledWith(expectedArgs)
console.log('函数调用次数:', spy.mock.calls.length)
```

#### 3. 检查 Mock 状态
```typescript
beforeEach(() => {
  vi.clearAllMocks()

  // 验证 Mock 是否正确设置
  expect(vi.isMockFunction(mockPrisma.user.findUnique)).toBe(true)
})
```

#### 4. 异步测试调试
```typescript
it('异步操作调试', async () => {
  const startTime = Date.now()

  try {
    const result = await asyncOperation()
    console.log('操作耗时:', Date.now() - startTime, 'ms')
    expect(result).toBeDefined()
  } catch (error) {
    console.error('异步操作失败:', error)
    throw error
  }
})
```

### 测试环境重置

如果遇到持续的测试问题，可以尝试重置测试环境：

```bash
# 清理所有缓存和临时文件
npm run cache:clear
rm -rf node_modules/.cache
rm -rf .next
rm -f test.db

# 重新安装依赖
npm install

# 重置数据库
npx prisma migrate reset --force

# 重新运行测试
npm run test:setup
npm test
```

## 高级测试技巧

### 1. 数据库事务测试
```typescript
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { prisma } from '@/lib/prisma'

describe('数据库操作', () => {
  beforeEach(async () => {
    // 开始事务
    await prisma.$executeRaw`BEGIN`
  })

  afterEach(async () => {
    // 回滚事务
    await prisma.$executeRaw`ROLLBACK`
  })

  it('应该创建用户', async () => {
    const user = await prisma.user.create({
      data: { email: '<EMAIL>', name: 'Test User' }
    })
    expect(user.email).toBe('<EMAIL>')
  })
})
```

### 2. 文件上传测试
```typescript
import { describe, it, expect } from 'vitest'
import { POST } from '@/app/api/upload/route'

describe('文件上传API', () => {
  it('应该上传图片文件', async () => {
    const formData = new FormData()
    const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
    formData.append('file', file)

    const request = new Request('http://localhost/api/upload', {
      method: 'POST',
      body: formData
    })

    const response = await POST(request)
    expect(response.status).toBe(200)

    const data = await response.json()
    expect(data).toHaveProperty('url')
  })
})
```

### 3. WebSocket 测试
```typescript
import { describe, it, expect, vi } from 'vitest'
import { io } from 'socket.io-client'

describe('WebSocket 连接', () => {
  it('应该建立连接并接收消息', (done) => {
    const mockSocket = {
      on: vi.fn((event, callback) => {
        if (event === 'connect') {
          callback()
        }
        if (event === 'message') {
          callback({ text: 'Hello' })
        }
      }),
      emit: vi.fn(),
      disconnect: vi.fn()
    }

    vi.mocked(io).mockReturnValue(mockSocket as any)

    const socket = io('http://localhost:3000')

    socket.on('connect', () => {
      expect(mockSocket.on).toHaveBeenCalledWith('connect', expect.any(Function))
      done()
    })
  })
})
```

### 4. 性能测试
```typescript
import { describe, it, expect } from 'vitest'
import { performance } from 'perf_hooks'

describe('性能测试', () => {
  it('应该在合理时间内完成操作', async () => {
    const start = performance.now()

    // 执行需要测试性能的操作
    await heavyOperation()

    const end = performance.now()
    const duration = end - start

    // 断言操作应在1秒内完成
    expect(duration).toBeLessThan(1000)
  })

  it('应该处理大量数据', async () => {
    const largeDataSet = Array.from({ length: 10000 }, (_, i) => ({ id: i }))

    const start = performance.now()
    const result = await processLargeDataSet(largeDataSet)
    const end = performance.now()

    expect(result).toHaveLength(10000)
    expect(end - start).toBeLessThan(5000) // 5秒内完成
  })
})
```

## 测试数据管理

### 1. 测试数据工厂
```typescript
// test/factories/user-factory.ts
import { faker } from '@faker-js/faker'

export const createUser = (overrides = {}) => ({
  id: faker.string.uuid(),
  email: faker.internet.email(),
  name: faker.person.fullName(),
  createdAt: faker.date.past(),
  ...overrides
})

export const createProduct = (overrides = {}) => ({
  id: faker.string.uuid(),
  title: faker.commerce.productName(),
  price: parseFloat(faker.commerce.price()),
  description: faker.commerce.productDescription(),
  category: faker.helpers.arrayElement(['ELECTRONICS', 'CLOTHING', 'BOOKS']),
  ...overrides
})
```

### 2. 测试数据清理
```typescript
// test/helpers/cleanup.ts
import { prisma } from '@/lib/prisma'

export async function cleanupTestData() {
  await prisma.order.deleteMany()
  await prisma.product.deleteMany()
  await prisma.user.deleteMany()
}

export async function createTestData() {
  const user = await prisma.user.create({
    data: createUser({ email: '<EMAIL>' })
  })

  const product = await prisma.product.create({
    data: createProduct({ sellerId: user.id })
  })

  return { user, product }
}
```

## 测试环境配置

### 1. 环境变量
```bash
# .env.test
NODE_ENV=test
DATABASE_URL=file:./test.db
NEXTAUTH_SECRET=test-secret
NEXTAUTH_URL=http://localhost:3000
JWT_SECRET=test-jwt-secret
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1
```

### 2. 测试数据库设置
```typescript
// test/setup-db.ts
import { execSync } from 'child_process'

export async function setupTestDatabase() {
  // 重置测试数据库
  execSync('npx prisma migrate reset --force --skip-generate', {
    env: { ...process.env, DATABASE_URL: 'file:./test.db' }
  })

  // 运行迁移
  execSync('npx prisma migrate deploy', {
    env: { ...process.env, DATABASE_URL: 'file:./test.db' }
  })
}
```

## 测试系统状态

### 当前测试覆盖情况

✅ **已完成的测试**:
- 基础功能测试 (5/5 通过)
- 商品API测试 (大部分通过)
- 消息API测试 (10/10 通过)
- 数据工厂测试 (26/26 通过)

⚠️ **需要修复的测试**:
- 认证API测试 (部分失败 - 数据验证问题)
- 订单API测试 (部分失败 - Mock设置问题)
- 性能测试 (统计计算问题)
- 文件上传测试 (Mock配置问题)

### 快速开始测试

1. **运行自动修复**:
```bash
npm run test:fix
```

2. **运行基础测试**:
```bash
npm run test:unit -- test/basic.test.ts
```

3. **运行工作正常的测试**:
```bash
npm run test:unit -- test/api/messages.test.ts
npm run test:unit -- test/factories/factory.test.ts
```

4. **查看测试报告**:
```bash
open test-results/report.html
open coverage/index.html
```

### 测试命令总结

| 命令 | 用途 | 状态 |
|------|------|------|
| `npm test` | 运行所有测试 | ⚠️ 部分失败 |
| `npm run test:unit` | 单元测试 | ✅ 大部分通过 |
| `npm run test:watch` | 监视模式 | ✅ 可用 |
| `npm run test:coverage` | 覆盖率报告 | ✅ 可用 |
| `npm run test:fix` | 自动修复 | ✅ 可用 |

## 扩展阅读

- [测试命令快速参考](./TEST_COMMANDS_QUICK_REF.md)
- [Vitest 官方文档](https://vitest.dev/)
- [Testing Library 文档](https://testing-library.com/)
- [Jest DOM 匹配器](https://github.com/testing-library/jest-dom)
- [Faker.js 文档](https://fakerjs.dev/)
- [Prisma 测试指南](https://www.prisma.io/docs/guides/testing)
