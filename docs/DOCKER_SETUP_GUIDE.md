# 🐳 BitMarket Docker 数据库环境完整设置指南

## 📋 概述

本指南提供了 BitMarket 项目 Docker 数据库环境的完整设置和使用说明。我们已经为您配置了一个完整的 Docker 化 MySQL 数据库环境，包括自动化脚本、配置文件和管理工具。

## 🎯 已完成的配置

### ✅ 核心配置文件
- **docker-compose.yml**: Docker 服务编排配置
- **.env.docker**: Docker 环境变量配置
- **mysql-config/my.cnf**: MySQL 优化配置
- **mysql-init/01-init.sql**: 数据库初始化脚本

### ✅ 自动化脚本
- **scripts/docker-setup.sh**: 一键环境设置脚本
- **scripts/docker-manage.sh**: 数据库管理脚本
- **scripts/docker-config-test.sh**: 配置验证脚本

### ✅ npm 脚本集成
已在 package.json 中添加了 16 个 Docker 相关的 npm 脚本命令。

## 🚀 快速开始 (3 步设置)

### 步骤 1: 启动 Docker Desktop
```bash
# macOS: 从应用程序启动 Docker Desktop
# Windows: 启动 Docker Desktop 应用
# Linux: 启动 Docker 服务
sudo systemctl start docker
```

### 步骤 2: 一键设置数据库
```bash
# 设置 MySQL 数据库环境
npm run docker:setup

# 或者包含 Redis 缓存
npm run docker:setup:redis
```

### 步骤 3: 配置并启动应用
```bash
# 复制 Docker 环境配置
cp .env.docker .env.local

# 启动应用程序
npm run dev
```

## 📝 所有可用的 npm 命令

### 🔧 环境设置
```bash
npm run docker:setup          # 设置 MySQL 数据库环境
npm run docker:setup:redis    # 设置 MySQL + Redis 环境
```

### 🎮 服务管理
```bash
npm run docker:start          # 启动 MySQL 服务
npm run docker:start:redis    # 启动 MySQL + Redis 服务
npm run docker:stop           # 停止所有服务
npm run docker:restart        # 重启服务
npm run docker:status         # 查看服务状态
npm run docker:logs           # 查看服务日志
```

### 🗃️ 数据库操作
```bash
npm run docker:shell          # 进入 MySQL 命令行
npm run docker:migrate        # 运行 Prisma 数据库迁移
npm run docker:backup         # 备份数据库
npm run docker:reset          # 重置数据库 (删除所有数据)
```

### 🧹 维护命令
```bash
npm run docker:clean          # 清理未使用的 Docker 资源
```

## 🔧 高级管理脚本

### 直接使用管理脚本
```bash
# 查看所有可用命令
./scripts/docker-manage.sh help

# 启动服务 (包含 Redis)
./scripts/docker-manage.sh start --redis

# 查看特定服务日志
./scripts/docker-manage.sh logs mysql
./scripts/docker-manage.sh logs redis

# 恢复数据库备份
./scripts/docker-manage.sh restore backup/bitmarket_20241225_120000.sql
```

## 📊 配置详情

### 数据库配置
- **数据库类型**: MySQL 8.0
- **数据库名称**: bitmarket
- **用户名**: bitmarket_user
- **密码**: bitmarket_pass_2024
- **端口**: 3306
- **字符集**: utf8mb4

### Redis 配置 (可选)
- **版本**: Redis 7 Alpine
- **端口**: 6379
- **密码**: bitmarket_redis_2024

### 连接字符串
```env
# MySQL 连接
DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"

# Redis 连接
REDIS_URL="redis://:bitmarket_redis_2024@localhost:6379"
```

## 🔍 验证和测试

### 配置验证
```bash
# 运行配置验证脚本
bash scripts/docker-config-test.sh
```

### 手动验证步骤
```bash
# 1. 检查容器状态
docker-compose ps

# 2. 测试数据库连接
npm run docker:shell
# 在 MySQL shell 中执行:
SELECT 'Connection successful' AS status;

# 3. 验证 Prisma 集成
npx prisma generate
npm run docker:migrate

# 4. 启动应用程序
npm run dev
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. Docker daemon 未运行
**错误**: `Cannot connect to the Docker daemon`
```bash
# 解决方案: 启动 Docker Desktop 或 Docker 服务
# macOS/Windows: 启动 Docker Desktop 应用
# Linux: sudo systemctl start docker
```

#### 2. 端口冲突
**错误**: `Port 3306 is already in use`
```bash
# 检查端口占用
lsof -i :3306

# 解决方案: 修改端口配置
# 编辑 .env.docker 文件
MYSQL_PORT=3307
```

#### 3. 权限问题
**错误**: `Permission denied`
```bash
# 解决方案: 添加用户到 docker 组
sudo usermod -aG docker $USER
newgrp docker
```

#### 4. 内存不足
**错误**: 容器启动失败
```bash
# 解决方案: 调整 MySQL 内存配置
# 编辑 mysql-config/my.cnf
innodb_buffer_pool_size = 128M
```

### 日志查看
```bash
# 查看 MySQL 日志
npm run docker:logs

# 查看特定服务日志
./scripts/docker-manage.sh logs mysql

# 进入容器查看详细日志
docker-compose exec mysql bash
tail -f /var/log/mysql/error.log
```

## 🔒 安全最佳实践

### 1. 密码安全
```bash
# 生成强密码
openssl rand -base64 32

# 更新 .env.docker 中的密码
MYSQL_ROOT_PASSWORD=<strong_password>
MYSQL_PASSWORD=<strong_password>
```

### 2. 定期备份
```bash
# 设置自动备份
crontab -e

# 添加每日备份任务 (凌晨 2 点)
0 2 * * * cd /path/to/bitmarket && npm run docker:backup
```

### 3. 更新镜像
```bash
# 定期更新 Docker 镜像
docker-compose pull
docker-compose up -d
```

## 📊 性能监控

### 资源监控
```bash
# 监控容器资源使用
docker stats bitmarket-mysql bitmarket-redis

# 查看容器详细信息
docker inspect bitmarket-mysql
```

### 数据库性能
```bash
# 进入 MySQL 查看性能
npm run docker:shell

# 在 MySQL shell 中执行:
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';
```

## 🔗 相关文档

- [Docker 数据库详细指南](./deployment/docker-database-guide.md)
- [开发环境搭建](./deployment/development-setup.md)
- [数据库管理指南](./database-management-guide.md)
- [项目结构说明](./project-structure.md)

## 📞 获取帮助

如果遇到问题：
1. 查看故障排除部分
2. 运行配置验证脚本
3. 检查 Docker 和应用程序日志
4. 参考相关文档

---

*配置完成时间: 2025-07-25*  
*维护团队: BitMarket Development Team*
