# 🛡️ BitMarket 中间人面板使用指南

## 📋 概述

BitMarket 的中间人面板是专为已成为中间人的用户设计的控制中心，集成在用户的个人资料页面 (`/profile`) 中。当用户成为认证中间人后，该面板会自动显示，提供全面的中间人服务管理功能。

## 🎯 功能特性

### 1. 自动显示条件
- 用户必须是认证中间人 (`isMediator: true`)
- 面板会根据中间人状态动态显示不同内容
- 非中间人用户会看到申请成为中间人的入口

### 2. 核心功能模块

#### 📊 统计数据展示
- **活跃订单**: 当前正在处理的托管订单数量
- **已完成订单**: 历史完成的托管订单总数
- **总收益**: 累计获得的中间人服务费
- **待处理争议**: 需要处理的争议案例数量

#### 💰 保证金管理
- **总保证金**: 中间人账户的总保证金金额
- **已占用**: 当前活跃订单占用的保证金
- **可用余额**: 可用于新订单的保证金余额
- **使用率**: 保证金使用率的可视化进度条

#### 📈 本月表现
- **新订单**: 本月新增的托管订单数量
- **收益**: 本月获得的服务费收入
- **争议处理**: 本月处理的争议案例数量

#### 🚀 快捷操作
- **托管订单管理**: 跳转到托管订单列表 (带活跃订单徽章)
- **仲裁投票**: 参与争议案例投票 (带待处理争议徽章)
- **中间人控制台**: 完整的中间人管理面板
- **奖励券管理**: 查看和使用奖励券 (带券数量徽章)

#### 📋 最近订单
- 显示最近5个托管订单的概览
- 包含订单号、状态、交易双方、金额等信息
- 提供快速跳转到订单详情的链接

## 🔧 技术实现

### 数据源
- **用户信息**: `/api/user/profile` - 获取中间人基本信息
- **统计数据**: `/api/mediator/stats` - 获取详细的中间人统计

### 组件结构
```
/profile 页面
├── 个人信息卡片
├── MediatorPanel (中间人面板) ← 新增
│   ├── 中间人基本信息
│   ├── 统计数据展示
│   ├── 保证金状态
│   ├── 本月表现
│   ├── 快捷操作按钮
│   ├── 最近订单列表
│   └── 状态提示
└── 快捷操作区域
```

### API 端点
- `GET /api/mediator/stats` - 获取中间人统计数据
- `GET /api/user/profile` - 获取用户资料 (包含中间人字段)

## 📱 用户体验

### 状态指示
- **PENDING**: 申请审核中 - 显示黄色提示
- **ACTIVE**: 正常服务中 - 显示完整面板
- **SUSPENDED**: 账户暂停 - 显示红色警告

### 响应式设计
- 桌面端: 4列网格布局
- 平板端: 2列网格布局  
- 移动端: 单列布局

### 实时更新
- 统计数据自动刷新
- 徽章数量实时显示
- 状态变化即时反映

## 🚀 使用流程

### 1. 普通用户访问
1. 访问 `/profile` 页面
2. 看到"申请成为中间人"的入口
3. 点击跳转到申请页面 `/mediator/apply`

### 2. 中间人用户访问
1. 访问 `/profile` 页面
2. 自动显示中间人面板
3. 查看统计数据和快捷操作
4. 通过快捷按钮跳转到相关功能

### 3. 快捷操作使用
- **托管订单管理**: 直接跳转到 `/escrow/orders?role=mediator`
- **仲裁投票**: 跳转到 `/mediator/arbitration`
- **中间人控制台**: 跳转到 `/mediator/dashboard`
- **奖励券管理**: 跳转到 `/rewards/coupons`

## 🔍 监控和统计

### 关键指标
- 保证金使用率
- 月度订单增长
- 争议处理效率
- 用户满意度

### 性能优化
- 统计数据缓存
- 按需加载组件
- 图片懒加载
- API 请求优化

## 🛠️ 开发和测试

### 测试脚本
```bash
# 运行中间人面板功能测试
node scripts/test-mediator-panel.js
```

### 开发环境设置
1. 确保数据库包含所有中间人相关表
2. 创建测试中间人用户
3. 生成测试托管订单数据
4. 验证 API 端点正常工作

### 调试技巧
- 检查用户的 `isMediator` 字段
- 验证 API 返回的统计数据
- 确认组件条件渲染逻辑
- 测试不同中间人状态的显示

## 📚 相关文档

- [托管系统实现指南](./ESCROW_SYSTEM_IMPLEMENTATION.md)
- [中间人服务指南](./ESCROW_SYSTEM_GUIDE.md)
- [快速开始指南](./ESCROW_QUICK_START.md)

## 🎉 总结

中间人面板为认证中间人提供了一个集中的管理界面，让他们能够：

1. **快速了解业务状况** - 通过统计数据和图表
2. **高效管理订单** - 通过快捷操作和徽章提醒
3. **监控资金状态** - 通过保证金使用率显示
4. **跟踪业绩表现** - 通过月度统计和历史数据

这个设计大大提升了中间人的工作效率，同时为平台提供了更好的中间人服务质量保障。

---

*文档版本: v1.0.0*  
*最后更新: 2025-07-26*  
*BitMarket Development Team*
