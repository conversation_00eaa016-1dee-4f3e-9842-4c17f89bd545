# BitMarket 托管系统实现文档

## 概述

本文档详细描述了BitMarket平台的中间人托管系统实现，包括数据库模型、API接口、前端页面和核心功能。

## 🎯 系统特性

### 核心功能
- ✅ 中间人注册和认证系统
- ✅ 托管订单创建和管理
- ✅ 多方实时聊天系统
- ✅ 争议举报和仲裁投票
- ✅ 提现券奖励系统
- ✅ 区块链交易记录
- ✅ 资金冻结和释放机制

### 安全特性
- 🔒 多重身份验证
- 🔒 资金托管保护
- 🔒 争议仲裁机制
- 🔒 信誉评级系统
- 🔒 交易记录追踪

## 📊 数据库模型

### 新增模型

#### 1. EscrowOrder (托管订单)
```sql
- id: 主键
- orderId: 关联订单ID (唯一)
- mediatorId: 中间人ID
- buyerId: 买家ID
- sellerId: 卖家ID
- amount: 托管金额
- mediatorFee: 中间人费用
- platformFee: 平台费用
- status: 状态 (PENDING, FUNDED, SHIPPED, DELIVERED, COMPLETED, DISPUTED, CANCELLED)
- bnbTransactionHash: BNB链交易哈希
- mediatorWalletAddress: 中间人钱包地址
- 时间戳字段: fundedAt, shippedAt, deliveredAt, completedAt, disputedAt, cancelledAt
```

#### 2. MediatorApplication (中间人申请)
```sql
- id: 主键
- userId: 用户ID (唯一)
- bnbWalletAddress: BNB钱包地址
- depositAmount: 保证金金额
- feeRate: 手续费率
- experience: 相关经验
- introduction: 个人介绍
- status: 状态 (PENDING, APPROVED, REJECTED, SUSPENDED)
- reviewedBy: 审核人ID
- reviewNotes: 审核备注
- 时间戳字段: approvedAt, rejectedAt, suspendedAt
```

#### 3. EscrowDispute (托管争议)
```sql
- id: 主键
- escrowOrderId: 托管订单ID
- reporterId: 举报人ID
- reportedId: 被举报人ID
- reason: 争议原因
- description: 详细描述
- evidence: 证据文件 (JSON)
- status: 状态 (PENDING, UNDER_REVIEW, VOTING, RESOLVED, CLOSED)
- priority: 优先级 (LOW, MEDIUM, HIGH, URGENT)
- adminAssigned: 分配的管理员ID
- resolution: 解决方案
- resolvedAt: 解决时间
```

#### 4. WithdrawalVoucher (提现券)
```sql
- id: 主键
- code: 券码 (唯一)
- amount: 面值
- description: 描述
- validUntil: 有效期
- isUsed: 是否已使用
- usedBy: 使用者ID
- usedAt: 使用时间
- issuedBy: 发放者ID
```

#### 5. MediatorReward (中间人奖励)
```sql
- id: 主键
- mediatorId: 中间人ID
- rewardType: 奖励类型 (VOTING_PARTICIPATION, SUCCESSFUL_MEDIATION, MONTHLY_BONUS)
- amount: 奖励金额
- voucherId: 提现券ID
- description: 描述
- earnedAt: 获得时间
- claimedAt: 领取时间
```

#### 6. EscrowChatRoom (托管聊天室)
```sql
- id: 主键
- escrowOrderId: 托管订单ID (唯一)
- roomCode: 房间代码 (唯一)
- isActive: 是否活跃
```

#### 7. BlockchainTransaction (区块链交易)
```sql
- id: 主键
- txHash: 交易哈希 (唯一)
- network: 网络 (默认BNB_CHAIN)
- fromAddress: 发送地址
- toAddress: 接收地址
- amount: 金额
- tokenSymbol: 代币符号 (默认USDT)
- status: 状态 (PENDING, CONFIRMED, FAILED)
- blockNumber: 区块号
- gasUsed: 消耗Gas
- gasFee: Gas费用
- confirmations: 确认数
- relatedOrderId: 关联订单ID
- relatedEscrowId: 关联托管ID
```

### 扩展模型

#### User 模型新增字段
```sql
- bnbWalletAddress: BNB钱包地址
- bnbWalletVerified: 钱包是否验证
- bnbWalletVerifiedAt: 钱包验证时间
- mediatorFeeRate: 中间人费率
- mediatorDeposit: 中间人保证金
- mediatorReputation: 中间人信誉度
- mediatorSuccessRate: 中间人成功率
- mediatorTotalOrders: 中间人总订单数
- mediatorExperience: 中间人经验
- mediatorIntroduction: 中间人介绍
- mediatorLastActiveAt: 中间人最后活跃时间
- mediatorVerifiedAt: 中间人验证时间
```

#### Order 模型新增字段
```sql
- useEscrow: 是否使用托管
- escrowFee: 托管费用
- escrowFeeRate: 托管费率
- disputeStatus: 争议状态
- disputeReportedAt: 争议举报时间
- disputeResolvedAt: 争议解决时间
- disputeReason: 争议原因
```

## 🔌 API 接口

### 中间人申请相关
- `POST /api/mediator/application` - 提交中间人申请
- `GET /api/mediator/application` - 获取申请状态
- `PUT /api/mediator/application` - 更新申请信息

### 管理员审核相关
- `GET /api/admin/mediator/applications` - 获取申请列表
- `POST /api/admin/mediator/applications` - 审核申请
- `PUT /api/admin/mediator/applications` - 暂停/恢复中间人

### 托管订单相关
- `POST /api/escrow/create` - 创建托管订单
- `GET /api/escrow/create` - 获取可用中间人列表
- `GET /api/escrow/[escrowId]` - 获取托管订单详情
- `PUT /api/escrow/[escrowId]` - 更新托管订单状态

### 争议举报相关
- `POST /api/radar/report` - 提交争议举报
- `GET /api/radar/report` - 获取举报列表

### 仲裁投票相关
- `POST /api/escrow/dispute/vote` - 提交仲裁投票
- `GET /api/escrow/dispute/vote` - 获取待投票争议

### 提现券相关
- `GET /api/withdrawal/voucher` - 获取提现券列表
- `POST /api/withdrawal/voucher` - 使用提现券
- `PUT /api/withdrawal/voucher` - 验证提现券

### 管理员提现券管理
- `GET /api/admin/withdrawal-vouchers` - 获取提现券列表
- `POST /api/admin/withdrawal-vouchers` - 创建提现券
- `PUT /api/admin/withdrawal-vouchers` - 停用/启用提现券
- `DELETE /api/admin/withdrawal-vouchers` - 删除提现券

## 🎨 前端页面

### 用户页面
- `/mediator/apply` - 中间人申请页面
- `/radar` - 争议举报页面 (已更新支持托管争议)

### 管理员页面
- 中间人申请审核界面
- 提现券管理界面
- 争议处理界面

## 🔄 核心流程

### 1. 中间人注册流程
1. 用户提交申请 (保证金、费率、经验介绍)
2. 系统冻结保证金
3. 管理员审核申请
4. 审核通过后用户成为认证中间人
5. 保证金转为中间人保证金

### 2. 托管订单流程
1. 买家选择中间人创建托管订单
2. 系统扣除买家资金 (订单金额 + 中间人费用)
3. 中间人确认资金到账
4. 卖家发货
5. 买家确认收货
6. 中间人完成托管，分配资金

### 3. 争议处理流程
1. 任一方提交争议举报
2. 管理员初步审核
3. 进入仲裁投票阶段
4. 中间人投票决定结果
5. 执行仲裁决定
6. 参与投票的中间人获得奖励

### 4. 奖励机制
- 参与仲裁投票: 10 USDT免手续费提现券
- 成功完成托管: 根据订单金额的奖励
- 月度活跃奖励: 根据表现发放

## 🧪 测试验证

### 数据库测试
- ✅ 所有模型创建成功
- ✅ 关系映射正确
- ✅ 约束条件有效
- ✅ 索引优化完成

### 功能测试
- ✅ 中间人申请和审核流程
- ✅ 托管订单完整生命周期
- ✅ 争议举报和处理
- ✅ 提现券系统
- ✅ 资金冻结和释放
- ✅ 多方聊天室创建

### 测试脚本
- `scripts/migrate-escrow-system.js` - 数据库迁移脚本
- `scripts/test-escrow-database.js` - 功能测试脚本

## 📈 系统优势

### 安全性
- 多重验证机制
- 资金托管保护
- 争议仲裁系统
- 信誉评级体系

### 用户体验
- 简化的申请流程
- 实时状态更新
- 多方沟通渠道
- 透明的费用结构

### 可扩展性
- 模块化设计
- 灵活的奖励机制
- 支持多种争议类型
- 可配置的参数设置

## 🚀 部署说明

### 数据库迁移
```bash
# 运行迁移脚本
node scripts/migrate-escrow-system.js

# 推送schema到数据库
npx prisma db push

# 生成Prisma客户端
npx prisma generate
```

### 功能测试
```bash
# 运行完整功能测试
node scripts/test-escrow-database.js
```

## 📝 注意事项

1. **保证金管理**: 中间人保证金需要足够覆盖可能的托管订单金额
2. **费率设置**: 中间人费率范围为1%-30%，平台抽取30%
3. **争议处理**: 争议需要至少3个中间人投票，超过50%权重支持的决定获胜
4. **提现券**: 每个中间人每月最多获得一次投票参与奖励
5. **钱包验证**: 中间人必须验证BNB钱包地址才能提供服务

## 🔮 未来扩展

- 支持更多区块链网络
- 增加自动化争议处理
- 实现智能合约托管
- 添加保险机制
- 扩展奖励类型

---

## 🎯 新增功能完成情况

### 前端页面
- ✅ 商品详情页托管选择功能 (`app/products/[id]/page.tsx`)
- ✅ 托管订单详情页面 (`app/escrow/orders/[escrowId]/page.tsx`)
- ✅ 4人托管聊天室 (`app/chat/escrow/[escrowId]/page.tsx`)
- ✅ 仲裁投票页面 (`app/mediator/arbitration/page.tsx`)
- ✅ 中间人控制台完善 (`app/mediator/dashboard/page.tsx`)
- ✅ 管理员托管管理页面 (`app/admin/escrow-orders/page.tsx`)
- ✅ 争议举报功能更新 (`app/radar/page.tsx`)
- ✅ 系统测试页面 (`app/test-escrow/page.tsx`)

### API接口
- ✅ 托管聊天API (`app/api/chat/escrow/[escrowId]/route.ts`)
- ✅ 中间人托管订单API (`app/api/mediator/escrow-orders/route.ts`)
- ✅ 用户托管订单API (`app/api/user/escrow-orders/route.ts`)
- ✅ 管理员托管订单API (`app/api/admin/escrow-orders/route.ts`)
- ✅ 托管统计API (`app/api/admin/escrow-orders/stats/route.ts`)
- ✅ 争议管理API (`app/api/admin/escrow-orders/disputes/route.ts`)
- ✅ 测试订单创建API (`app/api/test/create-escrow-order/route.ts`)

### 核心功能
- ✅ 商品价格>100 USDT自动显示托管选择
- ✅ 中间人BNB钱包地址上传和验证
- ✅ 买方资金托管（商品金额+中间人手续费）
- ✅ 4人聊天室（买家、卖家、中间人、管理员）
- ✅ 完整的托管流程（资金确认→发货→收货→完成）
- ✅ 争议举报和仲裁投票系统
- ✅ 提现券奖励机制（10 USDT，7天有效期，每月限1张）
- ✅ 平台手续费抽成（中间人费用的30%）

### 测试验证
- ✅ 数据库迁移脚本测试通过
- ✅ 完整功能流程测试通过
- ✅ 所有API接口测试通过
- ✅ 前端页面功能测试通过

## 🚀 使用指南

### 用户使用流程
1. **购买商品**: 在商品详情页选择托管服务（>100 USDT）
2. **选择中间人**: 从可用中间人列表中选择合适的中间人
3. **支付资金**: 系统自动扣除商品金额+中间人费用
4. **等待确认**: 中间人确认资金到账
5. **卖家发货**: 卖家确认发货并提供快递信息
6. **买家收货**: 买家确认收到商品
7. **完成交易**: 中间人释放资金给卖家

### 中间人使用流程
1. **申请认证**: 提交中间人申请（保证金、费率、经验）
2. **等待审核**: 管理员审核申请材料
3. **开始服务**: 审核通过后可接受托管订单
4. **处理订单**: 确认资金、监督交易、处理争议
5. **获得收益**: 完成托管后获得服务费

### 争议处理流程
1. **提交举报**: 买家/卖家通过/radar页面举报问题
2. **管理员审核**: 管理员初步审核争议内容
3. **仲裁投票**: 升级到中间人仲裁投票
4. **执行决定**: 根据投票结果执行资金分配
5. **获得奖励**: 参与投票的中间人获得提现券

## 🔧 管理员功能

### 中间人管理
- 审核中间人申请
- 暂停/恢复中间人资格
- 查看中间人表现统计

### 托管订单管理
- 查看所有托管订单
- 强制完成/退款订单
- 重新分配中间人

### 争议处理
- 处理争议举报
- 升级到仲裁投票
- 直接解决争议

### 提现券管理
- 创建和分发提现券
- 查看使用统计
- 设置有效期和限制

## 📊 系统监控

访问 `/test-escrow` 页面可以：
- 查看系统运行状态
- 测试各项功能
- 查看统计数据
- 创建测试订单

**实现状态**: ✅ 完成
**测试状态**: ✅ 通过
**部署状态**: ✅ 可部署
