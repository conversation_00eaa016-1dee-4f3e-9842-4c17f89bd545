# 🧪 BitMarket 测试账号快速参考

## 🚀 快速登录信息

### 👑 管理员账号
```
邮箱: <EMAIL>
密码: admin123456
角色: 系统管理员
权限: 完整管理权限
```

### 👤 用户账号 1
```
邮箱: zhang<PERSON>@example.com
密码: user123456
姓名: 张三
地区: 北京市 朝阳区
信用等级: GOLD (85分)
保证金: 1,000 USDT
```

### 👤 用户账号 2
```
邮箱: <EMAIL>
密码: user123456
姓名: 李四
地区: 上海市 浦东新区
信用等级: SILVER (75分)
保证金: 500 USDT
```

## 🔧 管理命令

```bash
# 创建测试账号
npm run test:accounts

# 验证测试账号
npm run test:accounts:verify

# 重置并重新创建
npm run test:accounts:reset
```

## 🌐 访问地址

**应用地址**: http://localhost:3000

## 📊 账号特性

| 账号类型 | 邮箱 | 信用分数 | 保证金 | 权限 |
|---------|------|----------|--------|------|
| 管理员 | <EMAIL> | 100 | 10,000 USDT | 完整管理 |
| 用户1 | <EMAIL> | 85 | 1,000 USDT | 普通用户 |
| 用户2 | <EMAIL> | 75 | 500 USDT | 普通用户 |

---

*快速参考 | BitMarket v1.3.2*
