# 测试账号使用指南

## 🎯 测试目标

验证充值系统的管理员手动确认流程，以及真实API统计数据的显示。

## 👥 测试账号

### 1. 管理员账号
- **邮箱**: `<EMAIL>`
- **密码**: `admin123`
- **角色**: 管理员
- **权限**: 可以审核充值申请

### 2. 买家账号
- **邮箱**: `<EMAIL>`
- **密码**: `buyer123`
- **余额**: 1000 USDT
- **角色**: 普通用户
- **特点**: 有交易记录，可以测试充值功能

### 3. 卖家账号
- **邮箱**: `<EMAIL>`
- **密码**: `seller123`
- **余额**: 2000 USDT
- **角色**: 普通用户 + 中间人
- **特点**: 有销售记录，信誉等级较高

## 📱 测试商品

- **商品名称**: iPhone 15 Pro Max 256GB
- **价格**: ¥8999
- **卖家**: 测试卖家
- **状态**: 已审核通过

## 🔗 测试链接

1. **登录页面**: http://localhost:3001/auth/signin
2. **充值页面**: http://localhost:3001/deposit
3. **管理员后台**: http://localhost:3001/admin/deposits
4. **商品列表**: http://localhost:3001/products

## 🧪 测试流程

### 步骤1: 测试买家充值申请

1. 使用买家账号登录 (`<EMAIL>` / `buyer123`)
2. 访问充值页面: http://localhost:3001/deposit
3. 查看概览页面的真实统计数据：
   - ✅ **信誉等级**: 基于信用分数和履约率计算
   - ✅ **履约率**: 基于真实订单完成率 (应该显示100%)
   - ✅ **信任分数**: 综合评分 (信用分数70% + 履约率30%)
   - ✅ **综合评分**: 实时计算的综合分数
   - ✅ **月交易量**: 基于最近一个月的真实交易数据
4. 点击"充值"标签页
5. 选择支付方式（链上转账或币安支付）
6. 输入充值金额（如100 USDT）
7. 点击"立即充值"按钮
8. 查看返回的支付信息和PIN码
9. **模拟支付过程**：
   - 如果选择链上转账：复制钱包地址，模拟转账，获得交易哈希
   - 如果选择币安支付：扫描二维码，模拟支付，获得订单号
10. 在弹出的PIN码验证窗口中：
    - 输入返回的PIN码
    - 输入模拟的交易哈希或订单号
    - 点击"验证并提交"
11. 系统提示"充值申请已提交，请等待管理员确认到账"
12. 在"历史记录"标签页查看申请状态（应为"等待确认"）

### 步骤1.5: 查看充值历史记录

1. 在充值页面点击"历史记录"标签页
2. 查看充值记录列表，应该包含：
   - ✅ **已完成**: 100 USDT (链上转账) - 2天前
   - 🟠 **等待确认**: 50 USDT (币安支付) - 1小时前
   - 🟡 **待验证**: 200 USDT (链上转账) - 5分钟前
   - ❌ **已拒绝**: 75 USDT (币安支付) - 3天前
3. 每条记录显示：
   - 支付方式（链上转账/币安支付）
   - 状态标签（已完成/等待确认/待验证/已拒绝）
   - 充值金额
   - 交易凭证（如果有）
   - 申请时间

### 步骤1.6: 测试礼品卡购买功能

1. 在充值页面点击"礼品卡"标签页
2. 查看可用的礼品卡商品：
   - USDT充值卡 - 10元 (面值10, 售价9.5)
   - USDT充值卡 - 50元 (面值50, 售价47.5)
   - USDT充值卡 - 100元 (面值100, 售价92)
   - 平台礼品卡 - 25元 (面值25, 售价24)
   - 限时特惠卡 - 200元 (面值200, 售价180)
3. 选择一个商品点击"立即兑换"
4. 在弹出的购买确认框中：
   - 选择购买数量（1-10张）
   - 选择发货方式：
     - 🎫 **兑换码**: 生成兑换码，可分享给他人
     - 🎁 **礼品卡**: 生成礼品卡号，可作为礼品
     - 💰 **直充账号**: 直接充值到账户余额
5. 点击"确认购买"完成购买
6. 查看购买结果：
   - 兑换码发货：显示生成的兑换码
   - 礼品卡发货：显示礼品卡号
   - 直充账号：余额立即增加

### 步骤2: 测试管理员审核

1. 使用管理员账号登录 (`<EMAIL>` / `admin123`)
2. 访问管理员后台: http://localhost:3001/admin/deposits
3. 查看"充值申请"标签页
4. 找到刚才提交的充值申请（状态应为"等待确认"）
5. 点击"确认到账"按钮
6. 查看申请状态变为"已完成"

### 步骤3: 验证余额更新

1. 切换回买家账号
2. 刷新充值页面
3. 查看余额是否已更新
4. 在"历史记录"中查看充值记录状态

### 步骤4: 测试卖家账号统计

1. 使用卖家账号登录 (`<EMAIL>` / `seller123`)
2. 访问充值页面查看统计数据
3. 对比与买家账号的差异：
   - 卖家应该有更高的信誉等级
   - 更多的交易记录
   - 中间人身份显示

## ✅ 验证要点

### 1. 充值流程验证
- [ ] 支付方式只有"链上转账"和"币安支付"（银行转账和人工处理已删除）
- [ ] 点击充值后显示支付信息和PIN码
- [ ] 用户需要先完成支付，然后提交PIN码和交易信息
- [ ] PIN码验证成功后状态为"等待确认"
- [ ] 管理员可以看到待审核申请
- [ ] 确认后用户余额自动更新
- [ ] 历史记录正确显示

### 2. 统计数据验证
- [ ] **信誉等级**: 不是固定值，基于真实计算
- [ ] **履约率**: 显示真实的订单完成率
- [ ] **信任分数**: 综合计算的分数
- [ ] **综合评分**: 实时更新的评分
- [ ] **月交易量**: 基于真实交易数据

### 3. 数据一致性验证
- [ ] 不同账号显示不同的统计数据
- [ ] 卖家账号显示中间人身份
- [ ] 交易记录与统计数据匹配
- [ ] 余额变化正确记录

## 🐛 常见问题

### Q: 登录失败
A: 确保使用正确的邮箱和密码，注意大小写

### Q: 充值申请不显示
A: 刷新页面或检查网络连接

### Q: 统计数据为0
A: 确认测试数据已正确创建，可以重新运行创建脚本

### Q: 管理员看不到申请
A: 确认使用管理员账号登录，检查申请状态

## 📊 预期结果

### 买家账号统计数据
- 信誉等级: 3-4级
- 履约率: 100%
- 信任分数: 85-90分
- 月交易量: 498 USDT
- 担保次数: 2次

### 卖家账号统计数据
- 信誉等级: 4-5级
- 履约率: 100%
- 信任分数: 90-95分
- 月交易量: 498 USDT
- 担保次数: 2次
- 中间人身份: 是

## 🔄 重置测试数据

如需重新开始测试，运行以下命令：

```bash
node scripts/create-fresh-test-data.js
```

这将清理所有现有数据并创建新的测试账号和商品。

## 步骤5: 礼品卡管理员功能测试

### 步骤5.1: 礼品卡商品管理

1. 使用管理员账号登录 (`<EMAIL>` / `admin123`)
2. 访问 http://localhost:3001/admin/giftcard-products
3. 查看礼品卡商品列表，应该包含：
   - USDT充值卡 - 10元 (面值10, 售价9.5, 库存100)
   - USDT充值卡 - 50元 (面值50, 售价47.5, 库存50)
   - USDT充值卡 - 100元 (面值100, 售价92, 库存20)
   - 平台礼品卡 - 25元 (面值25, 售价24, 库存30)
   - 限时特惠卡 - 200元 (面值200, 售价180, 库存5)
   - 测试商品 - 已下架 (禁用状态)
4. 测试商品管理功能：
   - 点击"新建商品"创建新的礼品卡商品
   - 点击编辑按钮修改现有商品
   - 查看商品统计数据（礼品卡数量、订单数量）

### 步骤5.2: 礼品卡管理

1. 访问 http://localhost:3001/admin/giftcards
2. 查看生成的礼品卡列表
3. 筛选不同状态的礼品卡：
   - GENERATED: 已生成，待销售
   - ISSUED: 已发放给用户（礼品卡发货）
   - REDEEMED: 已兑换使用（直充账号）
   - SOLD: 已售出
4. 查看礼品卡详细信息：
   - 卡号、面值、状态
   - 购买者、兑换者信息
   - 创建时间、有效期

### 步骤5.3: 兑换码管理

1. 访问 http://localhost:3001/admin/redemption-codes
2. 查看生成的兑换码列表（兑换码发货方式生成的）
3. 查看兑换码信息：
   - 兑换码、类型、价值
   - 使用状态、有效期
   - 创建者、使用者信息
4. 管理兑换码：
   - 查看使用记录
   - 启用/禁用兑换码

## 总结

这个测试指南涵盖了BitMarket平台的主要功能：
- 用户注册和登录
- 充值申请和管理员审核
- 余额管理和历史记录
- 礼品卡购买和发货方式选择
- 管理员礼品卡商品管理
- 兑换码和礼品卡后台管理
- 不同用户角色的功能差异

通过这些测试，您可以验证平台的核心功能是否正常工作。
