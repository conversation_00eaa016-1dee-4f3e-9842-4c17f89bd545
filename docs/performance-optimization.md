# 性能优化指南

本文档详细说明了项目中实施的各种性能优化措施，以及如何使用和维护这些优化。

## 🚀 优化概览

### 已实施的优化

1. **编译和构建优化**
   - 启用 Next.js Turbopack
   - SWC 编译器优化
   - 代码分割和懒加载
   - 静态资源优化

2. **数据库优化**
   - 优化的 Prisma 客户端配置
   - 连接池管理
   - 查询优化和索引策略
   - 事务优化

3. **缓存系统**
   - Redis 缓存层
   - API 响应缓存
   - 数据库查询结果缓存
   - 会话缓存

4. **服务器优化**
   - Socket.IO 性能优化
   - 集群模式支持
   - 内存管理优化
   - 进程管理

5. **监控和分析**
   - 性能监控系统
   - 健康检查
   - 响应时间追踪
   - 资源使用监控

## 📋 快速开始

### 1. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

配置必要的环境变量：
- `DATABASE_URL`: 数据库连接字符串
- `REDIS_URL`: Redis 连接字符串（可选，但推荐）
- `NEXTAUTH_SECRET`: NextAuth 密钥
- `JWT_SECRET`: JWT 密钥

### 2. 性能设置

运行性能优化设置：
```bash
npm run perf:setup
```

### 3. 启动应用

开发环境（带性能监控）：
```bash
npm run dev
```

生产环境：
```bash
npm run start
```

集群模式（生产环境推荐）：
```bash
npm run start:cluster
```

## 🔧 配置说明

### Next.js 配置优化

`next.config.ts` 中的关键优化：

```typescript
// Turbopack 配置
experimental: {
  turbo: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
}

// 图片优化
images: {
  formats: ['image/webp', 'image/avif'],
  minimumCacheTTL: 60 * 60 * 24 * 30, // 30天缓存
}

// Webpack 优化
webpack: (config, { dev, isServer }) => {
  if (!dev) {
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    }
  }
}
```

### 数据库优化

使用优化的 Prisma 客户端：

```typescript
import { optimizedQueries } from '@/lib/prisma-optimized'

// 优化的商品查询
const products = await optimizedQueries.getProducts(page, limit, filters)
```

### 缓存配置

Redis 缓存使用示例：

```typescript
import { cache, CACHE_TTL } from '@/lib/cache'

// 缓存用户数据
await cache.setUserCache(userId, userData, CACHE_TTL.LONG)

// 获取缓存数据
const cachedUser = await cache.getUserCache(userId)
```

### API 缓存中间件

为 API 路由添加缓存：

```typescript
import { withApiCache, cacheConfigs } from '@/lib/api-cache-middleware'

export const GET = withApiCache(cacheConfigs.products)(async function(request) {
  // API 逻辑
})
```

## 📊 监控和分析

### 性能监控

查看性能统计：
```bash
npm run perf:monitor
```

### 健康检查

运行系统健康检查：
```bash
npm run health:check
```

### 缓存管理

清理缓存：
```bash
npm run cache:clear
```

## 🎯 性能指标

### 目标指标

- **API 响应时间**: < 200ms (平均)
- **数据库查询时间**: < 100ms (平均)
- **页面加载时间**: < 2s (首次)
- **内存使用**: < 512MB (开发环境)

### 监控指标

系统会自动监控以下指标：
- API 响应时间分布
- 数据库查询性能
- 内存和CPU使用情况
- 缓存命中率
- 错误率

## 🔍 故障排除

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 服务是否运行
   - 验证 `REDIS_URL` 配置
   - 查看防火墙设置

2. **数据库查询慢**
   - 检查数据库索引
   - 分析慢查询日志
   - 考虑查询优化

3. **内存使用过高**
   - 检查内存泄漏
   - 优化缓存策略
   - 调整 Node.js 内存限制

### 调试工具

启用调试模式：
```bash
DEBUG_MODE=true npm run dev
```

查看查询日志：
```bash
ENABLE_QUERY_LOGGING=true npm run dev
```

## 📈 性能优化建议

### 短期优化

1. **启用 Redis 缓存**
   - 显著提升 API 响应速度
   - 减少数据库查询负载

2. **优化数据库查询**
   - 添加必要的索引
   - 使用 `select` 限制返回字段
   - 避免 N+1 查询问题

3. **启用压缩**
   - 配置 gzip 压缩
   - 优化图片格式

### 中期优化

1. **实施 CDN**
   - 加速静态资源加载
   - 减少服务器负载

2. **数据库升级**
   - 从 SQLite 升级到 PostgreSQL
   - 实施读写分离

3. **微服务架构**
   - 拆分大型服务
   - 实现服务间缓存

### 长期优化

1. **容器化部署**
   - 使用 Docker 容器
   - 实现自动扩缩容

2. **负载均衡**
   - 多实例部署
   - 智能流量分发

3. **边缘计算**
   - 实施边缘缓存
   - 就近服务用户

## 🛠️ 维护指南

### 定期维护任务

1. **每日**
   - 检查系统健康状态
   - 监控性能指标
   - 清理过期缓存

2. **每周**
   - 分析性能趋势
   - 优化慢查询
   - 更新依赖包

3. **每月**
   - 数据库维护
   - 性能基准测试
   - 容量规划评估

### 监控告警

建议设置以下告警：
- API 响应时间 > 2s
- 数据库查询时间 > 1s
- 内存使用率 > 80%
- 错误率 > 1%
- 缓存命中率 < 70%

## 📚 相关文档

- [Next.js 性能优化](https://nextjs.org/docs/advanced-features/measuring-performance)
- [Prisma 性能指南](https://www.prisma.io/docs/guides/performance-and-optimization)
- [Redis 最佳实践](https://redis.io/docs/manual/performance/)
- [Node.js 性能优化](https://nodejs.org/en/docs/guides/simple-profiling/)

## 🤝 贡献

如果您发现性能问题或有优化建议，请：
1. 创建 Issue 描述问题
2. 提供性能测试数据
3. 提交 Pull Request

---

**注意**: 性能优化是一个持续的过程，需要根据实际使用情况不断调整和改进。
