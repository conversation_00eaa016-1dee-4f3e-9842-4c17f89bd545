# BitMarket 托管服务完整指南

## 🎯 功能概述

BitMarket 托管服务是一个基于区块链的中间人托管系统，为 USDT C2C 交易提供安全保障。系统集成了智能合约、多方聊天、争议仲裁和激励机制，确保交易的安全性和公平性。

## 🏗️ 系统架构

### 核心组件

1. **区块链集成层**
   - BNB Chain 钱包连接
   - USDT 智能合约交互
   - 交易监控和验证

2. **托管服务层**
   - 资金托管管理
   - 订单状态跟踪
   - 自动化流程控制

3. **争议仲裁系统**
   - 多方投票机制
   - 智能权重计算
   - 自动执行决议

4. **激励机制**
   - 自动奖励发放
   - 提现券管理
   - 信誉评级系统

5. **安全风控系统**
   - 实时风险评估
   - 多维度检查
   - 智能限额管理

## 📋 功能详细说明

### 1. 中间人注册和认证

#### 注册流程
```typescript
// 1. 用户申请成为中间人
POST /api/mediator/application
{
  "bnbWalletAddress": "0x...",
  "depositAmount": 1000,
  "feeRate": 0.02,
  "description": "专业中间人服务"
}

// 2. 钱包验证
POST /api/blockchain/verify-wallet
{
  "address": "0x...",
  "signature": "0x...",
  "timestamp": 1640995200000
}
```

#### 认证要求
- ✅ BNB Chain 钱包地址验证
- ✅ 最低保证金：1000 USDT
- ✅ 身份信息完善
- ✅ 服务费率设置（1%-30%）

### 2. 托管交易流程

#### 创建托管订单
```typescript
// 买家选择托管服务
POST /api/escrow/create
{
  "orderId": "order-123",
  "mediatorId": "mediator-456",
  "amount": 500,
  "buyerWallet": "0x...",
  "sellerWallet": "0x..."
}
```

#### 完整交易流程

1. **订单创建** (`PENDING`)
   - 买家选择中间人
   - 系统创建托管订单
   - 生成四方聊天室

2. **资金托管** (`FUNDED`)
   - 买家转账到中间人地址
   - 系统验证链上交易
   - 通知卖家发货

3. **商品发货** (`SHIPPED`)
   - 卖家提供物流信息
   - 系统跟踪物流状态
   - 买家确认收货

4. **交易完成** (`COMPLETED`)
   - 中间人释放资金给卖家
   - 扣除平台费用（30%）
   - 更新信誉评分

### 3. 多方聊天系统

#### 聊天室功能
- 🔐 **四方参与**: 管理员、中间人、买家、卖家
- 💬 **实时通信**: WebSocket 支持
- 📎 **文件分享**: 图片、文档上传
- 🔒 **证据保全**: 聊天记录加密存储
- 📄 **导出功能**: PDF 格式导出

#### 使用示例
```typescript
// 发送消息
POST /api/escrow/chat/{chatRoomId}/messages
{
  "content": "商品已发货，物流单号：SF1234567890",
  "messageType": "TEXT"
}

// 上传文件
POST /api/escrow/chat/upload
FormData: {
  file: File,
  escrowOrderId: "escrow-123"
}
```

### 4. 争议处理机制

#### 举报流程
```typescript
// 提交争议
POST /api/escrow/dispute
{
  "escrowOrderId": "escrow-123",
  "reason": "PRODUCT_NOT_RECEIVED",
  "description": "买家未收到商品，卖家无回应",
  "evidence": ["file1.jpg", "chat-export.pdf"]
}
```

#### 仲裁投票
```typescript
// 中间人投票
POST /api/escrow/dispute/vote
{
  "disputeId": "dispute-456",
  "decision": "FAVOR_BUYER",
  "reasoning": "买家提供了充分的证据证明未收到商品"
}
```

#### 投票权重计算
```javascript
function calculateVoteWeight(reputation, successRate, totalOrders) {
  const baseWeight = 1.0
  const reputationBonus = Math.min((reputation - 4.0) * 0.5, 1.0)
  const successRateBonus = Math.min((successRate - 0.8) * 2, 0.5)
  const experienceBonus = Math.min(Math.log10(totalOrders / 10) * 0.3, 0.5)
  
  return baseWeight + reputationBonus + successRateBonus + experienceBonus
}
```

### 5. 激励机制详解

#### 奖励类型

1. **仲裁投票奖励**
   - 金额：10 USDT 免手续费提现券
   - 条件：投票与最终决定一致
   - 限制：每月最多 1 张

2. **成功调解奖励**
   - 金额：20 USDT 免手续费提现券
   - 条件：托管订单成功完成
   - 限制：每月最多 5 张

3. **月度活跃奖励**
   - 金额：根据信誉等级（10-50 USDT）
   - 条件：当月至少参与 1 次仲裁或调解
   - 发放：每月自动发放

#### 信誉等级制度
```typescript
enum ReputationLevel {
  BRONZE = "青铜级", // 3.5-4.0 分
  SILVER = "白银级", // 4.0-4.5 分
  GOLD = "黄金级",   // 4.5-4.8 分
  DIAMOND = "钻石级" // 4.8+ 分
}
```

### 6. 安全风控系统

#### 风险评估维度

1. **金额风险**
   - 中等：≥1000 USDT
   - 高：≥5000 USDT
   - 临界：≥10000 USDT

2. **用户风险**
   - 新用户（注册7天内）
   - 信誉分数 <3.0
   - 黑名单用户

3. **交易频率风险**
   - 日交易次数 >10
   - 日交易金额 >5000 USDT

4. **钱包风险**
   - 黑名单地址检查
   - 第三方风险评估

#### 风险控制措施
```typescript
interface RiskControl {
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  requiresApproval: boolean
  requiresKYC: boolean
  maxAllowedAmount?: number
  recommendations: string[]
}
```

## 🔧 API 接口文档

### 认证接口

```typescript
// 钱包验证
POST /api/blockchain/verify-wallet
Request: {
  address: string
  signature: string
  timestamp: number
}
Response: {
  success: boolean
  data: {
    address: string
    verified: boolean
    verifiedAt: string
  }
}
```

### 托管接口

```typescript
// 创建托管订单
POST /api/escrow/create
Request: {
  orderId: string
  mediatorId: string
  amount: number
  buyerWallet: string
  sellerWallet: string
}
Response: {
  success: boolean
  data: {
    escrowOrderId: string
    chatRoomId: string
    status: string
  }
}

// 获取托管订单列表
GET /api/escrow/orders?status=PENDING&limit=10&offset=0
Response: {
  success: boolean
  data: {
    orders: EscrowOrder[]
    pagination: {
      total: number
      hasMore: boolean
    }
  }
}
```

### 争议接口

```typescript
// 提交争议
POST /api/escrow/dispute
Request: {
  escrowOrderId: string
  reason: string
  description: string
  evidence: string[]
}
Response: {
  success: boolean
  data: {
    disputeId: string
    status: string
    votingDeadline: string
  }
}

// 仲裁投票
POST /api/escrow/dispute/vote
Request: {
  disputeId: string
  decision: 'FAVOR_BUYER' | 'FAVOR_SELLER' | 'SPLIT_FUNDS'
  reasoning: string
}
Response: {
  success: boolean
  data: {
    voteId: string
    voteWeight: number
  }
}
```

### 奖励接口

```typescript
// 获取奖励统计
GET /api/mediator/rewards
Response: {
  success: boolean
  data: {
    totalRewards: number
    totalAmount: number
    availableVouchers: number
    recentRewards: Reward[]
  }
}

// 使用提现券
POST /api/withdrawal/voucher/use
Request: {
  voucherId: string
  withdrawalAmount: number
}
Response: {
  success: boolean
  data: {
    savedFee: number
    remainingAmount: number
  }
}
```

## 🎮 使用示例

### 完整交易流程示例

```typescript
// 1. 买家选择托管服务
const escrowOrder = await createEscrowOrder({
  orderId: "ORD-2024-001",
  mediatorId: "MED-001",
  amount: 500,
  buyerWallet: "0xBuyer...",
  sellerWallet: "0xSeller..."
})

// 2. 买家转账到中间人地址
const transaction = await transferUSDT({
  from: "0xBuyer...",
  to: "0xMediator...",
  amount: 515, // 500 + 15 手续费
  txHash: "0xTxHash..."
})

// 3. 系统验证交易
const verification = await verifyTransaction({
  txHash: "0xTxHash...",
  expectedAmount: 515,
  expectedFrom: "0xBuyer...",
  expectedTo: "0xMediator..."
})

// 4. 卖家发货
await sendMessage({
  chatRoomId: escrowOrder.chatRoomId,
  content: "商品已发货，物流单号：SF1234567890",
  attachments: ["shipping-receipt.jpg"]
})

// 5. 买家确认收货
await updateOrderStatus({
  escrowOrderId: escrowOrder.id,
  status: "COMPLETED",
  confirmationType: "BUYER_CONFIRMED"
})

// 6. 中间人释放资金
await releaseFunds({
  escrowOrderId: escrowOrder.id,
  toAddress: "0xSeller...",
  amount: 500
})
```

### 争议处理示例

```typescript
// 1. 买家提交争议
const dispute = await createDispute({
  escrowOrderId: "ESC-001",
  reason: "PRODUCT_NOT_RECEIVED",
  description: "已过发货期限，卖家未发货且无回应",
  evidence: ["chat-screenshot.png", "order-details.pdf"]
})

// 2. 系统自动分配仲裁员
const arbitrators = await assignArbitrators({
  disputeId: dispute.id,
  requiredVotes: 3,
  excludeParties: ["buyer-id", "seller-id", "mediator-id"]
})

// 3. 仲裁员投票
for (const arbitrator of arbitrators) {
  await submitVote({
    disputeId: dispute.id,
    voterId: arbitrator.id,
    decision: "FAVOR_BUYER",
    reasoning: "卖家未能提供发货证明"
  })
}

// 4. 系统自动执行决议
const resolution = await executeResolution({
  disputeId: dispute.id,
  finalDecision: "FAVOR_BUYER",
  refundAmount: 500
})

// 5. 发放仲裁奖励
for (const arbitrator of arbitrators) {
  await issueVotingReward({
    mediatorId: arbitrator.id,
    disputeId: dispute.id,
    voteDecision: arbitrator.decision,
    finalDecision: resolution.decision
  })
}
```

## 📊 监控和分析

### 关键指标

1. **业务指标**
   - 托管订单数量和金额
   - 争议率和解决时间
   - 中间人活跃度和满意度

2. **技术指标**
   - API 响应时间
   - 区块链交易确认时间
   - 系统可用性

3. **安全指标**
   - 风险评估准确率
   - 异常交易检测率
   - 安全事件数量

### 报表示例

```typescript
// 获取托管服务统计
GET /api/admin/escrow/stats?period=30d
Response: {
  totalOrders: 1250,
  totalAmount: 625000,
  completionRate: 0.94,
  averageResolutionTime: "2.3 days",
  disputeRate: 0.06,
  mediatorSatisfaction: 4.7
}
```

## 🚀 最佳实践

### 中间人服务建议

1. **响应时间**: 2小时内回复消息
2. **专业服务**: 提供详细的服务说明
3. **风险控制**: 合理设置保证金和服务费率
4. **持续改进**: 根据用户反馈优化服务

### 用户使用建议

1. **选择中间人**: 优先选择高信誉、低费率的中间人
2. **保留证据**: 及时保存聊天记录和交易凭证
3. **及时沟通**: 遇到问题及时在聊天室沟通
4. **理性维权**: 通过正当渠道解决争议

### 系统维护建议

1. **定期备份**: 每日备份数据库和重要文件
2. **监控告警**: 设置关键指标的监控告警
3. **安全更新**: 及时更新系统和依赖包
4. **性能优化**: 定期分析和优化系统性能

## 📞 技术支持

如需技术支持，请联系：

- **技术文档**: https://docs.bitmarket.com
- **API 文档**: https://api.bitmarket.com/docs
- **GitHub**: https://github.com/bitmarket/escrow-service
- **邮箱**: <EMAIL>
- **微信群**: 扫描二维码加入技术交流群

---

*本文档持续更新，最新版本请访问官方文档网站。*
