# 🛡️ BitMarket 区块链托管服务系统

## 📋 系统概述

BitMarket 托管服务系统是一个基于区块链的中间人托管解决方案，为大金额交易提供安全保障。系统集成了 BNB Chain 钱包、智能合约交互、争议仲裁和奖励机制。

### 🎯 核心特性

- **🔒 资金安全托管**：买家资金由中间人安全托管，确认收货后释放
- **👥 专业中间人服务**：经过认证的中间人提供托管服务
- **⚖️ 争议仲裁机制**：多方投票的公正仲裁系统
- **💬 多方聊天室**：买家、卖家、中间人、管理员四方沟通
- **🎁 激励奖励系统**：中间人参与仲裁获得奖励券
- **🔗 区块链集成**：BNB Chain 钱包验证和交易监控

## 🏗️ 系统架构

### 数据库设计

#### 用户表扩展 (User)
```sql
-- 中间人相关字段
isMediator: Boolean         -- 是否为中间人
mediatorStatus: String      -- INACTIVE, PENDING, ACTIVE, SUSPENDED
mediatorFeeRate: Float      -- 服务费率 (1%-30%)
mediatorDeposit: Float      -- 保证金金额
mediatorReputation: Float   -- 信誉评分
walletVerified: Boolean     -- 钱包是否已验证
```

#### 订单表扩展 (Order)
```sql
-- 托管相关字段
useEscrow: Boolean          -- 是否使用托管服务
escrowStatus: String        -- 托管状态
escrowFee: Float           -- 托管服务费
mediatorId: String         -- 中间人ID
escrowFundedAt: DateTime   -- 资金托管时间
escrowReleasedAt: DateTime -- 资金释放时间
```

#### 新增表结构
- **EscrowTransaction**: 托管交易记录
- **ArbitrationCase**: 仲裁案例
- **ArbitrationVote**: 仲裁投票
- **RewardCoupon**: 奖励券管理

### API 架构

#### 托管服务 API (`/api/escrow/*`)
- `GET /api/escrow/config` - 获取托管配置
- `POST /api/escrow/orders` - 创建托管订单
- `GET /api/escrow/orders` - 获取托管订单列表
- `PUT /api/escrow/orders/[id]` - 更新订单状态

#### 中间人管理 API (`/api/mediator/*`)
- `POST /api/mediator/register` - 申请成为中间人
- `GET /api/mediator/register` - 获取中间人状态
- `GET /api/mediator/list` - 获取可用中间人列表

#### 仲裁系统 API (`/api/arbitration/*`)
- `POST /api/arbitration/cases` - 创建仲裁案例
- `GET /api/arbitration/cases` - 获取仲裁案例列表
- `POST /api/arbitration/vote` - 提交仲裁投票

#### 奖励系统 API (`/api/rewards/*`)
- `GET /api/rewards/coupons` - 获取奖励券列表
- `POST /api/rewards/coupons/[id]/use` - 使用奖励券

## 🔄 业务流程

### 1. 托管交易流程

```mermaid
sequenceDiagram
    participant B as 买家
    participant M as 中间人
    participant S as 卖家
    participant P as 平台

    B->>P: 选择托管服务
    B->>M: 支付(商品金额+托管费)
    M->>S: 通知发货
    S->>M: 确认发货
    M->>B: 通知已发货
    B->>M: 确认收货
    M->>S: 释放资金
    M->>P: 支付平台费用(30%)
```

### 2. 中间人注册流程

1. **提交申请**：填写钱包地址、费率、保证金等信息
2. **管理员审核**：验证钱包地址和资质
3. **保证金充值**：向指定地址转入保证金
4. **激活账户**：审核通过后激活中间人身份

### 3. 争议仲裁流程

1. **提交争议**：买家或卖家在 `/radar` 页面提交争议
2. **案例创建**：系统创建仲裁案例，状态为 `VOTING`
3. **中间人投票**：活跃中间人参与投票（7天期限）
4. **自动执行**：根据投票结果自动执行资金分配
5. **奖励发放**：参与投票的中间人获得奖励券

## 🎨 前端组件

### 核心组件

#### EscrowServiceSelector
- **功能**：托管服务选择和中间人选择
- **位置**：`components/escrow/EscrowServiceSelector.tsx`
- **特性**：
  - 最低金额验证（100 USDT）
  - 中间人列表展示
  - 费用计算和预览

#### EscrowOrderStatus
- **功能**：托管订单状态展示和操作
- **位置**：`components/escrow/EscrowOrderStatus.tsx`
- **特性**：
  - 状态时间线展示
  - 角色相关操作按钮
  - 交易信息详情

#### MediatorRegistration
- **功能**：中间人注册表单
- **位置**：`components/mediator/MediatorRegistration.tsx`
- **特性**：
  - 钱包地址验证
  - 费率滑块选择
  - 保证金计算

#### ArbitrationVoting
- **功能**：仲裁投票界面
- **位置**：`components/arbitration/ArbitrationVoting.tsx`
- **特性**：
  - 投票选项展示
  - 实时统计更新
  - 截止时间倒计时

### 页面结构

#### 托管订单管理 (`/escrow/orders`)
- 多角色视图（买家、卖家、中间人）
- 状态筛选和搜索
- 订单详情链接

#### 中间人控制台 (`/mediator/dashboard`)
- 中间人注册/状态展示
- 统计数据概览
- 快捷操作入口

#### 争议举报中心 (`/radar`)
- 集成托管争议举报功能
- 案例列表和状态跟踪
- 管理员审核界面

## 🔒 安全机制

### 权限控制

1. **用户认证**：所有 API 都需要有效的用户会话
2. **角色验证**：根据用户角色限制操作权限
3. **订单权限**：只有相关方可以查看和操作订单

### 输入验证

1. **参数验证**：严格验证所有输入参数
2. **业务规则**：检查费率范围、保证金要求等
3. **钱包验证**：验证 BNB Chain 钱包地址格式

### 资金安全

1. **保证金检查**：确保中间人保证金足够承接订单
2. **交易监控**：记录所有资金流向和状态变更
3. **争议保护**：争议期间冻结资金操作

## 💰 经济模型

### 费用结构

- **托管服务费**：由中间人设定（1%-30%）
- **平台抽成**：从中间人费用中抽取 30%
- **最低金额**：100 USDT 起

### 奖励机制

- **仲裁奖励**：参与投票获得 10 USDT 免手续费提现券
- **奖励限制**：每月最多获得 1 张奖励券
- **有效期限**：奖励券 7 天有效期

### 保证金管理

- **最低要求**：1000 USDT
- **占用计算**：活跃订单金额总和不能超过保证金
- **费用扣除**：平台费用从保证金中自动扣除

## 🧪 测试指南

### 自动化测试

运行完整的系统测试：
```bash
npm run test:escrow-system
```

测试覆盖：
- ✅ 数据库模式验证
- ✅ API 路由完整性
- ✅ 前端组件存在性
- ✅ 功能完整性检查
- ✅ 安全机制验证

### 手动测试流程

#### 1. 中间人注册测试
1. 访问 `/mediator/dashboard`
2. 填写注册表单
3. 验证表单验证逻辑
4. 检查提交后状态

#### 2. 托管订单测试
1. 在商品页面选择托管服务
2. 选择中间人
3. 创建托管订单
4. 测试状态流转

#### 3. 争议仲裁测试
1. 在 `/radar` 页面提交争议
2. 中间人参与投票
3. 验证自动执行逻辑
4. 检查奖励发放

## 🚀 部署指南

### 环境要求

- Node.js 18+
- MySQL 8.0+
- Redis (可选，用于缓存)

### 部署步骤

1. **数据库迁移**
   ```bash
   npx prisma db push
   npx prisma generate
   ```

2. **环境变量配置**
   ```env
   # 区块链相关
   BNB_CHAIN_RPC_URL=https://bsc-dataseed.binance.org/
   WALLET_PRIVATE_KEY=your_private_key
   
   # 托管配置
   ESCROW_MIN_AMOUNT=100
   PLATFORM_FEE_RATE=0.3
   ```

3. **启动服务**
   ```bash
   npm run build
   npm start
   ```

### 监控和维护

- **交易监控**：监控区块链交易状态
- **争议处理**：及时处理仲裁案例
- **保证金管理**：监控中间人保证金状态
- **系统健康**：定期检查 API 和数据库状态

## 📞 技术支持

### 常见问题

**Q: 托管服务的最低金额是多少？**
A: 100 USDT，低于此金额无法使用托管服务。

**Q: 中间人费率范围是多少？**
A: 1%-30%，中间人可以自由设定在此范围内的费率。

**Q: 仲裁投票的截止时间是多久？**
A: 7天，超过截止时间系统会自动处理结果。

**Q: 奖励券的有效期是多久？**
A: 7天，过期后自动失效。

### 联系方式

- **技术支持**：<EMAIL>
- **业务咨询**：<EMAIL>
- **紧急联系**：<EMAIL>

## 👥 用户操作指南

### 买家使用指南

#### 1. 选择托管服务
1. 浏览商品时，在商品详情页面查看是否支持托管服务
2. 如果商品金额 ≥ 100 USDT，会显示托管服务选项
3. 勾选"使用托管服务"
4. 从列表中选择合适的中间人（考虑费率和信誉）
5. 确认总费用（商品金额 + 托管费）

#### 2. 支付托管资金
1. 创建订单后，系统会生成支付信息
2. 使用 BNB Chain 钱包向指定地址转账
3. 在订单页面提交交易哈希
4. 等待中间人确认资金到账

#### 3. 跟踪订单状态
1. 在 `/escrow/orders` 页面查看订单状态
2. 资金托管后，卖家会收到发货通知
3. 卖家发货后，您会收到物流信息
4. 收到商品后，点击"确认收货"释放资金

#### 4. 争议处理
1. 如有问题，在 `/radar` 页面提交争议
2. 详细描述问题并提供证据
3. 等待中间人仲裁投票结果
4. 根据仲裁结果执行退款或释放资金

### 卖家使用指南

#### 1. 接受托管订单
1. 买家选择托管服务后，您会收到订单通知
2. 等待买家支付托管资金
3. 中间人确认资金到账后，准备发货

#### 2. 发货流程
1. 在订单详情页面点击"确认发货"
2. 填写快递公司和快递单号
3. 系统会通知买家和中间人
4. 保持物流信息更新

#### 3. 资金释放
1. 买家确认收货后，资金自动释放
2. 如买家长时间未确认，可申请中间人介入
3. 资金释放后，交易完成

### 中间人使用指南

#### 1. 注册成为中间人
1. 访问 `/mediator/dashboard`
2. 填写注册表单：
   - BNB Chain 钱包地址
   - 服务费率（1%-30%）
   - 保证金金额（≥1000 USDT）
   - 个人介绍和经验
3. 提交申请等待审核
4. 审核通过后充值保证金激活账户

#### 2. 管理托管订单
1. 在中间人控制台查看待处理订单
2. 确认买家资金到账
3. 监督卖家发货流程
4. 处理买家确认收货
5. 必要时介入争议处理

#### 3. 参与仲裁投票
1. 查看 `/arbitration/cases?role=mediator&status=VOTING`
2. 仔细阅读争议详情和证据
3. 根据事实进行公正投票：
   - 支持买家：认为应该退款
   - 支持卖家：认为应该释放资金
   - 中性：需要更多信息
4. 填写投票理由
5. 获得 10 USDT 免手续费提现券奖励

#### 4. 保证金管理
1. 监控保证金余额和占用情况
2. 及时补充保证金以承接更多订单
3. 平台费用会自动从保证金中扣除
4. 可以申请提取多余的保证金

### 管理员操作指南

#### 1. 中间人审核
1. 查看中间人申请列表
2. 验证钱包地址和资质信息
3. 批准或拒绝申请
4. 监控中间人表现和信誉

#### 2. 争议处理
1. 在 `/radar` 页面查看所有争议案例
2. 查看仲裁投票进展
3. 必要时介入复杂争议
4. 监控仲裁结果执行

#### 3. 奖励券管理
1. 查看奖励券发放统计
2. 手动发放特殊奖励券
3. 处理过期和异常券
4. 监控使用情况

#### 4. 系统监控
1. 监控托管资金流向
2. 检查异常交易和争议
3. 维护系统配置参数
4. 处理用户反馈和问题

---

*文档版本: v1.0.0*
*最后更新: 2025-07-25*
*BitMarket Development Team*
