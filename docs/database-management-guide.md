# 数据库管理指南

## 概述

BitMarket项目提供了多种数据库管理工具，帮助开发者轻松管理数据库数据。

## 🛠️ 管理工具

### 1. 可视化管理 - Prisma Studio

**启动方式**：
```bash
# 方法1: 直接启动
npx prisma studio

# 方法2: 使用管理工具
node scripts/database-manager.js studio
```

**功能**：
- 可视化查看所有表数据
- 直接编辑记录
- 添加新记录
- 删除记录
- 查看表关系

**访问地址**：http://localhost:5555

### 2. 命令行管理工具

#### 交互式管理器
```bash
node scripts/database-manager.js
```

提供菜单选择：
1. 启动 Prisma Studio
2. 显示数据库信息
3. 备份数据库
4. 生成测试数据
5. 删除所有数据
6. 删除测试数据

#### 快速命令
```bash
# 显示数据库信息
node scripts/database-manager.js info

# 备份数据库
node scripts/database-manager.js backup

# 生成测试数据
node scripts/database-manager.js test

# 清空数据库
node scripts/database-manager.js clean
```

### 3. 快速删除工具

```bash
# 删除测试数据
node scripts/quick-delete.js test

# 删除所有数据
node scripts/quick-delete.js all

# 删除特定用户
node scripts/quick-delete.<NAME_EMAIL>

# 重置数据库
node scripts/quick-delete.js reset
```

### 4. 详细清理工具

```bash
node scripts/database-cleanup.js
```

提供详细的删除选项：
- 删除所有数据（需要确认）
- 删除测试数据
- 按表删除数据
- 重置数据库
- 查看统计信息

## 📊 常用操作

### 查看数据库状态

```bash
node scripts/database-manager.js info
```

输出示例：
```
📊 数据库信息
========================================
📋 表记录统计:
  users          : 18 条
  products       : 12 条
  orders         : 5 条
  demands        : 8 条
  reviews        : 3 条
  messages       : 15 条
  accounts       : 2 条

📅 最近活动:
  最新用户:
    • 测试用户 (2025/7/21)
    • 演示用户 (2025/7/21)
  
  最新商品:
    • iPhone 15 Pro - ¥8999 (2025/7/21)
    • MacBook Pro - ¥15999 (2025/7/21)

💾 总记录数: 63
```

### 清理测试数据

```bash
node scripts/quick-delete.js test
```

会删除所有测试邮箱的用户及其相关数据：
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- 等等...

### 完全重置数据库

```bash
# 方法1: 使用快速删除
node scripts/quick-delete.js all

# 方法2: 使用Prisma重置
npx prisma migrate reset

# 方法3: 使用清理工具
node scripts/database-cleanup.js
# 然后选择 "1. 删除所有数据"
```

### 备份数据库

```bash
node scripts/database-manager.js backup
```

会在 `backups/` 目录下创建JSON格式的备份文件：
```
backups/backup-2025-07-21T12-30-45-123Z.json
```

### 生成测试数据

```bash
node scripts/database-manager.js test
```

会创建：
- 测试用户 (<EMAIL> / password)
- 测试商品
- 测试需求单

## 🔐 安全注意事项

### 1. 生产环境警告

⚠️ **警告**: 这些工具主要用于开发环境，在生产环境使用时要格外小心！

### 2. 删除确认

重要操作需要确认：
- 删除所有数据需要输入 "DELETE ALL"
- 重置数据库需要输入 "RESET"

### 3. 备份建议

在执行删除操作前，建议先备份：
```bash
node scripts/database-manager.js backup
```

## 🚀 开发工作流

### 日常开发

1. **查看当前状态**：
   ```bash
   node scripts/database-manager.js info
   ```

2. **清理测试数据**：
   ```bash
   node scripts/quick-delete.js test
   ```

3. **生成新测试数据**：
   ```bash
   node scripts/database-manager.js test
   ```

### 功能测试

1. **启动可视化管理**：
   ```bash
   npx prisma studio
   ```

2. **在浏览器中查看和编辑数据**

3. **测试完成后清理**：
   ```bash
   node scripts/quick-delete.js test
   ```

### 数据库迁移

1. **备份当前数据**：
   ```bash
   node scripts/database-manager.js backup
   ```

2. **应用迁移**：
   ```bash
   npx prisma migrate dev
   ```

3. **如果出问题，重置数据库**：
   ```bash
   npx prisma migrate reset
   ```

## 📝 脚本说明

### database-manager.js
- 主要的数据库管理工具
- 提供交互式菜单
- 支持命令行参数
- 包含备份和信息查看功能

### quick-delete.js
- 快速删除工具
- 专注于删除操作
- 支持批量删除
- 命令行友好

### database-cleanup.js
- 详细的清理工具
- 交互式确认
- 按表删除选项
- 安全性较高

### sync-users.js
- 用户同步工具
- 修复用户数据不一致问题
- 创建测试账户
- 数据完整性检查

## 🔧 故障排除

### 数据库连接失败

```bash
# 检查数据库状态
npx prisma db push

# 重新生成客户端
npx prisma generate
```

### 迁移问题

```bash
# 查看迁移状态
npx prisma migrate status

# 重置迁移
npx prisma migrate reset
```

### 权限问题

确保数据库文件有正确的读写权限：
```bash
# SQLite数据库文件
chmod 664 prisma/dev.db
```

## 📞 获取帮助

如果遇到问题：

1. **查看工具帮助**：
   ```bash
   node scripts/database-manager.js
   node scripts/quick-delete.js
   ```

2. **查看Prisma文档**：
   - [Prisma Studio](https://www.prisma.io/studio)
   - [Prisma CLI](https://www.prisma.io/docs/reference/api-reference/command-reference)

3. **检查日志**：
   - 查看控制台输出
   - 检查数据库日志

---

**记住**: 数据无价，操作需谨慎！在生产环境中务必先备份再操作。
