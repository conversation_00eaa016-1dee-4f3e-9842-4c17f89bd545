# 📦 BitMarket 部署依赖安装指南 | Dependencies Installation Guide

本文档详细介绍 BitMarket 项目的所有依赖包及其安装配置方法。

## 📊 依赖统计概览 | Dependencies Overview

### 📈 总体统计
- **生产依赖**: 38 个包
- **开发依赖**: 19 个包
- **总计**: 57 个直接依赖包
- **间接依赖**: 约 2000+ 个包（通过 pnpm-lock.yaml）

### 🏷️ 版本信息
- **项目版本**: v1.3.2
- **Node.js 要求**: >= 18.0.0
- **包管理器**: npm/yarn/pnpm

## 🚀 核心框架依赖 | Core Framework Dependencies

### Next.js 生态系统
```json
{
  "next": "15.3.5",                    // Next.js 框架
  "react": "^19.0.0",                  // React 核心库
  "react-dom": "^19.0.0",              // React DOM 渲染
  "eslint-config-next": "15.3.5"      // Next.js ESLint 配置
}
```

**安装命令**:
```bash
npm install next@15.3.5 react@^19.0.0 react-dom@^19.0.0
```

## 🗃️ 数据库相关依赖 | Database Dependencies

### Prisma ORM
```json
{
  "@prisma/client": "^6.11.1",        // Prisma 客户端
  "prisma": "^6.11.1",                // Prisma CLI (开发依赖)
  "mysql2": "^3.14.2"                 // MySQL 驱动
}
```

**安装命令**:
```bash
npm install @prisma/client@^6.11.1 mysql2@^3.14.2
npm install -D prisma@^6.11.1
```

## 🔐 认证与安全依赖 | Authentication & Security

### 认证系统
```json
{
  "next-auth": "^4.24.11",            // NextAuth.js 认证
  "bcryptjs": "^3.0.2",               // 密码加密
  "jsonwebtoken": "^9.0.2",           // JWT 令牌
  "@types/bcryptjs": "^3.0.0",        // bcryptjs 类型定义
  "@types/jsonwebtoken": "^9.0.10"    // JWT 类型定义
}
```

**安装命令**:
```bash
npm install next-auth@^4.24.11 bcryptjs@^3.0.2 jsonwebtoken@^9.0.2
npm install -D @types/bcryptjs@^3.0.0 @types/jsonwebtoken@^9.0.10
```

## 💬 实时通信依赖 | Real-time Communication

### Socket.IO 生态
```json
{
  "socket.io": "^4.8.1",              // Socket.IO 服务端
  "socket.io-client": "^4.8.1",       // Socket.IO 客户端
  "@socket.io/redis-adapter": "^8.3.0", // Redis 适配器
  "ws": "^8.18.3",                    // WebSocket 库
  "@types/ws": "^8.18.1"              // WebSocket 类型定义
}
```

**安装命令**:
```bash
npm install socket.io@^4.8.1 socket.io-client@^4.8.1 @socket.io/redis-adapter@^8.3.0 ws@^8.18.3
npm install -D @types/ws@^8.18.1
```

## 🗄️ 缓存与存储依赖 | Cache & Storage

### Redis 缓存
```json
{
  "redis": "^5.6.0",                  // Redis 客户端
  "ioredis": "^5.6.1"                 // IORedis 客户端
}
```

### 文件存储
```json
{
  "minio": "^8.0.5",                  // MinIO 对象存储
  "multer": "^2.0.1",                 // 文件上传中间件
  "sharp": "^0.34.3",                 // 图像处理
  "@types/multer": "^2.0.0"           // Multer 类型定义
}
```

**安装命令**:
```bash
npm install redis@^5.6.0 ioredis@^5.6.1 minio@^8.0.5 multer@^2.0.1 sharp@^0.34.3
npm install -D @types/multer@^2.0.0
```

## 🎨 UI 与样式依赖 | UI & Styling

### Tailwind CSS 生态
```json
{
  "tailwindcss": "^4",                // Tailwind CSS 框架
  "@tailwindcss/postcss": "^4",       // PostCSS 插件
  "tailwind-merge": "^3.3.1",         // 类名合并工具
  "clsx": "^2.1.1",                   // 条件类名工具
  "class-variance-authority": "^0.7.1" // 变体管理
}
```

### 图标与组件
```json
{
  "lucide-react": "^0.525.0",         // Lucide 图标库
  "@heroicons/react": "^2.2.0"        // Heroicons 图标库
}
```

**安装命令**:
```bash
npm install tailwind-merge@^3.3.1 clsx@^2.1.1 class-variance-authority@^0.7.1
npm install lucide-react@^0.525.0 @heroicons/react@^2.2.0
npm install -D tailwindcss@^4 @tailwindcss/postcss@^4
```

## 📝 富文本编辑依赖 | Rich Text Editor

### TinyMCE 编辑器
```json
{
  "tinymce": "^7.9.1",                // TinyMCE 核心
  "@tinymce/tinymce-react": "^6.2.1"  // React 集成
}
```

**安装命令**:
```bash
npm install tinymce@^7.9.1 @tinymce/tinymce-react@^6.2.1
```

## 🌐 HTTP 与网络依赖 | HTTP & Network

### HTTP 客户端
```json
{
  "axios": "^1.10.0",                 // HTTP 客户端
  "node-fetch": "^3.3.2",             // Fetch API
  "form-data": "^4.0.3"               // 表单数据处理
}
```

**安装命令**:
```bash
npm install axios@^1.10.0 node-fetch@^3.3.2 form-data@^4.0.3
```

## 🛠️ 工具库依赖 | Utility Libraries

### 通用工具
```json
{
  "dayjs": "^1.11.13",                // 日期时间处理
  "zod": "^4.0.5",                    // 数据验证
  "zustand": "^5.0.6",                // 状态管理
  "qrcode": "^1.5.4",                 // 二维码生成
  "dotenv": "^17.2.0",                // 环境变量
  "@types/qrcode": "^1.5.5"           // QR码类型定义
}
```

**安装命令**:
```bash
npm install dayjs@^1.11.13 zod@^4.0.5 zustand@^5.0.6 qrcode@^1.5.4 dotenv@^17.2.0
npm install -D @types/qrcode@^1.5.5
```

## 🧪 测试相关依赖 | Testing Dependencies

### 测试框架
```json
{
  "vitest": "^3.2.4",                 // 测试运行器
  "@vitest/coverage-v8": "^3.2.4",    // 覆盖率报告
  "@vitejs/plugin-react": "^4.6.0",   // Vite React 插件
  "jsdom": "^26.1.0"                  // DOM 模拟
}
```

### 测试工具
```json
{
  "@testing-library/react": "^16.3.0",      // React 测试库
  "@testing-library/jest-dom": "^6.6.3",    // Jest DOM 匹配器
  "@testing-library/user-event": "^14.5.2", // 用户事件模拟
  "@faker-js/faker": "^9.4.0"               // 假数据生成
}
```

**安装命令**:
```bash
npm install -D vitest@^3.2.4 @vitest/coverage-v8@^3.2.4 @vitejs/plugin-react@^4.6.0
npm install -D jsdom@^26.1.0 @testing-library/react@^16.3.0 @testing-library/jest-dom@^6.6.3
npm install -D @testing-library/user-event@^14.5.2 @faker-js/faker@^9.4.0
```

## 🔧 开发工具依赖 | Development Tools

### TypeScript 生态
```json
{
  "typescript": "^5",                  // TypeScript 编译器
  "@types/node": "^20",                // Node.js 类型定义
  "@types/react": "^19",               // React 类型定义
  "@types/react-dom": "^19"            // React DOM 类型定义
}
```

### 代码质量
```json
{
  "eslint": "^9",                      // ESLint 代码检查
  "@eslint/eslintrc": "^3"             // ESLint 配置
}
```

**安装命令**:
```bash
npm install -D typescript@^5 @types/node@^20 @types/react@^19 @types/react-dom@^19
npm install -D eslint@^9 @eslint/eslintrc@^3
```

## 🚀 一键安装脚本 | One-Click Installation

### 完整安装脚本
```bash
#!/bin/bash
# BitMarket 依赖安装脚本

echo "🚀 开始安装 BitMarket 项目依赖..."

# 安装生产依赖
echo "📦 安装生产依赖..."
npm install \
  next@15.3.5 \
  react@^19.0.0 \
  react-dom@^19.0.0 \
  @prisma/client@^6.11.1 \
  mysql2@^3.14.2 \
  next-auth@^4.24.11 \
  bcryptjs@^3.0.2 \
  jsonwebtoken@^9.0.2 \
  socket.io@^4.8.1 \
  socket.io-client@^4.8.1 \
  @socket.io/redis-adapter@^8.3.0 \
  ws@^8.18.3 \
  redis@^5.6.0 \
  ioredis@^5.6.1 \
  minio@^8.0.5 \
  multer@^2.0.1 \
  sharp@^0.34.3 \
  tailwind-merge@^3.3.1 \
  clsx@^2.1.1 \
  class-variance-authority@^0.7.1 \
  lucide-react@^0.525.0 \
  @heroicons/react@^2.2.0 \
  tinymce@^7.9.1 \
  @tinymce/tinymce-react@^6.2.1 \
  axios@^1.10.0 \
  node-fetch@^3.3.2 \
  form-data@^4.0.3 \
  dayjs@^1.11.13 \
  zod@^4.0.5 \
  zustand@^5.0.6 \
  qrcode@^1.5.4 \
  dotenv@^17.2.0

# 安装开发依赖
echo "🛠️ 安装开发依赖..."
npm install -D \
  prisma@^6.11.1 \
  @types/bcryptjs@^3.0.0 \
  @types/jsonwebtoken@^9.0.10 \
  @types/ws@^8.18.1 \
  @types/multer@^2.0.0 \
  @types/qrcode@^1.5.5 \
  tailwindcss@^4 \
  @tailwindcss/postcss@^4 \
  vitest@^3.2.4 \
  @vitest/coverage-v8@^3.2.4 \
  @vitejs/plugin-react@^4.6.0 \
  jsdom@^26.1.0 \
  @testing-library/react@^16.3.0 \
  @testing-library/jest-dom@^6.6.3 \
  @testing-library/user-event@^14.5.2 \
  @faker-js/faker@^9.4.0 \
  typescript@^5 \
  @types/node@^20 \
  @types/react@^19 \
  @types/react-dom@^19 \
  eslint@^9 \
  @eslint/eslintrc@^3 \
  eslint-config-next@15.3.5

echo "✅ 依赖安装完成！"
echo "🔧 运行 'npx prisma generate' 生成 Prisma 客户端"
echo "🚀 运行 'npm run dev' 启动开发服务器"
```

### Windows 批处理脚本
```batch
@echo off
echo 🚀 开始安装 BitMarket 项目依赖...

echo 📦 安装所有依赖...
npm install

echo 🔧 生成 Prisma 客户端...
npx prisma generate

echo ✅ 安装完成！
echo 🚀 运行 'npm run dev' 启动开发服务器
pause
```

## 📋 依赖分类详解 | Dependency Categories

### 🏗️ 核心架构 (8个包)
- **Next.js 15.3.5**: 全栈 React 框架
- **React 19.0.0**: 用户界面库
- **TypeScript 5**: 类型安全的 JavaScript

### 🗃️ 数据层 (3个包)
- **Prisma 6.11.1**: 现代数据库 ORM
- **MySQL2**: MySQL 数据库驱动
- **Redis/IORedis**: 缓存和会话存储

### 🔐 安全认证 (3个包)
- **NextAuth.js**: 认证解决方案
- **bcryptjs**: 密码哈希
- **jsonwebtoken**: JWT 令牌管理

### 💬 实时通信 (4个包)
- **Socket.IO**: WebSocket 通信
- **Redis Adapter**: 多实例支持
- **WebSocket**: 底层协议支持

### 🎨 用户界面 (7个包)
- **Tailwind CSS 4**: 原子化 CSS 框架
- **Lucide React**: 现代图标库
- **TinyMCE**: 富文本编辑器

### 🧪 测试工具 (8个包)
- **Vitest**: 快速测试运行器
- **Testing Library**: React 组件测试
- **Faker.js**: 测试数据生成

## ⚠️ 安装注意事项 | Installation Notes

### 版本兼容性
- **Node.js**: 必须 >= 18.0.0
- **npm**: 建议 >= 8.0.0
- **Python**: Sharp 包需要 Python 3.x (Windows)

### 常见问题解决
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# Windows 上 Sharp 安装问题
npm install --platform=win32 --arch=x64 sharp

# 权限问题 (Linux/macOS)
sudo chown -R $(whoami) ~/.npm
```

### 环境变量配置
安装完成后，需要配置以下环境变量：
```env
DATABASE_URL="mysql://user:password@localhost:3306/bitmarket"
REDIS_URL="redis://localhost:6379"
NEXTAUTH_SECRET="your-secret-key"
JWT_SECRET="your-jwt-secret"
```

## 📊 依赖大小分析 | Bundle Size Analysis

### 主要包大小 (压缩后)
- **Next.js**: ~500KB
- **React**: ~42KB
- **Prisma Client**: ~2MB
- **Socket.IO**: ~200KB
- **Tailwind CSS**: ~10KB (按需加载)
- **TinyMCE**: ~1.5MB

### 优化建议
- 使用动态导入减少初始包大小
- 启用 Tree Shaking 移除未使用代码
- 配置 Webpack Bundle Analyzer 分析包大小

---

*最后更新: 2025-07-24*  
*维护团队: BitMarket Development Team*
