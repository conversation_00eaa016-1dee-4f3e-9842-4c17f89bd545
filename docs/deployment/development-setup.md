# 🛠️ 开发环境搭建指南 | Development Environment Setup

本指南将帮助您在本地搭建 BitMarket 的完整开发环境。

## 📋 系统要求 | System Requirements

### 基础要求 | Basic Requirements
- **Node.js**: >= 18.0.0 (推荐 18.x LTS)
- **npm**: >= 8.0.0 (或 yarn >= 1.22.0, pnpm >= 7.0.0)
- **Git**: >= 2.0.0
- **内存**: 8GB+ RAM (推荐)
- **存储**: 5GB+ 可用空间

### 支持的操作系统 | Supported Operating Systems
- ✅ Windows 10/11
- ✅ macOS 10.15+ (Catalina 或更高版本)
- ✅ Ubuntu 18.04+ / Debian 10+
- ✅ CentOS 7+ / RHEL 7+

## 🔧 环境安装 | Environment Installation

### 1. Node.js 安装

#### Windows
```bash
# 使用 Chocolatey
choco install nodejs

# 或下载官方安装包
# https://nodejs.org/zh-cn/download/
```

#### macOS
```bash
# 使用 Homebrew
brew install node

# 或使用 MacPorts
sudo port install nodejs18
```

#### Linux (Ubuntu/Debian)
```bash
# 使用 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. Git 配置
```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置默认分支名
git config --global init.defaultBranch main

# 配置换行符处理 (Windows)
git config --global core.autocrlf true

# 配置换行符处理 (macOS/Linux)
git config --global core.autocrlf input
```

## 📦 项目设置 | Project Setup

### 1. 克隆项目
```bash
# 克隆主仓库
git clone https://github.com/liusu-ally/bitmarket.git
cd bitmarket

# 或克隆您的 fork
git clone https://github.com/YOUR_USERNAME/bitmarket.git
cd bitmarket

# 添加上游仓库 (如果是 fork)
git remote add upstream https://github.com/liusu-ally/bitmarket.git
```

### 2. 安装依赖
```bash
# 使用 npm (推荐)
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm (更快)
pnpm install
```

### 3. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
# Windows: notepad .env.local
# macOS: open -e .env.local
# Linux: nano .env.local
```

#### 必需的环境变量
```env
# 数据库配置
DATABASE_URL="file:./dev.db"

# NextAuth 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-development-secret-key"

# JWT 密钥
JWT_SECRET="your-jwt-secret-here"

# 环境类型
NODE_ENV="development"
```

## 🗃️ 数据库配置 | Database Configuration

### SQLite (开发环境推荐)
```bash
# 生成 Prisma Client
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev --name init

# 查看数据库
npx prisma studio
```

### MySQL (可选)
```bash
# 安装 MySQL (Ubuntu)
sudo apt update
sudo apt install mysql-server

# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 创建数据库
mysql -u root -p
CREATE DATABASE bitmarket_dev;
CREATE USER 'bitmarket'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON bitmarket_dev.* TO 'bitmarket'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 更新 .env.local
DATABASE_URL="mysql://bitmarket:password@localhost:3306/bitmarket_dev"
```

### PostgreSQL (可选)
```bash
# 安装 PostgreSQL (Ubuntu)
sudo apt update
sudo apt install postgresql postgresql-contrib

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE bitmarket_dev;
CREATE USER bitmarket WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE bitmarket_dev TO bitmarket;
\q

# 更新 .env.local
DATABASE_URL="postgresql://bitmarket:password@localhost:5432/bitmarket_dev"
```

## 🚀 Redis 配置 | Redis Configuration (可选)

### 安装 Redis

#### Windows
```bash
# 使用 Chocolatey
choco install redis-64

# 或使用 WSL2 + Ubuntu
```

#### macOS
```bash
# 使用 Homebrew
brew install redis

# 启动 Redis
brew services start redis
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# 启动 Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 验证安装
redis-cli ping
```

### Redis 配置
```env
# 添加到 .env.local
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_DB="0"
```

## 🔧 开发工具配置 | Development Tools Setup

### VS Code 扩展推荐
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "prisma.prisma",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### VS Code 设置
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

## 🧪 测试环境配置 | Test Environment Setup

### 安装测试依赖
```bash
# 测试依赖已包含在 package.json 中
npm install
```

### 运行测试
```bash
# 运行所有测试
npm run test

# 运行测试并监听变化
npm run test:watch

# 运行测试覆盖率
npm run test:coverage

# 运行特定测试
npm run test -- --testNamePattern="UserCard"
```

### 测试数据库配置
```env
# 添加到 .env.test.local
DATABASE_URL="file:./test.db"
NODE_ENV="test"
```

## 🚀 启动开发服务器 | Start Development Server

### 基础启动
```bash
npm run dev
```

### 优化启动 (包含性能监控)
```bash
npm run dev:optimized
```

### 使用 Turbopack (更快的热重载)
```bash
npm run dev:next
```

### 访问应用
- 🌐 主应用: http://localhost:3000
- 📊 Prisma Studio: http://localhost:5555 (运行 `npx prisma studio`)
- 🔧 API 健康检查: http://localhost:3000/api/health

## 🔍 验证安装 | Verify Installation

### 1. 检查服务状态
```bash
# 检查 Node.js
node --version

# 检查 npm
npm --version

# 检查 Git
git --version

# 检查数据库连接
npx prisma db pull
```

### 2. 运行健康检查
```bash
# 运行项目健康检查
npm run health:check

# 检查代码质量
npm run lint

# 检查类型
npm run type:check
```

### 3. 测试核心功能
- ✅ 访问主页 (http://localhost:3000)
- ✅ 用户注册/登录
- ✅ API 接口响应
- ✅ 数据库连接
- ✅ 实时聊天功能

## 🐛 常见问题 | Troubleshooting

### Node.js 版本问题
```bash
# 使用 nvm 管理 Node.js 版本
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 安装并使用 Node.js 18
nvm install 18
nvm use 18
```

### 端口占用问题
```bash
# 查找占用端口的进程
# Windows
netstat -ano | findstr :3000

# macOS/Linux
lsof -i :3000

# 杀死进程
# Windows
taskkill /PID <PID> /F

# macOS/Linux
kill -9 <PID>
```

### 权限问题 (Linux/macOS)
```bash
# 修复 npm 权限
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules
```

### 数据库连接问题
```bash
# 重置数据库
npx prisma migrate reset

# 重新生成 Prisma Client
npx prisma generate

# 检查数据库状态
npx prisma db pull
```

## 📚 下一步 | Next Steps

1. 📖 阅读 [项目结构文档](../project-structure.md)
2. 🧪 查看 [测试指南](../TESTING.md)
3. 🔌 了解 [API 文档](../api-reference.md)
4. 🤝 阅读 [贡献指南](../../CONTRIBUTING.md)

## 📞 获取帮助 | Getting Help

如果在环境搭建过程中遇到问题：

- 📧 发送邮件到 <EMAIL>
- 💬 在 GitHub Discussions 中提问
- 🐛 在 GitHub Issues 中报告问题
- 📚 查看 [故障排除文档](../troubleshooting.md)

---

*最后更新: 2025-07-24*  
*维护团队: BitMarket Development Team*
