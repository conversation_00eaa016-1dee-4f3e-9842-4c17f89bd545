# 🐳 Docker 容器化部署指南 | Docker Deployment Guide

本指南详细介绍如何使用 Docker 和 Docker Compose 部署 BitMarket 应用。

## 📋 前置要求 | Prerequisites

### 系统要求
- Docker >= 20.10.0
- Docker Compose >= 2.0.0
- 4GB+ RAM
- 20GB+ 可用磁盘空间

### 安装 Docker

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加 Docker 仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到 docker 组
sudo usermod -aG docker $USER
```

#### CentOS/RHEL
```bash
# 安装 Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

#### macOS
```bash
# 使用 Homebrew
brew install --cask docker

# 或下载 Docker Desktop
# https://www.docker.com/products/docker-desktop
```

#### Windows
```bash
# 使用 Chocolatey
choco install docker-desktop

# 或下载 Docker Desktop
# https://www.docker.com/products/docker-desktop
```

## 📦 Docker 配置文件 | Docker Configuration

### 1. Dockerfile
创建项目根目录下的 `Dockerfile`:

```dockerfile
# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY prisma ./prisma/

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 生成 Prisma Client
RUN npx prisma generate

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS runner

# 安装 dumb-init
RUN apk add --no-cache dumb-init

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置工作目录
WORKDIR /app

# 复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma

# 设置文件权限
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]
```

### 2. .dockerignore
创建 `.dockerignore` 文件：

```dockerignore
# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next
out
dist
build

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
.nyc_output

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Documentation
README.md
docs/
*.md

# Test files
test/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Temporary files
tmp/
temp/
.tmp

# Release files
release/
```

### 3. Docker Compose 配置
创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  # BitMarket 应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bitmarket-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://bitmarket:${POSTGRES_PASSWORD}@postgres:5432/bitmarket
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bitmarket-network
    volumes:
      - uploads:/app/public/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: bitmarket-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=bitmarket
      - POSTGRES_USER=bitmarket
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - bitmarket-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U bitmarket -d bitmarket"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: bitmarket-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - bitmarket-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: bitmarket-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads:/var/www/uploads:ro
    depends_on:
      - app
    networks:
      - bitmarket-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local

networks:
  bitmarket-network:
    driver: bridge
```

### 4. 环境变量配置
创建 `.env.docker`:

```env
# 数据库密码
POSTGRES_PASSWORD=your_secure_postgres_password

# Redis 密码
REDIS_PASSWORD=your_secure_redis_password

# NextAuth 密钥
NEXTAUTH_SECRET=your_very_secure_nextauth_secret

# JWT 密钥
JWT_SECRET=your_jwt_secret_key

# 应用配置
NODE_ENV=production
PORT=3000

# 安全配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# 文件上传
UPLOAD_MAX_SIZE=10485760
```

### 5. Nginx 配置
创建 `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 上游服务器
    upstream app {
        server app:3000;
    }

    # HTTP 重定向到 HTTPS
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }

    # HTTPS 服务器
    server {
        listen 443 ssl http2;
        server_name _;

        # SSL 配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;

        # 文件上传大小限制
        client_max_body_size 10M;

        # 静态文件
        location /uploads {
            alias /var/www/uploads;
            expires 30d;
            add_header Cache-Control "public";
        }

        # 反向代理
        location / {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
    }
}
```

## 🚀 部署步骤 | Deployment Steps

### 1. 准备部署环境
```bash
# 克隆项目
git clone https://github.com/liusu-ally/bitmarket.git
cd bitmarket

# 复制环境变量
cp .env.docker .env

# 编辑环境变量
nano .env
```

### 2. 构建和启动服务
```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 3. 数据库初始化
```bash
# 运行数据库迁移
docker-compose exec app npx prisma migrate deploy

# (可选) 填充初始数据
docker-compose exec app npx prisma db seed
```

### 4. 验证部署
```bash
# 检查应用健康状态
curl http://localhost:3000/api/health

# 查看日志
docker-compose logs -f app
```

## 🔧 管理命令 | Management Commands

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]
```

### 数据库管理
```bash
# 连接到数据库
docker-compose exec postgres psql -U bitmarket -d bitmarket

# 备份数据库
docker-compose exec postgres pg_dump -U bitmarket bitmarket > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U bitmarket bitmarket < backup.sql

# 运行数据库迁移
docker-compose exec app npx prisma migrate deploy
```

### 应用管理
```bash
# 进入应用容器
docker-compose exec app sh

# 重新构建应用
docker-compose build app
docker-compose up -d app

# 查看应用日志
docker-compose logs -f app
```

## 📊 监控和维护 | Monitoring and Maintenance

### 健康检查
```bash
# 检查所有服务健康状态
docker-compose ps

# 检查特定服务
docker-compose exec app curl -f http://localhost:3000/api/health
```

### 日志管理
```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app

# 限制日志输出行数
docker-compose logs --tail=100 app
```

### 资源监控
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
docker system df

# 清理未使用的资源
docker system prune -a
```

## 🔒 安全配置 | Security Configuration

### SSL 证书配置
```bash
# 创建 SSL 目录
mkdir -p ssl

# 生成自签名证书 (开发环境)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem

# 或使用 Let's Encrypt (生产环境)
certbot certonly --standalone -d your-domain.com
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
```

### 防火墙配置
```bash
# 只开放必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

## 🔄 更新和备份 | Updates and Backups

### 应用更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
docker-compose build app
docker-compose up -d app

# 运行数据库迁移
docker-compose exec app npx prisma migrate deploy
```

### 数据备份
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec postgres pg_dump -U bitmarket bitmarket > $BACKUP_DIR/db_$DATE.sql

# 备份上传文件
docker run --rm -v bitmarket_uploads:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/uploads_$DATE.tar.gz -C /data .

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x backup.sh

# 设置定时备份
crontab -e
# 添加: 0 2 * * * /path/to/backup.sh
```

## 🐛 故障排除 | Troubleshooting

### 常见问题

#### 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs app

# 检查配置文件
docker-compose config

# 重新构建镜像
docker-compose build --no-cache app
```

#### 数据库连接问题
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U bitmarket

# 查看数据库日志
docker-compose logs postgres

# 重启数据库服务
docker-compose restart postgres
```

#### 内存不足
```bash
# 增加 Docker 内存限制
# 编辑 docker-compose.yml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

## 📞 获取帮助 | Getting Help

如果在 Docker 部署过程中遇到问题：

- 📧 技术支持: <EMAIL>
- 📚 查看 [Docker 官方文档](https://docs.docker.com/)
- 💬 GitHub Discussions
- 🐛 GitHub Issues

---

*最后更新: 2025-07-24*  
*维护团队: BitMarket Development Team*
