# 🐳 BitMarket Docker 数据库环境指南

本指南详细介绍如何使用 Docker 为 BitMarket 项目配置和管理 MySQL 数据库环境。

## 📋 系统要求

### 必需软件
- **Docker**: >= 20.10.0
- **Docker Compose**: >= 2.0.0  
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0

### 系统资源
- **内存**: 4GB+ RAM (推荐 8GB+)
- **存储**: 10GB+ 可用空间
- **网络**: 稳定的互联网连接

## 🚀 快速开始

### 1. 检查 Docker 环境
```bash
# 检查 Docker 版本
docker --version
docker-compose --version

# 确保 Docker 服务运行
docker info
```

### 2. 一键设置数据库环境
```bash
# 设置 MySQL 数据库
npm run docker:setup

# 设置 MySQL + Redis (可选)
npm run docker:setup:redis
```

### 3. 配置环境变量
```bash
# 复制 Docker 环境配置到本地
cp .env.docker .env.local

# 验证配置
cat .env.local | grep DATABASE_URL
```

### 4. 启动应用程序
```bash
# 启动开发服务器
npm run dev
```

## 🔧 详细配置说明

### Docker Compose 服务配置

#### MySQL 数据库服务
- **镜像**: mysql:8.0
- **容器名**: bitmarket-mysql
- **端口**: 3306
- **数据库**: bitmarket
- **用户**: bitmarket_user
- **密码**: bitmarket_pass_2024

#### Redis 缓存服务 (可选)
- **镜像**: redis:7-alpine
- **容器名**: bitmarket-redis
- **端口**: 6379
- **密码**: bitmarket_redis_2024

### 环境变量说明

#### 核心数据库配置
```env
DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"
MYSQL_ROOT_PASSWORD=bitmarket_root_2024
MYSQL_DATABASE=bitmarket
MYSQL_USER=bitmarket_user
MYSQL_PASSWORD=bitmarket_pass_2024
```

#### Redis 配置 (可选)
```env
REDIS_URL="redis://:bitmarket_redis_2024@localhost:6379"
REDIS_PASSWORD=bitmarket_redis_2024
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 📝 管理命令

### 基本服务管理
```bash
# 启动 MySQL 服务
npm run docker:start

# 启动 MySQL + Redis 服务
npm run docker:start:redis

# 停止所有服务
npm run docker:stop

# 重启服务
npm run docker:restart

# 查看服务状态
npm run docker:status

# 查看服务日志
npm run docker:logs
```

### 数据库操作
```bash
# 进入 MySQL 命令行
npm run docker:shell

# 运行 Prisma 数据库迁移
npm run docker:migrate

# 备份数据库
npm run docker:backup

# 重置数据库 (删除所有数据)
npm run docker:reset

# 清理未使用的 Docker 资源
npm run docker:clean
```

### 高级管理脚本
```bash
# 使用管理脚本 (更多选项)
./scripts/docker-manage.sh <command> [options]

# 查看所有可用命令
./scripts/docker-manage.sh help

# 示例：查看特定服务日志
./scripts/docker-manage.sh logs mysql
./scripts/docker-manage.sh logs redis
```

## 🗃️ 数据持久化

### 数据卷说明
- **mysql_data**: MySQL 数据文件存储
- **redis_data**: Redis 数据文件存储 (可选)

### 备份和恢复
```bash
# 自动备份 (保存到 backup/ 目录)
npm run docker:backup

# 查看备份文件
ls backup/

# 恢复指定备份
./scripts/docker-manage.sh restore backup/bitmarket_20241225_120000.sql
```

## 🔍 验证和测试

### 1. 验证数据库连接
```bash
# 检查容器状态
docker-compose ps

# 测试数据库连接
npm run docker:shell
# 在 MySQL shell 中执行
SELECT 'Connection successful' AS status;
SHOW DATABASES;
```

### 2. 验证 Prisma 集成
```bash
# 生成 Prisma Client
npx prisma generate

# 运行数据库迁移
npm run docker:migrate

# 查看数据库结构
npx prisma studio
```

### 3. 验证应用程序连接
```bash
# 启动应用程序
npm run dev

# 检查应用程序日志中的数据库连接状态
# 应该看到成功连接的消息
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 端口冲突
**问题**: 端口 3306 或 6379 已被占用
```bash
# 检查端口占用
lsof -i :3306
lsof -i :6379

# 解决方案：修改端口配置
# 编辑 .env.docker 文件
MYSQL_PORT=3307
REDIS_PORT=6380
```

#### 2. Docker 权限问题
**问题**: Permission denied 错误
```bash
# 解决方案：添加用户到 docker 组
sudo usermod -aG docker $USER
newgrp docker

# 重启 Docker 服务
sudo systemctl restart docker
```

#### 3. 数据库连接失败
**问题**: 应用程序无法连接到数据库
```bash
# 检查容器状态
docker-compose ps

# 查看 MySQL 日志
docker-compose logs mysql

# 重启数据库服务
npm run docker:restart

# 验证网络连接
docker-compose exec mysql ping localhost
```

#### 4. 内存不足
**问题**: 容器启动失败或性能差
```bash
# 检查系统资源
docker stats

# 解决方案：调整 MySQL 配置
# 编辑 mysql-config/my.cnf
innodb_buffer_pool_size = 128M  # 减少内存使用
```

#### 5. Prisma 迁移失败
**问题**: 数据库迁移执行失败
```bash
# 检查数据库连接字符串
echo $DATABASE_URL

# 重置数据库
npm run docker:reset

# 重新运行迁移
npm run docker:migrate
```

### 日志分析

#### 查看实时日志
```bash
# MySQL 服务日志
docker-compose logs -f mysql

# Redis 服务日志  
docker-compose logs -f redis

# 所有服务日志
docker-compose logs -f
```

#### 进入容器调试
```bash
# 进入 MySQL 容器
docker-compose exec mysql bash

# 查看 MySQL 错误日志
tail -f /var/log/mysql/error.log

# 查看慢查询日志
tail -f /var/log/mysql/slow.log
```

## 🔒 安全最佳实践

### 1. 密码安全
```bash
# 生成强密码
openssl rand -base64 32

# 更新 .env.docker 中的密码
MYSQL_ROOT_PASSWORD=<strong_password>
MYSQL_PASSWORD=<strong_password>
REDIS_PASSWORD=<strong_password>
```

### 2. 网络安全
- 使用内部网络隔离数据库
- 限制数据库端口仅本地访问
- 定期更新 Docker 镜像

### 3. 数据备份
```bash
# 设置定时备份
crontab -e

# 添加每日备份任务 (凌晨 2 点)
0 2 * * * cd /path/to/bitmarket && npm run docker:backup
```

## 📊 性能优化

### MySQL 优化配置
```ini
# mysql-config/my.cnf 关键配置
innodb_buffer_pool_size = 256M
max_connections = 200
query_cache_size = 32M
innodb_log_file_size = 64M
```

### 容器资源监控
```bash
# 监控容器资源使用
docker stats bitmarket-mysql bitmarket-redis

# 查看容器详细信息
docker inspect bitmarket-mysql
```

## 🔗 相关文档

- [开发环境搭建指南](./development-setup.md)
- [依赖安装文档](./dependencies-installation.md)
- [数据库管理指南](../database-management-guide.md)
- [项目结构说明](../project-structure.md)

---

*最后更新: 2025-07-25*  
*维护团队: BitMarket Development Team*
