# 🚀 生产环境部署指南 | Production Deployment Guide

本指南详细介绍如何将 BitMarket 部署到生产环境，包括云平台和自建服务器的部署方案。

## 📋 部署前准备 | Pre-deployment Checklist

### 🔍 环境检查
- [ ] Node.js >= 18.0.0
- [ ] 数据库服务 (PostgreSQL/MySQL)
- [ ] Redis 缓存服务
- [ ] SSL 证书
- [ ] 域名配置
- [ ] 防火墙设置

### 📦 构建检查
- [ ] 代码构建成功: `npm run build`
- [ ] 测试全部通过: `npm run test`
- [ ] 类型检查通过: `npm run type:check`
- [ ] 代码质量检查: `npm run lint`

## 🌐 云平台部署 | Cloud Platform Deployment

### Vercel 部署 (推荐)

#### 1. 准备工作
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login
```

#### 2. 配置环境变量
在 Vercel 控制台设置以下环境变量：

```env
# 基础配置
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-production-secret

# 数据库配置
DATABASE_URL=************************************/database

# Redis 配置
REDIS_URL=redis://user:password@host:6379

# 其他配置
JWT_SECRET=your-jwt-secret
UPLOAD_DIR=/tmp/uploads
```

#### 3. 部署配置
创建 `vercel.json`:
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "NODE_ENV": "production"
  },
  "build": {
    "env": {
      "NODE_ENV": "production"
    }
  }
}
```

#### 4. 执行部署
```bash
# 部署到生产环境
vercel --prod

# 或通过 Git 自动部署
git push origin main
```

### Railway 部署

#### 1. 连接 GitHub 仓库
- 访问 [Railway](https://railway.app)
- 连接您的 GitHub 仓库
- 选择 BitMarket 项目

#### 2. 配置环境变量
```env
NODE_ENV=production
NEXTAUTH_URL=${{RAILWAY_STATIC_URL}}
NEXTAUTH_SECRET=your-production-secret
DATABASE_URL=${{DATABASE_URL}}
REDIS_URL=${{REDIS_URL}}
```

#### 3. 配置构建
创建 `railway.json`:
```json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/api/health"
  }
}
```

### DigitalOcean App Platform

#### 1. 应用配置
创建 `.do/app.yaml`:
```yaml
name: bitmarket
services:
- name: web
  source_dir: /
  github:
    repo: your-username/bitmarket
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: NEXTAUTH_URL
    value: ${APP_URL}
  - key: DATABASE_URL
    value: ${DATABASE_URL}
databases:
- name: bitmarket-db
  engine: PG
  version: "13"
```

## 🖥️ 自建服务器部署 | Self-hosted Server Deployment

### Ubuntu/Debian 服务器

#### 1. 系统准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
sudo npm install -g pm2
```

#### 2. 数据库安装
```bash
# 安装 PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 配置数据库
sudo -u postgres psql
CREATE DATABASE bitmarket_prod;
CREATE USER bitmarket WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE bitmarket_prod TO bitmarket;
\q

# 安装 Redis
sudo apt install -y redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

#### 3. 应用部署
```bash
# 创建应用目录
sudo mkdir -p /var/www/bitmarket
sudo chown $USER:$USER /var/www/bitmarket

# 克隆代码
cd /var/www/bitmarket
git clone https://github.com/liusu-ally/bitmarket.git .

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.example .env.production
nano .env.production
```

#### 4. 环境变量配置
```env
# /var/www/bitmarket/.env.production
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-very-secure-secret-key
DATABASE_URL=postgresql://bitmarket:secure_password@localhost:5432/bitmarket_prod
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret-key

# 文件上传
UPLOAD_DIR=/var/www/bitmarket/public/uploads
UPLOAD_MAX_SIZE=10485760

# 安全配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
CORS_ORIGINS=https://your-domain.com

# 性能配置
USE_CLUSTER=true
CACHE_DEFAULT_TTL=300
ENABLE_PERFORMANCE_MONITORING=true
```

#### 5. 构建应用
```bash
# 生成 Prisma Client
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 构建应用
npm run build
```

#### 6. PM2 配置
创建 `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'bitmarket',
    script: 'server.js',
    cwd: '/var/www/bitmarket',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    error_file: '/var/log/bitmarket/error.log',
    out_file: '/var/log/bitmarket/out.log',
    log_file: '/var/log/bitmarket/combined.log',
    time: true,
    autorestart: true,
    watch: false,
    max_restarts: 10,
    min_uptime: '10s'
  }]
}
```

#### 7. 启动应用
```bash
# 创建日志目录
sudo mkdir -p /var/log/bitmarket
sudo chown $USER:$USER /var/log/bitmarket

# 启动应用
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save

# 设置开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

### Nginx 反向代理配置

#### 1. Nginx 配置
创建 `/etc/nginx/sites-available/bitmarket`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 文件上传大小限制
    client_max_body_size 10M;

    # 静态文件缓存
    location /_next/static {
        alias /var/www/bitmarket/.next/static;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }

    location /uploads {
        alias /var/www/bitmarket/public/uploads;
        expires 30d;
        add_header Cache-Control "public";
    }

    # 反向代理到 Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}
```

#### 2. 启用配置
```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/bitmarket /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

#### 3. SSL 证书
```bash
# 获取 Let's Encrypt 证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔒 安全配置 | Security Configuration

### 防火墙设置
```bash
# 配置 UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 系统安全
```bash
# 禁用 root 登录
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 更改 SSH 端口 (可选)
sudo sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# 重启 SSH 服务
sudo systemctl restart ssh

# 安装 fail2ban
sudo apt install -y fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 📊 监控和日志 | Monitoring and Logging

### 应用监控
```bash
# 查看 PM2 状态
pm2 status
pm2 logs
pm2 monit

# 查看系统资源
htop
df -h
free -h
```

### 日志管理
```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/bitmarket

# 内容:
/var/log/bitmarket/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔄 部署自动化 | Deployment Automation

### GitHub Actions 配置
创建 `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/bitmarket
          git pull origin main
          npm ci --only=production
          npx prisma migrate deploy
          npm run build
          pm2 reload bitmarket
```

## 🚨 故障排除 | Troubleshooting

### 常见问题

#### 应用无法启动
```bash
# 检查日志
pm2 logs bitmarket

# 检查端口占用
sudo netstat -tlnp | grep :3000

# 检查环境变量
pm2 env 0
```

#### 数据库连接问题
```bash
# 测试数据库连接
npx prisma db pull

# 检查数据库状态
sudo systemctl status postgresql
```

#### 内存不足
```bash
# 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

## 📈 性能优化 | Performance Optimization

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

### 缓存配置
```bash
# Redis 优化
sudo nano /etc/redis/redis.conf

# 添加配置
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 📞 获取帮助 | Getting Help

如果在部署过程中遇到问题：

- 📧 技术支持: <EMAIL>
- 📚 查看 [故障排除文档](../troubleshooting.md)
- 💬 GitHub Discussions
- 🐛 GitHub Issues

## 🔗 相关文档 | Related Documentation

- [🐳 Docker 部署指南](./docker-deployment.md)
- [🧪 测试环境部署](./testing-deployment.md)
- [🛠️ 开发环境搭建](./development-setup.md)
- [🖥️ 传统服务器部署](./traditional-server.md)

---

*最后更新: 2025-07-24*
*维护团队: BitMarket Development Team*
