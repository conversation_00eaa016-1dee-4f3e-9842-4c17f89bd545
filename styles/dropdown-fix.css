/* 下拉列表修复样式 */

/* 确保下拉列表有足够高的z-index */
.dropdown-container {
  position: relative;
  z-index: 1;
}

.dropdown-menu {
  position: absolute;
  z-index: 9999 !important;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-height: 20rem;
  overflow-y: auto;
  width: 100%;
}

.dropdown-menu.drop-up {
  bottom: 100%;
  margin-bottom: 0.25rem;
}

.dropdown-menu.drop-down {
  top: 100%;
  margin-top: 0.25rem;
}

/* 确保下拉列表项目可以正确显示 */
.dropdown-item {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
}

/* 遮罩层样式 */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998 !important;
  background: transparent;
}

/* 确保页面容器不会裁剪下拉列表 */
.page-container {
  overflow: visible !important;
}

.form-container {
  overflow: visible !important;
}

/* 修复可能的容器溢出问题 */
.location-selector-container {
  position: relative;
  z-index: 1;
}

.location-selector-container .relative {
  overflow: visible !important;
}

/* 确保下拉列表在移动设备上也能正确显示 */
@media (max-width: 640px) {
  .dropdown-menu {
    max-height: 16rem;
  }
}

/* 滚动条样式优化 */
.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
