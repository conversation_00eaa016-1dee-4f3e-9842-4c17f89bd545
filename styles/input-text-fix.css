/* 修复输入框文字颜色问题 - 只改文字颜色，保持背景白色 */

/* 确保所有输入框和文本域的文字都是浅灰色，字体细一点 */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea {
  color: #6b7280 !important;
  /* 保持背景白色不变 */
  background-color: #ffffff !important;
  font-weight: 400 !important;
}

/* 确保占位符文字是灰色且可见 */
input::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

/* 确保输入框获得焦点时文字仍然是浅灰色 */
input:focus,
textarea:focus {
  color: #6b7280 !important;
  background-color: #ffffff !important;
  font-weight: 400 !important;
}

/* 确保选中文字时的对比度 */
input::selection,
textarea::selection {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* 特别针对数字输入框（充值金额等） */
input[type="number"] {
  color: #6b7280 !important;
  background-color: #ffffff !important;
  font-weight: 400 !important;
}

/* 特别针对文本域（备注等） */
textarea {
  color: #6b7280 !important;
  background-color: #ffffff !important;
  line-height: 1.5 !important;
  font-weight: 400 !important;
}

/* 确保禁用状态下文字仍然可见 */
input:disabled,
textarea:disabled {
  color: #6b7280 !important;
  background-color: #f9fafb !important;
}

/* 修复可能的浏览器默认样式 */
input,
textarea {
  -webkit-text-fill-color: #6b7280 !important;
  -webkit-opacity: 1 !important;
  font-weight: 400 !important;
}

/* 确保自动填充时文字可见 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-text-fill-color: #6b7280 !important;
  -webkit-box-shadow: 0 0 0 30px #ffffff inset !important;
  background-color: #ffffff !important;
  font-weight: 400 !important;
}

/* 修复Firefox的样式问题 */
input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

/* 修复IE/Edge的样式问题 */
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #9ca3af !important;
}

/* 确保在所有状态下文字都是浅灰色 */
input:not(:placeholder-shown),
textarea:not(:placeholder-shown) {
  color: #6b7280 !important;
  font-weight: 400 !important;
}
