/* 修复选择框显示问题 - 简化版本 */

/* 最重要的修复：确保选择框文本可见 */
select {
  color: #1f2937 !important;
  background-color: #ffffff !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* 确保选择框选项文本可见 */
select option {
  color: #1f2937 !important;
  background-color: #ffffff !important;
  font-family: inherit !important;
  font-size: 14px !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 强制重置可能的样式冲突 */
select {
  all: revert !important;
  color: #1f2937 !important;
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  padding: 0.5rem 0.75rem !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: 100% !important;
  min-height: 2.5rem !important;
  cursor: pointer !important;
  -webkit-appearance: menulist !important;
  -moz-appearance: menulist !important;
  appearance: menulist !important;
}

/* 强制重置选项样式 */
select option {
  all: revert !important;
  color: #1f2937 !important;
  background-color: #ffffff !important;
  font-family: inherit !important;
  font-size: 14px !important;
  padding: 0.5rem !important;
}
