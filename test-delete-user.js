// 测试用户删除功能的脚本
const testEmail = '<EMAIL>'
const testName = '测试删除用户'

async function testUserDeletion() {
  try {
    console.log('🚀 开始测试用户删除功能...')
    
    // 1. 创建测试用户
    console.log('📝 创建测试用户...')
    const createResponse = await fetch('http://localhost:3000/api/test/create-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=your-admin-session-token' // 需要管理员权限
      },
      body: JSON.stringify({
        email: testEmail,
        name: testName
      })
    })
    
    const createData = await createResponse.json()
    if (!createData.success) {
      console.error('❌ 创建用户失败:', createData.error)
      return
    }
    
    const userId = createData.data.id
    console.log('✅ 用户创建成功, ID:', userId)
    
    // 2. 验证用户存在
    console.log('🔍 验证用户存在...')
    const checkResponse = await fetch(`http://localhost:3000/api/test/check-user?email=${encodeURIComponent(testEmail)}`)
    const checkData = await checkResponse.json()
    
    if (!checkData.exists) {
      console.error('❌ 用户不存在')
      return
    }
    console.log('✅ 用户存在确认')
    
    // 3. 删除用户
    console.log('🗑️ 删除用户...')
    const deleteResponse = await fetch('http://localhost:3000/api/admin/users', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=your-admin-session-token' // 需要管理员权限
      },
      body: JSON.stringify({
        userId: userId,
        action: 'deleteUser',
        data: {}
      })
    })
    
    const deleteData = await deleteResponse.json()
    if (!deleteData.success) {
      console.error('❌ 删除用户失败:', deleteData.error)
      return
    }
    console.log('✅ 用户删除成功')
    
    // 4. 验证用户已删除
    console.log('🔍 验证用户已删除...')
    const verifyResponse = await fetch(`http://localhost:3000/api/test/check-user?email=${encodeURIComponent(testEmail)}`)
    const verifyData = await verifyResponse.json()
    
    if (verifyData.exists) {
      console.error('❌ 用户仍然存在，删除失败')
      return
    }
    console.log('✅ 用户已成功删除')
    
    console.log('🎉 用户删除功能测试通过！')
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
  // 需要安装 node-fetch: npm install node-fetch
  const fetch = require('node-fetch')
  testUserDeletion()
}

console.log(`
📋 测试说明:
1. 这个脚本测试用户删除功能
2. 需要管理员权限才能执行
3. 会创建一个测试用户然后删除它
4. 验证删除是否成功

🔧 使用方法:
1. 在浏览器控制台中运行此脚本
2. 或者在Node.js中运行: node test-delete-user.js
3. 确保服务器在 http://localhost:3000 运行
4. 确保有管理员权限

⚠️ 注意:
- 这是真实的删除操作
- 删除的用户数据无法恢复
- 仅用于测试目的
`)
