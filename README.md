# 🚀 比特市场 BitMarket

<div align="center">

![BitMarket Logo](https://img.shields.io/badge/BitMarket-v1.3.2-blue?style=for-the-badge&logo=bitcoin)
![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=for-the-badge&logo=next.js)
![React](https://img.shields.io/badge/React-19.0.0-61DAFB?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Prisma](https://img.shields.io/badge/Prisma-6.11.1-2D3748?style=for-the-badge&logo=prisma)
![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)
![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen?style=for-the-badge)

**安全可靠的数字资产交易平台 | Secure & Reliable Digital Asset Trading Platform**

[🌟 功能特性](#-功能特性-features) • [🚀 快速开始](#-快速开始-quick-start) • [📖 文档](#-文档-documentation) • [🔧 Release管理](#-release管理-release-management) • [🤝 贡献](#-贡献-contributing)

</div>

---

## 📋 项目简介 | Project Overview

比特市场（BitMarket）是一个基于 Next.js 15 构建的现代化数字资产交易平台，专注于提供安全、便捷的 USDT C2C 交易服务。平台采用最新的技术栈，集成了完整的用户管理系统、创新的中间人担保机制、实时聊天功能等核心特性。

### 🎯 v1.3.2 版本亮点

- **🛡️ 区块链托管服务** - 基于 BNB Chain 的中间人托管系统，为大金额交易提供安全保障
- **⚖️ 争议仲裁机制** - 多方投票的公正仲裁系统，自动化争议处理
- **👥 中间人生态** - 完整的中间人注册、认证、管理和奖励体系
- **💬 多方聊天室** - 买家、卖家、中间人、管理员四方实时沟通
- **🎁 激励奖励系统** - 中间人参与仲裁获得免手续费提现券
- **🎨 品牌形象重构** - 导航栏采用 logo图片 + BitMarket文字 的组合设计，提升品牌识别度
- **🧩 完整UI组件库** - 基于 Tailwind CSS 和 Lucide React 的可复用组件系统
- **⚡ 性能全面优化** - 构建系统优化，依赖管理完善，代码质量显著提升
- **🔄 自动化Release** - 完整的版本发布自动化系统，支持一键发布

BitMarket is a modern digital asset trading platform built with Next.js 15, providing secure and convenient USDT trading services. The platform integrates comprehensive user management, guarantor mechanisms, real-time chat, and other core features.

### 🎯 核心价值 | Core Values

- **🛡️ 安全第一** - 多层安全防护，保障用户资产安全
- **🤝 信任机制** - 信用评级系统 + 中间人担保服务
- **💬 实时沟通** - 内置聊天系统，促进交易协商
- **📱 响应式设计** - 完美适配桌面端和移动端

## ✨ 功能特性 | Features

### 🎨 界面设计 | UI Design
- **现代化导航栏** - logo图片 + BitMarket文字的组合品牌标识
- **优化布局** - 重新设计的导航结构，提升用户体验
- **响应式设计** - 完美适配桌面端和移动端设备
- **一致性体验** - 统一的设计语言和交互模式

### 🔐 用户管理系统 | User Management
- **用户注册登录** - 邮箱验证 + 安全认证
- **个人资料管理** - 头像、城市、币安UID绑定
- **信用评级系统** - 基于交易历史的动态信用分数
- **用户状态管理** - 支持封禁/解封，状态实时同步

### 🛡️ 安全防护系统 | Security System
- **用户封禁机制** - 临时/永久封禁，自动解封
- **权限验证** - 多级权限控制，API安全防护
- **状态监控** - 实时用户状态检查和重定向
- **安全日志** - 完整的操作记录和审计追踪

### 💰 交易系统 | Trading System
- **商品发布** - 支持图片上传，详细描述
- **需求发布** - 求购信息发布，匹配推荐
- **订单管理** - 完整的交易流程跟踪
- **支付集成** - Binance Pay + BNB Chain 支付

### 🤝 中间人担保系统 | Guarantor System
- **智能担保服务** - 第三方担保机制，显著降低交易风险
- **保证金管理** - 优化的充值/提现机制，增强资金安全托管
- **风险控制系统** - 完善的保证金风险评估和实时管理
- **资质审核流程** - 严格的中间人申请审核和认证体系
- **收益分成机制** - 透明的担保服务费收入分配系统
- **资金监控预警** - 实时的保证金状态监控和风险预警
- **信用评级集成** - 基于交易历史的动态信用评分系统

### 🛡️ 区块链托管服务 | Blockchain Escrow Service
- **安全托管机制** - 基于 BNB Chain 的资金托管，确保交易安全
- **中间人生态系统** - 专业中间人提供托管服务，费率1%-30%可选
- **智能合约集成** - 钱包验证和交易监控，透明可信
- **争议仲裁系统** - 多方投票的公正仲裁，自动化执行结果
- **激励奖励机制** - 中间人参与仲裁获得免手续费提现券
- **多方聊天室** - 买家、卖家、中间人、管理员四方实时沟通

### 💬 实时沟通系统 | Communication System
- **WebSocket实时聊天** - 基于Socket.IO的实时消息推送
- **交易协商功能** - 内置聊天系统，便捷的交易沟通
- **多媒体支持** - 支持图片、文档等文件分享
- **消息历史管理** - 完整的聊天记录保存和检索
- **通知系统** - 实时消息通知和状态提醒

### 🎨 UI组件库系统 | UI Component Library
- **完整组件库** - 基于Tailwind CSS的可复用组件系统
- **Lucide图标集成** - 现代化的图标库支持
- **响应式设计** - 完美适配各种设备尺寸
- **主题系统** - 统一的设计语言和样式规范
- **可访问性支持** - 符合WCAG标准的无障碍设计

### 👨‍💼 管理后台 | Admin Panel
- **用户管理** - 用户列表，状态管理，封禁操作
- **交易监控** - 订单统计，异常交易检测
- **系统设置** - 平台参数配置，功能开关
- **数据分析** - 用户行为分析，交易数据统计

## 🛠️ 技术栈 | Tech Stack

### 前端技术 | Frontend
- **Framework**: Next.js 15.3.5 (App Router)
- **Language**: TypeScript 5.0+
- **UI Library**: React 19.0.0
- **Styling**: Tailwind CSS 4.0
- **UI Components**: 自定义组件库 + Lucide React 图标
- **State Management**: Zustand + React Hooks + Context API
- **Real-time**: Socket.IO Client 4.8.1
- **Form Handling**: React Hook Form + Zod 验证
- **HTTP Client**: Axios 1.10.0

### 后端技术 | Backend
- **Runtime**: Node.js 18.0.0+
- **Database ORM**: Prisma 6.11.1
- **Database**: SQLite/PostgreSQL/MySQL 支持
- **Authentication**: NextAuth.js 4.24.11
- **API Framework**: Next.js API Routes
- **Real-time**: Socket.IO Server 4.8.1
- **Caching**: Redis 5.6.0 + IORedis 5.6.1
- **File Storage**: MinIO 8.0.5 + Sharp 0.34.3
- **Security**: bcryptjs + jsonwebtoken

### 开发工具 | Development Tools
- **Package Manager**: npm/yarn/pnpm
- **Code Quality**: ESLint 9 + Prettier
- **Type Checking**: TypeScript Strict Mode
- **Testing**: Vitest 3.2.4 + Testing Library
- **Database Tools**: Prisma Studio + Prisma Migrate
- **Performance**: Next.js Built-in Optimization + 自定义性能监控
- **Release Management**: 自动化Release系统

## 🚀 快速开始 | Quick Start

### 📋 环境要求 | Prerequisites

确保您的开发环境满足以下要求：

```bash
Node.js >= 18.0.0
npm >= 8.0.0 (或 yarn >= 1.22.0, pnpm >= 7.0.0)
Git >= 2.0.0
```

**推荐配置：**
- Node.js 18.x LTS 或更高版本
- 8GB+ RAM（用于大型项目构建）
- 支持的操作系统：Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 🔧 安装步骤 | Installation

#### 1. **克隆项目 | Clone Repository**
```bash
git clone https://github.com/liusu-ally/bitmarket.git
cd bitmarket
```

#### 2. **安装依赖 | Install Dependencies**
```bash
# 使用 npm (推荐)
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm (更快的安装速度)
pnpm install
```

#### 3. **环境配置 | Environment Setup**
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量文件
# Windows: notepad .env.local
# macOS/Linux: nano .env.local 或 vim .env.local
```

> ⚠️ **重要提示**: 请务必修改 `NEXTAUTH_SECRET` 和 `JWT_SECRET` 为您自己的密钥

#### 4. **数据库设置 | Database Setup**
```bash
# 生成 Prisma Client
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev --name init

# (可选) 填充测试数据
npx prisma db seed
```

#### 5. **启动开发服务器 | Start Development Server**
```bash
# 基础启动
npm run dev

# 优化启动（包含性能监控）
npm run dev:optimized

# 使用 Turbopack（更快的热重载）
npm run dev:next
```

#### 6. **访问应用 | Access Application**
```
🌐 主应用: http://localhost:3000
📊 Prisma Studio: http://localhost:5555 (运行 npx prisma studio)
```

### ✅ 验证安装 | Verify Installation

访问以下页面确认安装成功：
- 主页：`http://localhost:3000`
- 登录页：`http://localhost:3000/auth/signin`
- API健康检查：`http://localhost:3000/api/health`

### 🔧 环境变量配置 | Environment Variables

```env
# 数据库配置 | Database
DATABASE_URL="file:./dev.db"

# NextAuth 配置 | NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# 邮件服务 | Email Service (Optional)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# Redis 缓存 | Redis Cache (Optional)
REDIS_URL="redis://localhost:6379"

# 文件上传 | File Upload (Optional)
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="10485760" # 10MB

# 区块链配置 | Blockchain Configuration
BNB_CHAIN_RPC_URL="https://bsc-dataseed.binance.org/"
WALLET_PRIVATE_KEY="your-private-key"

# 托管服务配置 | Escrow Service Configuration
ESCROW_MIN_AMOUNT=100
PLATFORM_FEE_RATE=0.3
MEDIATOR_MIN_DEPOSIT=1000
```

> 📝 **完整配置说明**: 查看 [.env.example](/.env.example) 文件获取所有可用的环境变量配置选项

## 🧪 测试系统 | Testing System

BitMarket 提供了完整的测试体系，确保系统功能的稳定性和可靠性。

### 🔍 系统测试 | System Tests

```bash
# 运行所有系统测试
npm test

# 托管系统功能测试
npm run test:escrow-system

# 下拉列表修复测试
npm run test:dropdown-fix

# 性能优化测试
npm run test:optimizations
```

### 🛡️ 托管系统测试 | Escrow System Tests

托管系统测试覆盖以下方面：
- ✅ **数据库模式验证** - 检查托管相关表结构
- ✅ **API路由完整性** - 验证所有托管API端点
- ✅ **前端组件存在性** - 确认UI组件正确创建
- ✅ **功能完整性检查** - 验证核心业务逻辑
- ✅ **安全机制验证** - 检查权限和输入验证

```bash
# 运行托管系统测试
npm run test:escrow-system

# 预期输出: 24/24 测试通过 (100%)
```

### 📊 测试覆盖范围 | Test Coverage

- **数据库层**: Prisma模式验证，关系完整性
- **API层**: 路由存在性，权限验证，输入验证
- **业务逻辑层**: 托管流程，仲裁机制，奖励系统
- **前端层**: 组件完整性，页面功能，用户交互
- **安全层**: 权限控制，数据验证，业务规则

## 🔧 Release管理 | Release Management

BitMarket 提供了完整的自动化release系统，支持版本管理、项目打包、Git操作和release内容生成。

### 🚀 快速Release | Quick Release

```bash
# 基本patch版本release (1.3.2 → 1.3.3)
npm run release

# minor版本release (1.3.2 → 1.4.0)
npm run release:minor

# major版本release (1.3.2 → 2.0.0)
npm run release:major

# 演练模式（安全预览）
npm run release:dry
```

### 🎯 高级Release选项 | Advanced Release Options

```bash
# 带自定义消息的release
node scripts/release.js --version-type minor --message "新增用户管理功能"

# 跳过测试的快速release
node scripts/release.js --skip-tests

# 只创建发布包，不执行Git操作
node scripts/release.js --skip-git

# 当前版本重新打包
npm run release:current
```

### 📦 Release输出 | Release Output

每次release会在 `release/` 目录生成：
- 📁 源码目录（包含完整项目文件）
- 📦 压缩包（.tar.gz格式）
- 🔐 SHA256校验和文件
- 📝 Release Notes文档
- 📋 安装指南

### 🔍 Release流程 | Release Process

1. **环境检查** - 验证Git状态和项目完整性
2. **版本管理** - 自动更新版本号和CHANGELOG
3. **质量保证** - 运行测试和构建验证
4. **项目打包** - 创建发布包和校验和
5. **Git操作** - 提交更改、创建标签、推送远程
6. **文档生成** - 自动生成Release Notes

> 📚 **详细指南**: 查看 [Release系统文档](./RELEASE_GUIDE.md) 了解更多信息

## 📖 文档 | Documentation

### 📚 用户指南 | User Guide
- [🔰 新手入门指南](./docs/user-guide/getting-started.md)
- [💰 交易流程详解](./docs/user-guide/trading-process.md)
- [🛡️ 安全设置指南](./docs/user-guide/security-settings.md)
- [🤝 中间人担保服务](./docs/user-guide/guarantor-service.md)
- [💳 支付系统使用](./docs/binance-pin-payment-system.md)
- [🛡️ 区块链托管服务指南](./docs/ESCROW_SYSTEM_GUIDE.md)
- [🚀 托管服务快速开始](./docs/ESCROW_QUICK_START.md)

### 🔧 开发文档 | Developer Docs
- [⚡ 快速开始](./docs/quick-start.md)
- [🏗️ 项目结构](./docs/project-structure.md)
- [🗃️ 数据库管理](./docs/database-management-guide.md)
- [🔌 API 接口文档](./docs/api-reference.md)
- [🧪 测试指南](./docs/TESTING.md)
- [📊 性能优化](./docs/performance-optimization.md)

### 🚀 部署指南 | Deployment
- [🐳 Docker 容器化部署](./docs/deployment/docker-deployment.md)
- [🖥️ 传统服务器部署](./docs/deployment/traditional-server.md)
- [☁️ 云平台部署](./docs/deployment/production-deployment.md)
- [🧪 测试环境搭建](./docs/deployment/testing-deployment.md)
- [⚙️ 开发环境配置](./docs/deployment/development-setup.md)

### 🛠️ 系统管理 | System Management
- [🔄 Release管理系统](./RELEASE_GUIDE.md)
- [📈 费用改革系统](./docs/fee-reform-implementation.md)
- [💰 资金流系统](./docs/fund-flow-system.md)
- [🏪 成本优化系统](./docs/cost-optimization-system.md)
- [📍 位置功能实现](./docs/location-features-implementation.md)

### 🔧 技术参考 | Technical Reference
- [📜 脚本文档](./docs/scripts-documentation.md)
- [🐛 TypeScript修复](./docs/typescript-fixes.md)
- [🗺️ 地图集成指南](./docs/map-integration-guide.md)
- [🔗 Redis设置指南](./docs/redis-setup.md)
- [📋 快速参考](./docs/quick-reference.md)

## 🎯 项目结构 | Project Structure

```
bitmarket/
├── 📁 app/                    # Next.js 15 App Router
│   ├── 📁 admin/             # 管理后台系统
│   │   ├── 📁 deposits/      # 充值管理
│   │   ├── 📁 orders/        # 订单管理
│   │   └── 📁 users/         # 用户管理
│   ├── 📁 api/               # API 路由
│   │   ├── 📁 admin/         # 管理员API
│   │   ├── 📁 auth/          # 认证API
│   │   ├── 📁 chat/          # 聊天API
│   │   └── 📁 payments/      # 支付API
│   ├── 📁 auth/              # 认证页面
│   ├── 📁 chat/              # 实时聊天功能
│   ├── 📁 deposit/           # 充值系统
│   ├── 📁 orders/            # 订单管理
│   ├── 📁 products/          # 商品管理
│   └── 📁 profile/           # 用户中心
├── 📁 components/            # 可复用组件库
│   ├── 📁 ui/                # 基础UI组件
│   ├── 📁 admin/             # 管理后台组件
│   ├── 📁 chat/              # 聊天组件
│   └── 📁 payment/           # 支付组件
├── 📁 lib/                   # 工具库和配置
│   ├── 📁 adapters/          # 数据适配器
│   ├── 📄 auth.ts            # 认证配置
│   ├── 📄 prisma.ts          # 数据库配置
│   └── 📄 utils.ts           # 工具函数
├── 📁 prisma/               # 数据库相关
│   ├── 📁 migrations/        # 数据库迁移
│   └── 📄 schema.prisma      # 数据库模式
├── 📁 public/               # 静态资源
├── 📁 scripts/              # 自动化脚本
│   ├── 📄 release.js         # Release自动化
│   ├── 📄 create-release-package.js  # 打包脚本
│   └── 📄 performance-*.js   # 性能监控脚本
├── 📁 docs/                 # 项目文档
│   ├── 📁 deployment/        # 部署指南
│   └── 📄 *.md              # 各类文档
├── 📁 test/                 # 测试文件
└── 📄 README.md             # 项目说明
```

## 🔄 更新日志 | Changelog

### 🎉 v1.3.2 (2025-07-25) - 当前版本
- 🛡️ **区块链托管服务**: 基于 BNB Chain 的完整托管系统，支持大金额交易安全保障
- ⚖️ **争议仲裁机制**: 多方投票的公正仲裁系统，自动化争议处理和资金分配
- 👥 **中间人生态系统**: 完整的中间人注册、认证、管理和奖励体系
- 💬 **多方聊天室**: 买家、卖家、中间人、管理员四方实时沟通功能
- 🎁 **激励奖励系统**: 中间人参与仲裁获得免手续费提现券奖励
- 🔗 **区块链集成**: BNB Chain 钱包验证、交易监控和智能合约交互
- 🧪 **完整测试体系**: 24个测试用例100%通过，覆盖数据库、API、前端、安全等
- 🔄 **Release系统**: 完整的自动化release管理系统
- 📦 **打包优化**: 智能文件排除和跨平台校验和生成
- 📚 **文档完善**: 全面更新项目文档和使用指南

### 🎉 v1.3.0 (2025-01-23)
- 🛡️ **保证金机制**: 优化中间人担保系统，增强资金安全托管
- 🎨 **品牌重构**: 导航栏logo图片+BitMarket文字组合设计
- 🧩 **UI组件库**: 完整的可复用组件系统（Card、Badge、Tabs等）
- 🔧 **技术改进**: 构建系统优化，依赖管理完善，代码质量提升
- 📱 **用户体验**: 全面改善用户界面，提升品牌识别度

### 🎉 v1.2.0 (2025-07-21)
- ⚡ **性能优化**: 系统性能全面优化，87/100 (A+级)评分
- 🧪 **测试体系**: 完整测试框架，42个测试用例100%通过
- 📊 **监控系统**: 实时性能监控和智能告警系统
- 🗄️ **数据库优化**: 49.2%查询性能提升，24个关键索引
- 🌐 **API性能**: 响应时间从秒级降至毫秒级，98%+提升

[查看完整更新日志 | View Full Changelog](./CHANGELOG.md)

## 🤝 贡献 | Contributing

我们欢迎所有形式的贡献！无论是bug报告、功能建议还是代码贡献，都能帮助BitMarket变得更好。

### 🐛 问题报告 | Bug Reports
- 使用 [GitHub Issues](https://github.com/liusu-ally/bitmarket/issues) 报告问题
- 提供详细的复现步骤和环境信息
- 包含错误截图和日志信息
- 标明您的操作系统和浏览器版本

### 💡 功能建议 | Feature Requests
- 在 Issues 中提出新功能建议
- 详细描述功能需求和使用场景
- 说明功能的预期效果和价值
- 提供相关的设计思路或参考

### 🔧 代码贡献 | Code Contributions

#### 贡献流程 | Contribution Process
1. **Fork 项目仓库** - 创建您自己的项目副本
2. **创建功能分支**: `git checkout -b feature/amazing-feature`
3. **本地开发测试** - 确保代码质量和功能正常
4. **提交更改**: `git commit -m 'feat: add amazing feature'`
5. **推送分支**: `git push origin feature/amazing-feature`
6. **创建 Pull Request** - 详细描述更改内容

#### 提交规范 | Commit Convention
```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建/工具相关
```

### 📝 开发规范 | Development Guidelines

#### 代码质量 | Code Quality
- ✅ 遵循 TypeScript 严格模式
- ✅ 使用 ESLint + Prettier 格式化代码
- ✅ 编写单元测试覆盖新功能
- ✅ 更新相关文档和注释
- ✅ 确保构建无错误：`npm run build`

#### 测试要求 | Testing Requirements
```bash
# 运行所有测试
npm run test

# 运行特定测试
npm run test:unit
npm run test:integration

# 检查测试覆盖率
npm run test:coverage
```

#### 文档更新 | Documentation Updates
- 更新相关的 README 部分
- 添加或更新 API 文档
- 更新 CHANGELOG.md
- 提供使用示例和说明

> 📚 **详细贡献指南**: 查看 [CONTRIBUTING.md](./CONTRIBUTING.md) 了解完整的贡献流程

## 📄 许可证 | License

本项目采用 MIT 许可证 - 查看 [LICENSE](./LICENSE) 文件了解详情。

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 📞 联系我们 | Contact

### 🔗 项目链接 | Project Links
- **项目主页**: [GitHub Repository](https://github.com/liusu-ally/bitmarket)
- **问题反馈**: [GitHub Issues](https://github.com/liusu-ally/bitmarket/issues)
- **Release下载**: [GitHub Releases](https://github.com/liusu-ally/bitmarket/releases)
- **项目文档**: [Documentation](./docs/README.md)

### 📧 技术支持 | Technical Support
- **邮箱支持**: <EMAIL>
- **开发团队**: <EMAIL>
- **文档反馈**: <EMAIL>
- **在线帮助**: 访问应用内 `/help` 页面

### 🌐 社区 | Community
- **讨论区**: [GitHub Discussions](https://github.com/liusu-ally/bitmarket/discussions)
- **问题追踪**: [GitHub Issues](https://github.com/liusu-ally/bitmarket/issues)
- **功能请求**: [Feature Requests](https://github.com/liusu-ally/bitmarket/issues/new?template=feature_request.md)

---

<div align="center">

### 🙏 致谢 | Acknowledgments

感谢所有为BitMarket项目做出贡献的开发者和用户！

Thanks to all developers and users who contributed to the BitMarket project!

### ⭐ 支持项目 | Support the Project

**如果这个项目对您有帮助，请给我们一个 Star！**

**If this project helps you, please give us a Star!**

[![GitHub stars](https://img.shields.io/github/stars/liusu-ally/bitmarket?style=social)](https://github.com/liusu-ally/bitmarket/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/liusu-ally/bitmarket?style=social)](https://github.com/liusu-ally/bitmarket/network/members)

---

*最后更新: 2025-07-24 | Last Updated: 2025-07-24*
*项目版本: v1.3.2 | Project Version: v1.3.2*
*维护团队: BitMarket Development Team*

Made with ❤️ by BitMarket Team

*Version: v1.3.0 | Last Updated: 2025-01-23*

</div>
