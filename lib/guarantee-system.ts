import { prisma } from '@/lib/prisma'
import { Decimal } from 'decimal.js'

// 担保金交易类型
export enum GuaranteeTransactionType {
  DEPOSIT = 'DEPOSIT',           // 充值
  WITHDRAW = 'WITHDRAW',         // 提现
  FREEZE = 'FREEZE',             // 冻结
  UNFREEZE = 'UNFREEZE',         // 解冻
  EARN = 'EARN',                 // 收入
  SPEND = 'SPEND',               // 支出
  TRANSFER = 'TRANSFER',         // 转账
  FEE = 'FEE',                   // 手续费
  REFUND = 'REFUND',             // 退款
  REWARD = 'REWARD'              // 奖励
}

// 担保金等级
export enum GuaranteeLevel {
  BRONZE = 'BRONZE',
  SILVER = 'SILVER', 
  GOLD = 'GOLD',
  DIAMOND = 'DIAMOND',
  PLATINUM = 'PLATINUM'
}

// 担保金操作结果
export interface GuaranteeOperationResult {
  success: boolean
  transactionId?: string
  balanceBefore: number
  balanceAfter: number
  frozenBefore: number
  frozenAfter: number
  error?: string
}

// 用户余额信息
export interface UserBalanceInfo {
  userId: string
  depositBalance: number
  frozenBalance: number
  availableBalance: number
  totalEarnings: number
  totalWithdrawals: number
  guaranteeLevel: string
  balanceVersion: number
}

/**
 * 担保金系统核心类
 */
export class GuaranteeSystem {
  
  /**
   * 获取用户余额信息
   */
  static async getUserBalance(userId: string): Promise<UserBalanceInfo | null> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        depositBalance: true,
        frozenBalance: true,
        availableBalance: true,
        totalEarnings: true,
        totalWithdrawals: true,
        guaranteeLevel: true,
        balanceVersion: true
      }
    })

    return user ? {
      userId: user.id,
      depositBalance: user.depositBalance,
      frozenBalance: user.frozenBalance,
      availableBalance: user.availableBalance,
      totalEarnings: user.totalEarnings,
      totalWithdrawals: user.totalWithdrawals,
      guaranteeLevel: user.guaranteeLevel,
      balanceVersion: user.balanceVersion
    } : null
  }

  /**
   * 充值担保金
   */
  static async deposit(
    userId: string,
    amount: number,
    description: string,
    txHash?: string,
    operatorId?: string
  ): Promise<GuaranteeOperationResult> {
    if (amount <= 0) {
      return { success: false, error: '充值金额必须大于0', balanceBefore: 0, balanceAfter: 0, frozenBefore: 0, frozenAfter: 0 }
    }

    return await this.executeBalanceOperation(
      userId,
      GuaranteeTransactionType.DEPOSIT,
      amount,
      description,
      { txHash, operatorId }
    )
  }

  /**
   * 提现担保金
   */
  static async withdraw(
    userId: string,
    amount: number,
    description: string,
    txHash?: string,
    operatorId?: string
  ): Promise<GuaranteeOperationResult> {
    if (amount <= 0) {
      return { success: false, error: '提现金额必须大于0', balanceBefore: 0, balanceAfter: 0, frozenBefore: 0, frozenAfter: 0 }
    }

    // 检查可用余额
    const balance = await this.getUserBalance(userId)
    if (!balance || balance.availableBalance < amount) {
      return { 
        success: false, 
        error: '可用余额不足', 
        balanceBefore: balance?.depositBalance || 0, 
        balanceAfter: balance?.depositBalance || 0,
        frozenBefore: balance?.frozenBalance || 0,
        frozenAfter: balance?.frozenBalance || 0
      }
    }

    return await this.executeBalanceOperation(
      userId,
      GuaranteeTransactionType.WITHDRAW,
      -amount, // 负数表示减少
      description,
      { txHash, operatorId }
    )
  }

  /**
   * 冻结担保金
   */
  static async freeze(
    userId: string,
    amount: number,
    description: string,
    relatedType?: string,
    relatedId?: string,
    operatorId?: string
  ): Promise<GuaranteeOperationResult> {
    if (amount <= 0) {
      return { success: false, error: '冻结金额必须大于0', balanceBefore: 0, balanceAfter: 0, frozenBefore: 0, frozenAfter: 0 }
    }

    // 检查可用余额
    const balance = await this.getUserBalance(userId)
    if (!balance || balance.availableBalance < amount) {
      return { 
        success: false, 
        error: '可用余额不足', 
        balanceBefore: balance?.depositBalance || 0, 
        balanceAfter: balance?.depositBalance || 0,
        frozenBefore: balance?.frozenBalance || 0,
        frozenAfter: balance?.frozenBalance || 0
      }
    }

    return await this.executeBalanceOperation(
      userId,
      GuaranteeTransactionType.FREEZE,
      0, // 总余额不变，只是在可用和冻结之间转移
      description,
      { relatedType, relatedId, operatorId, freezeAmount: amount }
    )
  }

  /**
   * 解冻担保金
   */
  static async unfreeze(
    userId: string,
    amount: number,
    description: string,
    relatedType?: string,
    relatedId?: string,
    operatorId?: string
  ): Promise<GuaranteeOperationResult> {
    if (amount <= 0) {
      return { success: false, error: '解冻金额必须大于0', balanceBefore: 0, balanceAfter: 0, frozenBefore: 0, frozenAfter: 0 }
    }

    // 检查冻结余额
    const balance = await this.getUserBalance(userId)
    if (!balance || balance.frozenBalance < amount) {
      return { 
        success: false, 
        error: '冻结余额不足', 
        balanceBefore: balance?.depositBalance || 0, 
        balanceAfter: balance?.depositBalance || 0,
        frozenBefore: balance?.frozenBalance || 0,
        frozenAfter: balance?.frozenBalance || 0
      }
    }

    return await this.executeBalanceOperation(
      userId,
      GuaranteeTransactionType.UNFREEZE,
      0, // 总余额不变，只是在冻结和可用之间转移
      description,
      { relatedType, relatedId, operatorId, unfreezeAmount: amount }
    )
  }

  /**
   * 交易收入（自动沉淀到担保金）
   */
  static async addEarning(
    userId: string,
    amount: number,
    description: string,
    relatedType?: string,
    relatedId?: string
  ): Promise<GuaranteeOperationResult> {
    if (amount <= 0) {
      return { success: false, error: '收入金额必须大于0', balanceBefore: 0, balanceAfter: 0, frozenBefore: 0, frozenAfter: 0 }
    }

    return await this.executeBalanceOperation(
      userId,
      GuaranteeTransactionType.EARN,
      amount,
      description,
      { relatedType, relatedId, isEarning: true }
    )
  }

  /**
   * 支付消费（从担保金扣除）
   */
  static async spend(
    userId: string,
    amount: number,
    description: string,
    relatedType?: string,
    relatedId?: string
  ): Promise<GuaranteeOperationResult> {
    if (amount <= 0) {
      return { success: false, error: '支付金额必须大于0', balanceBefore: 0, balanceAfter: 0, frozenBefore: 0, frozenAfter: 0 }
    }

    // 检查可用余额
    const balance = await this.getUserBalance(userId)
    if (!balance || balance.availableBalance < amount) {
      return { 
        success: false, 
        error: '可用余额不足', 
        balanceBefore: balance?.depositBalance || 0, 
        balanceAfter: balance?.depositBalance || 0,
        frozenBefore: balance?.frozenBalance || 0,
        frozenAfter: balance?.frozenBalance || 0
      }
    }

    return await this.executeBalanceOperation(
      userId,
      GuaranteeTransactionType.SPEND,
      -amount, // 负数表示减少
      description,
      { relatedType, relatedId }
    )
  }

  /**
   * 执行余额操作的核心方法
   */
  private static async executeBalanceOperation(
    userId: string,
    type: GuaranteeTransactionType,
    amount: number,
    description: string,
    options: {
      txHash?: string
      operatorId?: string
      relatedType?: string
      relatedId?: string
      freezeAmount?: number
      unfreezeAmount?: number
      isEarning?: boolean
      batchId?: string
    } = {}
  ): Promise<GuaranteeOperationResult> {
    
    return await prisma.$transaction(async (tx) => {
      // 获取当前用户余额（加锁）
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: {
          depositBalance: true,
          frozenBalance: true,
          availableBalance: true,
          totalEarnings: true,
          totalWithdrawals: true,
          balanceVersion: true
        }
      })

      if (!user) {
        throw new Error('用户不存在')
      }

      const balanceBefore = user.depositBalance
      const frozenBefore = user.frozenBalance
      const availableBefore = user.availableBalance

      // 计算新的余额
      let newDepositBalance = balanceBefore
      let newFrozenBalance = frozenBefore
      let newAvailableBalance = availableBefore
      let newTotalEarnings = user.totalEarnings
      let newTotalWithdrawals = user.totalWithdrawals

      // 根据操作类型计算新余额
      switch (type) {
        case GuaranteeTransactionType.DEPOSIT:
          newDepositBalance += amount
          newAvailableBalance += amount
          break
          
        case GuaranteeTransactionType.WITHDRAW:
          newDepositBalance += amount // amount 是负数
          newAvailableBalance += amount
          newTotalWithdrawals += Math.abs(amount)
          break
          
        case GuaranteeTransactionType.FREEZE:
          const freezeAmount = options.freezeAmount || 0
          newFrozenBalance += freezeAmount
          newAvailableBalance -= freezeAmount
          break
          
        case GuaranteeTransactionType.UNFREEZE:
          const unfreezeAmount = options.unfreezeAmount || 0
          newFrozenBalance -= unfreezeAmount
          newAvailableBalance += unfreezeAmount
          break
          
        case GuaranteeTransactionType.EARN:
          newDepositBalance += amount
          newAvailableBalance += amount
          if (options.isEarning) {
            newTotalEarnings += amount
          }
          break
          
        case GuaranteeTransactionType.SPEND:
          newDepositBalance += amount // amount 是负数
          newAvailableBalance += amount
          break
      }

      // 更新用户余额
      await tx.user.update({
        where: { 
          id: userId,
          balanceVersion: user.balanceVersion // 乐观锁
        },
        data: {
          depositBalance: newDepositBalance,
          frozenBalance: newFrozenBalance,
          availableBalance: newAvailableBalance,
          totalEarnings: newTotalEarnings,
          totalWithdrawals: newTotalWithdrawals,
          lastBalanceUpdate: new Date(),
          balanceVersion: user.balanceVersion + 1
        }
      })

      // 创建交易记录
      const transaction = await tx.guaranteeTransaction.create({
        data: {
          userId,
          type,
          amount: type === GuaranteeTransactionType.FREEZE || type === GuaranteeTransactionType.UNFREEZE 
            ? (options.freezeAmount || options.unfreezeAmount || 0) 
            : amount,
          balanceBefore,
          balanceAfter: newDepositBalance,
          frozenBefore,
          frozenAfter: newFrozenBalance,
          description,
          relatedType: options.relatedType,
          relatedId: options.relatedId,
          txHash: options.txHash,
          operatorId: options.operatorId,
          batchId: options.batchId,
          status: 'COMPLETED'
        }
      })

      return {
        success: true,
        transactionId: transaction.id,
        balanceBefore,
        balanceAfter: newDepositBalance,
        frozenBefore,
        frozenAfter: newFrozenBalance
      }
    })
  }

  /**
   * 获取用户担保金等级
   */
  static async getUserGuaranteeLevel(userId: string): Promise<string> {
    const balance = await this.getUserBalance(userId)
    if (!balance) return GuaranteeLevel.BRONZE

    const levels = await prisma.guaranteeLevel.findMany({
      where: { isActive: true },
      orderBy: { minBalance: 'desc' }
    })

    for (const level of levels) {
      if (balance.depositBalance >= level.minBalance) {
        return level.level
      }
    }

    return GuaranteeLevel.BRONZE
  }

  /**
   * 更新用户担保金等级
   */
  static async updateUserGuaranteeLevel(userId: string): Promise<void> {
    const newLevel = await this.getUserGuaranteeLevel(userId)

    await prisma.user.update({
      where: { id: userId },
      data: { guaranteeLevel: newLevel }
    })
  }

  /**
   * 批量提现处理
   */
  static async processBatchWithdrawal(
    withdrawalRequests: Array<{
      userId: string
      amount: number
      description: string
    }>,
    operatorId: string
  ): Promise<{
    batchId: string
    successCount: number
    failureCount: number
    totalAmount: number
  }> {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    let successCount = 0
    let failureCount = 0
    let totalAmount = 0

    // 创建批次记录
    await prisma.batchSettlement.create({
      data: {
        batchId,
        type: 'WITHDRAWAL',
        totalAmount: withdrawalRequests.reduce((sum, req) => sum + req.amount, 0),
        totalTransactions: withdrawalRequests.length,
        status: 'PROCESSING',
        startedAt: new Date(),
        operatorId
      }
    })

    // 处理每个提现请求
    for (const request of withdrawalRequests) {
      try {
        const result = await this.executeBalanceOperation(
          request.userId,
          GuaranteeTransactionType.WITHDRAW,
          -request.amount,
          request.description,
          { operatorId, batchId }
        )

        if (result.success) {
          successCount++
          totalAmount += request.amount
        } else {
          failureCount++
        }
      } catch (error) {
        failureCount++
        console.error(`批量提现失败 - 用户: ${request.userId}, 金额: ${request.amount}`, error)
      }
    }

    // 更新批次状态
    await prisma.batchSettlement.update({
      where: { batchId },
      data: {
        status: failureCount === 0 ? 'COMPLETED' : 'PARTIALLY_COMPLETED',
        completedAt: new Date(),
        metadata: {
          successCount,
          failureCount,
          actualTotalAmount: totalAmount
        }
      }
    })

    return {
      batchId,
      successCount,
      failureCount,
      totalAmount
    }
  }

  /**
   * 获取用户交易历史
   */
  static async getUserTransactionHistory(
    userId: string,
    options: {
      type?: GuaranteeTransactionType
      limit?: number
      offset?: number
      startDate?: Date
      endDate?: Date
    } = {}
  ) {
    const where: any = { userId }

    if (options.type) {
      where.type = options.type
    }

    if (options.startDate || options.endDate) {
      where.createdAt = {}
      if (options.startDate) where.createdAt.gte = options.startDate
      if (options.endDate) where.createdAt.lte = options.endDate
    }

    const [transactions, total] = await Promise.all([
      prisma.guaranteeTransaction.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: options.limit || 50,
        skip: options.offset || 0,
        include: {
          operator: {
            select: { id: true, name: true }
          }
        }
      }),
      prisma.guaranteeTransaction.count({ where })
    ])

    return { transactions, total }
  }

  /**
   * 获取资金池统计
   */
  static async getFundPoolStats(date?: Date): Promise<any> {
    const targetDate = date || new Date()
    const dateStr = targetDate.toISOString().split('T')[0]

    // 尝试获取当日统计
    let stats = await prisma.fundPoolStats.findUnique({
      where: { date: new Date(dateStr) }
    })

    // 如果不存在，则计算并创建
    if (!stats) {
      const userStats = await prisma.user.aggregate({
        _sum: {
          depositBalance: true,
          frozenBalance: true,
          availableBalance: true,
          totalEarnings: true,
          totalWithdrawals: true
        },
        _count: {
          id: true
        },
        _avg: {
          depositBalance: true
        }
      })

      const transactionStats = await prisma.guaranteeTransaction.aggregate({
        where: {
          createdAt: {
            gte: new Date(dateStr),
            lt: new Date(new Date(dateStr).getTime() + 24 * 60 * 60 * 1000)
          }
        },
        _count: { id: true },
        _sum: {
          amount: true
        }
      })

      stats = await prisma.fundPoolStats.create({
        data: {
          date: new Date(dateStr),
          totalDeposits: userStats._sum.depositBalance || 0,
          totalFrozen: userStats._sum.frozenBalance || 0,
          totalAvailable: userStats._sum.availableBalance || 0,
          totalEarnings: userStats._sum.totalEarnings || 0,
          totalWithdrawals: userStats._sum.totalWithdrawals || 0,
          activeUsers: userStats._count.id || 0,
          transactionCount: transactionStats._count.id || 0,
          averageBalance: userStats._avg.depositBalance || 0,
          utilizationRate: userStats._sum.depositBalance
            ? ((userStats._sum.frozenBalance || 0) / (userStats._sum.depositBalance || 1)) * 100
            : 0
        }
      })
    }

    return stats
  }

  /**
   * 计算用户可提现金额（考虑等级限制）
   */
  static async getMaxWithdrawableAmount(userId: string): Promise<number> {
    const balance = await this.getUserBalance(userId)
    if (!balance) return 0

    const level = await prisma.guaranteeLevel.findUnique({
      where: { level: balance.guaranteeLevel }
    })

    if (!level) return balance.availableBalance

    // 检查今日已提现金额
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const todayWithdrawals = await prisma.guaranteeTransaction.aggregate({
      where: {
        userId,
        type: GuaranteeTransactionType.WITHDRAW,
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      },
      _sum: { amount: true }
    })

    const todayWithdrawn = Math.abs(todayWithdrawals._sum.amount || 0)
    const remainingDailyLimit = Math.max(0, level.maxDailyWithdrawal - todayWithdrawn)

    return Math.min(balance.availableBalance, remainingDailyLimit)
  }
}
