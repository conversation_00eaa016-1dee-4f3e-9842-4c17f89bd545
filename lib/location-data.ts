/**
 * 中国标准化城市和区县数据
 * 支持城市-区县级联选择和搜索功能
 */

export interface District {
  code: string
  name: string
  pinyin: string
}

export interface City {
  code: string
  name: string
  pinyin: string
  province: string
  districts: District[]
}

export interface Province {
  code: string
  name: string
  cities: City[]
}

// 主要城市和区县数据（精选版）
export const locationData: Province[] = [
  {
    code: '11',
    name: '北京市',
    cities: [
      {
        code: '1101',
        name: '北京市',
        pinyin: 'beijing',
        province: '北京市',
        districts: [
          { code: '110101', name: '东城区', pinyin: 'dongcheng' },
          { code: '110102', name: '西城区', pinyin: 'xicheng' },
          { code: '110105', name: '朝阳区', pinyin: 'chaoyang' },
          { code: '110106', name: '丰台区', pinyin: 'fengtai' },
          { code: '110107', name: '石景山区', pinyin: 'shijin<PERSON>han' },
          { code: '110108', name: '海淀区', pinyin: 'haidian' },
          { code: '110109', name: '门头沟区', pinyin: 'mentougou' },
          { code: '110111', name: '房山区', pinyin: 'fangshan' },
          { code: '110112', name: '通州区', pinyin: 'tongzhou' },
          { code: '110113', name: '顺义区', pinyin: 'shunyi' },
          { code: '110114', name: '昌平区', pinyin: 'changping' },
          { code: '110115', name: '大兴区', pinyin: 'daxing' },
          { code: '110116', name: '怀柔区', pinyin: 'huairou' },
          { code: '110117', name: '平谷区', pinyin: 'pinggu' },
          { code: '110118', name: '密云区', pinyin: 'miyun' },
          { code: '110119', name: '延庆区', pinyin: 'yanqing' }
        ]
      }
    ]
  },
  {
    code: '31',
    name: '上海市',
    cities: [
      {
        code: '3101',
        name: '上海市',
        pinyin: 'shanghai',
        province: '上海市',
        districts: [
          { code: '310101', name: '黄浦区', pinyin: 'huangpu' },
          { code: '310104', name: '徐汇区', pinyin: 'xuhui' },
          { code: '310105', name: '长宁区', pinyin: 'changning' },
          { code: '310106', name: '静安区', pinyin: 'jingan' },
          { code: '310107', name: '普陀区', pinyin: 'putuo' },
          { code: '310109', name: '虹口区', pinyin: 'hongkou' },
          { code: '310110', name: '杨浦区', pinyin: 'yangpu' },
          { code: '310112', name: '闵行区', pinyin: 'minhang' },
          { code: '310113', name: '宝山区', pinyin: 'baoshan' },
          { code: '310114', name: '嘉定区', pinyin: 'jiading' },
          { code: '310115', name: '浦东新区', pinyin: 'pudong' },
          { code: '310116', name: '金山区', pinyin: 'jinshan' },
          { code: '310117', name: '松江区', pinyin: 'songjiang' },
          { code: '310118', name: '青浦区', pinyin: 'qingpu' },
          { code: '310120', name: '奉贤区', pinyin: 'fengxian' },
          { code: '310151', name: '崇明区', pinyin: 'chongming' }
        ]
      }
    ]
  },
  {
    code: '44',
    name: '广东省',
    cities: [
      {
        code: '4401',
        name: '广州市',
        pinyin: 'guangzhou',
        province: '广东省',
        districts: [
          { code: '440103', name: '荔湾区', pinyin: 'liwan' },
          { code: '440104', name: '越秀区', pinyin: 'yuexiu' },
          { code: '440105', name: '海珠区', pinyin: 'haizhu' },
          { code: '440106', name: '天河区', pinyin: 'tianhe' },
          { code: '440111', name: '白云区', pinyin: 'baiyun' },
          { code: '440112', name: '黄埔区', pinyin: 'huangpu' },
          { code: '440113', name: '番禺区', pinyin: 'panyu' },
          { code: '440114', name: '花都区', pinyin: 'huadu' },
          { code: '440115', name: '南沙区', pinyin: 'nansha' },
          { code: '440117', name: '从化区', pinyin: 'conghua' },
          { code: '440118', name: '增城区', pinyin: 'zengcheng' }
        ]
      },
      {
        code: '4403',
        name: '深圳市',
        pinyin: 'shenzhen',
        province: '广东省',
        districts: [
          { code: '440303', name: '罗湖区', pinyin: 'luohu' },
          { code: '440304', name: '福田区', pinyin: 'futian' },
          { code: '440305', name: '南山区', pinyin: 'nanshan' },
          { code: '440306', name: '宝安区', pinyin: 'baoan' },
          { code: '440307', name: '龙岗区', pinyin: 'longgang' },
          { code: '440308', name: '盐田区', pinyin: 'yantian' },
          { code: '440309', name: '龙华区', pinyin: 'longhua' },
          { code: '440310', name: '坪山区', pinyin: 'pingshan' },
          { code: '440311', name: '光明区', pinyin: 'guangming' },
          { code: '440312', name: '大鹏新区', pinyin: 'dapeng' }
        ]
      }
    ]
  },
  {
    code: '33',
    name: '浙江省',
    cities: [
      {
        code: '3301',
        name: '杭州市',
        pinyin: 'hangzhou',
        province: '浙江省',
        districts: [
          { code: '330102', name: '上城区', pinyin: 'shangcheng' },
          { code: '330105', name: '拱墅区', pinyin: 'gongshu' },
          { code: '330106', name: '西湖区', pinyin: 'xihu' },
          { code: '330108', name: '滨江区', pinyin: 'binjiang' },
          { code: '330109', name: '萧山区', pinyin: 'xiaoshan' },
          { code: '330110', name: '余杭区', pinyin: 'yuhang' },
          { code: '330111', name: '富阳区', pinyin: 'fuyang' },
          { code: '330112', name: '临安区', pinyin: 'linan' },
          { code: '330113', name: '临平区', pinyin: 'linping' },
          { code: '330114', name: '钱塘区', pinyin: 'qiantang' }
        ]
      }
    ]
  },
  {
    code: '32',
    name: '江苏省',
    cities: [
      {
        code: '3201',
        name: '南京市',
        pinyin: 'nanjing',
        province: '江苏省',
        districts: [
          { code: '320102', name: '玄武区', pinyin: 'xuanwu' },
          { code: '320104', name: '秦淮区', pinyin: 'qinhuai' },
          { code: '320105', name: '建邺区', pinyin: 'jianye' },
          { code: '320106', name: '鼓楼区', pinyin: 'gulou' },
          { code: '320111', name: '浦口区', pinyin: 'pukou' },
          { code: '320113', name: '栖霞区', pinyin: 'qixia' },
          { code: '320114', name: '雨花台区', pinyin: 'yuhuatai' },
          { code: '320115', name: '江宁区', pinyin: 'jiangning' },
          { code: '320116', name: '六合区', pinyin: 'liuhe' },
          { code: '320117', name: '溧水区', pinyin: 'lishui' },
          { code: '320118', name: '高淳区', pinyin: 'gaochun' }
        ]
      }
    ]
  }
]

// 获取所有城市列表
export const getAllCities = (): City[] => {
  return locationData.flatMap(province => province.cities)
}

// 获取所有省份列表
export const getAllProvinces = (): Province[] => {
  return locationData
}

// 根据城市名称获取区县列表
export const getDistrictsByCity = (cityName: string): District[] => {
  const city = getAllCities().find(c => c.name === cityName)
  return city?.districts || []
}

// 搜索城市（支持拼音搜索）
export const searchCities = (query: string): City[] => {
  if (!query.trim()) return []
  
  const lowerQuery = query.toLowerCase()
  return getAllCities().filter(city => 
    city.name.includes(query) || 
    city.pinyin.includes(lowerQuery)
  )
}

// 搜索区县（支持拼音搜索）
export const searchDistricts = (query: string, cityName?: string): District[] => {
  if (!query.trim()) return []
  
  const lowerQuery = query.toLowerCase()
  let districts: District[] = []
  
  if (cityName) {
    districts = getDistrictsByCity(cityName)
  } else {
    districts = getAllCities().flatMap(city => city.districts)
  }
  
  return districts.filter(district => 
    district.name.includes(query) || 
    district.pinyin.includes(lowerQuery)
  )
}

// 验证城市和区县的有效性
export const validateLocation = (cityName: string, districtName?: string): boolean => {
  const city = getAllCities().find(c => c.name === cityName)
  if (!city) return false
  
  if (districtName) {
    return city.districts.some(d => d.name === districtName)
  }
  
  return true
}
