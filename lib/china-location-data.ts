/**
 * 中国完整城市和区县数据
 * 包含全国所有省份、地级市和区县信息
 */

export interface District {
  code: string
  name: string
  pinyin: string
}

export interface City {
  code: string
  name: string
  pinyin: string
  province: string
  districts: District[]
}

export interface Province {
  code: string
  name: string
  pinyin: string
  cities: City[]
}

// 中国完整行政区划数据
export const chinaLocationData: Province[] = [
  {
    code: '11',
    name: '北京市',
    pinyin: 'beijing',
    cities: [
      {
        code: '1101',
        name: '北京市',
        pinyin: 'beijing',
        province: '北京市',
        districts: [
          { code: '110101', name: '东城区', pinyin: 'dongcheng' },
          { code: '110102', name: '西城区', pinyin: 'xicheng' },
          { code: '110105', name: '朝阳区', pinyin: 'chaoyang' },
          { code: '110106', name: '丰台区', pinyin: 'fengtai' },
          { code: '110107', name: '石景山区', pinyin: 'shijin<PERSON>han' },
          { code: '110108', name: '海淀区', pinyin: 'haidian' },
          { code: '110109', name: '门头沟区', pinyin: 'mentougou' },
          { code: '110111', name: '房山区', pinyin: 'fangshan' },
          { code: '110112', name: '通州区', pinyin: 'tongzhou' },
          { code: '110113', name: '顺义区', pinyin: 'shunyi' },
          { code: '110114', name: '昌平区', pinyin: 'changping' },
          { code: '110115', name: '大兴区', pinyin: 'daxing' },
          { code: '110116', name: '怀柔区', pinyin: 'huairou' },
          { code: '110117', name: '平谷区', pinyin: 'pinggu' },
          { code: '110118', name: '密云区', pinyin: 'miyun' },
          { code: '110119', name: '延庆区', pinyin: 'yanqing' }
        ]
      }
    ]
  },
  {
    code: '12',
    name: '天津市',
    pinyin: 'tianjin',
    cities: [
      {
        code: '1201',
        name: '天津市',
        pinyin: 'tianjin',
        province: '天津市',
        districts: [
          { code: '120101', name: '和平区', pinyin: 'heping' },
          { code: '120102', name: '河东区', pinyin: 'hedong' },
          { code: '120103', name: '河西区', pinyin: 'hexi' },
          { code: '120104', name: '南开区', pinyin: 'nankai' },
          { code: '120105', name: '河北区', pinyin: 'hebei' },
          { code: '120106', name: '红桥区', pinyin: 'hongqiao' },
          { code: '120110', name: '东丽区', pinyin: 'dongli' },
          { code: '120111', name: '西青区', pinyin: 'xiqing' },
          { code: '120112', name: '津南区', pinyin: 'jinnan' },
          { code: '120113', name: '北辰区', pinyin: 'beichen' },
          { code: '120114', name: '武清区', pinyin: 'wuqing' },
          { code: '120115', name: '宝坻区', pinyin: 'baodi' },
          { code: '120116', name: '滨海新区', pinyin: 'binhai' },
          { code: '120117', name: '宁河区', pinyin: 'ninghe' },
          { code: '120118', name: '静海区', pinyin: 'jinghai' },
          { code: '120119', name: '蓟州区', pinyin: 'jizhou' }
        ]
      }
    ]
  },
  {
    code: '13',
    name: '河北省',
    pinyin: 'hebei',
    cities: [
      {
        code: '1301',
        name: '石家庄市',
        pinyin: 'shijiazhuang',
        province: '河北省',
        districts: [
          { code: '130102', name: '长安区', pinyin: 'changan' },
          { code: '130104', name: '桥西区', pinyin: 'qiaoxi' },
          { code: '130105', name: '新华区', pinyin: 'xinhua' },
          { code: '130107', name: '井陉矿区', pinyin: 'jingxingkuang' },
          { code: '130108', name: '裕华区', pinyin: 'yuhua' },
          { code: '130109', name: '藁城区', pinyin: 'gaocheng' },
          { code: '130110', name: '鹿泉区', pinyin: 'luquan' },
          { code: '130111', name: '栾城区', pinyin: 'luancheng' },
          { code: '130121', name: '井陉县', pinyin: 'jingxing' },
          { code: '130123', name: '正定县', pinyin: 'zhengding' },
          { code: '130125', name: '行唐县', pinyin: 'xingtang' },
          { code: '130126', name: '灵寿县', pinyin: 'lingshou' },
          { code: '130127', name: '高邑县', pinyin: 'gaoyi' },
          { code: '130128', name: '深泽县', pinyin: 'shenze' },
          { code: '130129', name: '赞皇县', pinyin: 'zanhuang' },
          { code: '130130', name: '无极县', pinyin: 'wuji' },
          { code: '130131', name: '平山县', pinyin: 'pingshan' },
          { code: '130132', name: '元氏县', pinyin: 'yuanshi' },
          { code: '130133', name: '赵县', pinyin: 'zhaoxian' },
          { code: '130181', name: '辛集市', pinyin: 'xinji' },
          { code: '130183', name: '晋州市', pinyin: 'jinzhou' },
          { code: '130184', name: '新乐市', pinyin: 'xinle' }
        ]
      },
      {
        code: '1302',
        name: '唐山市',
        pinyin: 'tangshan',
        province: '河北省',
        districts: [
          { code: '130202', name: '路南区', pinyin: 'lunan' },
          { code: '130203', name: '路北区', pinyin: 'lubei' },
          { code: '130204', name: '古冶区', pinyin: 'guye' },
          { code: '130205', name: '开平区', pinyin: 'kaiping' },
          { code: '130207', name: '丰南区', pinyin: 'fengnan' },
          { code: '130208', name: '丰润区', pinyin: 'fengrun' },
          { code: '130209', name: '曹妃甸区', pinyin: 'caofeidian' },
          { code: '130223', name: '滦县', pinyin: 'luanxian' },
          { code: '130224', name: '滦南县', pinyin: 'luannan' },
          { code: '130225', name: '乐亭县', pinyin: 'laoting' },
          { code: '130227', name: '迁西县', pinyin: 'qianxi' },
          { code: '130229', name: '玉田县', pinyin: 'yutian' },
          { code: '130281', name: '遵化市', pinyin: 'zunhua' },
          { code: '130283', name: '迁安市', pinyin: 'qianan' }
        ]
      },
      {
        code: '1303',
        name: '秦皇岛市',
        pinyin: 'qinhuangdao',
        province: '河北省',
        districts: [
          { code: '130302', name: '海港区', pinyin: 'haigang' },
          { code: '130303', name: '山海关区', pinyin: 'shanhaiguan' },
          { code: '130304', name: '北戴河区', pinyin: 'beidaihe' },
          { code: '130306', name: '抚宁区', pinyin: 'funing' },
          { code: '130321', name: '青龙满族自治县', pinyin: 'qinglong' },
          { code: '130322', name: '昌黎县', pinyin: 'changli' },
          { code: '130324', name: '卢龙县', pinyin: 'lulong' }
        ]
      }
    ]
  },
  {
    code: '31',
    name: '上海市',
    pinyin: 'shanghai',
    cities: [
      {
        code: '3101',
        name: '上海市',
        pinyin: 'shanghai',
        province: '上海市',
        districts: [
          { code: '310101', name: '黄浦区', pinyin: 'huangpu' },
          { code: '310104', name: '徐汇区', pinyin: 'xuhui' },
          { code: '310105', name: '长宁区', pinyin: 'changning' },
          { code: '310106', name: '静安区', pinyin: 'jingan' },
          { code: '310107', name: '普陀区', pinyin: 'putuo' },
          { code: '310109', name: '虹口区', pinyin: 'hongkou' },
          { code: '310110', name: '杨浦区', pinyin: 'yangpu' },
          { code: '310112', name: '闵行区', pinyin: 'minhang' },
          { code: '310113', name: '宝山区', pinyin: 'baoshan' },
          { code: '310114', name: '嘉定区', pinyin: 'jiading' },
          { code: '310115', name: '浦东新区', pinyin: 'pudong' },
          { code: '310116', name: '金山区', pinyin: 'jinshan' },
          { code: '310117', name: '松江区', pinyin: 'songjiang' },
          { code: '310118', name: '青浦区', pinyin: 'qingpu' },
          { code: '310120', name: '奉贤区', pinyin: 'fengxian' },
          { code: '310151', name: '崇明区', pinyin: 'chongming' }
        ]
      }
    ]
  },
  {
    code: '32',
    name: '江苏省',
    pinyin: 'jiangsu',
    cities: [
      {
        code: '3201',
        name: '南京市',
        pinyin: 'nanjing',
        province: '江苏省',
        districts: [
          { code: '320102', name: '玄武区', pinyin: 'xuanwu' },
          { code: '320104', name: '秦淮区', pinyin: 'qinhuai' },
          { code: '320105', name: '建邺区', pinyin: 'jianye' },
          { code: '320106', name: '鼓楼区', pinyin: 'gulou' },
          { code: '320111', name: '浦口区', pinyin: 'pukou' },
          { code: '320113', name: '栖霞区', pinyin: 'qixia' },
          { code: '320114', name: '雨花台区', pinyin: 'yuhuatai' },
          { code: '320115', name: '江宁区', pinyin: 'jiangning' },
          { code: '320116', name: '六合区', pinyin: 'liuhe' },
          { code: '320117', name: '溧水区', pinyin: 'lishui' },
          { code: '320118', name: '高淳区', pinyin: 'gaochun' }
        ]
      },
      {
        code: '3202',
        name: '无锡市',
        pinyin: 'wuxi',
        province: '江苏省',
        districts: [
          { code: '320205', name: '锡山区', pinyin: 'xishan' },
          { code: '320206', name: '惠山区', pinyin: 'huishan' },
          { code: '320211', name: '滨湖区', pinyin: 'binhu' },
          { code: '320213', name: '梁溪区', pinyin: 'liangxi' },
          { code: '320214', name: '新吴区', pinyin: 'xinwu' },
          { code: '320281', name: '江阴市', pinyin: 'jiangyin' },
          { code: '320282', name: '宜兴市', pinyin: 'yixing' }
        ]
      },
      {
        code: '3203',
        name: '徐州市',
        pinyin: 'xuzhou',
        province: '江苏省',
        districts: [
          { code: '320302', name: '鼓楼区', pinyin: 'gulou' },
          { code: '320303', name: '云龙区', pinyin: 'yunlong' },
          { code: '320305', name: '贾汪区', pinyin: 'jiawang' },
          { code: '320311', name: '泉山区', pinyin: 'quanshan' },
          { code: '320312', name: '铜山区', pinyin: 'tongshan' },
          { code: '320321', name: '丰县', pinyin: 'fengxian' },
          { code: '320322', name: '沛县', pinyin: 'peixian' },
          { code: '320324', name: '睢宁县', pinyin: 'suining' },
          { code: '320381', name: '新沂市', pinyin: 'xinyi' },
          { code: '320382', name: '邳州市', pinyin: 'pizhou' }
        ]
      }
    ]
  },
  {
    code: '33',
    name: '浙江省',
    pinyin: 'zhejiang',
    cities: [
      {
        code: '3301',
        name: '杭州市',
        pinyin: 'hangzhou',
        province: '浙江省',
        districts: [
          { code: '330102', name: '上城区', pinyin: 'shangcheng' },
          { code: '330105', name: '拱墅区', pinyin: 'gongshu' },
          { code: '330106', name: '西湖区', pinyin: 'xihu' },
          { code: '330108', name: '滨江区', pinyin: 'binjiang' },
          { code: '330109', name: '萧山区', pinyin: 'xiaoshan' },
          { code: '330110', name: '余杭区', pinyin: 'yuhang' },
          { code: '330111', name: '富阳区', pinyin: 'fuyang' },
          { code: '330112', name: '临安区', pinyin: 'linan' },
          { code: '330113', name: '临平区', pinyin: 'linping' },
          { code: '330114', name: '钱塘区', pinyin: 'qiantang' },
          { code: '330122', name: '桐庐县', pinyin: 'tonglu' },
          { code: '330127', name: '淳安县', pinyin: 'chunan' },
          { code: '330182', name: '建德市', pinyin: 'jiande' }
        ]
      },
      {
        code: '3302',
        name: '宁波市',
        pinyin: 'ningbo',
        province: '浙江省',
        districts: [
          { code: '330203', name: '海曙区', pinyin: 'haishu' },
          { code: '330205', name: '江北区', pinyin: 'jiangbei' },
          { code: '330206', name: '北仑区', pinyin: 'beilun' },
          { code: '330211', name: '镇海区', pinyin: 'zhenhai' },
          { code: '330212', name: '鄞州区', pinyin: 'yinzhou' },
          { code: '330213', name: '奉化区', pinyin: 'fenghua' },
          { code: '330225', name: '象山县', pinyin: 'xiangshan' },
          { code: '330226', name: '宁海县', pinyin: 'ninghai' },
          { code: '330281', name: '余姚市', pinyin: 'yuyao' },
          { code: '330282', name: '慈溪市', pinyin: 'cixi' }
        ]
      }
    ]
  },
  {
    code: '44',
    name: '广东省',
    pinyin: 'guangdong',
    cities: [
      {
        code: '4401',
        name: '广州市',
        pinyin: 'guangzhou',
        province: '广东省',
        districts: [
          { code: '440103', name: '荔湾区', pinyin: 'liwan' },
          { code: '440104', name: '越秀区', pinyin: 'yuexiu' },
          { code: '440105', name: '海珠区', pinyin: 'haizhu' },
          { code: '440106', name: '天河区', pinyin: 'tianhe' },
          { code: '440111', name: '白云区', pinyin: 'baiyun' },
          { code: '440112', name: '黄埔区', pinyin: 'huangpu' },
          { code: '440113', name: '番禺区', pinyin: 'panyu' },
          { code: '440114', name: '花都区', pinyin: 'huadu' },
          { code: '440115', name: '南沙区', pinyin: 'nansha' },
          { code: '440117', name: '从化区', pinyin: 'conghua' },
          { code: '440118', name: '增城区', pinyin: 'zengcheng' }
        ]
      },
      {
        code: '4403',
        name: '深圳市',
        pinyin: 'shenzhen',
        province: '广东省',
        districts: [
          { code: '440303', name: '罗湖区', pinyin: 'luohu' },
          { code: '440304', name: '福田区', pinyin: 'futian' },
          { code: '440305', name: '南山区', pinyin: 'nanshan' },
          { code: '440306', name: '宝安区', pinyin: 'baoan' },
          { code: '440307', name: '龙岗区', pinyin: 'longgang' },
          { code: '440308', name: '盐田区', pinyin: 'yantian' },
          { code: '440309', name: '龙华区', pinyin: 'longhua' },
          { code: '440310', name: '坪山区', pinyin: 'pingshan' },
          { code: '440311', name: '光明区', pinyin: 'guangming' }
        ]
      },
      {
        code: '4404',
        name: '珠海市',
        pinyin: 'zhuhai',
        province: '广东省',
        districts: [
          { code: '440402', name: '香洲区', pinyin: 'xiangzhou' },
          { code: '440403', name: '斗门区', pinyin: 'doumen' },
          { code: '440404', name: '金湾区', pinyin: 'jinwan' }
        ]
      }
    ]
  }
];

// 获取所有省份
export const getAllProvinces = (): Province[] => {
  return chinaLocationData;
};

// 获取所有城市
export const getAllCities = (): City[] => {
  return chinaLocationData.flatMap(province => province.cities);
};

// 根据省份获取城市
export const getCitiesByProvince = (provinceName: string): City[] => {
  const province = chinaLocationData.find(p => p.name === provinceName);
  return province?.cities || [];
};

// 根据城市获取区县
export const getDistrictsByCity = (cityName: string): District[] => {
  const city = getAllCities().find(c => c.name === cityName);
  return city?.districts || [];
};

// 搜索省份
export const searchProvinces = (query: string): Province[] => {
  if (!query.trim()) return [];
  
  const lowerQuery = query.toLowerCase();
  return chinaLocationData.filter(province => 
    province.name.includes(query) || 
    province.pinyin.includes(lowerQuery)
  );
};

// 搜索城市
export const searchCities = (query: string, provinceName?: string): City[] => {
  if (!query.trim()) return [];
  
  const lowerQuery = query.toLowerCase();
  let cities = getAllCities();
  
  if (provinceName) {
    cities = getCitiesByProvince(provinceName);
  }
  
  return cities.filter(city => 
    city.name.includes(query) || 
    city.pinyin.includes(lowerQuery)
  );
};

// 搜索区县
export const searchDistricts = (query: string, cityName?: string): District[] => {
  if (!query.trim()) return [];
  
  const lowerQuery = query.toLowerCase();
  let districts: District[] = [];
  
  if (cityName) {
    districts = getDistrictsByCity(cityName);
  } else {
    districts = getAllCities().flatMap(city => city.districts);
  }
  
  return districts.filter(district => 
    district.name.includes(query) || 
    district.pinyin.includes(lowerQuery)
  );
};

// 验证地理位置
export const validateLocation = (provinceName?: string, cityName?: string, districtName?: string): boolean => {
  if (provinceName) {
    const province = chinaLocationData.find(p => p.name === provinceName);
    if (!province) return false;
    
    if (cityName) {
      const city = province.cities.find(c => c.name === cityName);
      if (!city) return false;
      
      if (districtName) {
        return city.districts.some(d => d.name === districtName);
      }
    }
  }
  
  return true;
};

// 获取完整地址字符串
export const getFullAddress = (provinceName?: string, cityName?: string, districtName?: string): string => {
  const parts = [provinceName, cityName, districtName].filter(Boolean);
  return parts.join(' ');
};
