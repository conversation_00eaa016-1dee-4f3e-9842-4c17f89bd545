/**
 * 统一资金流动管理系统
 * 实现"冻结 → 状态确认 → 划扣"的核心机制
 */

import { prisma } from './prisma'

// 资金冻结目的枚举
export enum FundPurpose {
  PURCHASE = 'PURCHASE',           // 商品购买
  WITHDRAWAL = 'WITHDRAWAL',       // 提现
  CONSUMPTION = 'CONSUMPTION',     // 平台消费
  GUARANTEE = 'GUARANTEE'          // 担保
}

// 资金冻结状态枚举
export enum FreezeStatus {
  FROZEN = 'FROZEN',               // 已冻结，等待状态确认
  CONFIRMED = 'CONFIRMED',         // 状态已确认，可以划扣
  SETTLED = 'SETTLED',             // 已完成划扣
  CANCELLED = 'CANCELLED'          // 已取消，资金解冻
}

// 冻结资金接口参数
export interface FreezeFundsParams {
  userId: string
  amount: number
  purpose: FundPurpose
  relatedId?: string
  relatedType?: string
  metadata?: any
  notes?: string
}

// 划扣资金接口参数
export interface SettleFundsParams {
  freezeId: string
  toUserId?: string
  platformFeeRate?: number
  settledBy?: string
  notes?: string
}

/**
 * 获取用户可用余额
 * 可用余额 = 总担保金 - 已冻结金额
 */
export async function getAvailableBalance(userId: string): Promise<number> {
  try {
    // 获取用户总担保金
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { depositBalance: true }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 计算已冻结金额（状态为FROZEN或CONFIRMED的记录）
    const frozenAmount = await prisma.fundFreeze.aggregate({
      where: {
        userId,
        status: {
          in: [FreezeStatus.FROZEN, FreezeStatus.CONFIRMED]
        }
      },
      _sum: {
        amount: true
      }
    })

    const totalFrozen = frozenAmount._sum.amount || 0
    const availableBalance = user.depositBalance - totalFrozen

    return Math.max(0, availableBalance) // 确保不返回负数
  } catch (error) {
    console.error('获取可用余额失败:', error)
    throw error
  }
}

/**
 * 冻结资金
 * 检查用户余额是否充足，如果充足则创建冻结记录
 */
export async function freezeFunds(params: FreezeFundsParams): Promise<string> {
  const { userId, amount, purpose, relatedId, relatedType, metadata, notes } = params

  try {
    // 验证参数
    if (amount <= 0) {
      throw new Error('冻结金额必须大于0')
    }

    // 在事务中执行冻结操作
    const result = await prisma.$transaction(async (tx) => {
      // 检查用户可用余额
      const availableBalance = await getAvailableBalance(userId)
      
      if (availableBalance < amount) {
        throw new Error(`余额不足，可用余额: ${availableBalance} USDT，需要: ${amount} USDT`)
      }

      // 创建冻结记录
      const freezeRecord = await tx.fundFreeze.create({
        data: {
          userId,
          amount,
          purpose,
          relatedId,
          relatedType,
          status: FreezeStatus.FROZEN,
          metadata,
          notes
        }
      })

      return freezeRecord.id
    })

    console.log(`资金冻结成功: 用户 ${userId}, 金额 ${amount} USDT, 目的 ${purpose}`)
    return result

  } catch (error) {
    console.error('冻结资金失败:', error)
    throw error
  }
}

/**
 * 确认状态
 * 将冻结状态从FROZEN变更为CONFIRMED，表示可以进行划扣
 */
export async function confirmStatus(freezeId: string, confirmedBy?: string): Promise<void> {
  try {
    const freezeRecord = await prisma.fundFreeze.findUnique({
      where: { id: freezeId }
    })

    if (!freezeRecord) {
      throw new Error('冻结记录不存在')
    }

    if (freezeRecord.status !== FreezeStatus.FROZEN) {
      throw new Error(`无法确认状态，当前状态: ${freezeRecord.status}`)
    }

    await prisma.fundFreeze.update({
      where: { id: freezeId },
      data: {
        status: FreezeStatus.CONFIRMED,
        confirmedBy,
        confirmedAt: new Date()
      }
    })

    console.log(`状态确认成功: 冻结记录 ${freezeId}`)

  } catch (error) {
    console.error('确认状态失败:', error)
    throw error
  }
}

/**
 * 执行划扣
 * 将资金从冻结状态转为已划扣，并处理平台费用
 */
export async function settleFunds(params: SettleFundsParams): Promise<void> {
  const { freezeId, toUserId, platformFeeRate = 0, settledBy, notes } = params

  try {
    const result = await prisma.$transaction(async (tx) => {
      // 获取冻结记录
      const freezeRecord = await tx.fundFreeze.findUnique({
        where: { id: freezeId },
        include: { user: true }
      })

      if (!freezeRecord) {
        throw new Error('冻结记录不存在')
      }

      if (freezeRecord.status !== FreezeStatus.CONFIRMED) {
        throw new Error(`无法执行划扣，当前状态: ${freezeRecord.status}`)
      }

      // 计算平台费用和实际划扣金额
      const platformFee = freezeRecord.amount * platformFeeRate
      const actualAmount = freezeRecord.amount - platformFee

      // 更新冻结记录状态
      await tx.fundFreeze.update({
        where: { id: freezeId },
        data: {
          status: FreezeStatus.SETTLED,
          toUserId,
          platformFee,
          actualAmount,
          settledBy,
          settledAt: new Date(),
          notes: notes || freezeRecord.notes
        }
      })

      // 从用户担保金中扣除
      await tx.user.update({
        where: { id: freezeRecord.userId },
        data: {
          depositBalance: {
            decrement: freezeRecord.amount
          }
        }
      })

      // 如果有目标用户，增加其担保金余额
      if (toUserId && actualAmount > 0) {
        await tx.user.update({
          where: { id: toUserId },
          data: {
            depositBalance: {
              increment: actualAmount
            }
          }
        })
      }

      return { freezeRecord, platformFee, actualAmount }
    })

    console.log(`资金划扣成功: 冻结记录 ${freezeId}, 划扣金额 ${result.actualAmount} USDT, 平台费用 ${result.platformFee} USDT`)

  } catch (error) {
    console.error('执行划扣失败:', error)
    throw error
  }
}

/**
 * 取消冻结
 * 将冻结状态变更为CANCELLED，释放冻结的资金
 */
export async function cancelFreeze(freezeId: string, reason?: string, cancelledBy?: string): Promise<void> {
  try {
    const freezeRecord = await prisma.fundFreeze.findUnique({
      where: { id: freezeId }
    })

    if (!freezeRecord) {
      throw new Error('冻结记录不存在')
    }

    if (![FreezeStatus.FROZEN, FreezeStatus.CONFIRMED].includes(freezeRecord.status as FreezeStatus)) {
      throw new Error(`无法取消冻结，当前状态: ${freezeRecord.status}`)
    }

    await prisma.fundFreeze.update({
      where: { id: freezeId },
      data: {
        status: FreezeStatus.CANCELLED,
        cancelledBy,
        cancelledAt: new Date(),
        notes: reason ? `${freezeRecord.notes || ''}\n取消原因: ${reason}` : freezeRecord.notes
      }
    })

    console.log(`冻结取消成功: 冻结记录 ${freezeId}, 原因: ${reason || '未提供'}`)

  } catch (error) {
    console.error('取消冻结失败:', error)
    throw error
  }
}

/**
 * 查询用户的冻结记录
 */
export async function getUserFreezeRecords(
  userId: string, 
  status?: FreezeStatus, 
  purpose?: FundPurpose,
  limit: number = 50
) {
  try {
    const where: any = { userId }
    
    if (status) {
      where.status = status
    }
    
    if (purpose) {
      where.purpose = purpose
    }

    const records = await prisma.fundFreeze.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        toUser: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return records
  } catch (error) {
    console.error('查询冻结记录失败:', error)
    throw error
  }
}
