/**
 * 增强的商品分类和成色系统常量定义
 * 包含主分类、细分类别和完整的选项配置
 */

// 商品主分类枚举
export enum ProductCategory {
  ELECTRONICS = 'ELECTRONICS',
  FASHION = 'FASHION',
  HOME_GARDEN = 'HOME_GARDEN',
  SPORTS_OUTDOOR = 'SPORTS_OUTDOOR',
  BOOKS_MEDIA = 'BOOKS_MEDIA',
  BEAUTY_HEALTH = 'BEAUTY_HEALTH',
  AUTOMOTIVE = 'AUTOMOTIVE',
  TOYS_HOBBIES = 'TOYS_HOBBIES',
  FOOD_BEVERAGE = 'FOOD_BEVERAGE',
  SERVICES = 'SERVICES',
  VIRTUAL_DIGITAL = 'VIRTUAL_DIGITAL',
  OTHER = 'OTHER'
}

// 商品细分类别枚举
export enum ProductSubcategory {
  // 电子产品细分
  MOBILE_PHONE = 'MOBILE_PHONE',
  COMPUTER_LAPTOP = 'COMPUTER_LAPTOP',
  TABLET_EREADER = 'TABLET_EREADER',
  AUDIO_VIDEO = 'AUDIO_VIDEO',
  CAMERA_PHOTO = 'CAMERA_PHOTO',
  GAMING_CONSOLE = 'GAMING_CONSOLE',
  SMART_HOME = 'SMART_HOME',
  ELECTRONICS_ACCESSORIES = 'ELECTRONICS_ACCESSORIES',
  
  // 时尚服饰细分
  MENS_CLOTHING = 'MENS_CLOTHING',
  WOMENS_CLOTHING = 'WOMENS_CLOTHING',
  SHOES = 'SHOES',
  BAGS_LUGGAGE = 'BAGS_LUGGAGE',
  JEWELRY_WATCHES = 'JEWELRY_WATCHES',
  FASHION_ACCESSORIES = 'FASHION_ACCESSORIES',
  
  // 家居园艺细分
  FURNITURE = 'FURNITURE',
  HOME_APPLIANCES = 'HOME_APPLIANCES',
  KITCHEN_DINING = 'KITCHEN_DINING',
  BEDDING_BATH = 'BEDDING_BATH',
  HOME_DECOR = 'HOME_DECOR',
  GARDEN_OUTDOOR = 'GARDEN_OUTDOOR',
  TOOLS_HARDWARE = 'TOOLS_HARDWARE',
  
  // 运动户外细分
  FITNESS_EQUIPMENT = 'FITNESS_EQUIPMENT',
  OUTDOOR_RECREATION = 'OUTDOOR_RECREATION',
  SPORTS_EQUIPMENT = 'SPORTS_EQUIPMENT',
  ATHLETIC_CLOTHING = 'ATHLETIC_CLOTHING',
  
  // 图书媒体细分
  BOOKS = 'BOOKS',
  MAGAZINES = 'MAGAZINES',
  MUSIC_MOVIES = 'MUSIC_MOVIES',
  EDUCATIONAL = 'EDUCATIONAL',
  STATIONERY = 'STATIONERY',
  
  // 美容健康细分
  SKINCARE = 'SKINCARE',
  MAKEUP = 'MAKEUP',
  PERSONAL_CARE = 'PERSONAL_CARE',
  HEALTH_WELLNESS = 'HEALTH_WELLNESS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  
  // 汽车用品细分
  CAR_PARTS = 'CAR_PARTS',
  CAR_ACCESSORIES = 'CAR_ACCESSORIES',
  CAR_CARE = 'CAR_CARE',
  MOTORCYCLE = 'MOTORCYCLE',
  
  // 玩具爱好细分
  TOYS_GAMES = 'TOYS_GAMES',
  BABY_KIDS = 'BABY_KIDS',
  COLLECTIBLES = 'COLLECTIBLES',
  CRAFTS_HOBBIES = 'CRAFTS_HOBBIES',
  
  // 食品饮料细分
  FRESH_FOOD = 'FRESH_FOOD',
  PACKAGED_FOOD = 'PACKAGED_FOOD',
  BEVERAGES = 'BEVERAGES',
  SPECIALTY_FOOD = 'SPECIALTY_FOOD',
  
  // 服务细分
  PROFESSIONAL_SERVICES = 'PROFESSIONAL_SERVICES',
  PERSONAL_SERVICES = 'PERSONAL_SERVICES',
  EDUCATIONAL_SERVICES = 'EDUCATIONAL_SERVICES',
  REPAIR_MAINTENANCE = 'REPAIR_MAINTENANCE',
  
  // 虚拟数字细分
  SOFTWARE = 'SOFTWARE',
  DIGITAL_CONTENT = 'DIGITAL_CONTENT',
  GAMING_VIRTUAL = 'GAMING_VIRTUAL',
  ONLINE_SERVICES = 'ONLINE_SERVICES',
  
  // 其他
  GENERAL = 'GENERAL'
}

// 商品成色枚举
export enum ProductCondition {
  NEW = 'NEW',
  LIKE_NEW = 'LIKE_NEW',
  GOOD = 'GOOD',
  FAIR = 'FAIR',
  POOR = 'POOR',
  NOT_APPLICABLE = 'NOT_APPLICABLE'
}

// 细分类别选项配置
export interface SubcategoryOption {
  value: ProductSubcategory
  label: string
  description: string
  icon: string
  keywords: string[]
}

// 商品分类选项配置
export interface CategoryOption {
  value: ProductCategory
  label: string
  description: string
  icon: string
  color: string
  keywords: string[]
  subcategories: SubcategoryOption[]
}

// 商品成色选项配置
export interface ConditionOption {
  value: ProductCondition
  label: string
  description: string
  icon: string
  color: string
  applicableCategories?: ProductCategory[]
}

// 商品分类选项列表
export const categoryOptions: CategoryOption[] = [
  {
    value: ProductCategory.ELECTRONICS,
    label: '电子产品',
    description: '手机、电脑、数码设备等',
    icon: '📱',
    color: 'bg-blue-100 text-blue-700',
    keywords: ['手机', '电脑', '数码', '电子', '平板', '耳机', '音响', '相机'],
    subcategories: [
      { value: ProductSubcategory.MOBILE_PHONE, label: '手机通讯', description: '智能手机、功能机、配件', icon: '📱', keywords: ['手机', '通讯', '智能机'] },
      { value: ProductSubcategory.COMPUTER_LAPTOP, label: '电脑办公', description: '台式机、笔记本、配件', icon: '💻', keywords: ['电脑', '笔记本', '台式机'] },
      { value: ProductSubcategory.TABLET_EREADER, label: '平板电子书', description: '平板电脑、电子书阅读器', icon: '📱', keywords: ['平板', '电子书', 'iPad'] },
      { value: ProductSubcategory.AUDIO_VIDEO, label: '影音设备', description: '耳机、音响、播放器', icon: '🎧', keywords: ['耳机', '音响', '播放器'] },
      { value: ProductSubcategory.CAMERA_PHOTO, label: '摄影摄像', description: '相机、摄像机、镜头', icon: '📷', keywords: ['相机', '摄像', '镜头'] },
      { value: ProductSubcategory.GAMING_CONSOLE, label: '游戏设备', description: '游戏机、掌机、配件', icon: '🎮', keywords: ['游戏机', '掌机', 'PS5'] },
      { value: ProductSubcategory.SMART_HOME, label: '智能家居', description: '智能音箱、智能家电', icon: '🏠', keywords: ['智能', '家居', '物联网'] },
      { value: ProductSubcategory.ELECTRONICS_ACCESSORIES, label: '数码配件', description: '充电器、数据线、保护套', icon: '🔌', keywords: ['配件', '充电器', '数据线'] }
    ]
  },
  {
    value: ProductCategory.FASHION,
    label: '时尚服饰',
    description: '服装、鞋子、包包、配饰等',
    icon: '👕',
    color: 'bg-pink-100 text-pink-700',
    keywords: ['衣服', '鞋子', '包包', '配饰', '首饰', '帽子', '围巾', '手表'],
    subcategories: [
      { value: ProductSubcategory.MENS_CLOTHING, label: '男装', description: '男士服装、内衣、配饰', icon: '👔', keywords: ['男装', '西装', '衬衫'] },
      { value: ProductSubcategory.WOMENS_CLOTHING, label: '女装', description: '女士服装、内衣、配饰', icon: '👗', keywords: ['女装', '连衣裙', '上衣'] },
      { value: ProductSubcategory.SHOES, label: '鞋靴', description: '男女鞋子、运动鞋、靴子', icon: '👟', keywords: ['鞋子', '运动鞋', '靴子'] },
      { value: ProductSubcategory.BAGS_LUGGAGE, label: '箱包', description: '手提包、背包、行李箱', icon: '👜', keywords: ['包包', '背包', '行李箱'] },
      { value: ProductSubcategory.JEWELRY_WATCHES, label: '珠宝手表', description: '首饰、手表、饰品', icon: '⌚', keywords: ['手表', '首饰', '项链'] },
      { value: ProductSubcategory.FASHION_ACCESSORIES, label: '时尚配饰', description: '帽子、围巾、眼镜等', icon: '🧢', keywords: ['帽子', '围巾', '眼镜'] }
    ]
  },
  {
    value: ProductCategory.HOME_GARDEN,
    label: '家居园艺',
    description: '家具、家电、装饰、园艺用品等',
    icon: '🏠',
    color: 'bg-green-100 text-green-700',
    keywords: ['家具', '家电', '装饰', '厨具', '床上用品', '收纳', '灯具'],
    subcategories: [
      { value: ProductSubcategory.FURNITURE, label: '家具', description: '沙发、床、桌椅、柜子', icon: '🛋️', keywords: ['家具', '沙发', '床', '桌椅'] },
      { value: ProductSubcategory.HOME_APPLIANCES, label: '家用电器', description: '冰箱、洗衣机、空调等', icon: '🔌', keywords: ['家电', '冰箱', '洗衣机'] },
      { value: ProductSubcategory.KITCHEN_DINING, label: '厨房餐具', description: '厨具、餐具、小家电', icon: '🍽️', keywords: ['厨具', '餐具', '锅具'] },
      { value: ProductSubcategory.BEDDING_BATH, label: '床上用品', description: '床单、被子、枕头、毛巾', icon: '🛏️', keywords: ['床单', '被子', '枕头'] },
      { value: ProductSubcategory.HOME_DECOR, label: '家居装饰', description: '装饰品、灯具、窗帘', icon: '🕯️', keywords: ['装饰', '灯具', '窗帘'] },
      { value: ProductSubcategory.GARDEN_OUTDOOR, label: '园艺户外', description: '花卉、园艺工具、户外用品', icon: '🌱', keywords: ['园艺', '花卉', '户外'] },
      { value: ProductSubcategory.TOOLS_HARDWARE, label: '五金工具', description: '工具、五金、建材', icon: '🔧', keywords: ['工具', '五金', '建材'] }
    ]
  },
  {
    value: ProductCategory.SPORTS_OUTDOOR,
    label: '运动户外',
    description: '运动器材、户外用品、健身设备等',
    icon: '⚽',
    color: 'bg-orange-100 text-orange-700',
    keywords: ['运动', '健身', '户外', '球类', '器材', '装备', '鞋服'],
    subcategories: [
      { value: ProductSubcategory.FITNESS_EQUIPMENT, label: '健身器材', description: '跑步机、哑铃、健身器械', icon: '🏋️', keywords: ['健身', '器材', '跑步机'] },
      { value: ProductSubcategory.OUTDOOR_RECREATION, label: '户外休闲', description: '帐篷、登山、野营用品', icon: '🏕️', keywords: ['户外', '帐篷', '登山'] },
      { value: ProductSubcategory.SPORTS_EQUIPMENT, label: '运动器材', description: '球类、球拍、运动用品', icon: '🏀', keywords: ['球类', '球拍', '运动'] },
      { value: ProductSubcategory.ATHLETIC_CLOTHING, label: '运动服饰', description: '运动服、运动鞋、配件', icon: '👟', keywords: ['运动服', '运动鞋', '健身服'] }
    ]
  },
  {
    value: ProductCategory.BOOKS_MEDIA,
    label: '图书媒体',
    description: '书籍、文具、音像制品等',
    icon: '📚',
    color: 'bg-yellow-100 text-yellow-700',
    keywords: ['书籍', '文具', '办公', '笔记本', '教材', '小说', '工具书'],
    subcategories: [
      { value: ProductSubcategory.BOOKS, label: '图书', description: '小说、教材、工具书', icon: '📖', keywords: ['书籍', '小说', '教材'] },
      { value: ProductSubcategory.MAGAZINES, label: '杂志期刊', description: '杂志、期刊、报纸', icon: '📰', keywords: ['杂志', '期刊', '报纸'] },
      { value: ProductSubcategory.MUSIC_MOVIES, label: '音像制品', description: 'CD、DVD、蓝光碟', icon: '💿', keywords: ['CD', 'DVD', '音乐'] },
      { value: ProductSubcategory.EDUCATIONAL, label: '教育用品', description: '教学用具、学习资料', icon: '🎓', keywords: ['教育', '学习', '教学'] },
      { value: ProductSubcategory.STATIONERY, label: '文具办公', description: '笔、纸、办公用品', icon: '✏️', keywords: ['文具', '办公', '笔'] }
    ]
  },
  {
    value: ProductCategory.BEAUTY_HEALTH,
    label: '美容健康',
    description: '化妆品、护肤品、健康用品等',
    icon: '💄',
    color: 'bg-purple-100 text-purple-700',
    keywords: ['化妆品', '护肤', '个护', '香水', '美容', '洗护', '彩妆'],
    subcategories: [
      { value: ProductSubcategory.SKINCARE, label: '护肤用品', description: '洁面、爽肤水、面霜', icon: '🧴', keywords: ['护肤', '洁面', '面霜'] },
      { value: ProductSubcategory.MAKEUP, label: '彩妆用品', description: '口红、粉底、眼影', icon: '💄', keywords: ['彩妆', '口红', '粉底'] },
      { value: ProductSubcategory.PERSONAL_CARE, label: '个人护理', description: '洗发水、沐浴露、牙膏', icon: '🧼', keywords: ['洗发', '沐浴', '牙膏'] },
      { value: ProductSubcategory.HEALTH_WELLNESS, label: '健康保健', description: '保健品、营养品、器械', icon: '💊', keywords: ['保健', '营养', '健康'] },
      { value: ProductSubcategory.MEDICAL_SUPPLIES, label: '医疗用品', description: '医疗器械、药品、用具', icon: '🩺', keywords: ['医疗', '器械', '药品'] }
    ]
  },
  {
    value: ProductCategory.AUTOMOTIVE,
    label: '汽车用品',
    description: '汽车配件、用品、装饰等',
    icon: '🚗',
    color: 'bg-gray-100 text-gray-700',
    keywords: ['汽车', '配件', '装饰', '保养', '改装', '工具', '用品'],
    subcategories: [
      { value: ProductSubcategory.CAR_PARTS, label: '汽车配件', description: '发动机、轮胎、刹车片', icon: '⚙️', keywords: ['配件', '发动机', '轮胎'] },
      { value: ProductSubcategory.CAR_ACCESSORIES, label: '汽车用品', description: '座垫、脚垫、装饰品', icon: '🚗', keywords: ['用品', '座垫', '装饰'] },
      { value: ProductSubcategory.CAR_CARE, label: '汽车保养', description: '机油、清洁剂、保养品', icon: '🧽', keywords: ['保养', '机油', '清洁'] },
      { value: ProductSubcategory.MOTORCYCLE, label: '摩托车用品', description: '摩托车及配件用品', icon: '🏍️', keywords: ['摩托车', '头盔', '护具'] }
    ]
  },
  {
    value: ProductCategory.TOYS_HOBBIES,
    label: '玩具爱好',
    description: '玩具、母婴用品、收藏品等',
    icon: '🧸',
    color: 'bg-red-100 text-red-700',
    keywords: ['玩具', '母婴', '儿童', '婴儿', '益智', '模型', '游戏'],
    subcategories: [
      { value: ProductSubcategory.TOYS_GAMES, label: '玩具游戏', description: '益智玩具、桌游、模型', icon: '🎲', keywords: ['玩具', '桌游', '模型'] },
      { value: ProductSubcategory.BABY_KIDS, label: '母婴用品', description: '婴儿用品、儿童用品', icon: '👶', keywords: ['母婴', '婴儿', '儿童'] },
      { value: ProductSubcategory.COLLECTIBLES, label: '收藏品', description: '手办、邮票、古董', icon: '🏺', keywords: ['收藏', '手办', '古董'] },
      { value: ProductSubcategory.CRAFTS_HOBBIES, label: '手工爱好', description: '手工材料、DIY用品', icon: '🎨', keywords: ['手工', 'DIY', '爱好'] }
    ]
  },
  {
    value: ProductCategory.FOOD_BEVERAGE,
    label: '食品饮料',
    description: '食品、饮料、特产等',
    icon: '🍎',
    color: 'bg-yellow-100 text-yellow-700',
    keywords: ['食品', '饮料', '零食', '特产', '茶叶', '咖啡'],
    subcategories: [
      { value: ProductSubcategory.FRESH_FOOD, label: '生鲜食品', description: '水果、蔬菜、肉类', icon: '🥬', keywords: ['生鲜', '水果', '蔬菜'] },
      { value: ProductSubcategory.PACKAGED_FOOD, label: '包装食品', description: '零食、罐头、调料', icon: '🥫', keywords: ['零食', '罐头', '调料'] },
      { value: ProductSubcategory.BEVERAGES, label: '饮料酒水', description: '饮料、茶叶、咖啡、酒类', icon: '🥤', keywords: ['饮料', '茶叶', '咖啡'] },
      { value: ProductSubcategory.SPECIALTY_FOOD, label: '特色美食', description: '地方特产、进口食品', icon: '🍯', keywords: ['特产', '进口', '美食'] }
    ]
  },
  {
    value: ProductCategory.SERVICES,
    label: '服务',
    description: '各类服务项目',
    icon: '🛠️',
    color: 'bg-indigo-100 text-indigo-700',
    keywords: ['服务', '维修', '教育', '咨询', '设计'],
    subcategories: [
      { value: ProductSubcategory.PROFESSIONAL_SERVICES, label: '专业服务', description: '法律、财务、咨询', icon: '💼', keywords: ['专业', '法律', '咨询'] },
      { value: ProductSubcategory.PERSONAL_SERVICES, label: '生活服务', description: '家政、美容、摄影', icon: '💇', keywords: ['生活', '家政', '美容'] },
      { value: ProductSubcategory.EDUCATIONAL_SERVICES, label: '教育培训', description: '培训、辅导、课程', icon: '📚', keywords: ['教育', '培训', '课程'] },
      { value: ProductSubcategory.REPAIR_MAINTENANCE, label: '维修保养', description: '设备维修、保养服务', icon: '🔧', keywords: ['维修', '保养', '服务'] }
    ]
  },
  {
    value: ProductCategory.VIRTUAL_DIGITAL,
    label: '虚拟数字',
    description: '软件、数字内容、虚拟物品等',
    icon: '💾',
    color: 'bg-indigo-100 text-indigo-700',
    keywords: ['游戏', '软件', '数字', '虚拟', '道具', '账号', '会员'],
    subcategories: [
      { value: ProductSubcategory.SOFTWARE, label: '软件应用', description: '软件、应用、系统', icon: '💻', keywords: ['软件', '应用', '系统'] },
      { value: ProductSubcategory.DIGITAL_CONTENT, label: '数字内容', description: '电子书、音乐、视频', icon: '📱', keywords: ['电子书', '音乐', '视频'] },
      { value: ProductSubcategory.GAMING_VIRTUAL, label: '游戏虚拟', description: '游戏道具、账号、点卡', icon: '🎮', keywords: ['游戏', '道具', '账号'] },
      { value: ProductSubcategory.ONLINE_SERVICES, label: '在线服务', description: '会员、订阅、云服务', icon: '☁️', keywords: ['会员', '订阅', '云服务'] }
    ]
  },
  {
    value: ProductCategory.OTHER,
    label: '其他',
    description: '其他未分类商品',
    icon: '📦',
    color: 'bg-gray-100 text-gray-700',
    keywords: ['其他', '杂项', '未分类'],
    subcategories: [
      { value: ProductSubcategory.GENERAL, label: '一般商品', description: '其他未分类商品', icon: '📦', keywords: ['其他', '杂项', '一般'] }
    ]
  }
];

// 商品成色选项列表
export const conditionOptions: ConditionOption[] = [
  {
    value: ProductCondition.NEW,
    label: '全新',
    description: '未使用过，包装完整',
    icon: '✨',
    color: 'bg-green-100 text-green-700'
  },
  {
    value: ProductCondition.LIKE_NEW,
    label: '几乎全新',
    description: '使用极少，无明显磨损',
    icon: '🌟',
    color: 'bg-blue-100 text-blue-700'
  },
  {
    value: ProductCondition.GOOD,
    label: '轻微使用痕迹',
    description: '有轻微使用痕迹，功能完好',
    icon: '👍',
    color: 'bg-yellow-100 text-yellow-700'
  },
  {
    value: ProductCondition.FAIR,
    label: '明显使用痕迹',
    description: '有明显使用痕迹，功能正常',
    icon: '👌',
    color: 'bg-orange-100 text-orange-700'
  },
  {
    value: ProductCondition.POOR,
    label: '需要维修',
    description: '有损坏或功能问题',
    icon: '🔧',
    color: 'bg-red-100 text-red-700'
  },
  {
    value: ProductCondition.NOT_APPLICABLE,
    label: '无成色类型',
    description: '不适用成色描述的商品',
    icon: '➖',
    color: 'bg-gray-100 text-gray-700',
    applicableCategories: [ProductCategory.VIRTUAL_DIGITAL, ProductCategory.SERVICES, ProductCategory.FOOD_BEVERAGE]
  }
];

// 获取分类选项
export const getCategoryOptions = (includeAll = false) => {
  const options = [...categoryOptions];
  if (includeAll) {
    options.unshift({
      value: 'ALL' as ProductCategory,
      label: '全部分类',
      description: '显示所有分类的商品',
      icon: '🔍',
      color: 'bg-gray-100 text-gray-700',
      keywords: [],
      subcategories: []
    });
  }
  return options;
};

// 获取细分类别选项
export const getSubcategoryOptions = (category?: ProductCategory) => {
  if (!category) {
    return categoryOptions.flatMap(cat => cat.subcategories);
  }
  
  const categoryOption = categoryOptions.find(opt => opt.value === category);
  return categoryOption?.subcategories || [];
};

// 获取成色选项
export const getConditionOptions = (category?: ProductCategory, includeAll = false) => {
  let options = [...conditionOptions];
  
  // 根据分类过滤适用的成色选项
  if (category) {
    options = options.filter(option => 
      !option.applicableCategories || 
      option.applicableCategories.includes(category)
    );
  }
  
  if (includeAll) {
    options.unshift({
      value: 'ALL' as ProductCondition,
      label: '全部成色',
      description: '显示所有成色的商品',
      icon: '🔍',
      color: 'bg-gray-100 text-gray-700'
    });
  }
  
  return options;
};

// 根据关键词推荐分类和细分类别
export const suggestCategoryByKeywords = (title: string, description?: string): { category: ProductCategory, subcategory?: ProductSubcategory } => {
  const text = `${title} ${description || ''}`.toLowerCase();
  
  for (const categoryOption of categoryOptions) {
    // 检查主分类关键词
    if (categoryOption.keywords.some(keyword => text.includes(keyword))) {
      // 检查细分类别关键词
      for (const subcategory of categoryOption.subcategories) {
        if (subcategory.keywords.some(keyword => text.includes(keyword))) {
          return { category: categoryOption.value, subcategory: subcategory.value };
        }
      }
      return { category: categoryOption.value };
    }
  }
  
  return { category: ProductCategory.OTHER, subcategory: ProductSubcategory.GENERAL };
};

// 获取分类显示文本
export const getCategoryLabel = (category: string): string => {
  const option = categoryOptions.find(opt => opt.value === category);
  return option?.label || category;
};

// 获取细分类别显示文本
export const getSubcategoryLabel = (subcategory: string): string => {
  const allSubcategories = categoryOptions.flatMap(cat => cat.subcategories);
  const option = allSubcategories.find(opt => opt.value === subcategory);
  return option?.label || subcategory;
};

// 获取成色显示文本
export const getConditionLabel = (condition: string): string => {
  const option = conditionOptions.find(opt => opt.value === condition);
  return option?.label || condition;
};

// 获取分类图标
export const getCategoryIcon = (category: string): string => {
  const option = categoryOptions.find(opt => opt.value === category);
  return option?.icon || '📦';
};

// 获取细分类别图标
export const getSubcategoryIcon = (subcategory: string): string => {
  const allSubcategories = categoryOptions.flatMap(cat => cat.subcategories);
  const option = allSubcategories.find(opt => opt.value === subcategory);
  return option?.icon || '📦';
};

// 获取成色图标
export const getConditionIcon = (condition: string): string => {
  const option = conditionOptions.find(opt => opt.value === condition);
  return option?.icon || '❓';
};

// 获取分类颜色样式
export const getCategoryColor = (category: string): string => {
  const option = categoryOptions.find(opt => opt.value === category);
  return option?.color || 'bg-gray-100 text-gray-700';
};

// 获取成色颜色样式
export const getConditionColor = (condition: string): string => {
  const option = conditionOptions.find(opt => opt.value === condition);
  return option?.color || 'bg-gray-100 text-gray-700';
};
