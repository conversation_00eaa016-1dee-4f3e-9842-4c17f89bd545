/**
 * 提现手续费改革系统
 * 实现阶梯式手续费结构，鼓励集中提现
 */

import { prisma } from './prisma'
import { getUserBenefits } from './credit-level'

// 阶梯式手续费配置
export const WITHDRAWAL_FEE_TIERS = [
  {
    minAmount: 0,
    maxAmount: 100,
    feeType: 'FIXED' as const,
    feeValue: 0.5, // 固定0.5 USDT
    description: '小额提现固定费用'
  },
  {
    minAmount: 101,
    maxAmount: 500,
    feeType: 'PERCENTAGE' as const,
    feeValue: 0.005, // 0.5%
    description: '中额提现按比例收费'
  },
  {
    minAmount: 501,
    maxAmount: 2000,
    feeType: 'PERCENTAGE' as const,
    feeValue: 0.003, // 0.3%
    description: '大额提现优惠费率'
  },
  {
    minAmount: 2001,
    maxAmount: Infinity,
    feeType: 'FREE' as const,
    feeValue: 0, // 免手续费
    description: '超大额提现免手续费'
  }
]

// 批量提现时间配置
export const BATCH_WITHDRAWAL_SCHEDULE = {
  times: ['12:00', '20:00'], // 每日12点和20点处理
  timezone: 'Asia/Shanghai',
  batchDiscount: 0.1 // 批量处理额外10%折扣
}

/**
 * 计算阶梯式提现手续费
 */
export function calculateTieredWithdrawalFee(amount: number): {
  tier: typeof WITHDRAWAL_FEE_TIERS[0]
  baseFee: number
  description: string
} {
  // 找到对应的费率档位
  const tier = WITHDRAWAL_FEE_TIERS.find(
    t => amount >= t.minAmount && amount <= t.maxAmount
  ) || WITHDRAWAL_FEE_TIERS[WITHDRAWAL_FEE_TIERS.length - 1]

  let baseFee = 0
  
  switch (tier.feeType) {
    case 'FIXED':
      baseFee = tier.feeValue
      break
    case 'PERCENTAGE':
      baseFee = amount * tier.feeValue
      break
    case 'FREE':
      baseFee = 0
      break
  }

  return {
    tier,
    baseFee,
    description: tier.description
  }
}

/**
 * 计算综合提现费用（包含信用等级折扣）
 */
export async function calculateComprehensiveWithdrawalFee(
  userId: string,
  amount: number,
  isBatchWithdrawal: boolean = false
): Promise<{
  originalFee: number
  creditDiscount: number
  batchDiscount: number
  finalFee: number
  savings: number
  savingsRate: number
  tier: string
  breakdown: {
    baseFee: number
    creditDiscountAmount: number
    batchDiscountAmount: number
  }
}> {
  try {
    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditLevel: true }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 计算基础阶梯费用
    const { tier, baseFee } = calculateTieredWithdrawalFee(amount)
    
    // 获取信用等级折扣
    const userBenefits = getUserBenefits(user.creditLevel as any)
    const creditDiscount = userBenefits.withdrawalFeeDiscount
    
    // 计算各种折扣
    const creditDiscountAmount = baseFee * creditDiscount
    const batchDiscountAmount = isBatchWithdrawal ? baseFee * BATCH_WITHDRAWAL_SCHEDULE.batchDiscount : 0
    
    // 计算最终费用（折扣不叠加，取最大折扣）
    const maxDiscountAmount = Math.max(creditDiscountAmount, batchDiscountAmount)
    const finalFee = Math.max(0, baseFee - maxDiscountAmount)
    
    const savings = baseFee - finalFee
    const savingsRate = baseFee > 0 ? (savings / baseFee) * 100 : 0

    return {
      originalFee: baseFee,
      creditDiscount,
      batchDiscount: isBatchWithdrawal ? BATCH_WITHDRAWAL_SCHEDULE.batchDiscount : 0,
      finalFee,
      savings,
      savingsRate,
      tier: tier.description,
      breakdown: {
        baseFee,
        creditDiscountAmount,
        batchDiscountAmount
      }
    }

  } catch (error) {
    console.error('计算综合提现费用失败:', error)
    throw error
  }
}

/**
 * 提现费用计算器（用户工具）
 */
export async function getWithdrawalFeeCalculator(userId: string): Promise<{
  tiers: Array<{
    range: string
    feeStructure: string
    example: {
      amount: number
      fee: number
      actualReceived: number
    }
  }>
  userLevel: string
  userDiscount: number
  recommendations: string[]
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditLevel: true, depositBalance: true }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const userBenefits = getUserBenefits(user.creditLevel as any)
    
    // 生成各档位示例
    const tiers = WITHDRAWAL_FEE_TIERS.map(tier => {
      const exampleAmount = tier.minAmount === 0 ? 50 : 
                           tier.maxAmount === Infinity ? 3000 : 
                           Math.floor((tier.minAmount + Math.min(tier.maxAmount, 2000)) / 2)
      
      const { baseFee } = calculateTieredWithdrawalFee(exampleAmount)
      const discountedFee = baseFee * (1 - userBenefits.withdrawalFeeDiscount)
      
      let feeStructure = ''
      switch (tier.feeType) {
        case 'FIXED':
          feeStructure = `固定 ${tier.feeValue} USDT`
          break
        case 'PERCENTAGE':
          feeStructure = `${(tier.feeValue * 100).toFixed(1)}% 手续费`
          break
        case 'FREE':
          feeStructure = '免手续费'
          break
      }

      return {
        range: tier.maxAmount === Infinity ? 
               `${tier.minAmount}+ USDT` : 
               `${tier.minAmount}-${tier.maxAmount} USDT`,
        feeStructure,
        example: {
          amount: exampleAmount,
          fee: Math.round(discountedFee * 10000) / 10000,
          actualReceived: exampleAmount - discountedFee
        }
      }
    })

    // 生成个性化建议
    const recommendations: string[] = []
    
    if (user.depositBalance < 100) {
      recommendations.push('建议积累到100 USDT以上再提现，可享受更优惠的费率')
    }
    
    if (user.depositBalance >= 500 && user.depositBalance < 2000) {
      recommendations.push('您的余额已达到优惠档位，当前提现费率为0.3%')
    }
    
    if (user.depositBalance >= 2000) {
      recommendations.push('🎉 您的余额已达到免手续费档位！')
    }
    
    if (userBenefits.withdrawalFeeDiscount > 0) {
      recommendations.push(`您的${user.creditLevel}等级可享受${(userBenefits.withdrawalFeeDiscount * 100).toFixed(0)}%手续费折扣`)
    }
    
    recommendations.push('选择批量提现时间（12:00或20:00）可享受额外10%折扣')

    return {
      tiers,
      userLevel: user.creditLevel,
      userDiscount: userBenefits.withdrawalFeeDiscount,
      recommendations
    }

  } catch (error) {
    console.error('获取提现费用计算器失败:', error)
    throw error
  }
}

/**
 * 获取最优提现建议
 */
export async function getOptimalWithdrawalSuggestion(
  userId: string,
  currentBalance: number
): Promise<{
  currentTier: string
  currentFee: number
  suggestions: Array<{
    amount: number
    tier: string
    fee: number
    savings: number
    reason: string
  }>
}> {
  try {
    const currentFeeCalc = await calculateComprehensiveWithdrawalFee(userId, currentBalance)
    
    const suggestions: Array<{
      amount: number
      tier: string
      fee: number
      savings: number
      reason: string
    }> = []
    
    // 如果当前在小额档位，建议积累到更高档位
    if (currentBalance <= 100) {
      const nextTierAmount = 150
      const nextTierFee = await calculateComprehensiveWithdrawalFee(userId, nextTierAmount)
      
      suggestions.push({
        amount: nextTierAmount,
        tier: nextTierFee.tier,
        fee: nextTierFee.finalFee,
        savings: currentFeeCalc.finalFee - nextTierFee.finalFee,
        reason: '积累到中额档位可享受按比例收费'
      })
    }
    
    // 如果接近免费档位，建议积累到2000+
    if (currentBalance >= 1500 && currentBalance < 2000) {
      const freeTierAmount = 2100
      const freeTierFee = await calculateComprehensiveWithdrawalFee(userId, freeTierAmount)
      
      suggestions.push({
        amount: freeTierAmount,
        tier: freeTierFee.tier,
        fee: freeTierFee.finalFee,
        savings: currentFeeCalc.finalFee,
        reason: '积累到超大额档位可享受免手续费'
      })
    }
    
    // 批量提现建议
    const batchFee = await calculateComprehensiveWithdrawalFee(userId, currentBalance, true)
    if (batchFee.finalFee < currentFeeCalc.finalFee) {
      suggestions.push({
        amount: currentBalance,
        tier: '批量提现',
        fee: batchFee.finalFee,
        savings: currentFeeCalc.finalFee - batchFee.finalFee,
        reason: '选择批量提现时间可享受额外折扣'
      })
    }

    return {
      currentTier: currentFeeCalc.tier,
      currentFee: currentFeeCalc.finalFee,
      suggestions
    }

  } catch (error) {
    console.error('获取最优提现建议失败:', error)
    throw error
  }
}

/**
 * 统计提现费用改革效果
 */
export async function getReformEffectStats(days: number = 30): Promise<{
  totalWithdrawals: number
  totalFeesSaved: number
  avgWithdrawalAmount: number
  tierDistribution: Record<string, number>
  userSatisfaction: {
    freeWithdrawals: number
    discountedWithdrawals: number
  }
}> {
  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const withdrawals = await prisma.withdrawal.findMany({
      where: {
        createdAt: {
          gte: startDate
        },
        status: 'COMPLETED'
      }
    })

    const totalWithdrawals = withdrawals.length
    const totalAmount = withdrawals.reduce((sum, w) => sum + w.amount, 0)
    const avgWithdrawalAmount = totalWithdrawals > 0 ? totalAmount / totalWithdrawals : 0

    // 计算如果使用旧费率的总费用
    const oldTotalFees = withdrawals.reduce((sum, w) => sum + (w.amount * 0.01), 0) // 假设旧费率1%
    const newTotalFees = withdrawals.reduce((sum, w) => sum + w.withdrawalFee, 0)
    const totalFeesSaved = oldTotalFees - newTotalFees

    // 档位分布统计
    const tierDistribution: Record<string, number> = {}
    withdrawals.forEach(w => {
      const { tier } = calculateTieredWithdrawalFee(w.amount)
      tierDistribution[tier.description] = (tierDistribution[tier.description] || 0) + 1
    })

    // 用户满意度指标
    const freeWithdrawals = withdrawals.filter(w => w.withdrawalFee === 0).length
    const discountedWithdrawals = withdrawals.filter(w => w.withdrawalFee < w.amount * 0.01).length

    return {
      totalWithdrawals,
      totalFeesSaved,
      avgWithdrawalAmount,
      tierDistribution,
      userSatisfaction: {
        freeWithdrawals,
        discountedWithdrawals
      }
    }

  } catch (error) {
    console.error('获取改革效果统计失败:', error)
    throw error
  }
}
