import Redis from 'ioredis'

// 内存缓存回退
const memoryCache = new Map<string, { value: any; expiry: number }>()

// Redis客户端配置（带错误处理）
let redis: Redis | null = null

// 只在配置了Redis时才创建连接
if (process.env.REDIS_URL || process.env.REDIS_HOST) {
  try {
    redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),

      maxRetriesPerRequest: 0, // 禁用重试，快速失败
      lazyConnect: true,
      connectTimeout: 2000, // 2秒连接超时
      // 连接池配置
      family: 4,
      keepAlive: 0, // 禁用keepAlive减少连接
      // 性能优化
      enableReadyCheck: false,
      // 错误处理
      showFriendlyErrorStack: false, // 减少错误堆栈输出
    })

    let errorLogged = false

    // 错误处理
    redis.on('error', (error) => {
      if (!errorLogged) {
        console.warn('Redis connection error, falling back to memory cache:', error.message)
        errorLogged = true
      }
      redis = null // 禁用Redis，使用内存缓存
    })

    redis.on('connect', () => {
      console.log('✅ Redis connected successfully')
      errorLogged = false
    })
  } catch (error) {
    console.warn('Redis initialization failed, using memory cache:', error instanceof Error ? error.message : 'Unknown error')
    redis = null
  }
} else {
  console.log('ℹ️  Redis not configured, using memory cache')
}

// 缓存键前缀
const CACHE_PREFIXES = {
  USER: 'user:',
  PRODUCT: 'product:',
  ORDER: 'order:',
  SESSION: 'session:',
  API: 'api:',
  SEARCH: 'search:',
  STATS: 'stats:',
} as const

// 缓存时间配置（秒）
const CACHE_TTL = {
  SHORT: 60, // 1分钟
  MEDIUM: 300, // 5分钟
  LONG: 1800, // 30分钟
  VERY_LONG: 3600, // 1小时
  DAY: 86400, // 24小时
} as const

// 缓存管理类
export class CacheManager {
  private redis: Redis | null

  constructor() {
    this.redis = redis
  }

  // 内存缓存操作
  private memoryGet(key: string): any | null {
    const item = memoryCache.get(key)
    if (!item) return null

    if (Date.now() > item.expiry) {
      memoryCache.delete(key)
      return null
    }

    return item.value
  }

  private memorySet(key: string, value: any, ttl: number): void {
    const expiry = Date.now() + (ttl * 1000)
    memoryCache.set(key, { value, expiry })
  }

  private memoryDel(key: string): void {
    memoryCache.delete(key)
  }

  private memoryExists(key: string): boolean {
    const item = memoryCache.get(key)
    if (!item) return false

    if (Date.now() > item.expiry) {
      memoryCache.delete(key)
      return false
    }

    return true
  }

  // 基础缓存操作
  async get<T>(key: string): Promise<T | null> {
    try {
      if (this.redis) {
        const value = await this.redis.get(key)
        return value ? JSON.parse(value) : null
      } else {
        return this.memoryGet(key)
      }
    } catch (error) {
      console.error('Cache get error:', error)
      // 回退到内存缓存
      return this.memoryGet(key)
    }
  }

  async set(key: string, value: any, ttl: number = CACHE_TTL.MEDIUM): Promise<boolean> {
    try {
      if (this.redis) {
        await this.redis.setex(key, ttl, JSON.stringify(value))
      } else {
        this.memorySet(key, value, ttl)
      }
      return true
    } catch (error) {
      console.error('Cache set error:', error)
      // 回退到内存缓存
      this.memorySet(key, value, ttl)
      return true
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      if (!this.redis) return false
      await this.redis.del(key)
      return true
    } catch (error) {
      console.error('Cache delete error:', error)
      return false
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      if (!this.redis) return false
      const result = await this.redis.exists(key)
      return result === 1
    } catch (error) {
      console.error('Cache exists error:', error)
      return false
    }
  }

  // 批量操作
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      if (!this.redis) return keys.map(() => null)
      const values = await this.redis.mget(...keys)
      return values.map(value => value ? JSON.parse(value) : null)
    } catch (error) {
      console.error('Cache mget error:', error)
      return keys.map(() => null)
    }
  }

  async mset(keyValues: Record<string, any>, ttl: number = CACHE_TTL.MEDIUM): Promise<boolean> {
    try {
      if (!this.redis) return false
      const pipeline = this.redis.pipeline()
      
      Object.entries(keyValues).forEach(([key, value]) => {
        pipeline.setex(key, ttl, JSON.stringify(value))
      })
      
      await pipeline.exec()
      return true
    } catch (error) {
      console.error('Cache mset error:', error)
      return false
    }
  }

  // 模式删除
  async delPattern(pattern: string): Promise<number> {
    try {
      if (!this.redis) return 0
      const keys = await this.redis.keys(pattern)
      if (keys.length > 0) {
        return await this.redis.del(...keys)
      }
      return 0
    } catch (error) {
      console.error('Cache delete pattern error:', error)
      return 0
    }
  }

  // 用户缓存
  async getUserCache(userId: string) {
    return this.get(`${CACHE_PREFIXES.USER}${userId}`)
  }

  async setUserCache(userId: string, userData: any, ttl: number = CACHE_TTL.LONG) {
    return this.set(`${CACHE_PREFIXES.USER}${userId}`, userData, ttl)
  }

  async invalidateUserCache(userId: string) {
    return this.del(`${CACHE_PREFIXES.USER}${userId}`)
  }

  // 商品缓存
  async getProductCache(productId: string) {
    return this.get(`${CACHE_PREFIXES.PRODUCT}${productId}`)
  }

  async setProductCache(productId: string, productData: any, ttl: number = CACHE_TTL.MEDIUM) {
    return this.set(`${CACHE_PREFIXES.PRODUCT}${productId}`, productData, ttl)
  }

  async invalidateProductCache(productId: string) {
    return this.del(`${CACHE_PREFIXES.PRODUCT}${productId}`)
  }

  // 商品列表缓存
  async getProductListCache(cacheKey: string) {
    return this.get(`${CACHE_PREFIXES.PRODUCT}list:${cacheKey}`)
  }

  async setProductListCache(cacheKey: string, products: any, ttl: number = CACHE_TTL.SHORT) {
    return this.set(`${CACHE_PREFIXES.PRODUCT}list:${cacheKey}`, products, ttl)
  }

  // API响应缓存
  async getApiCache(endpoint: string, params?: string) {
    const key = params ? `${CACHE_PREFIXES.API}${endpoint}:${params}` : `${CACHE_PREFIXES.API}${endpoint}`
    return this.get(key)
  }

  async setApiCache(endpoint: string, data: any, ttl: number = CACHE_TTL.SHORT, params?: string) {
    const key = params ? `${CACHE_PREFIXES.API}${endpoint}:${params}` : `${CACHE_PREFIXES.API}${endpoint}`
    return this.set(key, data, ttl)
  }

  // 搜索结果缓存
  async getSearchCache(query: string, filters?: any) {
    const cacheKey = filters ? `${query}:${JSON.stringify(filters)}` : query
    return this.get(`${CACHE_PREFIXES.SEARCH}${cacheKey}`)
  }

  async setSearchCache(query: string, results: any, ttl: number = CACHE_TTL.SHORT, filters?: any) {
    const cacheKey = filters ? `${query}:${JSON.stringify(filters)}` : query
    return this.set(`${CACHE_PREFIXES.SEARCH}${cacheKey}`, results, ttl)
  }

  // 统计数据缓存
  async getStatsCache(type: string) {
    return this.get(`${CACHE_PREFIXES.STATS}${type}`)
  }

  async setStatsCache(type: string, stats: any, ttl: number = CACHE_TTL.LONG) {
    return this.set(`${CACHE_PREFIXES.STATS}${type}`, stats, ttl)
  }

  // 会话缓存
  async getSessionCache(sessionId: string) {
    return this.get(`${CACHE_PREFIXES.SESSION}${sessionId}`)
  }

  async setSessionCache(sessionId: string, sessionData: any, ttl: number = CACHE_TTL.VERY_LONG) {
    return this.set(`${CACHE_PREFIXES.SESSION}${sessionId}`, sessionData, ttl)
  }

  async invalidateSessionCache(sessionId: string) {
    return this.del(`${CACHE_PREFIXES.SESSION}${sessionId}`)
  }

  // 缓存预热
  async warmupCache() {
    try {
      // 预热热门商品
      // 预热用户统计
      // 预热系统配置
      console.log('Cache warmup completed')
    } catch (error) {
      console.error('Cache warmup error:', error)
    }
  }

  // 缓存清理
  async clearCache(pattern?: string) {
    try {
      if (!this.redis) return false
      if (pattern) {
        return await this.delPattern(pattern)
      } else {
        await this.redis.flushdb()
        return true
      }
    } catch (error) {
      console.error('Cache clear error:', error)
      return false
    }
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.redis) return false
      await this.redis.ping()
      return true
    } catch (error) {
      console.error('Redis health check failed:', error)
      return false
    }
  }

  // 关闭连接
  async disconnect() {
    if (this.redis) {
      await this.redis.disconnect()
    }
  }
}

// 单例实例
export const cache = new CacheManager()

// 缓存装饰器
export function cached(ttl: number = CACHE_TTL.MEDIUM) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cachedResult = await cache.get(cacheKey)
      if (cachedResult !== null) {
        return cachedResult
      }

      // 执行原方法
      const result = await method.apply(this, args)
      
      // 缓存结果
      await cache.set(cacheKey, result, ttl)
      
      return result
    }
  }
}

export { CACHE_TTL, CACHE_PREFIXES }
