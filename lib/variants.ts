import { prisma } from './prisma'

export interface VariantAttribute {
  name: string
  value: string
}

export interface ProductVariantData {
  id?: string
  sku?: string
  price: number
  stock: number
  status?: string
  isDefault?: boolean
  attributes: VariantAttribute[]
}

export interface ProductWithVariants {
  id: string
  title: string
  description?: string
  images?: string
  price: number
  hasVariants: boolean
  variants: ProductVariantData[]
}

/**
 * 创建商品变体
 */
export async function createProductVariant(
  productId: string,
  variantData: ProductVariantData
) {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // 创建变体
      const variant = await tx.productVariant.create({
        data: {
          productId,
          sku: variantData.sku,
          price: variantData.price,
          stock: variantData.stock,
          status: variantData.status || 'AVAILABLE',
          isDefault: variantData.isDefault || false
        }
      })

      // 创建变体属性
      if (variantData.attributes && variantData.attributes.length > 0) {
        await tx.variantAttribute.createMany({
          data: variantData.attributes.map(attr => ({
            variantId: variant.id,
            name: attr.name,
            value: attr.value
          }))
        })
      }

      // 如果是默认变体，取消其他变体的默认状态
      if (variantData.isDefault) {
        await tx.productVariant.updateMany({
          where: {
            productId,
            id: { not: variant.id }
          },
          data: {
            isDefault: false
          }
        })
      }

      // 更新商品的hasVariants标志
      await tx.product.update({
        where: { id: productId },
        data: { hasVariants: true }
      })

      return variant
    })

    return { success: true, variant: result }
  } catch (error) {
    console.error('创建商品变体失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '创建失败' }
  }
}

/**
 * 更新商品变体
 */
export async function updateProductVariant(
  variantId: string,
  variantData: Partial<ProductVariantData>
) {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // 获取当前变体信息
      const currentVariant = await tx.productVariant.findUnique({
        where: { id: variantId },
        include: { attributes: true }
      })

      if (!currentVariant) {
        throw new Error('变体不存在')
      }

      // 更新变体基本信息
      const updateData: any = {}
      if (variantData.sku !== undefined) updateData.sku = variantData.sku
      if (variantData.price !== undefined) updateData.price = variantData.price
      if (variantData.stock !== undefined) updateData.stock = variantData.stock
      if (variantData.status !== undefined) updateData.status = variantData.status
      if (variantData.isDefault !== undefined) updateData.isDefault = variantData.isDefault

      const variant = await tx.productVariant.update({
        where: { id: variantId },
        data: updateData
      })

      // 更新属性
      if (variantData.attributes) {
        // 删除现有属性
        await tx.variantAttribute.deleteMany({
          where: { variantId }
        })

        // 创建新属性
        if (variantData.attributes.length > 0) {
          await tx.variantAttribute.createMany({
            data: variantData.attributes.map(attr => ({
              variantId,
              name: attr.name,
              value: attr.value
            }))
          })
        }
      }

      // 如果设置为默认变体，取消其他变体的默认状态
      if (variantData.isDefault) {
        await tx.productVariant.updateMany({
          where: {
            productId: currentVariant.productId,
            id: { not: variantId }
          },
          data: {
            isDefault: false
          }
        })
      }

      return variant
    })

    return { success: true, variant: result }
  } catch (error) {
    console.error('更新商品变体失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '更新失败' }
  }
}

/**
 * 删除商品变体
 */
export async function deleteProductVariant(variantId: string) {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // 获取变体信息
      const variant = await tx.productVariant.findUnique({
        where: { id: variantId },
        select: { productId: true, isDefault: true }
      })

      if (!variant) {
        throw new Error('变体不存在')
      }

      // 检查是否有相关订单
      const orderCount = await tx.orderItem.count({
        where: { variantId }
      })

      if (orderCount > 0) {
        throw new Error('该变体已有订单，无法删除')
      }

      // 删除变体（会级联删除属性）
      await tx.productVariant.delete({
        where: { id: variantId }
      })

      // 检查商品是否还有其他变体
      const remainingVariants = await tx.productVariant.count({
        where: { productId: variant.productId }
      })

      // 如果没有变体了，更新商品的hasVariants标志
      if (remainingVariants === 0) {
        await tx.product.update({
          where: { id: variant.productId },
          data: { hasVariants: false }
        })
      } else if (variant.isDefault) {
        // 如果删除的是默认变体，设置第一个变体为默认
        const firstVariant = await tx.productVariant.findFirst({
          where: { productId: variant.productId }
        })
        
        if (firstVariant) {
          await tx.productVariant.update({
            where: { id: firstVariant.id },
            data: { isDefault: true }
          })
        }
      }

      return true
    })

    return { success: true }
  } catch (error) {
    console.error('删除商品变体失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '删除失败' }
  }
}

/**
 * 获取商品的所有变体
 */
export async function getProductVariants(productId: string) {
  try {
    const variants = await prisma.productVariant.findMany({
      where: { productId },
      include: {
        attributes: true
      },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'asc' }
      ]
    })

    return { success: true, variants }
  } catch (error) {
    console.error('获取商品变体失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '获取失败' }
  }
}

/**
 * 获取单个变体详情
 */
export async function getVariantById(variantId: string) {
  try {
    const variant = await prisma.productVariant.findUnique({
      where: { id: variantId },
      include: {
        attributes: true,
        product: {
          select: {
            id: true,
            title: true,
            sellerId: true
          }
        }
      }
    })

    if (!variant) {
      return { success: false, error: '变体不存在' }
    }

    return { success: true, variant }
  } catch (error) {
    console.error('获取变体详情失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '获取失败' }
  }
}

/**
 * 检查变体库存并自动下架
 */
export async function checkVariantStock(variantId: string) {
  try {
    const variant = await prisma.productVariant.findUnique({
      where: { id: variantId },
      select: {
        id: true,
        stock: true,
        status: true,
        product: {
          select: {
            id: true,
            title: true,
            sellerId: true
          }
        }
      }
    })

    if (!variant) {
      return { success: false, error: '变体不存在' }
    }

    // 如果库存为0且状态为AVAILABLE，自动下架
    if (variant.stock <= 0 && variant.status === 'AVAILABLE') {
      await prisma.productVariant.update({
        where: { id: variantId },
        data: { status: 'SOLD_OUT' }
      })

      return {
        success: true,
        statusChanged: true,
        message: '变体库存不足，已自动下架'
      }
    }

    return {
      success: true,
      statusChanged: false,
      message: null
    }
  } catch (error) {
    console.error('检查变体库存失败:', error)
    return { success: false, error: error instanceof Error ? error.message : '检查失败' }
  }
}

/**
 * 安全地减少变体库存
 */
export async function decrementVariantStock(variantId: string, quantity: number = 1) {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // 检查当前库存
      const variant = await tx.productVariant.findUnique({
        where: { id: variantId },
        select: {
          id: true,
          stock: true,
          status: true,
          product: {
            select: {
              id: true,
              title: true
            }
          }
        }
      })

      if (!variant) {
        throw new Error('变体不存在')
      }

      if (variant.stock < quantity) {
        throw new Error('变体库存不足')
      }

      // 减少库存
      const updatedVariant = await tx.productVariant.update({
        where: { id: variantId },
        data: {
          stock: {
            decrement: quantity
          }
        }
      })

      // 检查是否需要自动下架
      if (updatedVariant.stock <= 0 && updatedVariant.status === 'AVAILABLE') {
        await tx.productVariant.update({
          where: { id: variantId },
          data: { status: 'SOLD_OUT' }
        })

        return {
          success: true,
          autoDeactivated: true,
          newStock: updatedVariant.stock,
          message: '变体库存已扣减，已自动下架'
        }
      }

      return {
        success: true,
        autoDeactivated: false,
        newStock: updatedVariant.stock,
        message: '变体库存已扣减'
      }
    })

    return result
  } catch (error) {
    console.error('减少变体库存失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '系统错误'
    }
  }
}
