import { prisma } from './prisma'

/**
 * 生成随机的12位字符串（数字和字母组合）
 */
function generateRandomString(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成唯一的用户ID
 * 格式：user-adiw81723641
 */
export async function generateUniqueUserId(): Promise<string> {
  let attempts = 0
  const maxAttempts = 10

  while (attempts < maxAttempts) {
    const randomString = generateRandomString()
    const userId = `user-${randomString}`

    // 检查是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { userId }
    })

    if (!existingUser) {
      return userId
    }

    attempts++
  }

  // 如果10次尝试都失败了，使用时间戳确保唯一性
  const timestamp = Date.now().toString(36)
  const randomString = generateRandomString().substring(0, 8)
  return `user-${randomString}${timestamp}`
}

/**
 * 为现有用户生成并更新用户ID
 */
export async function updateExistingUsersWithUserId(): Promise<void> {
  try {
    // 获取所有没有userId的用户
    const usersWithoutUserId = await prisma.user.findMany({
      where: {
        userId: null
      },
      select: {
        id: true,
        email: true
      }
    })

    console.log(`找到 ${usersWithoutUserId.length} 个需要更新userId的用户`)

    // 为每个用户生成并更新userId
    for (const user of usersWithoutUserId) {
      const userId = await generateUniqueUserId()
      
      await prisma.user.update({
        where: { id: user.id },
        data: { userId }
      })

      console.log(`用户 ${user.email} 的userId已更新为: ${userId}`)
    }

    console.log('所有用户的userId更新完成')
  } catch (error) {
    console.error('更新用户userId时出错:', error)
    throw error
  }
}
