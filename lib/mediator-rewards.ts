import { prisma } from '@/lib/prisma'

// 奖励类型
export enum RewardType {
  VOTING_PARTICIPATION = 'VOTING_PARTICIPATION',
  SUCCESSFUL_MEDIATION = 'SUCCESSFUL_MEDIATION',
  MONTHLY_BONUS = 'MONTHLY_BONUS',
  DISPUTE_RESOLUTION = 'DISPUTE_RESOLUTION'
}

// 提现券配置
export const WITHDRAWAL_VOUCHER_CONFIG = {
  VOTING_PARTICIPATION: {
    amount: 10, // 10 USDT 免手续费
    validDays: 7, // 7天有效期
    monthlyLimit: 1, // 每月最多1张
    description: '仲裁投票参与奖励'
  },
  SUCCESSFUL_MEDIATION: {
    amount: 20,
    validDays: 14,
    monthlyLimit: 5,
    description: '成功调解奖励'
  },
  DISPUTE_RESOLUTION: {
    amount: 15,
    validDays: 10,
    monthlyLimit: 3,
    description: '争议解决奖励'
  }
}

// 生成提现券代码
export function generateVoucherCode(): string {
  const prefix = 'WV'
  const timestamp = Date.now().toString(36).toUpperCase()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `${prefix}${timestamp}${random}`
}

// 检查月度奖励限制
export async function checkMonthlyRewardLimit(
  mediatorId: string,
  rewardType: RewardType
): Promise<boolean> {
  const config = WITHDRAWAL_VOUCHER_CONFIG[rewardType]
  if (!config) return false

  const startOfMonth = new Date()
  startOfMonth.setDate(1)
  startOfMonth.setHours(0, 0, 0, 0)

  const rewardCount = await prisma.mediatorReward.count({
    where: {
      mediatorId,
      rewardType,
      earnedAt: {
        gte: startOfMonth
      }
    }
  })

  return rewardCount < config.monthlyLimit
}

// 发放仲裁投票奖励
export async function issueVotingParticipationReward(
  mediatorId: string,
  disputeId: string,
  voteDecision: string,
  finalDecision: string
): Promise<{ success: boolean; reward?: any; message: string }> {
  try {
    // 检查是否已经发放过奖励
    const existingReward = await prisma.mediatorReward.findFirst({
      where: {
        mediatorId,
        rewardType: RewardType.VOTING_PARTICIPATION,
        description: {
          contains: disputeId
        }
      }
    })

    if (existingReward) {
      return {
        success: false,
        message: '该争议的投票奖励已发放'
      }
    }

    // 检查月度限制
    const canReceiveReward = await checkMonthlyRewardLimit(
      mediatorId,
      RewardType.VOTING_PARTICIPATION
    )

    if (!canReceiveReward) {
      return {
        success: false,
        message: '本月投票奖励已达上限'
      }
    }

    // 检查投票是否与最终决定一致（准确投票才有奖励）
    const isAccurateVote = voteDecision === finalDecision
    if (!isAccurateVote) {
      return {
        success: false,
        message: '投票与最终决定不一致，不发放奖励'
      }
    }

    const config = WITHDRAWAL_VOUCHER_CONFIG[RewardType.VOTING_PARTICIPATION]
    const validUntil = new Date()
    validUntil.setDate(validUntil.getDate() + config.validDays)

    // 创建提现券和奖励记录
    const result = await prisma.$transaction(async (tx) => {
      // 创建提现券
      const voucher = await tx.withdrawalVoucher.create({
        data: {
          code: generateVoucherCode(),
          amount: config.amount,
          description: config.description,
          validUntil,
          isUsed: false,
          usedBy: mediatorId,
          issuedBy: 'system' // 系统发放
        }
      })

      // 创建奖励记录
      const reward = await tx.mediatorReward.create({
        data: {
          mediatorId,
          rewardType: RewardType.VOTING_PARTICIPATION,
          amount: config.amount,
          voucherId: voucher.id,
          description: `仲裁投票参与奖励 - 争议ID: ${disputeId}`,
          earnedAt: new Date()
        }
      })

      return { voucher, reward }
    })

    // 发送通知
    await prisma.notification.create({
      data: {
        userId: mediatorId,
        type: 'REWARD',
        title: '获得仲裁投票奖励',
        message: `恭喜！您因参与争议仲裁投票获得 ${config.amount} USDT 免手续费提现券`,
        data: {
          rewardType: RewardType.VOTING_PARTICIPATION,
          amount: config.amount,
          voucherCode: result.voucher.code,
          validUntil: validUntil.toISOString(),
          disputeId
        },
        priority: 'MEDIUM'
      }
    }).catch(() => {
      // 忽略通知发送失败
    })

    return {
      success: true,
      reward: result.reward,
      message: '投票奖励发放成功'
    }

  } catch (error) {
    console.error('发放投票奖励失败:', error)
    return {
      success: false,
      message: '发放奖励失败'
    }
  }
}

// 发放成功调解奖励
export async function issueSuccessfulMediationReward(
  mediatorId: string,
  escrowOrderId: string
): Promise<{ success: boolean; reward?: any; message: string }> {
  try {
    // 检查是否已经发放过奖励
    const existingReward = await prisma.mediatorReward.findFirst({
      where: {
        mediatorId,
        rewardType: RewardType.SUCCESSFUL_MEDIATION,
        description: {
          contains: escrowOrderId
        }
      }
    })

    if (existingReward) {
      return {
        success: false,
        message: '该托管订单的调解奖励已发放'
      }
    }

    // 检查月度限制
    const canReceiveReward = await checkMonthlyRewardLimit(
      mediatorId,
      RewardType.SUCCESSFUL_MEDIATION
    )

    if (!canReceiveReward) {
      return {
        success: false,
        message: '本月调解奖励已达上限'
      }
    }

    const config = WITHDRAWAL_VOUCHER_CONFIG[RewardType.SUCCESSFUL_MEDIATION]
    const validUntil = new Date()
    validUntil.setDate(validUntil.getDate() + config.validDays)

    // 创建提现券和奖励记录
    const result = await prisma.$transaction(async (tx) => {
      const voucher = await tx.withdrawalVoucher.create({
        data: {
          code: generateVoucherCode(),
          amount: config.amount,
          description: config.description,
          validUntil,
          isUsed: false,
          usedBy: mediatorId,
          issuedBy: 'system'
        }
      })

      const reward = await tx.mediatorReward.create({
        data: {
          mediatorId,
          rewardType: RewardType.SUCCESSFUL_MEDIATION,
          amount: config.amount,
          voucherId: voucher.id,
          description: `成功调解奖励 - 托管订单ID: ${escrowOrderId}`,
          earnedAt: new Date()
        }
      })

      return { voucher, reward }
    })

    // 发送通知
    await prisma.notification.create({
      data: {
        userId: mediatorId,
        type: 'REWARD',
        title: '获得成功调解奖励',
        message: `恭喜！您因成功调解托管订单获得 ${config.amount} USDT 免手续费提现券`,
        data: {
          rewardType: RewardType.SUCCESSFUL_MEDIATION,
          amount: config.amount,
          voucherCode: result.voucher.code,
          validUntil: validUntil.toISOString(),
          escrowOrderId
        },
        priority: 'MEDIUM'
      }
    }).catch(() => {})

    return {
      success: true,
      reward: result.reward,
      message: '调解奖励发放成功'
    }

  } catch (error) {
    console.error('发放调解奖励失败:', error)
    return {
      success: false,
      message: '发放奖励失败'
    }
  }
}

// 批量发放月度奖励
export async function issueMonthlyBonuses(): Promise<{
  success: boolean
  processed: number
  errors: string[]
}> {
  try {
    const errors: string[] = []
    let processed = 0

    // 获取活跃的中间人（上月至少参与1次仲裁或调解）
    const lastMonth = new Date()
    lastMonth.setMonth(lastMonth.getMonth() - 1)
    lastMonth.setDate(1)
    lastMonth.setHours(0, 0, 0, 0)

    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)

    const activeMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        OR: [
          {
            arbitrationVotes: {
              some: {
                votedAt: {
                  gte: lastMonth,
                  lt: thisMonth
                }
              }
            }
          },
          {
            mediatorEscrows: {
              some: {
                completedAt: {
                  gte: lastMonth,
                  lt: thisMonth
                }
              }
            }
          }
        ]
      },
      select: {
        id: true,
        name: true,
        mediatorReputation: true
      }
    })

    for (const mediator of activeMediators) {
      try {
        // 检查是否已发放本月奖励
        const existingBonus = await prisma.mediatorReward.findFirst({
          where: {
            mediatorId: mediator.id,
            rewardType: RewardType.MONTHLY_BONUS,
            earnedAt: {
              gte: thisMonth
            }
          }
        })

        if (existingBonus) {
          continue // 已发放，跳过
        }

        // 根据信誉度计算奖励金额
        const bonusAmount = calculateMonthlyBonus(mediator.mediatorReputation || 0)
        
        if (bonusAmount > 0) {
          const validUntil = new Date()
          validUntil.setDate(validUntil.getDate() + 30) // 30天有效期

          await prisma.$transaction(async (tx) => {
            const voucher = await tx.withdrawalVoucher.create({
              data: {
                code: generateVoucherCode(),
                amount: bonusAmount,
                description: '月度活跃奖励',
                validUntil,
                isUsed: false,
                usedBy: mediator.id,
                issuedBy: 'system'
              }
            })

            await tx.mediatorReward.create({
              data: {
                mediatorId: mediator.id,
                rewardType: RewardType.MONTHLY_BONUS,
                amount: bonusAmount,
                voucherId: voucher.id,
                description: `月度活跃奖励 - ${lastMonth.getFullYear()}年${lastMonth.getMonth() + 1}月`,
                earnedAt: new Date()
              }
            })
          })

          processed++
        }

      } catch (error) {
        console.error(`发放月度奖励失败 - 中间人ID: ${mediator.id}`, error)
        errors.push(`中间人 ${mediator.name} 奖励发放失败`)
      }
    }

    return {
      success: true,
      processed,
      errors
    }

  } catch (error) {
    console.error('批量发放月度奖励失败:', error)
    return {
      success: false,
      processed: 0,
      errors: ['批量发放失败']
    }
  }
}

// 计算月度奖励金额
function calculateMonthlyBonus(reputation: number): number {
  if (reputation >= 4.8) return 50 // 钻石级
  if (reputation >= 4.5) return 30 // 黄金级
  if (reputation >= 4.0) return 20 // 白银级
  if (reputation >= 3.5) return 10 // 青铜级
  return 0 // 不符合条件
}

// 获取中间人奖励统计
export async function getMediatorRewardStats(mediatorId: string): Promise<{
  totalRewards: number
  totalAmount: number
  monthlyRewards: number
  monthlyAmount: number
  availableVouchers: number
  availableAmount: number
  recentRewards: any[]
}> {
  try {
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const [
      totalStats,
      monthlyStats,
      availableVouchers,
      recentRewards
    ] = await Promise.all([
      // 总奖励统计
      prisma.mediatorReward.aggregate({
        where: { mediatorId },
        _count: { _all: true },
        _sum: { amount: true }
      }),
      
      // 本月奖励统计
      prisma.mediatorReward.aggregate({
        where: {
          mediatorId,
          earnedAt: { gte: startOfMonth }
        },
        _count: { _all: true },
        _sum: { amount: true }
      }),
      
      // 可用提现券
      prisma.withdrawalVoucher.findMany({
        where: {
          usedBy: mediatorId,
          isUsed: false,
          validUntil: { gt: new Date() }
        },
        select: { amount: true }
      }),
      
      // 最近奖励记录
      prisma.mediatorReward.findMany({
        where: { mediatorId },
        include: {
          voucher: {
            select: {
              code: true,
              isUsed: true,
              validUntil: true
            }
          }
        },
        orderBy: { earnedAt: 'desc' },
        take: 10
      })
    ])

    const availableAmount = availableVouchers.reduce((sum, v) => sum + v.amount, 0)

    return {
      totalRewards: totalStats._count._all || 0,
      totalAmount: totalStats._sum.amount || 0,
      monthlyRewards: monthlyStats._count._all || 0,
      monthlyAmount: monthlyStats._sum.amount || 0,
      availableVouchers: availableVouchers.length,
      availableAmount,
      recentRewards
    }

  } catch (error) {
    console.error('获取奖励统计失败:', error)
    throw new Error('获取奖励统计失败')
  }
}
