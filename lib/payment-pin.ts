import crypto from 'crypto'
import { prisma } from './prisma'

/**
 * 生成支付PIN码
 * @param length PIN码长度，默认6位
 * @returns 生成的PIN码
 */
export function generatePaymentPin(length: number = 6): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let pin = ''
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, characters.length)
    pin += characters[randomIndex]
  }
  
  return pin
}

/**
 * 验证PIN码格式
 * @param pin PIN码
 * @returns 是否有效
 */
export function validatePinFormat(pin: string): boolean {
  // PIN码应该是6位字母数字组合，全大写
  const pinRegex = /^[A-Z0-9]{6}$/
  return pinRegex.test(pin)
}

/**
 * 检查PIN码是否过期
 * @param expiryTime 过期时间
 * @returns 是否过期
 */
export function isPinExpired(expiryTime: Date): boolean {
  return new Date() > expiryTime
}

/**
 * 为订单生成PIN码并保存到数据库
 * @param orderId 订单ID
 * @returns 生成的PIN码
 */
export async function generatePinForOrder(orderId: string): Promise<string> {
  let pin: string
  let isUnique = false
  let attempts = 0
  const maxAttempts = 10
  
  // 确保PIN码唯一性
  while (!isUnique && attempts < maxAttempts) {
    pin = generatePaymentPin()
    
    // 检查PIN码是否已存在且未过期
    const existingOrder = await prisma.order.findFirst({
      where: {
        paymentPin: pin,
        paymentPinExpiry: {
          gt: new Date()
        },
        paymentPinUsed: false
      }
    })
    
    if (!existingOrder) {
      isUnique = true
    }
    attempts++
  }
  
  if (!isUnique) {
    throw new Error('无法生成唯一的PIN码，请稍后重试')
  }
  
  // 设置PIN码过期时间（30分钟）
  const expiryTime = new Date()
  expiryTime.setMinutes(expiryTime.getMinutes() + 30)
  
  // 更新订单
  await prisma.order.update({
    where: { id: orderId },
    data: {
      paymentPin: pin!,
      paymentPinExpiry: expiryTime,
      paymentPinUsed: false,
      verificationAttempts: 0
    }
  })
  
  return pin!
}

/**
 * 验证支付PIN码和订单号
 * @param orderId 订单ID
 * @param submittedPin 用户提交的PIN码
 * @param submittedOrderNumber 用户提交的币安订单号
 * @param ipAddress 用户IP地址
 * @param userAgent 用户代理
 * @returns 验证结果
 */
export async function verifyPaymentPin(
  orderId: string,
  submittedPin: string,
  submittedOrderNumber: string,
  ipAddress?: string,
  userAgent?: string
): Promise<{
  success: boolean
  status: 'SUCCESS' | 'FAILED' | 'EXPIRED' | 'INVALID_PIN' | 'INVALID_ORDER' | 'TOO_MANY_ATTEMPTS'
  message: string
}> {
  try {
    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true,
        seller: true
      }
    })
    
    if (!order) {
      return {
        success: false,
        status: 'FAILED',
        message: '订单不存在'
      }
    }
    
    // 检查验证尝试次数
    if (order.verificationAttempts >= 5) {
      await logVerificationAttempt(
        orderId,
        submittedPin,
        submittedOrderNumber,
        false,
        'TOO_MANY_ATTEMPTS',
        '验证尝试次数过多',
        ipAddress,
        userAgent
      )
      
      return {
        success: false,
        status: 'TOO_MANY_ATTEMPTS',
        message: '验证尝试次数过多，请联系客服'
      }
    }
    
    // 检查PIN码是否存在
    if (!order.paymentPin || !order.paymentPinExpiry) {
      return {
        success: false,
        status: 'INVALID_PIN',
        message: 'PIN码不存在'
      }
    }
    
    // 检查PIN码是否已使用
    if (order.paymentPinUsed) {
      await logVerificationAttempt(
        orderId,
        submittedPin,
        submittedOrderNumber,
        false,
        'INVALID_PIN',
        'PIN码已使用',
        ipAddress,
        userAgent
      )
      
      return {
        success: false,
        status: 'INVALID_PIN',
        message: 'PIN码已使用'
      }
    }
    
    // 检查PIN码是否过期
    if (isPinExpired(order.paymentPinExpiry)) {
      await logVerificationAttempt(
        orderId,
        submittedPin,
        submittedOrderNumber,
        false,
        'EXPIRED',
        'PIN码已过期',
        ipAddress,
        userAgent
      )
      
      return {
        success: false,
        status: 'EXPIRED',
        message: 'PIN码已过期，请重新生成'
      }
    }
    
    // 验证PIN码
    if (order.paymentPin !== submittedPin.toUpperCase()) {
      // 增加验证尝试次数
      await prisma.order.update({
        where: { id: orderId },
        data: {
          verificationAttempts: order.verificationAttempts + 1,
          lastVerificationAt: new Date()
        }
      })
      
      await logVerificationAttempt(
        orderId,
        submittedPin,
        submittedOrderNumber,
        false,
        'INVALID_PIN',
        'PIN码不正确',
        ipAddress,
        userAgent
      )
      
      return {
        success: false,
        status: 'INVALID_PIN',
        message: `PIN码不正确，还可尝试 ${4 - order.verificationAttempts} 次`
      }
    }
    
    // 验证币安订单号格式（简单验证）
    if (!submittedOrderNumber || submittedOrderNumber.length < 10) {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          verificationAttempts: order.verificationAttempts + 1,
          lastVerificationAt: new Date()
        }
      })
      
      await logVerificationAttempt(
        orderId,
        submittedPin,
        submittedOrderNumber,
        false,
        'INVALID_ORDER',
        '币安订单号格式不正确',
        ipAddress,
        userAgent
      )
      
      return {
        success: false,
        status: 'INVALID_ORDER',
        message: '币安订单号格式不正确'
      }
    }
    
    // 验证成功，标记PIN码为已使用
    await prisma.order.update({
      where: { id: orderId },
      data: {
        paymentPinUsed: true,
        paymentTxHash: submittedOrderNumber,
        lastVerificationAt: new Date(),
        status: 'PAID',
        paymentConfirmed: false
      }
    })
    
    await logVerificationAttempt(
      orderId,
      submittedPin,
      submittedOrderNumber,
      true,
      'SUCCESS',
      '验证成功',
      ipAddress,
      userAgent
    )
    
    return {
      success: true,
      status: 'SUCCESS',
      message: '验证成功，支付已提交等待确认'
    }
    
  } catch (error) {
    console.error('PIN verification error:', error)
    
    await logVerificationAttempt(
      orderId,
      submittedPin,
      submittedOrderNumber,
      false,
      'FAILED',
      '系统错误',
      ipAddress,
      userAgent
    )
    
    return {
      success: false,
      status: 'FAILED',
      message: '系统错误，请稍后重试'
    }
  }
}

/**
 * 记录验证尝试日志
 */
async function logVerificationAttempt(
  orderId: string,
  submittedPin: string,
  submittedOrderNumber: string,
  isValid: boolean,
  status: string,
  failureReason: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await prisma.paymentPinVerification.create({
      data: {
        orderId,
        submittedPin,
        submittedOrderNumber,
        isValid,
        verificationStatus: status,
        failureReason: isValid ? null : failureReason,
        ipAddress,
        userAgent
      }
    })
  } catch (error) {
    console.error('Failed to log verification attempt:', error)
  }
}

/**
 * 重新生成PIN码（当PIN码过期时）
 * @param orderId 订单ID
 * @returns 新的PIN码
 */
export async function regeneratePinForOrder(orderId: string): Promise<string> {
  // 检查订单状态
  const order = await prisma.order.findUnique({
    where: { id: orderId }
  })
  
  if (!order) {
    throw new Error('订单不存在')
  }
  
  if (order.status !== 'PENDING_PAYMENT') {
    throw new Error('订单状态不允许重新生成PIN码')
  }
  
  if (order.paymentPinUsed) {
    throw new Error('PIN码已使用，无法重新生成')
  }
  
  // 生成新的PIN码
  return await generatePinForOrder(orderId)
}
