// 帮助内容数据结构和内容定义

export interface HelpCategory {
  id: string
  title: string
  description: string
  icon: string
  color: string
  articles: HelpArticle[]
}

export interface HelpArticle {
  id: string
  title: string
  content: string
  tags: string[]
  lastUpdated: string
  helpful: number
  notHelpful: number
  relatedArticles: string[]
}

// 帮助中心内容数据
export const helpCategories: HelpCategory[] = [
  {
    id: 'getting-started',
    title: '新手入门',
    description: '快速了解BitMarket平台的基本功能和操作流程',
    icon: '🚀',
    color: 'bg-blue-100 text-blue-600',
    articles: [
      {
        id: 'registration-guide',
        title: '注册账户指南',
        content: `
# 注册账户指南

欢迎来到BitMarket！按照以下步骤快速注册您的账户：

## 1. 访问注册页面
- 点击页面右上角的"注册"按钮
- 或直接访问 [注册页面](/auth/register)

## 2. 填写注册信息
### 必填信息：
- **邮箱地址**：用于登录和接收重要通知
- **密码**：至少8位，包含大小写字母和数字
- **确认密码**：再次输入密码确认
- **用户名**：显示在平台上的昵称

### 可选信息：
- **手机号码**：用于安全验证（推荐填写）
- **所在城市**：帮助匹配本地交易

## 3. 邮箱验证
- 注册成功后，系统会发送验证邮件到您的邮箱
- 点击邮件中的验证链接完成邮箱验证
- 验证后即可正常使用所有功能

## 4. 完善个人资料
- 上传头像照片
- 填写个人简介
- 绑定币安UID（用于USDT交易）

## 注意事项
⚠️ **重要提醒**：
- 请使用真实有效的邮箱地址
- 密码请妥善保管，不要泄露给他人
- 建议开启两步验证提高账户安全性

## 遇到问题？
如果注册过程中遇到任何问题，请：
- 查看 [常见问题](/help#faq)
- 联系客服：<EMAIL>
        `,
        tags: ['注册', '新手', '账户'],
        lastUpdated: '2024-01-15',
        helpful: 156,
        notHelpful: 8,
        relatedArticles: ['login-guide', 'profile-setup', 'security-basics']
      },
      {
        id: 'login-guide',
        title: '登录使用指南',
        content: `
# 登录使用指南

## 登录方式
BitMarket支持以下登录方式：

### 1. 邮箱登录
- 输入注册时使用的邮箱地址
- 输入密码
- 点击"登录"按钮

### 2. 记住登录状态
- 勾选"记住我"选项
- 下次访问时自动登录（30天有效）

## 忘记密码？
如果忘记密码，可以通过以下方式重置：

1. 点击登录页面的"忘记密码"链接
2. 输入注册邮箱地址
3. 查收重置密码邮件
4. 点击邮件中的重置链接
5. 设置新密码

## 登录安全
为了保护您的账户安全：

- 不要在公共设备上保存登录状态
- 定期更换密码
- 发现异常登录及时联系客服
- 建议开启两步验证

## 登录问题排查
### 无法登录？
1. 检查邮箱地址是否正确
2. 确认密码输入无误
3. 检查网络连接
4. 清除浏览器缓存
5. 尝试使用其他浏览器

### 账户被锁定？
- 连续输错密码5次会临时锁定账户
- 等待30分钟后自动解锁
- 或通过邮箱重置密码立即解锁
        `,
        tags: ['登录', '密码', '安全'],
        lastUpdated: '2024-01-15',
        helpful: 89,
        notHelpful: 3,
        relatedArticles: ['registration-guide', 'password-reset', 'security-basics']
      },
      {
        id: 'basic-operations',
        title: '基本操作介绍',
        content: `
# 基本操作介绍

## 平台导航
### 主要功能区域：
- **首页**：查看最新商品和平台动态
- **浏览商品**：搜索和浏览所有商品
- **需求广场**：发布和查看购买需求
- **个人中心**：管理个人信息和交易记录

## 核心功能
### 1. 商品浏览
- 使用搜索框查找特定商品
- 通过分类筛选商品
- 查看商品详情和卖家信息
- 收藏感兴趣的商品

### 2. 发布商品
- 点击"发布商品"按钮
- 填写商品信息和价格
- 上传商品图片
- 设置交易方式

### 3. 需求发布
- 在需求广场发布购买需求
- 设置预算和要求
- 等待卖家报价
- 选择合适的报价

### 4. 交易流程
- 下单购买商品
- 选择支付方式
- 等待卖家发货
- 确认收货并评价

## 个人中心功能
- **我的订单**：查看所有交易记录
- **我的商品**：管理发布的商品
- **我的需求**：管理发布的需求
- **收藏夹**：查看收藏的商品
- **信用记录**：查看信用积分变化
- **安全设置**：管理账户安全

## 快捷操作
- 使用快捷键 Ctrl+K 打开搜索
- 点击头像快速访问个人中心
- 使用面包屑导航快速返回上级页面
        `,
        tags: ['基础', '操作', '导航'],
        lastUpdated: '2024-01-15',
        helpful: 234,
        notHelpful: 12,
        relatedArticles: ['trading-guide', 'product-management', 'profile-setup']
      }
    ]
  },
  {
    id: 'trading-guide',
    title: '交易指南',
    description: '详细了解商品发布、购买流程和支付方式',
    icon: '💰',
    color: 'bg-green-100 text-green-600',
    articles: [
      {
        id: 'publish-product',
        title: '发布商品指南',
        content: `
# 发布商品指南

## 发布前准备
### 1. 商品信息准备
- 商品标题（简洁明了）
- 详细描述（包含规格、成色等）
- 清晰的商品图片（建议3-5张）
- 准确的价格（USDT计价）

### 2. 商品分类
选择合适的商品分类：
- 数码产品
- 服装配饰
- 家居用品
- 图书音像
- 其他分类

## 发布步骤
### 1. 进入发布页面
- 登录账户
- 点击"发布商品"按钮
- 或访问个人中心 → 我的商品 → 发布新商品

### 2. 填写商品信息
#### 基本信息：
- **商品标题**：不超过50字，突出商品特点
- **商品分类**：选择最匹配的分类
- **商品描述**：详细描述商品状况、规格等
- **商品成色**：全新、几乎全新、轻微使用痕迹、明显使用痕迹

#### 价格设置：
- **销售价格**：以USDT为单位
- **参考人民币价格**：系统自动换算显示
- **是否接受议价**：可选择固定价格或可议价

#### 交易方式：
- **配送方式**：快递配送、同城自取、线下交易
- **所在地区**：选择您的城市和区域

### 3. 上传商品图片
- 支持JPG、PNG格式
- 单张图片不超过5MB
- 建议上传3-5张不同角度的图片
- 第一张图片将作为封面显示

### 4. 商品变体（可选）
如果商品有多个规格（如颜色、尺寸），可以设置变体：
- 添加变体属性（如颜色：红色、蓝色）
- 为每个变体设置价格和库存
- 上传变体专属图片

### 5. 提交审核
- 检查所有信息是否准确
- 点击"提交审核"
- 等待管理员审核（通常1-2个工作日）

## 商品管理
### 发布后可以：
- 编辑商品信息
- 调整价格
- 上下架商品
- 查看浏览量和收藏数
- 回复买家咨询

## 提高成交率的技巧
1. **优质图片**：清晰、多角度展示商品
2. **详细描述**：诚实描述商品状况
3. **合理定价**：参考市场价格
4. **及时回复**：快速回应买家咨询
5. **良好信誉**：保持高信用评分

## 注意事项
⚠️ **禁止发布**：
- 违法违规商品
- 虚假信息商品
- 侵权盗版商品
- 危险品和管制品

违规发布可能导致商品下架或账户封禁。
        `,
        tags: ['发布', '商品', '卖家'],
        lastUpdated: '2024-01-15',
        helpful: 312,
        notHelpful: 18,
        relatedArticles: ['buying-guide', 'payment-methods', 'product-management']
      }
    ]
  }
]

// 搜索帮助内容
export function searchHelpContent(query: string): HelpArticle[] {
  const results: HelpArticle[] = []
  const searchTerm = query.toLowerCase()

  helpCategories.forEach(category => {
    category.articles.forEach(article => {
      const titleMatch = article.title.toLowerCase().includes(searchTerm)
      const contentMatch = article.content.toLowerCase().includes(searchTerm)
      const tagMatch = article.tags.some(tag => tag.toLowerCase().includes(searchTerm))

      if (titleMatch || contentMatch || tagMatch) {
        results.push(article)
      }
    })
  })

  return results
}

// 根据ID获取文章
export function getArticleById(id: string): HelpArticle | null {
  for (const category of helpCategories) {
    const article = category.articles.find(a => a.id === id)
    if (article) return article
  }
  return null
}

// 获取相关文章
export function getRelatedArticles(articleId: string): HelpArticle[] {
  const article = getArticleById(articleId)
  if (!article) return []

  const related: HelpArticle[] = []
  article.relatedArticles.forEach(relatedId => {
    const relatedArticle = getArticleById(relatedId)
    if (relatedArticle) {
      related.push(relatedArticle)
    }
  })

  return related
}
