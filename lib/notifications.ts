import { getSocketIO, sendSystemNotification, broadcastSystemAnnouncement } from './socket'
import { prisma } from './prisma'

// 通知类型定义
export const NOTIFICATION_TYPES = {
  ORDER_STATUS_CHANGE: 'ORDER_STATUS_CHANGE',
  NEW_MESSAGE: 'NEW_MESSAGE',
  PAYMENT_CONFIRMED: 'PAYMENT_CONFIRMED',
  PRODUCT_SOLD_OUT: 'PRODUCT_SOLD_OUT',
  REVIEW_RECEIVED: 'REVIEW_RECEIVED',
  SYSTEM_ANNOUNCEMENT: 'SYSTEM_ANNOUNCEMENT',
  STOCK_ALERT: 'STOCK_ALERT',
  DEMAND_RESPONSE: 'DEMAND_RESPONSE'
} as const

export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES]

// 通知数据结构
export interface NotificationData {
  type: NotificationType
  title: string
  message: string
  userId?: string
  orderId?: string
  productId?: string
  demandId?: string
  data?: any
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  persistent?: boolean // 是否持久化到数据库
}

// 发送订单状态变化通知
export async function sendOrderStatusNotification(
  orderId: string,
  newStatus: string,
  buyerId: string,
  sellerId: string
) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        product: { select: { title: true } }
      }
    })

    if (!order) return

    const statusMessages = {
      'PENDING_PAYMENT': '订单等待付款',
      'PAID': '订单已付款',
      'SHIPPED': '订单已发货',
      'COMPLETED': '订单已完成',
      'CANCELLED': '订单已取消',
      'REFUND_REQUESTED': '申请退款'
    }

    const message = statusMessages[newStatus as keyof typeof statusMessages] || '订单状态已更新'

    // 通知买家
    sendSystemNotification(buyerId, {
      type: NOTIFICATION_TYPES.ORDER_STATUS_CHANGE,
      title: '订单状态更新',
      message: `您的订单"${order.product.title}"${message}`,
      data: {
        orderId,
        orderNumber: order.orderNumber,
        status: newStatus,
        productTitle: order.product.title
      }
    })

    // 通知卖家
    sendSystemNotification(sellerId, {
      type: NOTIFICATION_TYPES.ORDER_STATUS_CHANGE,
      title: '订单状态更新',
      message: `订单"${order.product.title}"${message}`,
      data: {
        orderId,
        orderNumber: order.orderNumber,
        status: newStatus,
        productTitle: order.product.title
      }
    })

  } catch (error) {
    console.error('Failed to send order status notification:', error)
  }
}

// 发送新消息通知
export async function sendNewMessageNotification(
  messageId: string,
  senderId: string,
  receiverId: string,
  orderId: string
) {
  try {
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        sender: { select: { name: true } },
        order: {
          include: {
            product: { select: { title: true } }
          }
        }
      }
    })

    if (!message) return

    sendSystemNotification(receiverId, {
      type: NOTIFICATION_TYPES.NEW_MESSAGE,
      title: '新消息',
      message: `${message.sender.name || '用户'}在订单"${message.order.product.title}"中发送了新消息`,
      data: {
        messageId,
        orderId,
        senderId,
        senderName: message.sender.name,
        productTitle: message.order.product.title
      }
    })

  } catch (error) {
    console.error('Failed to send new message notification:', error)
  }
}

// 发送支付确认通知
export async function sendPaymentConfirmedNotification(orderId: string, buyerId: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        product: { select: { title: true } }
      }
    })

    if (!order) return

    sendSystemNotification(buyerId, {
      type: NOTIFICATION_TYPES.PAYMENT_CONFIRMED,
      title: '支付已确认',
      message: `您的订单"${order.product.title}"支付已确认，卖家将尽快发货`,
      data: {
        orderId,
        orderNumber: order.orderNumber,
        productTitle: order.product.title
      }
    })

  } catch (error) {
    console.error('Failed to send payment confirmed notification:', error)
  }
}

// 发送商品售罄通知
export async function sendProductSoldOutNotification(productId: string, sellerId: string) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { title: true, stock: true }
    })

    if (!product) return

    sendSystemNotification(sellerId, {
      type: NOTIFICATION_TYPES.PRODUCT_SOLD_OUT,
      title: '商品库存不足',
      message: `您的商品"${product.title}"库存已不足，当前库存：${product.stock}`,
      data: {
        productId,
        productTitle: product.title,
        currentStock: product.stock
      }
    })

  } catch (error) {
    console.error('Failed to send product sold out notification:', error)
  }
}

// 发送评价通知
export async function sendReviewNotification(reviewId: string, revieweeId: string) {
  try {
    const review = await prisma.review.findUnique({
      where: { id: reviewId },
      include: {
        reviewer: { select: { name: true } },
        product: { select: { title: true } }
      }
    })

    if (!review) return

    sendSystemNotification(revieweeId, {
      type: NOTIFICATION_TYPES.REVIEW_RECEIVED,
      title: '收到新评价',
      message: `${review.reviewer.name || '用户'}对订单"${review.product.title}"给出了${review.rating}星评价`,
      data: {
        reviewId,
        rating: review.rating,
        reviewerName: review.reviewer.name,
        productTitle: review.product.title
      }
    })

  } catch (error) {
    console.error('Failed to send review notification:', error)
  }
}

// 发送需求响应通知
export async function sendDemandResponseNotification(demandId: string, offerId: string, demandUserId: string) {
  try {
    const offer = await prisma.demandOffer.findUnique({
      where: { id: offerId },
      include: {
        seller: { select: { name: true } },
        demand: { select: { title: true } }
      }
    })

    if (!offer) return

    sendSystemNotification(demandUserId, {
      type: NOTIFICATION_TYPES.DEMAND_RESPONSE,
      title: '需求有新响应',
      message: `${offer.seller.name || '用户'}响应了您的需求"${offer.demand.title}"，报价：${offer.offerPrice} USDT`,
      data: {
        demandId,
        offerId,
        sellerName: offer.seller.name,
        demandTitle: offer.demand.title,
        offerPrice: offer.offerPrice
      }
    })

  } catch (error) {
    console.error('Failed to send demand response notification:', error)
  }
}

// 发送系统公告
export async function sendSystemAnnouncement(
  title: string,
  content: string,
  type: 'info' | 'warning' | 'success' | 'error' = 'info'
) {
  try {
    broadcastSystemAnnouncement({
      title,
      content,
      type
    })

    // 可选：保存公告到数据库
    // await prisma.systemAnnouncement.create({
    //   data: { title, content, type }
    // })

  } catch (error) {
    console.error('Failed to send system announcement:', error)
  }
}

// 发送库存警告通知
export async function sendStockAlertNotification(productId: string, sellerId: string, currentStock: number) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { title: true }
    })

    if (!product) return

    sendSystemNotification(sellerId, {
      type: NOTIFICATION_TYPES.STOCK_ALERT,
      title: '库存警告',
      message: `您的商品"${product.title}"库存不足，当前库存：${currentStock}`,
      data: {
        productId,
        productTitle: product.title,
        currentStock
      }
    })

  } catch (error) {
    console.error('Failed to send stock alert notification:', error)
  }
}
