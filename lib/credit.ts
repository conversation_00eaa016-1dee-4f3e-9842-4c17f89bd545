import { prisma } from './prisma'

// 信用变动类型
export enum CreditChangeType {
  ORDER_COMPLETE = 'ORDER_COMPLETE',      // 订单完成
  REVIEW_RECEIVED = 'REVIEW_RECEIVED',    // 收到评价
  ADMIN_ADJUST = 'ADMIN_ADJUST',          // 管理员调整
  PENALTY = 'PENALTY',                    // 违规扣分
  BONUS = 'BONUS',                        // 奖励加分
  FIRST_ORDER = 'FIRST_ORDER',            // 首次交易奖励
  MONTHLY_ACTIVE = 'MONTHLY_ACTIVE'       // 月度活跃奖励
}

// 信用分数变动规则
export const CREDIT_RULES = {
  ORDER_COMPLETE: 2,        // 完成订单 +2分
  REVIEW_5_STAR: 3,         // 5星好评 +3分
  REVIEW_4_STAR: 2,         // 4星好评 +2分
  REVIEW_3_STAR: 1,         // 3星好评 +1分
  REVIEW_2_STAR: -1,        // 2星差评 -1分
  REVIEW_1_STAR: -3,        // 1星差评 -3分
  FIRST_ORDER_BONUS: 5,     // 首次交易奖励 +5分
  ORDER_CANCEL: -2,         // 取消订单 -2分
  DISPUTE_PENALTY: -5,      // 争议处罚 -5分
  FRAUD_PENALTY: -20,       // 欺诈处罚 -20分
  MONTHLY_ACTIVE_BONUS: 3   // 月度活跃奖励 +3分
}

// 记录信用变动
export async function recordCreditChange(
  userId: string,
  changeType: CreditChangeType,
  changeScore: number,
  reason: string,
  options: {
    orderId?: string
    reviewId?: string
    adminId?: string
    metadata?: any
  } = {}
) {
  try {
    // 获取用户当前信用分数
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditScore: true }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const beforeScore = user.creditScore
    const afterScore = Math.max(0, Math.min(1000, beforeScore + changeScore)) // 限制在0-1000之间

    // 使用事务更新信用分数和记录历史
    const result = await prisma.$transaction(async (tx) => {
      // 更新用户信用分数
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: { creditScore: afterScore }
      })

      // 记录信用变动历史
      const creditHistory = await tx.creditHistory.create({
        data: {
          userId,
          changeType,
          changeScore,
          reason,
          beforeScore,
          afterScore,
          orderId: options.orderId,
          reviewId: options.reviewId,
          adminId: options.adminId,
          metadata: options.metadata
        }
      })

      return { updatedUser, creditHistory }
    })

    return result

  } catch (error) {
    console.error('Record credit change error:', error)
    throw error
  }
}

// 更新用户信用分数（基于评价）
export async function updateUserCreditScore(userId: string) {
  try {
    // 获取用户最近的评价
    const recentReviews = await prisma.review.findMany({
      where: {
        revieweeId: userId,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 为每个评价记录信用变动
    for (const review of recentReviews) {
      // 检查是否已经为这个评价记录过信用变动
      const existingRecord = await prisma.creditHistory.findFirst({
        where: {
          userId,
          reviewId: review.id,
          changeType: CreditChangeType.REVIEW_RECEIVED
        }
      })

      if (!existingRecord) {
        let changeScore = 0
        let reason = ''

        switch (review.rating) {
          case 5:
            changeScore = CREDIT_RULES.REVIEW_5_STAR
            reason = '收到5星好评'
            break
          case 4:
            changeScore = CREDIT_RULES.REVIEW_4_STAR
            reason = '收到4星好评'
            break
          case 3:
            changeScore = CREDIT_RULES.REVIEW_3_STAR
            reason = '收到3星评价'
            break
          case 2:
            changeScore = CREDIT_RULES.REVIEW_2_STAR
            reason = '收到2星差评'
            break
          case 1:
            changeScore = CREDIT_RULES.REVIEW_1_STAR
            reason = '收到1星差评'
            break
        }

        if (changeScore !== 0) {
          await recordCreditChange(
            userId,
            CreditChangeType.REVIEW_RECEIVED,
            changeScore,
            reason,
            { reviewId: review.id }
          )
        }
      }
    }

  } catch (error) {
    console.error('Update user credit score error:', error)
    throw error
  }
}

// 订单完成时的信用奖励
export async function handleOrderCompletionCredit(orderId: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true,
        seller: true
      }
    })

    if (!order) {
      throw new Error('订单不存在')
    }

    // 检查买家是否是首次交易
    const buyerOrderCount = await prisma.order.count({
      where: {
        buyerId: order.buyerId,
        status: 'COMPLETED'
      }
    })

    // 检查卖家是否是首次交易
    const sellerOrderCount = await prisma.order.count({
      where: {
        sellerId: order.sellerId,
        status: 'COMPLETED'
      }
    })

    // 为买家记录信用变动
    await recordCreditChange(
      order.buyerId,
      CreditChangeType.ORDER_COMPLETE,
      CREDIT_RULES.ORDER_COMPLETE,
      '完成订单交易',
      { orderId }
    )

    // 为卖家记录信用变动
    await recordCreditChange(
      order.sellerId,
      CreditChangeType.ORDER_COMPLETE,
      CREDIT_RULES.ORDER_COMPLETE,
      '完成订单交易',
      { orderId }
    )

    // 首次交易奖励
    if (buyerOrderCount === 1) {
      await recordCreditChange(
        order.buyerId,
        CreditChangeType.FIRST_ORDER,
        CREDIT_RULES.FIRST_ORDER_BONUS,
        '首次交易奖励',
        { orderId }
      )
    }

    if (sellerOrderCount === 1) {
      await recordCreditChange(
        order.sellerId,
        CreditChangeType.FIRST_ORDER,
        CREDIT_RULES.FIRST_ORDER_BONUS,
        '首次交易奖励',
        { orderId }
      )
    }

  } catch (error) {
    console.error('Handle order completion credit error:', error)
    throw error
  }
}

// 获取信用等级信息
export function getCreditLevelInfo(score: number) {
  if (score >= 90) {
    return {
      level: 'EXCELLENT',
      name: '优秀',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      borderColor: 'border-green-200',
      icon: '⭐',
      description: '信用优秀，值得信赖',
      nextLevel: null,
      progress: 100
    }
  } else if (score >= 70) {
    return {
      level: 'GOOD',
      name: '良好',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      borderColor: 'border-blue-200',
      icon: '👍',
      description: '信用良好，交易可靠',
      nextLevel: '优秀',
      progress: ((score - 70) / 20) * 100
    }
  } else if (score >= 50) {
    return {
      level: 'FAIR',
      name: '一般',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      borderColor: 'border-yellow-200',
      icon: '⚡',
      description: '信用一般，需要提升',
      nextLevel: '良好',
      progress: ((score - 50) / 20) * 100
    }
  } else if (score >= 30) {
    return {
      level: 'POOR',
      name: '较差',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      borderColor: 'border-orange-200',
      icon: '⚠️',
      description: '信用较差，请注意改善',
      nextLevel: '一般',
      progress: ((score - 30) / 20) * 100
    }
  } else {
    return {
      level: 'BAD',
      name: '很差',
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      borderColor: 'border-red-200',
      icon: '❌',
      description: '信用很差，需要重建信任',
      nextLevel: '较差',
      progress: (score / 30) * 100
    }
  }
}
