/**
 * 信用等级管理系统
 * 基于担保金余额、交易历史、信用积分计算用户等级
 */

import { prisma } from './prisma'

// 信用等级枚举
export enum CreditLevel {
  BRONZE = 'BRONZE',     // 青铜级
  SILVER = 'SILVER',     // 白银级
  GOLD = 'GOLD',         // 黄金级
  PLATINUM = 'PLATINUM', // 铂金级
  DIAMOND = 'DIAMOND'    // 钻石级
}

// 等级配置
export const LEVEL_CONFIG = {
  [CreditLevel.BRONZE]: {
    minDeposit: 0,
    minCreditPoints: 0,
    benefits: {
      withdrawalFeeDiscount: 0,
      priorityDisplay: false,
      customService: false,
      tradingFeeDiscount: 0
    },
    color: '#CD7F32',
    icon: '🥉'
  },
  [CreditLevel.SILVER]: {
    minDeposit: 100,
    minCreditPoints: 50,
    benefits: {
      withdrawalFeeDiscount: 0.1, // 10% 提现手续费折扣
      priorityDisplay: true,      // 商品优先展示
      customService: false,
      tradingFeeDiscount: 0.05    // 5% 交易手续费折扣
    },
    color: '#C0C0C0',
    icon: '🥈'
  },
  [CreditLevel.GOLD]: {
    minDeposit: 500,
    minCreditPoints: 200,
    benefits: {
      withdrawalFeeDiscount: 0.2,
      priorityDisplay: true,
      customService: false,
      tradingFeeDiscount: 0.1
    },
    color: '#FFD700',
    icon: '🥇'
  },
  [CreditLevel.PLATINUM]: {
    minDeposit: 2000,
    minCreditPoints: 500,
    benefits: {
      withdrawalFeeDiscount: 0.3,
      priorityDisplay: true,
      customService: true,        // 专属客服
      tradingFeeDiscount: 0.15
    },
    color: '#E5E4E2',
    icon: '💎'
  },
  [CreditLevel.DIAMOND]: {
    minDeposit: 5000,
    minCreditPoints: 1000,
    benefits: {
      withdrawalFeeDiscount: 0.5, // 50% 提现手续费折扣
      priorityDisplay: true,
      customService: true,
      tradingFeeDiscount: 0.2     // 20% 交易手续费折扣
    },
    color: '#B9F2FF',
    icon: '💎'
  }
}

/**
 * 计算用户信用积分
 */
export async function calculateCreditPoints(userId: string): Promise<number> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        fundFreezes: {
          where: {
            status: 'SETTLED'
          }
        },
        ordersAsBuyer: {
          where: {
            status: 'COMPLETED'
          }
        },
        ordersAsSeller: {
          where: {
            status: 'COMPLETED'
          }
        },
        reviews: true,
        receivedReviews: true,
        creditHistories: true
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    let points = 0

    // 1. 担保金余额积分 (每100 USDT = 10分)
    points += Math.floor(user.depositBalance / 100) * 10

    // 2. 交易完成积分 (每笔交易 = 5分)
    const totalOrders = user.ordersAsBuyer.length + user.ordersAsSeller.length
    points += totalOrders * 5

    // 3. 交易金额积分 (每1000 USDT交易额 = 20分)
    const totalVolume = user.fundFreezes
      .filter(f => f.purpose === 'PURCHASE')
      .reduce((sum, f) => sum + f.amount, 0)
    points += Math.floor(totalVolume / 1000) * 20

    // 4. 好评积分 (每个好评 = 15分)
    const positiveReviews = user.receivedReviews.filter(r => r.rating >= 4).length
    points += positiveReviews * 15

    // 5. 差评扣分 (每个差评 = -30分)
    const negativeReviews = user.receivedReviews.filter(r => r.rating <= 2).length
    points -= negativeReviews * 30

    // 6. 账户年龄积分 (每30天 = 5分)
    const accountAgeDays = Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24))
    points += Math.floor(accountAgeDays / 30) * 5

    // 7. 信用历史扣分
    const creditDeductions = user.creditHistories
      ?.filter(h => h.changeScore < 0)
      .reduce((sum, h) => sum + Math.abs(h.changeScore), 0) || 0
    points -= creditDeductions

    return Math.max(0, points) // 确保积分不为负数

  } catch (error) {
    console.error('计算信用积分失败:', error)
    throw error
  }
}

/**
 * 根据担保金和积分确定信用等级
 */
export function determineCreditLevel(depositBalance: number, creditPoints: number): CreditLevel {
  // 从最高等级开始检查
  const levels = [CreditLevel.DIAMOND, CreditLevel.PLATINUM, CreditLevel.GOLD, CreditLevel.SILVER, CreditLevel.BRONZE]
  
  for (const level of levels) {
    const config = LEVEL_CONFIG[level]
    if (depositBalance >= config.minDeposit && creditPoints >= config.minCreditPoints) {
      return level
    }
  }
  
  return CreditLevel.BRONZE
}

/**
 * 更新用户信用等级
 */
export async function updateUserCreditLevel(userId: string): Promise<{
  oldLevel: CreditLevel
  newLevel: CreditLevel
  creditPoints: number
  upgraded: boolean
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        creditLevel: true,
        creditPoints: true,
        depositBalance: true
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const oldLevel = user.creditLevel as CreditLevel
    const newCreditPoints = await calculateCreditPoints(userId)
    const newLevel = determineCreditLevel(user.depositBalance, newCreditPoints)

    // 更新用户信息
    await prisma.user.update({
      where: { id: userId },
      data: {
        creditLevel: newLevel,
        creditPoints: newCreditPoints,
        lastCreditUpdate: new Date()
      }
    })

    const upgraded = getLevelRank(newLevel) > getLevelRank(oldLevel)

    // 如果升级了，可以发送通知或奖励
    if (upgraded) {
      await handleLevelUpgrade(userId, oldLevel, newLevel)
    }

    return {
      oldLevel,
      newLevel,
      creditPoints: newCreditPoints,
      upgraded
    }

  } catch (error) {
    console.error('更新用户信用等级失败:', error)
    throw error
  }
}

/**
 * 获取等级排名（用于比较）
 */
function getLevelRank(level: CreditLevel): number {
  const ranks = {
    [CreditLevel.BRONZE]: 1,
    [CreditLevel.SILVER]: 2,
    [CreditLevel.GOLD]: 3,
    [CreditLevel.PLATINUM]: 4,
    [CreditLevel.DIAMOND]: 5
  }
  return ranks[level]
}

/**
 * 处理等级升级
 */
async function handleLevelUpgrade(userId: string, oldLevel: CreditLevel, newLevel: CreditLevel) {
  try {
    console.log(`用户 ${userId} 从 ${oldLevel} 升级到 ${newLevel}`)
    
    // 这里可以添加升级奖励逻辑
    // 例如：发送通知、给予奖励积分、解锁新功能等
    
    // 记录升级历史
    await prisma.creditHistory.create({
      data: {
        userId,
        changeType: 'LEVEL_UPGRADE',
        changeScore: 0,
        reason: `等级升级: ${oldLevel} → ${newLevel}`,
        beforeScore: 0,
        afterScore: 0,
        metadata: {
          oldLevel,
          newLevel,
          upgradeTime: new Date()
        }
      }
    })

  } catch (error) {
    console.error('处理等级升级失败:', error)
  }
}

/**
 * 获取用户等级权益
 */
export function getUserBenefits(level: CreditLevel) {
  return LEVEL_CONFIG[level].benefits
}

/**
 * 批量更新所有用户的信用等级
 */
export async function batchUpdateCreditLevels(limit: number = 100): Promise<{
  processed: number
  upgraded: number
  errors: number
}> {
  try {
    let processed = 0
    let upgraded = 0
    let errors = 0
    let offset = 0

    while (true) {
      const users = await prisma.user.findMany({
        skip: offset,
        take: limit,
        select: { id: true }
      })

      if (users.length === 0) break

      for (const user of users) {
        try {
          const result = await updateUserCreditLevel(user.id)
          processed++
          if (result.upgraded) {
            upgraded++
          }
        } catch (error) {
          console.error(`更新用户 ${user.id} 信用等级失败:`, error)
          errors++
        }
      }

      offset += limit
    }

    return { processed, upgraded, errors }

  } catch (error) {
    console.error('批量更新信用等级失败:', error)
    throw error
  }
}

/**
 * 获取等级分布统计
 */
export async function getCreditLevelStats() {
  try {
    const stats = await prisma.user.groupBy({
      by: ['creditLevel'],
      _count: {
        id: true
      }
    })

    const distribution = stats.reduce((acc, stat) => {
      acc[stat.creditLevel] = stat._count.id
      return acc
    }, {} as Record<string, number>)

    // 确保所有等级都有数据
    Object.values(CreditLevel).forEach(level => {
      if (!distribution[level]) {
        distribution[level] = 0
      }
    })

    return distribution

  } catch (error) {
    console.error('获取等级分布统计失败:', error)
    throw error
  }
}
