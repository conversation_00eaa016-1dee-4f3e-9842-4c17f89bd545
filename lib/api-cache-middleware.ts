import { NextRequest, NextResponse } from 'next/server'
import { cache, CACHE_TTL } from './cache-fallback'
import { performanceMonitor } from './performance-monitor'

// 缓存配置类型
interface CacheConfig {
  ttl?: number
  key?: string
  condition?: (req: NextRequest) => boolean
  skipCache?: (req: NextRequest) => boolean
  varyBy?: string[]
}

// 默认缓存配置
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  ttl: CACHE_TTL.SHORT,
  condition: () => true,
  skipCache: (req) => req.method !== 'GET',
  varyBy: [],
}

// 生成缓存键
function generateCacheKey(req: NextRequest, config: CacheConfig): string {
  const url = new URL(req.url)
  const baseKey = config.key || `${req.method}:${url.pathname}`
  
  // 添加查询参数
  const searchParams = url.searchParams.toString()
  const queryKey = searchParams ? `:${searchParams}` : ''
  
  // 添加变化因子
  const varyKey = config.varyBy?.map(header => {
    const value = req.headers.get(header)
    return value ? `${header}:${value}` : ''
  }).filter(Boolean).join(':') || ''
  
  return `api:${baseKey}${queryKey}${varyKey ? `:${varyKey}` : ''}`
}

// API缓存中间件
export function withApiCache(config: CacheConfig = {}) {
  const finalConfig = { ...DEFAULT_CACHE_CONFIG, ...config }
  
  return function (handler: (req: NextRequest) => Promise<NextResponse>) {
    return async function (req: NextRequest): Promise<NextResponse> {
      const startTime = performance.now()
      
      try {
        // 检查是否应该跳过缓存
        if (finalConfig.skipCache?.(req) || !finalConfig.condition?.(req)) {
          const response = await handler(req)
          const duration = performance.now() - startTime
          performanceMonitor.addApiMetric(
            new URL(req.url).pathname,
            req.method,
            duration,
            response.status
          )
          return response
        }

        const cacheKey = generateCacheKey(req, finalConfig)
        
        // 尝试从缓存获取
        const cachedResponse = await cache.get<{
          status: number
          headers: Record<string, string>
          body: any
        }>(cacheKey)
        
        if (cachedResponse) {
          const duration = performance.now() - startTime
          performanceMonitor.addApiMetric(
            new URL(req.url).pathname,
            req.method,
            duration,
            cachedResponse.status,
            'cached'
          )
          
          // 返回缓存的响应
          return new NextResponse(JSON.stringify(cachedResponse.body), {
            status: cachedResponse.status,
            headers: {
              ...cachedResponse.headers,
              'X-Cache': 'HIT',
              'Content-Type': 'application/json',
            },
          })
        }

        // 执行原始处理器
        const response = await handler(req)
        const duration = performance.now() - startTime
        
        performanceMonitor.addApiMetric(
          new URL(req.url).pathname,
          req.method,
          duration,
          response.status
        )

        // 只缓存成功的响应
        if (response.status >= 200 && response.status < 300) {
          try {
            const responseBody = await response.json()
            const headersObj: Record<string, string> = {}
            
            response.headers.forEach((value, key) => {
              headersObj[key] = value
            })

            await cache.set(cacheKey, {
              status: response.status,
              headers: headersObj,
              body: responseBody,
            }, finalConfig.ttl)

            // 返回新的响应（因为原响应已被消费）
            return new NextResponse(JSON.stringify(responseBody), {
              status: response.status,
              headers: {
                ...headersObj,
                'X-Cache': 'MISS',
                'Content-Type': 'application/json',
              },
            })
          } catch (error) {
            // 如果无法解析JSON，直接返回原响应
            return new NextResponse(response.body, {
              status: response.status,
              headers: {
                ...Object.fromEntries(response.headers.entries()),
                'X-Cache': 'MISS',
              },
            })
          }
        }

        return response
      } catch (error) {
        const duration = performance.now() - startTime
        performanceMonitor.addApiMetric(
          new URL(req.url).pathname,
          req.method,
          duration,
          500
        )
        throw error
      }
    }
  }
}

// 预定义的缓存配置
export const cacheConfigs = {
  // 短期缓存（1分钟）
  short: { ttl: CACHE_TTL.SHORT },
  
  // 中期缓存（5分钟）
  medium: { ttl: CACHE_TTL.MEDIUM },
  
  // 长期缓存（30分钟）
  long: { ttl: CACHE_TTL.LONG },
  
  // 用户相关数据缓存
  user: {
    ttl: CACHE_TTL.MEDIUM,
    varyBy: ['authorization'],
    condition: (req: NextRequest) => req.headers.has('authorization'),
  },
  
  // 商品列表缓存
  products: {
    ttl: CACHE_TTL.SHORT,
    condition: (req: NextRequest) => {
      const url = new URL(req.url)
      return !url.searchParams.has('nocache')
    },
  },
  
  // 搜索结果缓存
  search: {
    ttl: CACHE_TTL.SHORT,
    key: 'search',
  },
  
  // 统计数据缓存
  stats: {
    ttl: CACHE_TTL.LONG,
    condition: (req: NextRequest) => req.method === 'GET',
  },
}

// 缓存失效工具
export class CacheInvalidator {
  // 失效用户相关缓存
  static async invalidateUser(userId: string) {
    await Promise.all([
      cache.delPattern(`api:*user*${userId}*`),
      cache.delPattern(`user:${userId}*`),
    ])
  }

  // 失效商品相关缓存
  static async invalidateProduct(productId: string) {
    await Promise.all([
      cache.delPattern(`api:*product*${productId}*`),
      cache.delPattern(`product:${productId}*`),
      cache.delPattern(`api:*products*`), // 商品列表
    ])
  }

  // 失效订单相关缓存
  static async invalidateOrder(orderId: string, userId?: string) {
    const patterns = [
      `api:*order*${orderId}*`,
      `order:${orderId}*`,
    ]
    
    if (userId) {
      patterns.push(`api:*user*${userId}*order*`)
    }
    
    await Promise.all(patterns.map(pattern => cache.delPattern(pattern)))
  }

  // 失效搜索缓存
  static async invalidateSearch() {
    await cache.delPattern('search:*')
    await cache.delPattern('api:*search*')
  }

  // 失效所有API缓存
  static async invalidateAll() {
    await cache.delPattern('api:*')
  }
}

// 缓存预热
export class CacheWarmer {
  // 预热热门商品
  static async warmupProducts() {
    // 实现商品预热逻辑
  }

  // 预热用户数据
  static async warmupUsers() {
    // 实现用户数据预热逻辑
  }

  // 预热统计数据
  static async warmupStats() {
    // 实现统计数据预热逻辑
  }
}

// 缓存统计
export async function getCacheStats() {
  const stats = await cache.getStatsCache('api-cache')
  return stats || {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
  }
}
