import { PrismaClient } from '@prisma/client'

// 全局Prisma客户端实例，避免重复创建
declare global {
  var __prisma: PrismaClient | undefined
}

// 性能优化的Prisma配置
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })
}

// 单例模式，避免多次创建客户端
const prisma = globalThis.__prisma || createPrismaClient()

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

// 优化的查询方法
export const optimizedQueries = {
  // 获取用户信息（带缓存）
  async getUserById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        creditScore: true,
        role: true,
        status: true,
        createdAt: true,
      },
    })
  },

  // 获取商品列表（分页优化）
  async getProducts(page: number = 1, limit: number = 20, filters?: any) {
    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {
      reviewStatus: 'APPROVED'
    }

    // 状态筛选
    if (filters?.status) {
      where.status = filters.status
    } else if (!filters?.sellerId) {
      where.status = 'AVAILABLE'
    }

    // 城市筛选
    if (filters?.city && filters.city.trim() !== '') {
      where.city = filters.city.trim()
    }

    // 分类筛选
    if (filters?.category && filters.category !== 'ALL') {
      where.category = filters.category
    }

    // 条件筛选
    if (filters?.condition && filters.condition !== 'ALL') {
      where.condition = filters.condition
    }

    // 关键词搜索
    if (filters?.keyword && filters.keyword.trim() !== '') {
      where.title = {
        contains: filters.keyword.trim(),
        mode: 'insensitive'
      }
    }

    // 按卖家ID筛选
    if (filters?.sellerId) {
      where.sellerId = filters.sellerId
    }

    // 价格范围筛选
    if (filters?.minPrice || filters?.maxPrice) {
      where.price = {}
      if (filters.minPrice) {
        where.price.gte = parseFloat(filters.minPrice)
      }
      if (filters.maxPrice) {
        where.price.lte = parseFloat(filters.maxPrice)
      }
    }

    // 构建排序条件
    let orderBy: any = { createdAt: 'desc' }

    switch (filters?.sortBy) {
      case 'newest':
        orderBy = { createdAt: 'desc' }
        break
      case 'oldest':
        orderBy = { createdAt: 'asc' }
        break
      case 'price_low':
        orderBy = { price: 'asc' }
        break
      case 'price_high':
        orderBy = { price: 'desc' }
        break
      default:
        orderBy = { createdAt: 'desc' }
    }

    const [products, total, availableCities] = await Promise.all([
      prisma.product.findMany({
        where,
        select: {
          id: true,
          title: true,
          description: true,
          images: true,
          price: true,
          city: true,
          district: true,
          stock: true,
          category: true,
          condition: true,
          createdAt: true,
          seller: {
            select: {
              id: true,
              userId: true,
              name: true,
              avatar: true,
              creditScore: true,
              depositBalance: true,
              city: true,
              district: true,
            },
          },
          _count: {
            select: {
              favorites: true,
              reviews: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy,
      }),
      prisma.product.count({ where }),
      // 获取可用城市列表
      prisma.product.findMany({
        where: {
          status: 'AVAILABLE',
          reviewStatus: 'APPROVED',
          city: { not: null }
        },
        select: { city: true },
        distinct: ['city']
      })
    ])

    const cities = availableCities
      .map(p => p.city)
      .filter(city => city !== null)
      .sort()

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      filters: {
        availableCities: cities
      }
    }
  },

  // 获取订单详情（优化关联查询）
  async getOrderById(id: string, userId: string) {
    return await prisma.order.findFirst({
      where: {
        id,
        OR: [
          { buyerId: userId },
          { sellerId: userId },
        ],
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            images: true,
            price: true,
          },
        },
        buyer: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                title: true,
                images: true,
              },
            },
            variant: {
              select: {
                id: true,
                sku: true,
                attributes: true,
              },
            },
          },
        },
        messages: {
          select: {
            id: true,
            content: true,
            messageType: true,
            status: true,
            createdAt: true,
            sender: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
          take: 50, // 限制消息数量
        },
      },
    })
  },

  // 批量查询优化
  async getUsersWithStats(userIds: string[]) {
    return await prisma.user.findMany({
      where: {
        id: {
          in: userIds,
        },
      },
      select: {
        id: true,
        name: true,
        avatar: true,
        creditScore: true,
        _count: {
          select: {
            products: true,
            ordersAsBuyer: true,
            ordersAsSeller: true,
            reviews: true,
          },
        },
      },
    })
  },
}

// 事务优化
export const optimizedTransactions = {
  // 创建订单事务
  async createOrder(orderData: any) {
    return await prisma.$transaction(async (tx) => {
      // 检查库存
      const product = await tx.product.findUnique({
        where: { id: orderData.productId },
        select: { stock: true, price: true },
      })

      if (!product || product.stock < orderData.quantity) {
        throw new Error('库存不足')
      }

      // 创建订单
      const order = await tx.order.create({
        data: orderData,
      })

      // 更新库存
      await tx.product.update({
        where: { id: orderData.productId },
        data: {
          stock: {
            decrement: orderData.quantity,
          },
        },
      })

      return order
    })
  },

  // 完成订单事务
  async completeOrder(orderId: string) {
    return await prisma.$transaction(async (tx) => {
      const order = await tx.order.update({
        where: { id: orderId },
        data: {
          status: 'COMPLETED',
          receivedAt: new Date(),
        },
        include: {
          buyer: true,
          seller: true,
        },
      })

      // 更新信用积分
      await Promise.all([
        tx.user.update({
          where: { id: order.buyerId },
          data: {
            creditScore: {
              increment: 1,
            },
          },
        }),
        tx.user.update({
          where: { id: order.sellerId },
          data: {
            creditScore: {
              increment: 2,
            },
          },
        }),
      ])

      return order
    })
  },
}

// 连接管理
export const prismaConnection = {
  async disconnect() {
    await prisma.$disconnect()
  },

  async connect() {
    await prisma.$connect()
  },

  // 健康检查
  async healthCheck() {
    try {
      await prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      console.error('Database health check failed:', error)
      return false
    }
  },
}

export default prisma
