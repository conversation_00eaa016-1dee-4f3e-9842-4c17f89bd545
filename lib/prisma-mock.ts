// 临时的Prisma客户端模拟，用于测试
// 当Prisma引擎下载完成后，可以删除此文件

export const prisma = {
  user: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  product: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  order: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  message: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  demand: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  demandOffer: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  review: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  address: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({})
  },
  $transaction: async (callback: any) => {
    return callback(prisma)
  }
}

console.log('⚠️  使用模拟Prisma客户端 - 仅用于测试界面')
console.log('📥 请等待Prisma引擎下载完成后重新启动服务器')
