import { prisma } from '@/lib/prisma'

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED', 
  BANNED = 'BANNED'
}

// 用户状态检查结果接口
export interface UserStatusCheck {
  isActive: boolean
  isBanned: boolean
  isSuspended: boolean
  banInfo?: {
    bannedAt: Date
    bannedUntil: Date | null
    banReason: string | null
    bannedBy: string | null
    isExpired: boolean
    remainingTime?: number // 剩余封禁时间（毫秒）
  }
}

/**
 * 检查用户当前状态
 * @param userId 用户ID
 * @returns 用户状态检查结果
 */
export async function checkUserStatus(userId: string): Promise<UserStatusCheck> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        status: true,
        bannedAt: true,
        bannedUntil: true,
        banReason: true,
        bannedBy: true
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const now = new Date()
    const isBanned = user.status === UserStatus.BANNED
    const isSuspended = user.status === UserStatus.SUSPENDED
    
    let banInfo: any = undefined
    
    if (isBanned && user.bannedAt) {
      const isExpired = user.bannedUntil ? now > user.bannedUntil : false
      const remainingTime = user.bannedUntil ? Math.max(0, user.bannedUntil.getTime() - now.getTime()) : undefined
      
      banInfo = {
        bannedAt: user.bannedAt,
        bannedUntil: user.bannedUntil,
        banReason: user.banReason,
        bannedBy: user.bannedBy,
        isExpired,
        remainingTime
      }

      // 如果封禁已过期，自动解封
      if (isExpired) {
        await autoUnbanUser(userId)
        return {
          isActive: true,
          isBanned: false,
          isSuspended: false
        }
      }
    }

    return {
      isActive: user.status === UserStatus.ACTIVE,
      isBanned: isBanned && (!banInfo?.isExpired),
      isSuspended,
      banInfo
    }
  } catch (error) {
    console.error('检查用户状态失败:', error)
    throw error
  }
}

/**
 * 自动解封过期的用户
 * @param userId 用户ID
 */
export async function autoUnbanUser(userId: string): Promise<void> {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        status: UserStatus.ACTIVE,
        bannedAt: null,
        bannedUntil: null,
        banReason: null,
        bannedBy: null
      }
    })
    console.log(`用户 ${userId} 封禁已过期，自动解封`)
  } catch (error) {
    console.error('自动解封用户失败:', error)
    throw error
  }
}

/**
 * 检查用户是否可以进行交易
 * @param userId 用户ID
 * @returns 是否可以交易
 */
export async function canUserTrade(userId: string): Promise<boolean> {
  const status = await checkUserStatus(userId)
  return status.isActive && !status.isBanned && !status.isSuspended
}

/**
 * 检查用户是否可以创建订单
 * @param userId 用户ID
 * @returns 是否可以创建订单
 */
export async function canUserCreateOrder(userId: string): Promise<boolean> {
  return await canUserTrade(userId)
}

/**
 * 格式化剩余封禁时间
 * @param remainingTime 剩余时间（毫秒）
 * @returns 格式化的时间字符串
 */
export function formatRemainingBanTime(remainingTime: number): string {
  if (remainingTime <= 0) return '已过期'
  
  const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24))
  const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) {
    return `${days}天${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 获取封禁状态的中文描述
 * @param status 用户状态
 * @returns 状态描述
 */
export function getUserStatusText(status: string): string {
  switch (status) {
    case UserStatus.ACTIVE:
      return '正常'
    case UserStatus.SUSPENDED:
      return '暂停'
    case UserStatus.BANNED:
      return '封禁'
    default:
      return '未知'
  }
}
