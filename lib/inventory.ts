import { prisma } from './prisma'
import { cache } from './cache-fallback'

/**
 * 清除商品相关的缓存
 * @param sellerId 卖家ID
 * @param productId 商品ID
 */
async function clearProductRelatedCache(sellerId: string, productId: string) {
  try {
    // 清除商品相关的API缓存
    await Promise.all([
      // 清除商品列表缓存
      cache.delPattern(`api:products*sellerId=${sellerId}*`),
      cache.delPattern(`api:products*sellerId%3D${sellerId}*`),

      // 清除特定商品缓存
      cache.invalidateProductCache(productId),

      // 清除用户相关缓存
      cache.invalidateUserCache(sellerId),

      // 清除搜索缓存
      cache.delPattern('api:search*'),

      // 清除统计缓存
      cache.delPattern('api:stats*')
    ])

    console.log(`已清除商品 ${productId} 和卖家 ${sellerId} 的相关缓存`)
  } catch (error) {
    console.error('清除缓存失败:', error)
  }
}

/**
 * 检查商品库存并自动下架库存不足的商品
 * @param productId 商品ID
 * @returns 返回更新后的商品信息和是否发生了状态变更
 */
export async function checkAndUpdateProductStock(productId: string) {
  try {
    // 获取商品当前信息
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!product) {
      throw new Error('商品不存在')
    }

    // 检查库存是否需要下架
    const shouldDeactivate = product.stock <= 0 && product.status === 'AVAILABLE'
    
    if (shouldDeactivate) {
      // 自动下架商品
      const updatedProduct = await prisma.product.update({
        where: { id: productId },
        data: {
          status: 'SOLD_OUT'
        },
        include: {
          seller: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      console.log(`商品 ${product.title} (ID: ${productId}) 库存不足，已自动下架`)

      // 清除相关缓存
      await clearProductRelatedCache(updatedProduct.sellerId, productId)

      return {
        product: updatedProduct,
        statusChanged: true,
        message: '商品库存不足，已自动下架'
      }
    }

    return {
      product,
      statusChanged: false,
      message: null
    }
  } catch (error) {
    console.error('检查商品库存失败:', error)
    throw error
  }
}

/**
 * 批量检查所有商品库存并自动下架库存不足的商品
 * @returns 返回下架的商品列表
 */
export async function batchCheckProductStock() {
  try {
    // 查找所有库存不足但仍在售的商品
    const outOfStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lte: 0
        },
        status: 'AVAILABLE'
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (outOfStockProducts.length === 0) {
      return []
    }

    // 批量更新商品状态为SOLD_OUT
    const productIds = outOfStockProducts.map(p => p.id)
    
    await prisma.product.updateMany({
      where: {
        id: {
          in: productIds
        }
      },
      data: {
        status: 'SOLD_OUT'
      }
    })

    console.log(`批量下架了 ${outOfStockProducts.length} 个库存不足的商品`)
    
    return outOfStockProducts
  } catch (error) {
    console.error('批量检查商品库存失败:', error)
    throw error
  }
}

/**
 * 创建库存不足通知
 * @param productId 商品ID
 * @param sellerId 卖家ID
 * @param message 通知消息
 */
export async function createStockNotification(productId: string, sellerId: string, message: string) {
  try {
    // 这里可以扩展为发送邮件、站内消息等
    // 目前先记录到控制台
    console.log(`库存通知 - 卖家: ${sellerId}, 商品: ${productId}, 消息: ${message}`)
    
    // 未来可以添加通知表或发送邮件逻辑
    // await prisma.notification.create({
    //   data: {
    //     userId: sellerId,
    //     type: 'STOCK_ALERT',
    //     title: '商品库存不足',
    //     message,
    //     productId,
    //     read: false
    //   }
    // })
    
    return true
  } catch (error) {
    console.error('创建库存通知失败:', error)
    return false
  }
}

/**
 * 检查商品是否可以购买
 * @param productId 商品ID
 * @param quantity 购买数量
 * @returns 返回检查结果
 */
export async function checkProductAvailability(productId: string, quantity: number = 1) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        sellerId: true
      }
    })

    if (!product) {
      return {
        available: false,
        reason: '商品不存在'
      }
    }

    if (product.status !== 'AVAILABLE') {
      return {
        available: false,
        reason: '商品不可购买'
      }
    }

    if (product.stock < quantity) {
      return {
        available: false,
        reason: '库存不足'
      }
    }

    return {
      available: true,
      product
    }
  } catch (error) {
    console.error('检查商品可用性失败:', error)
    return {
      available: false,
      reason: '系统错误'
    }
  }
}

/**
 * 安全地减少商品库存
 * @param productId 商品ID
 * @param quantity 减少数量
 * @returns 返回操作结果
 */
export async function decrementProductStock(productId: string, quantity: number = 1) {
  try {
    // 使用事务确保库存操作的原子性
    const result = await prisma.$transaction(async (tx) => {
      // 先检查当前库存
      const product = await tx.product.findUnique({
        where: { id: productId },
        select: {
          id: true,
          title: true,
          stock: true,
          status: true
        }
      })

      if (!product) {
        throw new Error('商品不存在')
      }

      if (product.stock < quantity) {
        throw new Error('库存不足')
      }

      // 减少库存
      const updatedProduct = await tx.product.update({
        where: { id: productId },
        data: {
          stock: {
            decrement: quantity
          }
        }
      })

      // 检查是否需要自动下架
      if (updatedProduct.stock <= 0 && updatedProduct.status === 'AVAILABLE') {
        await tx.product.update({
          where: { id: productId },
          data: {
            status: 'SOLD_OUT'
          }
        })
        
        return {
          success: true,
          autoDeactivated: true,
          newStock: updatedProduct.stock,
          message: '库存已扣减，商品已自动下架'
        }
      }

      return {
        success: true,
        autoDeactivated: false,
        newStock: updatedProduct.stock,
        message: '库存已扣减'
      }
    })

    return result
  } catch (error) {
    console.error('减少商品库存失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '系统错误'
    }
  }
}
