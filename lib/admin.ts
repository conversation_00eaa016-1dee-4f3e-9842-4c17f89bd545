import { getServerSession } from 'next-auth/next'
import { authOptions } from './auth'
import { prisma } from './prisma'

export async function checkAdminPermission() {
  console.log('🔍 [checkAdminPermission] 开始检查管理员权限...')

  const session = await getServerSession(authOptions)
  console.log('📋 [checkAdminPermission] 会话信息:', {
    hasSession: !!session,
    userId: session?.user?.id,
    userEmail: session?.user?.email
  })

  if (!session?.user?.id) {
    console.log('❌ [checkAdminPermission] 没有有效会话')
    return { isAdmin: false, user: null }
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      email: true,
      name: true,
      role: true
    }
  })

  console.log('👤 [checkAdminPermission] 用户信息:', user)

  if (!user || user.role !== 'ADMIN') {
    console.log('❌ [checkAdminPermission] 用户不是管理员，角色:', user?.role)
    return { isAdmin: false, user }
  }

  console.log('✅ [checkAdminPermission] 管理员权限验证通过')
  return { isAdmin: true, user }
}

export async function requireAdmin() {
  const { isAdmin, user } = await checkAdminPermission()
  
  if (!isAdmin) {
    throw new Error('需要管理员权限')
  }
  
  return user
}
