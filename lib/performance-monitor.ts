import { performance } from 'perf_hooks'
import { cache } from './cache'

// 性能指标类型
interface PerformanceMetric {
  name: string
  duration: number
  timestamp: number
  metadata?: any
}

interface DatabaseMetric {
  query: string
  duration: number
  timestamp: number
  params?: any
}

interface ApiMetric {
  endpoint: string
  method: string
  duration: number
  statusCode: number
  timestamp: number
  userId?: string
}

// 性能监控管理器
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private dbMetrics: DatabaseMetric[] = []
  private apiMetrics: ApiMetric[] = []
  private maxMetrics = 1000 // 最大保存指标数量

  // 开始计时
  startTimer(name: string): () => number {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.addMetric(name, duration)
      return duration
    }
  }

  // 添加性能指标
  addMetric(name: string, duration: number, metadata?: any) {
    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: Date.now(),
      metadata,
    }

    this.metrics.push(metric)
    
    // 保持指标数量在限制内
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // 如果性能较差，记录警告
    if (duration > 1000) {
      console.warn(`Performance warning: ${name} took ${duration.toFixed(2)}ms`)
    }
  }

  // 添加数据库查询指标
  addDbMetric(query: string, duration: number, params?: any) {
    const metric: DatabaseMetric = {
      query,
      duration,
      timestamp: Date.now(),
      params,
    }

    this.dbMetrics.push(metric)
    
    if (this.dbMetrics.length > this.maxMetrics) {
      this.dbMetrics = this.dbMetrics.slice(-this.maxMetrics)
    }

    // 慢查询警告
    if (duration > 500) {
      console.warn(`Slow query detected: ${query} took ${duration.toFixed(2)}ms`)
    }
  }

  // 添加API指标
  addApiMetric(endpoint: string, method: string, duration: number, statusCode: number, userId?: string) {
    const metric: ApiMetric = {
      endpoint,
      method,
      duration,
      statusCode,
      timestamp: Date.now(),
      userId,
    }

    this.apiMetrics.push(metric)
    
    if (this.apiMetrics.length > this.maxMetrics) {
      this.apiMetrics = this.apiMetrics.slice(-this.maxMetrics)
    }

    // API性能警告
    if (duration > 2000) {
      console.warn(`Slow API: ${method} ${endpoint} took ${duration.toFixed(2)}ms`)
    }
  }

  // 获取性能统计
  getStats() {
    const now = Date.now()
    const oneHourAgo = now - 60 * 60 * 1000

    // 过滤最近一小时的数据
    const recentMetrics = this.metrics.filter(m => m.timestamp > oneHourAgo)
    const recentDbMetrics = this.dbMetrics.filter(m => m.timestamp > oneHourAgo)
    const recentApiMetrics = this.apiMetrics.filter(m => m.timestamp > oneHourAgo)

    return {
      general: this.calculateStats(recentMetrics.map(m => m.duration)),
      database: this.calculateStats(recentDbMetrics.map(m => m.duration)),
      api: this.calculateStats(recentApiMetrics.map(m => m.duration)),
      counts: {
        totalRequests: recentMetrics.length,
        dbQueries: recentDbMetrics.length,
        apiCalls: recentApiMetrics.length,
      },
      slowQueries: recentDbMetrics.filter(m => m.duration > 500).length,
      slowApis: recentApiMetrics.filter(m => m.duration > 2000).length,
    }
  }

  // 计算统计数据
  private calculateStats(durations: number[]) {
    if (durations.length === 0) {
      return { avg: 0, min: 0, max: 0, p95: 0, p99: 0 }
    }

    const sorted = durations.sort((a, b) => a - b)
    const sum = durations.reduce((a, b) => a + b, 0)

    return {
      avg: sum / durations.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    }
  }

  // 获取慢查询
  getSlowQueries(limit: number = 10) {
    return this.dbMetrics
      .filter(m => m.duration > 500)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  // 获取慢API
  getSlowApis(limit: number = 10) {
    return this.apiMetrics
      .filter(m => m.duration > 2000)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  // 清理旧数据
  cleanup() {
    const oneHourAgo = Date.now() - 60 * 60 * 1000
    
    this.metrics = this.metrics.filter(m => m.timestamp > oneHourAgo)
    this.dbMetrics = this.dbMetrics.filter(m => m.timestamp > oneHourAgo)
    this.apiMetrics = this.apiMetrics.filter(m => m.timestamp > oneHourAgo)
  }

  // 导出数据到缓存
  async exportToCache() {
    const stats = this.getStats()
    await cache.setStatsCache('performance', stats, 300) // 5分钟缓存
  }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitor()

// 性能装饰器
export function monitor(name?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyName}`

    descriptor.value = async function (...args: any[]) {
      const endTimer = performanceMonitor.startTimer(metricName)
      
      try {
        const result = await method.apply(this, args)
        endTimer()
        return result
      } catch (error) {
        endTimer()
        throw error
      }
    }
  }
}

// API监控中间件
export function apiMonitorMiddleware(req: any, res: any, next: any) {
  const start = performance.now()
  const originalSend = res.send

  res.send = function (data: any) {
    const duration = performance.now() - start
    const endpoint = req.route?.path || req.path
    const method = req.method
    const statusCode = res.statusCode
    const userId = req.user?.id

    performanceMonitor.addApiMetric(endpoint, method, duration, statusCode, userId)
    
    return originalSend.call(this, data)
  }

  next()
}

// 数据库查询监控
export function monitorDbQuery<T>(
  queryFn: () => Promise<T>,
  queryName: string,
  params?: any
): Promise<T> {
  const start = performance.now()
  
  return queryFn().then(
    (result) => {
      const duration = performance.now() - start
      performanceMonitor.addDbMetric(queryName, duration, params)
      return result
    },
    (error) => {
      const duration = performance.now() - start
      performanceMonitor.addDbMetric(queryName, duration, params)
      throw error
    }
  )
}

// 内存使用监控
export function getMemoryUsage() {
  const usage = process.memoryUsage()
  return {
    rss: Math.round(usage.rss / 1024 / 1024), // MB
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
    external: Math.round(usage.external / 1024 / 1024), // MB
  }
}

// 系统资源监控
export function getSystemStats() {
  const cpuUsage = process.cpuUsage()
  const memUsage = getMemoryUsage()
  
  return {
    uptime: process.uptime(),
    cpu: {
      user: cpuUsage.user / 1000, // 转换为毫秒
      system: cpuUsage.system / 1000,
    },
    memory: memUsage,
    pid: process.pid,
    version: process.version,
  }
}

// 定期清理和导出
setInterval(() => {
  performanceMonitor.cleanup()
  performanceMonitor.exportToCache()
}, 5 * 60 * 1000) // 每5分钟执行一次
