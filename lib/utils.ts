import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证币安UID格式（纯数字）
export function isValidBinanceUID(uid: string): boolean {
  const uidRegex = /^\d+$/
  return uidRegex.test(uid) && uid.length >= 8
}

// 验证BNB钱包地址格式
export function isValidBNBAddress(address: string): boolean {
  const bnbRegex = /^0x[a-fA-F0-9]{40}$/
  return bnbRegex.test(address)
}

// 生成随机验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// 格式化USDT金额
export function formatUSDT(amount: number): string {
  return `${amount.toFixed(2)} USDT`
}

// 格式化保证金数量显示
export function formatDeposit(amount: number): string {
  if (amount < 1000) {
    return amount.toString()
  } else if (amount < 1000000) {
    const value = amount / 1000
    const rounded = Math.round(value * 10) / 10 // 四舍五入到一位小数
    if (rounded >= 1000) {
      // 如果四舍五入后>=1000，则进位到m单位
      return '1m'
    }
    if (rounded === Math.floor(rounded)) {
      return `${Math.floor(rounded)}k`
    } else {
      return `${rounded.toFixed(1)}k`
    }
  } else if (amount < 1000000000) {
    const value = amount / 1000000
    const rounded = Math.round(value * 10) / 10 // 四舍五入到一位小数
    if (rounded >= 1000) {
      // 如果四舍五入后>=1000，则进位到b单位
      return '1b'
    }
    if (rounded === Math.floor(rounded)) {
      return `${Math.floor(rounded)}m`
    } else {
      return `${rounded.toFixed(1)}m`
    }
  } else {
    const value = amount / 1000000000
    const rounded = Math.round(value * 10) / 10 // 四舍五入到一位小数
    if (rounded === Math.floor(rounded)) {
      return `${Math.floor(rounded)}b`
    } else {
      return `${rounded.toFixed(1)}b`
    }
  }
}

// 生成订单号
export function generateOrderNumber(): string {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `ODR${date}-${random}`
}
