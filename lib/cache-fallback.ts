// 简化的缓存系统，支持Redis和内存缓存回退

// 内存缓存
const memoryCache = new Map<string, { value: any; expiry: number }>()

// 缓存时间配置（秒）
export const CACHE_TTL = {
  SHORT: 60, // 1分钟
  MEDIUM: 300, // 5分钟
  LONG: 1800, // 30分钟
  VERY_LONG: 3600, // 1小时
  DAY: 86400, // 24小时
} as const

// 缓存键前缀
export const CACHE_PREFIXES = {
  USER: 'user:',
  PRODUCT: 'product:',
  ORDER: 'order:',
  SESSION: 'session:',
  API: 'api:',
  SEARCH: 'search:',
  STATS: 'stats:',
} as const

// 简化的缓存管理类
export class SimpleCacheManager {
  // 内存缓存操作
  private memoryGet(key: string): any | null {
    const item = memoryCache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      memoryCache.delete(key)
      return null
    }
    
    return item.value
  }

  private memorySet(key: string, value: any, ttl: number): void {
    const expiry = Date.now() + (ttl * 1000)
    memoryCache.set(key, { value, expiry })
  }

  private memoryDel(key: string): void {
    memoryCache.delete(key)
  }

  private memoryExists(key: string): boolean {
    const item = memoryCache.get(key)
    if (!item) return false
    
    if (Date.now() > item.expiry) {
      memoryCache.delete(key)
      return false
    }
    
    return true
  }

  // 基础缓存操作（只使用内存缓存）
  async get<T>(key: string): Promise<T | null> {
    try {
      return this.memoryGet(key)
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  async set(key: string, value: any, ttl: number = CACHE_TTL.MEDIUM): Promise<boolean> {
    try {
      this.memorySet(key, value, ttl)
      return true
    } catch (error) {
      console.error('Cache set error:', error)
      return false
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      this.memoryDel(key)
      return true
    } catch (error) {
      console.error('Cache delete error:', error)
      return false
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      return this.memoryExists(key)
    } catch (error) {
      console.error('Cache exists error:', error)
      return false
    }
  }

  // 批量操作
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      return keys.map(key => this.memoryGet(key))
    } catch (error) {
      console.error('Cache mget error:', error)
      return keys.map(() => null)
    }
  }

  async mset(keyValues: Record<string, any>, ttl: number = CACHE_TTL.MEDIUM): Promise<boolean> {
    try {
      Object.entries(keyValues).forEach(([key, value]) => {
        this.memorySet(key, value, ttl)
      })
      return true
    } catch (error) {
      console.error('Cache mset error:', error)
      return false
    }
  }

  // 模式删除（简化版）
  async delPattern(pattern: string): Promise<number> {
    try {
      let count = 0
      const regex = new RegExp(pattern.replace(/\*/g, '.*'))
      
      for (const key of memoryCache.keys()) {
        if (regex.test(key)) {
          memoryCache.delete(key)
          count++
        }
      }
      
      return count
    } catch (error) {
      console.error('Cache delete pattern error:', error)
      return 0
    }
  }

  // 用户缓存
  async getUserCache(userId: string) {
    return this.get(`${CACHE_PREFIXES.USER}${userId}`)
  }

  async setUserCache(userId: string, userData: any, ttl: number = CACHE_TTL.LONG) {
    return this.set(`${CACHE_PREFIXES.USER}${userId}`, userData, ttl)
  }

  async invalidateUserCache(userId: string) {
    return this.del(`${CACHE_PREFIXES.USER}${userId}`)
  }

  // 商品缓存
  async getProductCache(productId: string) {
    return this.get(`${CACHE_PREFIXES.PRODUCT}${productId}`)
  }

  async setProductCache(productId: string, productData: any, ttl: number = CACHE_TTL.MEDIUM) {
    return this.set(`${CACHE_PREFIXES.PRODUCT}${productId}`, productData, ttl)
  }

  async invalidateProductCache(productId: string) {
    return this.del(`${CACHE_PREFIXES.PRODUCT}${productId}`)
  }

  // API响应缓存
  async getApiCache(endpoint: string, params?: string) {
    const key = params ? `${CACHE_PREFIXES.API}${endpoint}:${params}` : `${CACHE_PREFIXES.API}${endpoint}`
    return this.get(key)
  }

  async setApiCache(endpoint: string, data: any, ttl: number = CACHE_TTL.SHORT, params?: string) {
    const key = params ? `${CACHE_PREFIXES.API}${endpoint}:${params}` : `${CACHE_PREFIXES.API}${endpoint}`
    return this.set(key, data, ttl)
  }

  // 搜索结果缓存
  async getSearchCache(query: string, filters?: any) {
    const cacheKey = filters ? `${query}:${JSON.stringify(filters)}` : query
    return this.get(`${CACHE_PREFIXES.SEARCH}${cacheKey}`)
  }

  async setSearchCache(query: string, results: any, ttl: number = CACHE_TTL.SHORT, filters?: any) {
    const cacheKey = filters ? `${query}:${JSON.stringify(filters)}` : query
    return this.set(`${CACHE_PREFIXES.SEARCH}${cacheKey}`, results, ttl)
  }

  // 统计数据缓存
  async getStatsCache(type: string) {
    return this.get(`${CACHE_PREFIXES.STATS}${type}`)
  }

  async setStatsCache(type: string, stats: any, ttl: number = CACHE_TTL.LONG) {
    return this.set(`${CACHE_PREFIXES.STATS}${type}`, stats, ttl)
  }

  // 会话缓存
  async getSessionCache(sessionId: string) {
    return this.get(`${CACHE_PREFIXES.SESSION}${sessionId}`)
  }

  async setSessionCache(sessionId: string, sessionData: any, ttl: number = CACHE_TTL.VERY_LONG) {
    return this.set(`${CACHE_PREFIXES.SESSION}${sessionId}`, sessionData, ttl)
  }

  async invalidateSessionCache(sessionId: string) {
    return this.del(`${CACHE_PREFIXES.SESSION}${sessionId}`)
  }

  // 缓存预热（空实现）
  async warmupCache() {
    console.log('Memory cache warmup completed')
  }

  // 缓存清理
  async clearCache(pattern?: string) {
    try {
      if (pattern) {
        return await this.delPattern(pattern)
      } else {
        memoryCache.clear()
        return true
      }
    } catch (error) {
      console.error('Cache clear error:', error)
      return false
    }
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      // 测试内存缓存
      const testKey = 'health_check_test'
      await this.set(testKey, 'test', 1)
      const result = await this.get(testKey)
      await this.del(testKey)
      return result === 'test'
    } catch (error) {
      console.error('Cache health check failed:', error)
      return false
    }
  }

  // 获取缓存统计
  getStats() {
    return {
      type: 'memory',
      size: memoryCache.size,
      keys: Array.from(memoryCache.keys()).slice(0, 10), // 只显示前10个键
    }
  }

  // 关闭连接（空实现）
  async disconnect() {
    // 内存缓存不需要断开连接
  }
}

// 单例实例
export const cache = new SimpleCacheManager()

console.log('ℹ️  Using memory cache (Redis not available)')
