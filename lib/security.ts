import { NextRequest } from 'next/server'
import { prisma } from './prisma'

// 安全操作类型
export enum SecurityAction {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  EMAIL_CHANGE = 'EMAIL_CHANGE',
  BINANCE_BIND = 'BINANCE_BIND',
  PROFILE_UPDATE = 'PROFILE_UPDATE',
  SECURITY_SETTINGS = 'SECURITY_SETTINGS',
  SESSION_CREATE = 'SESSION_CREATE',
  SESSION_DELETE = 'SESSION_DELETE',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY'
}

// 操作状态
export enum SecurityStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PENDING = 'PENDING'
}

// 记录安全日志
export async function recordSecurityLog(
  userId: string,
  action: string,
  description: string,
  status: string,
  request?: NextRequest,
  metadata?: any
) {
  try {
    const ipAddress = request ? getClientIP(request) : null
    const userAgent = request ? request.headers.get('user-agent') : null
    const location = metadata?.location || null

    await prisma.securityLog.create({
      data: {
        userId,
        action,
        description,
        status,
        ipAddress,
        userAgent,
        location,
        metadata
      }
    })
  } catch (error) {
    console.error('Record security log error:', error)
    // 不抛出错误，避免影响主要业务流程
  }
}

// 获取客户端IP地址
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  if (remoteAddr) {
    return remoteAddr
  }
  
  return 'unknown'
}

// 密码强度检查
export function checkPasswordStrength(password: string): {
  score: number
  feedback: string[]
  isStrong: boolean
} {
  const feedback: string[] = []
  let score = 0

  // 长度检查
  if (password.length >= 8) {
    score += 1
  } else {
    feedback.push('密码至少需要8个字符')
  }

  if (password.length >= 12) {
    score += 1
  }

  // 字符类型检查
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    feedback.push('需要包含小写字母')
  }

  if (/[A-Z]/.test(password)) {
    score += 1
  } else {
    feedback.push('需要包含大写字母')
  }

  if (/\d/.test(password)) {
    score += 1
  } else {
    feedback.push('需要包含数字')
  }

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1
  } else {
    feedback.push('建议包含特殊字符')
  }

  // 常见密码检查
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey'
  ]
  
  if (commonPasswords.includes(password.toLowerCase())) {
    score = Math.max(0, score - 2)
    feedback.push('请避免使用常见密码')
  }

  const isStrong = score >= 4 && password.length >= 8

  return {
    score: Math.min(score, 5),
    feedback,
    isStrong
  }
}

// 检查可疑活动
export async function checkSuspiciousActivity(
  userId: string,
  action: string,
  ipAddress?: string
): Promise<boolean> {
  try {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    // 检查1小时内的失败尝试次数
    const recentFailures = await prisma.securityLog.count({
      where: {
        userId,
        action,
        status: SecurityStatus.FAILED,
        createdAt: { gte: oneHourAgo }
      }
    })

    // 如果1小时内失败超过5次，标记为可疑
    if (recentFailures >= 5) {
      await recordSecurityLog(
        userId,
        SecurityAction.SUSPICIOUS_ACTIVITY,
        `检测到可疑活动：${action} 在1小时内失败${recentFailures}次`,
        SecurityStatus.SUCCESS
      )
      return true
    }

    // 检查24小时内来自不同IP的登录
    if (action === SecurityAction.LOGIN && ipAddress) {
      const recentIPs = await prisma.securityLog.findMany({
        where: {
          userId,
          action: SecurityAction.LOGIN,
          status: SecurityStatus.SUCCESS,
          createdAt: { gte: oneDayAgo },
          ipAddress: { not: null }
        },
        select: { ipAddress: true },
        distinct: ['ipAddress']
      })

      // 如果24小时内从超过3个不同IP登录，标记为可疑
      if (recentIPs.length >= 3 && !recentIPs.some(log => log.ipAddress === ipAddress)) {
        await recordSecurityLog(
          userId,
          SecurityAction.SUSPICIOUS_ACTIVITY,
          `检测到可疑活动：24小时内从${recentIPs.length + 1}个不同IP登录`,
          SecurityStatus.SUCCESS
        )
        return true
      }
    }

    return false
  } catch (error) {
    console.error('Check suspicious activity error:', error)
    return false
  }
}

// 生成安全建议
export function generateSecurityRecommendations(user: any, stats: any): string[] {
  const recommendations: string[] = []

  // 密码相关建议
  if (stats.passwordChanges === 0) {
    recommendations.push('建议定期更换密码以提高账户安全性')
  }

  // 登录安全建议
  if (stats.failedLogins > 0) {
    recommendations.push('检测到登录失败记录，请确保密码安全')
  }

  if (stats.loginSuccessRate < 90) {
    recommendations.push('登录成功率较低，建议检查账户安全')
  }

  // 币安绑定建议
  if (!user.binanceUid) {
    recommendations.push('建议绑定币安UID以提高交易安全性')
  }

  // 邮箱验证建议
  if (!user.emailVerified) {
    recommendations.push('建议验证邮箱地址以确保账户安全')
  }

  // IP地址建议
  if (stats.recentIPs && stats.recentIPs.length > 2) {
    recommendations.push('检测到多个登录IP，请确认是否为本人操作')
  }

  // 默认建议
  if (recommendations.length === 0) {
    recommendations.push('您的账户安全状况良好，请继续保持')
  }

  return recommendations
}

// 计算安全等级
export function calculateSecurityLevel(user: any, stats: any): {
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXCELLENT'
  score: number
  description: string
} {
  let score = 0

  // 基础分数
  score += 20

  // 密码强度（假设已设置密码）
  if (user.password) {
    score += 20
  }

  // 邮箱验证
  if (user.emailVerified) {
    score += 15
  }

  // 币安绑定
  if (user.binanceUid) {
    score += 15
  }

  // 登录成功率
  if (stats.loginSuccessRate >= 95) {
    score += 10
  } else if (stats.loginSuccessRate >= 90) {
    score += 5
  }

  // 最近活动
  if (stats.recentLogs > 0) {
    score += 10
  }

  // 密码更改频率
  if (stats.passwordChanges > 0) {
    score += 10
  }

  // 确定等级
  let level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXCELLENT'
  let description: string

  if (score >= 85) {
    level = 'EXCELLENT'
    description = '安全等级优秀，账户保护完善'
  } else if (score >= 70) {
    level = 'HIGH'
    description = '安全等级较高，建议继续完善'
  } else if (score >= 50) {
    level = 'MEDIUM'
    description = '安全等级中等，需要加强保护'
  } else {
    level = 'LOW'
    description = '安全等级较低，请立即加强保护'
  }

  return { level, score, description }
}
