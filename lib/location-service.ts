/**
 * 地理位置服务
 * 提供距离计算、附近搜索、地址解析等功能
 */

import { prisma } from './prisma'

// 地理位置接口
export interface Location {
  latitude: number
  longitude: number
  address?: string
  city?: string
  district?: string
}

// 距离筛选参数
export interface DistanceFilter {
  latitude: number
  longitude: number
  radius: number // 半径(km)
}

// 搜索结果接口
export interface LocationSearchResult<T> {
  item: T
  distance: number // 距离(km)
  distanceText: string // 距离文本描述
}

/**
 * 使用Haversine公式计算两点间距离
 * @param lat1 纬度1
 * @param lon1 经度1
 * @param lat2 纬度2
 * @param lon2 经度2
 * @returns 距离(km)
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371 // 地球半径(km)
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c
  
  return Math.round(distance * 100) / 100 // 保留两位小数
}

/**
 * 角度转弧度
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * 格式化距离文本
 */
export function formatDistance(distance: number): string {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`
  } else if (distance < 10) {
    return `${distance.toFixed(1)}km`
  } else {
    return `${Math.round(distance)}km`
  }
}

/**
 * 计算边界框坐标（用于数据库查询优化）
 */
export function calculateBoundingBox(
  latitude: number,
  longitude: number,
  radiusKm: number
): {
  minLat: number
  maxLat: number
  minLon: number
  maxLon: number
} {
  const latDelta = radiusKm / 111.32 // 1度纬度约等于111.32km
  const lonDelta = radiusKm / (111.32 * Math.cos(toRadians(latitude)))
  
  return {
    minLat: latitude - latDelta,
    maxLat: latitude + latDelta,
    minLon: longitude - lonDelta,
    maxLon: longitude + lonDelta
  }
}

/**
 * 搜索附近的商品
 */
export async function searchNearbyProducts(
  filter: DistanceFilter,
  options: {
    category?: string
    status?: string
    limit?: number
    offset?: number
    sortBy?: 'distance' | 'price' | 'createdAt'
    sortOrder?: 'asc' | 'desc'
  } = {}
): Promise<{
  products: LocationSearchResult<any>[]
  total: number
  hasMore: boolean
}> {
  try {
    const { latitude, longitude, radius } = filter
    const { 
      category, 
      status = 'AVAILABLE', 
      limit = 20, 
      offset = 0,
      sortBy = 'distance',
      sortOrder = 'asc'
    } = options

    // 计算边界框以优化查询
    const bbox = calculateBoundingBox(latitude, longitude, radius)

    // 构建查询条件
    const where: any = {
      status,
      reviewStatus: 'APPROVED',
      latitude: {
        gte: bbox.minLat,
        lte: bbox.maxLat
      },
      longitude: {
        gte: bbox.minLon,
        lte: bbox.maxLon
      }
    }

    if (category) {
      where.category = category
    }

    // 查询商品
    const products = await prisma.product.findMany({
      where,
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            creditLevel: true,
            depositBalance: true
          }
        }
      },
      skip: offset,
      take: limit * 2 // 取更多数据以便精确距离筛选
    })

    // 计算距离并筛选
    const productsWithDistance = products
      .map(product => {
        if (!product.latitude || !product.longitude) return null
        
        const distance = calculateDistance(
          latitude,
          longitude,
          product.latitude,
          product.longitude
        )
        
        if (distance <= radius) {
          return {
            item: product,
            distance,
            distanceText: formatDistance(distance)
          }
        }
        return null
      })
      .filter(Boolean) as LocationSearchResult<any>[]

    // 排序
    productsWithDistance.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return sortOrder === 'asc' ? a.distance - b.distance : b.distance - a.distance
        case 'price':
          return sortOrder === 'asc' ? a.item.price - b.item.price : b.item.price - a.item.price
        case 'createdAt':
          const dateA = new Date(a.item.createdAt).getTime()
          const dateB = new Date(b.item.createdAt).getTime()
          return sortOrder === 'asc' ? dateA - dateB : dateB - dateA
        default:
          return 0
      }
    })

    // 分页
    const paginatedResults = productsWithDistance.slice(offset, offset + limit)
    const total = productsWithDistance.length
    const hasMore = offset + limit < total

    return {
      products: paginatedResults,
      total,
      hasMore
    }

  } catch (error) {
    console.error('搜索附近商品失败:', error)
    throw error
  }
}

/**
 * 搜索附近的需求单
 */
export async function searchNearbyDemands(
  filter: DistanceFilter,
  options: {
    demandType?: string
    status?: string
    limit?: number
    offset?: number
    sortBy?: 'distance' | 'budget' | 'createdAt'
    sortOrder?: 'asc' | 'desc'
  } = {}
): Promise<{
  demands: LocationSearchResult<any>[]
  total: number
  hasMore: boolean
}> {
  try {
    const { latitude, longitude, radius } = filter
    const { 
      demandType, 
      status = 'OPEN', 
      limit = 20, 
      offset = 0,
      sortBy = 'distance',
      sortOrder = 'asc'
    } = options

    // 计算边界框
    const bbox = calculateBoundingBox(latitude, longitude, radius)

    // 构建查询条件
    const where: any = {
      status,
      expirationTime: {
        gt: new Date() // 未过期
      },
      latitude: {
        gte: bbox.minLat,
        lte: bbox.maxLat
      },
      longitude: {
        gte: bbox.minLon,
        lte: bbox.maxLon
      }
    }

    if (demandType) {
      where.demandType = demandType
    }

    // 查询需求单
    const demands = await prisma.demand.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            creditLevel: true,
            depositBalance: true
          }
        },
        offers: {
          select: {
            id: true,
            offerPrice: true
          }
        }
      },
      skip: offset,
      take: limit * 2
    })

    // 计算距离并筛选
    const demandsWithDistance = demands
      .map(demand => {
        if (!demand.latitude || !demand.longitude) return null
        
        const distance = calculateDistance(
          latitude,
          longitude,
          demand.latitude,
          demand.longitude
        )
        
        if (distance <= radius) {
          return {
            item: demand,
            distance,
            distanceText: formatDistance(distance)
          }
        }
        return null
      })
      .filter(Boolean) as LocationSearchResult<any>[]

    // 排序
    demandsWithDistance.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return sortOrder === 'asc' ? a.distance - b.distance : b.distance - a.distance
        case 'budget':
          return sortOrder === 'asc' ? a.item.budget - b.item.budget : b.item.budget - a.item.budget
        case 'createdAt':
          const dateA = new Date(a.item.createdAt).getTime()
          const dateB = new Date(b.item.createdAt).getTime()
          return sortOrder === 'asc' ? dateA - dateB : dateB - dateA
        default:
          return 0
      }
    })

    // 分页
    const paginatedResults = demandsWithDistance.slice(offset, offset + limit)
    const total = demandsWithDistance.length
    const hasMore = offset + limit < total

    return {
      demands: paginatedResults,
      total,
      hasMore
    }

  } catch (error) {
    console.error('搜索附近需求单失败:', error)
    throw error
  }
}

/**
 * 获取热门地区统计
 */
export async function getPopularAreas(type: 'product' | 'demand' = 'product'): Promise<{
  city: string
  district: string
  count: number
  avgPrice?: number
}[]> {
  try {
    let result
    
    if (type === 'product') {
      result = await prisma.product.groupBy({
        by: ['city', 'district'],
        where: {
          status: 'AVAILABLE',
          reviewStatus: 'APPROVED',
          city: { not: null },
          district: { not: null }
        },
        _count: {
          id: true
        },
        _avg: {
          price: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 20
      })
      
      return result.map(item => ({
        city: item.city!,
        district: item.district!,
        count: item._count.id,
        avgPrice: item._avg.price || 0
      }))
    } else {
      result = await prisma.demand.groupBy({
        by: ['city', 'district'],
        where: {
          status: 'OPEN',
          city: { not: null },
          district: { not: null }
        },
        _count: {
          id: true
        },
        _avg: {
          budget: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 20
      })
      
      return result.map(item => ({
        city: item.city!,
        district: item.district!,
        count: item._count.id,
        avgPrice: item._avg.budget || 0
      }))
    }

  } catch (error) {
    console.error('获取热门地区失败:', error)
    throw error
  }
}
