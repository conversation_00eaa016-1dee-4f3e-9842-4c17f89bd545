import { Server as SocketIOServer, Socket } from 'socket.io'
import { Server as HTTPServer } from 'http'
import type { NextApiRequest } from 'next'
import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { prisma } from './prisma'
import jwt from 'jsonwebtoken'

export interface SocketWithAuth extends Socket {
  userId?: string
  user?: {
    id: string
    name: string
    email: string
  }
}

let io: SocketIOServer | null = null

// 在线用户管理
const onlineUsers = new Map<string, {
  socketId: string
  userId: string
  lastActive: Date
}>()

// 打字状态管理
const typingUsers = new Map<string, Set<string>>() // orderId -> Set<userId>

export const initializeSocket = (server: HTTPServer) => {
  if (io) {
    return io
  }

  io = new SocketIOServer(server, {
    cors: {
      origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
      methods: ["GET", "POST"]
    },
    path: '/api/socket'
  })

  // 身份验证中间件
  io.use(async (socket: any, next) => {
    try {
      const token = socket.handshake.auth.token
      if (!token) {
        return next(new Error('Authentication error'))
      }

      // 验证JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          name: true,
          email: true
        }
      })

      if (!user) {
        return next(new Error('User not found'))
      }

      socket.userId = user.id
      socket.user = user
      next()
    } catch (error) {
      next(new Error('Authentication error'))
    }
  })

  io.on('connection', (socket: any) => {
    // 用户上线
    onlineUsers.set(socket.userId, {
      socketId: socket.id,
      userId: socket.userId,
      lastActive: new Date()
    })

    // 广播用户上线状态
    socket.broadcast.emit('user_online', {
      userId: socket.userId,
      user: socket.user
    })

    // 加入用户的订单房间
    socket.on('join_order_rooms', async () => {
      try {
        const orders = await prisma.order.findMany({
          where: {
            OR: [
              { buyerId: socket.userId },
              { sellerId: socket.userId }
            ]
          },
          select: { id: true }
        })

        orders.forEach(order => {
          socket.join(`order_${order.id}`)
        })
      } catch (error) {
        console.error('Error joining order rooms:', error)
      }
    })

    // 发送消息
    socket.on('send_message', async (data: {
      orderId: string
      content: string
      messageType?: string
    }) => {
      try {
        const { orderId, content, messageType = 'TEXT' } = data

        // 验证用户是否有权限发送消息到此订单
        const order = await prisma.order.findFirst({
          where: {
            id: orderId,
            OR: [
              { buyerId: socket.userId },
              { sellerId: socket.userId }
            ]
          }
        })

        if (!order) {
          socket.emit('error', { message: '无权限发送消息到此订单' })
          return
        }

        // 确定接收者
        const receiverId = order.buyerId === socket.userId ? order.sellerId : order.buyerId

        // 创建消息
        const message = await prisma.message.create({
          data: {
            orderId,
            content: content.trim(),
            messageType,
            senderId: socket.userId,
            receiverId,
            status: 'SENT'
          },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            receiver: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })

        // 发送给订单房间的所有用户
        io?.to(`order_${orderId}`).emit('new_message', message)

        // 更新消息状态为已送达（如果接收者在线）
        const receiverOnline = onlineUsers.get(receiverId)
        if (receiverOnline) {
          await prisma.message.update({
            where: { id: message.id },
            data: { status: 'DELIVERED' }
          })

          // 通知消息状态更新
          io?.to(`order_${orderId}`).emit('message_status_update', {
            messageId: message.id,
            status: 'DELIVERED'
          })
        }

      } catch (error) {
        console.error('Error sending message:', error)
        socket.emit('error', { message: '发送消息失败' })
      }
    })

    // 打字状态
    socket.on('typing_start', (data: { orderId: string }) => {
      const { orderId } = data
      
      if (!typingUsers.has(orderId)) {
        typingUsers.set(orderId, new Set())
      }
      
      typingUsers.get(orderId)?.add(socket.userId)
      
      // 通知订单房间其他用户
      socket.to(`order_${orderId}`).emit('user_typing', {
        userId: socket.userId,
        user: socket.user,
        orderId
      })
    })

    socket.on('typing_stop', (data: { orderId: string }) => {
      const { orderId } = data
      
      typingUsers.get(orderId)?.delete(socket.userId)
      
      // 通知订单房间其他用户
      socket.to(`order_${orderId}`).emit('user_stop_typing', {
        userId: socket.userId,
        orderId
      })
    })

    // 标记消息为已读
    socket.on('mark_messages_read', async (data: { orderId: string }) => {
      try {
        const { orderId } = data

        // 更新消息状态为已读
        await prisma.message.updateMany({
          where: {
            orderId,
            receiverId: socket.userId,
            status: { in: ['SENT', 'DELIVERED'] }
          },
          data: { status: 'READ' }
        })

        // 通知发送者消息已读
        socket.to(`order_${orderId}`).emit('messages_read', {
          orderId,
          readBy: socket.userId
        })

      } catch (error) {
        console.error('Error marking messages as read:', error)
      }
    })

    // 获取在线用户列表
    socket.on('get_online_users', () => {
      const onlineUsersList = Array.from(onlineUsers.values()).map(user => ({
        userId: user.userId,
        lastActive: user.lastActive
      }))
      
      socket.emit('online_users_list', onlineUsersList)
    })

    // 用户断开连接
    socket.on('disconnect', () => {
      console.log(`User ${socket.user.name} disconnected: ${socket.id}`)
      
      // 更新最后活跃时间
      const userInfo = onlineUsers.get(socket.userId)
      if (userInfo) {
        userInfo.lastActive = new Date()
      }
      
      // 延迟移除用户（防止短暂断线）
      setTimeout(() => {
        const currentUser = onlineUsers.get(socket.userId)
        if (currentUser && currentUser.socketId === socket.id) {
          onlineUsers.delete(socket.userId)
          
          // 广播用户离线状态
          socket.broadcast.emit('user_offline', {
            userId: socket.userId,
            lastActive: new Date()
          })
        }
      }, 5000) // 5秒后移除
      
      // 清理打字状态
      typingUsers.forEach((users, orderId) => {
        if (users.has(socket.userId)) {
          users.delete(socket.userId)
          socket.to(`order_${orderId}`).emit('user_stop_typing', {
            userId: socket.userId,
            orderId
          })
        }
      })
    })
  })

  return io
}

export const getSocketIO = () => io

// 发送系统通知
export const sendSystemNotification = (userId: string, notification: {
  type: string
  title: string
  message: string
  data?: any
}) => {
  if (!io) return

  const userOnline = onlineUsers.get(userId)
  if (userOnline) {
    io.to(userOnline.socketId).emit('system_notification', notification)
  }
}

// 广播系统公告
export const broadcastSystemAnnouncement = (announcement: {
  title: string
  content: string
  type: string
}) => {
  if (!io) return

  io.emit('system_announcement', announcement)
}

// 获取在线用户数量
export const getOnlineUsersCount = () => {
  return onlineUsers.size
}

// 获取用户在线状态
export const getUserOnlineStatus = (userId: string) => {
  return onlineUsers.get(userId) || null
}
