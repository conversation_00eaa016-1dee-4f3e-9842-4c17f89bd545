/**
 * 资金池统计服务
 * 管理平台资金沉淀和利差收益
 */

import { prisma } from './prisma'
import { getCreditLevelStats } from './credit-level'

/**
 * 生成每日资金池统计
 */
export async function generateDailyStats(date?: Date): Promise<void> {
  try {
    const statsDate = date || new Date()
    statsDate.setHours(0, 0, 0, 0) // 设置为当天开始

    // 检查是否已存在当天统计
    const existingStats = await prisma.fundPoolStats.findUnique({
      where: { statsDate }
    })

    if (existingStats) {
      console.log(`${statsDate.toISOString().split('T')[0]} 的统计已存在，跳过生成`)
      return
    }

    // 计算资金池数据
    const fundPoolData = await calculateFundPoolData()
    
    // 计算用户统计
    const userStats = await calculateUserStats(statsDate)
    
    // 计算交易统计
    const transactionStats = await calculateTransactionStats(statsDate)
    
    // 获取等级分布
    const levelDistribution = await getCreditLevelStats()
    
    // 计算收益数据
    const revenueData = await calculateRevenueData(statsDate)

    // 创建统计记录
    await prisma.fundPoolStats.create({
      data: {
        statsDate,
        ...fundPoolData,
        ...userStats,
        ...transactionStats,
        bronzeUsers: levelDistribution.BRONZE || 0,
        silverUsers: levelDistribution.SILVER || 0,
        goldUsers: levelDistribution.GOLD || 0,
        platinumUsers: levelDistribution.PLATINUM || 0,
        diamondUsers: levelDistribution.DIAMOND || 0,
        ...revenueData
      }
    })

    console.log(`生成 ${statsDate.toISOString().split('T')[0]} 的资金池统计完成`)

  } catch (error) {
    console.error('生成每日统计失败:', error)
    throw error
  }
}

/**
 * 计算资金池数据
 */
async function calculateFundPoolData() {
  try {
    // 总担保金
    const totalDepositsResult = await prisma.user.aggregate({
      _sum: {
        depositBalance: true
      }
    })
    const totalDeposits = totalDepositsResult._sum.depositBalance || 0

    // 总冻结金额
    const totalFrozenResult = await prisma.fundFreeze.aggregate({
      where: {
        status: {
          in: ['FROZEN', 'CONFIRMED']
        }
      },
      _sum: {
        amount: true
      }
    })
    const totalFrozen = totalFrozenResult._sum.amount || 0

    // 总可用金额
    const totalAvailable = totalDeposits - totalFrozen

    // 总提现金额（当日）
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const totalWithdrawnResult = await prisma.withdrawal.aggregate({
      where: {
        status: 'COMPLETED',
        processedAt: {
          gte: today,
          lt: tomorrow
        }
      },
      _sum: {
        actualAmount: true
      }
    })
    const totalWithdrawn = totalWithdrawnResult._sum.actualAmount || 0

    return {
      totalDeposits,
      totalFrozen,
      totalAvailable,
      totalWithdrawn
    }

  } catch (error) {
    console.error('计算资金池数据失败:', error)
    throw error
  }
}

/**
 * 计算用户统计
 */
async function calculateUserStats(date: Date) {
  try {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    // 活跃用户数（当日有交易活动的用户）
    const activeUsersResult = await prisma.fundFreeze.findMany({
      where: {
        createdAt: {
          gte: startOfDay,
          lte: endOfDay
        }
      },
      select: {
        userId: true
      },
      distinct: ['userId']
    })
    const activeUsers = activeUsersResult.length

    // 新增用户数
    const newUsersResult = await prisma.user.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lte: endOfDay
        }
      }
    })
    const newUsers = newUsersResult

    return {
      activeUsers,
      newUsers
    }

  } catch (error) {
    console.error('计算用户统计失败:', error)
    throw error
  }
}

/**
 * 计算交易统计
 */
async function calculateTransactionStats(date: Date) {
  try {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    // 日交易量
    const dailyTransactions = await prisma.fundFreeze.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lte: endOfDay
        },
        purpose: {
          in: ['PURCHASE', 'CONSUMPTION']
        }
      }
    })

    // 日交易额
    const dailyVolumeResult = await prisma.fundFreeze.aggregate({
      where: {
        createdAt: {
          gte: startOfDay,
          lte: endOfDay
        },
        purpose: {
          in: ['PURCHASE', 'CONSUMPTION']
        }
      },
      _sum: {
        amount: true
      }
    })
    const dailyVolume = dailyVolumeResult._sum.amount || 0

    // 平均交易金额
    const avgTransactionSize = dailyTransactions > 0 ? dailyVolume / dailyTransactions : 0

    return {
      dailyTransactions,
      dailyVolume,
      avgTransactionSize
    }

  } catch (error) {
    console.error('计算交易统计失败:', error)
    throw error
  }
}

/**
 * 计算收益数据
 */
async function calculateRevenueData(date: Date) {
  try {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    // 平台收入（手续费）
    const platformRevenueResult = await prisma.fundFreeze.aggregate({
      where: {
        settledAt: {
          gte: startOfDay,
          lte: endOfDay
        },
        status: 'SETTLED'
      },
      _sum: {
        platformFee: true
      }
    })
    const platformRevenue = platformRevenueResult._sum.platformFee || 0

    // 用户奖励（暂时设为平台收入的10%）
    const userRewards = platformRevenue * 0.1

    return {
      platformRevenue,
      userRewards
    }

  } catch (error) {
    console.error('计算收益数据失败:', error)
    throw error
  }
}

/**
 * 获取资金池概览
 */
export async function getFundPoolOverview() {
  try {
    // 获取最新统计数据
    const latestStats = await prisma.fundPoolStats.findFirst({
      orderBy: { statsDate: 'desc' }
    })

    // 获取实时数据
    const realtimeData = await calculateFundPoolData()

    // 计算资金沉淀率
    const depositRate = realtimeData.totalDeposits > 0 
      ? (realtimeData.totalAvailable / realtimeData.totalDeposits) * 100 
      : 0

    // 计算资金周转率（基于最近7天数据）
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    
    const weeklyVolumeResult = await prisma.fundFreeze.aggregate({
      where: {
        createdAt: {
          gte: sevenDaysAgo
        },
        purpose: {
          in: ['PURCHASE', 'CONSUMPTION']
        }
      },
      _sum: {
        amount: true
      }
    })
    const weeklyVolume = weeklyVolumeResult._sum.amount || 0
    const turnoverRate = realtimeData.totalDeposits > 0 
      ? (weeklyVolume / realtimeData.totalDeposits) * 100 
      : 0

    return {
      ...realtimeData,
      depositRate,
      turnoverRate,
      latestStats,
      lastUpdated: new Date()
    }

  } catch (error) {
    console.error('获取资金池概览失败:', error)
    throw error
  }
}

/**
 * 获取历史趋势数据
 */
export async function getFundPoolTrends(days: number = 30) {
  try {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const trends = await prisma.fundPoolStats.findMany({
      where: {
        statsDate: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { statsDate: 'asc' }
    })

    return trends

  } catch (error) {
    console.error('获取历史趋势数据失败:', error)
    throw error
  }
}

/**
 * 计算平台利差收益
 */
export async function calculateInterestRevenue(
  principal: number,
  annualRate: number = 0.05, // 5% 年化收益率
  days: number = 1
): Promise<{
  dailyRevenue: number
  projectedMonthly: number
  projectedAnnual: number
}> {
  try {
    const dailyRate = annualRate / 365
    const dailyRevenue = principal * dailyRate * days
    const projectedMonthly = principal * (annualRate / 12)
    const projectedAnnual = principal * annualRate

    return {
      dailyRevenue,
      projectedMonthly,
      projectedAnnual
    }

  } catch (error) {
    console.error('计算利差收益失败:', error)
    throw error
  }
}
