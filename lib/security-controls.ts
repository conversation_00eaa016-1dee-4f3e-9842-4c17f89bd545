import { prisma } from '@/lib/prisma'

// 风险等级
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// 风险类型
export enum RiskType {
  LARGE_AMOUNT = 'LARGE_AMOUNT',
  FREQUENT_TRANSACTIONS = 'FREQUENT_TRANSACTIONS',
  SUSPICIOUS_PATTERN = 'SUSPICIOUS_PATTERN',
  NEW_USER = 'NEW_USER',
  BLACKLISTED_ADDRESS = 'BLACKLISTED_ADDRESS',
  VELOCITY_CHECK = 'VELOCITY_CHECK',
  REPUTATION_RISK = 'REPUTATION_RISK'
}

// 风险检查配置
export const RISK_THRESHOLDS = {
  LARGE_AMOUNT: {
    MEDIUM: 1000, // 1000 USDT
    HIGH: 5000,   // 5000 USDT
    CRITICAL: 10000 // 10000 USDT
  },
  DAILY_VOLUME: {
    MEDIUM: 5000,
    HIGH: 20000,
    CRITICAL: 50000
  },
  TRANSACTION_COUNT: {
    DAILY_MEDIUM: 10,
    DAILY_HIGH: 20,
    DAILY_CRITICAL: 50
  },
  NEW_USER_DAYS: 7, // 新用户定义：注册7天内
  MIN_REPUTATION: 3.0 // 最低信誉要求
}

// 风险检查结果
export interface RiskAssessment {
  riskLevel: RiskLevel
  riskScore: number
  riskFactors: Array<{
    type: RiskType
    level: RiskLevel
    description: string
    score: number
  }>
  requiresApproval: boolean
  requiresKYC: boolean
  maxAllowedAmount?: number
  recommendations: string[]
}

// 执行综合风险评估
export async function assessTransactionRisk(
  userId: string,
  amount: number,
  walletAddress?: string,
  mediatorId?: string
): Promise<RiskAssessment> {
  const riskFactors: RiskAssessment['riskFactors'] = []
  let totalRiskScore = 0

  try {
    // 1. 金额风险检查
    const amountRisk = assessAmountRisk(amount)
    if (amountRisk.level !== RiskLevel.LOW) {
      riskFactors.push(amountRisk)
      totalRiskScore += amountRisk.score
    }

    // 2. 用户历史风险检查
    const userRisk = await assessUserRisk(userId)
    if (userRisk.level !== RiskLevel.LOW) {
      riskFactors.push(userRisk)
      totalRiskScore += userRisk.score
    }

    // 3. 交易频率风险检查
    const velocityRisk = await assessVelocityRisk(userId, amount)
    if (velocityRisk.level !== RiskLevel.LOW) {
      riskFactors.push(velocityRisk)
      totalRiskScore += velocityRisk.score
    }

    // 4. 钱包地址风险检查
    if (walletAddress) {
      const walletRisk = await assessWalletRisk(walletAddress)
      if (walletRisk.level !== RiskLevel.LOW) {
        riskFactors.push(walletRisk)
        totalRiskScore += walletRisk.score
      }
    }

    // 5. 中间人风险检查
    if (mediatorId) {
      const mediatorRisk = await assessMediatorRisk(mediatorId)
      if (mediatorRisk.level !== RiskLevel.LOW) {
        riskFactors.push(mediatorRisk)
        totalRiskScore += mediatorRisk.score
      }
    }

    // 6. 新用户风险检查
    const newUserRisk = await assessNewUserRisk(userId)
    if (newUserRisk.level !== RiskLevel.LOW) {
      riskFactors.push(newUserRisk)
      totalRiskScore += newUserRisk.score
    }

    // 计算最终风险等级
    const finalRiskLevel = calculateFinalRiskLevel(totalRiskScore)
    
    // 生成建议和限制
    const { requiresApproval, requiresKYC, maxAllowedAmount, recommendations } = 
      generateRiskRecommendations(finalRiskLevel, riskFactors, amount)

    return {
      riskLevel: finalRiskLevel,
      riskScore: totalRiskScore,
      riskFactors,
      requiresApproval,
      requiresKYC,
      maxAllowedAmount,
      recommendations
    }

  } catch (error) {
    console.error('风险评估失败:', error)
    
    // 出错时返回高风险，要求人工审核
    return {
      riskLevel: RiskLevel.HIGH,
      riskScore: 100,
      riskFactors: [{
        type: RiskType.SUSPICIOUS_PATTERN,
        level: RiskLevel.HIGH,
        description: '风险评估系统异常，需要人工审核',
        score: 100
      }],
      requiresApproval: true,
      requiresKYC: true,
      recommendations: ['系统异常，请联系客服进行人工审核']
    }
  }
}

// 金额风险评估
function assessAmountRisk(amount: number): RiskAssessment['riskFactors'][0] {
  const thresholds = RISK_THRESHOLDS.LARGE_AMOUNT
  
  if (amount >= thresholds.CRITICAL) {
    return {
      type: RiskType.LARGE_AMOUNT,
      level: RiskLevel.CRITICAL,
      description: `交易金额 ${amount} USDT 超过临界阈值`,
      score: 50
    }
  } else if (amount >= thresholds.HIGH) {
    return {
      type: RiskType.LARGE_AMOUNT,
      level: RiskLevel.HIGH,
      description: `交易金额 ${amount} USDT 较大`,
      score: 30
    }
  } else if (amount >= thresholds.MEDIUM) {
    return {
      type: RiskType.LARGE_AMOUNT,
      level: RiskLevel.MEDIUM,
      description: `交易金额 ${amount} USDT 中等`,
      score: 15
    }
  }
  
  return {
    type: RiskType.LARGE_AMOUNT,
    level: RiskLevel.LOW,
    description: '交易金额正常',
    score: 0
  }
}

// 用户历史风险评估
async function assessUserRisk(userId: string): Promise<RiskAssessment['riskFactors'][0]> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        creditScore: true,
        isBlacklisted: true,
        blacklistedAt: true,
        blacklistReason: true,
        createdAt: true
      }
    })

    if (!user) {
      return {
        type: RiskType.SUSPICIOUS_PATTERN,
        level: RiskLevel.HIGH,
        description: '用户不存在',
        score: 40
      }
    }

    if (user.isBlacklisted) {
      return {
        type: RiskType.SUSPICIOUS_PATTERN,
        level: RiskLevel.CRITICAL,
        description: `用户已被列入黑名单：${user.blacklistReason}`,
        score: 100
      }
    }

    if ((user.creditScore || 0) < RISK_THRESHOLDS.MIN_REPUTATION) {
      return {
        type: RiskType.REPUTATION_RISK,
        level: RiskLevel.HIGH,
        description: `用户信誉分数过低：${user.creditScore}`,
        score: 35
      }
    }

    return {
      type: RiskType.REPUTATION_RISK,
      level: RiskLevel.LOW,
      description: '用户历史记录正常',
      score: 0
    }

  } catch (error) {
    return {
      type: RiskType.SUSPICIOUS_PATTERN,
      level: RiskLevel.MEDIUM,
      description: '无法获取用户历史信息',
      score: 20
    }
  }
}

// 交易频率风险评估
async function assessVelocityRisk(
  userId: string, 
  amount: number
): Promise<RiskAssessment['riskFactors'][0]> {
  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // 获取今日交易统计
    const todayStats = await prisma.escrowOrder.aggregate({
      where: {
        OR: [
          { buyerId: userId },
          { sellerId: userId },
          { mediatorId: userId }
        ],
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      },
      _count: { _all: true },
      _sum: { amount: true }
    })

    const todayCount = todayStats._count._all || 0
    const todayVolume = (todayStats._sum.amount || 0) + amount

    // 检查交易次数
    if (todayCount >= RISK_THRESHOLDS.TRANSACTION_COUNT.DAILY_CRITICAL) {
      return {
        type: RiskType.FREQUENT_TRANSACTIONS,
        level: RiskLevel.CRITICAL,
        description: `今日交易次数过多：${todayCount}次`,
        score: 45
      }
    } else if (todayCount >= RISK_THRESHOLDS.TRANSACTION_COUNT.DAILY_HIGH) {
      return {
        type: RiskType.FREQUENT_TRANSACTIONS,
        level: RiskLevel.HIGH,
        description: `今日交易次数较多：${todayCount}次`,
        score: 30
      }
    } else if (todayCount >= RISK_THRESHOLDS.TRANSACTION_COUNT.DAILY_MEDIUM) {
      return {
        type: RiskType.FREQUENT_TRANSACTIONS,
        level: RiskLevel.MEDIUM,
        description: `今日交易次数中等：${todayCount}次`,
        score: 15
      }
    }

    // 检查交易金额
    if (todayVolume >= RISK_THRESHOLDS.DAILY_VOLUME.CRITICAL) {
      return {
        type: RiskType.VELOCITY_CHECK,
        level: RiskLevel.CRITICAL,
        description: `今日交易金额过大：${todayVolume} USDT`,
        score: 40
      }
    } else if (todayVolume >= RISK_THRESHOLDS.DAILY_VOLUME.HIGH) {
      return {
        type: RiskType.VELOCITY_CHECK,
        level: RiskLevel.HIGH,
        description: `今日交易金额较大：${todayVolume} USDT`,
        score: 25
      }
    } else if (todayVolume >= RISK_THRESHOLDS.DAILY_VOLUME.MEDIUM) {
      return {
        type: RiskType.VELOCITY_CHECK,
        level: RiskLevel.MEDIUM,
        description: `今日交易金额中等：${todayVolume} USDT`,
        score: 10
      }
    }

    return {
      type: RiskType.VELOCITY_CHECK,
      level: RiskLevel.LOW,
      description: '交易频率正常',
      score: 0
    }

  } catch (error) {
    return {
      type: RiskType.VELOCITY_CHECK,
      level: RiskLevel.MEDIUM,
      description: '无法检查交易频率',
      score: 20
    }
  }
}

// 钱包地址风险评估
async function assessWalletRisk(walletAddress: string): Promise<RiskAssessment['riskFactors'][0]> {
  try {
    // 检查黑名单钱包地址
    const blacklistedWallet = await prisma.blacklistedWallet.findUnique({
      where: { address: walletAddress.toLowerCase() }
    })

    if (blacklistedWallet) {
      return {
        type: RiskType.BLACKLISTED_ADDRESS,
        level: RiskLevel.CRITICAL,
        description: `钱包地址已被列入黑名单：${blacklistedWallet.reason}`,
        score: 100
      }
    }

    // 这里可以集成第三方风险评估服务
    // 例如 Chainalysis, Elliptic 等

    return {
      type: RiskType.BLACKLISTED_ADDRESS,
      level: RiskLevel.LOW,
      description: '钱包地址正常',
      score: 0
    }

  } catch (error) {
    return {
      type: RiskType.BLACKLISTED_ADDRESS,
      level: RiskLevel.MEDIUM,
      description: '无法验证钱包地址',
      score: 15
    }
  }
}

// 中间人风险评估
async function assessMediatorRisk(mediatorId: string): Promise<RiskAssessment['riskFactors'][0]> {
  try {
    const mediator = await prisma.user.findUnique({
      where: { id: mediatorId },
      select: {
        mediatorStatus: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true
      }
    })

    if (!mediator || mediator.mediatorStatus !== 'ACTIVE') {
      return {
        type: RiskType.REPUTATION_RISK,
        level: RiskLevel.HIGH,
        description: '中间人状态异常',
        score: 35
      }
    }

    const reputation = mediator.mediatorReputation || 0
    const successRate = mediator.mediatorSuccessRate || 0
    const totalOrders = mediator.mediatorTotalOrders || 0

    if (reputation < 3.0 || successRate < 0.7 || totalOrders < 5) {
      return {
        type: RiskType.REPUTATION_RISK,
        level: RiskLevel.MEDIUM,
        description: '中间人经验不足或信誉较低',
        score: 20
      }
    }

    return {
      type: RiskType.REPUTATION_RISK,
      level: RiskLevel.LOW,
      description: '中间人信誉良好',
      score: 0
    }

  } catch (error) {
    return {
      type: RiskType.REPUTATION_RISK,
      level: RiskLevel.MEDIUM,
      description: '无法验证中间人信息',
      score: 20
    }
  }
}

// 新用户风险评估
async function assessNewUserRisk(userId: string): Promise<RiskAssessment['riskFactors'][0]> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { createdAt: true }
    })

    if (!user) {
      return {
        type: RiskType.NEW_USER,
        level: RiskLevel.HIGH,
        description: '用户不存在',
        score: 40
      }
    }

    const daysSinceRegistration = Math.floor(
      (Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (daysSinceRegistration <= RISK_THRESHOLDS.NEW_USER_DAYS) {
      return {
        type: RiskType.NEW_USER,
        level: RiskLevel.MEDIUM,
        description: `新用户（注册${daysSinceRegistration}天）`,
        score: 15
      }
    }

    return {
      type: RiskType.NEW_USER,
      level: RiskLevel.LOW,
      description: '用户注册时间正常',
      score: 0
    }

  } catch (error) {
    return {
      type: RiskType.NEW_USER,
      level: RiskLevel.MEDIUM,
      description: '无法验证用户注册时间',
      score: 15
    }
  }
}

// 计算最终风险等级
function calculateFinalRiskLevel(totalScore: number): RiskLevel {
  if (totalScore >= 80) return RiskLevel.CRITICAL
  if (totalScore >= 50) return RiskLevel.HIGH
  if (totalScore >= 25) return RiskLevel.MEDIUM
  return RiskLevel.LOW
}

// 生成风险建议和限制
function generateRiskRecommendations(
  riskLevel: RiskLevel,
  riskFactors: RiskAssessment['riskFactors'],
  amount: number
): {
  requiresApproval: boolean
  requiresKYC: boolean
  maxAllowedAmount?: number
  recommendations: string[]
} {
  const recommendations: string[] = []
  let requiresApproval = false
  let requiresKYC = false
  let maxAllowedAmount: number | undefined

  switch (riskLevel) {
    case RiskLevel.CRITICAL:
      requiresApproval = true
      requiresKYC = true
      maxAllowedAmount = 500 // 限制最大金额
      recommendations.push('需要完成高级身份验证')
      recommendations.push('需要人工审核批准')
      recommendations.push('建议分批进行小额交易')
      break

    case RiskLevel.HIGH:
      requiresApproval = true
      requiresKYC = true
      maxAllowedAmount = 2000
      recommendations.push('需要完成身份验证')
      recommendations.push('需要人工审核')
      recommendations.push('建议提供额外的身份证明')
      break

    case RiskLevel.MEDIUM:
      if (amount > 1000) {
        requiresApproval = true
      }
      if (amount > 5000) {
        requiresKYC = true
      }
      recommendations.push('建议完成身份验证以提高交易限额')
      recommendations.push('建议选择信誉良好的中间人')
      break

    case RiskLevel.LOW:
      recommendations.push('交易风险较低，可正常进行')
      break
  }

  // 根据具体风险因素添加建议
  riskFactors.forEach(factor => {
    switch (factor.type) {
      case RiskType.NEW_USER:
        recommendations.push('建议先进行小额交易建立信誉')
        break
      case RiskType.LARGE_AMOUNT:
        recommendations.push('大额交易建议分批进行')
        break
      case RiskType.FREQUENT_TRANSACTIONS:
        recommendations.push('建议适当降低交易频率')
        break
      case RiskType.REPUTATION_RISK:
        recommendations.push('建议提高账户信誉度')
        break
    }
  })

  return {
    requiresApproval,
    requiresKYC,
    maxAllowedAmount,
    recommendations: [...new Set(recommendations)] // 去重
  }
}

// 记录风险评估日志
export async function logRiskAssessment(
  userId: string,
  assessment: RiskAssessment,
  transactionType: string,
  amount: number,
  additionalData?: any
): Promise<void> {
  try {
    await prisma.riskAssessmentLog.create({
      data: {
        userId,
        riskLevel: assessment.riskLevel,
        riskScore: assessment.riskScore,
        riskFactors: assessment.riskFactors,
        requiresApproval: assessment.requiresApproval,
        requiresKYC: assessment.requiresKYC,
        maxAllowedAmount: assessment.maxAllowedAmount,
        recommendations: assessment.recommendations,
        transactionType,
        amount,
        additionalData: additionalData || {}
      }
    })
  } catch (error) {
    console.error('记录风险评估日志失败:', error)
    // 不抛出错误，避免影响主流程
  }
}
