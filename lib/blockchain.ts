import { ethers } from 'ethers'

// BNB Chain 配置
export const BNB_CHAIN_CONFIG = {
  chainId: '0x38', // 56 in hex
  chainName: 'BNB Smart Chain',
  nativeCurrency: {
    name: '<PERSON><PERSON><PERSON>',
    symbol: 'BN<PERSON>',
    decimals: 18,
  },
  rpcUrls: [
    'https://bsc-dataseed1.binance.org/',
    'https://bsc-dataseed2.binance.org/',
    'https://bsc-dataseed3.binance.org/',
  ],
  blockExplorerUrls: ['https://bscscan.com/'],
}

// USDT 合约地址 (BEP-20)
export const USDT_CONTRACT_ADDRESS = '******************************************'

// USDT 合约 ABI (简化版)
export const USDT_ABI = [
  'function balanceOf(address owner) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function transferFrom(address from, address to, uint256 amount) returns (bool)',
  'function approve(address spender, uint256 amount) returns (bool)',
  'function allowance(address owner, address spender) view returns (uint256)',
  'event Transfer(address indexed from, address indexed to, uint256 value)',
]

// 创建 provider
export function createProvider(): ethers.JsonRpcProvider {
  const rpcUrl = process.env.BNB_CHAIN_RPC_URL || BNB_CHAIN_CONFIG.rpcUrls[0]
  return new ethers.JsonRpcProvider(rpcUrl)
}

// 创建钱包实例
export function createWallet(privateKey: string): ethers.Wallet {
  const provider = createProvider()
  return new ethers.Wallet(privateKey, provider)
}

// 获取 USDT 合约实例
export function getUSDTContract(signerOrProvider: ethers.Signer | ethers.Provider): ethers.Contract {
  return new ethers.Contract(USDT_CONTRACT_ADDRESS, USDT_ABI, signerOrProvider)
}

// 验证钱包地址格式
export function isValidAddress(address: string): boolean {
  try {
    return ethers.isAddress(address)
  } catch {
    return false
  }
}

// 格式化 USDT 金额 (18 decimals)
export function formatUSDT(amount: bigint): string {
  return ethers.formatUnits(amount, 18)
}

// 解析 USDT 金额
export function parseUSDT(amount: string): bigint {
  return ethers.parseUnits(amount, 18)
}

// 获取钱包 USDT 余额
export async function getUSDTBalance(address: string): Promise<string> {
  try {
    const provider = createProvider()
    const contract = getUSDTContract(provider)
    const balance = await contract.balanceOf(address)
    return formatUSDT(balance)
  } catch (error) {
    console.error('获取 USDT 余额失败:', error)
    throw new Error('获取余额失败')
  }
}

// 监控交易状态
export async function waitForTransaction(
  txHash: string,
  confirmations: number = 12
): Promise<ethers.TransactionReceipt | null> {
  try {
    const provider = createProvider()
    const receipt = await provider.waitForTransaction(txHash, confirmations)
    return receipt
  } catch (error) {
    console.error('等待交易确认失败:', error)
    return null
  }
}

// 获取交易详情
export async function getTransactionDetails(txHash: string): Promise<{
  transaction: ethers.TransactionResponse | null
  receipt: ethers.TransactionReceipt | null
}> {
  try {
    const provider = createProvider()
    const [transaction, receipt] = await Promise.all([
      provider.getTransaction(txHash),
      provider.getTransactionReceipt(txHash)
    ])
    
    return { transaction, receipt }
  } catch (error) {
    console.error('获取交易详情失败:', error)
    return { transaction: null, receipt: null }
  }
}

// 验证交易是否为 USDT 转账
export async function verifyUSDTTransfer(
  txHash: string,
  expectedFrom: string,
  expectedTo: string,
  expectedAmount: string
): Promise<boolean> {
  try {
    const { receipt } = await getTransactionDetails(txHash)
    
    if (!receipt || receipt.status !== 1) {
      return false
    }

    // 解析 Transfer 事件
    const contract = getUSDTContract(createProvider())
    const transferEvents = receipt.logs
      .filter(log => log.address.toLowerCase() === USDT_CONTRACT_ADDRESS.toLowerCase())
      .map(log => {
        try {
          return contract.interface.parseLog({
            topics: log.topics,
            data: log.data
          })
        } catch {
          return null
        }
      })
      .filter(event => event && event.name === 'Transfer')

    // 验证转账事件
    for (const event of transferEvents) {
      if (
        event.args.from.toLowerCase() === expectedFrom.toLowerCase() &&
        event.args.to.toLowerCase() === expectedTo.toLowerCase() &&
        formatUSDT(event.args.value) === expectedAmount
      ) {
        return true
      }
    }

    return false
  } catch (error) {
    console.error('验证 USDT 转账失败:', error)
    return false
  }
}

// 生成钱包签名验证消息
export function generateVerificationMessage(address: string, timestamp: number): string {
  return `BitMarket 钱包验证\n地址: ${address}\n时间戳: ${timestamp}\n请签名此消息以验证钱包所有权`
}

// 验证钱包签名
export async function verifyWalletSignature(
  address: string,
  message: string,
  signature: string
): Promise<boolean> {
  try {
    const recoveredAddress = ethers.verifyMessage(message, signature)
    return recoveredAddress.toLowerCase() === address.toLowerCase()
  } catch (error) {
    console.error('验证钱包签名失败:', error)
    return false
  }
}

// 估算 Gas 费用
export async function estimateGasFee(
  to: string,
  data: string = '0x',
  value: string = '0'
): Promise<{
  gasLimit: bigint
  gasPrice: bigint
  estimatedFee: string
}> {
  try {
    const provider = createProvider()
    
    const [gasLimit, gasPrice] = await Promise.all([
      provider.estimateGas({ to, data, value }),
      provider.getGasPrice()
    ])
    
    const estimatedFee = ethers.formatEther(gasLimit * gasPrice)
    
    return {
      gasLimit,
      gasPrice,
      estimatedFee
    }
  } catch (error) {
    console.error('估算 Gas 费用失败:', error)
    throw new Error('估算 Gas 费用失败')
  }
}
