/**
 * 批量结算服务
 * 实现延迟提现和批量处理，降低交易成本
 */

import { prisma } from './prisma'
import { getUserBenefits } from './credit-level'

// 结算类型
export enum SettlementType {
  DAILY = 'DAILY',     // 日结算
  WEEKLY = 'WEEKLY',   // 周结算
  MANUAL = 'MANUAL'    // 手动结算
}

// 结算配置
export const SETTLEMENT_CONFIG = {
  [SettlementType.DAILY]: {
    minAmount: 10,        // 最小结算金额
    baseFeeRate: 0.005,   // 基础手续费率 0.5%
    batchDiscount: 0.2,   // 批量折扣 20%
    schedule: '0 2 * * *' // 每日凌晨2点
  },
  [SettlementType.WEEKLY]: {
    minAmount: 50,
    baseFeeRate: 0.003,   // 更低的手续费率
    batchDiscount: 0.3,   // 更高的批量折扣
    schedule: '0 2 * * 1' // 每周一凌晨2点
  },
  [SettlementType.MANUAL]: {
    minAmount: 100,
    baseFeeRate: 0.002,   // 最低手续费率
    batchDiscount: 0.4,   // 最高批量折扣
    schedule: null
  }
}

/**
 * 创建批量结算任务
 */
export async function createBatchSettlement(
  settlementType: SettlementType,
  processedBy?: string
): Promise<string> {
  try {
    const config = SETTLEMENT_CONFIG[settlementType]
    const batchNumber = `BATCH-${settlementType}-${Date.now()}`

    // 查找符合条件的已确认冻结记录
    const eligibleFreezes = await prisma.fundFreeze.findMany({
      where: {
        status: 'CONFIRMED',
        purpose: {
          in: ['PURCHASE', 'CONSUMPTION'] // 只处理购买和消费类型
        },
        amount: {
          gte: config.minAmount
        },
        // 排除已经在其他批次中的记录
        settlementItems: {
          none: {}
        }
      },
      include: {
        user: {
          select: {
            id: true,
            creditLevel: true,
            depositBalance: true
          }
        }
      },
      orderBy: {
        confirmedAt: 'asc' // 按确认时间排序，优先处理早期交易
      }
    })

    if (eligibleFreezes.length === 0) {
      throw new Error('没有符合条件的交易需要结算')
    }

    // 计算总金额和手续费
    let totalAmount = 0
    let totalFee = 0

    const settlementItems = eligibleFreezes.map(freeze => {
      const userBenefits = getUserBenefits(freeze.user.creditLevel as any)
      
      // 计算手续费（考虑用户等级折扣）
      const baseFee = freeze.amount * config.baseFeeRate
      const levelDiscount = baseFee * userBenefits.tradingFeeDiscount
      const batchDiscount = baseFee * config.batchDiscount
      const finalFee = Math.max(0, baseFee - levelDiscount - batchDiscount)
      
      totalAmount += freeze.amount
      totalFee += finalFee

      return {
        userId: freeze.userId,
        freezeId: freeze.id,
        originalAmount: freeze.amount,
        platformFee: freeze.platformFee || 0,
        settlementFee: finalFee,
        finalAmount: freeze.amount - finalFee
      }
    })

    // 创建批量结算记录
    const batchSettlement = await prisma.batchSettlement.create({
      data: {
        batchNumber,
        settlementType,
        totalAmount,
        totalFee,
        userCount: new Set(settlementItems.map(item => item.userId)).size,
        transactionCount: settlementItems.length,
        status: 'PENDING',
        startTime: new Date(),
        processedBy,
        settlements: {
          create: settlementItems
        }
      }
    })

    console.log(`创建批量结算任务: ${batchNumber}, 交易数: ${settlementItems.length}, 总金额: ${totalAmount} USDT`)

    return batchSettlement.id

  } catch (error) {
    console.error('创建批量结算任务失败:', error)
    throw error
  }
}

/**
 * 处理批量结算
 */
export async function processBatchSettlement(batchId: string): Promise<{
  success: boolean
  processedCount: number
  failedCount: number
  totalAmount: number
}> {
  try {
    const batch = await prisma.batchSettlement.findUnique({
      where: { id: batchId },
      include: {
        settlements: {
          where: {
            status: 'PENDING'
          },
          include: {
            freeze: true,
            user: true
          }
        }
      }
    })

    if (!batch) {
      throw new Error('批量结算记录不存在')
    }

    if (batch.status !== 'PENDING') {
      throw new Error(`批量结算状态不正确: ${batch.status}`)
    }

    // 更新批次状态为处理中
    await prisma.batchSettlement.update({
      where: { id: batchId },
      data: { status: 'PROCESSING' }
    })

    let processedCount = 0
    let failedCount = 0
    let totalAmount = 0

    // 逐个处理结算项目
    for (const settlement of batch.settlements) {
      try {
        await prisma.$transaction(async (tx) => {
          // 更新冻结记录状态
          await tx.fundFreeze.update({
            where: { id: settlement.freezeId },
            data: {
              status: 'SETTLED',
              settledAt: new Date(),
              settledBy: batch.processedBy
            }
          })

          // 从用户担保金中扣除
          await tx.user.update({
            where: { id: settlement.userId },
            data: {
              depositBalance: {
                decrement: settlement.originalAmount
              }
            }
          })

          // 如果有目标用户，增加其余额
          if (settlement.freeze.toUserId) {
            await tx.user.update({
              where: { id: settlement.freeze.toUserId },
              data: {
                depositBalance: {
                  increment: settlement.finalAmount
                }
              }
            })
          }

          // 更新结算项目状态
          await tx.settlementItem.update({
            where: { id: settlement.id },
            data: {
              status: 'COMPLETED',
              processedAt: new Date()
            }
          })
        })

        processedCount++
        totalAmount += settlement.finalAmount

      } catch (error) {
        console.error(`处理结算项目 ${settlement.id} 失败:`, error)
        
        // 标记为失败
        await prisma.settlementItem.update({
          where: { id: settlement.id },
          data: {
            status: 'FAILED',
            errorMessage: error instanceof Error ? error.message : '未知错误',
            processedAt: new Date()
          }
        })

        failedCount++
      }
    }

    // 更新批次状态
    const finalStatus = failedCount === 0 ? 'COMPLETED' : 'COMPLETED'
    await prisma.batchSettlement.update({
      where: { id: batchId },
      data: {
        status: finalStatus,
        endTime: new Date(),
        processedAt: new Date()
      }
    })

    console.log(`批量结算完成: ${batchId}, 成功: ${processedCount}, 失败: ${failedCount}`)

    return {
      success: failedCount === 0,
      processedCount,
      failedCount,
      totalAmount
    }

  } catch (error) {
    console.error('处理批量结算失败:', error)
    
    // 更新批次状态为失败
    await prisma.batchSettlement.update({
      where: { id: batchId },
      data: {
        status: 'FAILED',
        endTime: new Date(),
        errorMessage: error instanceof Error ? error.message : '未知错误'
      }
    })

    throw error
  }
}

/**
 * 自动创建和处理日结算
 */
export async function runDailySettlement(): Promise<void> {
  try {
    console.log('开始执行日结算...')
    
    const batchId = await createBatchSettlement(SettlementType.DAILY, 'SYSTEM')
    const result = await processBatchSettlement(batchId)
    
    console.log(`日结算完成: 处理 ${result.processedCount} 笔交易, 总金额 ${result.totalAmount} USDT`)

  } catch (error) {
    console.error('日结算执行失败:', error)
  }
}

/**
 * 自动创建和处理周结算
 */
export async function runWeeklySettlement(): Promise<void> {
  try {
    console.log('开始执行周结算...')
    
    const batchId = await createBatchSettlement(SettlementType.WEEKLY, 'SYSTEM')
    const result = await processBatchSettlement(batchId)
    
    console.log(`周结算完成: 处理 ${result.processedCount} 笔交易, 总金额 ${result.totalAmount} USDT`)

  } catch (error) {
    console.error('周结算执行失败:', error)
  }
}

/**
 * 获取用户的待结算金额
 */
export async function getUserPendingSettlement(userId: string): Promise<{
  totalAmount: number
  transactionCount: number
  estimatedFee: number
  nextSettlementDate: Date
}> {
  try {
    const pendingFreezes = await prisma.fundFreeze.findMany({
      where: {
        userId,
        status: 'CONFIRMED',
        purpose: {
          in: ['PURCHASE', 'CONSUMPTION']
        },
        settlementItems: {
          none: {}
        }
      }
    })

    const totalAmount = pendingFreezes.reduce((sum, freeze) => sum + freeze.amount, 0)
    const transactionCount = pendingFreezes.length

    // 估算手续费（使用日结算费率）
    const config = SETTLEMENT_CONFIG[SettlementType.DAILY]
    const estimatedFee = totalAmount * config.baseFeeRate * (1 - config.batchDiscount)

    // 计算下次结算时间（明天凌晨2点）
    const nextSettlementDate = new Date()
    nextSettlementDate.setDate(nextSettlementDate.getDate() + 1)
    nextSettlementDate.setHours(2, 0, 0, 0)

    return {
      totalAmount,
      transactionCount,
      estimatedFee,
      nextSettlementDate
    }

  } catch (error) {
    console.error('获取用户待结算金额失败:', error)
    throw error
  }
}

/**
 * 获取批量结算历史
 */
export async function getSettlementHistory(
  limit: number = 20,
  settlementType?: SettlementType
): Promise<any[]> {
  try {
    const where: any = {}
    if (settlementType) {
      where.settlementType = settlementType
    }

    const settlements = await prisma.batchSettlement.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        _count: {
          select: {
            settlements: true
          }
        }
      }
    })

    return settlements

  } catch (error) {
    console.error('获取结算历史失败:', error)
    throw error
  }
}

/**
 * 计算批量结算节省的成本
 */
export async function calculateCostSavings(batchId: string): Promise<{
  originalCost: number
  batchCost: number
  savings: number
  savingsRate: number
}> {
  try {
    const batch = await prisma.batchSettlement.findUnique({
      where: { id: batchId },
      include: {
        settlements: true
      }
    })

    if (!batch) {
      throw new Error('批量结算记录不存在')
    }

    // 计算如果单独处理每笔交易的成本
    const originalCost = batch.settlements.reduce((sum, settlement) => {
      // 假设单独处理的手续费率为 1%
      return sum + (settlement.originalAmount * 0.01)
    }, 0)

    const batchCost = batch.totalFee
    const savings = originalCost - batchCost
    const savingsRate = originalCost > 0 ? (savings / originalCost) * 100 : 0

    return {
      originalCost,
      batchCost,
      savings,
      savingsRate
    }

  } catch (error) {
    console.error('计算成本节省失败:', error)
    throw error
  }
}
