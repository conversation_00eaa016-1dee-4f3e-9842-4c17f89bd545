import { io, Socket } from 'socket.io-client'
import { getSession } from 'next-auth/react'

class SocketManager {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  async connect() {
    if (this.socket?.connected) {
      return this.socket
    }

    try {
      const session = await getSession()
      if (!session?.user?.id) {
        throw new Error('No authenticated user')
      }

      // 获取WebSocket认证token
      const tokenResponse = await fetch('/api/auth/socket-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!tokenResponse.ok) {
        throw new Error('Failed to get socket token')
      }

      const { token } = await tokenResponse.json()

      this.socket = io({
        path: '/api/socket',
        auth: { token },
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true
      })

      this.setupEventHandlers()
      
      return new Promise<Socket>((resolve, reject) => {
        if (!this.socket) {
          reject(new Error('Socket not initialized'))
          return
        }

        this.socket.on('connect', () => {
          console.log('Connected to WebSocket server')
          this.reconnectAttempts = 0
          
          // 加入用户的订单房间
          this.socket?.emit('join_order_rooms')
          
          resolve(this.socket!)
        })

        this.socket.on('connect_error', (error) => {
          console.error('WebSocket connection error:', error)
          reject(error)
        })

        // 设置连接超时
        setTimeout(() => {
          if (!this.socket?.connected) {
            reject(new Error('Connection timeout'))
          }
        }, 10000)
      })

    } catch (error) {
      console.error('Failed to connect to WebSocket:', error)
      throw error
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from WebSocket server:', reason)
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，不自动重连
        return
      }

      // 自动重连
      this.attemptReconnect()
    })

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('Reconnected to WebSocket server, attempt:', attemptNumber)
      this.reconnectAttempts = 0
      
      // 重新加入订单房间
      this.socket?.emit('join_order_rooms')
    })

    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection error:', error)
    })

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error)
    })
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`)

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error)
        this.attemptReconnect()
      })
    }, delay)
  }

  getSocket() {
    return this.socket
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  // 发送消息
  sendMessage(orderId: string, content: string, messageType: string = 'TEXT') {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit('send_message', {
        orderId,
        content,
        messageType
      })

      // 监听消息发送结果
      const timeout = setTimeout(() => {
        reject(new Error('Message send timeout'))
      }, 5000)

      this.socket!.once('new_message', (message) => {
        clearTimeout(timeout)
        resolve(message)
      })

      this.socket!.once('error', (error) => {
        clearTimeout(timeout)
        reject(error)
      })
    })
  }

  // 开始打字
  startTyping(orderId: string) {
    if (this.socket?.connected) {
      this.socket.emit('typing_start', { orderId })
    }
  }

  // 停止打字
  stopTyping(orderId: string) {
    if (this.socket?.connected) {
      this.socket.emit('typing_stop', { orderId })
    }
  }

  // 标记消息为已读
  markMessagesAsRead(orderId: string) {
    if (this.socket?.connected) {
      this.socket.emit('mark_messages_read', { orderId })
    }
  }

  // 获取在线用户列表
  getOnlineUsers() {
    if (this.socket?.connected) {
      this.socket.emit('get_online_users')
    }
  }

  // 监听事件
  on(event: string, callback: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.on(event, callback)
    }
  }

  // 移除事件监听
  off(event: string, callback?: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.off(event, callback)
    }
  }

  // 监听一次事件
  once(event: string, callback: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.once(event, callback)
    }
  }
}

// 单例模式
const socketManager = new SocketManager()

export default socketManager
