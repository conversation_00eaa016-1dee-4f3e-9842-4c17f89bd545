import { prisma } from '@/lib/prisma'

/**
 * 手续费计算工具
 */

interface FeeConfig {
  id: string
  type: string
  name: string
  enabled: boolean
  feeType: string
  feeValue: number | null
  minFee: number | null
  maxFee: number | null
  tiers: any | null
  paymentMethod: string | null
  userType: string | null
}

interface FeeResult {
  fee: number
  feeRate: number | null
  configId: string | null
  configName: string | null
  feeType: string | null
}

/**
 * 计算交易手续费
 * @param amount 交易金额
 * @param paymentMethod 支付方式
 * @param userType 用户类型
 * @returns 手续费计算结果
 */
export async function calculateTransactionFee(
  amount: number,
  paymentMethod?: string,
  userType?: string
): Promise<FeeResult> {
  // 获取适用的交易手续费配置
  const configs = await prisma.feeConfig.findMany({
    where: {
      type: 'TRANSACTION',
      enabled: true,
      effectiveTo: null,
      AND: [
        {
          OR: [
            { paymentMethod: null },
            { paymentMethod: paymentMethod || null }
          ]
        },
        {
          OR: [
            { userType: null },
            { userType: userType || null }
          ]
        }
      ]
    },
    orderBy: [
      { paymentMethod: 'desc' }, // 优先使用特定支付方式的配置
      { userType: 'desc' },      // 其次是特定用户类型的配置
      { createdAt: 'desc' }      // 最后是最新的配置
    ]
  })

  // 如果没有找到配置，返回零手续费
  if (!configs || configs.length === 0) {
    return {
      fee: 0,
      feeRate: 0,
      configId: null,
      configName: null,
      feeType: null
    }
  }

  // 使用最匹配的配置（第一个）
  const config = configs[0]
  return calculateFee(amount, config)
}

/**
 * 计算提现手续费
 * @param amount 提现金额
 * @param paymentMethod 提现方式
 * @param userType 用户类型
 * @returns 手续费计算结果
 */
export async function calculateWithdrawalFee(
  amount: number,
  paymentMethod?: string,
  userType?: string
): Promise<FeeResult> {
  // 获取适用的提现手续费配置
  const configs = await prisma.feeConfig.findMany({
    where: {
      type: 'WITHDRAWAL',
      enabled: true,
      effectiveTo: null,
      AND: [
        {
          OR: [
            { paymentMethod: null },
            { paymentMethod: paymentMethod || null }
          ]
        },
        {
          OR: [
            { userType: null },
            { userType: userType || null }
          ]
        }
      ]
    },
    orderBy: [
      { paymentMethod: 'desc' }, // 优先使用特定提现方式的配置
      { userType: 'desc' },      // 其次是特定用户类型的配置
      { createdAt: 'desc' }      // 最后是最新的配置
    ]
  })

  // 如果没有找到配置，返回零手续费
  if (!configs || configs.length === 0) {
    return {
      fee: 0,
      feeRate: 0,
      configId: null,
      configName: null,
      feeType: null
    }
  }

  // 使用最匹配的配置（第一个）
  const config = configs[0]
  return calculateFee(amount, config)
}

/**
 * 根据配置计算手续费
 * @param amount 金额
 * @param config 手续费配置
 * @returns 手续费计算结果
 */
function calculateFee(amount: number, config: FeeConfig): FeeResult {
  let fee = 0
  let feeRate: number | null = null

  if (!config.enabled) {
    return {
      fee: 0,
      feeRate: 0,
      configId: config.id,
      configName: config.name,
      feeType: config.feeType
    }
  }

  switch (config.feeType) {
    case 'FIXED':
      // 固定金额
      fee = config.feeValue || 0
      break

    case 'PERCENTAGE':
      // 百分比
      feeRate = config.feeValue || 0
      fee = amount * (feeRate || 0)
      break

    case 'TIERED':
      // 分段计算
      if (config.tiers && Array.isArray(config.tiers)) {
        // 找到适用的分段
        const tier = config.tiers.find(
          (t: any) => amount >= t.min && amount < t.max
        )

        if (tier) {
          if (tier.type === 'FIXED') {
            fee = tier.rate
          } else {
            feeRate = tier.rate
            fee = amount * (feeRate || 0)
          }
        }
      }
      break
  }

  // 应用最低和最高限制
  if (config.minFee !== null && fee < config.minFee) {
    fee = config.minFee
  }
  if (config.maxFee !== null && fee > config.maxFee) {
    fee = config.maxFee
  }

  return {
    fee,
    feeRate,
    configId: config.id,
    configName: config.name,
    feeType: config.feeType
  }
}

/**
 * 获取所有当前有效的手续费配置
 * @returns 手续费配置列表
 */
export async function getAllActiveConfigs() {
  const configs = await prisma.feeConfig.findMany({
    where: {
      enabled: true,
      effectiveTo: null
    },
    orderBy: [
      { type: 'asc' },
      { paymentMethod: 'asc' },
      { userType: 'asc' }
    ]
  })

  // 按类型分组
  const groupedConfigs = configs.reduce((acc, config) => {
    if (!acc[config.type]) {
      acc[config.type] = []
    }
    acc[config.type].push(config)
    return acc
  }, {} as Record<string, any[]>)

  return groupedConfigs
}

/**
 * 导出手续费配置
 * @returns 手续费配置JSON
 */
export async function exportFeeConfigs() {
  const configs = await prisma.feeConfig.findMany({
    where: {
      enabled: true,
      effectiveTo: null
    }
  })

  return JSON.stringify(configs, null, 2)
}

/**
 * 导入手续费配置
 * @param configsJson 手续费配置JSON
 * @param adminId 管理员ID
 * @returns 导入结果
 */
export async function importFeeConfigs(configsJson: string, adminId: string) {
  try {
    const configs = JSON.parse(configsJson)
    if (!Array.isArray(configs)) {
      throw new Error('无效的配置格式')
    }

    const results = await prisma.$transaction(async (tx) => {
      const imported: any[] = []

      for (const config of configs) {
        // 检查是否存在相同配置
        const existing = await tx.feeConfig.findFirst({
          where: {
            type: config.type,
            name: config.name,
            paymentMethod: config.paymentMethod || null,
            userType: config.userType || null,
            effectiveTo: null
          }
        })

        if (existing) {
          // 设置旧配置过期
          await tx.feeConfig.update({
            where: { id: existing.id },
            data: {
              effectiveTo: new Date(),
              updatedBy: adminId
            }
          })
        }

        // 创建新配置
        const newConfig = await tx.feeConfig.create({
          data: {
            type: config.type,
            name: config.name,
            description: config.description || null,
            enabled: config.enabled ?? true,
            feeType: config.feeType,
            feeValue: config.feeValue,
            minFee: config.minFee,
            maxFee: config.maxFee,
            tiers: config.tiers,
            paymentMethod: config.paymentMethod || null,
            userType: config.userType || null,
            effectiveFrom: new Date(),
            createdBy: adminId,
            updatedBy: adminId,
            version: existing ? existing.version + 1 : 1,
            parentId: existing ? (existing.parentId || existing.id) : null
          }
        })

        imported.push(newConfig)
      }

      return imported
    })

    return {
      success: true,
      message: `成功导入 ${results.length} 个配置`,
      configs: results
    }
  } catch (error) {
    console.error('导入手续费配置失败:', error)
    throw error
  }
}
