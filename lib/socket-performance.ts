import { Socket } from 'socket.io'
import { cache } from './cache'
import { performanceMonitor } from './performance-monitor'

// Socket.IO 性能监控和优化工具
export class SocketPerformanceManager {
  private connectionMetrics = new Map<string, {
    connectTime: number
    messageCount: number
    lastActivity: number
    rooms: Set<string>
  }>()
  
  private rateLimiter = new Map<string, {
    count: number
    resetTime: number
  }>()

  // 连接性能监控
  trackConnection(socket: Socket, userId: string) {
    const connectTime = Date.now()
    
    this.connectionMetrics.set(socket.id, {
      connectTime,
      messageCount: 0,
      lastActivity: connectTime,
      rooms: new Set()
    })

    // 监控连接事件
    socket.on('disconnect', () => {
      this.trackDisconnection(socket.id)
    })

    // 监控消息事件
    const originalEmit = socket.emit.bind(socket)
    socket.emit = (event: string, ...args: any[]) => {
      this.trackMessage(socket.id, 'outgoing')
      return originalEmit(event, ...args)
    }

    console.log(`Socket ${socket.id} connected for user ${userId}`)
  }

  // 断开连接监控
  private trackDisconnection(socketId: string) {
    const metrics = this.connectionMetrics.get(socketId)
    if (metrics) {
      const duration = Date.now() - metrics.connectTime
      const messageRate = metrics.messageCount / (duration / 1000)
      
      performanceMonitor.addMetric('socket_connection_duration', duration)
      performanceMonitor.addMetric('socket_message_rate', messageRate)
      
      this.connectionMetrics.delete(socketId)
      console.log(`Socket ${socketId} disconnected after ${duration}ms, ${metrics.messageCount} messages`)
    }
  }

  // 消息计数
  private trackMessage(socketId: string, direction: 'incoming' | 'outgoing') {
    const metrics = this.connectionMetrics.get(socketId)
    if (metrics) {
      metrics.messageCount++
      metrics.lastActivity = Date.now()
    }
  }

  // 速率限制（调整为更宽松的限制）
  checkRateLimit(userId: string, limit: number = 30, windowMs: number = 1000): boolean {
    const now = Date.now()
    const userLimit = this.rateLimiter.get(userId)

    if (!userLimit || now > userLimit.resetTime) {
      this.rateLimiter.set(userId, {
        count: 1,
        resetTime: now + windowMs
      })
      return true
    }

    if (userLimit.count >= limit) {
      return false
    }

    userLimit.count++
    return true
  }

  // 房间管理优化
  async optimizeRoomJoining(socket: Socket, userId: string) {
    const cacheKey = `user_rooms:${userId}`
    let rooms = await cache.get<string[]>(cacheKey)

    if (!rooms) {
      // 这里应该从数据库获取用户应该加入的房间
      // 例如：用户的订单房间、群组房间等
      rooms = await this.getUserRooms(userId)
      await cache.set(cacheKey, rooms, 300) // 缓存5分钟
    }

    // 批量加入房间
    rooms.forEach(room => {
      socket.join(room)
      const metrics = this.connectionMetrics.get(socket.id)
      if (metrics) {
        metrics.rooms.add(room)
      }
    })

    return rooms
  }

  // 获取用户房间（需要根据业务逻辑实现）
  private async getUserRooms(userId: string): Promise<string[]> {
    // 这里应该查询数据库获取用户相关的房间
    // 例如：订单房间、群组房间等
    return []
  }

  // 内存清理
  cleanup() {
    const now = Date.now()
    const maxIdleTime = 30 * 60 * 1000 // 30分钟

    // 清理长时间不活跃的连接指标
    for (const [socketId, metrics] of this.connectionMetrics.entries()) {
      if (now - metrics.lastActivity > maxIdleTime) {
        this.connectionMetrics.delete(socketId)
      }
    }

    // 清理过期的速率限制记录
    for (const [userId, limit] of this.rateLimiter.entries()) {
      if (now > limit.resetTime) {
        this.rateLimiter.delete(userId)
      }
    }
  }

  // 获取性能统计
  getStats() {
    const activeConnections = this.connectionMetrics.size
    const totalRooms = Array.from(this.connectionMetrics.values())
      .reduce((total, metrics) => total + metrics.rooms.size, 0)
    
    const avgMessagesPerConnection = Array.from(this.connectionMetrics.values())
      .reduce((total, metrics) => total + metrics.messageCount, 0) / activeConnections || 0

    return {
      activeConnections,
      totalRooms,
      avgMessagesPerConnection,
      rateLimitedUsers: this.rateLimiter.size
    }
  }
}

// 消息验证中间件
export function validateMessage(data: any): { valid: boolean; error?: string } {
  if (!data || typeof data !== 'object') {
    return { valid: false, error: '无效的消息格式' }
  }

  if (!data.content || typeof data.content !== 'string') {
    return { valid: false, error: '消息内容不能为空' }
  }

  if (data.content.length > 1000) {
    return { valid: false, error: '消息内容过长' }
  }

  if (data.content.trim().length === 0) {
    return { valid: false, error: '消息内容不能为空' }
  }

  return { valid: true }
}

// 房间权限验证
export async function validateRoomAccess(
  userId: string, 
  roomId: string
): Promise<{ hasAccess: boolean; error?: string }> {
  try {
    // 缓存权限检查结果
    const cacheKey = `room_access:${roomId}:${userId}`
    const cachedResult = await cache.get<{ hasAccess: boolean }>(cacheKey)
    
    if (cachedResult !== null) {
      return cachedResult
    }

    // 这里应该实现具体的权限检查逻辑
    // 例如：检查用户是否是订单的买家或卖家
    let hasAccess = false
    
    if (roomId.startsWith('order_')) {
      const orderId = roomId.replace('order_', '')
      // 检查订单权限的逻辑
      hasAccess = await checkOrderAccess(userId, orderId)
    }

    const result = { hasAccess }
    
    // 缓存结果10分钟
    await cache.set(cacheKey, result, 600)
    
    return result
  } catch (error) {
    return { 
      hasAccess: false, 
      error: error instanceof Error ? error.message : '权限检查失败' 
    }
  }
}

// 检查订单访问权限
async function checkOrderAccess(userId: string, orderId: string): Promise<boolean> {
  try {
    // 这里应该查询数据库检查用户是否有权限访问该订单
    // 暂时返回 true，实际实现需要查询数据库
    return true
  } catch (error) {
    console.error('Order access check error:', error)
    return false
  }
}

// 连接池管理
export class SocketConnectionPool {
  private pools = new Map<string, Set<string>>() // userId -> Set<socketId>
  private socketToUser = new Map<string, string>() // socketId -> userId

  addConnection(userId: string, socketId: string) {
    if (!this.pools.has(userId)) {
      this.pools.set(userId, new Set())
    }
    
    this.pools.get(userId)!.add(socketId)
    this.socketToUser.set(socketId, userId)
  }

  removeConnection(socketId: string) {
    const userId = this.socketToUser.get(socketId)
    if (userId) {
      const userSockets = this.pools.get(userId)
      if (userSockets) {
        userSockets.delete(socketId)
        if (userSockets.size === 0) {
          this.pools.delete(userId)
        }
      }
      this.socketToUser.delete(socketId)
    }
  }

  getUserSockets(userId: string): Set<string> {
    return this.pools.get(userId) || new Set()
  }

  isUserOnline(userId: string): boolean {
    const sockets = this.pools.get(userId)
    return sockets ? sockets.size > 0 : false
  }

  getOnlineUsers(): string[] {
    return Array.from(this.pools.keys())
  }

  getStats() {
    return {
      totalUsers: this.pools.size,
      totalConnections: this.socketToUser.size,
      avgConnectionsPerUser: this.socketToUser.size / this.pools.size || 0
    }
  }
}

// 单例实例
export const socketPerformanceManager = new SocketPerformanceManager()
export const socketConnectionPool = new SocketConnectionPool()

// 定期清理
setInterval(() => {
  socketPerformanceManager.cleanup()
}, 5 * 60 * 1000) // 每5分钟清理一次
