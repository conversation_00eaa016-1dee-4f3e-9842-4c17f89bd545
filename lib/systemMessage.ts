import { prisma } from './prisma'

// 系统消息类型
export const SYSTEM_MESSAGE_TYPES = {
  ORDER_CREATED: 'ORDER_CREATED',
  PAYMENT_UPLOADED: 'PAYMENT_UPLOADED',
  PAYMENT_CONFIRMED: 'PAYMENT_CONFIRMED',
  ORDER_SHIPPED: 'ORDER_SHIPPED',
  ORDER_COMPLETED: 'ORDER_COMPLETED',
  ORDER_CANCELLED: 'ORDER_CANCELLED',
  REFUND_REQUESTED: 'REFUND_REQUESTED'
} as const

// 系统消息模板
const MESSAGE_TEMPLATES = {
  [SYSTEM_MESSAGE_TYPES.ORDER_CREATED]: '订单已创建，请及时付款',
  [SYSTEM_MESSAGE_TYPES.PAYMENT_UPLOADED]: '买家已上传支付凭证，请确认收款',
  [SYSTEM_MESSAGE_TYPES.PAYMENT_CONFIRMED]: '卖家已确认收款，请及时发货',
  [SYSTEM_MESSAGE_TYPES.ORDER_SHIPPED]: '卖家已发货，请注意查收',
  [SYSTEM_MESSAGE_TYPES.ORDER_COMPLETED]: '订单已完成，感谢您的交易',
  [SYSTEM_MESSAGE_TYPES.ORDER_CANCELLED]: '订单已取消',
  [SYSTEM_MESSAGE_TYPES.REFUND_REQUESTED]: '买家申请退款，请处理'
}

// 发送系统消息
export async function sendSystemMessage(
  orderId: string,
  messageType: keyof typeof SYSTEM_MESSAGE_TYPES,
  recipientId: string,
  customContent?: string
) {
  try {
    const content = customContent || MESSAGE_TEMPLATES[messageType]
    
    // 获取订单信息以确定发送者（系统消息的发送者设为对方）
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        buyerId: true,
        sellerId: true
      }
    })

    if (!order) {
      throw new Error('订单不存在')
    }

    // 系统消息的发送者设为对方用户
    const senderId = recipientId === order.buyerId ? order.sellerId : order.buyerId

    await prisma.message.create({
      data: {
        orderId,
        content,
        messageType: 'SYSTEM',
        senderId,
        receiverId: recipientId,
        status: 'DELIVERED'
      }
    })

    console.log(`System message sent: ${messageType} to ${recipientId}`)
  } catch (error) {
    console.error('Failed to send system message:', error)
  }
}

// 订单状态变化时发送相应的系统消息
export async function handleOrderStatusChange(
  orderId: string,
  newStatus: string,
  buyerId: string,
  sellerId: string
) {
  try {
    switch (newStatus) {
      case 'PENDING_PAYMENT':
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.ORDER_CREATED, buyerId)
        break
      
      case 'PAID':
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.PAYMENT_UPLOADED, sellerId)
        break
      
      case 'SHIPPED':
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.ORDER_SHIPPED, buyerId)
        break
      
      case 'COMPLETED':
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.ORDER_COMPLETED, buyerId)
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.ORDER_COMPLETED, sellerId)
        break
      
      case 'CANCELLED':
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.ORDER_CANCELLED, buyerId)
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.ORDER_CANCELLED, sellerId)
        break
      
      case 'REFUND_REQUESTED':
        await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.REFUND_REQUESTED, sellerId)
        break
    }
  } catch (error) {
    console.error('Failed to handle order status change:', error)
  }
}

// 支付确认时发送系统消息
export async function handlePaymentConfirmed(orderId: string, buyerId: string) {
  try {
    await sendSystemMessage(orderId, SYSTEM_MESSAGE_TYPES.PAYMENT_CONFIRMED, buyerId)
  } catch (error) {
    console.error('Failed to send payment confirmed message:', error)
  }
}
