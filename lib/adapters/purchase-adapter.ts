/**
 * 商品购买场景适配器
 * 处理商品购买的资金流动逻辑
 */

import { 
  freezeFunds, 
  confirmStatus, 
  settleFunds, 
  cancelFreeze, 
  getAvailableBalance,
  FundPurpose 
} from '../fund-flow'
import { prisma } from '../prisma'

// 购买流程状态
export enum PurchaseFlowStatus {
  BALANCE_CHECK = 'BALANCE_CHECK',     // 余额检查
  FUNDS_FROZEN = 'FUNDS_FROZEN',       // 资金已冻结
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED', // 支付已确认
  SHIPPED = 'SHIPPED',                 // 已发货
  DELIVERED = 'DELIVERED',             // 已收货
  COMPLETED = 'COMPLETED',             // 交易完成
  CANCELLED = 'CANCELLED'              // 已取消
}

// 购买参数接口
export interface PurchaseParams {
  userId: string
  orderId: string
  totalAmount: number
  sellerId: string
  platformFeeRate?: number
  metadata?: any
}

// 购买流程结果
export interface PurchaseFlowResult {
  success: boolean
  freezeId?: string
  availableBalance?: number
  requiredAmount?: number
  message: string
}

/**
 * 检查用户余额是否充足
 */
export async function checkPurchaseBalance(userId: string, requiredAmount: number): Promise<{
  sufficient: boolean
  availableBalance: number
  shortfall: number
}> {
  try {
    const availableBalance = await getAvailableBalance(userId)
    const sufficient = availableBalance >= requiredAmount
    const shortfall = sufficient ? 0 : requiredAmount - availableBalance

    return {
      sufficient,
      availableBalance,
      shortfall
    }
  } catch (error) {
    console.error('检查购买余额失败:', error)
    throw error
  }
}

/**
 * 启动购买流程 - 冻结资金
 */
export async function initiatePurchase(params: PurchaseParams): Promise<PurchaseFlowResult> {
  const { userId, orderId, totalAmount, sellerId, metadata } = params

  try {
    // 首先检查余额
    const balanceCheck = await checkPurchaseBalance(userId, totalAmount)
    
    if (!balanceCheck.sufficient) {
      return {
        success: false,
        availableBalance: balanceCheck.availableBalance,
        requiredAmount: totalAmount,
        message: `余额不足，需要 ${balanceCheck.shortfall} USDT`
      }
    }

    // 冻结资金
    const freezeId = await freezeFunds({
      userId,
      amount: totalAmount,
      purpose: FundPurpose.PURCHASE,
      relatedId: orderId,
      relatedType: 'ORDER',
      metadata: {
        ...metadata,
        sellerId,
        purchaseFlow: PurchaseFlowStatus.FUNDS_FROZEN
      },
      notes: `商品购买冻结资金，订单: ${orderId}`
    })

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'PENDING_PAYMENT',
        // 使用 escrowNotes 字段存储 freezeId 信息
        escrowNotes: `购买流程冻结资金ID: ${freezeId}`
      }
    })

    return {
      success: true,
      freezeId,
      availableBalance: balanceCheck.availableBalance - totalAmount,
      message: '资金冻结成功，等待支付确认'
    }

  } catch (error) {
    console.error('启动购买流程失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '启动购买流程失败'
    }
  }
}

/**
 * 确认支付 - 买家完成支付后调用
 */
export async function confirmPurchasePayment(orderId: string, confirmedBy?: string): Promise<PurchaseFlowResult> {
  try {
    // 查找订单对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: orderId,
        purpose: FundPurpose.PURCHASE,
        status: 'FROZEN'
      }
    })

    if (!freezeRecord) {
      return {
        success: false,
        message: '未找到对应的资金冻结记录'
      }
    }

    // 确认状态
    await confirmStatus(freezeRecord.id, confirmedBy)

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'PAID',
        paymentConfirmed: true
      }
    })

    return {
      success: true,
      freezeId: freezeRecord.id,
      message: '支付确认成功，等待发货'
    }

  } catch (error) {
    console.error('确认支付失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '确认支付失败'
    }
  }
}

/**
 * 确认收货 - 买家确认收货后执行划扣
 */
export async function confirmPurchaseDelivery(
  orderId: string, 
  platformFeeRate: number = 0.05,
  confirmedBy?: string
): Promise<PurchaseFlowResult> {
  try {
    // 查找订单对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: orderId,
        purpose: FundPurpose.PURCHASE,
        status: 'CONFIRMED'
      },
      include: {
        user: true
      }
    })

    if (!freezeRecord) {
      return {
        success: false,
        message: '未找到对应的已确认资金冻结记录'
      }
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        seller: true
      }
    })

    if (!order) {
      return {
        success: false,
        message: '订单不存在'
      }
    }

    // 执行划扣，将资金转给卖家
    await settleFunds({
      freezeId: freezeRecord.id,
      toUserId: order.sellerId,
      platformFeeRate,
      settledBy: confirmedBy,
      notes: `商品购买完成，订单: ${orderId}`
    })

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'COMPLETED',
        receivedAt: new Date()
      }
    })

    return {
      success: true,
      freezeId: freezeRecord.id,
      message: '交易完成，资金已转给卖家'
    }

  } catch (error) {
    console.error('确认收货失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '确认收货失败'
    }
  }
}

/**
 * 取消购买 - 取消订单并解冻资金
 */
export async function cancelPurchase(
  orderId: string, 
  reason: string,
  cancelledBy?: string
): Promise<PurchaseFlowResult> {
  try {
    // 查找订单对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: orderId,
        purpose: FundPurpose.PURCHASE,
        status: {
          in: ['FROZEN', 'CONFIRMED']
        }
      }
    })

    if (!freezeRecord) {
      return {
        success: false,
        message: '未找到对应的资金冻结记录'
      }
    }

    // 取消冻结
    await cancelFreeze(freezeRecord.id, reason, cancelledBy)

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'CANCELLED'
      }
    })

    return {
      success: true,
      freezeId: freezeRecord.id,
      message: '订单已取消，资金已解冻'
    }

  } catch (error) {
    console.error('取消购买失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '取消购买失败'
    }
  }
}

/**
 * 获取购买流程状态
 */
export async function getPurchaseFlowStatus(orderId: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            depositBalance: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!order) {
      throw new Error('订单不存在')
    }

    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: orderId,
        purpose: FundPurpose.PURCHASE
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return {
      order,
      freezeRecord,
      availableBalance: await getAvailableBalance(order.buyerId)
    }

  } catch (error) {
    console.error('获取购买流程状态失败:', error)
    throw error
  }
}
