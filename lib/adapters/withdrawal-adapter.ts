/**
 * 提现场景适配器
 * 处理用户提现的资金流动逻辑
 */

import {
  freezeFunds,
  confirmStatus,
  settleFunds,
  cancelFreeze,
  getAvailableBalance,
  FundPurpose
} from '../fund-flow'
import { prisma } from '../prisma'
import { getUserBenefits } from '../credit-level'
import { calculateComprehensiveWithdrawalFee } from '../withdrawal-fee-reform'

// 提现状态枚举
export enum WithdrawalStatus {
  PENDING = 'PENDING',           // 待审核
  APPROVED = 'APPROVED',         // 已批准
  PROCESSING = 'PROCESSING',     // 处理中
  COMPLETED = 'COMPLETED',       // 已完成
  REJECTED = 'REJECTED',         // 已拒绝
  CANCELLED = 'CANCELLED'        // 已取消
}

// 提现参数接口
export interface WithdrawalParams {
  userId: string
  amount: number
  walletAddress: string
  withdrawalFeeRate?: number
  metadata?: any
  notes?: string
}

// 提现结果接口
export interface WithdrawalResult {
  success: boolean
  withdrawalId?: string
  freezeId?: string
  availableBalance?: number
  message: string
}

/**
 * 创建提现申请（优化版本，支持智能费率）
 */
export async function createWithdrawal(params: WithdrawalParams): Promise<WithdrawalResult> {
  const { userId, amount, walletAddress, withdrawalFeeRate, metadata, notes } = params

  try {
    // 验证提现金额
    if (amount <= 0) {
      return {
        success: false,
        message: '提现金额必须大于0'
      }
    }

    // 检查用户余额
    const availableBalance = await getAvailableBalance(userId)
    if (availableBalance < amount) {
      return {
        success: false,
        availableBalance,
        message: `余额不足，可用余额: ${availableBalance} USDT`
      }
    }

    // 验证钱包地址格式（简单验证）
    if (!walletAddress || walletAddress.length < 10) {
      return {
        success: false,
        message: '钱包地址格式不正确'
      }
    }

    // 获取用户信息和智能费率
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        creditLevel: true,
        minWithdrawalAmount: true,
        depositBalance: true
      }
    })

    if (!user) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    // 检查最小提现金额
    if (amount < user.minWithdrawalAmount) {
      return {
        success: false,
        message: `提现金额不能低于 ${user.minWithdrawalAmount} USDT`
      }
    }

    // 使用新的阶梯式费率计算
    const feeCalculation = await calculateComprehensiveWithdrawalFee(userId, amount, false)
    const smartFeeRate = feeCalculation.finalFee / amount

    const result = await prisma.$transaction(async (tx) => {
      // 创建提现记录
      const withdrawal = await tx.withdrawal.create({
        data: {
          userId,
          amount,
          walletAddress,
          status: WithdrawalStatus.PENDING,
          withdrawalFee: amount * smartFeeRate,
          actualAmount: amount * (1 - smartFeeRate),
          metadata: {
            ...metadata,
            originalFeeRate: withdrawalFeeRate,
            smartFeeRate,
            savings: withdrawalFeeRate ? amount * (withdrawalFeeRate - smartFeeRate) : 0
          },
          notes
        }
      })

      // 冻结资金
      const freezeId = await freezeFunds({
        userId,
        amount,
        purpose: FundPurpose.WITHDRAWAL,
        relatedId: withdrawal.id,
        relatedType: 'WITHDRAWAL',
        metadata: {
          ...metadata,
          walletAddress,
          smartFeeRate
        },
        notes: `提现申请冻结资金，提现ID: ${withdrawal.id}`
      })

      return { withdrawal, freezeId }
    })

    return {
      success: true,
      withdrawalId: result.withdrawal.id,
      freezeId: result.freezeId,
      availableBalance: availableBalance - amount,
      message: '提现申请已提交，等待审核'
    }

  } catch (error) {
    console.error('创建提现申请失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '创建提现申请失败'
    }
  }
}

/**
 * 审核提现申请
 */
export async function reviewWithdrawal(
  withdrawalId: string,
  approved: boolean,
  reviewedBy: string,
  reviewNotes?: string
): Promise<WithdrawalResult> {
  try {
    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id: withdrawalId }
    })

    if (!withdrawal) {
      return {
        success: false,
        message: '提现记录不存在'
      }
    }

    if (withdrawal.status !== WithdrawalStatus.PENDING) {
      return {
        success: false,
        message: `提现状态不正确，当前状态: ${withdrawal.status}`
      }
    }

    // 查找对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: withdrawalId,
        purpose: FundPurpose.WITHDRAWAL,
        status: 'FROZEN'
      }
    })

    if (!freezeRecord) {
      return {
        success: false,
        message: '未找到对应的资金冻结记录'
      }
    }

    if (approved) {
      // 批准提现
      await confirmStatus(freezeRecord.id, reviewedBy)
      
      await prisma.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: WithdrawalStatus.APPROVED,
          reviewedBy,
          reviewedAt: new Date(),
          reviewNotes
        }
      })

      return {
        success: true,
        withdrawalId,
        freezeId: freezeRecord.id,
        message: '提现申请已批准，准备处理'
      }
    } else {
      // 拒绝提现
      await cancelFreeze(freezeRecord.id, reviewNotes || '提现申请被拒绝', reviewedBy)
      
      await prisma.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: WithdrawalStatus.REJECTED,
          reviewedBy,
          reviewedAt: new Date(),
          reviewNotes
        }
      })

      return {
        success: true,
        withdrawalId,
        freezeId: freezeRecord.id,
        message: '提现申请已拒绝，资金已解冻'
      }
    }

  } catch (error) {
    console.error('审核提现申请失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '审核提现申请失败'
    }
  }
}

/**
 * 处理提现 - 实际转账
 */
export async function processWithdrawal(
  withdrawalId: string,
  txHash: string,
  processedBy: string
): Promise<WithdrawalResult> {
  try {
    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id: withdrawalId }
    })

    if (!withdrawal) {
      return {
        success: false,
        message: '提现记录不存在'
      }
    }

    if (withdrawal.status !== WithdrawalStatus.APPROVED) {
      return {
        success: false,
        message: `提现状态不正确，当前状态: ${withdrawal.status}`
      }
    }

    // 查找对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: withdrawalId,
        purpose: FundPurpose.WITHDRAWAL,
        status: 'CONFIRMED'
      }
    })

    if (!freezeRecord) {
      return {
        success: false,
        message: '未找到对应的已确认资金冻结记录'
      }
    }

    // 执行划扣（提现不转给其他用户，而是从系统中移除）
    await settleFunds({
      freezeId: freezeRecord.id,
      toUserId: undefined, // 提现不转给其他用户
      platformFeeRate: withdrawal.withdrawalFee / withdrawal.amount,
      settledBy: processedBy,
      notes: `提现处理完成，交易哈希: ${txHash}`
    })

    // 更新提现记录
    await prisma.withdrawal.update({
      where: { id: withdrawalId },
      data: {
        status: WithdrawalStatus.COMPLETED,
        txHash,
        processedBy,
        processedAt: new Date()
      }
    })

    return {
      success: true,
      withdrawalId,
      freezeId: freezeRecord.id,
      message: '提现处理完成'
    }

  } catch (error) {
    console.error('处理提现失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '处理提现失败'
    }
  }
}

/**
 * 取消提现申请
 */
export async function cancelWithdrawal(
  withdrawalId: string,
  reason: string,
  cancelledBy?: string
): Promise<WithdrawalResult> {
  try {
    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id: withdrawalId }
    })

    if (!withdrawal) {
      return {
        success: false,
        message: '提现记录不存在'
      }
    }

    if (![WithdrawalStatus.PENDING, WithdrawalStatus.APPROVED].includes(withdrawal.status as WithdrawalStatus)) {
      return {
        success: false,
        message: `无法取消提现，当前状态: ${withdrawal.status}`
      }
    }

    // 查找对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: withdrawalId,
        purpose: FundPurpose.WITHDRAWAL,
        status: {
          in: ['FROZEN', 'CONFIRMED']
        }
      }
    })

    if (freezeRecord) {
      await cancelFreeze(freezeRecord.id, reason, cancelledBy)
    }

    // 更新提现记录
    await prisma.withdrawal.update({
      where: { id: withdrawalId },
      data: {
        status: WithdrawalStatus.CANCELLED,
        cancelledBy,
        cancelledAt: new Date(),
        cancelReason: reason
      }
    })

    return {
      success: true,
      withdrawalId,
      freezeId: freezeRecord?.id,
      message: '提现申请已取消，资金已解冻'
    }

  } catch (error) {
    console.error('取消提现申请失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '取消提现申请失败'
    }
  }
}

/**
 * 获取用户提现记录
 */
export async function getUserWithdrawals(userId: string, limit: number = 20) {
  try {
    const withdrawals = await prisma.withdrawal.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return withdrawals
  } catch (error) {
    console.error('获取用户提现记录失败:', error)
    throw error
  }
}

/**
 * 计算智能提现费率
 * 基于用户等级、提现金额、时间等因素动态调整
 */
async function calculateSmartWithdrawalFee(
  userId: string,
  amount: number,
  baseFeeRate?: number
): Promise<number> {
  try {
    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        creditLevel: true,
        depositBalance: true,
        createdAt: true
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 基础费率（如果未提供则使用默认值）
    let feeRate = baseFeeRate || 0.01 // 默认1%

    // 1. 用户等级折扣
    const userBenefits = getUserBenefits(user.creditLevel as any)
    const levelDiscount = userBenefits.withdrawalFeeDiscount
    feeRate *= (1 - levelDiscount)

    // 2. 大额提现折扣
    if (amount >= 1000) {
      feeRate *= 0.8 // 20% 折扣
    } else if (amount >= 500) {
      feeRate *= 0.9 // 10% 折扣
    }

    // 3. 高余额用户折扣
    if (user.depositBalance >= 5000) {
      feeRate *= 0.7 // 30% 折扣
    } else if (user.depositBalance >= 2000) {
      feeRate *= 0.85 // 15% 折扣
    }

    // 4. 错峰提现折扣（非工作时间）
    const now = new Date()
    const hour = now.getHours()
    if (hour < 8 || hour > 18) { // 非工作时间
      feeRate *= 0.9 // 10% 折扣
    }

    // 5. 老用户折扣
    const accountAgeDays = Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24))
    if (accountAgeDays >= 365) { // 一年以上老用户
      feeRate *= 0.9 // 10% 折扣
    }

    // 确保费率不低于最小值
    const minFeeRate = 0.001 // 最低0.1%
    feeRate = Math.max(feeRate, minFeeRate)

    return feeRate

  } catch (error) {
    console.error('计算智能提现费率失败:', error)
    return baseFeeRate || 0.01 // 返回默认费率
  }
}

/**
 * 获取提现费率预估
 */
export async function getWithdrawalFeeEstimate(
  userId: string,
  amount: number
): Promise<{
  baseFeeRate: number
  smartFeeRate: number
  baseFee: number
  smartFee: number
  savings: number
  savingsRate: number
}> {
  try {
    const baseFeeRate = 0.01 // 基础费率1%
    const smartFeeRate = await calculateSmartWithdrawalFee(userId, amount, baseFeeRate)

    const baseFee = amount * baseFeeRate
    const smartFee = amount * smartFeeRate
    const savings = baseFee - smartFee
    const savingsRate = baseFee > 0 ? (savings / baseFee) * 100 : 0

    return {
      baseFeeRate,
      smartFeeRate,
      baseFee,
      smartFee,
      savings,
      savingsRate
    }

  } catch (error) {
    console.error('获取提现费率预估失败:', error)
    throw error
  }
}

/**
 * 推荐最佳提现时机
 */
export async function getOptimalWithdrawalTime(userId: string, amount: number): Promise<{
  currentFee: number
  optimalTime: Date
  optimalFee: number
  potentialSavings: number
  recommendation: string
}> {
  try {
    // 计算当前费率
    const currentFeeRate = await calculateSmartWithdrawalFee(userId, amount)
    const currentFee = amount * currentFeeRate

    // 计算最佳时间（非工作时间）
    const now = new Date()
    const optimalTime = new Date(now)

    // 如果当前是工作时间，推荐到晚上
    if (now.getHours() >= 8 && now.getHours() <= 18) {
      optimalTime.setHours(20, 0, 0, 0) // 晚上8点
      if (optimalTime <= now) {
        optimalTime.setDate(optimalTime.getDate() + 1) // 明天晚上
      }
    } else {
      // 当前已经是最佳时间
      optimalTime.setTime(now.getTime())
    }

    // 计算最佳时间的费率（假设有错峰折扣）
    const optimalFeeRate = currentFeeRate * 0.9 // 10% 错峰折扣
    const optimalFee = amount * optimalFeeRate
    const potentialSavings = currentFee - optimalFee

    let recommendation = ''
    if (potentialSavings > 0.1) { // 如果能节省超过0.1 USDT
      recommendation = `建议在 ${optimalTime.toLocaleString('zh-CN')} 提现，可节省 ${potentialSavings.toFixed(4)} USDT 手续费`
    } else {
      recommendation = '当前是提现的好时机，手续费已经很优惠了'
    }

    return {
      currentFee,
      optimalTime,
      optimalFee,
      potentialSavings,
      recommendation
    }

  } catch (error) {
    console.error('获取最佳提现时机失败:', error)
    throw error
  }
}
