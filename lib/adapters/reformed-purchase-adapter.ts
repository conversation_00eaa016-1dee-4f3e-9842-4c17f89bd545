/**
 * 改革后的商品购买适配器
 * 取消交易时手续费，改为提现时统一收取
 */

import {
  freezeFunds,
  settleFunds,
  getAvailableBalance,
  FundPurpose
} from '../fund-flow'
import { prisma } from '../prisma'

// 购买流程状态
export enum ReformedPurchaseFlowStatus {
  BALANCE_CHECK = 'BALANCE_CHECK',
  FUNDS_FROZEN = 'FUNDS_FROZEN',
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  SHIPPED = 'SHIPPED',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 购买参数接口
export interface ReformedPurchaseParams {
  userId: string
  orderId: string
  totalAmount: number
  sellerId: string
  metadata?: any
}

// 购买流程结果
export interface ReformedPurchaseFlowResult {
  success: boolean
  freezeId?: string
  availableBalance?: number
  requiredAmount?: number
  message: string
  feeInfo?: {
    transactionFee: number // 现在为0
    note: string
  }
}

/**
 * 启动改革后的购买流程 - 无交易手续费
 */
export async function initiateReformedPurchase(params: ReformedPurchaseParams): Promise<ReformedPurchaseFlowResult> {
  const { userId, orderId, totalAmount, sellerId, metadata } = params

  try {
    // 检查余额（现在只需要商品金额，无需额外手续费）
    const availableBalance = await getAvailableBalance(userId)
    
    if (availableBalance < totalAmount) {
      return {
        success: false,
        availableBalance,
        requiredAmount: totalAmount,
        message: `余额不足，需要 ${totalAmount - availableBalance} USDT`,
        feeInfo: {
          transactionFee: 0,
          note: '改革后交易无手续费，费用将在提现时统一收取'
        }
      }
    }

    // 冻结资金（无手续费）
    const freezeId = await freezeFunds({
      userId,
      amount: totalAmount,
      purpose: FundPurpose.PURCHASE,
      relatedId: orderId,
      relatedType: 'ORDER',
      metadata: {
        ...metadata,
        sellerId,
        reformedFlow: true, // 标记为改革后流程
        noTransactionFee: true
      },
      notes: `改革后商品购买，订单: ${orderId}，无交易手续费`
    })

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'PENDING_PAYMENT',
        metadata: {
          freezeId,
          reformedFlow: true,
          transactionFee: 0
        }
      }
    } as any)

    return {
      success: true,
      freezeId,
      availableBalance: availableBalance - totalAmount,
      message: '资金冻结成功，无交易手续费！费用将在提现时统一收取',
      feeInfo: {
        transactionFee: 0,
        note: '🎉 改革后交易零手续费！提现时享受阶梯式优惠费率'
      }
    }

  } catch (error) {
    console.error('启动改革后购买流程失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '启动购买流程失败'
    }
  }
}

/**
 * 确认收货 - 改革后无平台手续费划扣
 */
export async function confirmReformedPurchaseDelivery(
  orderId: string, 
  confirmedBy?: string
): Promise<ReformedPurchaseFlowResult> {
  try {
    // 查找订单对应的冻结记录
    const freezeRecord = await prisma.fundFreeze.findFirst({
      where: {
        relatedId: orderId,
        purpose: FundPurpose.PURCHASE,
        status: 'CONFIRMED'
      },
      include: {
        user: true
      }
    })

    if (!freezeRecord) {
      return {
        success: false,
        message: '未找到对应的已确认资金冻结记录'
      }
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        seller: true
      }
    })

    if (!order) {
      return {
        success: false,
        message: '订单不存在'
      }
    }

    // 执行划扣，改革后无平台手续费
    await settleFunds({
      freezeId: freezeRecord.id,
      toUserId: order.sellerId,
      platformFeeRate: 0, // 改革后无交易手续费
      settledBy: confirmedBy,
      notes: `改革后商品购买完成，订单: ${orderId}，无平台手续费`
    })

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'COMPLETED',
        receivedAt: new Date()
      }
    })

    return {
      success: true,
      freezeId: freezeRecord.id,
      message: '交易完成，全额转给卖家！无平台手续费',
      feeInfo: {
        transactionFee: 0,
        note: '改革后交易零手续费，卖家收到全额款项'
      }
    }

  } catch (error) {
    console.error('确认改革后收货失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '确认收货失败'
    }
  }
}

/**
 * 获取改革前后的费用对比
 */
export async function getReformComparison(
  userId: string,
  orderAmount: number
): Promise<{
  oldSystem: {
    transactionFee: number
    withdrawalFee: number
    totalFee: number
  }
  newSystem: {
    transactionFee: number
    withdrawalFee: number
    totalFee: number
    savings: number
  }
  recommendation: string
}> {
  try {
    // 旧系统：交易时5%手续费 + 提现时1%手续费
    const oldTransactionFee = orderAmount * 0.05
    const oldWithdrawalFee = orderAmount * 0.01
    const oldTotalFee = oldTransactionFee + oldWithdrawalFee

    // 新系统：交易时0手续费 + 提现时阶梯费率
    const { calculateComprehensiveWithdrawalFee } = require('../withdrawal-fee-reform')
    const newWithdrawalCalc = await calculateComprehensiveWithdrawalFee(userId, orderAmount)
    
    const newTransactionFee = 0
    const newWithdrawalFee = newWithdrawalCalc.finalFee
    const newTotalFee = newTransactionFee + newWithdrawalFee
    const savings = oldTotalFee - newTotalFee

    let recommendation = ''
    if (savings > 0) {
      recommendation = `🎉 改革后可节省 ${savings.toFixed(4)} USDT (${((savings / oldTotalFee) * 100).toFixed(1)}%)`
    } else if (savings === 0) {
      recommendation = '改革后费用相同，但交易体验更流畅'
    } else {
      recommendation = '建议积累更多资金后再提现，可享受更优惠费率'
    }

    return {
      oldSystem: {
        transactionFee: oldTransactionFee,
        withdrawalFee: oldWithdrawalFee,
        totalFee: oldTotalFee
      },
      newSystem: {
        transactionFee: newTransactionFee,
        withdrawalFee: newWithdrawalFee,
        totalFee: newTotalFee,
        savings
      },
      recommendation
    }

  } catch (error) {
    console.error('获取改革对比失败:', error)
    throw error
  }
}

/**
 * 批量迁移现有订单到改革后流程
 */
export async function migrateOrdersToReformedFlow(limit: number = 100): Promise<{
  processed: number
  migrated: number
  errors: number
}> {
  try {
    let processed = 0
    let migrated = 0
    let errors = 0

    // 查找未标记为改革流程的已完成订单
    const orders = await prisma.order.findMany({
      where: {
        status: 'COMPLETED',
        OR: [
          { metadata: { equals: null } },
          { metadata: { not: { path: ['reformedFlow'], equals: true } } }
        ]
      } as any,
      take: limit,
      include: {
        buyer: true,
        seller: true
      }
    })

    for (const order of orders) {
      try {
        // 标记为改革后流程
        await prisma.order.update({
          where: { id: order.id },
          data: {
            metadata: {
              ...((order as any).metadata || {}),
              reformedFlow: true,
              migratedAt: new Date(),
              originalTransactionFee: order.platformFee || 0,
              newTransactionFee: 0
            }
          }
        } as any)

        migrated++
        processed++

      } catch (error) {
        console.error(`迁移订单 ${order.id} 失败:`, error)
        errors++
        processed++
      }
    }

    return { processed, migrated, errors }

  } catch (error) {
    console.error('批量迁移订单失败:', error)
    throw error
  }
}

/**
 * 获取改革效果统计
 */
export async function getReformEffectStatistics(days: number = 30): Promise<{
  totalOrders: number
  reformedOrders: number
  oldSystemOrders: number
  totalFeesSaved: number
  avgOrderAmount: number
  userSatisfactionImprovement: number
}> {
  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const [allOrders, reformedOrders] = await Promise.all([
      prisma.order.findMany({
        where: {
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      }),
      prisma.order.findMany({
        where: {
          createdAt: { gte: startDate },
          status: 'COMPLETED',
          metadata: { path: ['reformedFlow'], equals: true }
        } as any
      })
    ])

    const totalOrders = allOrders.length
    const reformedOrdersCount = reformedOrders.length
    const oldSystemOrders = totalOrders - reformedOrdersCount

    // 计算节省的费用
    const totalFeesSaved = reformedOrders.reduce((sum, order) => {
      const originalFee = ((order as any).metadata)?.originalTransactionFee || order.totalAmount * 0.05
      return sum + originalFee
    }, 0)

    const avgOrderAmount = totalOrders > 0 
      ? allOrders.reduce((sum, order) => sum + order.totalAmount, 0) / totalOrders 
      : 0

    // 模拟用户满意度提升（基于无手续费交易的比例）
    const userSatisfactionImprovement = totalOrders > 0 
      ? (reformedOrdersCount / totalOrders) * 100 
      : 0

    return {
      totalOrders,
      reformedOrders: reformedOrdersCount,
      oldSystemOrders,
      totalFeesSaved,
      avgOrderAmount,
      userSatisfactionImprovement
    }

  } catch (error) {
    console.error('获取改革效果统计失败:', error)
    throw error
  }
}
