/**
 * 信誉展示系统
 * 将担保金转化为可视化的信誉指标
 */

import { prisma } from './prisma'

// 信誉等级配置
export const REPUTATION_LEVELS = {
  NEWCOMER: {
    minScore: 0,
    maxScore: 100,
    stars: 1,
    icon: '🌱',
    title: '新手',
    color: '#94a3b8',
    benefits: ['基础交易功能']
  },
  TRUSTED: {
    minScore: 101,
    maxScore: 300,
    stars: 2,
    icon: '🌿',
    title: '可信用户',
    color: '#22c55e',
    benefits: ['优先展示', '基础客服']
  },
  RELIABLE: {
    minScore: 301,
    maxScore: 600,
    stars: 3,
    icon: '🌳',
    title: '可靠用户',
    color: '#3b82f6',
    benefits: ['优先展示', '快速审核', '专属客服']
  },
  EXPERT: {
    minScore: 601,
    maxScore: 1000,
    stars: 4,
    icon: '🏆',
    title: '专家用户',
    color: '#8b5cf6',
    benefits: ['最高优先级', '免审核', '专属客服', '特殊标识']
  },
  MASTER: {
    minScore: 1001,
    maxScore: Infinity,
    stars: 5,
    icon: '👑',
    title: '大师级用户',
    color: '#f59e0b',
    benefits: ['所有特权', '平台合作伙伴', '收益分成']
  }
}

/**
 * 计算用户信誉分数
 */
export async function calculateReputationScore(userId: string): Promise<{
  totalScore: number
  breakdown: {
    depositScore: number      // 担保金积分
    transactionScore: number  // 交易积分
    reviewScore: number       // 评价积分
    timeScore: number         // 时间积分
    penaltyScore: number      // 扣分项
  }
  level: keyof typeof REPUTATION_LEVELS
  nextLevel?: keyof typeof REPUTATION_LEVELS
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        ordersAsBuyer: {
          where: { status: 'COMPLETED' }
        },
        ordersAsSeller: {
          where: { status: 'COMPLETED' }
        },
        receivedReviews: true,
        creditHistories: {
          where: { changeScore: { lt: 0 } }
        }
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 1. 担保金积分 (每100 USDT = 20分，最高300分)
    const depositScore = Math.min(Math.floor(user.depositBalance / 100) * 20, 300)

    // 2. 交易积分 (每笔完成交易 = 10分，最高400分)
    const totalTransactions = user.ordersAsBuyer.length + user.ordersAsSeller.length
    const transactionScore = Math.min(totalTransactions * 10, 400)

    // 3. 评价积分 (好评+20分，中评+5分，差评-10分，最高200分)
    const reviewScore = Math.min(
      user.receivedReviews.reduce((score, review) => {
        if (review.rating >= 4) return score + 20
        if (review.rating === 3) return score + 5
        return score - 10
      }, 0),
      200
    )

    // 4. 时间积分 (每30天 = 5分，最高100分)
    const accountAgeDays = Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24))
    const timeScore = Math.min(Math.floor(accountAgeDays / 30) * 5, 100)

    // 5. 扣分项 (违规记录等)
    const penaltyScore = user.creditHistories.reduce((sum, history) => sum + Math.abs(history.changeScore), 0)

    const totalScore = Math.max(0, depositScore + transactionScore + reviewScore + timeScore - penaltyScore)

    // 确定信誉等级
    const level = Object.entries(REPUTATION_LEVELS).find(([_, config]) => 
      totalScore >= config.minScore && totalScore <= config.maxScore
    )?.[0] as keyof typeof REPUTATION_LEVELS || 'NEWCOMER'

    // 确定下一等级
    const currentLevelIndex = Object.keys(REPUTATION_LEVELS).indexOf(level)
    const nextLevel = currentLevelIndex < Object.keys(REPUTATION_LEVELS).length - 1 
      ? Object.keys(REPUTATION_LEVELS)[currentLevelIndex + 1] as keyof typeof REPUTATION_LEVELS
      : undefined

    return {
      totalScore,
      breakdown: {
        depositScore,
        transactionScore,
        reviewScore,
        timeScore,
        penaltyScore
      },
      level,
      nextLevel
    }

  } catch (error) {
    console.error('计算信誉分数失败:', error)
    throw error
  }
}

/**
 * 获取用户履约统计
 */
export async function getUserPerformanceStats(userId: string): Promise<{
  totalOrders: number
  completedOrders: number
  cancelledOrders: number
  disputedOrders: number
  fulfillmentRate: number
  avgRating: number
  totalReviews: number
  responseTime: number // 平均响应时间（小时）
}> {
  try {
    const [buyerOrders, sellerOrders, reviews] = await Promise.all([
      prisma.order.findMany({
        where: { buyerId: userId },
        select: { status: true, createdAt: true, escrowFundedAt: true }
      }),
      prisma.order.findMany({
        where: { sellerId: userId },
        select: { status: true, createdAt: true, escrowFundedAt: true }
      }),
      prisma.review.findMany({
        where: { revieweeId: userId },
        select: { rating: true }
      })
    ])

    const allOrders = [...buyerOrders, ...sellerOrders]
    const totalOrders = allOrders.length
    const completedOrders = allOrders.filter(o => o.status === 'COMPLETED').length
    const cancelledOrders = allOrders.filter(o => o.status === 'CANCELLED').length
    const disputedOrders = allOrders.filter(o => o.status === 'DISPUTED').length

    const fulfillmentRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0

    const totalReviews = reviews.length
    const avgRating = totalReviews > 0 
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews 
      : 0

    // 计算平均响应时间（卖家处理订单时间，使用托管资金到账时间作为处理时间指标）
    const sellerOrdersWithProcessing = sellerOrders.filter(o => o.escrowFundedAt)
    const responseTime = sellerOrdersWithProcessing.length > 0
      ? sellerOrdersWithProcessing.reduce((sum, o) => {
          const hours = (o.escrowFundedAt!.getTime() - o.createdAt.getTime()) / (1000 * 60 * 60)
          return sum + hours
        }, 0) / sellerOrdersWithProcessing.length
      : 0

    return {
      totalOrders,
      completedOrders,
      cancelledOrders,
      disputedOrders,
      fulfillmentRate,
      avgRating,
      totalReviews,
      responseTime
    }

  } catch (error) {
    console.error('获取用户履约统计失败:', error)
    throw error
  }
}

/**
 * 生成信誉展示徽章
 */
export function generateReputationBadge(
  level: keyof typeof REPUTATION_LEVELS,
  score: number,
  fulfillmentRate: number
): {
  badge: string
  description: string
  trustLevel: string
} {
  const levelConfig = REPUTATION_LEVELS[level]
  const stars = '⭐'.repeat(levelConfig.stars) + '☆'.repeat(5 - levelConfig.stars)
  
  let trustLevel = '一般'
  if (fulfillmentRate >= 98) trustLevel = '极高'
  else if (fulfillmentRate >= 95) trustLevel = '很高'
  else if (fulfillmentRate >= 90) trustLevel = '较高'

  const badge = `${levelConfig.icon} ${levelConfig.title} ${stars}`
  const description = `信誉分: ${score} | 履约率: ${fulfillmentRate.toFixed(1)}% | 信任度: ${trustLevel}`

  return {
    badge,
    description,
    trustLevel
  }
}

/**
 * 获取用户完整信誉档案
 */
export async function getUserReputationProfile(userId: string): Promise<{
  user: {
    id: string
    name: string | null
    depositBalance: number
    createdAt: Date
  }
  reputation: {
    score: number
    level: keyof typeof REPUTATION_LEVELS
    levelConfig: typeof REPUTATION_LEVELS[keyof typeof REPUTATION_LEVELS]
    breakdown: any
    nextLevel?: {
      level: keyof typeof REPUTATION_LEVELS
      config: typeof REPUTATION_LEVELS[keyof typeof REPUTATION_LEVELS]
      pointsNeeded: number
    }
  }
  performance: any
  badge: {
    badge: string
    description: string
    trustLevel: string
  }
  benefits: string[]
  recommendations: string[]
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        depositBalance: true,
        createdAt: true
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const [reputation, performance] = await Promise.all([
      calculateReputationScore(userId),
      getUserPerformanceStats(userId)
    ])

    const levelConfig = REPUTATION_LEVELS[reputation.level]
    const badge = generateReputationBadge(reputation.level, reputation.totalScore, performance.fulfillmentRate)

    // 下一等级信息
    let nextLevel: {
      level: keyof typeof REPUTATION_LEVELS
      config: typeof REPUTATION_LEVELS[keyof typeof REPUTATION_LEVELS]
      pointsNeeded: number
    } | undefined = undefined
    if (reputation.nextLevel) {
      const nextLevelConfig = REPUTATION_LEVELS[reputation.nextLevel]
      nextLevel = {
        level: reputation.nextLevel,
        config: nextLevelConfig,
        pointsNeeded: nextLevelConfig.minScore - reputation.totalScore
      }
    }

    // 生成个性化建议
    const recommendations: string[] = []
    
    if (reputation.breakdown.depositScore < 300) {
      const neededDeposit = Math.ceil((300 - reputation.breakdown.depositScore) / 20) * 100
      recommendations.push(`增加担保金至 ${user.depositBalance + neededDeposit} USDT 可获得更多信誉分`)
    }
    
    if (performance.totalOrders < 10) {
      recommendations.push('完成更多交易可显著提升信誉等级')
    }
    
    if (performance.avgRating < 4.5 && performance.totalReviews > 0) {
      recommendations.push('提升服务质量以获得更好的用户评价')
    }
    
    if (performance.responseTime > 24) {
      recommendations.push('更快的响应速度有助于提升用户满意度')
    }

    return {
      user,
      reputation: {
        score: reputation.totalScore,
        level: reputation.level,
        levelConfig,
        breakdown: reputation.breakdown,
        nextLevel
      },
      performance,
      badge,
      benefits: levelConfig.benefits,
      recommendations
    }

  } catch (error) {
    console.error('获取用户信誉档案失败:', error)
    throw error
  }
}
