{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "skipDefaultLibCheck": true, "noImplicitAny": false, "importsNotUsedAsValues": "remove", "preserveValueImports": false, "verbatimModuleSyntax": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist", "release", "release/**", "release/**/*", "release/**/*.ts", "release/**/*.tsx", "**/*.test.ts", "**/*.test.tsx", "test-results", "test-results/**", "coverage", "coverage/**"]}