
-- MySQL 数据库设置命令

-- 1. 连接到 MySQL
mysql -u root -p

-- 2. 创建数据库
CREATE DATABASE bitmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 创建专用用户 (可选)
CREATE USER 'bitmarket_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON bitmarket.* TO 'bitmarket_user'@'localhost';
FLUSH PRIVILEGES;

-- 4. 查看数据库
SHOW DATABASES;

-- 5. 使用数据库
USE bitmarket;

-- 6. 查看表
SHOW TABLES;

-- 7. 退出
EXIT;
