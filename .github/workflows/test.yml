name: 测试套件

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行完整测试套件
    - cron: '0 2 * * *'

jobs:
  # 单元测试和集成测试
  test:
    name: 单元测试和集成测试
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 类型检查
      run: npm run type:check
      
    - name: 代码检查
      run: npm run lint
      
    - name: 运行单元测试
      run: npm run test -- --coverage --reporter=json --outputFile=test-results/unit-results.json
      env:
        NODE_ENV: test
        DATABASE_URL: file:./test.db
        
    - name: 运行集成测试
      run: node scripts/test-runner.js integration
      env:
        NODE_ENV: test
        DATABASE_URL: file:./test.db
        
    - name: 上传测试结果
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          test-results/
          coverage/
          
    - name: 上传覆盖率到 Codecov
      uses: codecov/codecov-action@v3
      if: matrix.node-version == '20.x'
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # 端到端测试
  e2e-test:
    name: 端到端测试
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 构建应用
      run: npm run build
      env:
        NODE_ENV: production
        
    - name: 运行端到端测试
      run: node scripts/test-runner.js e2e
      env:
        NODE_ENV: test
        DATABASE_URL: file:./test.db
        
    - name: 上传E2E测试结果
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: test-results/

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[perf-test]')
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行性能测试
      run: node scripts/test-runner.js performance
      env:
        NODE_ENV: test
        DATABASE_URL: file:./test.db
        
    - name: 上传性能测试结果
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: test-results/

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 运行 npm audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
      
    - name: 运行 Snyk 安全扫描
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # 测试报告汇总
  test-summary:
    name: 测试报告汇总
    runs-on: ubuntu-latest
    needs: [test, e2e-test]
    if: always()
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 下载所有测试结果
      uses: actions/download-artifact@v4
      with:
        path: artifacts/
        
    - name: 生成测试摘要
      run: |
        echo "# 🧪 测试结果摘要" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # 检查测试结果
        if [ -f "artifacts/test-results-20.x/unit-results.json" ]; then
          echo "## ✅ 单元测试" >> $GITHUB_STEP_SUMMARY
          echo "单元测试已完成，详细结果请查看artifacts。" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -d "artifacts/e2e-test-results" ]; then
          echo "## 🔄 端到端测试" >> $GITHUB_STEP_SUMMARY
          echo "端到端测试已完成，详细结果请查看artifacts。" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -d "artifacts/performance-test-results" ]; then
          echo "## ⚡ 性能测试" >> $GITHUB_STEP_SUMMARY
          echo "性能测试已完成，详细结果请查看artifacts。" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "## 📊 覆盖率报告" >> $GITHUB_STEP_SUMMARY
        echo "代码覆盖率报告已上传到 Codecov。" >> $GITHUB_STEP_SUMMARY
        
    - name: 发送通知 (失败时)
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#dev-alerts'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  # 依赖更新检查
  dependency-check:
    name: 依赖更新检查
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: 检查过期依赖
      run: |
        npm outdated || true
        echo "## 📦 依赖更新检查" >> $GITHUB_STEP_SUMMARY
        echo "过期依赖列表：" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        npm outdated || echo "所有依赖都是最新的" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        
    - name: 检查安全漏洞
      run: |
        npm audit --audit-level=low || true
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## 🔒 安全漏洞检查" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        npm audit --audit-level=low || echo "未发现安全漏洞" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
