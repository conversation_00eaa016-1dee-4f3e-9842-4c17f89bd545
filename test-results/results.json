{"numTotalTestSuites": 120, "numPassedTestSuites": 120, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 216, "numPassedTests": 216, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1754035123774, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够运行基本测试", "status": "passed", "title": "应该能够运行基本测试", "duration": 0.5317500000001019, "failureMessages": [], "location": {"line": 4, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试字符串", "status": "passed", "title": "应该能够测试字符串", "duration": 0.06525000000056025, "failureMessages": [], "location": {"line": 8, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试对象", "status": "passed", "title": "应该能够测试对象", "duration": 0.22850000000016735, "failureMessages": [], "location": {"line": 12, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试数组", "status": "passed", "title": "应该能够测试数组", "duration": 0.39262499999949796, "failureMessages": [], "location": {"line": 17, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试异步函数", "status": "passed", "title": "应该能够测试异步函数", "duration": 0.2053329999998823, "failureMessages": [], "location": {"line": 23, "column": 3}, "meta": {}}], "startTime": 1754035129709, "endTime": 1754035129710.3926, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/basic.test.ts"}, {"assertionResults": [{"ancestorTitles": ["托管服务测试", "区块链钱包验证"], "fullName": "托管服务测试 区块链钱包验证 应该验证有效的钱包地址格式", "status": "passed", "title": "应该验证有效的钱包地址格式", "duration": 0.9916660000003503, "failureMessages": [], "location": {"line": 368, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "区块链钱包验证"], "fullName": "托管服务测试 区块链钱包验证 应该生成正确的验证消息", "status": "passed", "title": "应该生成正确的验证消息", "duration": 0.23633299999983137, "failureMessages": [], "location": {"line": 376, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "区块链钱包验证"], "fullName": "托管服务测试 区块链钱包验证 应该验证钱包签名", "status": "passed", "title": "应该验证钱包签名", "duration": 0.14366700000027777, "failureMessages": [], "location": {"line": 387, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "风险评估系统"], "fullName": "托管服务测试 风险评估系统 应该正确评估低风险交易", "status": "passed", "title": "应该正确评估低风险交易", "duration": 0.14066700000012133, "failureMessages": [], "location": {"line": 403, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "风险评估系统"], "fullName": "托管服务测试 风险评估系统 应该正确评估高风险交易", "status": "passed", "title": "应该正确评估高风险交易", "duration": 0.12329200000021956, "failureMessages": [], "location": {"line": 416, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "风险评估系统"], "fullName": "托管服务测试 风险评估系统 应该检测新用户风险", "status": "passed", "title": "应该检测新用户风险", "duration": 0.10112500000013824, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "托管订单管理"], "fullName": "托管服务测试 托管订单管理 应该创建托管订单", "status": "passed", "title": "应该创建托管订单", "duration": 0.12670799999978044, "failureMessages": [], "location": {"line": 470, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "托管订单管理"], "fullName": "托管服务测试 托管订单管理 应该更新托管订单状态", "status": "passed", "title": "应该更新托管订单状态", "duration": 0.10449999999991633, "failureMessages": [], "location": {"line": 480, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "争议仲裁系统"], "fullName": "托管服务测试 争议仲裁系统 应该创建争议记录", "status": "passed", "title": "应该创建争议记录", "duration": 0.11666700000023411, "failureMessages": [], "location": {"line": 506, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "争议仲裁系统"], "fullName": "托管服务测试 争议仲裁系统 应该记录仲裁投票", "status": "passed", "title": "应该记录仲裁投票", "duration": 0.15595899999971152, "failureMessages": [], "location": {"line": 516, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该发放投票参与奖励", "status": "passed", "title": "应该发放投票参与奖励", "duration": 0.1305419999998776, "failureMessages": [], "location": {"line": 553, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该发放成功调解奖励", "status": "passed", "title": "应该发放成功调解奖励", "duration": 0.09333300000025702, "failureMessages": [], "location": {"line": 573, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该正确统计奖励信息", "status": "passed", "title": "应该正确统计奖励信息", "duration": 0.10966599999983373, "failureMessages": [], "location": {"line": 591, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该检查月度奖励限制", "status": "passed", "title": "应该检查月度奖励限制", "duration": 0.09637499999962529, "failureMessages": [], "location": {"line": 613, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "区块链交易验证"], "fullName": "托管服务测试 区块链交易验证 应该验证 USDT 转账交易", "status": "passed", "title": "应该验证 USDT 转账交易", "duration": 0.4786669999998594, "failureMessages": [], "location": {"line": 636, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "数据完整性"], "fullName": "托管服务测试 数据完整性 应该维护托管订单和争议的关联关系", "status": "passed", "title": "应该维护托管订单和争议的关联关系", "duration": 0.22633300000006784, "failureMessages": [], "location": {"line": 659, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "数据完整性"], "fullName": "托管服务测试 数据完整性 应该维护用户和奖励的关联关系", "status": "passed", "title": "应该维护用户和奖励的关联关系", "duration": 0.14495799999986048, "failureMessages": [], "location": {"line": 672, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "错误处理"], "fullName": "托管服务测试 错误处理 应该处理无效的用户ID", "status": "passed", "title": "应该处理无效的用户ID", "duration": 0.07650000000012369, "failureMessages": [], "location": {"line": 697, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "错误处理"], "fullName": "托管服务测试 错误处理 应该处理重复的奖励发放", "status": "passed", "title": "应该处理重复的奖励发放", "duration": 0.07929100000001199, "failureMessages": [], "location": {"line": 707, "column": 5}, "meta": {}}, {"ancestorTitles": ["API 端点测试"], "fullName": "API 端点测试 应该测试钱包验证 API", "status": "passed", "title": "应该测试钱包验证 API", "duration": 0.02920799999992596, "failureMessages": [], "location": {"line": 737, "column": 3}, "meta": {}}, {"ancestorTitles": ["API 端点测试"], "fullName": "API 端点测试 应该测试风险评估 API", "status": "passed", "title": "应该测试风险评估 API", "duration": 0.02616699999998673, "failureMessages": [], "location": {"line": 741, "column": 3}, "meta": {}}, {"ancestorTitles": ["API 端点测试"], "fullName": "API 端点测试 应该测试托管订单 API", "status": "passed", "title": "应该测试托管订单 API", "duration": 0.024582999999893218, "failureMessages": [], "location": {"line": 745, "column": 3}, "meta": {}}], "startTime": 1754035126556, "endTime": 1754035126560.145, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/escrow-service.test.ts"}, {"assertionResults": [{"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should not render when user is not logged in", "status": "passed", "title": "should not render when user is not logged in", "duration": 9.464832999999999, "failureMessages": [], "location": {"line": 25, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should render heart icon when user is logged in", "status": "passed", "title": "should render heart icon when user is logged in", "duration": 26.089999999999918, "failureMessages": [], "location": {"line": 36, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should show solid heart when product is favorited", "status": "passed", "title": "should show solid heart when product is favorited", "duration": 9.2484579999998, "failureMessages": [], "location": {"line": 59, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should toggle favorite status when clicked", "status": "passed", "title": "should toggle favorite status when clicked", "duration": 6.810874999999896, "failureMessages": [], "location": {"line": 83, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "Favorite<PERSON><PERSON><PERSON> should show text when showText prop is true", "status": "passed", "title": "should show text when showText prop is true", "duration": 3.1166249999998854, "failureMessages": [], "location": {"line": 127, "column": 3}, "meta": {}}], "startTime": 1754035125377, "endTime": 1754035125431.1167, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/app/__tests__/favorites.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该成功注册新用户", "status": "passed", "title": "应该成功注册新用户", "duration": 5.784124999999904, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该拒绝重复邮箱注册", "status": "passed", "title": "应该拒绝重复邮箱注册", "duration": 1.5417499999998654, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 1.4476250000000164, "failureMessages": [], "location": {"line": 137, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 1.2083339999999225, "failureMessages": [], "location": {"line": 152, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该为已登录用户生成Socket令牌", "status": "passed", "title": "应该为已登录用户生成Socket令牌", "duration": 0.5139999999998963, "failureMessages": [], "location": {"line": 177, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该拒绝未登录用户", "status": "passed", "title": "应该拒绝未登录用户", "duration": 0.27424999999993815, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该处理JWT签名错误", "status": "passed", "title": "应该处理JWT签名错误", "duration": 0.6732090000000426, "failureMessages": [], "location": {"line": 235, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "认证中间件测试"], "fullName": "认证API测试 认证中间件测试 应该验证有效的JWT令牌", "status": "passed", "title": "应该验证有效的JWT令牌", "duration": 0.12266700000009223, "failureMessages": [], "location": {"line": 268, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "认证中间件测试"], "fullName": "认证API测试 认证中间件测试 应该拒绝无效的JWT令牌", "status": "passed", "title": "应该拒绝无效的JWT令牌", "duration": 0.35220800000001873, "failureMessages": [], "location": {"line": 284, "column": 5}, "meta": {}}], "startTime": 1754035125507, "endTime": 1754035125519.3523, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/auth.test.ts"}, {"assertionResults": [{"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该返回订单相关的消息", "status": "passed", "title": "应该返回订单相关的消息", "duration": 4.9363750000002256, "failureMessages": [], "location": {"line": 45, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该拒绝无关用户访问消息", "status": "passed", "title": "应该拒绝无关用户访问消息", "duration": 0.9258749999999054, "failureMessages": [], "location": {"line": 107, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该处理订单不存在的情况", "status": "passed", "title": "应该处理订单不存在的情况", "duration": 0.7135000000002947, "failureMessages": [], "location": {"line": 128, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该成功发送文本消息", "status": "passed", "title": "应该成功发送文本消息", "duration": 0.7272090000001299, "failureMessages": [], "location": {"line": 145, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该成功发送图片消息", "status": "passed", "title": "应该成功发送图片消息", "duration": 0.5601660000002084, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该验证文本消息的内容", "status": "passed", "title": "应该验证文本消息的内容", "duration": 1.1108750000003056, "failureMessages": [], "location": {"line": 267, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该验证文件消息的文件URL", "status": "passed", "title": "应该验证文件消息的文件URL", "duration": 0.4624999999996362, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该拒绝未登录用户发送消息", "status": "passed", "title": "应该拒绝未登录用户发送消息", "duration": 0.2755419999998594, "failureMessages": [], "location": {"line": 306, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该拒绝无关用户发送消息", "status": "passed", "title": "应该拒绝无关用户发送消息", "duration": 0.3102089999997588, "failureMessages": [], "location": {"line": 324, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 1.0093330000004244, "failureMessages": [], "location": {"line": 351, "column": 5}, "meta": {}}], "startTime": 1754035125680, "endTime": 1754035125692.0093, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/messages.test.ts"}, {"assertionResults": [{"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该成功创建订单", "status": "passed", "title": "应该成功创建订单", "duration": 4.712583999999879, "failureMessages": [], "location": {"line": 125, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该拒绝购买自己的商品", "status": "passed", "title": "应该拒绝购买自己的商品", "duration": 0.7479590000002645, "failureMessages": [], "location": {"line": 197, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该检查库存不足", "status": "passed", "title": "应该检查库存不足", "duration": 0.7097909999997682, "failureMessages": [], "location": {"line": 231, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id] - 获取订单详情"], "fullName": "订单API测试 GET /api/orders/[id] - 获取订单详情 应该返回买家的订单详情", "status": "passed", "title": "应该返回买家的订单详情", "duration": 0.697624999999789, "failureMessages": [], "location": {"line": 268, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id] - 获取订单详情"], "fullName": "订单API测试 GET /api/orders/[id] - 获取订单详情 应该拒绝无关用户访问订单", "status": "passed", "title": "应该拒绝无关用户访问订单", "duration": 0.4786669999998594, "failureMessages": [], "location": {"line": 297, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "PATCH /api/orders/[id] - 更新订单状态"], "fullName": "订单API测试 PATCH /api/orders/[id] - 更新订单状态 应该允许买家上传支付凭证", "status": "passed", "title": "应该允许买家上传支付凭证", "duration": 2.2359999999998763, "failureMessages": [], "location": {"line": 323, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "PATCH /api/orders/[id] - 更新订单状态"], "fullName": "订单API测试 PATCH /api/orders/[id] - 更新订单状态 应该允许卖家确认支付", "status": "passed", "title": "应该允许卖家确认支付", "duration": 0.8412079999998241, "failureMessages": [], "location": {"line": 362, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders/[id]/payment - 处理支付"], "fullName": "订单API测试 POST /api/orders/[id]/payment - 处理支付 应该处理币安支付", "status": "passed", "title": "应该处理币安支付", "duration": 1.4203749999996944, "failureMessages": [], "location": {"line": 399, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders/[id]/payment - 处理支付"], "fullName": "订单API测试 POST /api/orders/[id]/payment - 处理支付 应该处理BNB链支付", "status": "passed", "title": "应该处理BNB链支付", "duration": 0.8123329999998532, "failureMessages": [], "location": {"line": 440, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id]/payment - 获取支付信息"], "fullName": "订单API测试 GET /api/orders/[id]/payment - 获取支付信息 应该为买家生成支付二维码", "status": "passed", "title": "应该为买家生成支付二维码", "duration": 0.5309170000000449, "failureMessages": [], "location": {"line": 481, "column": 5}, "meta": {}}], "startTime": 1754035125785, "endTime": 1754035125798.531, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/orders.test.ts"}, {"assertionResults": [{"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该返回已审核通过的商品列表", "status": "passed", "title": "应该返回已审核通过的商品列表", "duration": 5.405749999999898, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持搜索功能", "status": "passed", "title": "应该支持搜索功能", "duration": 1.2387909999997646, "failureMessages": [], "location": {"line": 155, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持分类筛选", "status": "passed", "title": "应该支持分类筛选", "duration": 0.6587919999997212, "failureMessages": [], "location": {"line": 179, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持价格范围筛选", "status": "passed", "title": "应该支持价格范围筛选", "duration": 0.719916000000012, "failureMessages": [], "location": {"line": 203, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该成功创建商品", "status": "passed", "title": "应该成功创建商品", "duration": 0.9335000000000946, "failureMessages": [], "location": {"line": 231, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该拒绝未登录用户创建商品", "status": "passed", "title": "应该拒绝未登录用户创建商品", "duration": 0.6375000000002728, "failureMessages": [], "location": {"line": 292, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 0.41904199999999037, "failureMessages": [], "location": {"line": 309, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products/[id] - 获取商品详情"], "fullName": "商品API测试 GET /api/products/[id] - 获取商品详情 应该返回商品详情", "status": "passed", "title": "应该返回商品详情", "duration": 0.5812919999998485, "failureMessages": [], "location": {"line": 330, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products/[id] - 获取商品详情"], "fullName": "商品API测试 GET /api/products/[id] - 获取商品详情 应该处理商品不存在的情况", "status": "passed", "title": "应该处理商品不存在的情况", "duration": 0.3817079999998896, "failureMessages": [], "location": {"line": 353, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "PATCH /api/products/[id] - 更新商品"], "fullName": "商品API测试 PATCH /api/products/[id] - 更新商品 应该允许卖家更新自己的商品", "status": "passed", "title": "应该允许卖家更新自己的商品", "duration": 0.8193339999997988, "failureMessages": [], "location": {"line": 369, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "PATCH /api/products/[id] - 更新商品"], "fullName": "商品API测试 PATCH /api/products/[id] - 更新商品 应该拒绝非卖家更新商品", "status": "passed", "title": "应该拒绝非卖家更新商品", "duration": 0.6710829999997259, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}], "startTime": 1754035125804, "endTime": 1754035125816.6711, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/products.test.ts"}, {"assertionResults": [{"ancestorTitles": ["简化API测试", "Mock功能测试"], "fullName": "简化API测试 Mock功能测试 应该能够创建模拟请求", "status": "passed", "title": "应该能够创建模拟请求", "duration": 2.7318330000000515, "failureMessages": [], "location": {"line": 17, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "Mock功能测试"], "fullName": "简化API测试 Mock功能测试 应该能够创建模拟会话", "status": "passed", "title": "应该能够创建模拟会话", "duration": 0.30533300000001873, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "Mock功能测试"], "fullName": "简化API测试 Mock功能测试 应该能够模拟数据库操作", "status": "passed", "title": "应该能够模拟数据库操作", "duration": 0.8166660000001684, "failureMessages": [], "location": {"line": 29, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "测试数据验证"], "fullName": "简化API测试 测试数据验证 应该有有效的用户测试数据", "status": "passed", "title": "应该有有效的用户测试数据", "duration": 0.2671250000000782, "failureMessages": [], "location": {"line": 44, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "测试数据验证"], "fullName": "简化API测试 测试数据验证 应该有有效的商品测试数据", "status": "passed", "title": "应该有有效的商品测试数据", "duration": 0.20241599999985738, "failureMessages": [], "location": {"line": 58, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "测试数据验证"], "fullName": "简化API测试 测试数据验证 应该有有效的订单测试数据", "status": "passed", "title": "应该有有效的订单测试数据", "duration": 0.17979200000013407, "failureMessages": [], "location": {"line": 69, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "HTTP响应模拟"], "fullName": "简化API测试 HTTP响应模拟 应该能够模拟成功的API响应", "status": "passed", "title": "应该能够模拟成功的API响应", "duration": 0.19374999999990905, "failureMessages": [], "location": {"line": 81, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "HTTP响应模拟"], "fullName": "简化API测试 HTTP响应模拟 应该能够模拟错误的API响应", "status": "passed", "title": "应该能够模拟错误的API响应", "duration": 0.1484170000001086, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "异步操作测试"], "fullName": "简化API测试 异步操作测试 应该能够处理Promise", "status": "passed", "title": "应该能够处理Promise", "duration": 0.1254590000000917, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "异步操作测试"], "fullName": "简化API测试 异步操作测试 应该能够处理Promise拒绝", "status": "passed", "title": "应该能够处理Promise拒绝", "duration": 0.6042500000000928, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "异步操作测试"], "fullName": "简化API测试 异步操作测试 应该能够测试超时", "status": "passed", "title": "应该能够测试超时", "duration": 100.471, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "数据验证测试"], "fullName": "简化API测试 数据验证测试 应该验证邮箱格式", "status": "passed", "title": "应该验证邮箱格式", "duration": 0.33141699999987395, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "数据验证测试"], "fullName": "简化API测试 数据验证测试 应该验证价格范围", "status": "passed", "title": "应该验证价格范围", "duration": 0.1447920000000522, "failureMessages": [], "location": {"line": 153, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "数据验证测试"], "fullName": "简化API测试 数据验证测试 应该验证字符串长度", "status": "passed", "title": "应该验证字符串长度", "duration": 0.1252919999999449, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}], "startTime": 1754035125038, "endTime": 1754035125145.3313, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/simple-api.test.ts"}, {"assertionResults": [{"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该成功上传商品图片", "status": "passed", "title": "应该成功上传商品图片", "duration": 3.6722500000000764, "failureMessages": [], "location": {"line": 57, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该成功上传用户头像", "status": "passed", "title": "应该成功上传用户头像", "duration": 0.9995839999996861, "failureMessages": [], "location": {"line": 90, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该成功上传支付凭证", "status": "passed", "title": "应该成功上传支付凭证", "duration": 0.8702920000000631, "failureMessages": [], "location": {"line": 122, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝未登录用户上传", "status": "passed", "title": "应该拒绝未登录用户上传", "duration": 1.9843329999998787, "failureMessages": [], "location": {"line": 154, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝无文件上传", "status": "passed", "title": "应该拒绝无文件上传", "duration": 0.9827080000000024, "failureMessages": [], "location": {"line": 176, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝不支持的文件类型", "status": "passed", "title": "应该拒绝不支持的文件类型", "duration": 0.45083299999987503, "failureMessages": [], "location": {"line": 195, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝过大的文件", "status": "passed", "title": "应该拒绝过大的文件", "duration": 0.3478750000003856, "failureMessages": [], "location": {"line": 214, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该处理图片处理错误", "status": "passed", "title": "应该处理图片处理错误", "duration": 1.02350000000024, "failureMessages": [], "location": {"line": 234, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该处理文件系统错误", "status": "passed", "title": "应该处理文件系统错误", "duration": 0.587083999999777, "failureMessages": [], "location": {"line": 262, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该生成唯一的文件名", "status": "passed", "title": "应该生成唯一的文件名", "duration": 0.5847090000002026, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}], "startTime": 1754035126163, "endTime": 1754035126174.5847, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/upload.test.ts"}, {"assertionResults": [{"ancestorTitles": ["管理员功能测试", "管理员权限验证"], "fullName": "管理员功能测试 管理员权限验证 应该验证管理员身份", "status": "passed", "title": "应该验证管理员身份", "duration": 1.1862500000002, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "管理员权限验证"], "fullName": "管理员功能测试 管理员权限验证 应该拒绝非管理员用户访问管理功能", "status": "passed", "title": "应该拒绝非管理员用户访问管理功能", "duration": 0.2515410000000884, "failureMessages": [], "location": {"line": 45, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "商品审核功能"], "fullName": "管理员功能测试 商品审核功能 应该获取待审核商品列表", "status": "passed", "title": "应该获取待审核商品列表", "duration": 0.5075409999999465, "failureMessages": [], "location": {"line": 73, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "商品审核功能"], "fullName": "管理员功能测试 商品审核功能 应该能够审核通过商品", "status": "passed", "title": "应该能够审核通过商品", "duration": 0.19612499999993815, "failureMessages": [], "location": {"line": 102, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "商品审核功能"], "fullName": "管理员功能测试 商品审核功能 应该能够拒绝商品", "status": "passed", "title": "应该能够拒绝商品", "duration": 0.19312499999978172, "failureMessages": [], "location": {"line": 129, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该获取用户列表", "status": "passed", "title": "应该获取用户列表", "duration": 0.2543329999998605, "failureMessages": [], "location": {"line": 164, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该能够封禁用户", "status": "passed", "title": "应该能够封禁用户", "duration": 0.21749999999974534, "failureMessages": [], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该能够解封用户", "status": "passed", "title": "应该能够解封用户", "duration": 0.21574999999984357, "failureMessages": [], "location": {"line": 226, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该能够调整用户信用分", "status": "passed", "title": "应该能够调整用户信用分", "duration": 0.22879199999988487, "failureMessages": [], "location": {"line": 258, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "订单监控功能"], "fullName": "管理员功能测试 订单监控功能 应该获取所有订单列表", "status": "passed", "title": "应该获取所有订单列表", "duration": 0.2833330000003116, "failureMessages": [], "location": {"line": 294, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "订单监控功能"], "fullName": "管理员功能测试 订单监控功能 应该能够强制完成订单", "status": "passed", "title": "应该能够强制完成订单", "duration": 0.22537499999998545, "failureMessages": [], "location": {"line": 318, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "订单监控功能"], "fullName": "管理员功能测试 订单监控功能 应该能够处理退款申请", "status": "passed", "title": "应该能够处理退款申请", "duration": 0.1928749999997308, "failureMessages": [], "location": {"line": 347, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "申诉处理功能"], "fullName": "管理员功能测试 申诉处理功能 应该获取待处理申诉列表", "status": "passed", "title": "应该获取待处理申诉列表", "duration": 1.2480000000000473, "failureMessages": [], "location": {"line": 386, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "申诉处理功能"], "fullName": "管理员功能测试 申诉处理功能 应该能够处理申诉", "status": "passed", "title": "应该能够处理申诉", "duration": 0.47879199999988487, "failureMessages": [], "location": {"line": 430, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "系统配置管理"], "fullName": "管理员功能测试 系统配置管理 应该能够更新系统配置", "status": "passed", "title": "应该能够更新系统配置", "duration": 0.7232909999997901, "failureMessages": [], "location": {"line": 470, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "系统配置管理"], "fullName": "管理员功能测试 系统配置管理 应该能够获取系统统计数据", "status": "passed", "title": "应该能够获取系统统计数据", "duration": 0.2894169999999576, "failureMessages": [], "location": {"line": 498, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "管理员操作日志"], "fullName": "管理员功能测试 管理员操作日志 应该记录管理员操作", "status": "passed", "title": "应该记录管理员操作", "duration": 0.22029199999997218, "failureMessages": [], "location": {"line": 532, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "管理员操作日志"], "fullName": "管理员功能测试 管理员操作日志 应该获取操作日志列表", "status": "passed", "title": "应该获取操作日志列表", "duration": 0.24354099999982282, "failureMessages": [], "location": {"line": 560, "column": 5}, "meta": {}}], "startTime": 1754035126314, "endTime": 1754035126321.2437, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/e2e/admin-functions.test.ts"}, {"assertionResults": [{"ancestorTitles": ["完整业务流程测试", "商品上架到审核流程"], "fullName": "完整业务流程测试 商品上架到审核流程 应该完成商品上架到审核通过的完整流程", "status": "passed", "title": "应该完成商品上架到审核通过的完整流程", "duration": 1.3368749999999636, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "商品上架到审核流程"], "fullName": "完整业务流程测试 商品上架到审核流程 应该处理商品审核拒绝流程", "status": "passed", "title": "应该处理商品审核拒绝流程", "duration": 0.18829199999981938, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "完整交易流程"], "fullName": "完整业务流程测试 完整交易流程 应该完成从下单到交易完成的完整流程", "status": "passed", "title": "应该完成从下单到交易完成的完整流程", "duration": 0.49525000000039654, "failureMessages": [], "location": {"line": 148, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "消息交流流程"], "fullName": "完整业务流程测试 消息交流流程 应该支持买卖双方完整的消息交流", "status": "passed", "title": "应该支持买卖双方完整的消息交流", "duration": 0.47879199999988487, "failureMessages": [], "location": {"line": 324, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "争议处理流程"], "fullName": "完整业务流程测试 争议处理流程 应该完成从申诉到仲裁的完整流程", "status": "passed", "title": "应该完成从申诉到仲裁的完整流程", "duration": 0.40141700000003766, "failureMessages": [], "location": {"line": 444, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "异常情况处理"], "fullName": "完整业务流程测试 异常情况处理 应该处理订单超时自动完成", "status": "passed", "title": "应该处理订单超时自动完成", "duration": 0.16229100000009566, "failureMessages": [], "location": {"line": 579, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "异常情况处理"], "fullName": "完整业务流程测试 异常情况处理 应该处理支付超时取消订单", "status": "passed", "title": "应该处理支付超时取消订单", "duration": 0.14641699999992852, "failureMessages": [], "location": {"line": 606, "column": 5}, "meta": {}}], "startTime": 1754035126777, "endTime": 1754035126781.1465, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/e2e/business-flow.test.ts"}, {"assertionResults": [{"ancestorTitles": ["用户功能端到端测试", "用户注册和登录流程"], "fullName": "用户功能端到端测试 用户注册和登录流程 应该完成完整的注册流程", "status": "passed", "title": "应该完成完整的注册流程", "duration": 1.3008750000003602, "failureMessages": [], "location": {"line": 33, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "用户注册和登录流程"], "fullName": "用户功能端到端测试 用户注册和登录流程 应该完成登录流程", "status": "passed", "title": "应该完成登录流程", "duration": 0.780666999999994, "failureMessages": [], "location": {"line": 75, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "商品浏览和搜索流程"], "fullName": "用户功能端到端测试 商品浏览和搜索流程 应该能够浏览商品列表", "status": "passed", "title": "应该能够浏览商品列表", "duration": 0.3600409999999101, "failureMessages": [], "location": {"line": 104, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "商品浏览和搜索流程"], "fullName": "用户功能端到端测试 商品浏览和搜索流程 应该能够搜索商品", "status": "passed", "title": "应该能够搜索商品", "duration": 0.22808299999996962, "failureMessages": [], "location": {"line": 133, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "商品浏览和搜索流程"], "fullName": "用户功能端到端测试 商品浏览和搜索流程 应该能够查看商品详情", "status": "passed", "title": "应该能够查看商品详情", "duration": 0.20183300000007875, "failureMessages": [], "location": {"line": 160, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "下单和支付流程"], "fullName": "用户功能端到端测试 下单和支付流程 应该能够创建订单", "status": "passed", "title": "应该能够创建订单", "duration": 0.2701670000001286, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "下单和支付流程"], "fullName": "用户功能端到端测试 下单和支付流程 应该能够获取支付信息", "status": "passed", "title": "应该能够获取支付信息", "duration": 0.18320900000026086, "failureMessages": [], "location": {"line": 240, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "下单和支付流程"], "fullName": "用户功能端到端测试 下单和支付流程 应该能够提交支付信息", "status": "passed", "title": "应该能够提交支付信息", "duration": 0.1847090000001117, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "消息交流流程"], "fullName": "用户功能端到端测试 消息交流流程 应该能够发送文本消息", "status": "passed", "title": "应该能够发送文本消息", "duration": 0.20562500000005457, "failureMessages": [], "location": {"line": 309, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "消息交流流程"], "fullName": "用户功能端到端测试 消息交流流程 应该能够获取消息历史", "status": "passed", "title": "应该能够获取消息历史", "duration": 0.25250000000005457, "failureMessages": [], "location": {"line": 344, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "订单状态更新流程"], "fullName": "用户功能端到端测试 订单状态更新流程 应该能够确认收货", "status": "passed", "title": "应该能够确认收货", "duration": 0.19870799999989686, "failureMessages": [], "location": {"line": 399, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "完整用户流程集成测试"], "fullName": "用户功能端到端测试 完整用户流程集成测试 应该完成从注册到交易完成的完整流程", "status": "passed", "title": "应该完成从注册到交易完成的完整流程", "duration": 0.2632919999996375, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}], "startTime": 1754035126381, "endTime": 1754035126385.2632, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/e2e/user-flow.test.ts"}, {"assertionResults": [{"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建有效的用户数据", "status": "passed", "title": "应该创建有效的用户数据", "duration": 2.0012080000001333, "failureMessages": [], "location": {"line": 14, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建买家用户", "status": "passed", "title": "应该创建买家用户", "duration": 0.3388749999999163, "failureMessages": [], "location": {"line": 28, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建卖家用户", "status": "passed", "title": "应该创建卖家用户", "duration": 0.24383399999987887, "failureMessages": [], "location": {"line": 35, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建管理员用户", "status": "passed", "title": "应该创建管理员用户", "duration": 0.20241600000008475, "failureMessages": [], "location": {"line": 43, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该批量创建用户", "status": "passed", "title": "应该批量创建用户", "duration": 0.7447499999998399, "failureMessages": [], "location": {"line": 51, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建有效的商品数据", "status": "passed", "title": "应该创建有效的商品数据", "duration": 0.3189169999996011, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建可用商品", "status": "passed", "title": "应该创建可用商品", "duration": 0.12929099999973914, "failureMessages": [], "location": {"line": 76, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建待审核商品", "status": "passed", "title": "应该创建待审核商品", "duration": 0.10312500000009095, "failureMessages": [], "location": {"line": 84, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建有效的订单数据", "status": "passed", "title": "应该创建有效的订单数据", "duration": 0.39745799999991505, "failureMessages": [], "location": {"line": 92, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建待支付订单", "status": "passed", "title": "应该创建待支付订单", "duration": 0.15249999999969077, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建已支付订单", "status": "passed", "title": "应该创建已支付订单", "duration": 0.12670799999978044, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建已完成订单", "status": "passed", "title": "应该创建已完成订单", "duration": 0.1360829999998714, "failureMessages": [], "location": {"line": 124, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建有效的消息数据", "status": "passed", "title": "应该创建有效的消息数据", "duration": 0.2667500000002292, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建文本消息", "status": "passed", "title": "应该创建文本消息", "duration": 0.09937500000023647, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建图片消息", "status": "passed", "title": "应该创建图片消息", "duration": 0.11475000000018554, "failureMessages": [], "location": {"line": 155, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建有效的评价数据", "status": "passed", "title": "应该创建有效的评价数据", "duration": 0.13866700000016863, "failureMessages": [], "location": {"line": 170, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建正面评价", "status": "passed", "title": "应该创建正面评价", "duration": 0.07262500000024374, "failureMessages": [], "location": {"line": 180, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建负面评价", "status": "passed", "title": "应该创建负面评价", "duration": 0.0658330000001115, "failureMessages": [], "location": {"line": 187, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建有效的反馈数据", "status": "passed", "title": "应该创建有效的反馈数据", "duration": 0.3927500000004329, "failureMessages": [], "location": {"line": 196, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建申诉反馈", "status": "passed", "title": "应该创建申诉反馈", "duration": 0.24195899999995163, "failureMessages": [], "location": {"line": 208, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建Bug报告", "status": "passed", "title": "应该创建Bug报告", "duration": 0.1588329999999587, "failureMessages": [], "location": {"line": 215, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "DataGenerator"], "fullName": "数据工厂测试 DataGenerator 应该生成完整的用户场景", "status": "passed", "title": "应该生成完整的用户场景", "duration": 1.0539590000003045, "failureMessages": [], "location": {"line": 224, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "DataGenerator"], "fullName": "数据工厂测试 DataGenerator 应该生成市场数据", "status": "passed", "title": "应该生成市场数据", "duration": 1.3866250000000946, "failureMessages": [], "location": {"line": 236, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成唯一的ID", "status": "passed", "title": "应该生成唯一的ID", "duration": 0.3398329999999987, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成有效的邮箱地址", "status": "passed", "title": "应该生成有效的邮箱地址", "duration": 0.2399159999999938, "failureMessages": [], "location": {"line": 254, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成合理的价格范围", "status": "passed", "title": "应该生成合理的价格范围", "duration": 0.3569999999999709, "failureMessages": [], "location": {"line": 263, "column": 5}, "meta": {}}], "startTime": 1754035126002, "endTime": 1754035126012.357, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/factories/factory.test.ts"}, {"assertionResults": [{"ancestorTitles": ["性能和并发测试", "数据库并发访问测试"], "fullName": "性能和并发测试 数据库并发访问测试 应该处理大量并发商品查询", "status": "passed", "title": "应该处理大量并发商品查询", "duration": 2.7408329999999523, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "数据库并发访问测试"], "fullName": "性能和并发测试 数据库并发访问测试 应该处理并发订单创建", "status": "passed", "title": "应该处理并发订单创建", "duration": 0.5582910000000538, "failureMessages": [], "location": {"line": 68, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "数据库并发访问测试"], "fullName": "性能和并发测试 数据库并发访问测试 应该处理并发消息发送", "status": "passed", "title": "应该处理并发消息发送", "duration": 0.34704099999999016, "failureMessages": [], "location": {"line": 131, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "缓存性能测试"], "fullName": "性能和并发测试 缓存性能测试 应该测试缓存命中率", "status": "passed", "title": "应该测试缓存命中率", "duration": 0.6396250000000236, "failureMessages": [], "location": {"line": 183, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "缓存性能测试"], "fullName": "性能和并发测试 缓存性能测试 应该测试缓存失效和更新", "status": "passed", "title": "应该测试缓存失效和更新", "duration": 0.29920800000002146, "failureMessages": [], "location": {"line": 236, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "WebSocket连接测试"], "fullName": "性能和并发测试 WebSocket连接测试 应该处理大量并发WebSocket连接", "status": "passed", "title": "应该处理大量并发WebSocket连接", "duration": 96.71249999999998, "failureMessages": [], "location": {"line": 289, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "WebSocket连接测试"], "fullName": "性能和并发测试 WebSocket连接测试 应该测试WebSocket消息广播性能", "status": "passed", "title": "应该测试WebSocket消息广播性能", "duration": 0.754833000000076, "failureMessages": [], "location": {"line": 336, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "内存和资源使用测试"], "fullName": "性能和并发测试 内存和资源使用测试 应该测试大量数据处理的内存使用", "status": "passed", "title": "应该测试大量数据处理的内存使用", "duration": 1.038041000000021, "failureMessages": [], "location": {"line": 380, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "内存和资源使用测试"], "fullName": "性能和并发测试 内存和资源使用测试 应该测试文件上传的并发处理", "status": "passed", "title": "应该测试文件上传的并发处理", "duration": 4.267208000000096, "failureMessages": [], "location": {"line": 433, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "压力测试"], "fullName": "性能和并发测试 压力测试 应该进行系统整体压力测试", "status": "passed", "title": "应该进行系统整体压力测试", "duration": 5047.852791, "failureMessages": [], "location": {"line": 482, "column": 5}, "meta": {}}], "startTime": 1754035124316, "endTime": 1754035129470.8528, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/concurrent.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🧠 内存优化测试套件", "📊 内存优化数据工厂测试"], "fullName": "🧠 内存优化测试套件 📊 内存优化数据工厂测试 应该显著减少内存使用", "status": "passed", "title": "应该显著减少内存使用", "duration": 5.660332999999923, "failureMessages": [], "location": {"line": 60, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "📊 内存优化数据工厂测试"], "fullName": "🧠 内存优化测试套件 📊 内存优化数据工厂测试 应该有效使用对象池", "status": "passed", "title": "应该有效使用对象池", "duration": 1.440584000000058, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🌊 流式数据生成测试"], "fullName": "🧠 内存优化测试套件 🌊 流式数据生成测试 应该支持大规模流式数据生成", "status": "passed", "title": "应该支持大规模流式数据生成", "duration": 5.429957999999942, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🌊 流式数据生成测试"], "fullName": "🧠 内存优化测试套件 🌊 流式数据生成测试 应该支持异步批量处理", "status": "passed", "title": "应该支持异步批量处理", "duration": 24.87854200000004, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🔍 内存监控数据生成测试"], "fullName": "🧠 内存优化测试套件 🔍 内存监控数据生成测试 应该在内存监控下安全生成数据", "status": "passed", "title": "应该在内存监控下安全生成数据", "duration": 624.6650000000001, "failureMessages": [], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "📈 内存性能对比测试"], "fullName": "🧠 内存优化测试套件 📈 内存性能对比测试 应该比标准工厂更节省内存", "status": "passed", "title": "应该比标准工厂更节省内存", "duration": 3.2209170000000995, "failureMessages": [], "location": {"line": 250, "column": 5}, "meta": {}}], "startTime": 1754035124281, "endTime": 1754035124946.221, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/memory-optimization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该显著提升大批量用户数据生成速度", "status": "passed", "title": "应该显著提升大批量用户数据生成速度", "duration": 33.83137499999998, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该优化商品数据生成内存使用", "status": "passed", "title": "应该优化商品数据生成内存使用", "duration": 5.05174999999997, "failureMessages": [], "location": {"line": 36, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该支持高效的批量数据生成", "status": "passed", "title": "应该支持高效的批量数据生成", "duration": 9.13066600000002, "failureMessages": [], "location": {"line": 48, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "⚡ 并行处理性能测试"], "fullName": "🚀 优化后的性能测试套件 ⚡ 并行处理性能测试 应该支持高效的并行任务执行", "status": "passed", "title": "应该支持高效的并行任务执行", "duration": 6.186124999999947, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "⚡ 并行处理性能测试"], "fullName": "🚀 优化后的性能测试套件 ⚡ 并行处理性能测试 应该优化API模拟的并发性能", "status": "passed", "title": "应该优化API模拟的并发性能", "duration": 7.685165999999981, "failureMessages": [], "location": {"line": 89, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🗄️ 数据库查询优化测试"], "fullName": "🚀 优化后的性能测试套件 🗄️ 数据库查询优化测试 应该优化商品搜索查询性能", "status": "passed", "title": "应该优化商品搜索查询性能", "duration": 111.86054200000001, "failureMessages": [], "location": {"line": 111, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🗄️ 数据库查询优化测试"], "fullName": "🚀 优化后的性能测试套件 🗄️ 数据库查询优化测试 应该提供查询性能分析和优化建议", "status": "passed", "title": "应该提供查询性能分析和优化建议", "duration": 118.44583299999988, "failureMessages": [], "location": {"line": 128, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "💾 缓存系统性能测试"], "fullName": "🚀 优化后的性能测试套件 💾 缓存系统性能测试 应该实现高性能LRU缓存", "status": "passed", "title": "应该实现高性能LRU缓存", "duration": 38.935249999999996, "failureMessages": [], "location": {"line": 144, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "💾 缓存系统性能测试"], "fullName": "🚀 优化后的性能测试套件 💾 缓存系统性能测试 应该实现高效的多级缓存", "status": "passed", "title": "应该实现高效的多级缓存", "duration": 15.506624999999985, "failureMessages": [], "location": {"line": 168, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🎯 综合性能基准测试"], "fullName": "🚀 优化后的性能测试套件 🎯 综合性能基准测试 应该达到系统性能目标", "status": "passed", "title": "应该达到系统性能目标", "duration": 20.520375000000058, "failureMessages": [], "location": {"line": 203, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🎯 综合性能基准测试"], "fullName": "🚀 优化后的性能测试套件 🎯 综合性能基准测试 应该检测性能回归", "status": "passed", "title": "应该检测性能回归", "duration": 3.146582999999964, "failureMessages": [], "location": {"line": 252, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📈 性能监控和报告"], "fullName": "🚀 优化后的性能测试套件 📈 性能监控和报告 应该生成详细的性能报告", "status": "passed", "title": "应该生成详细的性能报告", "duration": 2.5711670000000595, "failureMessages": [], "location": {"line": 272, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📈 性能监控和报告"], "fullName": "🚀 优化后的性能测试套件 📈 性能监控和报告 应该保存性能基线数据", "status": "passed", "title": "应该保存性能基线数据", "duration": 1.2397080000000642, "failureMessages": [], "location": {"line": 295, "column": 5}, "meta": {}}], "startTime": 1754035124402, "endTime": 1754035124776.2397, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/optimized-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量用户数据", "status": "passed", "title": "应该快速生成大量用户数据", "duration": 36.30424999999991, "failureMessages": [], "location": {"line": 6, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量商品数据", "status": "passed", "title": "应该快速生成大量商品数据", "duration": 9.833540999999968, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量订单数据", "status": "passed", "title": "应该快速生成大量订单数据", "duration": 5.991750000000025, "failureMessages": [], "location": {"line": 34, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "并发操作模拟"], "fullName": "简单性能测试 并发操作模拟 应该处理并发数据库查询模拟", "status": "passed", "title": "应该处理并发数据库查询模拟", "duration": 11.995416000000091, "failureMessages": [], "location": {"line": 50, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "并发操作模拟"], "fullName": "简单性能测试 并发操作模拟 应该处理批量API请求模拟", "status": "passed", "title": "应该处理批量API请求模拟", "duration": 0.3869999999999436, "failureMessages": [], "location": {"line": 71, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "内存使用测试"], "fullName": "简单性能测试 内存使用测试 应该有效管理大量数据的内存使用", "status": "passed", "title": "应该有效管理大量数据的内存使用", "duration": 45.036749999999984, "failureMessages": [], "location": {"line": 94, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速搜索大量数据", "status": "passed", "title": "应该快速搜索大量数据", "duration": 127.03650000000005, "failureMessages": [], "location": {"line": 122, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速排序大量数据", "status": "passed", "title": "应该快速排序大量数据", "duration": 76.49820799999998, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速分页处理大量数据", "status": "passed", "title": "应该快速分页处理大量数据", "duration": 108.12016700000004, "failureMessages": [], "location": {"line": 165, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "缓存性能模拟"], "fullName": "简单性能测试 缓存性能模拟 应该模拟缓存命中性能", "status": "passed", "title": "应该模拟缓存命中性能", "duration": 0.18616599999995742, "failureMessages": [], "location": {"line": 188, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "缓存性能模拟"], "fullName": "简单性能测试 缓存性能模拟 应该模拟缓存未命中性能", "status": "passed", "title": "应该模拟缓存未命中性能", "duration": 0.39887500000008913, "failureMessages": [], "location": {"line": 211, "column": 5}, "meta": {}}], "startTime": 1754035124359, "endTime": 1754035124781.399, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/simple-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📊 性能基准对比测试"], "fullName": "🚀 用户数据生成性能优化测试 📊 性能基准对比测试 应该显著提升10,000用户生成性能", "status": "passed", "title": "应该显著提升10,000用户生成性能", "duration": 34.85245800000007, "failureMessages": [], "location": {"line": 14, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📊 性能基准对比测试"], "fullName": "🚀 用户数据生成性能优化测试 📊 性能基准对比测试 应该在并行模式下实现更高性能", "status": "passed", "title": "应该在并行模式下实现更高性能", "duration": 58.47691699999996, "failureMessages": [], "location": {"line": 98, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🔍 内存使用优化测试"], "fullName": "🚀 用户数据生成性能优化测试 🔍 内存使用优化测试 应该优化大批量生成的内存使用", "status": "passed", "title": "应该优化大批量生成的内存使用", "duration": 10.123874999999998, "failureMessages": [], "location": {"line": 134, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🔍 内存使用优化测试"], "fullName": "🚀 用户数据生成性能优化测试 🔍 内存使用优化测试 应该验证内存池的效果", "status": "passed", "title": "应该验证内存池的效果", "duration": 4.491792000000032, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🎯 数据质量验证"], "fullName": "🚀 用户数据生成性能优化测试 🎯 数据质量验证 应该生成高质量的用户数据", "status": "passed", "title": "应该生成高质量的用户数据", "duration": 32.69233299999996, "failureMessages": [], "location": {"line": 190, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🎯 数据质量验证"], "fullName": "🚀 用户数据生成性能优化测试 🎯 数据质量验证 应该支持专门的买家和卖家生成", "status": "passed", "title": "应该支持专门的买家和卖家生成", "duration": 2.002166999999872, "failureMessages": [], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📈 极限性能测试"], "fullName": "🚀 用户数据生成性能优化测试 📈 极限性能测试 应该在极限条件下保持高性能", "status": "passed", "title": "应该在极限条件下保持高性能", "duration": 151.15479200000004, "failureMessages": [], "location": {"line": 244, "column": 5}, "meta": {}}], "startTime": 1754035125096, "endTime": 1754035125390.1548, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/user-generation-optimization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该成功批量生成礼品卡", "status": "passed", "title": "应该成功批量生成礼品卡", "duration": 3.126709000000119, "failureMessages": [], "location": {"line": 57, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该拒绝非管理员用户批量生成礼品卡", "status": "passed", "title": "应该拒绝非管理员用户批量生成礼品卡", "duration": 0.4234169999999722, "failureMessages": [], "location": {"line": 99, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该验证批量生成参数", "status": "passed", "title": "应该验证批量生成参数", "duration": 0.3804169999998521, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该获取批次列表", "status": "passed", "title": "应该获取批次列表", "duration": 0.9889589999997952, "failureMessages": [], "location": {"line": 139, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡统计API"], "fullName": "增强礼品卡管理API测试 礼品卡统计API 应该获取礼品卡统计数据", "status": "passed", "title": "应该获取礼品卡统计数据", "duration": 0.7570000000000618, "failureMessages": [], "location": {"line": 178, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡统计API"], "fullName": "增强礼品卡管理API测试 礼品卡统计API 应该拒绝非管理员获取统计数据", "status": "passed", "title": "应该拒绝非管理员获取统计数据", "duration": 0.28074999999989814, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该成功创建兑换券", "status": "passed", "title": "应该成功创建兑换券", "duration": 0.445165999999972, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该获取兑换券列表", "status": "passed", "title": "应该获取兑换券列表", "duration": 0.3789160000001175, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该更新兑换券状态", "status": "passed", "title": "应该更新兑换券状态", "duration": 0.40404099999977916, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该验证兑换券创建参数", "status": "passed", "title": "应该验证兑换券创建参数", "duration": 0.2768750000000182, "failureMessages": [], "location": {"line": 318, "column": 5}, "meta": {}}], "startTime": 1754035126100, "endTime": 1754035126108.2769, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/admin/giftcards-enhanced.test.ts"}]}