{"timestamp": "2025-08-01T07:58:44.776Z", "summary": {"totalBenchmarks": 1, "totalMetrics": 2}, "benchmarks": {"基线测试_综合": {"testName": "基线测试_综合", "metrics": [{"name": "基线测试_综合_iteration_1", "duration": 0.1778750000000855, "memoryUsage": {"before": {"rss": 841302016, "heapTotal": 201965568, "heapUsed": 76532776, "external": 5550688, "arrayBuffers": 607069}, "after": {"rss": 841318400, "heapTotal": 201965568, "heapUsed": 76898872, "external": 5550688, "arrayBuffers": 607069}, "delta": 366096}, "timestamp": 1754035124776, "metadata": {"iteration": 1}}, {"name": "基线测试_综合_iteration_2", "duration": 0.1610000000000582, "memoryUsage": {"before": {"rss": 841318400, "heapTotal": 201965568, "heapUsed": 76902296, "external": 5550688, "arrayBuffers": 607069}, "after": {"rss": 841318400, "heapTotal": 201965568, "heapUsed": 77265784, "external": 5550688, "arrayBuffers": 607069}, "delta": 363488}, "timestamp": 1754035124776, "metadata": {"iteration": 2}}], "summary": {"avgDuration": 0.16943750000007185, "minDuration": 0.1610000000000582, "maxDuration": 0.1778750000000855, "totalMemoryDelta": 729584, "iterations": 2}}}, "regressions": []}