import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    // 确保会话 cookie 的正确设置
    const response = NextResponse.next()
    
    // 设置安全头部
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
    
    // 确保会话 cookie 的持久性
    if (req.nextauth.token) {
      // 如果有有效的 token，确保 cookie 设置正确
      const cookieOptions = [
        'HttpOnly',
        'SameSite=Lax',
        'Path=/',
        `Max-Age=${30 * 24 * 60 * 60}`, // 30 days
        process.env.NODE_ENV === 'production' ? 'Secure' : ''
      ].filter(Boolean).join('; ')
      
      response.headers.set('Set-Cookie', `next-auth.session-token=${req.nextauth.token}; ${cookieOptions}`)
    }
    
    return response
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // 对于 API 路由，检查是否需要认证
        if (req.nextUrl.pathname.startsWith('/api/')) {
          // 某些 API 路由需要认证
          const protectedApiRoutes = [
            '/api/user',
            '/api/orders',
            '/api/products/create',
            '/api/mediator',
            '/api/escrow'
          ]
          
          const isProtectedRoute = protectedApiRoutes.some(route => 
            req.nextUrl.pathname.startsWith(route)
          )
          
          if (isProtectedRoute) {
            return !!token
          }
        }
        
        // 对于页面路由，某些页面需要认证
        const protectedPageRoutes = [
          '/profile',
          '/orders',
          '/products/create',
          '/admin'
        ]
        
        const isProtectedPage = protectedPageRoutes.some(route => 
          req.nextUrl.pathname.startsWith(route)
        )
        
        if (isProtectedPage) {
          return !!token
        }
        
        // 其他路由允许访问
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public|logo.jpg).*)',
  ],
}
