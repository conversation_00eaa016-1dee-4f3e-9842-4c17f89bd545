'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { HeartIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

interface FavoriteButtonProps {
  productId: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
}

export default function FavoriteButton({ 
  productId, 
  className = '', 
  size = 'md',
  showText = false 
}: FavoriteButtonProps) {
  const { data: session } = useSession()
  const [isFavorited, setIsFavorited] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // 图标大小映射
  const sizeMap = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  // 检查收藏状态
  useEffect(() => {
    if (session?.user?.id) {
      checkFavoriteStatus()
    }
  }, [session, productId])

  const checkFavoriteStatus = async () => {
    try {
      const response = await fetch('/api/user/favorites/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productIds: [productId]
        })
      })

      if (response.ok) {
        const data = await response.json()
        setIsFavorited(data.favoriteProductIds.includes(productId))
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  }

  const toggleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!session?.user?.id) {
      alert('请先登录')
      return
    }

    if (isLoading) return

    setIsLoading(true)

    try {
      if (isFavorited) {
        // 取消收藏
        const response = await fetch(`/api/user/favorites/${productId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setIsFavorited(false)
        } else {
          const data = await response.json()
          alert(data.error || '取消收藏失败')
        }
      } else {
        // 添加收藏
        const response = await fetch('/api/user/favorites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            productId
          })
        })

        if (response.ok) {
          setIsFavorited(true)
        } else {
          const data = await response.json()
          alert(data.error || '收藏失败')
        }
      }
    } catch (error) {
      console.error('操作失败:', error)
      alert('操作失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (!session?.user?.id) {
    return null
  }

  return (
    <button
      onClick={toggleFavorite}
      disabled={isLoading}
      className={`
        inline-flex items-center justify-center
        transition-all duration-200
        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}
        ${className}
      `}
      title={isFavorited ? '取消收藏' : '收藏商品'}
    >
      {isFavorited ? (
        <HeartSolidIcon className={`${sizeMap[size]} text-red-500`} />
      ) : (
        <HeartIcon className={`${sizeMap[size]} text-gray-400 hover:text-red-500`} />
      )}
      {showText && (
        <span className="ml-1 text-sm">
          {isFavorited ? '已收藏' : '收藏'}
        </span>
      )}
    </button>
  )
}
