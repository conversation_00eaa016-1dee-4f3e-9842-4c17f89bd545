'use client'

import { useSession } from 'next-auth/react'

export default function SessionDebug() {
  const { data: session, status } = useSession()

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-75 text-white p-4 rounded-lg text-xs max-w-sm">
      <h3 className="font-bold mb-2">会话调试信息</h3>
      <div>
        <strong>状态:</strong> {status}
      </div>
      <div>
        <strong>会话:</strong> {session ? '已登录' : '未登录'}
      </div>
      {session && (
        <div className="mt-2">
          <div><strong>用户ID:</strong> {session.user?.id}</div>
          <div><strong>自定义ID:</strong> {session.user?.userId}</div>
          <div><strong>邮箱:</strong> {session.user?.email}</div>
          <div><strong>姓名:</strong> {session.user?.name}</div>
          <div><strong>角色:</strong> {session.user?.role}</div>
        </div>
      )}
    </div>
  )
}
