'use client'

import React, { useState, useEffect } from 'react'
import { WalletIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { BNB_CHAIN_CONFIG, generateVerificationMessage, isValidAddress } from '@/lib/blockchain'

interface WalletConnectorProps {
  onWalletConnected: (address: string, signature: string) => void
  onWalletDisconnected: () => void
  className?: string
}

declare global {
  interface Window {
    ethereum?: any
  }
}

export default function WalletConnector({
  onWalletConnected,
  onWalletDisconnected,
  className = ''
}: WalletConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false)
  const [connectedAddress, setConnectedAddress] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)

  // 检查是否已连接钱包
  useEffect(() => {
    checkConnection()
  }, [])

  const checkConnection = async () => {
    if (typeof window.ethereum !== 'undefined') {
      try {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' })
        if (accounts.length > 0) {
          setConnectedAddress(accounts[0])
        }
      } catch (error) {
        console.error('检查钱包连接失败:', error)
      }
    }
  }

  const connectWallet = async () => {
    if (typeof window.ethereum === 'undefined') {
      setError('请安装 MetaMask 或其他支持的钱包')
      return
    }

    setIsConnecting(true)
    setError(null)

    try {
      // 请求连接钱包
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length === 0) {
        throw new Error('未选择钱包账户')
      }

      const address = accounts[0]

      // 验证地址格式
      if (!isValidAddress(address)) {
        throw new Error('钱包地址格式不正确')
      }

      // 检查并切换到 BNB Chain
      await switchToBNBChain()

      setConnectedAddress(address)

      // 请求签名验证
      await verifyWalletOwnership(address)

    } catch (error: any) {
      console.error('连接钱包失败:', error)
      setError(error.message || '连接钱包失败')
      setConnectedAddress(null)
    } finally {
      setIsConnecting(false)
    }
  }

  const switchToBNBChain = async () => {
    try {
      // 尝试切换到 BNB Chain
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: BNB_CHAIN_CONFIG.chainId }],
      })
    } catch (switchError: any) {
      // 如果链不存在，添加链
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [BNB_CHAIN_CONFIG],
          })
        } catch (addError) {
          throw new Error('添加 BNB Chain 失败')
        }
      } else {
        throw new Error('切换到 BNB Chain 失败')
      }
    }
  }

  const verifyWalletOwnership = async (address: string) => {
    setIsVerifying(true)
    
    try {
      const timestamp = Date.now()
      const message = generateVerificationMessage(address, timestamp)

      // 请求用户签名
      const signature = await window.ethereum.request({
        method: 'personal_sign',
        params: [message, address],
      })

      // 通知父组件钱包已连接并验证
      onWalletConnected(address, signature)

    } catch (error: any) {
      console.error('钱包验证失败:', error)
      if (error.code === 4001) {
        setError('用户拒绝签名验证')
      } else {
        setError('钱包验证失败')
      }
      setConnectedAddress(null)
    } finally {
      setIsVerifying(false)
    }
  }

  const disconnectWallet = () => {
    setConnectedAddress(null)
    setError(null)
    onWalletDisconnected()
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <WalletIcon className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">钱包连接</h3>
        </div>
        
        {connectedAddress && (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircleIcon className="h-5 w-5" />
            <span className="text-sm font-medium">已连接</span>
          </div>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {!connectedAddress ? (
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            连接您的 BNB Chain 钱包以验证身份并使用托管服务
          </p>
          
          <button
            onClick={connectWallet}
            disabled={isConnecting}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-3 rounded-md font-medium transition-colors flex items-center justify-center space-x-2"
          >
            {isConnecting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>连接中...</span>
              </>
            ) : (
              <>
                <WalletIcon className="h-5 w-5" />
                <span>连接钱包</span>
              </>
            )}
          </button>

          <div className="text-xs text-gray-500 space-y-1">
            <p>• 支持 MetaMask、Trust Wallet 等主流钱包</p>
            <p>• 需要切换到 BNB Smart Chain 网络</p>
            <p>• 需要签名验证钱包所有权</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-800">钱包地址</p>
                <p className="text-sm text-green-700 font-mono">
                  {formatAddress(connectedAddress)}
                </p>
              </div>
              <CheckCircleIcon className="h-8 w-8 text-green-500" />
            </div>
          </div>

          {isVerifying && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                <span className="text-sm text-yellow-700">正在验证钱包所有权...</span>
              </div>
            </div>
          )}

          <button
            onClick={disconnectWallet}
            className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            断开连接
          </button>
        </div>
      )}
    </div>
  )
}
