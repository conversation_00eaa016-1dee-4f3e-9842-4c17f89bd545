'use client'

import React, { useState, useMemo, useEffect } from 'react'
import { ChevronDownIcon, MagnifyingGlassIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { 
  categoryOptions, 
  getCategoryOptions,
  getSubcategoryOptions,
  ProductCategory,
  ProductSubcategory,
  type CategoryOption,
  type SubcategoryOption
} from '@/lib/product-constants-enhanced'

interface EnhancedCategorySelectorProps {
  selectedCategory?: string
  selectedSubcategory?: string
  onCategoryChange: (category: string, subcategory?: string) => void
  placeholder?: string
  required?: boolean
  disabled?: boolean
  className?: string
  showSearch?: boolean
  includeAll?: boolean
  layout?: 'dropdown' | 'grid' | 'list'
  showSubcategories?: boolean
}

export default function EnhancedCategorySelector({
  selectedCategory = '',
  selectedSubcategory = '',
  onCategoryChange,
  placeholder = '选择商品分类',
  required = false,
  disabled = false,
  className = '',
  showSearch = true,
  includeAll = false,
  layout = 'dropdown',
  showSubcategories = true
}: EnhancedCategorySelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showDropdown, setShowDropdown] = useState(false)
  const [showSubcategoryDropdown, setShowSubcategoryDropdown] = useState(false)
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null)

  // 获取分类选项
  const options = useMemo(() => {
    const allOptions = getCategoryOptions(includeAll)
    
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      return allOptions.filter(option => 
        option.label.toLowerCase().includes(query) ||
        option.description.toLowerCase().includes(query) ||
        option.keywords.some(keyword => keyword.includes(query)) ||
        option.subcategories?.some(sub => 
          sub.label.toLowerCase().includes(query) ||
          sub.keywords.some(keyword => keyword.includes(query))
        )
      )
    }
    
    return allOptions
  }, [searchQuery, includeAll])

  // 获取细分类别选项
  const subcategoryOptions = useMemo(() => {
    return getSubcategoryOptions(selectedCategory as ProductCategory)
  }, [selectedCategory])

  // 处理主分类选择
  const handleCategorySelect = (option: CategoryOption) => {
    if (showSubcategories && option.subcategories.length > 0) {
      // 如果有细分类别，先选择主分类但不关闭下拉
      onCategoryChange(option.value, '')
      setExpandedCategory(option.value)
    } else {
      // 没有细分类别，直接选择
      onCategoryChange(option.value, '')
      setSearchQuery('')
      setShowDropdown(false)
      setExpandedCategory(null)
    }
  }

  // 处理细分类别选择
  const handleSubcategorySelect = (subcategory: SubcategoryOption) => {
    onCategoryChange(selectedCategory, subcategory.value)
    setSearchQuery('')
    setShowDropdown(false)
    setShowSubcategoryDropdown(false)
    setExpandedCategory(null)
  }

  // 获取选中分类的信息
  const selectedCategoryOption = options.find(opt => opt.value === selectedCategory)
  const selectedSubcategoryOption = subcategoryOptions.find(opt => opt.value === selectedSubcategory)

  // 获取显示文本
  const getDisplayText = () => {
    if (selectedSubcategoryOption) {
      return `${selectedCategoryOption?.label} > ${selectedSubcategoryOption.label}`
    }
    if (selectedCategoryOption) {
      return selectedCategoryOption.label
    }
    if (searchQuery) return searchQuery
    return placeholder
  }

  // 下拉选择器布局
  if (layout === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          商品分类 {required && <span className="text-red-500">*</span>}
        </label>
        
        <div className="relative">
          {showSearch ? (
            <div className="relative">
              <input
                type="text"
                value={searchQuery || getDisplayText()}
                onChange={(e) => {
                  setSearchQuery(e.target.value)
                  setShowDropdown(true)
                }}
                onFocus={() => setShowDropdown(true)}
                placeholder={placeholder}
                disabled={disabled}
                className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          ) : (
            <button
              type="button"
              onClick={() => setShowDropdown(!showDropdown)}
              disabled={disabled}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
            >
              <div className="flex items-center space-x-2">
                {selectedCategoryOption && (
                  <span className="text-lg">{selectedCategoryOption.icon}</span>
                )}
                <span className={selectedCategoryOption ? 'text-gray-900' : 'text-gray-500'}>
                  {getDisplayText()}
                </span>
              </div>
              <ChevronDownIcon className="h-4 w-4 text-gray-400" />
            </button>
          )}

          {/* 主分类下拉列表 */}
          {showDropdown && !disabled && (
            <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-auto">
              {options.length > 0 ? (
                <div className="py-1">
                  {options.map((option) => (
                    <div key={option.value}>
                      <button
                        type="button"
                        onClick={() => handleCategorySelect(option)}
                        className={`w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center justify-between ${
                          selectedCategory === option.value ? 'bg-blue-50 text-blue-700' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{option.icon}</span>
                          <div className="flex-1">
                            <div className="text-sm font-medium">{option.label}</div>
                            <div className="text-xs text-gray-500">{option.description}</div>
                          </div>
                        </div>
                        {showSubcategories && option.subcategories.length > 0 && (
                          <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                      
                      {/* 细分类别列表 */}
                      {showSubcategories && expandedCategory === option.value && option.subcategories.length > 0 && (
                        <div className="bg-gray-50 border-t">
                          {option.subcategories.map((subcategory) => (
                            <button
                              key={subcategory.value}
                              type="button"
                              onClick={() => handleSubcategorySelect(subcategory)}
                              className={`w-full px-6 py-2 text-left hover:bg-gray-100 flex items-center space-x-3 ${
                                selectedSubcategory === subcategory.value ? 'bg-blue-50 text-blue-700' : ''
                              }`}
                            >
                              <span className="text-sm">{subcategory.icon}</span>
                              <div className="flex-1">
                                <div className="text-sm">{subcategory.label}</div>
                                <div className="text-xs text-gray-500">{subcategory.description}</div>
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  未找到匹配的分类
                </div>
              )}
            </div>
          )}
        </div>

        {/* 细分类别选择器 */}
        {showSubcategories && selectedCategory && subcategoryOptions.length > 0 && !expandedCategory && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              细分类别
            </label>
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowSubcategoryDropdown(!showSubcategoryDropdown)}
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
              >
                <div className="flex items-center space-x-2">
                  {selectedSubcategoryOption && (
                    <span className="text-lg">{selectedSubcategoryOption.icon}</span>
                  )}
                  <span className={selectedSubcategoryOption ? 'text-gray-900' : 'text-gray-500'}>
                    {selectedSubcategoryOption?.label || '选择细分类别'}
                  </span>
                </div>
                <ChevronDownIcon className="h-4 w-4 text-gray-400" />
              </button>

              {showSubcategoryDropdown && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                  <div className="py-1">
                    {subcategoryOptions.map((option) => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => handleSubcategorySelect(option)}
                        className={`w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center space-x-3 ${
                          selectedSubcategory === option.value ? 'bg-blue-50 text-blue-700' : ''
                        }`}
                      >
                        <span className="text-lg">{option.icon}</span>
                        <div className="flex-1">
                          <div className="text-sm font-medium">{option.label}</div>
                          <div className="text-xs text-gray-500">{option.description}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 点击外部关闭下拉列表 */}
        {(showDropdown || showSubcategoryDropdown) && (
          <div
            className="fixed inset-0 z-0"
            onClick={() => {
              setShowDropdown(false)
              setShowSubcategoryDropdown(false)
              setExpandedCategory(null)
            }}
          />
        )}
      </div>
    )
  }

  // 网格布局
  if (layout === 'grid') {
    return (
      <div className={className}>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          商品分类 {required && <span className="text-red-500">*</span>}
        </label>
        
        {showSearch && (
          <div className="relative mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索分类..."
              disabled={disabled}
              className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
            />
            <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          </div>
        )}

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleCategorySelect(option)}
              disabled={disabled}
              className={`p-3 border rounded-lg text-center transition-all hover:shadow-md disabled:opacity-50 ${
                selectedCategory === option.value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-1">{option.icon}</div>
              <div className="text-sm font-medium">{option.label}</div>
              <div className="text-xs text-gray-500 mt-1">{option.description}</div>
            </button>
          ))}
        </div>

        {/* 细分类别网格 */}
        {showSubcategories && selectedCategory && subcategoryOptions.length > 0 && (
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              细分类别
            </label>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
              {subcategoryOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => handleSubcategorySelect(option)}
                  disabled={disabled}
                  className={`p-3 border rounded-lg text-center transition-all hover:shadow-md disabled:opacity-50 ${
                    selectedSubcategory === option.value
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-xl mb-1">{option.icon}</div>
                  <div className="text-sm font-medium">{option.label}</div>
                  <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 列表布局
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-3">
        商品分类 {required && <span className="text-red-500">*</span>}
      </label>
      
      {showSearch && (
        <div className="relative mb-4">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索分类..."
            disabled={disabled}
            className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
          />
          <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
        </div>
      )}

      <div className="space-y-2">
        {options.map((option) => (
          <div key={option.value}>
            <button
              type="button"
              onClick={() => handleCategorySelect(option)}
              disabled={disabled}
              className={`w-full p-3 border rounded-lg text-left transition-all hover:shadow-sm disabled:opacity-50 flex items-center justify-between ${
                selectedCategory === option.value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{option.icon}</span>
                <div className="flex-1">
                  <div className="font-medium">{option.label}</div>
                  <div className="text-sm text-gray-500">{option.description}</div>
                </div>
              </div>
              {showSubcategories && option.subcategories.length > 0 && (
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
              )}
            </button>
            
            {/* 细分类别列表 */}
            {showSubcategories && selectedCategory === option.value && option.subcategories.length > 0 && (
              <div className="ml-6 mt-2 space-y-1">
                {option.subcategories.map((subcategory) => (
                  <button
                    key={subcategory.value}
                    type="button"
                    onClick={() => handleSubcategorySelect(subcategory)}
                    disabled={disabled}
                    className={`w-full p-2 border rounded text-left transition-all hover:shadow-sm disabled:opacity-50 flex items-center space-x-3 ${
                      selectedSubcategory === subcategory.value
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <span className="text-sm">{subcategory.icon}</span>
                    <div className="flex-1">
                      <div className="text-sm font-medium">{subcategory.label}</div>
                      <div className="text-xs text-gray-500">{subcategory.description}</div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
