'use client'

import React, { useState, useMemo } from 'react'
import { ChevronDownIcon, InformationCircleIcon } from '@heroicons/react/24/outline'
import { 
  getConditionOptions,
  ProductCategory,
  ProductCondition,
  type ConditionOption 
} from '@/lib/product-constants'

interface ConditionSelectorProps {
  selectedCondition?: string
  onConditionChange: (condition: string) => void
  category?: ProductCategory
  placeholder?: string
  required?: boolean
  disabled?: boolean
  className?: string
  includeAll?: boolean
  layout?: 'dropdown' | 'grid' | 'list'
  showDescription?: boolean
}

export default function ConditionSelector({
  selectedCondition = '',
  onConditionChange,
  category,
  placeholder = '选择商品成色',
  required = false,
  disabled = false,
  className = '',
  includeAll = false,
  layout = 'dropdown',
  showDescription = true
}: ConditionSelectorProps) {
  const [showDropdown, setShowDropdown] = useState(false)
  const [showTooltip, setShowTooltip] = useState<string | null>(null)

  // 获取成色选项
  const options = useMemo(() => {
    return getConditionOptions(category, includeAll)
  }, [category, includeAll])

  // 处理成色选择
  const handleConditionSelect = (option: ConditionOption) => {
    onConditionChange(option.value)
    setShowDropdown(false)
  }

  // 获取选中成色的信息
  const selectedOption = options.find(opt => opt.value === selectedCondition)

  // 获取显示文本
  const getDisplayText = () => {
    if (selectedOption) return selectedOption.label
    return placeholder
  }

  // 下拉选择器布局
  if (layout === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          商品成色 {required && <span className="text-red-500">*</span>}
          {showDescription && (
            <button
              type="button"
              className="ml-1 text-gray-400 hover:text-gray-600"
              onMouseEnter={() => setShowTooltip('condition-help')}
              onMouseLeave={() => setShowTooltip(null)}
            >
              <InformationCircleIcon className="h-4 w-4 inline" />
            </button>
          )}
        </label>
        
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowDropdown(!showDropdown)}
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
          >
            <div className="flex items-center space-x-2">
              {selectedOption && (
                <span className="text-lg">{selectedOption.icon}</span>
              )}
              <span className={selectedOption ? 'text-gray-900' : 'text-gray-500'}>
                {getDisplayText()}
              </span>
            </div>
            <ChevronDownIcon className="h-4 w-4 text-gray-400" />
          </button>

          {/* 下拉列表 */}
          {showDropdown && !disabled && (
            <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
              {options.length > 0 ? (
                <div className="py-1">
                  {options.map((option) => (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => handleConditionSelect(option)}
                      className={`w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center space-x-3 ${
                        selectedCondition === option.value ? 'bg-blue-50 text-blue-700' : ''
                      }`}
                    >
                      <span className="text-lg">{option.icon}</span>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{option.label}</div>
                        {showDescription && (
                          <div className="text-xs text-gray-500">{option.description}</div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  暂无可选成色
                </div>
              )}
            </div>
          )}
        </div>

        {/* 帮助提示 */}
        {showTooltip === 'condition-help' && (
          <div className="absolute z-20 mt-1 p-3 bg-gray-800 text-white text-xs rounded-md shadow-lg max-w-xs">
            <div className="font-medium mb-1">成色说明：</div>
            <ul className="space-y-1">
              <li>• 全新：未使用过，包装完整</li>
              <li>• 几乎全新：使用极少，无明显磨损</li>
              <li>• 轻微使用痕迹：有轻微使用痕迹，功能完好</li>
              <li>• 明显使用痕迹：有明显使用痕迹，功能正常</li>
              <li>• 需要维修：有损坏或功能问题</li>
            </ul>
          </div>
        )}

        {/* 点击外部关闭下拉列表 */}
        {showDropdown && (
          <div
            className="fixed inset-0 z-0"
            onClick={() => setShowDropdown(false)}
          />
        )}
      </div>
    )
  }

  // 网格布局
  if (layout === 'grid') {
    return (
      <div className={className}>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          商品成色 {required && <span className="text-red-500">*</span>}
        </label>

        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleConditionSelect(option)}
              disabled={disabled}
              className={`p-3 border rounded-lg text-center transition-all hover:shadow-md disabled:opacity-50 ${
                selectedCondition === option.value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-1">{option.icon}</div>
              <div className="text-sm font-medium">{option.label}</div>
              {showDescription && (
                <div className="text-xs text-gray-500 mt-1">{option.description}</div>
              )}
            </button>
          ))}
        </div>
      </div>
    )
  }

  // 列表布局
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-3">
        商品成色 {required && <span className="text-red-500">*</span>}
      </label>

      <div className="space-y-2">
        {options.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => handleConditionSelect(option)}
            disabled={disabled}
            className={`w-full p-3 border rounded-lg text-left transition-all hover:shadow-sm disabled:opacity-50 flex items-center space-x-3 ${
              selectedCondition === option.value
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <span className="text-xl">{option.icon}</span>
            <div className="flex-1">
              <div className="font-medium">{option.label}</div>
              {showDescription && (
                <div className="text-sm text-gray-500">{option.description}</div>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}
