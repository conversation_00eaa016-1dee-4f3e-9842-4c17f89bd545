'use client'

import React, { useState, useMemo } from 'react'
import { ChevronDownIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { 
  categoryOptions, 
  getCategoryOptions,
  ProductCategory,
  type CategoryOption 
} from '@/lib/product-constants'

interface CategorySelectorProps {
  selectedCategory?: string
  onCategoryChange: (category: string) => void
  placeholder?: string
  required?: boolean
  disabled?: boolean
  className?: string
  showSearch?: boolean
  includeAll?: boolean
  layout?: 'dropdown' | 'grid' | 'list'
}

export default function CategorySelector({
  selectedCategory = '',
  onCategoryChange,
  placeholder = '选择商品分类',
  required = false,
  disabled = false,
  className = '',
  showSearch = true,
  includeAll = false,
  layout = 'dropdown'
}: CategorySelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showDropdown, setShowDropdown] = useState(false)

  // 获取分类选项
  const options = useMemo(() => {
    const allOptions = getCategoryOptions(includeAll)
    
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      return allOptions.filter(option => 
        option.label.toLowerCase().includes(query) ||
        option.description.toLowerCase().includes(query) ||
        option.keywords.some(keyword => keyword.includes(query))
      )
    }
    
    return allOptions
  }, [searchQuery, includeAll])

  // 处理分类选择
  const handleCategorySelect = (option: CategoryOption) => {
    onCategoryChange(option.value)
    setSearchQuery('')
    setShowDropdown(false)
  }

  // 获取选中分类的信息
  const selectedOption = options.find(opt => opt.value === selectedCategory)

  // 获取显示文本
  const getDisplayText = () => {
    if (selectedOption) return selectedOption.label
    if (searchQuery) return searchQuery
    return placeholder
  }

  // 下拉选择器布局
  if (layout === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          商品分类 {required && <span className="text-red-500">*</span>}
        </label>
        
        <div className="relative">
          {showSearch ? (
            <div className="relative">
              <input
                type="text"
                value={searchQuery || (selectedOption?.label || '')}
                onChange={(e) => {
                  setSearchQuery(e.target.value)
                  setShowDropdown(true)
                }}
                onFocus={() => setShowDropdown(true)}
                placeholder={placeholder}
                disabled={disabled}
                className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          ) : (
            <button
              type="button"
              onClick={() => setShowDropdown(!showDropdown)}
              disabled={disabled}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
            >
              <div className="flex items-center space-x-2">
                {selectedOption && (
                  <span className="text-lg">{selectedOption.icon}</span>
                )}
                <span className={selectedOption ? 'text-gray-900' : 'text-gray-500'}>
                  {getDisplayText()}
                </span>
              </div>
              <ChevronDownIcon className="h-4 w-4 text-gray-400" />
            </button>
          )}

          {/* 下拉列表 */}
          {showDropdown && !disabled && (
            <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
              {options.length > 0 ? (
                <div className="py-1">
                  {options.map((option) => (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => handleCategorySelect(option)}
                      className={`w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center space-x-3 ${
                        selectedCategory === option.value ? 'bg-blue-50 text-blue-700' : ''
                      }`}
                    >
                      <span className="text-lg">{option.icon}</span>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{option.label}</div>
                        <div className="text-xs text-gray-500">{option.description}</div>
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  未找到匹配的分类
                </div>
              )}
            </div>
          )}
        </div>

        {/* 点击外部关闭下拉列表 */}
        {showDropdown && (
          <div
            className="fixed inset-0 z-0"
            onClick={() => setShowDropdown(false)}
          />
        )}
      </div>
    )
  }

  // 网格布局
  if (layout === 'grid') {
    return (
      <div className={className}>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          商品分类 {required && <span className="text-red-500">*</span>}
        </label>
        
        {showSearch && (
          <div className="relative mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索分类..."
              disabled={disabled}
              className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
            />
            <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          </div>
        )}

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleCategorySelect(option)}
              disabled={disabled}
              className={`p-3 border rounded-lg text-center transition-all hover:shadow-md disabled:opacity-50 ${
                selectedCategory === option.value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-1">{option.icon}</div>
              <div className="text-sm font-medium">{option.label}</div>
              <div className="text-xs text-gray-500 mt-1">{option.description}</div>
            </button>
          ))}
        </div>
      </div>
    )
  }

  // 列表布局
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-3">
        商品分类 {required && <span className="text-red-500">*</span>}
      </label>
      
      {showSearch && (
        <div className="relative mb-4">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索分类..."
            disabled={disabled}
            className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
          />
          <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
        </div>
      )}

      <div className="space-y-2">
        {options.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => handleCategorySelect(option)}
            disabled={disabled}
            className={`w-full p-3 border rounded-lg text-left transition-all hover:shadow-sm disabled:opacity-50 flex items-center space-x-3 ${
              selectedCategory === option.value
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <span className="text-xl">{option.icon}</span>
            <div className="flex-1">
              <div className="font-medium">{option.label}</div>
              <div className="text-sm text-gray-500">{option.description}</div>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}
