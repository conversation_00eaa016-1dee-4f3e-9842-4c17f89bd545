'use client'

import { memo } from 'react'
import dynamic from 'next/dynamic'

// 动态导入常用图标
const ShieldCheckIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.ShieldCheckIcon })),
  { ssr: false }
)

const KeyIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.KeyIcon })),
  { ssr: false }
)

const EnvelopeIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.EnvelopeIcon })),
  { ssr: false }
)

const DevicePhoneMobileIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.DevicePhoneMobileIcon })),
  { ssr: false }
)

const ClockIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.ClockIcon })),
  { ssr: false }
)

const ExclamationTriangleIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.ExclamationTriangleIcon })),
  { ssr: false }
)

const CheckCircleIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.CheckCircleIcon })),
  { ssr: false }
)

const EyeIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.EyeIcon })),
  { ssr: false }
)

const EyeSlashIcon = dynamic(() => 
  import('@heroicons/react/24/outline').then(mod => ({ default: mod.EyeSlashIcon })),
  { ssr: false }
)

// 图标映射
const iconMap = {
  'shield-check': ShieldCheckIcon,
  'key': KeyIcon,
  'envelope': EnvelopeIcon,
  'device-phone-mobile': DevicePhoneMobileIcon,
  'clock': ClockIcon,
  'exclamation-triangle': ExclamationTriangleIcon,
  'check-circle': CheckCircleIcon,
  'eye': EyeIcon,
  'eye-slash': EyeSlashIcon,
}

interface OptimizedIconProps {
  name: keyof typeof iconMap
  className?: string
  size?: number
}

const OptimizedIcon = memo(({ name, className = 'h-6 w-6', size }: OptimizedIconProps) => {
  const IconComponent = iconMap[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in iconMap`)
    return null
  }

  const iconProps = {
    className: size ? `h-${size} w-${size}` : className,
  }

  return <IconComponent {...iconProps} />
})

OptimizedIcon.displayName = 'OptimizedIcon'

export default OptimizedIcon
export { iconMap }
