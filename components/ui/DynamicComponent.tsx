'use client'

import dynamic from 'next/dynamic'
import { type ComponentType, Suspense } from 'react'

// 加载中组件
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-2 text-gray-600">加载中...</span>
  </div>
)

const LoadingSkeleton = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
  </div>
)

// 动态组件配置
const dynamicComponents = {
  // 设置页面组件
  SecuritySettings: dynamic(() => import('@/app/settings/page'), {
    loading: () => <LoadingSpinner />,
    ssr: false
  }),
  
  // 管理员页面组件
  AdminDashboard: dynamic(() => import('@/app/admin/page'), {
    loading: () => <LoadingSpinner />,
    ssr: false
  }),
  
  // 用户资料页面组件
  UserProfile: dynamic(() => import('@/app/profile/page'), {
    loading: () => <LoadingSkeleton />,
    ssr: false
  }),
  
  // 性能监控组件
  PerformanceMonitor: dynamic(() => import('@/components/PerformanceMonitor'), {
    loading: () => <LoadingSpinner />,
    ssr: false
  }),
}

interface DynamicComponentProps {
  component: keyof typeof dynamicComponents
  fallback?: ComponentType
  [key: string]: any
}

export const DynamicComponent = ({ 
  component, 
  fallback: Fallback = LoadingSpinner,
  ...props 
}: DynamicComponentProps) => {
  const Component = dynamicComponents[component]
  
  if (!Component) {
    console.warn(`Dynamic component "${component}" not found`)
    return <div>组件未找到</div>
  }

  return (
    <Suspense fallback={<Fallback />}>
      <Component {...props} />
    </Suspense>
  )
}

export default DynamicComponent
export { dynamicComponents }
