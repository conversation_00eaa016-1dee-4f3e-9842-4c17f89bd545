'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  TicketIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  GiftIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Voucher {
  id: string
  code: string
  amount: number
  description: string
  validUntil: string
  isUsed: boolean
  usedAt?: string
  createdAt: string
}

interface VoucherStats {
  totalVouchers: number
  totalAmount: number
  usedVouchers: number
  usedAmount: number
  expiredVouchers: number
  expiredAmount: number
}

interface VoucherManagerProps {
  className?: string
}

export default function VoucherManager({ className = '' }: VoucherManagerProps) {
  const { data: session } = useSession()
  const [vouchers, setVouchers] = useState<Voucher[]>([])
  const [stats, setStats] = useState<VoucherStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'available' | 'used' | 'expired'>('all')
  const [selectedVoucher, setSelectedVoucher] = useState<string | null>(null)
  const [showUseModal, setShowUseModal] = useState(false)

  useEffect(() => {
    fetchVouchers()
  }, [])

  const fetchVouchers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/withdrawal/voucher')
      const data = await response.json()

      if (data.success) {
        setVouchers(data.data.vouchers)
        setStats(data.data.stats)
      } else {
        setError(data.error || '获取提现券失败')
      }
    } catch (error) {
      console.error('获取提现券失败:', error)
      setError('获取提现券失败')
    } finally {
      setLoading(false)
    }
  }

  const useVoucher = async (voucherId: string, withdrawalAmount: number) => {
    try {
      const response = await fetch('/api/withdrawal/voucher/use', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          voucherId,
          withdrawalAmount
        })
      })

      const data = await response.json()

      if (data.success) {
        await fetchVouchers() // 刷新列表
        setShowUseModal(false)
        setSelectedVoucher(null)
        // 这里可以触发提现流程
      } else {
        setError(data.error || '使用提现券失败')
      }
    } catch (error) {
      console.error('使用提现券失败:', error)
      setError('使用提现券失败')
    }
  }

  const getFilteredVouchers = () => {
    const now = new Date()
    
    return vouchers.filter(voucher => {
      const isExpired = new Date(voucher.validUntil) < now
      
      switch (filter) {
        case 'available':
          return !voucher.isUsed && !isExpired
        case 'used':
          return voucher.isUsed
        case 'expired':
          return !voucher.isUsed && isExpired
        default:
          return true
      }
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getVoucherStatus = (voucher: Voucher) => {
    if (voucher.isUsed) {
      return {
        label: '已使用',
        color: 'bg-gray-100 text-gray-800',
        icon: CheckCircleIcon
      }
    }
    
    const isExpired = new Date(voucher.validUntil) < new Date()
    if (isExpired) {
      return {
        label: '已过期',
        color: 'bg-red-100 text-red-800',
        icon: XCircleIcon
      }
    }
    
    return {
      label: '可使用',
      color: 'bg-green-100 text-green-800',
      icon: TicketIcon
    }
  }

  const getTimeRemaining = (validUntil: string) => {
    const now = new Date()
    const expiry = new Date(validUntil)
    const diff = expiry.getTime() - now.getTime()
    
    if (diff <= 0) return '已过期'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days}天${hours}小时`
    if (hours > 0) return `${hours}小时`
    return '即将过期'
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* 头部 */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <GiftIcon className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">我的提现券</h3>
          </div>
          
          <button
            onClick={fetchVouchers}
            className="text-gray-400 hover:text-gray-600"
            title="刷新"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>

        {/* 统计信息 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-600">总提现券</p>
                  <p className="text-2xl font-bold text-blue-900">{stats.totalVouchers}</p>
                </div>
                <TicketIcon className="h-8 w-8 text-blue-500" />
              </div>
              <p className="text-xs text-blue-600 mt-1">
                总价值 {stats.totalAmount} USDT
              </p>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-green-600">已使用</p>
                  <p className="text-2xl font-bold text-green-900">{stats.usedVouchers}</p>
                </div>
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
              </div>
              <p className="text-xs text-green-600 mt-1">
                已节省 {stats.usedAmount} USDT
              </p>
            </div>
            
            <div className="bg-red-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-red-600">已过期</p>
                  <p className="text-2xl font-bold text-red-900">{stats.expiredVouchers}</p>
                </div>
                <XCircleIcon className="h-8 w-8 text-red-500" />
              </div>
              <p className="text-xs text-red-600 mt-1">
                损失 {stats.expiredAmount} USDT
              </p>
            </div>
          </div>
        )}

        {/* 筛选器 */}
        <div className="flex space-x-2">
          {[
            { key: 'all', label: '全部' },
            { key: 'available', label: '可使用' },
            { key: 'used', label: '已使用' },
            { key: 'expired', label: '已过期' }
          ].map(option => (
            <button
              key={option.key}
              onClick={() => setFilter(option.key as any)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filter === option.key
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="border-b border-gray-200 p-4 bg-red-50">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
            <span className="text-sm text-red-700">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* 提现券列表 */}
      <div className="p-6">
        {getFilteredVouchers().length === 0 ? (
          <div className="text-center py-8">
            <TicketIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              {filter === 'all' ? '暂无提现券' : `暂无${filter === 'available' ? '可使用' : filter === 'used' ? '已使用' : '已过期'}的提现券`}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {getFilteredVouchers().map(voucher => {
              const status = getVoucherStatus(voucher)
              const StatusIcon = status.icon
              
              return (
                <div
                  key={voucher.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="bg-purple-100 rounded-full p-2">
                        <CurrencyDollarIcon className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {voucher.amount} USDT 免手续费
                        </h4>
                        <p className="text-sm text-gray-500">{voucher.description}</p>
                      </div>
                    </div>
                    
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${status.color}`}>
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {status.label}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">券码:</span>
                      <span className="ml-2 font-mono font-medium">{voucher.code}</span>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">有效期:</span>
                      <span className="ml-2">{formatDate(voucher.validUntil)}</span>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">
                        {voucher.isUsed ? '使用时间:' : '剩余时间:'}
                      </span>
                      <span className="ml-2">
                        {voucher.isUsed 
                          ? formatDate(voucher.usedAt!) 
                          : getTimeRemaining(voucher.validUntil)
                        }
                      </span>
                    </div>
                  </div>
                  
                  {!voucher.isUsed && new Date(voucher.validUntil) > new Date() && (
                    <div className="mt-3 flex justify-end">
                      <button
                        onClick={() => {
                          setSelectedVoucher(voucher.id)
                          setShowUseModal(true)
                        }}
                        className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                      >
                        立即使用
                      </button>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* 使用提现券模态框 */}
      {showUseModal && selectedVoucher && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">使用提现券</h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                此功能将在提现页面中集成，您可以在提现时选择使用提现券来免除手续费。
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <p className="text-sm text-blue-700">
                  💡 提示：前往提现页面即可使用您的提现券
                </p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowUseModal(false)
                  setSelectedVoucher(null)
                }}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md font-medium transition-colors"
              >
                取消
              </button>
              <button
                onClick={() => {
                  // 这里可以跳转到提现页面
                  window.location.href = '/withdraw'
                }}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                前往提现
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
