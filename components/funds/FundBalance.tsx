'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Wallet, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react'

interface FundBalanceData {
  user: {
    id: string
    name: string
    email: string
    creditScore: number
  }
  balance: {
    total: number
    available: number
    frozen: number
    settled: number
  }
  statistics: {
    totalFreezeRecords: number
    purposeStats: Record<string, any>
  }
  recentFreezeRecords: Array<{
    id: string
    amount: number
    purpose: string
    status: string
    createdAt: string
    notes?: string
  }>
}

const statusConfig = {
  FROZEN: { label: '已冻结', color: 'bg-yellow-500', icon: Clock },
  CONFIRMED: { label: '已确认', color: 'bg-blue-500', icon: AlertCircle },
  SETTLED: { label: '已划扣', color: 'bg-green-500', icon: CheckCircle },
  CANCELLED: { label: '已取消', color: 'bg-gray-500', icon: XCircle }
}

const purposeConfig = {
  PURCHASE: { label: '商品购买', icon: '🛒' },
  WITHDRAWAL: { label: '提现', icon: '💰' },
  CONSUMPTION: { label: '平台消费', icon: '🎯' },
  GUARANTEE: { label: '担保', icon: '🛡️' }
}

export default function FundBalance() {
  const [data, setData] = useState<FundBalanceData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchData = async () => {
    try {
      const response = await fetch('/api/funds/balance')
      if (response.ok) {
        const result = await response.json()
        setData(result)
      } else {
        console.error('获取资金数据失败')
      }
    } catch (error) {
      console.error('获取资金数据失败:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleRefresh = () => {
    setRefreshing(true)
    fetchData()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">无法加载资金数据</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 余额概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Wallet className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-500">总担保金</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">{data.balance.total.toFixed(2)}</span>
              <span className="text-sm text-gray-500 ml-1">USDT</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-gray-500">可用余额</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold text-green-600">{data.balance.available.toFixed(2)}</span>
              <span className="text-sm text-gray-500 ml-1">USDT</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-500" />
              <span className="text-sm font-medium text-gray-500">冻结金额</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold text-yellow-600">{data.balance.frozen.toFixed(2)}</span>
              <span className="text-sm text-gray-500 ml-1">USDT</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingDown className="h-5 w-5 text-gray-500" />
              <span className="text-sm font-medium text-gray-500">已划扣</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold text-gray-600">{data.balance.settled.toFixed(2)}</span>
              <span className="text-sm text-gray-500 ml-1">USDT</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息 */}
      <Tabs defaultValue="records" className="w-full">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="records">资金记录</TabsTrigger>
            <TabsTrigger value="statistics">统计信息</TabsTrigger>
          </TabsList>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        <TabsContent value="records">
          <Card>
            <CardHeader>
              <CardTitle>最近资金记录</CardTitle>
            </CardHeader>
            <CardContent>
              {data.recentFreezeRecords.length === 0 ? (
                <p className="text-gray-500 text-center py-8">暂无资金记录</p>
              ) : (
                <div className="space-y-4">
                  {data.recentFreezeRecords.map((record) => {
                    const statusInfo = statusConfig[record.status as keyof typeof statusConfig]
                    const purposeInfo = purposeConfig[record.purpose as keyof typeof purposeConfig]
                    const StatusIcon = statusInfo?.icon || AlertCircle

                    return (
                      <div key={record.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="text-2xl">{purposeInfo?.icon || '💳'}</div>
                          <div>
                            <div className="font-medium">{purposeInfo?.label || record.purpose}</div>
                            <div className="text-sm text-gray-500">
                              {new Date(record.createdAt).toLocaleString('zh-CN')}
                            </div>
                            {record.notes && (
                              <div className="text-xs text-gray-400 mt-1">{record.notes}</div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-lg">
                            {record.amount.toFixed(2)} USDT
                          </div>
                          <Badge variant="secondary" className="mt-1">
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {statusInfo?.label || record.status}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <Card>
            <CardHeader>
              <CardTitle>资金统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">按用途统计</h4>
                  <div className="space-y-2">
                    {Object.entries(data.statistics.purposeStats).map(([purpose, stats]: [string, any]) => {
                      const purposeInfo = purposeConfig[purpose as keyof typeof purposeConfig]
                      return (
                        <div key={purpose} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{purposeInfo?.icon || '💳'}</span>
                            <span className="font-medium">{purposeInfo?.label || purpose}</span>
                          </div>
                          <span className="font-bold">{stats.total?.toFixed(2) || '0.00'} USDT</span>
                        </div>
                      )
                    })}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">总体统计</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between p-3 bg-gray-50 rounded">
                      <span>总记录数</span>
                      <span className="font-bold">{data.statistics.totalFreezeRecords}</span>
                    </div>
                    <div className="flex justify-between p-3 bg-gray-50 rounded">
                      <span>信用积分</span>
                      <span className="font-bold">{data.user.creditScore}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
