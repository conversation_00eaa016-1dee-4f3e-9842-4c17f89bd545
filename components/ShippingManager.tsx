'use client'

import { useState } from 'react'
import Image from 'next/image'

interface ShippingManagerProps {
  orderId: string
  orderStatus: string
  onShippingComplete: () => void
}

interface ShippingProofImage {
  originalName: string
  fileName: string
  url: string
  size: number
  type: string
}

export default function ShippingManager({ orderId, orderStatus, onShippingComplete }: ShippingManagerProps) {
  const [shippingType, setShippingType] = useState<'direct' | 'text' | 'images'>('direct')
  const [loading, setLoading] = useState(false)
  const [uploadingImages, setUploadingImages] = useState(false)
  
  // 基本发货信息
  const [trackingNumber, setTrackingNumber] = useState('')
  const [shippingCompany, setShippingCompany] = useState('')
  
  // 文字凭证
  const [shippingProofText, setShippingProofText] = useState('')
  
  // 图片凭证
  const [shippingProofImages, setShippingProofImages] = useState<ShippingProofImage[]>([])

  const handleImageUpload = async (files: FileList) => {
    if (files.length === 0) return

    setUploadingImages(true)
    try {
      const formData = new FormData()
      Array.from(files).forEach(file => {
        formData.append('files', file)
      })

      const response = await fetch('/api/upload/shipping-proof', {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        setShippingProofImages(prev => [...prev, ...data.data])
      } else {
        const error = await response.json()
        alert(error.error || '上传失败')
      }
    } catch (error) {
      console.error('上传图片失败:', error)
      alert('上传失败，请稍后重试')
    } finally {
      setUploadingImages(false)
    }
  }

  const removeImage = (index: number) => {
    setShippingProofImages(prev => prev.filter((_, i) => i !== index))
  }

  const handleShipping = async () => {
    // 验证必填字段
    if (shippingType === 'text' && !shippingProofText.trim()) {
      alert('请输入发货凭证文字')
      return
    }

    if (shippingType === 'images' && shippingProofImages.length === 0) {
      alert('请上传发货凭证图片')
      return
    }

    setLoading(true)
    try {
      const requestData: any = {
        type: shippingType,
        trackingNumber: trackingNumber.trim() || undefined,
        shippingCompany: shippingCompany.trim() || undefined
      }

      if (shippingType === 'text') {
        requestData.shippingProofText = shippingProofText.trim()
      }

      if (shippingType === 'images') {
        requestData.shippingProofImages = shippingProofImages
      }

      const response = await fetch(`/api/orders/${orderId}/shipping`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const data = await response.json()
        alert('发货成功！')
        onShippingComplete()
      } else {
        const error = await response.json()
        alert(error.error || '发货失败')
      }
    } catch (error) {
      console.error('发货失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 只有在订单状态允许发货时才显示
  const canShip = ['PAID', 'PENDING_SHIPMENT', 'PROCESSING'].includes(orderStatus)
  if (!canShip) {
    return null
  }

  return (
    <div className="border rounded-lg p-4">
      <h4 className="font-medium text-gray-900 mb-4">发货管理</h4>
      
      {/* 发货类型选择 */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          发货方式
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="shippingType"
              value="direct"
              checked={shippingType === 'direct'}
              onChange={(e) => setShippingType(e.target.value as any)}
              className="mr-2"
            />
            <span className="text-sm">直接确认发货</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="shippingType"
              value="text"
              checked={shippingType === 'text'}
              onChange={(e) => setShippingType(e.target.value as any)}
              className="mr-2"
            />
            <span className="text-sm">上传文字凭证</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="shippingType"
              value="images"
              checked={shippingType === 'images'}
              onChange={(e) => setShippingType(e.target.value as any)}
              className="mr-2"
            />
            <span className="text-sm">上传图片凭证</span>
          </label>
        </div>
      </div>

      {/* 基本发货信息 */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            快递公司（可选）
          </label>
          <input
            type="text"
            value={shippingCompany}
            onChange={(e) => setShippingCompany(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="如：顺丰快递"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            快递单号（可选）
          </label>
          <input
            type="text"
            value={trackingNumber}
            onChange={(e) => setTrackingNumber(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="如：SF1234567890"
          />
        </div>
      </div>

      {/* 文字凭证输入 */}
      {shippingType === 'text' && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            发货凭证文字 *
          </label>
          <textarea
            value={shippingProofText}
            onChange={(e) => setShippingProofText(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            placeholder="请输入发货相关信息，如发货时间、发货方式、注意事项等..."
          />
        </div>
      )}

      {/* 图片凭证上传 */}
      {shippingType === 'images' && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            发货凭证图片 *
          </label>
          
          {/* 上传按钮 */}
          <div className="mb-3">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
              className="hidden"
              id="shipping-images"
              disabled={uploadingImages}
            />
            <label
              htmlFor="shipping-images"
              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${
                uploadingImages ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {uploadingImages ? '上传中...' : '选择图片'}
            </label>
            <span className="ml-2 text-xs text-gray-500">
              支持 JPG、PNG、GIF 格式，最多5张，每张不超过5MB
            </span>
          </div>

          {/* 已上传的图片预览 */}
          {shippingProofImages.length > 0 && (
            <div className="grid grid-cols-3 gap-2">
              {shippingProofImages.map((image, index) => (
                <div key={index} className="relative">
                  <Image
                    src={image.url}
                    alt={`发货凭证 ${index + 1}`}
                    width={100}
                    height={100}
                    className="w-full h-24 object-cover rounded border"
                  />
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 发货按钮 */}
      <button
        onClick={handleShipping}
        disabled={loading}
        className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-md font-medium"
      >
        {loading ? '发货中...' : '确认发货'}
      </button>
    </div>
  )
}
