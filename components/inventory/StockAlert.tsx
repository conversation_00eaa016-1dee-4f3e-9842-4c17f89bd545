'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface Product {
  id: string
  title: string
  stock: number
  status: string
}

interface StockAlertProps {
  productId?: string
  showGlobalAlerts?: boolean
}

export default function StockAlert({ productId, showGlobalAlerts = false }: StockAlertProps) {
  const { data: session } = useSession()
  const [alerts, setAlerts] = useState<{
    lowStock: Product[]
    outOfStock: Product[]
    loading: boolean
  }>({
    lowStock: [],
    outOfStock: [],
    loading: true
  })

  useEffect(() => {
    if (session?.user && showGlobalAlerts) {
      fetchStockAlerts()
    }
  }, [session, showGlobalAlerts])

  const fetchStockAlerts = async () => {
    try {
      const response = await fetch('/api/user/products/stock-alerts')
      if (response.ok) {
        const data = await response.json()
        setAlerts({
          lowStock: data.lowStock || [],
          outOfStock: data.outOfStock || [],
          loading: false
        })
      }
    } catch (error) {
      console.error('获取库存提醒失败:', error)
      setAlerts(prev => ({ ...prev, loading: false }))
    }
  }

  const handleRestockProduct = async (productId: string) => {
    // 跳转到商品编辑页面
    window.location.href = `/products/${productId}/edit`
  }

  if (!session?.user || alerts.loading) {
    return null
  }

  const totalAlerts = alerts.lowStock.length + alerts.outOfStock.length

  if (totalAlerts === 0) {
    return null
  }

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            库存提醒 ({totalAlerts} 个商品需要关注)
          </h3>
          
          {alerts.outOfStock.length > 0 && (
            <div className="mt-2">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                已售完商品 ({alerts.outOfStock.length})
              </h4>
              <div className="space-y-1">
                {alerts.outOfStock.slice(0, 3).map((product) => (
                  <div key={product.id} className="flex items-center justify-between bg-red-50 p-2 rounded">
                    <div>
                      <span className="text-sm text-red-800 font-medium">{product.title}</span>
                      <span className="text-xs text-red-600 ml-2">库存: {product.stock}</span>
                    </div>
                    <button
                      onClick={() => handleRestockProduct(product.id)}
                      className="text-xs bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded"
                    >
                      补充库存
                    </button>
                  </div>
                ))}
                {alerts.outOfStock.length > 3 && (
                  <p className="text-xs text-red-600">
                    还有 {alerts.outOfStock.length - 3} 个商品已售完...
                  </p>
                )}
              </div>
            </div>
          )}

          {alerts.lowStock.length > 0 && (
            <div className="mt-3">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">
                低库存商品 ({alerts.lowStock.length})
              </h4>
              <div className="space-y-1">
                {alerts.lowStock.slice(0, 3).map((product) => (
                  <div key={product.id} className="flex items-center justify-between bg-yellow-50 p-2 rounded">
                    <div>
                      <span className="text-sm text-yellow-800 font-medium">{product.title}</span>
                      <span className="text-xs text-yellow-600 ml-2">库存: {product.stock}</span>
                    </div>
                    <button
                      onClick={() => handleRestockProduct(product.id)}
                      className="text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded"
                    >
                      补充库存
                    </button>
                  </div>
                ))}
                {alerts.lowStock.length > 3 && (
                  <p className="text-xs text-yellow-600">
                    还有 {alerts.lowStock.length - 3} 个商品库存较低...
                  </p>
                )}
              </div>
            </div>
          )}

          <div className="mt-3">
            <button
              onClick={fetchStockAlerts}
              className="text-sm text-yellow-800 hover:text-yellow-900 underline"
            >
              刷新库存状态
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// 单个商品的库存状态组件
export function ProductStockStatus({
  product,
  isOwner = false
}: {
  product: Product
  isOwner?: boolean
}) {
  // 只有商品所有者才能看到库存不足提醒
  if (!isOwner || product.stock > 5) {
    return null
  }

  if (product.stock <= 0) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-4">
        <div className="flex items-center">
          <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span className="font-medium">商品已售完</span>
        </div>
        <p className="text-sm mt-1">
          该商品库存为 {product.stock}，已自动下架。请补充库存后重新上架。
        </p>
      </div>
    )
  }

  return (
    <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-3 py-2 rounded mb-4">
      <div className="flex items-center">
        <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span className="font-medium">库存不足</span>
      </div>
      <p className="text-sm mt-1">
        该商品仅剩 {product.stock} 件，建议及时补充库存。
      </p>
    </div>
  )
}
