'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Crown,
  Star,
  TrendingUp,
  Clock,
  Settings,
  RefreshCw
} from 'lucide-react'

interface CreditLevelData {
  user: {
    id: string
    name: string
    depositBalance: number
    creditPoints: number
    accountAge: number
  }
  currentLevel: {
    level: string
    config: {
      minDeposit: number
      minCreditPoints: number
      benefits: any
      color: string
      icon: string
    }
    benefits: any
    upgraded: boolean
  }
  nextLevel?: {
    level: string
    config: any
    progress: {
      deposit: number
      points: number
      overall: number
    }
    requirements: {
      depositNeeded: number
      pointsNeeded: number
    }
  }
  pendingSettlement: {
    totalAmount: number
    transactionCount: number
    estimatedFee: number
    nextSettlementDate: string
  }
  withdrawalOptimization: {
    estimate: {
      smartFee: number
      savings: number
      savingsRate: number
    }
    optimal: {
      currentFee: number
      optimalFee: number
      potentialSavings: number
      recommendation: string
    }
  }
}

const levelIcons = {
  BRONZE: '🥉',
  SILVER: '🥈', 
  GOLD: '🥇',
  PLATINUM: '💎',
  DIAMOND: '💎'
}

const levelColors = {
  BRONZE: 'from-amber-600 to-amber-800',
  SILVER: 'from-gray-400 to-gray-600',
  GOLD: 'from-yellow-400 to-yellow-600',
  PLATINUM: 'from-purple-400 to-purple-600',
  DIAMOND: 'from-blue-400 to-blue-600'
}

// 自定义进度条组件
const ProgressBar = ({ value, className = "" }: { value: number; className?: string }) => {
  const percentage = Math.min(Math.max(value, 0), 100)

  return (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <div
        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${percentage}%` }}
      />
    </div>
  )
}

export default function CreditLevelCard() {
  const [data, setData] = useState<CreditLevelData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchData = async () => {
    try {
      const response = await fetch('/api/user/credit-level')
      if (response.ok) {
        const result = await response.json()
        setData(result)
      } else {
        console.error('获取信用等级数据失败')
      }
    } catch (error) {
      console.error('获取信用等级数据失败:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleRefresh = () => {
    setRefreshing(true)
    fetchData()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">无法加载信用等级数据</p>
      </div>
    )
  }

  const currentLevel = data.currentLevel
  const nextLevel = data.nextLevel

  return (
    <div className="space-y-6">
      {/* 等级升级提示 */}
      {currentLevel.upgraded && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Crown className="h-5 w-5 text-green-600" />
              <span className="text-green-800 font-medium">
                恭喜！您的信用等级已升级到 {currentLevel.level}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 当前等级卡片 */}
      <Card className="overflow-hidden">
        <div className={`h-2 bg-gradient-to-r ${levelColors[currentLevel.level as keyof typeof levelColors]}`} />
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">{levelIcons[currentLevel.level as keyof typeof levelIcons]}</div>
              <div>
                <CardTitle className="text-xl">{currentLevel.level} 级用户</CardTitle>
                <p className="text-sm text-gray-500">信用积分: {data.user.creditPoints}</p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="benefits" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="benefits">权益</TabsTrigger>
              <TabsTrigger value="progress">进度</TabsTrigger>
              <TabsTrigger value="settlement">结算</TabsTrigger>
              <TabsTrigger value="optimization">优化</TabsTrigger>
            </TabsList>

            <TabsContent value="benefits" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">提现手续费折扣</span>
                  </div>
                  <p className="text-lg font-bold text-blue-600">
                    {(currentLevel.benefits.withdrawalFeeDiscount * 100).toFixed(0)}%
                  </p>
                </div>
                
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">交易手续费折扣</span>
                  </div>
                  <p className="text-lg font-bold text-green-600">
                    {(currentLevel.benefits.tradingFeeDiscount * 100).toFixed(0)}%
                  </p>
                </div>

                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Crown className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">优先展示</span>
                  </div>
                  <p className="text-sm text-purple-600">
                    {currentLevel.benefits.priorityDisplay ? '✅ 已开启' : '❌ 未开启'}
                  </p>
                </div>

                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Settings className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium">专属客服</span>
                  </div>
                  <p className="text-sm text-orange-600">
                    {currentLevel.benefits.customService ? '✅ 已开启' : '❌ 未开启'}
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="progress" className="space-y-4">
              {nextLevel ? (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">升级到 {nextLevel.level}</span>
                    <span className="text-sm text-gray-500">
                      {(nextLevel.progress.overall * 100).toFixed(0)}%
                    </span>
                  </div>
                  <ProgressBar value={nextLevel.progress.overall * 100} className="mb-4" />
                  
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>担保金要求</span>
                        <span>{data.user.depositBalance.toFixed(2)} / {nextLevel.config.minDeposit} USDT</span>
                      </div>
                      <ProgressBar value={nextLevel.progress.deposit * 100} className="mt-1" />
                      {nextLevel.requirements.depositNeeded > 0 && (
                        <p className="text-xs text-gray-500 mt-1">
                          还需充值 {nextLevel.requirements.depositNeeded.toFixed(2)} USDT
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>信用积分要求</span>
                        <span>{data.user.creditPoints} / {nextLevel.config.minCreditPoints}</span>
                      </div>
                      <ProgressBar value={nextLevel.progress.points * 100} className="mt-1" />
                      {nextLevel.requirements.pointsNeeded > 0 && (
                        <p className="text-xs text-gray-500 mt-1">
                          还需获得 {nextLevel.requirements.pointsNeeded} 积分
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-2" />
                  <p className="text-lg font-medium">您已达到最高等级！</p>
                  <p className="text-sm text-gray-500">享受所有平台权益</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="settlement" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium">待结算金额</span>
                  </div>
                  <p className="text-lg font-bold text-yellow-600">
                    {data.pendingSettlement.totalAmount.toFixed(2)} USDT
                  </p>
                  <p className="text-xs text-gray-500">
                    {data.pendingSettlement.transactionCount} 笔交易
                  </p>
                </div>

                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">预估手续费</span>
                  </div>
                  <p className="text-lg font-bold text-green-600">
                    {data.pendingSettlement.estimatedFee.toFixed(4)} USDT
                  </p>
                  <p className="text-xs text-gray-500">
                    下次结算: {new Date(data.pendingSettlement.nextSettlementDate).toLocaleDateString('zh-CN')}
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="optimization" className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">智能提现优化</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>智能费率节省</span>
                    <span className="font-medium text-green-600">
                      {data.withdrawalOptimization.estimate.savingsRate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>每100 USDT可节省</span>
                    <span className="font-medium text-green-600">
                      {data.withdrawalOptimization.estimate.savings.toFixed(4)} USDT
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-800 mb-2">最佳提现时机</h4>
                <p className="text-sm text-gray-700">
                  {data.withdrawalOptimization.optimal.recommendation}
                </p>
                {data.withdrawalOptimization.optimal.potentialSavings > 0 && (
                  <p className="text-xs text-green-600 mt-1">
                    可节省 {data.withdrawalOptimization.optimal.potentialSavings.toFixed(4)} USDT
                  </p>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
