'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import {
  HomeIcon,
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  GiftIcon,
  SpeakerWaveIcon,
  QuestionMarkCircleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline'

// 图标映射
const iconMap = {
  HomeIcon,
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  GiftIcon,
  SpeakerWaveIcon,
  QuestionMarkCircleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon
}

// 导航项接口
interface NavigationItem {
  id: string
  name: string
  href: string
  icon: string
  badge?: string | number
  children?: NavigationItem[]
  permissions?: string[]
  enabled: boolean
  order: number
}

// 默认导航（作为后备）
const defaultNavigation = [
  { id: 'dashboard', name: '仪表板', href: '/admin', icon: 'HomeIcon', enabled: true, order: 1 },
  { id: 'users', name: '用户管理', href: '/admin/users', icon: 'UsersIcon', enabled: true, order: 2 },
  { id: 'orders', name: '订单管理', href: '/admin/orders', icon: 'ShoppingBagIcon', enabled: true, order: 3 },
  { id: 'products', name: '商品管理', href: '/admin/products', icon: 'ShoppingBagIcon', enabled: true, order: 4 },
  { id: 'mediators', name: '中间人管理', href: '/admin/mediators', icon: 'ShieldCheckIcon', enabled: true, order: 5 },
  { id: 'escrow-orders', name: '托管订单', href: '/admin/escrow-orders', icon: 'ShieldCheckIcon', enabled: true, order: 6 },
  { id: 'payments', name: '财务管理', href: '/admin/payments', icon: 'CurrencyDollarIcon', enabled: true, order: 7 },
  { id: 'deposits', name: '保证金管理', href: '/admin/deposits', icon: 'CurrencyDollarIcon', enabled: true, order: 8 },
  { id: 'disputes', name: '争议处理', href: '/admin/disputes', icon: 'ExclamationTriangleIcon', enabled: true, order: 9 },
  { id: 'giftcards', name: '礼品卡管理', href: '/admin/giftcards', icon: 'GiftIcon', enabled: true, order: 10 },
  { id: 'redemption-codes', name: '兑换码管理', href: '/admin/redemption-codes', icon: 'GiftIcon', enabled: true, order: 11 },
  { id: 'announcements', name: '公告管理', href: '/admin/announcements', icon: 'SpeakerWaveIcon', enabled: true, order: 12 },
  { id: 'help', name: '帮助中心', href: '/admin/help', icon: 'QuestionMarkCircleIcon', enabled: true, order: 13 },
  { id: 'reports', name: '数据报告', href: '/admin/reports', icon: 'ChartBarIcon', enabled: true, order: 14 },
  { id: 'settings', name: '系统设置', href: '/admin/settings', icon: 'Cog6ToothIcon', enabled: true, order: 15 },
]

interface AdminLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
  showBackToAdmin?: boolean
}

export default function AdminLayout({
  children,
  title,
  subtitle,
  actions,
  showBackToAdmin = true
}: AdminLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [navigation, setNavigation] = useState<NavigationItem[]>(defaultNavigation)
  const [navigationLoading, setNavigationLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return // 等待会话加载完成
    checkAdminAccess()
    loadNavigation()
  }, [session, status])

  // 动态加载导航配置
  const loadNavigation = async () => {
    try {
      setNavigationLoading(true)
      const response = await fetch('/api/admin/navigation')

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data.navigation) {
          setNavigation(data.data.navigation)
        }
      } else {
        console.warn('无法加载动态导航，使用默认配置')
      }
    } catch (error) {
      console.error('加载导航配置失败:', error)
      // 保持使用默认导航
    } finally {
      setNavigationLoading(false)
    }
  }

  // 渲染导航项
  const renderNavigationItem = (item: NavigationItem) => {
    const isActive = pathname === item.href
    const IconComponent = iconMap[item.icon as keyof typeof iconMap] || HomeIcon

    return (
      <Link
        key={item.id}
        href={item.href}
        className={`group flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
          isActive
            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
        }`}
        onClick={() => setSidebarOpen(false)}
      >
        <div className="flex items-center">
          <IconComponent className={`mr-3 h-5 w-5 ${isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'}`} />
          {item.name}
        </div>
        {item.badge && (
          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
            isActive
              ? 'bg-blue-100 text-blue-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {item.badge}
          </span>
        )}
      </Link>
    )
  }

  const checkAdminAccess = async () => {
    if (status === 'loading') return // 会话还在加载中

    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      // 检查管理员权限
      const checkResponse = await fetch('/api/admin/check')
      const checkData = await checkResponse.json()

      if (!checkResponse.ok) {
        if (checkResponse.status === 401) {
          router.push('/auth/signin')
          return
        } else if (checkResponse.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError(checkData.error || '权限验证失败')
        }
        setIsLoading(false)
        return
      }

      // 权限验证通过
      setIsLoading(false)
    } catch (error) {
      console.error('管理员权限验证错误:', error)
      setError('网络错误')
      setIsLoading(false)
    }
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">验证管理员权限...</p>
        </div>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-md">
            <h3 className="font-bold">访问被拒绝</h3>
            <p className="mt-2">{error}</p>
            <div className="mt-4">
              <Link
                href="/admin"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm"
              >
                返回管理后台
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 移动端侧边栏 */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-900">管理后台</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {navigationLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-500">加载导航...</span>
              </div>
            ) : (
              navigation.map((item) => renderNavigationItem(item))
            )}
          </nav>
        </div>
      </div>

      {/* 桌面端侧边栏 */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
          <div className="flex h-16 items-center px-6 border-b border-gray-200">
            <Link href="/admin" className="flex items-center space-x-2">
              <img
                src="/logo.jpg"
                alt="BitMarket Logo"
                className="w-8 h-8 rounded-lg object-cover"
              />
              <h1 className="text-lg font-bold text-gray-900">BitMarket 管理</h1>
            </Link>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {navigationLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-500">加载导航...</span>
              </div>
            ) : (
              navigation.map((item) => renderNavigationItem(item))
            )}
          </nav>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="lg:pl-64">
        {/* 顶部导航栏 */}
        <div className="sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-500 hover:text-gray-700"
              >
                <Bars3Icon className="h-6 w-6" />
              </button>
              <div className="ml-4 lg:ml-0">
                {title && (
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                    {subtitle && (
                      <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {actions}
              <button className="text-gray-400 hover:text-gray-600">
                <BellIcon className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-2">
                <UserCircleIcon className="h-8 w-8 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">{session?.user?.name}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 页面内容 */}
        <main className="flex-1">
          <div className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
