'use client'

import { useState, useEffect } from 'react'
import {
  PlusIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  ArrowPathIcon,
  ChartBarIcon,
  GiftIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface Batch {
  batchId: string
  totalCards: number
  totalValue: number
  createdAt: string
  createdBy: {
    id: string
    name: string
    email: string
  }
  faceValue: number
  validUntil: string
  notes: string
  statusStats: Record<string, number>
}

interface BatchFormData {
  faceValue: number
  quantity: number
  validDays: number
  batchName: string
  notes: string
}

export default function GiftCardBatchManager() {
  const [batches, setBatches] = useState<Batch[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [formData, setFormData] = useState<BatchFormData>({
    faceValue: 100,
    quantity: 10,
    validDays: 365,
    batchName: '',
    notes: ''
  })

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    fetchBatches()
  }, [pagination.page])

  const fetchBatches = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/giftcards/batch?page=${pagination.page}&limit=${pagination.limit}`)
      const result = await response.json()

      if (result.success) {
        setBatches(result.data.batches)
        setPagination(prev => ({
          ...prev,
          ...result.data.pagination
        }))
      } else {
        setError(result.error || '获取批次列表失败')
      }
    } catch (error) {
      console.error('获取批次列表失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateBatch = async () => {
    if (!formData.faceValue || !formData.quantity) {
      setError('请填写完整信息')
      return
    }

    try {
      setCreating(true)
      setError('')

      const response = await fetch('/api/admin/giftcards/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        setSuccess(result.message)
        setShowCreateModal(false)
        resetForm()
        fetchBatches()
      } else {
        setError(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建批次失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setCreating(false)
    }
  }

  const resetForm = () => {
    setFormData({
      faceValue: 100,
      quantity: 10,
      validDays: 365,
      batchName: '',
      notes: ''
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'GENERATED': return 'text-blue-600 bg-blue-100'
      case 'SOLD': return 'text-green-600 bg-green-100'
      case 'REDEEMED': return 'text-purple-600 bg-purple-100'
      case 'EXPIRED': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">礼品卡批次管理</h2>
          <p className="text-gray-600">批量生成和管理礼品卡</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>批量生成</span>
          </button>
          <button
            onClick={fetchBatches}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <ArrowPathIcon className="h-5 w-5" />
            <span>刷新</span>
          </button>
        </div>
      </div>

      {/* 错误和成功提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* 批次列表 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">批次列表</h3>
        </div>

        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">加载中...</p>
          </div>
        ) : batches.length === 0 ? (
          <div className="p-6 text-center">
            <GiftIcon className="h-12 w-12 text-gray-400 mx-auto" />
            <p className="mt-2 text-gray-500">暂无批次数据</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    批次信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    面值/数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态分布
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {batches.map((batch) => (
                  <tr key={batch.batchId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {batch.batchId}
                        </div>
                        <div className="text-sm text-gray-500">
                          总价值: {batch.totalValue} USDT
                        </div>
                        {batch.notes && (
                          <div className="text-xs text-gray-400 mt-1">
                            {batch.notes}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {batch.faceValue} USDT × {batch.totalCards}
                      </div>
                      <div className="text-sm text-gray-500">
                        有效期至: {formatDate(batch.validUntil)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        {Object.entries(batch.statusStats).map(([status, count]) => (
                          <div key={status} className="flex items-center space-x-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status)}`}>
                              {status}
                            </span>
                            <span className="text-sm text-gray-600">{count}</span>
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {batch.createdBy.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatDate(batch.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {/* TODO: 查看详情 */}}
                          className="text-blue-600 hover:text-blue-900"
                          title="查看详情"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {/* TODO: 导出 */}}
                          className="text-green-600 hover:text-green-900"
                          title="导出"
                        >
                          <DocumentArrowDownIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
            <div className="text-sm text-gray-700">
              显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
              共 {pagination.total} 条
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 创建批次模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">批量生成礼品卡</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">关闭</span>
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    面值 (USDT)
                  </label>
                  <select
                    value={formData.faceValue}
                    onChange={(e) => setFormData(prev => ({ ...prev, faceValue: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={10}>10 USDT</option>
                    <option value={20}>20 USDT</option>
                    <option value={50}>50 USDT</option>
                    <option value={100}>100 USDT</option>
                    <option value={200}>200 USDT</option>
                    <option value={500}>500 USDT</option>
                    <option value={1000}>1000 USDT</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    生成数量
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="1000"
                    value={formData.quantity}
                    onChange={(e) => setFormData(prev => ({ ...prev, quantity: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入生成数量"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    有效天数
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.validDays}
                    onChange={(e) => setFormData(prev => ({ ...prev, validDays: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入有效天数"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    批次名称
                  </label>
                  <input
                    type="text"
                    value={formData.batchName}
                    onChange={(e) => setFormData(prev => ({ ...prev, batchName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入批次名称（可选）"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    备注
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入备注信息（可选）"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleCreateBatch}
                  disabled={creating}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {creating ? '生成中...' : '生成'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
