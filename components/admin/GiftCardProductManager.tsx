"use client"

import React, { useState, useEffect } from 'react'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  EyeIcon,
  GiftIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface GiftCardProduct {
  id: string
  name: string
  description?: string
  productType: string
  faceValue: number
  salePrice: number
  stock: number
  isActive: boolean
  terms?: string
  validDays: number
  supportedPayments: string[]
  createdAt: string
  updatedAt: string
  createdBy: {
    id: string
    name: string
    email: string
  }
  _count: {
    giftCards: number
    orders: number
  }
}

interface GiftCardProductManagerProps {
  isAdmin: boolean
}

export default function GiftCardProductManager({ isAdmin }: GiftCardProductManagerProps) {
  const [products, setProducts] = useState<GiftCardProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<GiftCardProduct | null>(null)

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    productType: 'GIFT_CARD',
    faceValue: 0,
    salePrice: 0,
    stock: 0,
    isActive: true,
    terms: '',
    validDays: 365,
    supportedPayments: ['BALANCE']
  })

  useEffect(() => {
    if (isAdmin) {
      fetchProducts()
    }
  }, [isAdmin])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/giftcard-products')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setProducts(result.data)
        } else {
          setError(result.error || '获取商品列表失败')
        }
      } else {
        setError('获取商品列表失败')
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      setError('获取商品列表失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = async () => {
    try {
      const response = await fetch('/api/admin/giftcard-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()
      if (result.success) {
        setShowCreateModal(false)
        resetForm()
        fetchProducts()
        alert('商品创建成功')
      } else {
        alert(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建商品失败:', error)
      alert('创建失败')
    }
  }

  const handleEdit = async () => {
    if (!selectedProduct) return

    try {
      const response = await fetch(`/api/admin/giftcard-products/${selectedProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()
      if (result.success) {
        setShowEditModal(false)
        setSelectedProduct(null)
        resetForm()
        fetchProducts()
        alert('商品更新成功')
      } else {
        alert(result.error || '更新失败')
      }
    } catch (error) {
      console.error('更新商品失败:', error)
      alert('更新失败')
    }
  }

  const handleDelete = async (productId: string) => {
    if (!confirm('确定要删除这个商品吗？')) return

    try {
      const response = await fetch(`/api/admin/giftcard-products/${productId}`, {
        method: 'DELETE'
      })

      const result = await response.json()
      if (result.success) {
        fetchProducts()
        alert('商品删除成功')
      } else {
        alert(result.error || '删除失败')
      }
    } catch (error) {
      console.error('删除商品失败:', error)
      alert('删除失败')
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      productType: 'GIFT_CARD',
      faceValue: 0,
      salePrice: 0,
      stock: 0,
      isActive: true,
      terms: '',
      validDays: 365,
      supportedPayments: ['BALANCE']
    })
  }

  const openEditModal = (product: GiftCardProduct) => {
    setSelectedProduct(product)
    setFormData({
      name: product.name,
      description: product.description || '',
      productType: product.productType,
      faceValue: product.faceValue,
      salePrice: product.salePrice,
      stock: product.stock,
      isActive: product.isActive,
      terms: product.terms || '',
      validDays: product.validDays,
      supportedPayments: product.supportedPayments
    })
    setShowEditModal(true)
  }

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">您没有权限访问此功能</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">加载中...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">礼品卡商品管理</h2>
          <p className="text-sm text-gray-600">管理平台礼品卡商品和库存</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>新建商品</span>
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* 商品列表 */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商品信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  价格
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  库存
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  统计
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                          <GiftIcon className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.description}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      面值: {product.faceValue} USDT
                    </div>
                    <div className="text-sm text-gray-500">
                      售价: {product.salePrice} USDT
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{product.stock}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      product.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.isActive ? '启用' : '禁用'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>卡片: {product._count.giftCards}</div>
                    <div>订单: {product._count.orders}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => openEditModal(product)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(product.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 创建商品模态框 */}
      {showCreateModal && (
        <ProductModal
          title="创建礼品卡商品"
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleCreate}
          onCancel={() => {
            setShowCreateModal(false)
            resetForm()
          }}
        />
      )}

      {/* 编辑商品模态框 */}
      {showEditModal && (
        <ProductModal
          title="编辑礼品卡商品"
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleEdit}
          onCancel={() => {
            setShowEditModal(false)
            setSelectedProduct(null)
            resetForm()
          }}
        />
      )}
    </div>
  )
}

// 商品表单模态框组件
interface ProductModalProps {
  title: string
  formData: any
  setFormData: (data: any) => void
  onSubmit: () => void
  onCancel: () => void
}

function ProductModal({ title, formData, setFormData, onSubmit, onCancel }: ProductModalProps) {
  const paymentOptions = [
    { value: 'BALANCE', label: '余额支付' },
    { value: 'BINANCE_PAY', label: '币安支付' },
    { value: 'CRYPTO', label: '加密货币' }
  ]

  const handlePaymentChange = (payment: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        supportedPayments: [...formData.supportedPayments, payment]
      })
    } else {
      setFormData({
        ...formData,
        supportedPayments: formData.supportedPayments.filter((p: string) => p !== payment)
      })
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">{title}</h3>

        <div className="space-y-4">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">商品名称 *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入商品名称"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">商品类型 *</label>
              <select
                value={formData.productType}
                onChange={(e) => setFormData({ ...formData, productType: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="GIFT_CARD">礼品卡</option>
                <option value="RECHARGE_CARD">充值卡</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">商品描述</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入商品描述"
            />
          </div>

          {/* 价格信息 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">面值 (USDT) *</label>
              <input
                type="number"
                value={formData.faceValue}
                onChange={(e) => setFormData({ ...formData, faceValue: parseFloat(e.target.value) || 0 })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">售价 (USDT) *</label>
              <input
                type="number"
                value={formData.salePrice}
                onChange={(e) => setFormData({ ...formData, salePrice: parseFloat(e.target.value) || 0 })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">库存数量 *</label>
              <input
                type="number"
                value={formData.stock}
                onChange={(e) => setFormData({ ...formData, stock: parseInt(e.target.value) || 0 })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          {/* 其他设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">有效期 (天)</label>
              <input
                type="number"
                value={formData.validDays}
                onChange={(e) => setFormData({ ...formData, validDays: parseInt(e.target.value) || 365 })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="365"
                min="1"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                启用商品
              </label>
            </div>
          </div>

          {/* 支付方式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">支持的支付方式</label>
            <div className="space-y-2">
              {paymentOptions.map((option) => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.supportedPayments.includes(option.value)}
                    onChange={(e) => handlePaymentChange(option.value, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-900">{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 使用条款 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">使用条款</label>
            <textarea
              value={formData.terms}
              onChange={(e) => setFormData({ ...formData, terms: e.target.value })}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入使用条款和注意事项"
            />
          </div>
        </div>

        {/* 按钮 */}
        <div className="flex space-x-3 mt-6">
          <button
            onClick={onCancel}
            className="flex-1 py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={onSubmit}
            className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            确认
          </button>
        </div>
      </div>
    </div>
  )
}
