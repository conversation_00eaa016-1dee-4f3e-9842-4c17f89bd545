import { useState } from 'react'
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface FilterOption {
  key: string
  label: string
  type: 'select' | 'date' | 'dateRange'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

interface SearchAndFilterProps {
  searchValue: string
  onSearchChange: (value: string) => void
  searchPlaceholder?: string
  filters?: FilterOption[]
  filterValues?: Record<string, any>
  onFilterChange?: (key: string, value: any) => void
  onClearFilters?: () => void
  className?: string
}

export default function SearchAndFilter({
  searchValue,
  onSearchChange,
  searchPlaceholder = '搜索...',
  filters = [],
  filterValues = {},
  onFilterChange,
  onClearFilters,
  className = ''
}: SearchAndFilterProps) {
  const [showFilters, setShowFilters] = useState(false)

  const hasActiveFilters = Object.values(filterValues).some(value => 
    value !== '' && value !== null && value !== undefined
  )

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-4">
        {/* 搜索框 */}
        <div className="flex-1 max-w-lg">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder={searchPlaceholder}
            />
          </div>
        </div>

        {/* 筛选按钮 */}
        {filters.length > 0 && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                hasActiveFilters ? 'bg-blue-50 border-blue-300 text-blue-700' : ''
              }`}
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              筛选
              {hasActiveFilters && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {Object.values(filterValues).filter(v => v !== '' && v !== null && v !== undefined).length}
                </span>
              )}
            </button>
            
            {hasActiveFilters && onClearFilters && (
              <button
                onClick={onClearFilters}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <XMarkIcon className="h-4 w-4 mr-2" />
                清除筛选
              </button>
            )}
          </div>
        )}
      </div>

      {/* 筛选面板 */}
      {showFilters && filters.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filters.map((filter) => (
              <div key={filter.key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {filter.label}
                </label>
                
                {filter.type === 'select' && (
                  <select
                    value={filterValues[filter.key] || ''}
                    onChange={(e) => onFilterChange?.(filter.key, e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">{filter.placeholder || '全部'}</option>
                    {filter.options?.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}
                
                {filter.type === 'date' && (
                  <input
                    type="date"
                    value={filterValues[filter.key] || ''}
                    onChange={(e) => onFilterChange?.(filter.key, e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                )}
                
                {filter.type === 'dateRange' && (
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={filterValues[`${filter.key}_start`] || ''}
                      onChange={(e) => onFilterChange?.(`${filter.key}_start`, e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="开始日期"
                    />
                    <input
                      type="date"
                      value={filterValues[`${filter.key}_end`] || ''}
                      onChange={(e) => onFilterChange?.(`${filter.key}_end`, e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="结束日期"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
