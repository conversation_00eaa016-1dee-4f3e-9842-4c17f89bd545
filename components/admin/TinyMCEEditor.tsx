'use client'

import { useRef, useState } from 'react'
import { Editor } from '@tinymce/tinymce-react'

import MediaLibraryModal from './MediaLibraryModal'

interface TinyMCEEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  articleId?: string | null
  height?: number
  className?: string
}

export default function TinyMCEEditor({
  value,
  onChange,
  placeholder = '请输入内容...',
  articleId = null,
  height = 400,
  className = ''
}: TinyMCEEditorProps) {
  const editorRef = useRef<any>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [showMediaLibrary, setShowMediaLibrary] = useState(false)

  // 自定义图片上传处理器
  const handleImageUpload = (blobInfo: any, progress: (percent: number) => void): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      try {
        setIsUploading(true)
        
        const formData = new FormData()
        formData.append('file', blobInfo.blob(), blobInfo.filename())
        if (articleId) {
          formData.append('articleId', articleId)
        }

        const xhr = new XMLHttpRequest()
        
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const percent = Math.round((e.loaded / e.total) * 100)
            progress(percent)
          }
        })

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText)
            resolve(response.file.url)
          } else {
            const error = JSON.parse(xhr.responseText)
            reject(error.error || '上传失败')
          }
          setIsUploading(false)
        })

        xhr.addEventListener('error', () => {
          reject('网络错误')
          setIsUploading(false)
        })

        xhr.open('POST', '/api/admin/help/media')
        xhr.send(formData)
      } catch (error) {
        reject(error)
        setIsUploading(false)
      }
    })
  }

  // 自定义视频插入功能
  const insertVideo = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'video/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        setIsUploading(true)
        
        const formData = new FormData()
        formData.append('file', file)
        if (articleId) {
          formData.append('articleId', articleId)
        }

        const response = await fetch('/api/admin/help/media', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const data = await response.json()
          const videoHtml = `<video controls style="max-width: 100%; height: auto;">
            <source src="${data.file.url}" type="${data.file.mimeType}">
            您的浏览器不支持视频播放。
          </video>`
          
          editorRef.current?.insertContent(videoHtml)
        } else {
          const error = await response.json()
          alert(error.error || '上传失败')
        }
      } catch (error) {
        console.error('Video upload error:', error)
        alert('上传失败，请重试')
      } finally {
        setIsUploading(false)
      }
    }
    input.click()
  }

  // 打开媒体库
  const openMediaLibrary = () => {
    setShowMediaLibrary(true)
  }

  // 处理媒体选择
  const handleMediaSelect = (mediaFile: any) => {
    let mediaHtml = ''

    if (mediaFile.mediaType === 'image') {
      mediaHtml = `<img src="${mediaFile.fileUrl}" alt="${mediaFile.filename}" style="max-width: 100%; height: auto;" />`
    } else if (mediaFile.mediaType === 'video') {
      mediaHtml = `<video controls style="max-width: 100%; height: auto;">
        <source src="${mediaFile.fileUrl}" type="${mediaFile.mimeType}">
        您的浏览器不支持视频播放。
      </video>`
    }

    editorRef.current?.insertContent(mediaHtml)
    setShowMediaLibrary(false)
  }

  const editorConfig = {
    height,
    menubar: false,
    plugins: [
      'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
      'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
      'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
    ],
    toolbar: [
      'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify',
      'bullist numlist outdent indent | removeformat | help | customimage customvideo customlibrary'
    ].join(' | '),
    content_style: `
      body { 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
        font-size: 14px; 
        line-height: 1.6;
      }
      img { max-width: 100%; height: auto; }
      video { max-width: 100%; height: auto; }
    `,
    paste_data_images: true,
    images_upload_handler: handleImageUpload,
    automatic_uploads: true,
    file_picker_types: 'image',
    setup: (editor: any) => {
      // 自定义图片按钮
      editor.ui.registry.addButton('customimage', {
        icon: 'image',
        tooltip: '插入图片',
        onAction: () => {
          const input = document.createElement('input')
          input.type = 'file'
          input.accept = 'image/*'
          input.multiple = true
          input.onchange = (e) => {
            const files = (e.target as HTMLInputElement).files
            if (files) {
              Array.from(files).forEach(file => {
                const reader = new FileReader()
                reader.onload = () => {
                  const id = 'blobid' + (new Date()).getTime()
                  const blobCache = editor.editorUpload.blobCache
                  const base64 = (reader.result as string).split(',')[1]
                  const blobInfo = blobCache.create(id, file, base64)
                  blobCache.add(blobInfo)
                  
                  editor.insertContent(`<img src="${blobInfo.blobUri()}" alt="${file.name}" style="max-width: 100%; height: auto;" />`)
                  editor.editorUpload.uploadImages()
                }
                reader.readAsDataURL(file)
              })
            }
          }
          input.click()
        }
      })

      // 自定义视频按钮
      editor.ui.registry.addButton('customvideo', {
        icon: 'embed',
        tooltip: '插入视频',
        onAction: insertVideo
      })

      // 自定义媒体库按钮
      editor.ui.registry.addButton('customlibrary', {
        icon: 'browse',
        tooltip: '媒体库',
        onAction: openMediaLibrary
      })
    },
    placeholder
  }

  return (
    <div className={className}>
      {isUploading && (
        <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
          正在上传文件，请稍候...
        </div>
      )}
      <Editor
        onInit={(evt, editor) => editorRef.current = editor}
        value={value}
        onEditorChange={onChange}
        init={editorConfig}
      />

      {showMediaLibrary && (
        <MediaLibraryModal
          articleId={articleId}
          onSelect={handleMediaSelect}
          onClose={() => setShowMediaLibrary(false)}
        />
      )}
    </div>
  )
}
