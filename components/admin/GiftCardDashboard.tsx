'use client'

import { useState, useEffect } from 'react'
import {
  CreditCardIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  GiftIcon,
  ShoppingCartIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface StatsData {
  overview: {
    totalCards: number
    totalValue: number
    avgFaceValue: number
    conversionRate: number
  }
  statusDistribution: Array<{
    status: string
    count: number
    value: number
    percentage: number
  }>
  faceValueDistribution: Array<{
    faceValue: number
    count: number
    totalValue: number
    percentage: number
  }>
  trends: {
    dailySales: Array<{
      date: string
      count: number
      value: number
    }>
    dailyRedemptions: Array<{
      date: string
      count: number
      value: number
    }>
  }
  recentActivity: Array<{
    id: string
    cardCode: string
    faceValue: number
    status: string
    createdAt: string
    soldAt?: string
    redeemedAt?: string
    soldTo?: { name: string; email: string }
    redeemedBy?: { name: string; email: string }
  }>
}

export default function GiftCardDashboard() {
  const [stats, setStats] = useState<StatsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [period, setPeriod] = useState('30')

  useEffect(() => {
    fetchStats()
  }, [period])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/giftcards/stats?period=${period}`)
      const result = await response.json()

      if (result.success) {
        setStats(result.data)
      } else {
        setError(result.error || '获取统计数据失败')
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'GENERATED': return <GiftIcon className="h-5 w-5" />
      case 'SOLD': return <ShoppingCartIcon className="h-5 w-5" />
      case 'REDEEMED': return <CheckCircleIcon className="h-5 w-5" />
      case 'EXPIRED': return <ClockIcon className="h-5 w-5" />
      default: return <CreditCardIcon className="h-5 w-5" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'GENERATED': return 'text-blue-600 bg-blue-100'
      case 'SOLD': return 'text-green-600 bg-green-100'
      case 'REDEEMED': return 'text-purple-600 bg-purple-100'
      case 'EXPIRED': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-500">加载统计数据中...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <div className="text-red-600 mb-2">获取数据失败</div>
        <p className="text-gray-500">{error}</p>
        <button
          onClick={fetchStats}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    )
  }

  if (!stats) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">礼品卡统计</h2>
          <p className="text-gray-600">礼品卡系统数据概览</p>
        </div>
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-700">统计周期:</label>
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="7">最近7天</option>
            <option value="30">最近30天</option>
            <option value="90">最近90天</option>
            <option value="365">最近一年</option>
          </select>
        </div>
      </div>

      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCardIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总卡数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.overview.totalCards}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总价值</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.overview.totalValue} USDT</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">平均面值</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.overview.avgFaceValue} USDT</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <ArrowTrendingUpIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">兑换率</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.overview.conversionRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* 状态分布和面值分布 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 状态分布 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">状态分布</h3>
          <div className="space-y-3">
            {stats.statusDistribution.map((item) => (
              <div key={item.status} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${getStatusColor(item.status)}`}>
                    {getStatusIcon(item.status)}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{item.status}</p>
                    <p className="text-xs text-gray-500">{item.value} USDT</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{item.count}</p>
                  <p className="text-xs text-gray-500">{item.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 面值分布 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">面值分布</h3>
          <div className="space-y-3">
            {stats.faceValueDistribution.map((item) => (
              <div key={item.faceValue} className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">{item.faceValue} USDT</p>
                  <p className="text-xs text-gray-500">总价值: {item.totalValue} USDT</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{item.count} 张</p>
                  <p className="text-xs text-gray-500">{item.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 趋势图表 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">销售和兑换趋势</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 销售趋势 */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">销售趋势</h4>
            <div className="space-y-2">
              {stats.trends.dailySales.slice(-7).map((item) => (
                <div key={item.date} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{item.date}</span>
                  <div className="text-right">
                    <span className="font-medium">{item.count} 张</span>
                    <span className="text-gray-500 ml-2">{item.value} USDT</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 兑换趋势 */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">兑换趋势</h4>
            <div className="space-y-2">
              {stats.trends.dailyRedemptions.slice(-7).map((item) => (
                <div key={item.date} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{item.date}</span>
                  <div className="text-right">
                    <span className="font-medium">{item.count} 张</span>
                    <span className="text-gray-500 ml-2">{item.value} USDT</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 最近活动 */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">最近活动</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  卡号
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  面值
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用户
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {stats.recentActivity.slice(0, 10).map((activity) => (
                <tr key={activity.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {activity.cardCode}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {activity.faceValue} USDT
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(activity.status)}`}>
                      {activity.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {activity.soldTo?.name || activity.redeemedBy?.name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(activity.soldAt || activity.redeemedAt || activity.createdAt)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
