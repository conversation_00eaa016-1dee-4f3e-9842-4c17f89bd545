'use client'

import { useState, useEffect } from 'react'
import { 
  XMarkIcon,
  PhotoIcon,
  VideoCameraIcon,
  MagnifyingGlassIcon,
  CloudArrowUpIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

interface MediaFile {
  id: string
  filename: string
  storedName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  mediaType: 'image' | 'video'
  metadata?: any
  isUsed: boolean
  createdAt: string
  uploader: {
    id: string
    name: string | null
    email: string | null
  }
  article?: {
    id: string
    title: string
  } | null
}

interface MediaLibraryModalProps {
  articleId?: string | null
  onSelect: (file: MediaFile) => void
  onClose: () => void
}

export default function MediaLibraryModal({
  articleId,
  onSelect,
  onClose
}: MediaLibraryModalProps) {
  const [files, setFiles] = useState<MediaFile[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState<'all' | 'image' | 'video'>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    fetchFiles()
  }, [currentPage, typeFilter, searchQuery])

  const fetchFiles = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (typeFilter !== 'all') {
        params.append('type', typeFilter)
      }

      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim())
      }

      const response = await fetch(`/api/admin/help/media?${params}`)
      if (response.ok) {
        const data = await response.json()
        setFiles(data.files || [])
        setTotalPages(data.pagination?.pages || 1)
      }
    } catch (error) {
      console.error('Error fetching files:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (uploadFiles: FileList) => {
    setUploading(true)
    
    try {
      for (let i = 0; i < uploadFiles.length; i++) {
        const file = uploadFiles[i]
        const formData = new FormData()
        formData.append('file', file)
        if (articleId) {
          formData.append('articleId', articleId)
        }

        const response = await fetch('/api/admin/help/media', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const data = await response.json()
          setFiles(prev => [data.file, ...prev])
        } else {
          const error = await response.json()
          alert(error.error || '上传失败')
        }
      }
    } catch (error) {
      console.error('Upload error:', error)
      alert('上传失败，请重试')
    } finally {
      setUploading(false)
    }
  }

  const handleDelete = async (file: MediaFile) => {
    if (!confirm(`确定要删除 "${file.filename}" 吗？`)) return

    try {
      const response = await fetch(`/api/admin/help/media/${file.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setFiles(prev => prev.filter(f => f.id !== file.id))
      } else {
        const error = await response.json()
        alert(error.error || '删除失败')
      }
    } catch (error) {
      console.error('Delete error:', error)
      alert('删除失败，请重试')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">媒体库</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 工具栏 */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* 上传按钮 */}
              <label className="cursor-pointer">
                <input
                  type="file"
                  multiple
                  accept="image/*,video/*"
                  onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                  className="hidden"
                  disabled={uploading}
                />
                <div className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                  <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                  <span className="text-sm">上传文件</span>
                </div>
              </label>

              {/* 类型筛选 */}
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as 'all' | 'image' | 'video')}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">全部类型</option>
                <option value="image">图片</option>
                <option value="video">视频</option>
              </select>
            </div>

            {/* 搜索框 */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文件名..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {uploading && (
            <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
              正在上传文件，请稍候...
            </div>
          )}
        </div>

        {/* 文件列表 */}
        <div className="flex-1 overflow-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : files.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <PhotoIcon className="h-16 w-16 mb-4" />
              <p className="text-lg font-medium">暂无媒体文件</p>
              <p className="text-sm">点击上传按钮添加图片或视频</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="group relative bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onSelect(file)}
                >
                  {/* 预览区域 */}
                  <div className="aspect-square bg-gray-100 flex items-center justify-center">
                    {file.mediaType === 'image' ? (
                      <img
                        src={file.fileUrl}
                        alt={file.filename}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex flex-col items-center text-gray-400">
                        <VideoCameraIcon className="h-8 w-8 mb-1" />
                        <span className="text-xs">视频</span>
                      </div>
                    )}
                  </div>

                  {/* 文件信息 */}
                  <div className="p-2">
                    <p className="text-xs font-medium text-gray-900 truncate" title={file.filename}>
                      {file.filename}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.fileSize)}
                    </p>
                  </div>

                  {/* 操作按钮 */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDelete(file)
                      }}
                      className="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </button>
                  </div>

                  {/* 使用状态指示器 */}
                  {file.isUsed && (
                    <div className="absolute top-2 left-2">
                      <span className="bg-green-500 text-white text-xs px-1 py-0.5 rounded">
                        已使用
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="p-4 border-t bg-gray-50">
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage <= 1}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              <span className="text-sm text-gray-600">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage >= totalPages}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
