'use client'

import { useRef, useEffect, useState } from 'react'
import {
  PhotoIcon,
  VideoCameraIcon,
  XMarkIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

interface MediaFile {
  id: string
  url: string
  filename: string
  size: number
  type: 'image' | 'video'
  mimeType: string
  metadata?: any
}

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  articleId?: string | null
  className?: string
  height?: number
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = '请输入内容...',
  articleId = null,
  className = '',
  height = 400
}: RichTextEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [showMediaLibrary, setShowMediaLibrary] = useState(false)
  const [dragOver, setDragOver] = useState(false)

  // 加载已有的媒体文件
  useEffect(() => {
    if (articleId) {
      fetchMediaFiles()
    }
  }, [articleId])

  const fetchMediaFiles = async () => {
    try {
      const response = await fetch(`/api/admin/help/media?articleId=${articleId}`)
      if (response.ok) {
        const data = await response.json()
        setMediaFiles(data.files || [])
      }
    } catch (error) {
      console.error('Error fetching media files:', error)
    }
  }

  // 处理文件上传
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    setIsUploading(true)
    setUploadProgress(0)

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const formData = new FormData()
        formData.append('file', file)
        if (articleId) {
          formData.append('articleId', articleId)
        }

        const response = await fetch('/api/admin/help/media', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const data = await response.json()
          const newFile: MediaFile = data.file
          setMediaFiles(prev => [...prev, newFile])
          
          // 自动插入到编辑器中
          insertMediaToEditor(newFile)
        } else {
          const error = await response.json()
          alert(error.error || '上传失败')
        }

        setUploadProgress(((i + 1) / files.length) * 100)
      }
    } catch (error) {
      console.error('Upload error:', error)
      alert('上传失败，请重试')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  // 插入媒体到编辑器
  const insertMediaToEditor = (mediaFile: MediaFile) => {
    const textarea = textareaRef.current
    if (!textarea) return

    let mediaHtml = ''
    if (mediaFile.type === 'image') {
      mediaHtml = `\n<img src="${mediaFile.url}" alt="${mediaFile.filename}" style="max-width: 100%; height: auto;" />\n`
    } else if (mediaFile.type === 'video') {
      mediaHtml = `\n<video controls style="max-width: 100%; height: auto;">
  <source src="${mediaFile.url}" type="${mediaFile.mimeType}">
  您的浏览器不支持视频播放。
</video>\n`
    }

    const cursorPosition = textarea.selectionStart
    const textBefore = value.substring(0, cursorPosition)
    const textAfter = value.substring(cursorPosition)
    const newValue = textBefore + mediaHtml + textAfter

    onChange(newValue)

    // 更新光标位置
    setTimeout(() => {
      const newCursorPosition = cursorPosition + mediaHtml.length
      textarea.setSelectionRange(newCursorPosition, newCursorPosition)
      textarea.focus()
    }, 0)
  }

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    const files = e.dataTransfer.files
    handleFileUpload(files)
  }

  // 删除媒体文件
  const handleDeleteMedia = async (mediaFile: MediaFile) => {
    if (!confirm(`确定要删除 "${mediaFile.filename}" 吗？`)) return

    try {
      const response = await fetch(`/api/admin/help/media/${mediaFile.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setMediaFiles(prev => prev.filter(f => f.id !== mediaFile.id))
        
        // 从编辑器内容中移除相关的媒体标签
        const updatedContent = value.replace(
          new RegExp(`<(img|video)[^>]*src="${mediaFile.url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>.*?(<\/video>)?`, 'gi'),
          ''
        )
        onChange(updatedContent)
      } else {
        const error = await response.json()
        alert(error.error || '删除失败')
      }
    } catch (error) {
      console.error('Delete error:', error)
      alert('删除失败，请重试')
    }
  }

  return (
    <div className={`border border-gray-300 rounded-md ${className}`}>
      {/* 工具栏 */}
      <div className="border-b border-gray-200 p-3 bg-gray-50 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <label className="cursor-pointer">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
              disabled={isUploading}
            />
            <div className="flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
              <PhotoIcon className="h-4 w-4 mr-1" />
              <span className="text-sm">插入图片</span>
            </div>
          </label>

          <label className="cursor-pointer">
            <input
              type="file"
              multiple
              accept="video/*"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
              disabled={isUploading}
            />
            <div className="flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors">
              <VideoCameraIcon className="h-4 w-4 mr-1" />
              <span className="text-sm">插入视频</span>
            </div>
          </label>

          {mediaFiles.length > 0 && (
            <button
              onClick={() => setShowMediaLibrary(!showMediaLibrary)}
              className="flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              <EyeIcon className="h-4 w-4 mr-1" />
              <span className="text-sm">媒体库 ({mediaFiles.length})</span>
            </button>
          )}
        </div>

        {isUploading && (
          <div className="flex items-center space-x-2">
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            <span className="text-sm text-gray-600">{Math.round(uploadProgress)}%</span>
          </div>
        )}
      </div>

      {/* 媒体库 */}
      {showMediaLibrary && (
        <div className="border-b border-gray-200 p-3 bg-gray-50">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900">媒体库</h4>
            <button
              onClick={() => setShowMediaLibrary(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          <div className="grid grid-cols-4 gap-2 max-h-32 overflow-y-auto">
            {mediaFiles.map((file) => (
              <div key={file.id} className="relative group">
                <div 
                  className="aspect-square bg-gray-100 rounded border cursor-pointer hover:bg-gray-200 transition-colors flex items-center justify-center"
                  onClick={() => insertMediaToEditor(file)}
                >
                  {file.type === 'image' ? (
                    <img 
                      src={file.url} 
                      alt={file.filename}
                      className="w-full h-full object-cover rounded"
                    />
                  ) : (
                    <VideoCameraIcon className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDeleteMedia(file)
                  }}
                  className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
                <div className="text-xs text-gray-600 mt-1 truncate" title={file.filename}>
                  {file.filename}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 编辑器 */}
      <div 
        className={`relative ${dragOver ? 'bg-blue-50 border-blue-300' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="w-full p-3 border-0 resize-none focus:outline-none"
          style={{ height: `${height}px` }}
        />
        
        {dragOver && (
          <div className="absolute inset-0 flex items-center justify-center bg-blue-50 bg-opacity-90 border-2 border-dashed border-blue-300">
            <div className="text-center">
              <PhotoIcon className="h-12 w-12 text-blue-400 mx-auto mb-2" />
              <p className="text-blue-600 font-medium">拖拽文件到此处上传</p>
              <p className="text-blue-500 text-sm">支持图片和视频文件</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
