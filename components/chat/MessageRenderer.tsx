'use client'

import { useState } from 'react'
import Image from 'next/image'
import { PlayIcon, PauseIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline'

interface Message {
  id: string
  content: string
  messageType: string
  status: string
  createdAt: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  fileMimeType?: string
  fileMetadata?: any
  sender: {
    id: string
    name: string | null
    email: string | null
  }
  receiver: {
    id: string
    name: string | null
    email: string | null
  }
}

interface MessageRendererProps {
  message: Message
  isOwnMessage: boolean
  formatTime: (dateString: string) => string
}

export default function MessageRenderer({ message, isOwnMessage, formatTime }: MessageRendererProps) {
  const [imageError, setImageError] = useState(false)
  const [videoError, setVideoError] = useState(false)
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = () => {
    if (message.fileUrl) {
      const link = document.createElement('a')
      link.href = message.fileUrl
      link.download = message.fileName || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const renderTextMessage = () => (
    <div className="break-words">{message.content}</div>
  )

  const renderImageMessage = () => (
    <div className="space-y-2">
      {message.content && (
        <div className="break-words">{message.content}</div>
      )}
      <div className="relative">
        {!imageError ? (
          <div 
            className="cursor-pointer"
            onClick={() => setIsImageModalOpen(true)}
          >
            <Image
              src={message.fileUrl!}
              alt={message.fileName || 'Image'}
              width={300}
              height={200}
              className="rounded-lg max-w-full h-auto"
              style={{ maxHeight: '300px', objectFit: 'cover' }}
              onError={() => setImageError(true)}
            />
          </div>
        ) : (
          <div className="bg-gray-100 rounded-lg p-4 text-center">
            <div className="text-gray-500 text-sm">图片加载失败</div>
            <button
              onClick={handleDownload}
              className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
            >
              下载文件
            </button>
          </div>
        )}
        
        {/* 文件信息 */}
        <div className="mt-1 text-xs opacity-75">
          {message.fileName} • {message.fileSize && formatFileSize(message.fileSize)}
        </div>
      </div>
    </div>
  )

  const renderVideoMessage = () => (
    <div className="space-y-2">
      {message.content && (
        <div className="break-words">{message.content}</div>
      )}
      <div className="relative">
        {!videoError ? (
          <video
            controls
            className="rounded-lg max-w-full"
            style={{ maxHeight: '300px', maxWidth: '400px' }}
            onError={() => setVideoError(true)}
            preload="metadata"
          >
            <source src={message.fileUrl!} type={message.fileMimeType} />
            您的浏览器不支持视频播放
          </video>
        ) : (
          <div className="bg-gray-100 rounded-lg p-4 text-center">
            <div className="text-gray-500 text-sm">视频加载失败</div>
            <button
              onClick={handleDownload}
              className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
            >
              下载文件
            </button>
          </div>
        )}
        
        {/* 文件信息 */}
        <div className="mt-1 text-xs opacity-75">
          {message.fileName} • {message.fileSize && formatFileSize(message.fileSize)}
          {message.fileMetadata?.duration && (
            <span> • {Math.round(message.fileMetadata.duration)}秒</span>
          )}
        </div>
      </div>
    </div>
  )

  const renderFileMessage = () => (
    <div className="space-y-2">
      {message.content && (
        <div className="break-words">{message.content}</div>
      )}
      <div className="bg-gray-100 rounded-lg p-3 flex items-center space-x-3">
        <div className="flex-shrink-0">
          <ArrowDownTrayIcon className="w-6 h-6 text-gray-500" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {message.fileName}
          </div>
          <div className="text-xs text-gray-500">
            {message.fileSize && formatFileSize(message.fileSize)}
          </div>
        </div>
        <button
          onClick={handleDownload}
          className="flex-shrink-0 text-blue-600 hover:text-blue-800 text-sm"
        >
          下载
        </button>
      </div>
    </div>
  )

  const renderSystemMessage = () => (
    <div className="flex justify-center">
      <div className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">
        {message.content}
        <span className="ml-2 text-xs text-gray-400">
          {formatTime(message.createdAt)}
        </span>
      </div>
    </div>
  )

  // 系统消息特殊处理
  if (message.messageType === 'SYSTEM') {
    return renderSystemMessage()
  }

  return (
    <>
      <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
        <div
          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
            isOwnMessage
              ? 'bg-blue-600 text-white'
              : 'bg-white text-gray-900 border'
          }`}
        >
          {/* 渲染不同类型的消息内容 */}
          {message.messageType === 'TEXT' && renderTextMessage()}
          {message.messageType === 'IMAGE' && renderImageMessage()}
          {message.messageType === 'VIDEO' && renderVideoMessage()}
          {message.messageType === 'FILE' && renderFileMessage()}
          
          {/* 时间和状态 */}
          <div
            className={`text-xs mt-1 ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}
          >
            {formatTime(message.createdAt)}
            {isOwnMessage && (
              <span className="ml-2">
                {message.status === 'read' && '已读'}
                {message.status === 'DELIVERED' && '已送达'}
                {message.status === 'SENT' && '已发送'}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 图片预览模态框 */}
      {isImageModalOpen && message.messageType === 'IMAGE' && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setIsImageModalOpen(false)}
        >
          <div className="relative max-w-4xl max-h-full p-4">
            <Image
              src={message.fileUrl!}
              alt={message.fileName || 'Image'}
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setIsImageModalOpen(false)}
              className="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  )
}
