import React from 'react'

interface TypingIndicatorProps {
  isTyping: boolean
  userName?: string
}

export default function TypingIndicator({ isTyping, userName = '对方' }: TypingIndicatorProps) {
  if (!isTyping) return null

  return (
    <div className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-500">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
      <span>{userName} 正在输入...</span>
    </div>
  )
}
