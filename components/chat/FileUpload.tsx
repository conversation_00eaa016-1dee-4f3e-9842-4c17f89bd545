'use client'

import { useState, useRef } from 'react'
import { PhotoIcon, VideoCameraIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface FileUploadProps {
  orderId: string
  onFileUploaded: (fileData: any) => void
  disabled?: boolean
}

interface UploadProgress {
  file: File
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
  result?: any
}

export default function FileUpload({ orderId, onFileUploaded, disabled }: FileUploadProps) {
  const [uploads, setUploads] = useState<UploadProgress[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 支持的文件类型
  const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/mov', 'video/avi', 'video/webm', 'video/quicktime']
  const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES]

  // 文件大小限制
  const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
  const MAX_VIDEO_DURATION = 5 * 60 // 5分钟

  const validateFile = (file: File): string | null => {
    // 检查文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      return '不支持的文件类型。支持的格式：JPG、PNG、GIF、WebP、MP4、MOV、AVI、WebM'
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      return `文件大小不能超过 ${MAX_FILE_SIZE / (1024 * 1024)}MB`
    }

    return null
  }

  const uploadFile = async (file: File) => {
    const uploadId = Date.now().toString()
    
    // 添加到上传列表
    const newUpload: UploadProgress = {
      file,
      progress: 0,
      status: 'uploading'
    }
    
    setUploads(prev => [...prev, newUpload])

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('orderId', orderId)

      const xhr = new XMLHttpRequest()

      // 监听上传进度
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 100)
          setUploads(prev => prev.map(upload => 
            upload.file === file ? { ...upload, progress } : upload
          ))
        }
      })

      // 处理上传完成
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          const result = JSON.parse(xhr.responseText)
          setUploads(prev => prev.map(upload => 
            upload.file === file 
              ? { ...upload, status: 'success', progress: 100, result }
              : upload
          ))
          
          // 通知父组件文件上传成功
          onFileUploaded(result)
          
          // 3秒后移除成功的上传项
          setTimeout(() => {
            setUploads(prev => prev.filter(upload => upload.file !== file))
          }, 3000)
        } else {
          const error = JSON.parse(xhr.responseText)
          setUploads(prev => prev.map(upload => 
            upload.file === file 
              ? { ...upload, status: 'error', error: error.error || '上传失败' }
              : upload
          ))
        }
      })

      // 处理上传错误
      xhr.addEventListener('error', () => {
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { ...upload, status: 'error', error: '网络错误' }
            : upload
        ))
      })

      xhr.open('POST', '/api/chat/upload')
      xhr.send(formData)

    } catch (error) {
      setUploads(prev => prev.map(upload => 
        upload.file === file 
          ? { ...upload, status: 'error', error: '上传失败' }
          : upload
      ))
    }
  }

  const handleFileSelect = (files: FileList | null) => {
    if (!files || disabled) return

    Array.from(files).forEach(file => {
      const error = validateFile(file)
      if (error) {
        alert(error)
        return
      }
      uploadFile(file)
    })
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const removeUpload = (file: File) => {
    setUploads(prev => prev.filter(upload => upload.file !== file))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-2">
      {/* 上传按钮 */}
      <div className="flex space-x-2">
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled}
          className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PhotoIcon className="w-4 h-4" />
          <span>图片</span>
        </button>
        
        <button
          type="button"
          onClick={() => {
            if (fileInputRef.current) {
              fileInputRef.current.accept = 'video/*'
              fileInputRef.current.click()
            }
          }}
          disabled={disabled}
          className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <VideoCameraIcon className="w-4 h-4" />
          <span>视频</span>
        </button>
      </div>

      {/* 拖拽上传区域 */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
          isDragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <div className="text-sm text-gray-600">
          拖拽文件到此处或点击上传
          <br />
          <span className="text-xs text-gray-500">
            支持图片和视频，最大50MB，视频限制5分钟
          </span>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,video/*"
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />

      {/* 上传进度列表 */}
      {uploads.length > 0 && (
        <div className="space-y-2">
          {uploads.map((upload, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {upload.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(upload.file.size)}
                  </p>
                </div>
                <button
                  onClick={() => removeUpload(upload.file)}
                  className="ml-2 text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
              
              {upload.status === 'uploading' && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${upload.progress}%` }}
                  />
                </div>
              )}
              
              {upload.status === 'success' && (
                <div className="text-xs text-green-600">上传成功</div>
              )}
              
              {upload.status === 'error' && (
                <div className="text-xs text-red-600">{upload.error}</div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
