import React from 'react'

interface OnlineStatusProps {
  isOnline: boolean
  lastActive?: Date | null
  className?: string
}

export default function OnlineStatus({ isOnline, lastActive, className = '' }: OnlineStatusProps) {
  const formatLastActive = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) {
      return '刚刚活跃'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前活跃`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours}小时前活跃`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days}天前活跃`
    }
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-gray-400'}`}></div>
      <span className="text-sm text-gray-600">
        {isOnline ? '在线' : lastActive ? formatLastActive(lastActive) : '离线'}
      </span>
    </div>
  )
}
