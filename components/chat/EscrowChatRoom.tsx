"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import {
  PaperClipIcon,
  ArrowPathIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline'
import FileUpload from '@/components/chat/FileUpload'
import MessageRenderer from '@/components/chat/MessageRenderer'
import TypingIndicator from '@/components/chat/TypingIndicator'

interface Participant {
  id: string
  name: string
  email: string
  role: 'buyer' | 'seller' | 'mediator' | 'admin'
  isOnline?: boolean
  lastSeen?: string
}

interface EscrowMessage {
  id: string
  content: string
  messageType: string
  status: string
  createdAt: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  fileMimeType?: string
  fileMetadata?: any
  sender: {
    id: string
    name: string
    email: string
  }
  senderRole: 'buyer' | 'seller' | 'mediator' | 'admin'
  isSystemMessage?: boolean
}

interface EscrowChatRoomProps {
  orderId: string
  chatRoomId: string
  className?: string
}

export default function EscrowChatRoom({ orderId, chatRoomId, className = '' }: EscrowChatRoomProps) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<EscrowMessage[]>([])
  const [participants, setParticipants] = useState<Participant[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [error, setError] = useState('')
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [typingUsers, setTypingUsers] = useState<string[]>([])
  const [showParticipants, setShowParticipants] = useState(false)
  const [canExportChat, setCanExportChat] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // 获取聊天记录
  const fetchMessages = useCallback(async () => {
    try {
      const response = await fetch(`/api/escrow/chat/${chatRoomId}/messages`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setMessages(result.data.messages)
          setParticipants(result.data.participants)
          setCanExportChat(result.data.canExport)
        } else {
          setError(result.error || '获取聊天记录失败')
        }
      } else {
        setError('获取聊天记录失败')
      }
    } catch (error) {
      console.error('获取聊天记录失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }, [chatRoomId])

  // 发送消息
  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return

    try {
      setSending(true)
      
      const response = await fetch(`/api/escrow/chat/${chatRoomId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: newMessage.trim(),
          messageType: 'TEXT'
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setNewMessage('')
        fetchMessages() // 刷新消息列表
      } else {
        setError(result.error || '发送消息失败')
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setSending(false)
    }
  }

  // 处理文件上传成功
  const handleFileUploadSuccess = (fileData: any) => {
    setShowFileUpload(false)
    fetchMessages() // 刷新消息列表
  }

  // 导出聊天记录
  const exportChatHistory = async () => {
    try {
      const response = await fetch(`/api/escrow/chat/${chatRoomId}/export`, {
        method: 'POST'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `escrow-chat-${orderId}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const result = await response.json()
        alert(result.error || '导出失败')
      }
    } catch (error) {
      console.error('导出聊天记录失败:', error)
      alert('导出失败，请稍后重试')
    }
  }

  // 获取用户角色颜色
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'buyer':
        return 'text-blue-600'
      case 'seller':
        return 'text-green-600'
      case 'mediator':
        return 'text-purple-600'
      case 'admin':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  // 获取用户角色标签
  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'buyer':
        return '买家'
      case 'seller':
        return '卖家'
      case 'mediator':
        return '中间人'
      case 'admin':
        return '管理员'
      default:
        return '用户'
    }
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  useEffect(() => {
    fetchMessages()
    
    // 设置定时刷新
    const interval = setInterval(fetchMessages, 10000) // 10秒刷新一次
    return () => clearInterval(interval)
  }, [fetchMessages])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg ${className}`}>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg flex flex-col ${className}`} style={{ height: '600px' }}>
      {/* 聊天室头部 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600 mr-2" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">托管聊天室</h3>
              <p className="text-sm text-gray-500">订单: {orderId}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowParticipants(!showParticipants)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              title="参与者"
            >
              <UserGroupIcon className="h-5 w-5" />
            </button>
            
            {canExportChat && (
              <button
                onClick={exportChatHistory}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
                title="导出聊天记录"
              >
                <DocumentArrowDownIcon className="h-5 w-5" />
              </button>
            )}
            
            <button
              onClick={fetchMessages}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              title="刷新"
            >
              <ArrowPathIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 参与者列表 */}
        {showParticipants && (
          <div className="mt-4 p-3 bg-white rounded-lg border">
            <h4 className="text-sm font-medium text-gray-900 mb-2">参与者</h4>
            <div className="space-y-2">
              {participants.map((participant) => (
                <div key={participant.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${participant.isOnline ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-sm font-medium">{participant.name}</span>
                    <span className={`ml-2 text-xs px-2 py-1 rounded-full bg-gray-100 ${getRoleColor(participant.role)}`}>
                      {getRoleLabel(participant.role)}
                    </span>
                  </div>
                  {!participant.isOnline && participant.lastSeen && (
                    <span className="text-xs text-gray-500">
                      {formatTime(participant.lastSeen)}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
              <button
                onClick={() => setError('')}
                className="mt-1 text-sm text-red-600 hover:text-red-800 underline"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length > 0 ? (
          messages.map((message) => (
            <div key={message.id} className="space-y-1">
              {/* 系统消息 */}
              {message.isSystemMessage ? (
                <div className="text-center">
                  <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {message.content}
                  </span>
                </div>
              ) : (
                <>
                  {/* 发送者信息 */}
                  <div className={`flex ${message.sender.id === session?.user?.id ? 'justify-end' : 'justify-start'}`}>
                    <div className="text-xs text-gray-500">
                      <span className={`font-medium ${getRoleColor(message.senderRole)}`}>
                        {message.sender.name}
                      </span>
                      <span className="ml-1">({getRoleLabel(message.senderRole)})</span>
                      <span className="ml-2">{formatTime(message.createdAt)}</span>
                    </div>
                  </div>
                  
                  {/* 消息内容 */}
                  <MessageRenderer
                    message={message}
                    isOwnMessage={message.sender.id === session?.user?.id}
                    formatTime={formatTime}
                  />
                </>
              )}
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-8">
            <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-300" />
            <p className="mt-2">暂无聊天记录</p>
            <p className="text-sm">开始您的第一条消息吧</p>
          </div>
        )}
        
        {/* 正在输入指示器 */}
        {typingUsers.length > 0 && (
          <TypingIndicator 
            isTyping={true} 
            userName={typingUsers.join(', ')} 
          />
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 消息输入区域 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  sendMessage()
                }
              }}
              placeholder="输入消息..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={2}
              disabled={sending}
            />
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => setShowFileUpload(true)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              title="上传文件"
            >
              <PaperClipIcon className="h-5 w-5" />
            </button>
            
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim() || sending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sending ? '发送中...' : '发送'}
            </button>
          </div>
        </div>
      </div>

      {/* 文件上传模态框 */}
      {showFileUpload && (
        <FileUpload
          orderId={orderId}
          onSuccess={handleFileUploadSuccess}
          onCancel={() => setShowFileUpload(false)}
        />
      )}
    </div>
  )
}
