'use client'

import { useState, useEffect } from 'react'
import { formatUSDT } from '@/lib/utils'

interface VariantAttribute {
  name: string
  value: string
}

interface ProductVariant {
  id?: string
  sku?: string
  price: number
  stock: number
  status: string
  isDefault: boolean
  attributes: VariantAttribute[]
}

interface VariantManagerProps {
  productId?: string
  initialVariants?: ProductVariant[]
  onVariantsChange: (variants: ProductVariant[]) => void
  disabled?: boolean
}

export default function VariantManager({
  productId,
  initialVariants = [],
  onVariantsChange,
  disabled = false
}: VariantManagerProps) {
  const [variants, setVariants] = useState<ProductVariant[]>(initialVariants)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [newVariant, setNewVariant] = useState<ProductVariant>({
    sku: '',
    price: 0,
    stock: 0,
    status: 'AVAILABLE',
    isDefault: false,
    attributes: []
  })

  // 通知父组件变体变化
  useEffect(() => {
    onVariantsChange(variants)
  }, [variants])

  // 添加属性
  const addAttribute = (variantIndex: number | 'new') => {
    if (variantIndex === 'new') {
      setNewVariant(prev => ({
        ...prev,
        attributes: [...prev.attributes, { name: '', value: '' }]
      }))
    } else {
      const updatedVariants = [...variants]
      updatedVariants[variantIndex].attributes.push({ name: '', value: '' })
      setVariants(updatedVariants)
    }
  }

  // 删除属性
  const removeAttribute = (variantIndex: number | 'new', attrIndex: number) => {
    if (variantIndex === 'new') {
      setNewVariant(prev => ({
        ...prev,
        attributes: prev.attributes.filter((_, i) => i !== attrIndex)
      }))
    } else {
      const updatedVariants = [...variants]
      updatedVariants[variantIndex].attributes = updatedVariants[variantIndex].attributes.filter((_, i) => i !== attrIndex)
      setVariants(updatedVariants)
    }
  }

  // 更新属性
  const updateAttribute = (
    variantIndex: number | 'new',
    attrIndex: number,
    field: 'name' | 'value',
    value: string
  ) => {
    if (variantIndex === 'new') {
      setNewVariant(prev => ({
        ...prev,
        attributes: prev.attributes.map((attr, i) =>
          i === attrIndex ? { ...attr, [field]: value } : attr
        )
      }))
    } else {
      const updatedVariants = [...variants]
      updatedVariants[variantIndex].attributes[attrIndex][field] = value
      setVariants(updatedVariants)
    }
  }

  // 添加变体
  const addVariant = () => {
    if (newVariant.attributes.length === 0) {
      alert('请至少添加一个属性')
      return
    }

    if (newVariant.attributes.some(attr => !attr.name || !attr.value)) {
      alert('请填写完整的属性信息')
      return
    }

    if (newVariant.price <= 0) {
      alert('价格必须大于0')
      return
    }

    if (newVariant.stock < 0) {
      alert('库存不能为负数')
      return
    }

    // 如果是第一个变体，自动设为默认
    const isFirstVariant = variants.length === 0
    const variantToAdd = {
      ...newVariant,
      isDefault: isFirstVariant || newVariant.isDefault
    }

    // 如果设为默认，取消其他变体的默认状态
    if (variantToAdd.isDefault) {
      setVariants(prev => prev.map(v => ({ ...v, isDefault: false })))
    }

    setVariants(prev => [...prev, variantToAdd])
    setNewVariant({
      sku: '',
      price: 0,
      stock: 0,
      status: 'AVAILABLE',
      isDefault: false,
      attributes: []
    })
    setShowAddForm(false)
  }

  // 删除变体
  const removeVariant = (index: number) => {
    if (confirm('确定要删除这个变体吗？')) {
      const removedVariant = variants[index]
      const updatedVariants = variants.filter((_, i) => i !== index)
      
      // 如果删除的是默认变体，设置第一个变体为默认
      if (removedVariant.isDefault && updatedVariants.length > 0) {
        updatedVariants[0].isDefault = true
      }
      
      setVariants(updatedVariants)
    }
  }

  // 设置默认变体
  const setDefaultVariant = (index: number) => {
    setVariants(prev => prev.map((v, i) => ({
      ...v,
      isDefault: i === index
    })))
  }

  // 更新变体基本信息
  const updateVariant = (index: number, field: keyof ProductVariant, value: any) => {
    const updatedVariants = [...variants]
    updatedVariants[index] = { ...updatedVariants[index], [field]: value }
    
    // 如果设为默认，取消其他变体的默认状态
    if (field === 'isDefault' && value) {
      updatedVariants.forEach((v, i) => {
        if (i !== index) v.isDefault = false
      })
    }
    
    setVariants(updatedVariants)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">商品变体管理</h3>
        <button
          onClick={() => setShowAddForm(true)}
          disabled={disabled}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
        >
          添加变体
        </button>
      </div>

      {/* 现有变体列表 */}
      {variants.length > 0 && (
        <div className="space-y-4">
          {variants.map((variant, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700">变体 {index + 1}</span>
                  {variant.isDefault && (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                      默认
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setDefaultVariant(index)}
                    disabled={disabled || variant.isDefault}
                    className="text-sm text-blue-600 hover:text-blue-500 disabled:opacity-50"
                  >
                    设为默认
                  </button>
                  <button
                    onClick={() => removeVariant(index)}
                    disabled={disabled}
                    className="text-sm text-red-600 hover:text-red-500 disabled:opacity-50"
                  >
                    删除
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                  <input
                    type="text"
                    value={variant.sku || ''}
                    onChange={(e) => updateVariant(index, 'sku', e.target.value)}
                    disabled={disabled}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                    placeholder="可选"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">价格 (USDT)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={variant.price}
                    onChange={(e) => updateVariant(index, 'price', parseFloat(e.target.value) || 0)}
                    disabled={disabled}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">库存</label>
                  <input
                    type="number"
                    min="0"
                    value={variant.stock}
                    onChange={(e) => updateVariant(index, 'stock', parseInt(e.target.value) || 0)}
                    disabled={disabled}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                  />
                </div>
              </div>

              {/* 属性列表 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">属性</label>
                  <button
                    onClick={() => addAttribute(index)}
                    disabled={disabled}
                    className="text-sm text-blue-600 hover:text-blue-500 disabled:opacity-50"
                  >
                    + 添加属性
                  </button>
                </div>
                {variant.attributes.map((attr, attrIndex) => (
                  <div key={attrIndex} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={attr.name}
                      onChange={(e) => updateAttribute(index, attrIndex, 'name', e.target.value)}
                      disabled={disabled}
                      placeholder="属性名（如：颜色）"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <input
                      type="text"
                      value={attr.value}
                      onChange={(e) => updateAttribute(index, attrIndex, 'value', e.target.value)}
                      disabled={disabled}
                      placeholder="属性值（如：红色）"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <button
                      onClick={() => removeAttribute(index, attrIndex)}
                      disabled={disabled}
                      className="text-red-600 hover:text-red-500 disabled:opacity-50"
                    >
                      删除
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 添加变体表单 */}
      {showAddForm && (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-md font-medium text-gray-900">添加新变体</h4>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">SKU</label>
              <input
                type="text"
                value={newVariant.sku || ''}
                onChange={(e) => setNewVariant(prev => ({ ...prev, sku: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="可选"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">价格 (USDT)</label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={newVariant.price}
                onChange={(e) => setNewVariant(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">库存</label>
              <input
                type="number"
                min="0"
                value={newVariant.stock}
                onChange={(e) => setNewVariant(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 属性设置 */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium text-gray-700">属性</label>
              <button
                onClick={() => addAttribute('new')}
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                + 添加属性
              </button>
            </div>
            {newVariant.attributes.map((attr, attrIndex) => (
              <div key={attrIndex} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={attr.name}
                  onChange={(e) => updateAttribute('new', attrIndex, 'name', e.target.value)}
                  placeholder="属性名（如：颜色）"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="text"
                  value={attr.value}
                  onChange={(e) => updateAttribute('new', attrIndex, 'value', e.target.value)}
                  placeholder="属性值（如：红色）"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={() => removeAttribute('new', attrIndex)}
                  className="text-red-600 hover:text-red-500"
                >
                  删除
                </button>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={newVariant.isDefault}
                onChange={(e) => setNewVariant(prev => ({ ...prev, isDefault: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">设为默认变体</span>
            </label>
            <div className="space-x-2">
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
              >
                取消
              </button>
              <button
                onClick={addVariant}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                添加变体
              </button>
            </div>
          </div>
        </div>
      )}

      {variants.length === 0 && !showAddForm && (
        <div className="text-center py-8 text-gray-500">
          <p>暂无变体，点击"添加变体"开始创建</p>
        </div>
      )}
    </div>
  )
}
