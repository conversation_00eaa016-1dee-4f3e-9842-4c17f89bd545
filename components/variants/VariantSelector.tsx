'use client'

import { useState, useEffect } from 'react'
import { formatUSDT } from '@/lib/utils'

interface VariantAttribute {
  id: string
  name: string
  value: string
}

interface ProductVariant {
  id: string
  sku?: string
  price: number
  stock: number
  status: string
  isDefault: boolean
  attributes: VariantAttribute[]
}

interface VariantSelectorProps {
  variants: ProductVariant[]
  onVariantChange: (variant: ProductVariant | null) => void
  disabled?: boolean
}

export default function VariantSelector({ 
  variants, 
  onVariantChange, 
  disabled = false 
}: VariantSelectorProps) {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, string>>({})

  // 获取所有可用的属性名称
  const attributeNames = Array.from(
    new Set(variants.flatMap(v => v.attributes.map(a => a.name)))
  )

  // 获取每个属性的可选值
  const getAttributeValues = (attributeName: string) => {
    const values = new Set<string>()
    variants.forEach(variant => {
      const attr = variant.attributes.find(a => a.name === attributeName)
      if (attr) {
        values.add(attr.value)
      }
    })
    return Array.from(values)
  }

  // 根据已选择的属性找到匹配的变体
  const findMatchingVariant = (attributes: Record<string, string>) => {
    return variants.find(variant => {
      return attributeNames.every(name => {
        const variantAttr = variant.attributes.find(a => a.name === name)
        const selectedValue = attributes[name]
        
        if (!selectedValue) return true // 如果没有选择这个属性，则不限制
        return variantAttr?.value === selectedValue
      })
    })
  }

  // 检查某个属性值是否可选（基于当前已选择的其他属性）
  const isAttributeValueAvailable = (attributeName: string, value: string) => {
    const testAttributes = { ...selectedAttributes, [attributeName]: value }
    const matchingVariant = findMatchingVariant(testAttributes)
    return matchingVariant && matchingVariant.status === 'AVAILABLE' && matchingVariant.stock > 0
  }

  // 处理属性选择
  const handleAttributeChange = (attributeName: string, value: string) => {
    const newAttributes = { ...selectedAttributes, [attributeName]: value }
    setSelectedAttributes(newAttributes)

    // 检查是否所有属性都已选择
    const allSelected = attributeNames.every(name => newAttributes[name])
    
    if (allSelected) {
      const variant = findMatchingVariant(newAttributes)
      setSelectedVariant(variant || null)
      onVariantChange(variant || null)
    } else {
      setSelectedVariant(null)
      onVariantChange(null)
    }
  }

  // 初始化默认变体
  useEffect(() => {
    if (variants.length > 0) {
      const defaultVariant = variants.find(v => v.isDefault) || variants[0]
      
      if (defaultVariant) {
        const defaultAttributes: Record<string, string> = {}
        defaultVariant.attributes.forEach(attr => {
          defaultAttributes[attr.name] = attr.value
        })
        
        setSelectedAttributes(defaultAttributes)
        setSelectedVariant(defaultVariant)
        onVariantChange(defaultVariant)
      }
    }
  }, [variants, onVariantChange])

  if (variants.length === 0) {
    return null
  }

  return (
    <div className="space-y-4">
      {attributeNames.map(attributeName => (
        <div key={attributeName} className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            {attributeName}
          </label>
          <div className="flex flex-wrap gap-2">
            {getAttributeValues(attributeName).map(value => {
              const isSelected = selectedAttributes[attributeName] === value
              const isAvailable = isAttributeValueAvailable(attributeName, value)
              
              return (
                <button
                  key={value}
                  onClick={() => handleAttributeChange(attributeName, value)}
                  disabled={disabled || !isAvailable}
                  className={`
                    px-3 py-2 text-sm border rounded-md transition-colors
                    ${isSelected
                      ? 'bg-blue-600 text-white border-blue-600'
                      : isAvailable
                        ? 'bg-white text-gray-700 border-gray-300 hover:border-blue-500'
                        : 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    }
                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  {value}
                </button>
              )
            })}
          </div>
        </div>
      ))}

      {selectedVariant && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {formatUSDT(selectedVariant.price)}
              </div>
              {selectedVariant.sku && (
                <div className="text-sm text-gray-500">
                  SKU: {selectedVariant.sku}
                </div>
              )}
            </div>
            <div className="text-right">
              <div className={`text-sm font-medium ${
                selectedVariant.stock > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {selectedVariant.stock > 0 
                  ? `库存: ${selectedVariant.stock} 件`
                  : '暂无库存'
                }
              </div>
              <div className={`text-xs ${
                selectedVariant.status === 'AVAILABLE' ? 'text-green-600' : 'text-red-600'
              }`}>
                {selectedVariant.status === 'AVAILABLE' ? '有货' : '缺货'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// 简化的变体显示组件
export function VariantDisplay({ variant }: { variant: ProductVariant }) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="text-lg font-semibold text-blue-600">
          {formatUSDT(variant.price)}
        </div>
        <div className={`text-sm font-medium ${
          variant.stock > 0 ? 'text-green-600' : 'text-red-600'
        }`}>
          库存: {variant.stock} 件
        </div>
      </div>
      
      {variant.sku && (
        <div className="text-sm text-gray-500">
          SKU: {variant.sku}
        </div>
      )}
      
      <div className="flex flex-wrap gap-2">
        {variant.attributes.map(attr => (
          <span
            key={attr.id}
            className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded"
          >
            {attr.name}: {attr.value}
          </span>
        ))}
      </div>
    </div>
  )
}
