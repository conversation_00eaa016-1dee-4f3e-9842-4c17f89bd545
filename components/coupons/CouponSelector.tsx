"use client"

import React, { useState, useEffect } from 'react'
import { TicketIcon, ClockIcon, CheckCircleIcon } from '@heroicons/react/24/outline'

interface Coupon {
  id: string
  type: string
  value: number
  description: string
  status: string
  expiresAt: string
  usedAt?: string
}

interface CouponSelectorProps {
  context: 'withdrawal' | 'platform_fee'
  onCouponSelect: (coupon: Coupon | null) => void
  selectedCouponId?: string
}

export default function CouponSelector({ context, onCouponSelect, selectedCouponId }: CouponSelectorProps) {
  const [coupons, setCoupons] = useState<Coupon[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchCoupons()
  }, [context])

  const fetchCoupons = async () => {
    try {
      setLoading(true)
      const typeFilter = context === 'withdrawal' ? 'WITHDRAWAL_FEE_WAIVER' : 'PLATFORM_CREDIT'
      const response = await fetch(`/api/rewards/coupons?status=ACTIVE&type=${typeFilter}`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // 过滤未过期的券
          const validCoupons = result.data.coupons.filter((coupon: Coupon) => 
            new Date(coupon.expiresAt) > new Date()
          )
          setCoupons(validCoupons)
        } else {
          setError(result.error || '获取减免券失败')
        }
      } else {
        setError('获取减免券失败')
      }
    } catch (error) {
      console.error('获取减免券失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleCouponSelect = (coupon: Coupon | null) => {
    onCouponSelect(coupon)
  }

  const formatExpiryDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getCouponTypeText = (type: string) => {
    switch (type) {
      case 'WITHDRAWAL_FEE_WAIVER':
        return '提现手续费减免'
      case 'PLATFORM_CREDIT':
        return '平台积分'
      default:
        return '未知类型'
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          选择减免券
        </label>
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded-md"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          选择减免券
        </label>
        <div className="text-sm text-red-600">{error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700">
        选择减免券 {coupons.length > 0 && `(${coupons.length}张可用)`}
      </label>
      
      {coupons.length === 0 ? (
        <div className="text-sm text-gray-500 p-3 bg-gray-50 rounded-md">
          暂无可用的减免券
        </div>
      ) : (
        <div className="space-y-2">
          {/* 不使用减免券选项 */}
          <label className="flex items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="coupon"
              value=""
              checked={!selectedCouponId}
              onChange={() => handleCouponSelect(null)}
              className="mr-3"
            />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">不使用减免券</div>
              <div className="text-xs text-gray-500">按正常费率收取手续费</div>
            </div>
          </label>

          {/* 减免券选项 */}
          {coupons.map((coupon) => (
            <label
              key={coupon.id}
              className="flex items-center p-3 border rounded-md cursor-pointer hover:bg-gray-50"
            >
              <input
                type="radio"
                name="coupon"
                value={coupon.id}
                checked={selectedCouponId === coupon.id}
                onChange={() => handleCouponSelect(coupon)}
                className="mr-3"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TicketIcon className="h-4 w-4 text-purple-500 mr-2" />
                    <span className="text-sm font-medium text-gray-900">
                      {getCouponTypeText(coupon.type)}
                    </span>
                  </div>
                  <div className="text-sm font-bold text-green-600">
                    {coupon.type === 'WITHDRAWAL_FEE_WAIVER' ? '免手续费' : `${coupon.value} USDT`}
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {coupon.description}
                </div>
                <div className="flex items-center text-xs text-gray-400 mt-1">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  有效期至: {formatExpiryDate(coupon.expiresAt)}
                </div>
              </div>
            </label>
          ))}
        </div>
      )}
    </div>
  )
}
