'use client'

import { SessionProvider } from 'next-auth/react'
import type { Session } from 'next-auth'
import type { ReactNode } from 'react'

interface AuthSessionProviderProps {
  children: ReactNode
  session?: Session | null
}

export default function AuthSessionProvider({
  children,
  session
}: AuthSessionProviderProps) {
  return (
    <SessionProvider
      session={session}
      refetchInterval={5 * 60} // 5分钟刷新一次
      refetchOnWindowFocus={true}
      refetchWhenOffline={false}
      basePath="/api/auth"
    >
      {children}
    </SessionProvider>
  )
}
