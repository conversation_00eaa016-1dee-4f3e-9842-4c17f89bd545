'use client'

import { useState } from 'react'
import {
  TicketIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  XMarkIcon,
  GiftIcon
} from '@heroicons/react/24/outline'

interface RedemptionCodeExchangeProps {
  userBalance: {
    available: number
    total: number
  }
  onExchange?: (result: any) => void
}

export default function RedemptionCodeExchange({ userBalance, onExchange }: RedemptionCodeExchangeProps) {
  const [exchangeType, setExchangeType] = useState<'redemption' | 'giftcard'>('redemption')
  const [codeValue, setCodeValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [verifying, setVerifying] = useState(false)
  const [codeInfo, setCodeInfo] = useState<any>(null)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // 验证兑换码或礼品卡
  const verifyCode = async () => {
    if (!codeValue.trim()) {
      setError(exchangeType === 'redemption' ? '请输入兑换码' : '请输入礼品卡码')
      return
    }

    try {
      setVerifying(true)
      setError('')
      setCodeInfo(null)

      const apiUrl = exchangeType === 'redemption'
        ? `/api/redemption-codes/verify?code=${encodeURIComponent(codeValue.trim())}`
        : `/api/giftcards/redeem?cardCode=${encodeURIComponent(codeValue.trim())}`

      const response = await fetch(apiUrl)
      const result = await response.json()

      if (response.ok && result.success) {
        setCodeInfo(result.data)
        if (exchangeType === 'redemption' && !result.data.canUse) {
          setError(result.data.statusMessage || '兑换码不可用')
        } else if (exchangeType === 'giftcard' && !result.data.canRedeem) {
          setError(result.data.statusMessage || '礼品卡不可兑换')
        }
      } else {
        setError(result.error || `验证${exchangeType === 'redemption' ? '兑换码' : '礼品卡'}失败`)
      }
    } catch (error) {
      console.error(`验证${exchangeType === 'redemption' ? '兑换码' : '礼品卡'}失败:`, error)
      setError('网络错误，请稍后重试')
    } finally {
      setVerifying(false)
    }
  }

  // 使用兑换码或礼品卡
  const useCode = async () => {
    const canUse = exchangeType === 'redemption' ? codeInfo?.canUse : codeInfo?.canRedeem
    if (!codeInfo || !canUse) {
      setError(`${exchangeType === 'redemption' ? '兑换码' : '礼品卡'}不可用`)
      return
    }

    try {
      setLoading(true)
      setError('')
      setSuccess('')

      const apiUrl = exchangeType === 'redemption' ? '/api/redemption-codes/use' : '/api/giftcards/redeem'
      const requestBody = exchangeType === 'redemption'
        ? { codeValue: codeValue.trim() }
        : { cardCode: codeValue.trim() }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess(result.message)
        setCodeValue('')
        setCodeInfo(null)
        onExchange?.(result.data)
      } else {
        setError(result.error || `使用${exchangeType === 'redemption' ? '兑换码' : '礼品卡'}失败`)
      }
    } catch (error) {
      console.error(`使用${exchangeType === 'redemption' ? '兑换码' : '礼品卡'}失败:`, error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setCodeValue('')
    setCodeInfo(null)
    setError('')
    setSuccess('')
  }

  const switchExchangeType = (type: 'redemption' | 'giftcard') => {
    setExchangeType(type)
    resetForm()
  }

  const getRewardTypeText = (rewardType: string) => {
    switch (rewardType) {
      case 'CASH_CREDIT': return '现金奖励'
      case 'WITHDRAWAL_FEE_DISCOUNT': return '提现手续费减免'
      case 'SHOPPING_VOUCHER': return '购物券'
      default: return rewardType
    }
  }

  const getDisplayValue = () => {
    if (exchangeType === 'giftcard' && codeInfo) {
      return `${codeInfo.faceValue} USDT`
    } else if (exchangeType === 'redemption' && codeInfo) {
      return `${codeInfo.rewardValue} ${codeInfo.rewardUnit}`
    }
    return ''
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600'
      case 'USED': return 'text-gray-500'
      case 'EXPIRED': return 'text-red-600'
      default: return 'text-gray-500'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          {exchangeType === 'redemption' ? (
            <TicketIcon className="h-6 w-6 text-purple-600 mr-3" />
          ) : (
            <GiftIcon className="h-6 w-6 text-green-600 mr-3" />
          )}
          <h3 className="text-lg font-semibold text-gray-900">
            {exchangeType === 'redemption' ? '兑换码使用' : '礼品卡兑换'}
          </h3>
        </div>

        {/* 类型切换按钮 */}
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => switchExchangeType('redemption')}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              exchangeType === 'redemption'
                ? 'bg-white text-purple-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            兑换码
          </button>
          <button
            onClick={() => switchExchangeType('giftcard')}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              exchangeType === 'giftcard'
                ? 'bg-white text-green-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            礼品卡
          </button>
        </div>
      </div>

      {/* 成功提示 */}
      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
            <p className="text-sm text-green-800">{success}</p>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* 兑换码/礼品卡输入 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {exchangeType === 'redemption' ? '兑换码' : '礼品卡码'}
          </label>
          <div className="flex space-x-3">
            <input
              type="text"
              value={codeValue}
              onChange={(e) => setCodeValue(e.target.value.toUpperCase())}
              placeholder={exchangeType === 'redemption' ? '输入兑换码' : '输入礼品卡码'}
              className={`flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:border-transparent ${
                exchangeType === 'redemption'
                  ? 'focus:ring-purple-500'
                  : 'focus:ring-green-500'
              }`}
              maxLength={16}
            />
            <button
              onClick={verifyCode}
              disabled={verifying || !codeValue.trim()}
              className={`px-4 py-2 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed ${
                exchangeType === 'redemption'
                  ? 'bg-purple-600 hover:bg-purple-700'
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {verifying ? '验证中...' : '验证'}
            </button>
          </div>
        </div>

        {/* 兑换码/礼品卡信息 */}
        {codeInfo && (
          <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
            <h4 className="font-medium text-gray-900 mb-3">
              {exchangeType === 'redemption' ? '兑换码信息' : '礼品卡信息'}
            </h4>
            <div className="space-y-2 text-sm">
              {exchangeType === 'redemption' ? (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">标题:</span>
                    <span className="font-medium">{codeInfo.title}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">类型:</span>
                    <span className="font-medium">{getRewardTypeText(codeInfo.rewardType)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">奖励:</span>
                    <span className="font-medium text-green-600">
                      {codeInfo.rewardValue} {codeInfo.rewardUnit}
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">卡码:</span>
                    <span className="font-medium">{codeInfo.cardCode}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">面值:</span>
                    <span className="font-medium text-green-600">
                      {codeInfo.faceValue} USDT
                    </span>
                  </div>
                </>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">状态:</span>
                <span className={`font-medium ${getStatusColor(codeInfo.status)}`}>
                  {codeInfo.statusMessage}
                </span>
              </div>
              {codeInfo.validUntil && (
                <div className="flex justify-between">
                  <span className="text-gray-600">有效期:</span>
                  <span className="font-medium">
                    {new Date(codeInfo.validUntil).toLocaleDateString('zh-CN')}
                  </span>
                </div>
              )}
              {codeInfo.description && (
                <div className="mt-2">
                  <span className="text-gray-600">描述:</span>
                  <p className="text-gray-800 mt-1">{codeInfo.description}</p>
                </div>
              )}
            </div>

            {/* 使用按钮 */}
            {(exchangeType === 'redemption' ? codeInfo.canUse : codeInfo.canRedeem) && (
              <div className="mt-4 flex space-x-3">
                <button
                  onClick={useCode}
                  disabled={loading}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (exchangeType === 'redemption' ? '使用中...' : '兑换中...') : (exchangeType === 'redemption' ? '立即使用' : '立即兑换')}
                </button>
                <button
                  onClick={resetForm}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  重新输入
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          {exchangeType === 'redemption' ? (
            <>
              <li>• 输入兑换码后点击"验证"查看详情</li>
              <li>• 确认信息无误后点击"立即使用"</li>
              <li>• 每个兑换码只能使用一次</li>
              <li>• 请在有效期内使用兑换码</li>
            </>
          ) : (
            <>
              <li>• 输入礼品卡码后点击"验证"查看详情</li>
              <li>• 确认信息无误后点击"立即兑换"</li>
              <li>• 每张礼品卡只能兑换一次</li>
              <li>• 请在有效期内兑换礼品卡</li>
            </>
          )}
        </ul>
      </div>
    </div>
  )
}
