'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { 
  ScaleIcon, 
  ClockIcon, 
  UserGroupIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

interface DisputeInfo {
  id: string
  reason: string
  description: string
  status: string
  priority: string
  votingDeadline: string | null
  finalDecision: string | null
  resolvedAt: string | null
  escrowOrder: {
    id: string
    amount: number
    order: {
      orderNumber: string
      product: {
        title: string
      }
    }
    mediator: { name: string }
    buyer: { name: string }
    seller: { name: string }
  }
  reporter: { name: string }
  reported: { name: string }
}

interface Vote {
  id: string
  decision: string
  reasoning: string
  voteWeight: number
  votedAt: string
  voter: {
    name: string
    mediatorReputation: number
  }
}

interface ArbitrationVotingPanelProps {
  disputeId: string
  onVoteSubmitted?: () => void
  className?: string
}

const DECISION_OPTIONS = [
  {
    value: 'FAVOR_BUYER',
    label: '支持买家',
    description: '买家的申诉有理，应获得退款',
    color: 'bg-blue-50 border-blue-200 text-blue-800'
  },
  {
    value: 'FAVOR_SELLER',
    label: '支持卖家',
    description: '卖家履行了义务，应获得货款',
    color: 'bg-green-50 border-green-200 text-green-800'
  },
  {
    value: 'SPLIT_FUNDS',
    label: '资金分割',
    description: '双方都有责任，按比例分配资金',
    color: 'bg-yellow-50 border-yellow-200 text-yellow-800'
  },
  {
    value: 'NEED_MORE_INFO',
    label: '需要更多信息',
    description: '证据不足，需要更多信息才能判决',
    color: 'bg-gray-50 border-gray-200 text-gray-800'
  }
]

export default function ArbitrationVotingPanel({
  disputeId,
  onVoteSubmitted,
  className = ''
}: ArbitrationVotingPanelProps) {
  const { data: session } = useSession()
  const [dispute, setDispute] = useState<DisputeInfo | null>(null)
  const [votes, setVotes] = useState<Vote[]>([])
  const [userVote, setUserVote] = useState<Vote | null>(null)
  const [votingStats, setVotingStats] = useState<any>(null)
  const [canVote, setCanVote] = useState(false)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [selectedDecision, setSelectedDecision] = useState('')
  const [reasoning, setReasoning] = useState('')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (disputeId) {
      fetchDisputeInfo()
    }
  }, [disputeId])

  const fetchDisputeInfo = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/escrow/dispute/vote?disputeId=${disputeId}`)
      const data = await response.json()

      if (data.success) {
        setDispute(data.data.dispute)
        setVotes(data.data.votes)
        setUserVote(data.data.userVote)
        setVotingStats(data.data.votingStats)
        setCanVote(data.data.canVote)
      } else {
        setError(data.error || '获取争议信息失败')
      }
    } catch (error) {
      console.error('获取争议信息失败:', error)
      setError('获取争议信息失败')
    } finally {
      setLoading(false)
    }
  }

  const submitVote = async () => {
    if (!selectedDecision || !reasoning.trim()) {
      setError('请选择投票决定并填写理由')
      return
    }

    setSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/escrow/dispute/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          disputeId,
          decision: selectedDecision,
          reasoning: reasoning.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        // 重新获取争议信息
        await fetchDisputeInfo()
        
        // 清空表单
        setSelectedDecision('')
        setReasoning('')
        
        // 通知父组件
        onVoteSubmitted?.()
      } else {
        setError(data.error || '提交投票失败')
      }
    } catch (error) {
      console.error('提交投票失败:', error)
      setError('提交投票失败')
    } finally {
      setSubmitting(false)
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getDecisionLabel = (decision: string) => {
    const option = DECISION_OPTIONS.find(opt => opt.value === decision)
    return option?.label || decision
  }

  const getDecisionColor = (decision: string) => {
    const option = DECISION_OPTIONS.find(opt => opt.value === decision)
    return option?.color || 'bg-gray-50 border-gray-200 text-gray-800'
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!dispute) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <ExclamationTriangleIcon className="h-12 w-12 mx-auto mb-4" />
          <p>争议信息不存在或无权限查看</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* 争议信息头部 */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <ScaleIcon className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">仲裁投票</h3>
          </div>
          
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            dispute.status === 'VOTING' ? 'bg-purple-100 text-purple-800' :
            dispute.status === 'RESOLVED' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {dispute.status === 'VOTING' ? '投票中' :
             dispute.status === 'RESOLVED' ? '已解决' : dispute.status}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">订单号:</span>
            <span className="ml-2 font-medium">{dispute.escrowOrder.order.orderNumber}</span>
          </div>
          <div>
            <span className="text-gray-500">商品:</span>
            <span className="ml-2 font-medium">{dispute.escrowOrder.order.product.title}</span>
          </div>
          <div>
            <span className="text-gray-500">金额:</span>
            <span className="ml-2 font-medium">{dispute.escrowOrder.amount} USDT</span>
          </div>
          <div>
            <span className="text-gray-500">优先级:</span>
            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
              dispute.priority === 'HIGH' ? 'bg-red-100 text-red-800' :
              dispute.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {dispute.priority === 'HIGH' ? '高' :
               dispute.priority === 'MEDIUM' ? '中' : '低'}
            </span>
          </div>
        </div>

        {dispute.votingDeadline && (
          <div className="mt-4 flex items-center space-x-2 text-sm text-orange-600">
            <ClockIcon className="h-4 w-4" />
            <span>投票截止: {formatDateTime(dispute.votingDeadline)}</span>
          </div>
        )}
      </div>

      {/* 争议详情 */}
      <div className="p-6 border-b border-gray-200">
        <h4 className="font-medium text-gray-900 mb-2">争议详情</h4>
        <div className="bg-gray-50 rounded-md p-4">
          <div className="mb-2">
            <span className="text-sm text-gray-500">争议原因:</span>
            <span className="ml-2 text-sm font-medium">{dispute.reason}</span>
          </div>
          <div>
            <span className="text-sm text-gray-500">详细描述:</span>
            <p className="mt-1 text-sm text-gray-700">{dispute.description}</p>
          </div>
        </div>
        
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">举报人:</span>
            <span className="ml-2 font-medium">{dispute.reporter.name}</span>
          </div>
          <div>
            <span className="text-gray-500">被举报人:</span>
            <span className="ml-2 font-medium">{dispute.reported.name}</span>
          </div>
        </div>
      </div>

      {/* 投票统计 */}
      {votingStats && (
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900">投票统计</h4>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <UserGroupIcon className="h-4 w-4" />
              <span>{votingStats.voteCount} 票</span>
            </div>
          </div>
          
          <div className="space-y-2">
            {Object.entries(votingStats.distribution).map(([decision, weight]: [string, any]) => (
              <div key={decision} className="flex items-center justify-between">
                <span className={`px-2 py-1 rounded text-xs font-medium ${getDecisionColor(decision)}`}>
                  {getDecisionLabel(decision)}
                </span>
                <span className="text-sm font-medium">{weight.toFixed(1)} 权重</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 投票表单或结果 */}
      <div className="p-6">
        {userVote ? (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
              <span className="font-medium text-green-800">您已投票</span>
            </div>
            <div className="text-sm text-green-700">
              <div className="mb-1">
                <span>决定: </span>
                <span className="font-medium">{getDecisionLabel(userVote.decision)}</span>
              </div>
              <div className="mb-1">
                <span>权重: </span>
                <span className="font-medium">{userVote.voteWeight}</span>
              </div>
              <div className="mb-1">
                <span>时间: </span>
                <span className="font-medium">{formatDateTime(userVote.votedAt)}</span>
              </div>
              {userVote.reasoning && (
                <div>
                  <span>理由: </span>
                  <span className="font-medium">{userVote.reasoning}</span>
                </div>
              )}
            </div>
          </div>
        ) : canVote ? (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">提交您的投票</h4>
            
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                投票决定
              </label>
              <div className="space-y-2">
                {DECISION_OPTIONS.map(option => (
                  <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="decision"
                      value={option.value}
                      checked={selectedDecision === option.value}
                      onChange={(e) => setSelectedDecision(e.target.value)}
                      className="mt-1 text-purple-600 focus:ring-purple-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{option.label}</div>
                      <div className="text-sm text-gray-500">{option.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                投票理由 <span className="text-red-500">*</span>
              </label>
              <textarea
                value={reasoning}
                onChange={(e) => setReasoning(e.target.value)}
                rows={4}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="请详细说明您的投票理由和判断依据..."
              />
            </div>

            <button
              onClick={submitVote}
              disabled={submitting || !selectedDecision || !reasoning.trim()}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-3 rounded-md font-medium transition-colors flex items-center justify-center space-x-2"
            >
              {submitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>提交中...</span>
                </>
              ) : (
                <>
                  <ScaleIcon className="h-5 w-5" />
                  <span>提交投票</span>
                </>
              )}
            </button>
          </div>
        ) : (
          <div className="text-center text-gray-500">
            <DocumentTextIcon className="h-12 w-12 mx-auto mb-4" />
            <p>
              {dispute.status === 'RESOLVED' ? '争议已解决' : 
               dispute.votingDeadline && new Date() > new Date(dispute.votingDeadline) ? '投票已截止' :
               '您无权参与此争议的投票'}
            </p>
          </div>
        )}
      </div>

      {/* 最终决定 */}
      {dispute.finalDecision && (
        <div className="border-t border-gray-200 p-6">
          <h4 className="font-medium text-gray-900 mb-2">最终决定</h4>
          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getDecisionColor(dispute.finalDecision)}`}>
            {getDecisionLabel(dispute.finalDecision)}
          </div>
          {dispute.resolvedAt && (
            <div className="mt-2 text-sm text-gray-500">
              解决时间: {formatDateTime(dispute.resolvedAt)}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
