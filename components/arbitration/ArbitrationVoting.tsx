'use client'

import React, { useState, useEffect } from 'react'
import { 
  ScaleIcon, 
  ClockIcon, 
  UserGroupIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

interface ArbitrationCase {
  id: string
  reason: string
  description: string
  evidence?: any
  reporterType: 'BUYER' | 'SELLER'
  votingDeadline: string
  order: {
    orderNumber: string
    totalAmount: number
    product: {
      title: string
      images?: string
    }
    buyer: {
      name: string
    }
    seller: {
      name: string
    }
  }
  votes: Array<{
    id: string
    vote: string
    reason?: string
    mediator: {
      name: string
    }
  }>
}

interface ArbitrationVotingProps {
  caseData: ArbitrationCase
  onVote?: (vote: string, reason: string) => void
  userHasVoted?: boolean
  className?: string
}

export default function ArbitrationVoting({
  caseData,
  onVote,
  userHasVoted = false,
  className = ''
}: ArbitrationVotingProps) {
  const [selectedVote, setSelectedVote] = useState('')
  const [voteReason, setVoteReason] = useState('')
  const [loading, setLoading] = useState(false)
  const [showEvidence, setShowEvidence] = useState(false)

  const voteOptions = [
    {
      value: 'BUYER_FAVOR',
      label: '支持买家',
      description: '认为买家的申诉有理，应退款给买家',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      value: 'SELLER_FAVOR',
      label: '支持卖家',
      description: '认为卖家履行了义务，应释放资金给卖家',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      value: 'NEUTRAL',
      label: '中性/需要更多信息',
      description: '证据不足或需要进一步调查',
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    }
  ]

  const handleVoteSubmit = async () => {
    if (!selectedVote || !voteReason.trim()) {
      alert('请选择投票选项并填写理由')
      return
    }

    setLoading(true)
    try {
      await onVote?.(selectedVote, voteReason)
    } finally {
      setLoading(false)
    }
  }

  const getVoteStats = () => {
    const stats = {
      BUYER_FAVOR: 0,
      SELLER_FAVOR: 0,
      NEUTRAL: 0,
      total: caseData.votes.length
    }

    caseData.votes.forEach(vote => {
      stats[vote.vote as keyof typeof stats]++
    })

    return stats
  }

  const isVotingExpired = () => {
    return new Date() > new Date(caseData.votingDeadline)
  }

  const formatTimeRemaining = () => {
    const deadline = new Date(caseData.votingDeadline)
    const now = new Date()
    const diff = deadline.getTime() - now.getTime()

    if (diff <= 0) return '已截止'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) return `${days}天${hours}小时`
    return `${hours}小时`
  }

  const voteStats = getVoteStats()
  const expired = isVotingExpired()

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* 案例头部信息 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <ScaleIcon className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900">仲裁投票</h2>
        </div>
        <div className="flex items-center text-sm text-gray-500">
          <ClockIcon className="h-4 w-4 mr-1" />
          <span>剩余时间: {formatTimeRemaining()}</span>
        </div>
      </div>

      {/* 订单信息 */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">争议订单信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">订单号:</span>
            <span className="ml-2 font-mono">{caseData.order.orderNumber}</span>
          </div>
          <div>
            <span className="text-gray-500">订单金额:</span>
            <span className="ml-2 font-semibold">{caseData.order.totalAmount.toFixed(2)} USDT</span>
          </div>
          <div>
            <span className="text-gray-500">商品:</span>
            <span className="ml-2">{caseData.order.product.title}</span>
          </div>
          <div>
            <span className="text-gray-500">申诉方:</span>
            <span className="ml-2">
              {caseData.reporterType === 'BUYER' ? caseData.order.buyer.name : caseData.order.seller.name}
              ({caseData.reporterType === 'BUYER' ? '买家' : '卖家'})
            </span>
          </div>
        </div>
      </div>

      {/* 争议详情 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">争议详情</h3>
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-yellow-800 mb-2">
            争议原因: {caseData.reason}
          </h4>
          <p className="text-sm text-yellow-700">
            {caseData.description}
          </p>
        </div>

        {/* 证据材料 */}
        {caseData.evidence && (
          <div className="mt-4">
            <button
              onClick={() => setShowEvidence(!showEvidence)}
              className="flex items-center text-sm text-blue-600 hover:text-blue-800"
            >
              <DocumentTextIcon className="h-4 w-4 mr-1" />
              {showEvidence ? '隐藏' : '查看'}证据材料
            </button>
            
            {showEvidence && (
              <div className="mt-2 bg-gray-50 rounded-md p-3">
                <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                  {JSON.stringify(caseData.evidence, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 当前投票统计 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">
          当前投票统计 ({voteStats.total} 票)
        </h3>
        <div className="space-y-2">
          {voteOptions.map(option => {
            const count = voteStats[option.value as keyof typeof voteStats]
            const percentage = voteStats.total > 0 ? (count / voteStats.total) * 100 : 0
            
            return (
              <div key={option.value} className="flex items-center justify-between">
                <span className="text-sm text-gray-700">{option.label}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${option.value === 'BUYER_FAVOR' ? 'bg-blue-500' : option.value === 'SELLER_FAVOR' ? 'bg-green-500' : 'bg-gray-500'}`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12 text-right">
                    {count} 票
                  </span>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 投票区域 */}
      {!userHasVoted && !expired && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">您的投票</h3>
          <div className="space-y-3">
            {voteOptions.map(option => (
              <label
                key={option.value}
                className={`block border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedVote === option.value
                    ? `${option.borderColor} ${option.bgColor}`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="vote"
                    value={option.value}
                    checked={selectedVote === option.value}
                    onChange={(e) => setSelectedVote(e.target.value)}
                    className="mr-3"
                  />
                  <div>
                    <div className={`font-medium ${selectedVote === option.value ? option.color : 'text-gray-900'}`}>
                      {option.label}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {option.description}
                    </div>
                  </div>
                </div>
              </label>
            ))}
          </div>

          {/* 投票理由 */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              投票理由 *
            </label>
            <textarea
              value={voteReason}
              onChange={(e) => setVoteReason(e.target.value)}
              rows={3}
              placeholder="请详细说明您的投票理由..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <button
            onClick={handleVoteSubmit}
            disabled={loading || !selectedVote || !voteReason.trim()}
            className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? '提交中...' : '提交投票'}
          </button>
        </div>
      )}

      {/* 已投票提示 */}
      {userHasVoted && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-green-800">
                您已完成投票
              </h3>
              <p className="text-sm text-green-700 mt-1">
                感谢您参与仲裁投票，您将获得10 USDT免手续费提现券奖励。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 投票截止提示 */}
      {expired && (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-400 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-gray-800">
                投票已截止
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                此案例的投票期限已过，正在等待系统自动处理结果。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 已投票的中间人列表 */}
      {caseData.votes.length > 0 && (
        <div className="mt-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">
            投票记录
          </h3>
          <div className="space-y-2">
            {caseData.votes.map(vote => {
              const option = voteOptions.find(opt => opt.value === vote.vote)
              return (
                <div key={vote.id} className="flex items-center justify-between text-sm">
                  <span className="text-gray-700">{vote.mediator.name}</span>
                  <span className={option?.color || 'text-gray-600'}>
                    {option?.label || vote.vote}
                  </span>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
