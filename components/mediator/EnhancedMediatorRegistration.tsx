"use client"

import React, { useState, useEffect } from 'react'
import { 
  ShieldCheckIcon, 
  WalletIcon, 
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  DocumentTextIcon,
  StarIcon,
  LinkIcon
} from '@heroicons/react/24/outline'

interface MediatorRegistrationProps {
  onSuccess?: () => void
  onCancel?: () => void
}

interface WalletConnection {
  address: string
  isConnected: boolean
  balance?: number
  chainId?: string
}

declare global {
  interface Window {
    ethereum?: any
  }
}

export default function EnhancedMediatorRegistration({ onSuccess, onCancel }: MediatorRegistrationProps) {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // 表单数据
  const [formData, setFormData] = useState({
    feeRate: 5, // 默认5%
    depositAmount: 1000, // 默认1000 USDT
    introduction: '',
    experience: '',
    specializations: [] as string[],
    agreeToTerms: false
  })

  // 钱包连接状态
  const [wallet, setWallet] = useState<WalletConnection>({
    address: '',
    isConnected: false
  })

  // 验证状态
  const [verification, setVerification] = useState({
    walletVerified: false,
    signatureVerified: false,
    depositVerified: false
  })

  const specializationOptions = [
    '数字货币交易',
    '电子产品',
    '奢侈品',
    '艺术品收藏',
    '房地产',
    '汽车交易',
    '商业服务',
    '国际贸易'
  ]

  // 检查钱包连接状态
  useEffect(() => {
    checkWalletConnection()
  }, [])

  const checkWalletConnection = async () => {
    if (typeof window.ethereum !== 'undefined') {
      try {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' })
        if (accounts.length > 0) {
          const chainId = await window.ethereum.request({ method: 'eth_chainId' })
          setWallet({
            address: accounts[0],
            isConnected: true,
            chainId
          })
          setVerification(prev => ({ ...prev, walletVerified: true }))
        }
      } catch (error) {
        console.error('检查钱包连接失败:', error)
      }
    }
  }

  // 连接BNB钱包
  const connectWallet = async () => {
    try {
      setLoading(true)
      setError('')

      // 检查是否安装了MetaMask或其他钱包
      if (typeof window.ethereum === 'undefined') {
        throw new Error('请安装MetaMask或其他Web3钱包')
      }

      // 请求连接钱包
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length === 0) {
        throw new Error('未找到钱包账户')
      }

      // 检查网络是否为BNB Smart Chain
      const chainId = await window.ethereum.request({
        method: 'eth_chainId'
      })

      if (chainId !== '0x38') { // BSC Mainnet
        // 尝试切换到BSC网络
        try {
          await window.ethereum.request({
            method: 'wallet_switchEthereumChain',
            params: [{ chainId: '0x38' }]
          })
        } catch (switchError: any) {
          if (switchError.code === 4902) {
            // 网络不存在，添加BSC网络
            await window.ethereum.request({
              method: 'wallet_addEthereumChain',
              params: [{
                chainId: '0x38',
                chainName: 'BNB Smart Chain',
                nativeCurrency: {
                  name: 'BNB',
                  symbol: 'BNB',
                  decimals: 18
                },
                rpcUrls: ['https://bsc-dataseed.binance.org/'],
                blockExplorerUrls: ['https://bscscan.com/']
              }]
            })
          } else {
            throw switchError
          }
        }
      }

      // 获取余额
      const balance = await window.ethereum.request({
        method: 'eth_getBalance',
        params: [accounts[0], 'latest']
      })

      setWallet({
        address: accounts[0],
        isConnected: true,
        balance: parseInt(balance, 16) / Math.pow(10, 18), // 转换为BNB
        chainId: '0x38'
      })

      setVerification(prev => ({ ...prev, walletVerified: true }))
      setSuccess('钱包连接成功！')

    } catch (error: any) {
      console.error('钱包连接失败:', error)
      setError(error.message || '钱包连接失败')
    } finally {
      setLoading(false)
    }
  }

  // 签名验证
  const verifySignature = async () => {
    try {
      setLoading(true)
      setError('')

      if (!wallet.isConnected) {
        throw new Error('请先连接钱包')
      }

      const message = `BitMarket中间人认证\n钱包地址: ${wallet.address}\n时间: ${new Date().toISOString()}`
      
      const signature = await window.ethereum.request({
        method: 'personal_sign',
        params: [message, wallet.address]
      })

      // 调用后端API验证签名
      const response = await fetch('/api/mediator/verify-wallet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          address: wallet.address,
          message,
          signature
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setVerification(prev => ({ ...prev, signatureVerified: true }))
        setSuccess('签名验证成功！')
      } else {
        throw new Error(result.error || '签名验证失败')
      }

    } catch (error: any) {
      console.error('签名验证失败:', error)
      setError(error.message || '签名验证失败')
    } finally {
      setLoading(false)
    }
  }

  // 提交申请
  const submitApplication = async () => {
    try {
      setLoading(true)
      setError('')

      // 验证表单数据
      if (formData.feeRate < 1 || formData.feeRate > 30) {
        throw new Error('手续费率必须在1%-30%之间')
      }

      if (formData.depositAmount < 500) {
        throw new Error('保证金不能少于500 USDT')
      }

      if (!formData.introduction.trim()) {
        throw new Error('请填写自我介绍')
      }

      if (!formData.agreeToTerms) {
        throw new Error('请同意服务条款')
      }

      // 调用注册API
      const response = await fetch('/api/mediator/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          feeRate: formData.feeRate / 100, // 转换为小数
          depositAmount: formData.depositAmount,
          walletAddress: wallet.address,
          introduction: formData.introduction,
          experience: formData.experience,
          specializations: formData.specializations
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess('中间人申请已提交，等待管理员审核')
        setTimeout(() => {
          onSuccess?.()
        }, 2000)
      } else {
        throw new Error(result.error || '申请提交失败')
      }

    } catch (error: any) {
      console.error('申请提交失败:', error)
      setError(error.message || '申请提交失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSpecializationToggle = (spec: string) => {
    setFormData(prev => ({
      ...prev,
      specializations: prev.specializations.includes(spec)
        ? prev.specializations.filter(s => s !== spec)
        : [...prev.specializations, spec]
    }))
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <WalletIcon className="mx-auto h-12 w-12 text-blue-600" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">连接BNB钱包</h3>
        <p className="mt-1 text-sm text-gray-500">
          请连接您的BNB Smart Chain钱包以验证身份
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">钱包要求</h4>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>必须是BNB Smart Chain (BSC)网络</li>
                <li>钱包地址将用于接收托管费用</li>
                <li>需要进行签名验证以确认所有权</li>
                <li>建议钱包中保留少量BNB用于交易费用</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {!wallet.isConnected ? (
        <button
          onClick={connectWallet}
          disabled={loading}
          className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <WalletIcon className="h-5 w-5 mr-2" />
          {loading ? '连接中...' : '连接钱包'}
        </button>
      ) : (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-green-800">钱包已连接</h3>
              <div className="mt-2 text-sm text-green-700">
                <p className="flex items-center">
                  <span className="font-medium">地址:</span>
                  <span className="ml-2 font-mono">{wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}</span>
                  <LinkIcon className="h-4 w-4 ml-2" />
                </p>
                {wallet.balance !== undefined && (
                  <p><span className="font-medium">余额:</span> {wallet.balance.toFixed(4)} BNB</p>
                )}
                <p><span className="font-medium">网络:</span> BNB Smart Chain</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {wallet.isConnected && !verification.signatureVerified && (
        <button
          onClick={verifySignature}
          disabled={loading}
          className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
        >
          <DocumentTextIcon className="h-5 w-5 mr-2" />
          {loading ? '验证中...' : '签名验证'}
        </button>
      )}

      {verification.signatureVerified && (
        <button
          onClick={() => setStep(2)}
          className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          下一步：设置服务参数
        </button>
      )}
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CurrencyDollarIcon className="mx-auto h-12 w-12 text-green-600" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">设置服务参数</h3>
        <p className="mt-1 text-sm text-gray-500">
          配置您的中间人服务费率和保证金
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            服务费率 (1% - 30%)
          </label>
          <div className="mt-1 relative">
            <input
              type="number"
              min="1"
              max="30"
              step="0.1"
              value={formData.feeRate}
              onChange={(e) => setFormData(prev => ({ ...prev, feeRate: parseFloat(e.target.value) }))}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <span className="text-gray-500 text-sm">%</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500 space-y-1">
            <p>• 平台将从您的手续费中抽取30%作为平台费用</p>
            <p>• 您的实际收益率: {((formData.feeRate * 0.7)).toFixed(1)}%</p>
            <p>• 示例：1000 USDT订单，您收取{formData.feeRate}% = {formData.feeRate * 10} USDT，实得{(formData.feeRate * 10 * 0.7).toFixed(1)} USDT</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            保证金金额 (USDT)
          </label>
          <input
            type="number"
            min="500"
            step="100"
            value={formData.depositAmount}
            onChange={(e) => setFormData(prev => ({ ...prev, depositAmount: parseFloat(e.target.value) }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          <div className="mt-2 text-xs text-gray-500 space-y-1">
            <p>• 保证金用于担保您处理的订单，最低500 USDT</p>
            <p>• 您可以同时处理的订单总金额不能超过保证金金额</p>
            <p>• 保证金可以随时增加，但减少需要审核</p>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
            <div className="ml-3">
              <h4 className="text-sm font-medium text-yellow-800">重要提醒</h4>
              <div className="mt-2 text-sm text-yellow-700">
                <p>保证金将从您的平台账户余额中冻结，用于担保托管服务。请确保您的账户有足够的余额。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setStep(1)}
          className="flex-1 py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          上一步
        </button>
        <button
          onClick={() => setStep(3)}
          className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          下一步
        </button>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <StarIcon className="mx-auto h-12 w-12 text-purple-600" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">个人信息</h3>
        <p className="mt-1 text-sm text-gray-500">
          完善您的个人资料和专业领域
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            自我介绍 *
          </label>
          <textarea
            value={formData.introduction}
            onChange={(e) => setFormData(prev => ({ ...prev, introduction: e.target.value }))}
            rows={4}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="请介绍您的背景、经验和为什么想成为中间人..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            相关经验
          </label>
          <textarea
            value={formData.experience}
            onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}
            rows={3}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="请描述您在交易、仲裁或相关领域的经验..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            专业领域 (可选择多个)
          </label>
          <div className="grid grid-cols-2 gap-2">
            {specializationOptions.map((spec) => (
              <label
                key={spec}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                  formData.specializations.includes(spec)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                <input
                  type="checkbox"
                  checked={formData.specializations.includes(spec)}
                  onChange={() => handleSpecializationToggle(spec)}
                  className="sr-only"
                />
                <span className="text-sm">{spec}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="flex items-start">
          <input
            type="checkbox"
            checked={formData.agreeToTerms}
            onChange={(e) => setFormData(prev => ({ ...prev, agreeToTerms: e.target.checked }))}
            className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div className="ml-3">
            <label className="text-sm text-gray-700">
              我已阅读并同意 <a href="/terms" className="text-blue-600 hover:underline">服务条款</a> 和 <a href="/privacy" className="text-blue-600 hover:underline">隐私政策</a>
            </label>
          </div>
        </div>
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setStep(2)}
          className="flex-1 py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          上一步
        </button>
        <button
          onClick={submitApplication}
          disabled={loading || !formData.agreeToTerms}
          className="flex-1 py-2 px-4 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? '提交中...' : '提交申请'}
        </button>
      </div>
    </div>
  )

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* 进度指示器 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3].map((stepNumber) => (
            <div key={stepNumber} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNumber
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {stepNumber}
              </div>
              {stepNumber < 3 && (
                <div
                  className={`w-16 h-1 mx-2 ${
                    step > stepNumber ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          <span>钱包验证</span>
          <span>服务设置</span>
          <span>个人信息</span>
        </div>
      </div>

      {/* 错误和成功消息 */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* 步骤内容 */}
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}

      {/* 取消按钮 */}
      {onCancel && (
        <div className="mt-6 text-center">
          <button
            onClick={onCancel}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            取消申请
          </button>
        </div>
      )}
    </div>
  )
}
