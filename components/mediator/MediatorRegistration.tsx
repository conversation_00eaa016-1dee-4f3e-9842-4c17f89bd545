'use client'

import React, { useState } from 'react'
import { 
  ShieldCheckIcon, 
  CurrencyDollarIcon, 
  UserGroupIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface MediatorRegistrationProps {
  onSuccess?: () => void
  className?: string
}

export default function MediatorRegistration({
  onSuccess,
  className = ''
}: MediatorRegistrationProps) {
  const [formData, setFormData] = useState({
    walletAddress: '',
    feeRate: 5, // 默认5%
    depositAmount: 1000, // 默认1000 USDT
    introduction: '',
    experience: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/mediator/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          feeRate: formData.feeRate / 100 // 转换为小数
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess(true)
        onSuccess?.()
      } else {
        setError(data.error || '申请失败')
      }
    } catch (error) {
      setError('网络错误，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (success) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center">
          <CheckCircleIcon className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            申请已提交
          </h3>
          <p className="text-gray-600 mb-4">
            您的中间人申请已成功提交，我们将在1-3个工作日内完成审核。
          </p>
          <p className="text-sm text-gray-500">
            审核期间请确保您的钱包地址有足够的保证金，并保持联系方式畅通。
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center mb-6">
        <ShieldCheckIcon className="h-6 w-6 text-blue-600 mr-3" />
        <h2 className="text-xl font-semibold text-gray-900">申请成为中间人</h2>
      </div>

      {/* 要求说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
        <h3 className="text-sm font-medium text-blue-800 mb-2">成为中间人的要求</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 拥有已验证的 BNB Chain 钱包地址</li>
          <li>• 最低保证金要求：1000 USDT</li>
          <li>• 服务费率范围：1% - 30%</li>
          <li>• 承诺公正处理交易争议</li>
          <li>• 积极参与仲裁投票</li>
        </ul>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 钱包地址 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            BNB Chain 钱包地址 *
          </label>
          <input
            type="text"
            value={formData.walletAddress}
            onChange={(e) => handleInputChange('walletAddress', e.target.value)}
            placeholder="0x..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            请确保钱包地址正确，这将用于接收和发送托管资金
          </p>
        </div>

        {/* 服务费率 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            服务费率 * ({formData.feeRate}%)
          </label>
          <div className="flex items-center space-x-4">
            <input
              type="range"
              min="1"
              max="30"
              value={formData.feeRate}
              onChange={(e) => handleInputChange('feeRate', parseInt(e.target.value))}
              className="flex-1"
            />
            <div className="flex items-center space-x-2">
              <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-900">
                {formData.feeRate}%
              </span>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            较低的费率更容易获得订单，但收益也相对较少
          </p>
        </div>

        {/* 保证金金额 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            保证金金额 (USDT) *
          </label>
          <input
            type="number"
            min="1000"
            step="100"
            value={formData.depositAmount}
            onChange={(e) => handleInputChange('depositAmount', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            保证金用于承担托管责任，最低1000 USDT。保证金越高，可承接的订单金额越大
          </p>
        </div>

        {/* 个人介绍 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            个人介绍
          </label>
          <textarea
            value={formData.introduction}
            onChange={(e) => handleInputChange('introduction', e.target.value)}
            rows={3}
            placeholder="简单介绍您的背景和优势..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* 相关经验 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            相关经验
          </label>
          <textarea
            value={formData.experience}
            onChange={(e) => handleInputChange('experience', e.target.value)}
            rows={3}
            placeholder="描述您在电商、金融或争议处理方面的经验..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* 费用计算示例 */}
        <div className="bg-gray-50 rounded-md p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">收益示例</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>1000 USDT 订单服务费:</span>
              <span>{(1000 * formData.feeRate / 100).toFixed(2)} USDT</span>
            </div>
            <div className="flex justify-between">
              <span>平台抽成 (30%):</span>
              <span>-{(1000 * formData.feeRate / 100 * 0.3).toFixed(2)} USDT</span>
            </div>
            <div className="flex justify-between font-medium">
              <span>您的净收益:</span>
              <span>{(1000 * formData.feeRate / 100 * 0.7).toFixed(2)} USDT</span>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? '提交中...' : '提交申请'}
        </button>
      </form>

      <div className="mt-6 text-xs text-gray-500">
        <p>提交申请后，我们将在1-3个工作日内完成审核。审核通过后，您需要向指定地址转入保证金以激活中间人身份。</p>
      </div>
    </div>
  )
}
