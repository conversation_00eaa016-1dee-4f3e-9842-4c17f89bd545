'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calculator, 
  TrendingDown, 
  Clock, 
  Star,
  Gift,
  ArrowRight,
  Lightbulb,
  DollarSign
} from 'lucide-react'

interface FeeCalculatorData {
  calculator: {
    tiers: Array<{
      range: string
      feeStructure: string
      example: {
        amount: number
        fee: number
        actualReceived: number
      }
    }>
    userLevel: string
    userDiscount: number
    recommendations: string[]
  }
  tiers: Array<{
    minAmount: number
    maxAmount: number
    feeType: string
    feeValue: number
    description: string
  }>
  tips: string[]
}

interface FeeEstimate {
  amount: number
  originalFee: number
  finalFee: number
  savings: number
  savingsRate: number
  tier: string
  actualReceived: number
  effectiveRate: number
}

export default function WithdrawalFeeCalculator() {
  const [data, setData] = useState<FeeCalculatorData | null>(null)
  const [amount, setAmount] = useState<string>('100')
  const [estimate, setEstimate] = useState<FeeEstimate | null>(null)
  const [loading, setLoading] = useState(true)
  const [calculating, setCalculating] = useState(false)

  useEffect(() => {
    fetchCalculatorData()
  }, [])

  useEffect(() => {
    if (amount && parseFloat(amount) > 0) {
      calculateFee()
    }
  }, [amount])

  const fetchCalculatorData = async () => {
    try {
      const response = await fetch('/api/reform/withdrawal-calculator')
      if (response.ok) {
        const result = await response.json()
        setData(result)
      }
    } catch (error) {
      console.error('获取计算器数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateFee = async () => {
    if (!amount || parseFloat(amount) <= 0) return

    setCalculating(true)
    try {
      const response = await fetch(`/api/reform/withdrawal-calculator?action=estimate&amount=${amount}`)
      if (response.ok) {
        const result = await response.json()
        setEstimate({
          amount: result.amount,
          originalFee: result.normalWithdrawal.originalFee,
          finalFee: result.normalWithdrawal.finalFee,
          savings: result.normalWithdrawal.savings,
          savingsRate: result.normalWithdrawal.savingsRate,
          tier: result.normalWithdrawal.tier,
          actualReceived: result.amount - result.normalWithdrawal.finalFee,
          effectiveRate: (result.normalWithdrawal.finalFee / result.amount) * 100
        })
      }
    } catch (error) {
      console.error('计算手续费失败:', error)
    } finally {
      setCalculating(false)
    }
  }

  const getTierColor = (feeType: string) => {
    switch (feeType) {
      case 'FIXED': return 'bg-blue-100 text-blue-800'
      case 'PERCENTAGE': return 'bg-green-100 text-green-800'
      case 'FREE': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTierIcon = (feeType: string) => {
    switch (feeType) {
      case 'FIXED': return <DollarSign className="h-4 w-4" />
      case 'PERCENTAGE': return <TrendingDown className="h-4 w-4" />
      case 'FREE': return <Gift className="h-4 w-4" />
      default: return <Calculator className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Calculator className="h-6 w-6 animate-pulse" />
        <span className="ml-2">加载计算器...</span>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">无法加载计算器数据</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 改革亮点横幅 */}
      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-blue-50">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Gift className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-bold text-green-800">🎉 手续费改革上线！</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span>交易零手续费</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingDown className="h-4 w-4 text-blue-500" />
              <span>阶梯式提现费率</span>
            </div>
            <div className="flex items-center space-x-2">
              <Gift className="h-4 w-4 text-purple-500" />
              <span>大额提现免费</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="calculator" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="calculator">费用计算</TabsTrigger>
          <TabsTrigger value="tiers">费率档位</TabsTrigger>
          <TabsTrigger value="tips">优化建议</TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>提现费用计算器</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">提现金额 (USDT)</label>
                <Input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="请输入提现金额"
                  min="0"
                  step="0.01"
                />
              </div>

              {estimate && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="text-sm text-gray-600">手续费</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {estimate.finalFee.toFixed(4)} USDT
                      </div>
                      <div className="text-xs text-gray-500">
                        有效费率: {estimate.effectiveRate.toFixed(3)}%
                      </div>
                    </div>

                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="text-sm text-gray-600">实际到账</div>
                      <div className="text-2xl font-bold text-green-600">
                        {estimate.actualReceived.toFixed(2)} USDT
                      </div>
                      <div className="text-xs text-gray-500">
                        {estimate.tier}
                      </div>
                    </div>
                  </div>

                  {estimate.savings > 0 && (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Star className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium text-yellow-800">
                          已为您节省 {estimate.savings.toFixed(4)} USDT ({estimate.savingsRate.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-600">选择批量提现时间</span>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">12:00 或 20:00</span>
                      <Badge variant="secondary">额外10%折扣</Badge>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tiers" className="space-y-4">
          <div className="grid gap-4">
            {data.tiers.map((tier, index) => (
              <Card key={index} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${getTierColor(tier.feeType)}`}>
                        {getTierIcon(tier.feeType)}
                      </div>
                      <div>
                        <div className="font-medium">
                          {tier.maxAmount === Infinity ? 
                            `${tier.minAmount}+ USDT` : 
                            `${tier.minAmount}-${tier.maxAmount} USDT`
                          }
                        </div>
                        <div className="text-sm text-gray-500">{tier.description}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">
                        {tier.feeType === 'FIXED' && `${tier.feeValue} USDT`}
                        {tier.feeType === 'PERCENTAGE' && `${(tier.feeValue * 100).toFixed(1)}%`}
                        {tier.feeType === 'FREE' && '免费'}
                      </div>
                      <Badge variant={tier.feeType === 'FREE' ? 'default' : 'secondary'}>
                        {tier.feeType === 'FREE' ? '🎉 免手续费' : '优惠费率'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="tips" className="space-y-4">
          <div className="grid gap-4">
            {data.tips.map((tip, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <span className="text-sm">{tip}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {data.calculator.recommendations.length > 0 && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-blue-800">个性化建议</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {data.calculator.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <ArrowRight className="h-4 w-4 text-blue-600 mt-0.5" />
                      <span className="text-sm text-blue-700">{rec}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="text-center">
                <h4 className="font-medium text-green-800 mb-2">
                  您的信用等级: {data.calculator.userLevel}
                </h4>
                <p className="text-sm text-green-700">
                  当前享受 {(data.calculator.userDiscount * 100).toFixed(0)}% 手续费折扣
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  查看等级权益
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
