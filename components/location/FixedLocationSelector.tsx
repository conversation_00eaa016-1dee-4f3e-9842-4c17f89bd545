'use client'

import React, { useState, useEffect, useMemo, useRef } from 'react'
import { ChevronDownIcon, MagnifyingGlassIcon, MapPinIcon } from '@heroicons/react/24/outline'
import { 
  locationData, 
  getAllCities, 
  getDistrictsByCity, 
  searchCities, 
  searchDistricts,
  validateLocation,
  type City,
  type District 
} from '@/lib/location-data'

interface FixedLocationSelectorProps {
  selectedCity?: string
  selectedDistrict?: string
  onLocationChange: (city: string, district?: string) => void
  placeholder?: {
    city?: string
    district?: string
  }
  required?: boolean
  disabled?: boolean
  className?: string
  showSearch?: boolean
}

export default function FixedLocationSelector({
  selectedCity = '',
  selectedDistrict = '',
  onLocationChange,
  placeholder = {
    city: '选择城市',
    district: '选择区县'
  },
  required = false,
  disabled = false,
  className = '',
  showSearch = true
}: FixedLocationSelectorProps) {
  const [citySearchQuery, setCitySearchQuery] = useState('')
  const [districtSearchQuery, setDistrictSearchQuery] = useState('')
  const [showCityDropdown, setShowCityDropdown] = useState(false)
  const [showDistrictDropdown, setShowDistrictDropdown] = useState(false)
  
  // 用于检测下拉列表位置的refs
  const cityContainerRef = useRef<HTMLDivElement>(null)
  const districtContainerRef = useRef<HTMLDivElement>(null)

  // 获取城市选项
  const cityOptions = useMemo(() => {
    if (citySearchQuery.trim()) {
      return searchCities(citySearchQuery)
    }
    return getAllCities()
  }, [citySearchQuery])

  // 获取区县选项
  const districtOptions = useMemo(() => {
    if (!selectedCity) return []
    
    if (districtSearchQuery.trim()) {
      return searchDistricts(districtSearchQuery, selectedCity)
    }
    return getDistrictsByCity(selectedCity)
  }, [selectedCity, districtSearchQuery])

  // 智能定位下拉列表位置
  const getDropdownPosition = (containerRef: React.RefObject<HTMLDivElement | null>) => {
    if (!containerRef.current) return { dropUp: false, zIndex: 9999 }
    
    const rect = containerRef.current.getBoundingClientRect()
    const viewportHeight = window.innerHeight
    const spaceBelow = viewportHeight - rect.bottom
    const spaceAbove = rect.top
    
    // 如果下方空间不足320px且上方空间更大，则向上展开
    const shouldDropUp = spaceBelow < 320 && spaceAbove > spaceBelow
    
    return {
      dropUp: shouldDropUp,
      zIndex: 9999
    }
  }

  // 处理城市选择
  const handleCitySelect = (city: City) => {
    onLocationChange(city.name, '')
    setCitySearchQuery('')
    setShowCityDropdown(false)
    setDistrictSearchQuery('')
  }

  // 处理区县选择
  const handleDistrictSelect = (district: District) => {
    onLocationChange(selectedCity, district.name)
    setDistrictSearchQuery('')
    setShowDistrictDropdown(false)
  }

  // 清除选择
  const handleClearCity = () => {
    onLocationChange('', '')
    setCitySearchQuery('')
    setDistrictSearchQuery('')
  }

  const handleClearDistrict = () => {
    onLocationChange(selectedCity, '')
    setDistrictSearchQuery('')
  }

  // 获取城市显示文本
  const getCityDisplayText = () => {
    if (selectedCity) return selectedCity
    if (citySearchQuery) return citySearchQuery
    return placeholder.city
  }

  // 获取区县显示文本
  const getDistrictDisplayText = () => {
    if (selectedDistrict) return selectedDistrict
    if (districtSearchQuery) return districtSearchQuery
    return placeholder.district
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 城市选择器 */}
      <div className="relative" ref={cityContainerRef}>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          城市 {required && <span className="text-red-500">*</span>}
        </label>
        
        <div className="relative">
          <div className="flex">
            {showSearch ? (
              <input
                type="text"
                value={citySearchQuery || selectedCity}
                onChange={(e) => {
                  setCitySearchQuery(e.target.value)
                  setShowCityDropdown(true)
                  if (!e.target.value && selectedCity) {
                    handleClearCity()
                  }
                }}
                onFocus={() => setShowCityDropdown(true)}
                placeholder={placeholder.city}
                disabled={disabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              />
            ) : (
              <button
                type="button"
                onClick={() => setShowCityDropdown(!showCityDropdown)}
                disabled={disabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
              >
                <span className={selectedCity ? 'text-gray-900' : 'text-gray-500'}>
                  {getCityDisplayText()}
                </span>
                <ChevronDownIcon className="h-4 w-4 text-gray-400" />
              </button>
            )}
            
            {selectedCity && (
              <button
                type="button"
                onClick={handleClearCity}
                disabled={disabled}
                className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                ✕
              </button>
            )}
          </div>

          {/* 城市下拉列表 */}
          {showCityDropdown && !disabled && (
            <div 
              className={`absolute w-full bg-white border border-gray-300 rounded-md shadow-xl max-h-80 overflow-auto ${
                getDropdownPosition(cityContainerRef).dropUp 
                  ? 'bottom-full mb-1' 
                  : 'top-full mt-1'
              }`}
              style={{ 
                zIndex: getDropdownPosition(cityContainerRef).zIndex,
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
              }}
            >
              {cityOptions.length > 0 ? (
                <div className="py-1">
                  {cityOptions.map((city) => (
                    <button
                      key={city.code}
                      type="button"
                      onClick={() => handleCitySelect(city)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center space-x-2 transition-colors"
                    >
                      <MapPinIcon className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{city.name}</div>
                        <div className="text-xs text-gray-500">{city.province}</div>
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  未找到匹配的城市
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 区县选择器 */}
      {selectedCity && (
        <div className="relative" ref={districtContainerRef}>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            区县
          </label>
          
          <div className="relative">
            <div className="flex">
              {showSearch ? (
                <input
                  type="text"
                  value={districtSearchQuery || selectedDistrict}
                  onChange={(e) => {
                    setDistrictSearchQuery(e.target.value)
                    setShowDistrictDropdown(true)
                    if (!e.target.value && selectedDistrict) {
                      handleClearDistrict()
                    }
                  }}
                  onFocus={() => setShowDistrictDropdown(true)}
                  placeholder={placeholder.district}
                  disabled={disabled}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                />
              ) : (
                <button
                  type="button"
                  onClick={() => setShowDistrictDropdown(!showDistrictDropdown)}
                  disabled={disabled}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
                >
                  <span className={selectedDistrict ? 'text-gray-900' : 'text-gray-500'}>
                    {getDistrictDisplayText()}
                  </span>
                  <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                </button>
              )}
              
              {selectedDistrict && (
                <button
                  type="button"
                  onClick={handleClearDistrict}
                  disabled={disabled}
                  className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  ✕
                </button>
              )}
            </div>

            {/* 区县下拉列表 */}
            {showDistrictDropdown && !disabled && (
              <div 
                className={`absolute w-full bg-white border border-gray-300 rounded-md shadow-xl max-h-80 overflow-auto ${
                  getDropdownPosition(districtContainerRef).dropUp 
                    ? 'bottom-full mb-1' 
                    : 'top-full mt-1'
                }`}
                style={{ 
                  zIndex: getDropdownPosition(districtContainerRef).zIndex,
                  boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                }}
              >
                {districtOptions.length > 0 ? (
                  <div className="py-1">
                    {districtOptions.map((district) => (
                      <button
                        key={district.code}
                        type="button"
                        onClick={() => handleDistrictSelect(district)}
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center space-x-2 transition-colors"
                      >
                        <div className="text-sm text-gray-900">{district.name}</div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="px-3 py-2 text-sm text-gray-500">
                    未找到匹配的区县
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 点击外部关闭下拉列表 */}
      {(showCityDropdown || showDistrictDropdown) && (
        <div
          className="fixed inset-0"
          style={{ zIndex: 9998 }}
          onClick={() => {
            setShowCityDropdown(false)
            setShowDistrictDropdown(false)
          }}
        />
      )}
    </div>
  )
}
