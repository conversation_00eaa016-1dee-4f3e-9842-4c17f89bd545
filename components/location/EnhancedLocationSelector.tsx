'use client'

import React, { useState, useEffect, useMemo, useRef } from 'react'
import { ChevronDownIcon, MagnifyingGlassIcon, MapPinIcon } from '@heroicons/react/24/outline'
import { 
  chinaLocationData, 
  getAllProvinces,
  getAllCities, 
  getCitiesByProvince,
  getDistrictsByCity, 
  searchProvinces,
  searchCities, 
  searchDistricts,
  validateLocation,
  getFullAddress,
  type Province,
  type City,
  type District 
} from '@/lib/china-location-data'

interface EnhancedLocationSelectorProps {
  selectedProvince?: string
  selectedCity?: string
  selectedDistrict?: string
  onLocationChange: (province?: string, city?: string, district?: string) => void
  placeholder?: {
    province?: string
    city?: string
    district?: string
  }
  required?: boolean
  disabled?: boolean
  className?: string
  showSearch?: boolean
  layout?: 'horizontal' | 'vertical'
  showFullAddress?: boolean
  level?: 'province' | 'city' | 'district' // 显示到哪一级
}

export default function EnhancedLocationSelector({
  selectedProvince = '',
  selectedCity = '',
  selectedDistrict = '',
  onLocationChange,
  placeholder = {
    province: '选择省份',
    city: '选择城市',
    district: '选择区县'
  },
  required = false,
  disabled = false,
  className = '',
  showSearch = true,
  layout = 'horizontal',
  showFullAddress = true,
  level = 'district'
}: EnhancedLocationSelectorProps) {
  const [provinceSearchQuery, setProvinceSearchQuery] = useState('')
  const [citySearchQuery, setCitySearchQuery] = useState('')
  const [districtSearchQuery, setDistrictSearchQuery] = useState('')
  const [showProvinceDropdown, setShowProvinceDropdown] = useState(false)
  const [showCityDropdown, setShowCityDropdown] = useState(false)
  const [showDistrictDropdown, setShowDistrictDropdown] = useState(false)

  // 获取省份选项
  const provinceOptions = useMemo(() => {
    if (provinceSearchQuery.trim()) {
      return searchProvinces(provinceSearchQuery)
    }
    return getAllProvinces()
  }, [provinceSearchQuery])

  // 获取城市选项
  const cityOptions = useMemo(() => {
    if (!selectedProvince) return []
    
    if (citySearchQuery.trim()) {
      return searchCities(citySearchQuery, selectedProvince)
    }
    return getCitiesByProvince(selectedProvince)
  }, [selectedProvince, citySearchQuery])

  // 获取区县选项
  const districtOptions = useMemo(() => {
    if (!selectedCity) return []
    
    if (districtSearchQuery.trim()) {
      return searchDistricts(districtSearchQuery, selectedCity)
    }
    return getDistrictsByCity(selectedCity)
  }, [selectedCity, districtSearchQuery])

  // 处理省份选择
  const handleProvinceSelect = (province: Province) => {
    onLocationChange(province.name, '', '')
    setProvinceSearchQuery('')
    setShowProvinceDropdown(false)
    setCitySearchQuery('')
    setDistrictSearchQuery('')
  }

  // 处理城市选择
  const handleCitySelect = (city: City) => {
    onLocationChange(selectedProvince, city.name, '')
    setCitySearchQuery('')
    setShowCityDropdown(false)
    setDistrictSearchQuery('')
  }

  // 处理区县选择
  const handleDistrictSelect = (district: District) => {
    onLocationChange(selectedProvince, selectedCity, district.name)
    setDistrictSearchQuery('')
    setShowDistrictDropdown(false)
  }

  // 清除选择
  const handleClearProvince = () => {
    onLocationChange('', '', '')
    setProvinceSearchQuery('')
    setCitySearchQuery('')
    setDistrictSearchQuery('')
  }

  const handleClearCity = () => {
    onLocationChange(selectedProvince, '', '')
    setCitySearchQuery('')
    setDistrictSearchQuery('')
  }

  const handleClearDistrict = () => {
    onLocationChange(selectedProvince, selectedCity, '')
    setDistrictSearchQuery('')
  }

  // 获取显示文本
  const getProvinceDisplayText = (): string => {
    if (selectedProvince) return selectedProvince
    if (provinceSearchQuery) return provinceSearchQuery
    return placeholder.province || '选择省份'
  }

  const getCityDisplayText = (): string => {
    if (selectedCity) return selectedCity
    if (citySearchQuery) return citySearchQuery
    return placeholder.city || '选择城市'
  }

  const getDistrictDisplayText = (): string => {
    if (selectedDistrict) return selectedDistrict
    if (districtSearchQuery) return districtSearchQuery
    return placeholder.district || '选择区县'
  }

  // 渲染选择器
  const renderSelector = (
    type: 'province' | 'city' | 'district',
    options: any[],
    selectedValue: string,
    searchQuery: string,
    setSearchQuery: (value: string) => void,
    showDropdown: boolean,
    setShowDropdown: (show: boolean) => void,
    onSelect: (option: any) => void,
    onClear: () => void,
    displayText: string,
    placeholderText?: string
  ) => {
    const isDisabled = disabled || (type === 'city' && !selectedProvince) || (type === 'district' && !selectedCity)
    
    return (
      <div className="relative flex-1">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {type === 'province' ? '省份' : type === 'city' ? '城市' : '区县'}
          {required && type === 'province' && <span className="text-red-500">*</span>}
        </label>
        
        <div className="relative">
          <div className="flex">
            {showSearch ? (
              <input
                type="text"
                value={searchQuery || selectedValue}
                onChange={(e) => {
                  setSearchQuery(e.target.value)
                  setShowDropdown(true)
                  if (!e.target.value && selectedValue) {
                    onClear()
                  }
                }}
                onFocus={() => setShowDropdown(true)}
                placeholder={placeholderText}
                disabled={isDisabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              />
            ) : (
              <button
                type="button"
                onClick={() => setShowDropdown(!showDropdown)}
                disabled={isDisabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md text-left focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 flex items-center justify-between"
              >
                <span className={selectedValue ? 'text-gray-900' : 'text-gray-500'}>
                  {displayText}
                </span>
                <ChevronDownIcon className="h-4 w-4 text-gray-400" />
              </button>
            )}
            
            {selectedValue && (
              <button
                type="button"
                onClick={onClear}
                disabled={isDisabled}
                className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                ✕
              </button>
            )}
          </div>

          {/* 下拉列表 */}
          {showDropdown && !isDisabled && (
            <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
              {options.length > 0 ? (
                <div className="py-1">
                  {options.map((option) => (
                    <button
                      key={option.code}
                      type="button"
                      onClick={() => onSelect(option)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center space-x-2"
                    >
                      <MapPinIcon className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{option.name}</div>
                        {type === 'city' && (
                          <div className="text-xs text-gray-500">{option.province}</div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  未找到匹配的{type === 'province' ? '省份' : type === 'city' ? '城市' : '区县'}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 完整地址显示 */}
      {showFullAddress && (selectedProvince || selectedCity || selectedDistrict) && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="text-sm font-medium text-blue-800">选择的地址：</div>
          <div className="text-blue-700">
            {getFullAddress(selectedProvince, selectedCity, selectedDistrict)}
          </div>
        </div>
      )}

      {/* 选择器布局 */}
      <div className={layout === 'horizontal' ? 'flex space-x-4' : 'space-y-4'}>
        {/* 省份选择器 */}
        {renderSelector(
          'province',
          provinceOptions,
          selectedProvince,
          provinceSearchQuery,
          setProvinceSearchQuery,
          showProvinceDropdown,
          setShowProvinceDropdown,
          handleProvinceSelect,
          handleClearProvince,
          getProvinceDisplayText(),
          placeholder.province
        )}

        {/* 城市选择器 */}
        {(level === 'city' || level === 'district') && renderSelector(
          'city',
          cityOptions,
          selectedCity,
          citySearchQuery,
          setCitySearchQuery,
          showCityDropdown,
          setShowCityDropdown,
          handleCitySelect,
          handleClearCity,
          getCityDisplayText(),
          placeholder.city
        )}

        {/* 区县选择器 */}
        {level === 'district' && renderSelector(
          'district',
          districtOptions,
          selectedDistrict,
          districtSearchQuery,
          setDistrictSearchQuery,
          showDistrictDropdown,
          setShowDistrictDropdown,
          handleDistrictSelect,
          handleClearDistrict,
          getDistrictDisplayText(),
          placeholder.district
        )}
      </div>

      {/* 点击外部关闭下拉列表 */}
      {(showProvinceDropdown || showCityDropdown || showDistrictDropdown) && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => {
            setShowProvinceDropdown(false)
            setShowCityDropdown(false)
            setShowDistrictDropdown(false)
          }}
        />
      )}
    </div>
  )
}
