'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MapPin, 
  Navigation, 
  ZoomIn,
  ZoomOut,
  Layers,
  Target,
  Info,
  X
} from 'lucide-react'

interface MapViewProps {
  center: {
    latitude: number
    longitude: number
  }
  items: Array<{
    id: string
    title: string
    latitude: number
    longitude: number
    price?: number
    budget?: number
    type: 'product' | 'demand'
    distance: number
    distanceText: string
    item: any
  }>
  onItemClick?: (item: any) => void
  searchRadius?: number
  className?: string
}

// 模拟地图API（实际项目中应该使用高德地图或百度地图）
interface MapInstance {
  setCenter: (lat: number, lng: number) => void
  setZoom: (zoom: number) => void
  addMarker: (marker: any) => void
  removeMarker: (markerId: string) => void
  addCircle: (circle: any) => void
  removeCircle: (circleId: string) => void
}

export default function MapView({
  center,
  items,
  onItemClick,
  searchRadius = 5,
  className = ''
}: MapViewProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [mapInstance, setMapInstance] = useState<MapInstance | null>(null)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [zoom, setZoom] = useState(13)
  const [showSatellite, setShowSatellite] = useState(false)

  // 初始化地图
  useEffect(() => {
    if (mapRef.current && !mapInstance) {
      initializeMap()
    }
  }, [])

  // 更新地图中心和标记
  useEffect(() => {
    if (mapInstance && mapLoaded) {
      updateMapContent()
    }
  }, [mapInstance, mapLoaded, center, items, searchRadius])

  const initializeMap = async () => {
    try {
      // 模拟地图初始化（实际应该加载地图SDK）
      const mockMapInstance: MapInstance = {
        setCenter: (lat: number, lng: number) => {
          console.log(`地图中心设置为: ${lat}, ${lng}`)
        },
        setZoom: (zoom: number) => {
          console.log(`地图缩放级别设置为: ${zoom}`)
        },
        addMarker: (marker: any) => {
          console.log('添加标记:', marker)
        },
        removeMarker: (markerId: string) => {
          console.log('移除标记:', markerId)
        },
        addCircle: (circle: any) => {
          console.log('添加圆形:', circle)
        },
        removeCircle: (circleId: string) => {
          console.log('移除圆形:', circleId)
        }
      }

      setMapInstance(mockMapInstance)
      setMapLoaded(true)

      // 模拟地图加载延迟
      setTimeout(() => {
        console.log('地图加载完成')
      }, 1000)

    } catch (error) {
      console.error('地图初始化失败:', error)
    }
  }

  const updateMapContent = () => {
    if (!mapInstance) return

    // 设置地图中心
    mapInstance.setCenter(center.latitude, center.longitude)
    mapInstance.setZoom(zoom)

    // 添加搜索范围圆圈
    mapInstance.addCircle({
      id: 'search-radius',
      center: [center.latitude, center.longitude],
      radius: searchRadius * 1000, // 转换为米
      fillColor: 'rgba(59, 130, 246, 0.1)',
      strokeColor: '#3b82f6',
      strokeWidth: 2
    })

    // 添加中心标记
    mapInstance.addMarker({
      id: 'center',
      position: [center.latitude, center.longitude],
      icon: 'center',
      title: '搜索中心'
    })

    // 添加商品/需求标记
    items.forEach(item => {
      mapInstance.addMarker({
        id: item.id,
        position: [item.latitude, item.longitude],
        icon: item.type === 'product' ? 'product' : 'demand',
        title: item.title,
        data: item
      })
    })
  }

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 1, 18)
    setZoom(newZoom)
    mapInstance?.setZoom(newZoom)
  }

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 1, 3)
    setZoom(newZoom)
    mapInstance?.setZoom(newZoom)
  }

  const handleRecenter = () => {
    mapInstance?.setCenter(center.latitude, center.longitude)
    setZoom(13)
    mapInstance?.setZoom(13)
  }

  const handleItemSelect = (item: any) => {
    setSelectedItem(item)
    mapInstance?.setCenter(item.latitude, item.longitude)
    onItemClick?.(item)
  }

  const getMarkerColor = (type: string) => {
    return type === 'product' ? 'bg-blue-500' : 'bg-green-500'
  }

  const getMarkerIcon = (type: string) => {
    return type === 'product' ? '🛍️' : '📋'
  }

  return (
    <div className={`relative ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>地图视图</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {items.length} 个结果
              </Badge>
              <Badge variant="outline">
                {searchRadius}km 范围
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="relative">
            {/* 地图容器 */}
            <div 
              ref={mapRef}
              className="h-96 bg-gradient-to-br from-blue-50 to-green-50 relative overflow-hidden"
            >
              {/* 模拟地图背景 */}
              <div className="absolute inset-0 bg-gray-100">
                <div className="absolute inset-0 opacity-20">
                  <div className="grid grid-cols-8 grid-rows-6 h-full">
                    {Array.from({ length: 48 }).map((_, i) => (
                      <div 
                        key={i} 
                        className={`border border-gray-300 ${
                          Math.random() > 0.7 ? 'bg-green-100' : 
                          Math.random() > 0.8 ? 'bg-blue-100' : 'bg-gray-50'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* 搜索范围圆圈 */}
              <div 
                className="absolute border-2 border-blue-400 border-dashed rounded-full bg-blue-100 bg-opacity-20"
                style={{
                  width: `${Math.min(searchRadius * 20, 300)}px`,
                  height: `${Math.min(searchRadius * 20, 300)}px`,
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
              />

              {/* 中心标记 */}
              <div 
                className="absolute transform -translate-x-1/2 -translate-y-1/2"
                style={{ left: '50%', top: '50%' }}
              >
                <div className="w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                  <Target className="h-3 w-3 text-white" />
                </div>
              </div>

              {/* 商品/需求标记 */}
              {items.map((item, index) => {
                const angle = (index / items.length) * 2 * Math.PI
                const radius = Math.min(item.distance * 15, 120)
                const x = 50 + (radius * Math.cos(angle)) / 3.84 // 转换为百分比
                const y = 50 + (radius * Math.sin(angle)) / 3.84
                
                return (
                  <div
                    key={item.id}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                    style={{ 
                      left: `${Math.max(10, Math.min(90, x))}%`, 
                      top: `${Math.max(10, Math.min(90, y))}%` 
                    }}
                    onClick={() => handleItemSelect(item)}
                  >
                    <div className={`
                      w-8 h-8 rounded-full border-2 border-white shadow-lg 
                      flex items-center justify-center text-white text-xs font-bold
                      hover:scale-110 transition-transform
                      ${getMarkerColor(item.type)}
                      ${selectedItem?.id === item.id ? 'ring-2 ring-yellow-400' : ''}
                    `}>
                      {getMarkerIcon(item.type)}
                    </div>
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                      <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                        {item.distanceText}
                      </div>
                    </div>
                  </div>
                )
              })}

              {/* 地图控制按钮 */}
              <div className="absolute top-4 right-4 flex flex-col space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomIn}
                  className="bg-white shadow-md"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomOut}
                  className="bg-white shadow-md"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRecenter}
                  className="bg-white shadow-md"
                >
                  <Navigation className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSatellite(!showSatellite)}
                  className="bg-white shadow-md"
                >
                  <Layers className="h-4 w-4" />
                </Button>
              </div>

              {/* 缩放级别显示 */}
              <div className="absolute bottom-4 left-4">
                <Badge variant="outline" className="bg-white">
                  缩放: {zoom}
                </Badge>
              </div>

              {/* 加载状态 */}
              {!mapLoaded && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p className="text-sm text-gray-600">地图加载中...</p>
                  </div>
                </div>
              )}
            </div>

            {/* 选中项目详情 */}
            {selectedItem && (
              <div className="absolute bottom-4 left-4 right-4">
                <Card className="shadow-lg">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge variant={selectedItem.type === 'product' ? 'default' : 'secondary'}>
                            {selectedItem.type === 'product' ? '商品' : '需求'}
                          </Badge>
                          <Badge variant="outline">
                            {selectedItem.distanceText}
                          </Badge>
                        </div>
                        <h4 className="font-medium text-sm mb-1">{selectedItem.title}</h4>
                        <p className="text-sm text-gray-600">
                          {selectedItem.type === 'product' ? '价格' : '预算'}:
                          <span className="font-medium ml-1">
                            {(selectedItem.price || selectedItem.budget || 0).toFixed(2)} USDT
                          </span>
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedItem(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 地图说明 */}
      <Card className="mt-4">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-500 mt-0.5" />
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">地图说明：</p>
              <div className="space-y-1">
                <p>• 🎯 红色标记：搜索中心位置</p>
                <p>• 🛍️ 蓝色标记：商品位置</p>
                <p>• 📋 绿色标记：需求位置</p>
                <p>• 虚线圆圈：搜索范围</p>
                <p>• 点击标记查看详细信息</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
