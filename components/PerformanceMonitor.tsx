'use client'

import { useState, useEffect } from 'react'
import { performanceMonitor } from '@/lib/performance-monitor'

interface PerformanceStats {
  general: {
    avg: number
    min: number
    max: number
    p95: number
    p99: number
  }
  database: {
    avg: number
    min: number
    max: number
    p95: number
    p99: number
  }
  api: {
    avg: number
    min: number
    max: number
    p95: number
    p99: number
  }
  counts: {
    totalRequests: number
    dbQueries: number
    apiCalls: number
  }
  slowQueries: number
  slowApis: number
}

export default function PerformanceMonitor() {
  const [stats, setStats] = useState<PerformanceStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  useEffect(() => {
    const fetchStats = () => {
      try {
        const currentStats = performanceMonitor.getStats()
        setStats(currentStats)
        setLastUpdate(new Date())
        setIsLoading(false)
      } catch (error) {
        console.error('获取性能统计失败:', error)
        setIsLoading(false)
      }
    }

    // 初始加载
    fetchStats()

    // 每10秒更新一次
    const interval = setInterval(fetchStats, 10000)

    return () => clearInterval(interval)
  }, [])

  const formatDuration = (ms: number) => {
    if (ms < 1) return `${(ms * 1000).toFixed(0)}μs`
    if (ms < 1000) return `${ms.toFixed(1)}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }

  const getStatusColor = (value: number, threshold: number) => {
    if (value > threshold) return 'text-red-600'
    if (value > threshold * 0.7) return 'text-yellow-600'
    return 'text-green-600'
  }

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">性能监控</h3>
        <p className="text-gray-500">暂无性能数据</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">性能监控</h3>
        {lastUpdate && (
          <span className="text-sm text-gray-500">
            最后更新: {lastUpdate.toLocaleTimeString()}
          </span>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 总体性能 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">总体性能</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>平均响应时间:</span>
              <span className={getStatusColor(stats.general.avg, 1000)}>
                {formatDuration(stats.general.avg)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>P95响应时间:</span>
              <span className={getStatusColor(stats.general.p95, 2000)}>
                {formatDuration(stats.general.p95)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>最大响应时间:</span>
              <span className={getStatusColor(stats.general.max, 5000)}>
                {formatDuration(stats.general.max)}
              </span>
            </div>
          </div>
        </div>

        {/* 数据库性能 */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">数据库性能</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>平均查询时间:</span>
              <span className={getStatusColor(stats.database.avg, 500)}>
                {formatDuration(stats.database.avg)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>慢查询数量:</span>
              <span className={getStatusColor(stats.slowQueries, 5)}>
                {stats.slowQueries}
              </span>
            </div>
            <div className="flex justify-between">
              <span>总查询数:</span>
              <span className="text-gray-700">{stats.counts.dbQueries}</span>
            </div>
          </div>
        </div>

        {/* API性能 */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">API性能</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>平均API时间:</span>
              <span className={getStatusColor(stats.api.avg, 2000)}>
                {formatDuration(stats.api.avg)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>慢API数量:</span>
              <span className={getStatusColor(stats.slowApis, 3)}>
                {stats.slowApis}
              </span>
            </div>
            <div className="flex justify-between">
              <span>总API调用:</span>
              <span className="text-gray-700">{stats.counts.apiCalls}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 请求统计 */}
      <div className="mt-6 bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">请求统计</h4>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {stats.counts.totalRequests}
            </div>
            <div className="text-sm text-gray-600">总请求数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {stats.counts.dbQueries}
            </div>
            <div className="text-sm text-gray-600">数据库查询</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {stats.counts.apiCalls}
            </div>
            <div className="text-sm text-gray-600">API调用</div>
          </div>
        </div>
      </div>

      {/* 性能警告 */}
      {(stats.slowQueries > 0 || stats.slowApis > 0) && (
        <div className="mt-6 bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">⚠️ 性能警告</h4>
          <div className="text-sm text-yellow-700 space-y-1">
            {stats.slowQueries > 0 && (
              <div>检测到 {stats.slowQueries} 个慢数据库查询</div>
            )}
            {stats.slowApis > 0 && (
              <div>检测到 {stats.slowApis} 个慢API调用</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
