'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

interface ShippingInfoProps {
  orderId: string
  orderStatus: string
}

interface ShippingData {
  orderId: string
  orderNumber: string
  status: string
  trackingNumber?: string
  shippingCompany?: string
  shippingProofText?: string
  shippingProofImages?: Array<{
    originalName: string
    fileName: string
    url: string
    size: number
    type: string
  }>
  shippedAt?: string
  sellerName: string
}

export default function ShippingInfo({ orderId, orderStatus }: ShippingInfoProps) {
  const [shippingData, setShippingData] = useState<ShippingData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)

  useEffect(() => {
    if (orderStatus === 'SHIPPED' || orderStatus === 'COMPLETED') {
      fetchShippingInfo()
    } else {
      setLoading(false)
    }
  }, [orderId, orderStatus])

  const fetchShippingInfo = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}/shipping`, {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setShippingData(data.data)
      } else {
        console.error('获取发货信息失败')
      }
    } catch (error) {
      console.error('获取发货信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 只有在已发货状态才显示
  if (!['SHIPPED', 'COMPLETED'].includes(orderStatus)) {
    return null
  }

  if (loading) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center text-gray-500">加载发货信息中...</div>
        </div>
      </div>
    )
  }

  if (!shippingData) {
    return null
  }

  return (
    <>
      <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            发货信息
          </h3>
          
          <div className="space-y-4">
            {/* 基本发货信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">发货时间</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {shippingData.shippedAt 
                    ? new Date(shippingData.shippedAt).toLocaleString('zh-CN')
                    : '未知'
                  }
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">卖家</dt>
                <dd className="mt-1 text-sm text-gray-900">{shippingData.sellerName}</dd>
              </div>
            </div>

            {/* 快递信息 */}
            {(shippingData.trackingNumber || shippingData.shippingCompany) && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">快递信息</h4>
                <div className="grid grid-cols-2 gap-4">
                  {shippingData.shippingCompany && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">快递公司</dt>
                      <dd className="mt-1 text-sm text-gray-900">{shippingData.shippingCompany}</dd>
                    </div>
                  )}
                  {shippingData.trackingNumber && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">快递单号</dt>
                      <dd className="mt-1 text-sm text-gray-900 font-mono">
                        {shippingData.trackingNumber}
                      </dd>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 文字凭证 */}
            {shippingData.shippingProofText && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">发货凭证</h4>
                <div className="bg-gray-50 rounded-md p-3">
                  <p className="text-sm text-gray-700 whitespace-pre-wrap">
                    {shippingData.shippingProofText}
                  </p>
                </div>
              </div>
            )}

            {/* 图片凭证 */}
            {shippingData.shippingProofImages && shippingData.shippingProofImages.length > 0 && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">发货凭证图片</h4>
                <div className="grid grid-cols-3 gap-2">
                  {shippingData.shippingProofImages.map((image, index) => (
                    <div
                      key={index}
                      className="relative cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => setSelectedImage(image.url)}
                    >
                      <Image
                        src={image.url}
                        alt={`发货凭证 ${index + 1}`}
                        width={120}
                        height={120}
                        className="w-full h-24 object-cover rounded border"
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-20 transition-all rounded">
                        <svg className="w-6 h-6 text-white opacity-0 hover:opacity-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 状态提示 */}
            <div className="border-t pt-4">
              <div className="flex items-center text-sm text-green-600">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>卖家已发货，请注意查收</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 图片预览模态框 */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full p-4">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-2 right-2 text-white hover:text-gray-300 text-2xl font-bold z-10"
            >
              ×
            </button>
            <Image
              src={selectedImage}
              alt="发货凭证预览"
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain"
            />
          </div>
        </div>
      )}
    </>
  )
}
