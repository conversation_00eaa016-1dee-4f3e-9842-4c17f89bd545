'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'

interface ImageUploadProps {
  type: 'product' | 'avatar' | 'payment'
  onUpload: (url: string) => void
  currentImage?: string
  multiple?: boolean
  maxFiles?: number
  className?: string
}

export default function ImageUpload({
  type,
  onUpload,
  currentImage,
  multiple = false,
  maxFiles = 5,
  className = ''
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<string[]>(
    currentImage ? [currentImage] : []
  )
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const filesToUpload = Array.from(files).slice(0, multiple ? maxFiles : 1)
    
    setUploading(true)
    try {
      const uploadPromises = filesToUpload.map(async (file) => {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', type)

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || '上传失败')
        }

        const result = await response.json()
        return result.url
      })

      const urls = await Promise.all(uploadPromises)
      
      if (multiple) {
        const newImages = [...uploadedImages, ...urls].slice(0, maxFiles)
        setUploadedImages(newImages)
        onUpload(newImages.join(','))
      } else {
        setUploadedImages([urls[0]])
        onUpload(urls[0])
      }

    } catch (error) {
      console.error('Upload error:', error)
      alert(error instanceof Error ? error.message : '上传失败，请重试')
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeImage = (index: number) => {
    const newImages = uploadedImages.filter((_, i) => i !== index)
    setUploadedImages(newImages)
    onUpload(newImages.join(','))
  }

  const getUploadText = () => {
    switch (type) {
      case 'product':
        return multiple ? '上传商品图片（最多5张）' : '上传商品图片'
      case 'avatar':
        return '上传头像'
      case 'payment':
        return '上传支付凭证'
      default:
        return '上传图片'
    }
  }

  const getAcceptTypes = () => {
    return 'image/jpeg,image/jpg,image/png,image/webp'
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 上传区域 */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          dragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => !uploading && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={getAcceptTypes()}
          multiple={multiple}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={uploading}
        />

        {uploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-600">上传中...</p>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-4xl text-gray-400">📷</div>
            <p className="text-sm text-gray-600">{getUploadText()}</p>
            <p className="text-xs text-gray-500">
              支持 JPG、PNG、WebP 格式，最大 5MB
            </p>
            <p className="text-xs text-gray-500">
              点击选择文件或拖拽文件到此处
            </p>
          </div>
        )}
      </div>

      {/* 已上传图片预览 */}
      {uploadedImages.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">已上传图片</h4>
          <div className={`grid gap-4 ${multiple ? 'grid-cols-2 md:grid-cols-3' : 'grid-cols-1'}`}>
            {uploadedImages.map((url, index) => (
              <div key={index} className="relative group">
                <div className="relative aspect-square rounded-lg overflow-hidden border">
                  <Image
                    src={url}
                    alt={`上传的图片 ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    removeImage(index)
                  }}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 上传提示 */}
      {multiple && uploadedImages.length > 0 && uploadedImages.length < maxFiles && (
        <p className="text-xs text-gray-500">
          已上传 {uploadedImages.length} 张，还可以上传 {maxFiles - uploadedImages.length} 张
        </p>
      )}
    </div>
  )
}
