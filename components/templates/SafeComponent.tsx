// 安全的React组件模板
import React, { memo, useCallback, useMemo } from 'react'

interface SafeComponentProps {
  data?: any[]
  title?: string
  onAction?: (item: any) => void
}

const SafeComponent = memo(({ data = [], title, onAction }: SafeComponentProps) => {
  // 使用useMemo优化计算
  const processedData = useMemo(() => {
    return data?.filter(Boolean) || []
  }, [data])

  // 使用useCallback优化事件处理
  const handleAction = useCallback((item: any) => {
    try {
      onAction?.(item)
    } catch (error) {
      console.error('Action failed:', error)
    }
  }, [onAction])

  // 安全的渲染
  return (
    <div className="safe-component">
      {title && <h2>{title}</h2>}
      
      {processedData.length > 0 ? (
        <ul>
          {processedData.map((item, index) => (
            <li key={item?.id || index}>
              <span>{item?.name || '未知项目'}</span>
              {onAction && (
                <button 
                  onClick={() => handleAction(item)}
                  type="button"
                >
                  操作
                </button>
              )}
            </li>
          ))}
        </ul>
      ) : (
        <p>暂无数据</p>
      )}
    </div>
  )
})

SafeComponent.displayName = 'SafeComponent'

export default SafeComponent

// 使用示例:
// <SafeComponent 
//   data={items} 
//   title="项目列表"
//   onAction={(item) => console.log('Action:', item)}
// />
