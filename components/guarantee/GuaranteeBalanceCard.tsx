'use client'

import React, { useState, useEffect } from 'react'
import {
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  LockClosedIcon,
  ChartBarIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { CheckCircleIcon } from '@heroicons/react/24/solid'

interface BalanceInfo {
  userId: string
  depositBalance: number
  frozenBalance: number
  availableBalance: number
  totalEarnings: number
  totalWithdrawals: number
  guaranteeLevel: string
  maxWithdrawable: number
  currentLevel: string
  totalBalance: number
  utilizationRate: string
  netEarnings: number
}

interface GuaranteeBalanceCardProps {
  className?: string
  onDeposit?: () => void
  onWithdraw?: () => void
  onViewHistory?: () => void
}

export default function GuaranteeBalanceCard({
  className = '',
  onDeposit,
  onWithdraw,
  onViewHistory
}: GuaranteeBalanceCardProps) {
  const [balance, setBalance] = useState<BalanceInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBalance()
  }, [])

  const fetchBalance = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/guarantee/balance')
      const data = await response.json()

      if (data.success) {
        setBalance(data.data)
      } else {
        setError(data.error || '获取余额失败')
      }
    } catch (error) {
      console.error('获取担保金余额失败:', error)
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'BRONZE':
        return 'text-amber-600 bg-amber-50 border-amber-200'
      case 'SILVER':
        return 'text-gray-600 bg-gray-50 border-gray-200'
      case 'GOLD':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'DIAMOND':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'PLATINUM':
        return 'text-purple-600 bg-purple-50 border-purple-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getLevelLabel = (level: string) => {
    const labels: { [key: string]: string } = {
      'BRONZE': '青铜',
      'SILVER': '白银',
      'GOLD': '黄金',
      'DIAMOND': '钻石',
      'PLATINUM': '铂金'
    }
    return labels[level] || level
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded w-32"></div>
          </div>
          <div className="space-y-4">
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-20 bg-gray-200 rounded"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchBalance}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  if (!balance) {
    return null
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* 标题和等级 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CurrencyDollarIcon className="h-8 w-8 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">担保金余额</h2>
        </div>
        
        <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getLevelColor(balance.currentLevel)}`}>
          {getLevelLabel(balance.currentLevel)}
        </div>
      </div>

      {/* 主要余额显示 */}
      <div className="mb-6">
        <div className="text-center">
          <div className="text-3xl font-bold text-gray-900 mb-2">
            {balance.totalBalance.toFixed(2)} <span className="text-lg text-gray-500">USDT</span>
          </div>
          <div className="text-sm text-gray-500">
            总担保金余额
          </div>
        </div>
      </div>

      {/* 余额详情 */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircleIcon className="h-5 w-5 text-green-500" />
            <span className="text-sm font-medium text-green-700">可用余额</span>
          </div>
          <div className="text-xl font-semibold text-green-900">
            {balance.availableBalance.toFixed(2)}
          </div>
          <div className="text-xs text-green-600 mt-1">
            可用于交易和提现
          </div>
        </div>

        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <LockClosedIcon className="h-5 w-5 text-orange-500" />
            <span className="text-sm font-medium text-orange-700">冻结余额</span>
          </div>
          <div className="text-xl font-semibold text-orange-900">
            {balance.frozenBalance.toFixed(2)}
          </div>
          <div className="text-xs text-orange-600 mt-1">
            交易中冻结资金
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 mb-6 text-center">
        <div>
          <div className="text-lg font-semibold text-gray-900">
            {balance.totalEarnings.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500">累计收入</div>
        </div>
        <div>
          <div className="text-lg font-semibold text-gray-900">
            {balance.totalWithdrawals.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500">累计提现</div>
        </div>
        <div>
          <div className="text-lg font-semibold text-gray-900">
            {balance.netEarnings.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500">净收益</div>
        </div>
      </div>

      {/* 资金利用率 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">资金利用率</span>
          <span className="text-sm text-gray-500">{balance.utilizationRate}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${Math.min(parseFloat(balance.utilizationRate), 100)}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          冻结资金占总余额的比例
        </div>
      </div>

      {/* 提现限额提示 */}
      {balance.maxWithdrawable < balance.availableBalance && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="h-5 w-5 text-yellow-500 mt-0.5" />
            <div className="text-sm">
              <div className="font-medium text-yellow-800 mb-1">提现限额提醒</div>
              <div className="text-yellow-700">
                根据您的担保金等级，今日最大可提现金额为 {balance.maxWithdrawable.toFixed(2)} USDT
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="grid grid-cols-3 gap-3">
        <button
          onClick={onDeposit}
          className="flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <ArrowUpIcon className="h-4 w-4" />
          <span>充值</span>
        </button>
        
        <button
          onClick={onWithdraw}
          disabled={balance.maxWithdrawable <= 0}
          className="flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          <ArrowDownIcon className="h-4 w-4" />
          <span>提现</span>
        </button>
        
        <button
          onClick={onViewHistory}
          className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <ChartBarIcon className="h-4 w-4" />
          <span>历史</span>
        </button>
      </div>

      {/* 刷新按钮 */}
      <div className="mt-4 text-center">
        <button
          onClick={fetchBalance}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          刷新余额
        </button>
      </div>
    </div>
  )
}
