'use client'

import { useState } from 'react'
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

const faqData: FAQItem[] = [
  {
    id: 'payment-time',
    question: '支付后多久能到账？',
    answer: 'USDT转账通常在5-30分钟内到账，具体时间取决于网络拥堵情况。如果超过1小时未到账，请联系客服。',
    category: 'payment'
  },
  {
    id: 'hash-not-found',
    question: '找不到交易哈希怎么办？',
    answer: '请确保您查看的是"提现"记录而不是"充值"记录。如果仍然找不到，可能是转账还在处理中，请稍后再查看。',
    category: 'payment'
  },
  {
    id: 'wrong-address',
    question: '转错地址了怎么办？',
    answer: '如果转账到错误地址，资金可能无法找回。请务必在转账前仔细核对收款地址。如有疑问，请立即联系客服。',
    category: 'payment'
  },
  {
    id: 'escrow-safety',
    question: '托管支付安全吗？',
    answer: '托管支付是平台提供的安全保障机制。买家付款后，资金会被平台托管，只有在买家确认收货后才会释放给卖家。',
    category: 'trading'
  },
  {
    id: 'dispute-process',
    question: '发生争议怎么处理？',
    answer: '如果交易过程中发生争议，可以在订单页面申请平台介入。我们会根据双方提供的证据进行公正处理。',
    category: 'trading'
  },
  {
    id: 'credit-score',
    question: '信誉积分如何计算？',
    answer: '信誉积分基于交易完成率、买家评价、交易金额等因素计算。完成交易并获得好评可以提升信誉积分。',
    category: 'account'
  },
  {
    id: 'binance-uid',
    question: '为什么要绑定币安UID？',
    answer: '绑定币安UID有助于验证您的身份，提高账户安全性，并可能享受更优惠的手续费率。',
    category: 'account'
  },
  {
    id: 'withdrawal-fee',
    question: '提现需要手续费吗？',
    answer: '平台会收取少量手续费用于维护运营。具体费率请查看费率说明页面。',
    category: 'fees'
  }
]

interface FAQProps {
  category?: string
  maxItems?: number
}

export default function FAQ({ category, maxItems }: FAQProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  // 筛选FAQ项目
  let filteredFAQ = faqData
  if (category && category !== 'all') {
    filteredFAQ = faqData.filter(item => item.category === category)
  }
  if (maxItems) {
    filteredFAQ = filteredFAQ.slice(0, maxItems)
  }

  const toggleItem = (id: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedItems(newExpanded)
  }

  return (
    <div className="space-y-4">
      {filteredFAQ.map((item) => (
        <div key={item.id} className="border border-gray-200 rounded-lg">
          <button
            onClick={() => toggleItem(item.id)}
            className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <span className="font-medium text-gray-900">{item.question}</span>
            {expandedItems.has(item.id) ? (
              <ChevronUpIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            )}
          </button>
          
          {expandedItems.has(item.id) && (
            <div className="px-6 pb-4 border-t border-gray-100">
              <p className="text-gray-700 leading-relaxed">{item.answer}</p>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// 分类标签
export function FAQCategories({ 
  selectedCategory, 
  onCategoryChange 
}: { 
  selectedCategory: string
  onCategoryChange: (category: string) => void 
}) {
  const categories = [
    { id: 'all', name: '全部', count: faqData.length },
    { id: 'payment', name: '支付相关', count: faqData.filter(item => item.category === 'payment').length },
    { id: 'trading', name: '交易流程', count: faqData.filter(item => item.category === 'trading').length },
    { id: 'account', name: '账户设置', count: faqData.filter(item => item.category === 'account').length },
    { id: 'fees', name: '费用说明', count: faqData.filter(item => item.category === 'fees').length }
  ]

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {categories.map(category => (
        <button
          key={category.id}
          onClick={() => onCategoryChange(category.id)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            selectedCategory === category.id
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {category.name} ({category.count})
        </button>
      ))}
    </div>
  )
}
