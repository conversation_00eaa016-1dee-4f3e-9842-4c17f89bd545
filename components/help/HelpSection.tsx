'use client'

import { type ReactNode } from 'react'
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'

interface HelpSectionProps {
  title: string
  icon?: ReactNode
  children: ReactNode
  isExpanded?: boolean
  onToggle?: () => void
  bgColor?: string
  borderColor?: string
}

export default function HelpSection({
  title,
  icon,
  children,
  isExpanded = true,
  onToggle,
  bgColor = 'bg-blue-50',
  borderColor = 'border-blue-200'
}: HelpSectionProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className={`${bgColor} px-6 py-4 border-b ${borderColor}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {icon && <div className="mr-3">{icon}</div>}
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          </div>
          {onToggle && (
            <button
              onClick={onToggle}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              {isExpanded ? (
                <ChevronUpIcon className="h-5 w-5" />
              ) : (
                <ChevronDownIcon className="h-5 w-5" />
              )}
            </button>
          )}
        </div>
      </div>
      
      {isExpanded && (
        <div className="p-6">
          {children}
        </div>
      )}
    </div>
  )
}

// 步骤组件
interface StepProps {
  stepNumber: number
  title: string
  children: ReactNode
  color?: string
}

export function HelpStep({ stepNumber, title, children, color = 'bg-blue-600' }: StepProps) {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        <div className={`flex-shrink-0 w-8 h-8 ${color} text-white rounded-full flex items-center justify-center text-sm font-medium`}>
          {stepNumber}
        </div>
        <h3 className="ml-3 text-lg font-medium text-gray-900">{title}</h3>
      </div>
      <div className="ml-11">
        {children}
      </div>
    </div>
  )
}

// 图片占位符组件
interface ImagePlaceholderProps {
  title: string
  description?: string
  height?: string
}

export function ImagePlaceholder({ title, description, height = 'h-32' }: ImagePlaceholderProps) {
  return (
    <div className={`bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300 ${height} flex flex-col items-center justify-center`}>
      <p className="text-center text-gray-500 text-sm font-medium">📸 {title}</p>
      {description && (
        <p className="text-center text-xs text-gray-400 mt-1">{description}</p>
      )}
    </div>
  )
}

// 提示框组件
interface TipBoxProps {
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  children: ReactNode
}

export function TipBox({ type, title, children }: TipBoxProps) {
  const styles = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  }

  const icons = {
    info: '💡',
    warning: '⚠️',
    success: '✅',
    error: '❌'
  }

  return (
    <div className={`${styles[type]} rounded-lg p-4 border`}>
      <h4 className="font-medium mb-2">
        {icons[type]} {title}
      </h4>
      <div className="text-sm">
        {children}
      </div>
    </div>
  )
}
