'use client'

import { useState, useEffect } from 'react'
import { 
  CurrencyDollarIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface DepositPaymentProps {
  amount: number
  currency?: string
  orderId: string
  onSuccess?: (paymentData: any) => void
  onError?: (error: string) => void
  onCancel?: () => void
}

interface UserBalance {
  total: number
  available: number
  guaranteePool: number
  pendingWithdrawal: number
}

interface SupplementPaymentData {
  method: 'binance_pay' | 'bnb_chain'
  amount: number
  pin?: string
  orderNumber?: string
  txHash?: string
}

export default function DepositPayment({
  amount,
  currency = 'USDT',
  orderId,
  onSuccess,
  onError,
  onCancel
}: DepositPaymentProps) {
  const [balance, setBalance] = useState<UserBalance | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [showSupplementModal, setShowSupplementModal] = useState(false)
  const [supplementAmount, setSupplementAmount] = useState(0)
  const [supplementMethod, setSupplementMethod] = useState<'binance_pay' | 'bnb_chain'>('binance_pay')
  const [supplementData, setSupplementData] = useState<SupplementPaymentData | null>(null)

  useEffect(() => {
    fetchBalance()
  }, [])

  const fetchBalance = async () => {
    try {
      const response = await fetch('/api/funds/balance')
      if (response.ok) {
        const data = await response.json()
        setBalance(data.balance)
      } else {
        throw new Error('获取余额失败')
      }
    } catch (error) {
      console.error('获取余额失败:', error)
      if (onError) {
        onError('获取余额失败，请稍后重试')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleDepositPayment = async () => {
    if (!balance) return

    setProcessing(true)
    try {
      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: 'deposit_balance',
          amount: amount
        })
      })

      const result = await response.json()

      if (response.ok) {
        if (onSuccess) {
          onSuccess({
            paymentMethod: 'deposit_balance',
            amount: amount,
            deductedAmount: amount
          })
        }
      } else {
        throw new Error(result.error || '支付失败')
      }
    } catch (error) {
      console.error('保证金支付失败:', error)
      if (onError) {
        onError(error instanceof Error ? error.message : '支付失败')
      }
    } finally {
      setProcessing(false)
    }
  }

  const handleInsufficientBalance = () => {
    const shortage = amount - (balance?.available || 0)
    setSupplementAmount(shortage)
    setShowSupplementModal(true)
  }

  const handleSupplementPayment = async () => {
    if (!balance) return

    setProcessing(true)
    try {
      // 先扣除可用保证金
      const depositAmount = balance.available
      const supplementAmount = amount - depositAmount

      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: 'deposit_supplement',
          depositAmount: depositAmount,
          supplementAmount: supplementAmount,
          supplementMethod: supplementMethod,
          ...supplementData
        })
      })

      const result = await response.json()

      if (response.ok) {
        if (onSuccess) {
          onSuccess({
            paymentMethod: 'deposit_supplement',
            depositAmount: depositAmount,
            supplementAmount: supplementAmount,
            supplementMethod: supplementMethod
          })
        }
      } else {
        throw new Error(result.error || '支付失败')
      }
    } catch (error) {
      console.error('补充支付失败:', error)
      if (onError) {
        onError(error instanceof Error ? error.message : '支付失败')
      }
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">加载余额信息...</span>
      </div>
    )
  }

  if (!balance) {
    return (
      <div className="text-center p-8">
        <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">无法获取余额信息</p>
        <button
          onClick={fetchBalance}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          重新加载
        </button>
      </div>
    )
  }

  const isBalanceSufficient = balance.available >= amount
  const shortage = amount - balance.available

  return (
    <div className="space-y-6">
      {/* 支付金额显示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex justify-between items-center">
          <span className="text-sm text-blue-700">支付金额</span>
          <span className="text-xl font-bold text-blue-900">
            {amount.toFixed(2)} {currency}
          </span>
        </div>
      </div>

      {/* 余额信息 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <CurrencyDollarIcon className="h-5 w-5 text-green-500 mr-2" />
          保证金余额
        </h3>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">总保证金</span>
            <span className="font-medium">{balance.total.toFixed(2)} {currency}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">可用余额</span>
            <span className={`font-medium ${isBalanceSufficient ? 'text-green-600' : 'text-red-600'}`}>
              {balance.available.toFixed(2)} {currency}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">担保池</span>
            <span className="font-medium text-gray-500">{balance.guaranteePool.toFixed(2)} {currency}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">待提现</span>
            <span className="font-medium text-gray-500">{balance.pendingWithdrawal.toFixed(2)} {currency}</span>
          </div>
        </div>
      </div>

      {/* 支付状态和操作 */}
      {isBalanceSufficient ? (
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <CheckCircleIcon className="h-5 w-5 text-green-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-green-800">余额充足</h4>
                <p className="mt-1 text-sm text-green-700">
                  您的可用保证金足够支付此订单，将扣除 {amount.toFixed(2)} {currency}
                </p>
              </div>
            </div>
          </div>

          <button
            onClick={handleDepositPayment}
            disabled={processing}
            className="w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {processing ? '处理中...' : `使用保证金支付 ${amount.toFixed(2)} ${currency}`}
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-yellow-800">余额不足</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  可用保证金: {balance.available.toFixed(2)} {currency}，
                  还需要: {shortage.toFixed(2)} {currency}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <button
              onClick={handleInsufficientBalance}
              className="bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              扣除保证金 + 补充支付
            </button>
            
            <button
              onClick={onCancel}
              className="bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              取消支付
            </button>
          </div>
        </div>
      )}

      {/* 补充支付模态框 */}
      {showSupplementModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                补充支付
              </h3>
              <button
                onClick={() => setShowSupplementModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-sm text-blue-700 space-y-1">
                  <div className="flex justify-between">
                    <span>扣除保证金:</span>
                    <span className="font-medium">{balance.available.toFixed(2)} {currency}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>需要补充:</span>
                    <span className="font-medium">{supplementAmount.toFixed(2)} {currency}</span>
                  </div>
                  <hr className="border-blue-200 my-2" />
                  <div className="flex justify-between font-bold">
                    <span>总计:</span>
                    <span>{amount.toFixed(2)} {currency}</span>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择补充支付方式
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="binance_pay"
                      checked={supplementMethod === 'binance_pay'}
                      onChange={(e) => setSupplementMethod(e.target.value as 'binance_pay')}
                      className="mr-2"
                    />
                    <span>币安支付 (Binance Pay)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="bnb_chain"
                      checked={supplementMethod === 'bnb_chain'}
                      onChange={(e) => setSupplementMethod(e.target.value as 'bnb_chain')}
                      className="mr-2"
                    />
                    <span>BNB链支付</span>
                  </label>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleSupplementPayment}
                  disabled={processing}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {processing ? '处理中...' : '确认支付'}
                </button>
                <button
                  onClick={() => setShowSupplementModal(false)}
                  className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 说明信息 */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-gray-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-700">保证金支付说明</h4>
            <ul className="mt-1 text-sm text-gray-600 space-y-1">
              <li>• 只能使用可用保证金支付，冻结和待提现资金无法使用</li>
              <li>• 支付成功后资金将进入托管，确认收货后释放给卖家</li>
              <li>• 如余额不足，可选择扣除保证金后补充其他支付方式</li>
              <li>• 保证金支付无需额外手续费</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
