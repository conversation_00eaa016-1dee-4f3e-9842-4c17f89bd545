'use client'

import { useState, useEffect } from 'react'

interface BinancePaymentProps {
  amount: number
  currency?: string
  orderId: string
  onSuccess?: (paymentData: any) => void
  onError?: (error: string) => void
  onPaymentComplete?: (orderNumber: string) => void
  onCancel?: () => void
}

interface PinInfo {
  hasPinCode: boolean
  pinCode?: string
  pinExpiry: string | null
  pinUsed: boolean
  verificationAttempts: number
  maxAttempts: number
  canRegenerate: boolean
}

export default function BinancePayment({
  amount,
  currency = 'USDT',
  orderId,
  onSuccess,
  onError,
  onPaymentComplete,
  onCancel
}: BinancePaymentProps) {
  // 使用静态二维码图片路径
  const qrCodeUrl = '/binance-qr-code.png'
  const [pinInfo, setPinInfo] = useState<PinInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeLeft, setTimeLeft] = useState(1800) // 30分钟倒计时
  const [orderNumber, setOrderNumber] = useState('')
  const [pin, setPin] = useState('')
  const [isVerifying, setIsVerifying] = useState(false)
  const [verificationStep, setVerificationStep] = useState<'payment' | 'verification'>('payment')

  useEffect(() => {
    initializePayment()
  }, [])

  useEffect(() => {
    // 倒计时
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const initializePayment = async () => {
    try {
      // 获取PIN信息
      const response = await fetch(`/api/orders/${orderId}/verify-pin`, {
        credentials: 'include' // 包含认证信息
      })

      if (response.ok) {
        const data = await response.json()
        console.log('PIN信息获取成功:', data) // 调试日志
        setPinInfo(data)

        // 根据PIN状态设置倒计时
        if (data.pinExpired || !data.hasPinCode) {
          setTimeLeft(300) // 5分钟
        } else if (data.pinExpiry) {
          // 根据PIN过期时间设置倒计时
          const expiryTime = new Date(data.pinExpiry).getTime()
          const currentTime = new Date().getTime()
          const remainingTime = Math.max(0, Math.floor((expiryTime - currentTime) / 1000))
          setTimeLeft(remainingTime)
          console.log(`PIN倒计时设置: ${remainingTime}秒`) // 调试日志
        }
      } else {
        console.error('获取PIN信息失败:', response.status, response.statusText)
        if (response.status === 401) {
          if (onError) {
            onError('请先登录')
          }
        } else {
          if (onError) {
            onError('获取支付信息失败')
          }
        }
      }
    } catch (error) {
      console.error('初始化支付失败:', error)
      if (onError) {
        onError('初始化支付失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handlePaymentCompleted = () => {
    setVerificationStep('verification')
  }

  const handleVerifyPin = async () => {
    if (!pin.trim() || !orderNumber.trim()) {
      alert('请输入PIN码和币安订单号')
      return
    }

    setIsVerifying(true)
    try {
      const response = await fetch(`/api/orders/${orderId}/verify-pin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin.trim(),
          orderNumber: orderNumber.trim()
        }),
      })

      const data = await response.json()

      if (data.success) {
        if (onSuccess) {
          onSuccess({
            pin: pin.trim(),
            orderNumber: orderNumber.trim(),
            paymentMethod: 'binance_pay'
          })
        }
        if (onPaymentComplete) {
          onPaymentComplete(orderNumber.trim())
        }
      } else {
        alert(data.message || '验证失败')
        // 如果验证失败，刷新PIN信息
        initializePayment()
      }
    } catch (error) {
      console.error('验证失败:', error)
      alert('验证失败，请稍后重试')
    } finally {
      setIsVerifying(false)
    }
  }

  const handleRegeneratePin = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}/verify-pin`, {
        method: 'PUT'
      })

      const data = await response.json()

      if (data.success) {
        alert('PIN码已重新生成')
        initializePayment() // 刷新PIN信息
      } else {
        alert(data.error || '重新生成PIN码失败')
      }
    } catch (error) {
      console.error('重新生成PIN码失败:', error)
      alert('重新生成PIN码失败，请稍后重试')
    }
  }

  // 移除旧的确认支付函数，使用新的验证流程

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在生成支付码...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* 头部信息 */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="text-3xl mr-2">💳</div>
          <h2 className="text-xl font-bold text-gray-900">Binance Pay</h2>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="text-sm text-blue-700 mb-1">支付金额</div>
          <div className="text-2xl font-bold text-blue-900">
            {amount.toFixed(2)} {currency}
          </div>
        </div>

        {/* 倒计时 */}
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          <span>剩余时间: {formatTime(timeLeft)}</span>
        </div>

        {/* PIN码显示 */}
        {pinInfo && pinInfo.hasPinCode && !pinInfo.pinUsed && pinInfo.pinCode && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
            <div className="text-sm text-yellow-700 mb-1">转账备注PIN码</div>
            <div className="text-2xl font-mono font-bold text-yellow-900 tracking-wider bg-white px-4 py-2 rounded border-2 border-yellow-300 text-center">
              {pinInfo.pinCode}
            </div>
            <div className="text-xs text-yellow-600 mt-2">
              请在币安转账备注中填写上方PIN码，PIN码有效期30分钟
            </div>
          </div>
        )}
      </div>

      {/* 二维码 */}
      <div className="text-center mb-6">
        <div className="inline-block p-2 bg-white border-2 border-gray-200 rounded-lg">
          <img
            src={qrCodeUrl}
            alt="Binance Pay QR Code"
            className="w-64 h-64 mx-auto"
            style={{
              width: '256px',
              height: '256px',
              backgroundColor: '#FFFFFF',
              padding: '2px'
            }}
            onLoad={() => console.log('二维码图片加载成功')}
            onError={(e) => console.error('二维码图片加载失败:', e)}
          />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          使用币安App扫描二维码支付
        </p>
      </div>

      {verificationStep === 'payment' ? (
        <>
          {/* 支付步骤 */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">支付步骤：</h3>
            <ol className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">1</span>
                打开币安App，点击"支付"
              </li>
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">2</span>
                扫描上方二维码
              </li>
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">3</span>
                在转账备注中填写上方显示的PIN码
              </li>
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">4</span>
                确认支付金额并完成转账
              </li>
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">5</span>
                点击下方"已完成转账"按钮进行验证
              </li>
            </ol>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={onCancel}
              className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium"
            >
              取消支付
            </button>
            <button
              onClick={handlePaymentCompleted}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium"
            >
              已完成转账
            </button>
          </div>
        </>
      ) : (
        <>
          {/* 验证步骤 */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">验证转账：</h3>

            {/* PIN码输入 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                PIN验证码
              </label>
              <input
                type="text"
                value={pin}
                onChange={(e) => setPin(e.target.value.toUpperCase())}
                placeholder="请输入转账备注中的PIN码"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={8}
              />
              <p className="text-xs text-gray-500 mt-1">
                请输入您在转账备注中填写的PIN码
              </p>
            </div>

            {/* 币安订单号输入 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                币安订单号
              </label>
              <input
                type="text"
                value={orderNumber}
                onChange={(e) => setOrderNumber(e.target.value)}
                placeholder="请输入币安转账完成后的订单号"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                转账完成后，请在币安App中查看交易详情并复制订单号
              </p>
            </div>
          </div>

          {/* 验证按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={() => setVerificationStep('payment')}
              className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium"
            >
              返回支付
            </button>
            <button
              onClick={handleVerifyPin}
              disabled={!pin.trim() || !orderNumber.trim() || isVerifying}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-md font-medium"
            >
              {isVerifying ? '验证中...' : '确认验证'}
            </button>
          </div>
        </>
      )}

      {/* 安全提示 */}
      <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <svg className="w-4 h-4 text-yellow-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div className="text-xs text-yellow-700">
            <p className="font-medium mb-1">安全提示：</p>
            <p>• 请确保在币安官方App中完成转账</p>
            <p>• 转账时务必在备注中填写PIN码</p>
            <p>• 转账完成后请保存交易记录</p>
            <p>• 如遇问题请联系客服</p>
          </div>
        </div>
      </div>

      {/* PIN管理 */}
      {pinInfo && pinInfo.canRegenerate && (
        <div className="mt-4 text-center">
          <button
            onClick={handleRegeneratePin}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            重新生成PIN码
          </button>
        </div>
      )}
    </div>
  )
}
