'use client'

import { useState } from 'react'
import Image from 'next/image'

export interface PaymentMethod {
  id: 'deposit_balance' | 'binance_pay' | 'bnb_chain'
  name: string
  description: string
  icon: string
  features: string[]
  recommended: boolean
}

interface PaymentMethodSelectorProps {
  selectedMethod?: PaymentMethod | null
  onSelect?: (method: PaymentMethod) => void
  onMethodSelect?: (method: PaymentMethod) => void
  amount: number
  currency?: string
}

export default function PaymentMethodSelector({
  selectedMethod,
  onSelect,
  onMethodSelect,
  amount,
  currency = 'USDT'
}: PaymentMethodSelectorProps) {
  const handleMethodSelect = (method: PaymentMethod) => {
    if (onSelect) {
      onSelect(method)
    }
    if (onMethodSelect) {
      onMethodSelect(method)
    }
  }

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'deposit_balance',
      name: '保证金支付',
      description: '使用账户保证金余额支付',
      icon: '💰',
      features: ['即时到账', '无手续费', '安全便捷'],
      recommended: true
    },
    {
      id: 'binance_pay',
      name: 'Binance Pay',
      description: '使用币安付款码支付',
      icon: '💳',
      features: ['扫码支付', '即时到账', '低手续费'],
      recommended: false
    },
    {
      id: 'bnb_chain',
      name: 'BNB Smart Chain',
      description: '直接在BNB链上转账',
      icon: '⛓️',
      features: ['链上交易', '去中心化', '可验证'],
      recommended: false
    }
  ]

  return (
    <div className="space-y-4">
      <div className="text-lg font-semibold text-gray-900 mb-4">
        选择支付方式
      </div>
      
      {/* 支付金额显示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <span className="text-sm text-blue-700">支付金额</span>
          <span className="text-xl font-bold text-blue-900">
            {amount.toFixed(2)} {currency}
          </span>
        </div>
      </div>

      {/* 支付方式选项 */}
      <div className="grid gap-4">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
              selectedMethod?.id === method.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
            onClick={() => handleMethodSelect(method)}
          >
            {/* 推荐标签 */}
            {method.recommended && (
              <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                推荐
              </div>
            )}

            <div className="flex items-start space-x-4">
              {/* 图标 */}
              <div className="text-3xl">{method.icon}</div>
              
              {/* 内容 */}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {method.name}
                  </h3>
                  {selectedMethod?.id === method.id && (
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 mb-3">
                  {method.description}
                </p>
                
                {/* 特性标签 */}
                <div className="flex flex-wrap gap-2">
                  {method.features.map((feature, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 支付说明 */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
        <div className="flex items-start space-x-3">
          <div className="text-yellow-600">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h4 className="text-sm font-medium text-yellow-800 mb-1">
              托管支付说明
            </h4>
            <p className="text-sm text-yellow-700">
              您的资金将暂时托管在平台账户中，确保交易安全。只有在您确认收货后，资金才会释放给卖家。
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
