'use client'

import { useState, useEffect } from 'react'

interface BNBChainPaymentProps {
  amount: number
  currency?: string
  orderId: string
  onSuccess?: (paymentData: any) => void
  onError?: (error: string) => void
  onPaymentComplete?: (txHash: string) => void
  onCancel?: () => void
}

export default function BNBChainPayment({
  amount,
  currency = 'USDT',
  orderId,
  onSuccess,
  onError,
  onPaymentComplete,
  onCancel
}: BNBChainPaymentProps) {
  const [walletAddress, setWalletAddress] = useState('')
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [txHash, setTxHash] = useState('')
  const [timeLeft, setTimeLeft] = useState(1800) // 30分钟倒计时
  const [isConnectingWallet, setIsConnectingWallet] = useState(false)

  // 模拟的平台钱包地址
  const platformWalletAddress = '******************************************'

  useEffect(() => {
    generatePaymentAddress()
  }, [])

  useEffect(() => {
    // 倒计时
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const generatePaymentAddress = async () => {
    try {
      // 实际实现中，这里应该生成或获取专用的收款地址
      setWalletAddress(platformWalletAddress)

      // 生成包含地址和金额的二维码
      const paymentData = `${platformWalletAddress}?amount=${amount}&token=USDT&memo=ORDER_${orderId}`

      // 动态导入 QRCode 以避免服务器端渲染问题
      const QRCode = (await import('qrcode')).default
      const qrUrl = await QRCode.toDataURL(paymentData, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
      setQrCodeUrl(qrUrl)
    } catch (error) {
      console.error('生成支付地址失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const connectWallet = async () => {
    setIsConnectingWallet(true)
    try {
      // 检查是否安装了MetaMask或其他钱包
      if (typeof window !== 'undefined' && (window as any).ethereum) {
        const accounts = await (window as any).ethereum.request({
          method: 'eth_requestAccounts'
        })
        
        if (accounts.length > 0) {
          // 切换到BSC网络
          try {
            await (window as any).ethereum.request({
              method: 'wallet_switchEthereumChain',
              params: [{ chainId: '0x38' }], // BSC Mainnet
            })
          } catch (switchError: any) {
            // 如果网络不存在，添加BSC网络
            if (switchError.code === 4902) {
              await (window as any).ethereum.request({
                method: 'wallet_addEthereumChain',
                params: [{
                  chainId: '0x38',
                  chainName: 'BNB Smart Chain',
                  nativeCurrency: {
                    name: 'BNB',
                    symbol: 'BNB',
                    decimals: 18
                  },
                  rpcUrls: ['https://bsc-dataseed.binance.org/'],
                  blockExplorerUrls: ['https://bscscan.com/']
                }]
              })
            }
          }
          
          alert('钱包连接成功！请手动发送交易到指定地址。')
        }
      } else {
        alert('请安装MetaMask或其他Web3钱包')
      }
    } catch (error) {
      console.error('连接钱包失败:', error)
      alert('连接钱包失败，请重试')
    } finally {
      setIsConnectingWallet(false)
    }
  }

  const copyAddress = () => {
    navigator.clipboard.writeText(walletAddress)
    alert('地址已复制到剪贴板')
  }

  const handleConfirmPayment = () => {
    if (!txHash.trim()) {
      alert('请输入交易哈希')
      return
    }

    const paymentData = {
      txHash: txHash.trim(),
      paymentMethod: 'bnb_chain',
      walletAddress
    }

    if (onSuccess) {
      onSuccess(paymentData)
    }
    if (onPaymentComplete) {
      onPaymentComplete(txHash)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在生成支付地址...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* 头部信息 */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="text-3xl mr-2">⛓️</div>
          <h2 className="text-xl font-bold text-gray-900">BNB Smart Chain</h2>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <div className="text-sm text-yellow-700 mb-1">支付金额</div>
          <div className="text-2xl font-bold text-yellow-900">
            {amount.toFixed(2)} {currency}
          </div>
          <div className="text-xs text-yellow-600 mt-1">
            网络: BNB Smart Chain (BSC)
          </div>
        </div>

        {/* 倒计时 */}
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          <span>剩余时间: {formatTime(timeLeft)}</span>
        </div>
      </div>

      {/* 收款地址 */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          收款地址 (USDT-BSC)
        </label>
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={walletAddress}
            readOnly
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm font-mono"
          />
          <button
            onClick={copyAddress}
            className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-md text-sm"
          >
            复制
          </button>
        </div>
      </div>

      {/* 二维码 */}
      <div className="text-center mb-6">
        <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
          {qrCodeUrl && (
            <img 
              src={qrCodeUrl} 
              alt="Payment Address QR Code" 
              className="w-48 h-48 mx-auto"
            />
          )}
        </div>
        <p className="text-sm text-gray-600 mt-2">
          扫描二维码或复制地址进行转账
        </p>
      </div>

      {/* 连接钱包按钮 */}
      <div className="mb-6">
        <button
          onClick={connectWallet}
          disabled={isConnectingWallet}
          className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-md font-medium"
        >
          {isConnectingWallet ? '连接中...' : '连接钱包支付'}
        </button>
      </div>

      {/* 支付说明 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">支付说明：</h3>
        <div className="text-sm text-gray-600 space-y-2">
          <p>• 请确保使用 <strong>BNB Smart Chain (BSC)</strong> 网络</p>
          <p>• 代币类型: <strong>USDT (BEP-20)</strong></p>
          <p>• 转账金额: <strong>{amount.toFixed(2)} USDT</strong></p>
          <p>• 请在备注中填写: <strong>ORDER_{orderId}</strong></p>
        </div>
      </div>

      {/* 交易哈希输入 */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          交易哈希 (Transaction Hash)
        </label>
        <input
          type="text"
          value={txHash}
          onChange={(e) => setTxHash(e.target.value)}
          placeholder="请输入转账完成后的交易哈希"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
        />
        <p className="text-xs text-gray-500 mt-1">
          转账完成后，请在区块链浏览器中查看交易详情并复制交易哈希
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="flex space-x-3">
        <button
          onClick={onCancel}
          className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium"
        >
          取消支付
        </button>
        <button
          onClick={handleConfirmPayment}
          disabled={!txHash.trim()}
          className="flex-1 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-md font-medium"
        >
          确认支付
        </button>
      </div>

      {/* 安全提示 */}
      <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <svg className="w-4 h-4 text-red-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div className="text-xs text-red-700">
            <p className="font-medium mb-1">重要提示：</p>
            <p>• 请确保网络选择正确 (BSC)</p>
            <p>• 代币必须是 USDT (BEP-20)</p>
            <p>• 转账金额必须精确匹配</p>
            <p>• 转账不可撤销，请仔细核对</p>
          </div>
        </div>
      </div>
    </div>
  )
}
