'use client'

import Link from 'next/link'
import Image from 'next/image'
import FavoriteButton from './FavoriteButton'
import { formatUSDT } from '@/lib/utils'

interface Product {
  id: string
  title: string
  price: number
  images: string
  seller: {
    name: string | null
    userId: string
    avatar?: string | null
  }
  _count?: {
    favorites: number
  }
}

interface CompactProductCardProps {
  product: Product
}

export default function CompactProductCard({ product }: CompactProductCardProps) {
  const firstImage = product.images ? product.images.split(',')[0] : ''
  const favoriteCount = product._count?.favorites || 0

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 overflow-hidden">
      <Link href={`/products/${product.id}`} className="block">
        {/* 商品图片 */}
        <div className="relative h-48">
          {firstImage ? (
            <Image
              src={firstImage}
              alt={product.title}
              fill
              className="object-cover"
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <div className="text-gray-400 text-4xl">📦</div>
            </div>
          )}

          {/* 收藏按钮 - 右上角 */}
          <div className="absolute top-3 right-3">
            <FavoriteButton productId={product.id} />
          </div>
        </div>
      </Link>

      {/* 商品信息 */}
      <div className="p-4">
        {/* 商品标题 */}
        <Link href={`/products/${product.id}`}>
          <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors mb-2 overflow-hidden" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {product.title}
          </h3>
        </Link>

        {/* 价格 */}
        <div className="text-2xl font-bold text-blue-600 mb-3">
          {formatUSDT(product.price)}
        </div>

        {/* 用户信息和收藏数 */}
        <div className="flex items-center justify-between">
          {/* 用户信息 */}
          <Link
            href={`/products/user/${product.seller.userId}`}
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
          >
            {/* 用户头像 */}
            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
              {product.seller.avatar ? (
                <Image
                  src={product.seller.avatar}
                  alt={product.seller.name || '用户头像'}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                  {product.seller.name ? product.seller.name.charAt(0).toUpperCase() : 'U'}
                </div>
              )}
            </div>

            {/* 用户名称 */}
            <span className="text-sm text-gray-600 truncate max-w-[100px]">
              {product.seller.name || '匿名用户'}
            </span>
          </Link>

          {/* 收藏数 */}
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
            </svg>
            <span>{favoriteCount}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
