'use client'

import React, { useState, useEffect } from 'react'
import {
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface RiskFactor {
  type: string
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  description: string
  score: number
}

interface RiskAssessment {
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  riskScore: number
  riskFactors: RiskFactor[]
  requiresApproval: boolean
  requiresKYC: boolean
  maxAllowedAmount?: number
  recommendations: string[]
  canProceed: boolean
}

interface RiskAssessmentDisplayProps {
  amount: number
  walletAddress?: string
  mediatorId?: string
  onAssessmentComplete?: (assessment: RiskAssessment) => void
  className?: string
}

export default function RiskAssessmentDisplay({
  amount,
  walletAddress,
  mediatorId,
  onAssessmentComplete,
  className = ''
}: RiskAssessmentDisplayProps) {
  const [assessment, setAssessment] = useState<RiskAssessment | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (amount > 0) {
      performRiskAssessment()
    }
  }, [amount, walletAddress, mediatorId])

  const performRiskAssessment = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/security/risk-assessment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount,
          walletAddress,
          mediatorId,
          transactionType: 'ESCROW_TRANSACTION'
        })
      })

      const data = await response.json()

      if (data.success) {
        setAssessment(data.data)
        onAssessmentComplete?.(data.data)
      } else {
        setError(data.error || '风险评估失败')
      }
    } catch (error) {
      console.error('风险评估失败:', error)
      setError('风险评估失败')
    } finally {
      setLoading(false)
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'LOW':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'HIGH':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'CRITICAL':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'LOW':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'MEDIUM':
        return <InformationCircleIcon className="h-5 w-5 text-yellow-500" />
      case 'HIGH':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />
      case 'CRITICAL':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ShieldCheckIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getRiskLevelLabel = (level: string) => {
    switch (level) {
      case 'LOW':
        return '低风险'
      case 'MEDIUM':
        return '中等风险'
      case 'HIGH':
        return '高风险'
      case 'CRITICAL':
        return '极高风险'
      default:
        return '未知风险'
    }
  }

  const getRiskFactorLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'LARGE_AMOUNT': '大额交易',
      'FREQUENT_TRANSACTIONS': '频繁交易',
      'SUSPICIOUS_PATTERN': '可疑模式',
      'NEW_USER': '新用户',
      'BLACKLISTED_ADDRESS': '黑名单地址',
      'VELOCITY_CHECK': '交易频率',
      'REPUTATION_RISK': '信誉风险'
    }
    return labels[type] || type
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="flex items-center space-x-3 mb-4">
          <ShieldCheckIcon className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">安全风险评估</h3>
        </div>
        
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">正在评估交易风险...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="flex items-center space-x-3 mb-4">
          <ShieldCheckIcon className="h-6 w-6 text-red-600" />
          <h3 className="text-lg font-semibold text-gray-900">安全风险评估</h3>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center space-x-2">
            <XCircleIcon className="h-5 w-5 text-red-500" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
          <button
            onClick={performRiskAssessment}
            className="mt-3 text-sm text-red-600 hover:text-red-800 underline"
          >
            重新评估
          </button>
        </div>
      </div>
    )
  }

  if (!assessment) {
    return null
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-6">
        <ShieldCheckIcon className="h-6 w-6 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">安全风险评估</h3>
      </div>

      {/* 风险等级概览 */}
      <div className={`rounded-lg border p-4 mb-6 ${getRiskLevelColor(assessment.riskLevel)}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            {getRiskLevelIcon(assessment.riskLevel)}
            <span className="font-medium">
              {getRiskLevelLabel(assessment.riskLevel)}
            </span>
          </div>
          <span className="text-sm font-mono">
            风险分数: {assessment.riskScore.toFixed(1)}
          </span>
        </div>
        
        <div className="text-sm">
          <div className="flex items-center space-x-4">
            {assessment.requiresApproval && (
              <div className="flex items-center space-x-1">
                <ClockIcon className="h-4 w-4" />
                <span>需要人工审核</span>
              </div>
            )}
            {assessment.requiresKYC && (
              <div className="flex items-center space-x-1">
                <InformationCircleIcon className="h-4 w-4" />
                <span>需要身份验证</span>
              </div>
            )}
            {assessment.maxAllowedAmount && (
              <div className="flex items-center space-x-1">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span>限额: {assessment.maxAllowedAmount} USDT</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 风险因素详情 */}
      {assessment.riskFactors.length > 0 && (
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-3">风险因素</h4>
          <div className="space-y-3">
            {assessment.riskFactors.map((factor, index) => (
              <div
                key={index}
                className={`rounded-md border p-3 ${getRiskLevelColor(factor.level)}`}
              >
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    {getRiskLevelIcon(factor.level)}
                    <span className="font-medium text-sm">
                      {getRiskFactorLabel(factor.type)}
                    </span>
                  </div>
                  <span className="text-xs font-mono">
                    +{factor.score}分
                  </span>
                </div>
                <p className="text-sm">{factor.description}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 建议和说明 */}
      {assessment.recommendations.length > 0 && (
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-3">安全建议</h4>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <ul className="space-y-2">
              {assessment.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm text-blue-700">
                  <InformationCircleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>{recommendation}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* 交易状态 */}
      <div className="border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">交易状态</span>
          <div className="flex items-center space-x-2">
            {assessment.canProceed ? (
              <>
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium text-green-700">
                  {assessment.requiresApproval ? '等待审核' : '可以继续'}
                </span>
              </>
            ) : (
              <>
                <XCircleIcon className="h-5 w-5 text-red-500" />
                <span className="text-sm font-medium text-red-700">暂时无法继续</span>
              </>
            )}
          </div>
        </div>
        
        {assessment.requiresApproval && (
          <div className="mt-2 text-xs text-gray-500">
            由于风险等级较高，此交易需要人工审核。预计审核时间：1-24小时。
          </div>
        )}
      </div>

      {/* 刷新按钮 */}
      <div className="mt-4 text-center">
        <button
          onClick={performRiskAssessment}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          重新评估风险
        </button>
      </div>
    </div>
  )
}
