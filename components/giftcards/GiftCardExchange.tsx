"use client"

import React, { useState, useEffect } from 'react'
import { 
  GiftIcon, 
  CurrencyDollarIcon, 
  ShoppingBagIcon,
  SparklesIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface GiftCard {
  id: string
  name: string
  description: string
  value: number
  cost: number // 兑换所需保证金
  type: 'GIFT_CARD' | 'RECHARGE_CARD'
  category: string
  image?: string
  available: boolean
  stock?: number
}

interface DeliveryMethod {
  id: string
  name: string
  description: string
  icon: string
}

interface UserBalance {
  available: number
  total: number
}

interface GiftCardExchangeProps {
  userBalance: UserBalance
  onExchange: (giftCard: GiftCard) => void
}

export default function GiftCardExchange({ userBalance, onExchange }: GiftCardExchangeProps) {
  const [giftCards, setGiftCards] = useState<GiftCard[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedCard, setSelectedCard] = useState<GiftCard | null>(null)
  const [exchangeLoading, setExchangeLoading] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [selectedDeliveryMethod, setSelectedDeliveryMethod] = useState('redemption_code')
  const [quantity, setQuantity] = useState(1)

  // 发货方式选项
  const deliveryMethods: DeliveryMethod[] = [
    {
      id: 'redemption_code',
      name: '兑换码',
      description: '生成兑换码，可分享给他人使用',
      icon: '🎫'
    },
    {
      id: 'gift_card',
      name: '礼品卡',
      description: '生成实体礼品卡，可作为礼品赠送',
      icon: '🎁'
    },
    {
      id: 'direct_recharge',
      name: '直充账号',
      description: '直接充值到您的账户余额',
      icon: '💰'
    }
  ]

  useEffect(() => {
    fetchGiftCards()
  }, [])

  const fetchGiftCards = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/giftcards')

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setGiftCards(result.data.giftCards)
        } else {
          setError(result.error || '获取礼品卡失败')
        }
      } else {
        setError('获取礼品卡失败，请稍后重试')
      }
    } catch (error) {
      console.error('获取礼品卡失败:', error)
      setError('获取礼品卡失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleExchangeClick = (giftCard: GiftCard) => {
    if (userBalance.available < giftCard.cost) {
      setError(`余额不足，需要 ${giftCard.cost} USDT，当前可用余额 ${userBalance.available.toFixed(2)} USDT`)
      return
    }
    
    setSelectedCard(giftCard)
    setShowConfirmModal(true)
    setError('')
  }

  const confirmExchange = async () => {
    if (!selectedCard) return

    setExchangeLoading(true)
    try {
      const response = await fetch('/api/giftcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          giftCardId: selectedCard.id,
          deliveryMethod: selectedDeliveryMethod,
          quantity: quantity
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        // 根据发货方式显示不同的成功消息
        let successMessage = result.message || `成功购买 ${selectedCard.name}！`

        if (result.data?.deliveryMethod === 'direct_recharge') {
          successMessage += `\n已直充 ${result.data.rechargedAmount} USDT 到您的账户`
        } else if (result.data?.redemptionCodes) {
          const codes = result.data.redemptionCodes
          if (codes.length > 0) {
            successMessage += `\n兑换码: ${codes.map((code: any) => code.code).join(', ')}`
            successMessage += `\n请妥善保管兑换码，有效期至 ${new Date(codes[0].expiresAt).toLocaleDateString()}`
          }
        } else if (result.data?.giftCards) {
          const cards = result.data.giftCards
          if (cards.length > 0) {
            successMessage += `\n礼品卡号: ${cards.map((card: any) => card.cardCode).join(', ')}`
            successMessage += `\n请妥善保管礼品卡，有效期至 ${new Date(cards[0].validUntil).toLocaleDateString()}`
          }
        }

        alert(successMessage)

        onExchange(selectedCard)
        setShowConfirmModal(false)
        setSelectedCard(null)
        setQuantity(1)
        setSelectedDeliveryMethod('redemption_code')
        setError('')

        // 刷新礼品卡列表
        await fetchGiftCards()
      } else {
        setError(result.error || '购买失败，请稍后重试')
      }
    } catch (error) {
      console.error('兑换失败:', error)
      setError('兑换失败，请稍后重试')
    } finally {
      setExchangeLoading(false)
    }
  }

  const getCardIcon = (type: string) => {
    switch (type) {
      case 'GIFT_CARD':
        return <GiftIcon className="h-6 w-6" />
      case 'RECHARGE_CARD':
        return <CurrencyDollarIcon className="h-6 w-6" />
      default:
        return <GiftIcon className="h-6 w-6" />
    }
  }

  const getCardColor = (type: string) => {
    switch (type) {
      case 'GIFT_CARD':
        return 'from-pink-500 to-pink-600'
      case 'RECHARGE_CARD':
        return 'from-blue-500 to-blue-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  const getDiscountPercentage = (value: number, cost: number) => {
    return Math.round((1 - cost / value) * 100)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">礼品卡兑换</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">礼品卡兑换</h3>
        <div className="text-sm text-gray-600">
          可用余额: <span className="font-medium text-green-600">{userBalance.available.toFixed(2)} USDT</span>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">兑换说明</h4>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>使用保证金余额兑换礼品卡，享受优惠折扣</li>
                <li>兑换后的礼品卡将自动添加到您的账户</li>
                <li>礼品卡有效期为兑换后30天</li>
                <li>部分礼品卡数量有限，先到先得</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {giftCards.map((card) => (
          <div
            key={card.id}
            className="relative bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
          >
            {/* 折扣标签 */}
            <div className="absolute top-3 right-3 z-10">
              <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                {getDiscountPercentage(card.value, card.cost)}% OFF
              </span>
            </div>

            {/* 卡片头部 */}
            <div className={`bg-gradient-to-r ${getCardColor(card.type)} p-4 text-white`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {getCardIcon(card.type)}
                  <div className="ml-3">
                    <h4 className="font-medium">{card.name}</h4>
                    <p className="text-sm opacity-90">价值 {card.value} USDT</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 卡片内容 */}
            <div className="p-4">
              <p className="text-sm text-gray-600 mb-4">{card.description}</p>
              
              <div className="flex items-center justify-between mb-4">
                <div>
                  <span className="text-lg font-bold text-gray-900">{card.cost} USDT</span>
                  <span className="text-sm text-gray-500 line-through ml-2">{card.value} USDT</span>
                </div>
                {card.stock && (
                  <span className="text-xs text-gray-500">库存: {card.stock}</span>
                )}
              </div>

              <button
                onClick={() => handleExchangeClick(card)}
                disabled={!card.available || userBalance.available < card.cost}
                className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
                  card.available && userBalance.available >= card.cost
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {!card.available 
                  ? '暂时缺货' 
                  : userBalance.available < card.cost 
                    ? '余额不足' 
                    : '立即兑换'
                }
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* 确认兑换模态框 */}
      {showConfirmModal && selectedCard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">确认购买礼品卡</h3>

            {/* 商品信息 */}
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-600">礼品卡:</span>
                <span className="font-medium">{selectedCard.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">单价:</span>
                <span className="font-medium">{selectedCard.cost} USDT</span>
              </div>
            </div>

            {/* 数量选择 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">购买数量</label>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  -
                </button>
                <span className="w-12 text-center font-medium">{quantity}</span>
                <button
                  onClick={() => setQuantity(Math.min(10, quantity + 1))}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>

            {/* 发货方式选择 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">发货方式</label>
              <div className="space-y-2">
                {deliveryMethods.map((method) => (
                  <label key={method.id} className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="deliveryMethod"
                      value={method.id}
                      checked={selectedDeliveryMethod === method.id}
                      onChange={(e) => setSelectedDeliveryMethod(e.target.value)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{method.icon}</span>
                        <span className="font-medium">{method.name}</span>
                      </div>
                      <p className="text-sm text-gray-600">{method.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* 总计 */}
            <div className="border-t pt-4 mb-6">
              <div className="flex justify-between text-lg font-semibold">
                <span>总计:</span>
                <span className="text-blue-600">{(selectedCard.cost * quantity).toFixed(2)} USDT</span>
              </div>
              <div className="text-sm text-gray-600 mt-1">
                节省: {((selectedCard.value - selectedCard.cost) * quantity).toFixed(2)} USDT
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                disabled={exchangeLoading}
                className="flex-1 py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
              >
                取消
              </button>
              <button
                onClick={confirmExchange}
                disabled={exchangeLoading}
                className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {exchangeLoading ? '购买中...' : '确认购买'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
