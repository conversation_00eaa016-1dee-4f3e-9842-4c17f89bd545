"use client"

import React, { useState, useEffect } from 'react'
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  TruckIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface OrderStep {
  id: string
  title: string
  description: string
  status: 'completed' | 'current' | 'pending' | 'error'
  timestamp?: string
  details?: string
  icon: React.ComponentType<any>
}

interface EscrowOrderTrackerProps {
  orderId: string
  className?: string
}

interface OrderDetails {
  id: string
  orderNumber: string
  status: string
  escrowStatus: string
  totalAmount: number
  escrowFee: number
  createdAt: string
  paymentConfirmed: boolean
  shippedAt?: string
  receivedAt?: string
  escrowFundedAt?: string
  escrowReleasedAt?: string
  trackingNumber?: string
  disputeStatus?: string
  product: {
    title: string
    images: string[]
  }
  buyer: {
    id: string
    name: string
  }
  seller: {
    id: string
    name: string
  }
  mediator: {
    id: string
    name: string
    bnbWalletAddress: string
  }
  escrowChatRooms: Array<{
    id: string
    status: string
  }>
}

export default function EscrowOrderTracker({ orderId, className = '' }: EscrowOrderTrackerProps) {
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchOrderDetails()
    
    // 设置定时刷新
    const interval = setInterval(fetchOrderDetails, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [orderId])

  const fetchOrderDetails = async () => {
    try {
      if (!loading) setRefreshing(true)
      
      const response = await fetch(`/api/escrow/orders/${orderId}`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setOrderDetails(result.data)
          setError('')
        } else {
          setError(result.error || '获取订单详情失败')
        }
      } else {
        setError('获取订单详情失败')
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const generateOrderSteps = (order: OrderDetails): OrderStep[] => {
    const steps: OrderStep[] = [
      {
        id: 'created',
        title: '订单创建',
        description: '托管订单已创建，等待买家支付',
        status: 'completed',
        timestamp: order.createdAt,
        details: `订单金额: ${order.totalAmount} USDT + 托管费: ${order.escrowFee} USDT`,
        icon: DocumentTextIcon
      },
      {
        id: 'payment',
        title: '资金托管',
        description: '买家支付资金到中间人托管账户',
        status: order.paymentConfirmed ? 'completed' : 
                order.status === 'PENDING_PAYMENT' ? 'current' : 'pending',
        timestamp: order.escrowFundedAt,
        details: order.paymentConfirmed ? 
          `资金已托管到: ${order.mediator.bnbWalletAddress.slice(0, 6)}...${order.mediator.bnbWalletAddress.slice(-4)}` :
          '等待买家支付到托管账户',
        icon: CurrencyDollarIcon
      },
      {
        id: 'shipping',
        title: '卖家发货',
        description: '卖家确认发货并提供物流信息',
        status: order.shippedAt ? 'completed' : 
                order.status === 'PAID' ? 'current' :
                order.paymentConfirmed ? 'pending' : 'pending',
        timestamp: order.shippedAt,
        details: order.trackingNumber ? 
          `快递单号: ${order.trackingNumber}` : 
          order.shippedAt ? '已发货' : '等待卖家发货',
        icon: TruckIcon
      },
      {
        id: 'delivery',
        title: '买家确认',
        description: '买家确认收货，订单完成',
        status: order.receivedAt ? 'completed' : 
                order.status === 'SHIPPED' ? 'current' : 'pending',
        timestamp: order.receivedAt,
        details: order.receivedAt ? '买家已确认收货' : '等待买家确认收货',
        icon: CheckCircleIcon
      },
      {
        id: 'release',
        title: '资金释放',
        description: '中间人释放资金给卖家',
        status: order.escrowReleasedAt ? 'completed' : 
                order.status === 'COMPLETED' ? 'completed' : 'pending',
        timestamp: order.escrowReleasedAt,
        details: order.escrowReleasedAt ? '资金已释放给卖家' : '等待资金释放',
        icon: ShieldCheckIcon
      }
    ]

    // 如果有争议，添加争议步骤
    if (order.disputeStatus && order.disputeStatus !== 'NONE') {
      const disputeStep: OrderStep = {
        id: 'dispute',
        title: '争议处理',
        description: '订单存在争议，正在处理中',
        status: 'error',
        details: '请等待中间人或管理员处理争议',
        icon: ExclamationTriangleIcon
      }
      
      // 在适当位置插入争议步骤
      const insertIndex = steps.findIndex(step => step.status === 'current' || step.status === 'pending')
      if (insertIndex > 0) {
        steps.splice(insertIndex, 0, disputeStep)
      }
    }

    return steps
  }

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500'
      case 'current':
        return 'bg-blue-500'
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-gray-300'
    }
  }

  const getStepTextColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600'
      case 'current':
        return 'text-blue-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-500'
    }
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error || !orderDetails) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">加载失败</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <button
            onClick={fetchOrderDetails}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  const steps = generateOrderSteps(orderDetails)

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      {/* 头部信息 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">订单跟踪</h3>
            <p className="text-sm text-gray-500">订单号: {orderDetails.orderNumber}</p>
          </div>
          <div className="flex items-center space-x-2">
            {refreshing && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            )}
            <button
              onClick={fetchOrderDetails}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              刷新
            </button>
          </div>
        </div>
      </div>

      {/* 订单基本信息 */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm text-gray-500">买家</div>
            <div className="font-medium">{orderDetails.buyer.name}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500">卖家</div>
            <div className="font-medium">{orderDetails.seller.name}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500">中间人</div>
            <div className="font-medium">{orderDetails.mediator.name}</div>
          </div>
        </div>
      </div>

      {/* 进度跟踪 */}
      <div className="p-6">
        <div className="flow-root">
          <ul className="-mb-8">
            {steps.map((step, stepIdx) => (
              <li key={step.id}>
                <div className="relative pb-8">
                  {stepIdx !== steps.length - 1 ? (
                    <span
                      className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                      aria-hidden="true"
                    />
                  ) : null}
                  <div className="relative flex space-x-3">
                    <div>
                      <span
                        className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getStepStatusColor(step.status)}`}
                      >
                        <step.icon className="h-5 w-5 text-white" aria-hidden="true" />
                      </span>
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5">
                      <div>
                        <p className={`text-sm font-medium ${getStepTextColor(step.status)}`}>
                          {step.title}
                        </p>
                        <p className="text-sm text-gray-500">{step.description}</p>
                        {step.details && (
                          <p className="text-xs text-gray-400 mt-1">{step.details}</p>
                        )}
                      </div>
                      {step.timestamp && (
                        <div className="mt-2 text-xs text-gray-400">
                          {new Date(step.timestamp).toLocaleString('zh-CN')}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* 聊天室入口 */}
      {orderDetails.escrowChatRooms.length > 0 && (
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">托管聊天室</span>
            </div>
            <button
              onClick={() => window.open(`/chat/escrow/${orderDetails.escrowChatRooms[0].id}`, '_blank')}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              进入聊天
            </button>
          </div>
        </div>
      )}

      {/* 重要提示 */}
      <div className="p-6 border-t border-gray-200">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex">
            <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
            <div className="ml-3">
              <h4 className="text-sm font-medium text-blue-800">托管服务说明</h4>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>您的资金由专业中间人安全托管</li>
                  <li>如有问题可通过聊天室与各方沟通</li>
                  <li>如需争议处理，请及时联系中间人</li>
                  <li>订单完成后资金将自动释放给卖家</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
