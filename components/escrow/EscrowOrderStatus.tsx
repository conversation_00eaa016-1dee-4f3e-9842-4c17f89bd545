'use client'

import React from 'react'
import { 
  ClockIcon, 
  CreditCardIcon, 
  TruckIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'

interface EscrowOrderStatusProps {
  order: {
    id: string
    orderNumber: string
    status: string
    escrowStatus: string
    totalAmount: number
    escrowFee?: number
    createdAt: string
    escrowFundedAt?: string
    shippedAt?: string
    deliveredAt?: string
    escrowReleasedAt?: string
    disputeCreatedAt?: string
    mediator?: {
      id: string
      name: string
      avatar?: string
    }
    product: {
      title: string
      images?: string
    }
  }
  userRole: 'buyer' | 'seller' | 'mediator'
  onAction?: (action: string, data?: any) => void
}

export default function EscrowOrderStatus({
  order,
  userRole,
  onAction
}: EscrowOrderStatusProps) {
  const getStatusInfo = () => {
    switch (order.escrowStatus) {
      case 'PENDING':
        return {
          icon: ClockIcon,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: '等待支付',
          description: '买家需要支付托管资金'
        }
      case 'FUNDED':
        return {
          icon: CreditCardIcon,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: '资金已托管',
          description: '等待卖家发货'
        }
      case 'SHIPPED':
        return {
          icon: TruckIcon,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          title: '已发货',
          description: '等待买家确认收货'
        }
      case 'RELEASED':
        return {
          icon: CheckCircleIcon,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: '交易完成',
          description: '资金已释放给卖家'
        }
      case 'DISPUTED':
        return {
          icon: ExclamationTriangleIcon,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: '争议处理中',
          description: '等待仲裁结果'
        }
      case 'REFUNDED':
        return {
          icon: CheckCircleIcon,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: '已退款',
          description: '资金已退还给买家'
        }
      default:
        return {
          icon: ClockIcon,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: '未知状态',
          description: ''
        }
    }
  }

  const statusInfo = getStatusInfo()
  const StatusIcon = statusInfo.icon

  const getAvailableActions = () => {
    const actions: Array<{
      label: string
      action: string
      variant: 'primary' | 'secondary'
    }> = []

    if (userRole === 'buyer') {
      if (order.escrowStatus === 'PENDING') {
        actions.push({
          label: '支付托管资金',
          action: 'fund_escrow',
          variant: 'primary'
        })
      }
      if (order.escrowStatus === 'SHIPPED' || order.status === 'SHIPPED') {
        actions.push({
          label: '确认收货',
          action: 'confirm_delivery',
          variant: 'primary'
        })
      }
      if (['FUNDED', 'SHIPPED'].includes(order.escrowStatus)) {
        actions.push({
          label: '申请仲裁',
          action: 'create_dispute',
          variant: 'secondary'
        })
      }
    }

    if (userRole === 'seller') {
      if (order.escrowStatus === 'FUNDED') {
        actions.push({
          label: '确认发货',
          action: 'ship_order',
          variant: 'primary'
        })
      }
      if (['FUNDED', 'SHIPPED'].includes(order.escrowStatus)) {
        actions.push({
          label: '申请仲裁',
          action: 'create_dispute',
          variant: 'secondary'
        })
      }
    }

    if (userRole === 'mediator') {
      if (order.escrowStatus === 'DISPUTED') {
        actions.push({
          label: '处理争议',
          action: 'handle_dispute',
          variant: 'primary'
        })
      }
      if (['FUNDED', 'SHIPPED'].includes(order.escrowStatus)) {
        actions.push({
          label: '释放资金',
          action: 'release_funds',
          variant: 'secondary'
        })
      }
    }

    return actions
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const actions = getAvailableActions()

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      {/* 状态头部 */}
      <div className={`${statusInfo.bgColor} ${statusInfo.borderColor} border rounded-lg p-4 mb-6`}>
        <div className="flex items-center">
          <StatusIcon className={`h-6 w-6 ${statusInfo.color} mr-3`} />
          <div>
            <h3 className={`text-lg font-semibold ${statusInfo.color}`}>
              {statusInfo.title}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {statusInfo.description}
            </p>
          </div>
        </div>
      </div>

      {/* 订单信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">订单信息</h4>
          <dl className="space-y-2 text-sm">
            <div className="flex justify-between">
              <dt className="text-gray-500">订单号:</dt>
              <dd className="text-gray-900 font-mono">{order.orderNumber}</dd>
            </div>
            <div className="flex justify-between">
              <dt className="text-gray-500">商品金额:</dt>
              <dd className="text-gray-900">{order.totalAmount.toFixed(2)} USDT</dd>
            </div>
            {order.escrowFee && (
              <div className="flex justify-between">
                <dt className="text-gray-500">托管费用:</dt>
                <dd className="text-gray-900">{order.escrowFee.toFixed(2)} USDT</dd>
              </div>
            )}
            <div className="flex justify-between font-semibold">
              <dt className="text-gray-900">总计:</dt>
              <dd className="text-gray-900">
                {(order.totalAmount + (order.escrowFee || 0)).toFixed(2)} USDT
              </dd>
            </div>
          </dl>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">时间线</h4>
          <dl className="space-y-2 text-sm">
            <div className="flex justify-between">
              <dt className="text-gray-500">订单创建:</dt>
              <dd className="text-gray-900">{formatDate(order.createdAt)}</dd>
            </div>
            {order.escrowFundedAt && (
              <div className="flex justify-between">
                <dt className="text-gray-500">资金托管:</dt>
                <dd className="text-gray-900">{formatDate(order.escrowFundedAt)}</dd>
              </div>
            )}
            {order.shippedAt && (
              <div className="flex justify-between">
                <dt className="text-gray-500">发货时间:</dt>
                <dd className="text-gray-900">{formatDate(order.shippedAt)}</dd>
              </div>
            )}
            {order.deliveredAt && (
              <div className="flex justify-between">
                <dt className="text-gray-500">确认收货:</dt>
                <dd className="text-gray-900">{formatDate(order.deliveredAt)}</dd>
              </div>
            )}
            {order.escrowReleasedAt && (
              <div className="flex justify-between">
                <dt className="text-gray-500">资金释放:</dt>
                <dd className="text-gray-900">{formatDate(order.escrowReleasedAt)}</dd>
              </div>
            )}
            {order.disputeCreatedAt && (
              <div className="flex justify-between">
                <dt className="text-gray-500">争议创建:</dt>
                <dd className="text-gray-900">{formatDate(order.disputeCreatedAt)}</dd>
              </div>
            )}
          </dl>
        </div>
      </div>

      {/* 中间人信息 */}
      {order.mediator && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-5 w-5 text-blue-600 mr-2" />
            <h4 className="text-sm font-medium text-gray-900">托管中间人</h4>
          </div>
          <div className="mt-2 flex items-center">
            {order.mediator.avatar ? (
              <img
                src={order.mediator.avatar}
                alt={order.mediator.name}
                className="h-8 w-8 rounded-full mr-3"
              />
            ) : (
              <div className="h-8 w-8 rounded-full bg-gray-300 mr-3"></div>
            )}
            <span className="text-sm text-gray-900">{order.mediator.name}</span>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      {actions.length > 0 && (
        <div className="flex flex-wrap gap-3">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={() => onAction?.(action.action)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                action.variant === 'primary'
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-200 text-gray-900 hover:bg-gray-300'
              }`}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
