"use client"

import React, { useState, useEffect } from 'react'
import { 
  ShieldCheckIcon, 
  StarIcon, 
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  UserIcon,
  TrophyIcon,
  WalletIcon
} from '@heroicons/react/24/outline'

interface Mediator {
  id: string
  name: string
  avatar?: string
  mediatorFeeRate: number
  mediatorDeposit: number
  mediatorReputation: number
  mediatorVerifiedAt: string
  introduction: string
  experience?: string
  stats: {
    totalOrders: number
    totalVolume: number
    completedOrders: number
    activeOrders: number
    successRate: number
    availableDeposit: number
  }
}

interface EnhancedEscrowSelectorProps {
  productPrice: number
  quantity?: number
  onMediatorSelect: (mediator: Mediator | null, useEscrow: boolean) => void
  selectedMediatorId?: string
  className?: string
}

export default function EnhancedEscrowSelector({ 
  productPrice, 
  quantity = 1,
  onMediatorSelect, 
  selectedMediatorId,
  className = '' 
}: EnhancedEscrowSelectorProps) {
  const [mediators, setMediators] = useState<Mediator[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showDetails, setShowDetails] = useState<string | null>(null)

  const totalAmount = productPrice * quantity
  const isEligibleForEscrow = totalAmount >= 100

  useEffect(() => {
    if (isEligibleForEscrow) {
      fetchMediators()
    }
  }, [totalAmount, isEligibleForEscrow])

  const fetchMediators = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch(`/api/mediator/list?minAmount=${totalAmount}&sortBy=reputation&limit=8`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setMediators(result.data.mediators)
        } else {
          setError(result.error || '获取中间人列表失败')
        }
      } else {
        setError('获取中间人列表失败')
      }
    } catch (error) {
      console.error('获取中间人列表失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleMediatorSelect = (mediator: Mediator | null) => {
    onMediatorSelect(mediator, !!mediator)
  }

  const calculateEscrowFee = (mediator: Mediator) => {
    return totalAmount * mediator.mediatorFeeRate
  }

  const calculateTotalWithEscrow = (mediator: Mediator) => {
    return totalAmount + calculateEscrowFee(mediator)
  }

  const getReputationColor = (reputation: number) => {
    if (reputation >= 4.5) return 'text-green-600'
    if (reputation >= 4.0) return 'text-blue-600'
    if (reputation >= 3.5) return 'text-yellow-600'
    return 'text-gray-600'
  }

  const getReputationBadge = (reputation: number) => {
    if (reputation >= 4.8) return { text: '金牌中间人', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    if (reputation >= 4.5) return { text: '银牌中间人', color: 'bg-gray-100 text-gray-800 border-gray-200' }
    if (reputation >= 4.0) return { text: '铜牌中间人', color: 'bg-orange-100 text-orange-800 border-orange-200' }
    return { text: '认证中间人', color: 'bg-blue-100 text-blue-800 border-blue-200' }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!isEligibleForEscrow) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-gray-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900">托管服务不可用</h4>
            <p className="mt-1 text-sm text-gray-600">
              托管服务仅适用于100 USDT以上的商品交易。
            </p>
            <p className="text-sm text-gray-600">
              当前订单总额：{totalAmount.toFixed(2)} USDT
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">选择交易方式</h3>
              <p className="text-sm text-gray-500">为您的交易选择合适的安全保障</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">订单金额</div>
            <div className="text-lg font-semibold text-gray-900">{totalAmount.toFixed(2)} USDT</div>
          </div>
        </div>
      </div>

      {/* 托管服务说明 */}
      <div className="p-6 bg-blue-50 border-b border-gray-200">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">托管服务优势</h4>
            <div className="mt-2 text-sm text-blue-700 grid grid-cols-1 md:grid-cols-2 gap-2">
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                <span>资金安全托管</span>
              </div>
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                <span>专业争议仲裁</span>
              </div>
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                <span>交易过程监督</span>
              </div>
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                <span>多方聊天支持</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
                <button
                  onClick={fetchMediators}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  重试
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {/* 普通交易选项 */}
          <label className="flex items-start p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
            <input
              type="radio"
              name="escrow"
              value=""
              checked={!selectedMediatorId}
              onChange={() => handleMediatorSelect(null)}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <div className="ml-4 flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-base font-medium text-gray-900">普通交易</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    直接与卖家交易，无额外费用，适合信任度较高的交易
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900">{totalAmount.toFixed(2)} USDT</div>
                  <div className="text-sm text-gray-500">无额外费用</div>
                </div>
              </div>
            </div>
          </label>

          {/* 托管服务选项 */}
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="animate-pulse p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="h-4 w-4 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : mediators.length > 0 ? (
            mediators.map((mediator) => {
              const escrowFee = calculateEscrowFee(mediator)
              const totalWithEscrow = calculateTotalWithEscrow(mediator)
              const badge = getReputationBadge(mediator.mediatorReputation)
              const isSelected = selectedMediatorId === mediator.id
              const canHandle = mediator.stats.availableDeposit >= totalAmount
              
              return (
                <div key={mediator.id} className="space-y-2">
                  <label className={`flex items-start p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                    isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'
                  } ${!canHandle ? 'opacity-60' : ''}`}>
                    <input
                      type="radio"
                      name="escrow"
                      value={mediator.id}
                      checked={isSelected}
                      onChange={() => handleMediatorSelect(mediator)}
                      disabled={!canHandle}
                      className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <div className="ml-4 flex-1">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            {mediator.avatar ? (
                              <img
                                src={mediator.avatar}
                                alt={mediator.name}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <UserIcon className="h-6 w-6 text-gray-600" />
                              </div>
                            )}
                            <div className="ml-3">
                              <div className="flex items-center">
                                <h4 className="text-base font-medium text-gray-900">{mediator.name}</h4>
                                <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full border ${badge.color}`}>
                                  {badge.text}
                                </span>
                              </div>
                              <div className="flex items-center mt-1">
                                <div className="flex items-center">
                                  <StarIcon className={`h-4 w-4 ${getReputationColor(mediator.mediatorReputation)}`} />
                                  <span className={`text-sm ml-1 ${getReputationColor(mediator.mediatorReputation)}`}>
                                    {mediator.mediatorReputation.toFixed(1)}
                                  </span>
                                </div>
                                <span className="mx-2 text-gray-300">•</span>
                                <span className="text-sm text-gray-600">
                                  认证于 {formatDate(mediator.mediatorVerifiedAt)}
                                </span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <TrophyIcon className="h-4 w-4 mr-1" />
                              <span>成功率 {mediator.stats.successRate.toFixed(1)}%</span>
                            </div>
                            <div className="flex items-center">
                              <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                              <span>交易量 {(mediator.stats.totalVolume / 1000).toFixed(1)}K</span>
                            </div>
                            <div className="flex items-center">
                              <ClockIcon className="h-4 w-4 mr-1" />
                              <span>订单 {mediator.stats.totalOrders}</span>
                            </div>
                            <div className="flex items-center">
                              <WalletIcon className="h-4 w-4 mr-1" />
                              <span>保证金 {(mediator.mediatorDeposit / 1000).toFixed(1)}K</span>
                            </div>
                          </div>

                          {!canHandle && (
                            <div className="mt-2 flex items-center text-sm text-yellow-600">
                              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                              <span>保证金不足，无法处理此订单</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="text-right ml-4">
                          <div className="text-lg font-semibold text-gray-900">{totalWithEscrow.toFixed(2)} USDT</div>
                          <div className="text-sm text-gray-500">
                            含服务费 {escrowFee.toFixed(2)} USDT
                          </div>
                          <div className="text-xs text-gray-400">
                            费率 {(mediator.mediatorFeeRate * 100).toFixed(1)}%
                          </div>
                        </div>
                      </div>

                      {mediator.introduction && (
                        <p className="mt-3 text-sm text-gray-600 line-clamp-2">
                          {mediator.introduction}
                        </p>
                      )}
                    </div>
                  </label>

                  {/* 详细信息展开 */}
                  {isSelected && (
                    <div className="ml-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h5 className="text-sm font-medium text-blue-900 mb-2">托管服务详情</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-blue-700 font-medium">服务费用：</span>
                          <span className="text-blue-800">{escrowFee.toFixed(2)} USDT ({(mediator.mediatorFeeRate * 100).toFixed(1)}%)</span>
                        </div>
                        <div>
                          <span className="text-blue-700 font-medium">您的实付：</span>
                          <span className="text-blue-800">{((mediator.mediatorFeeRate * 0.7) * 100).toFixed(1)}% (平台抽取30%)</span>
                        </div>
                        <div>
                          <span className="text-blue-700 font-medium">可用保证金：</span>
                          <span className="text-blue-800">{mediator.stats.availableDeposit.toFixed(0)} USDT</span>
                        </div>
                        <div>
                          <span className="text-blue-700 font-medium">活跃订单：</span>
                          <span className="text-blue-800">{mediator.stats.activeOrders} 个</span>
                        </div>
                      </div>
                      {mediator.experience && (
                        <div className="mt-3">
                          <span className="text-blue-700 font-medium">经验介绍：</span>
                          <p className="text-blue-800 mt-1">{mediator.experience}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })
          ) : (
            <div className="text-center py-8 text-gray-500">
              <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-300" />
              <p className="mt-2 text-sm">暂无可用的中间人</p>
              <p className="text-xs">请稍后再试或选择普通交易</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
