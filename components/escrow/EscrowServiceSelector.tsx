'use client'

import React, { useState, useEffect } from 'react'
import { ShieldCheckIcon, UserGroupIcon, CurrencyDollarIcon, ClockIcon } from '@heroicons/react/24/outline'
import { CheckCircleIcon } from '@heroicons/react/24/solid'

interface Mediator {
  id: string
  name: string
  avatar?: string
  mediatorFeeRate: number
  mediatorReputation: number
  stats: {
    totalOrders: number
    successRate: number
    availableDeposit: number
  }
}

interface EscrowServiceSelectorProps {
  productPrice: number
  quantity: number
  onSelect: (mediator: Mediator | null) => void
  className?: string
}

export default function EscrowServiceSelector({
  productPrice,
  quantity,
  onSelect,
  className = ''
}: EscrowServiceSelectorProps) {
  const [useEscrow, setUseEscrow] = useState(false)
  const [selectedMediator, setSelectedMediator] = useState<Mediator | null>(null)
  const [mediators, setMediators] = useState<Mediator[]>([])
  const [loading, setLoading] = useState(false)

  const totalAmount = productPrice * quantity
  const isEligible = totalAmount >= 100 // 最低100 USDT

  // 获取可用中间人列表
  useEffect(() => {
    if (useEscrow && isEligible) {
      fetchMediators()
    }
  }, [useEscrow, isEligible, totalAmount])

  const fetchMediators = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/mediator/list?minAmount=${totalAmount}`)
      const data = await response.json()
      
      if (data.success) {
        setMediators(data.data.mediators)
      }
    } catch (error) {
      console.error('获取中间人列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEscrowToggle = (enabled: boolean) => {
    setUseEscrow(enabled)
    if (!enabled) {
      setSelectedMediator(null)
      onSelect(null)
    }
  }

  const handleMediatorSelect = (mediator: Mediator) => {
    setSelectedMediator(mediator)
    onSelect(mediator)
  }

  const calculateEscrowFee = (feeRate: number) => {
    return totalAmount * feeRate
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <ShieldCheckIcon className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">托管服务</h3>
        </div>
        
        {isEligible && (
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={useEscrow}
              onChange={(e) => handleEscrowToggle(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">使用托管服务</span>
          </label>
        )}
      </div>

      {!isEligible && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                订单金额不足
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>托管服务要求订单金额至少为 100 USDT，当前订单金额为 {totalAmount.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {isEligible && !useEscrow && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                推荐使用托管服务
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>对于大金额交易，托管服务可以为您提供额外的安全保障：</p>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>资金安全托管，交易完成后释放</li>
                  <li>专业中间人监督交易过程</li>
                  <li>争议处理和仲裁机制</li>
                  <li>多方聊天室实时沟通</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {useEscrow && isEligible && (
        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">选择中间人</h4>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">正在加载中间人列表...</p>
            </div>
          ) : mediators.length === 0 ? (
            <div className="text-center py-8">
              <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto" />
              <p className="mt-2 text-sm text-gray-500">暂无可用的中间人</p>
            </div>
          ) : (
            <div className="space-y-3">
              {mediators.map((mediator) => {
                const escrowFee = calculateEscrowFee(mediator.mediatorFeeRate)
                const isSelected = selectedMediator?.id === mediator.id
                
                return (
                  <div
                    key={mediator.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleMediatorSelect(mediator)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          {mediator.avatar ? (
                            <img
                              src={mediator.avatar}
                              alt={mediator.name}
                              className="h-10 w-10 rounded-full"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <UserGroupIcon className="h-6 w-6 text-gray-600" />
                            </div>
                          )}
                          {isSelected && (
                            <CheckCircleIcon className="absolute -top-1 -right-1 h-5 w-5 text-blue-600" />
                          )}
                        </div>
                        
                        <div>
                          <h5 className="font-medium text-gray-900">{mediator.name}</h5>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>信誉: {mediator.mediatorReputation.toFixed(1)}</span>
                            <span>成功率: {mediator.stats.successRate.toFixed(1)}%</span>
                            <span>订单: {mediator.stats.totalOrders}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          {escrowFee.toFixed(2)} USDT
                        </div>
                        <div className="text-sm text-gray-500">
                          费率: {(mediator.mediatorFeeRate * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      )}

      {selectedMediator && (
        <div className="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                托管服务已选择
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <p>中间人: {selectedMediator.name}</p>
                <p>服务费: {calculateEscrowFee(selectedMediator.mediatorFeeRate).toFixed(2)} USDT</p>
                <p>总支付: {(totalAmount + calculateEscrowFee(selectedMediator.mediatorFeeRate)).toFixed(2)} USDT</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {useEscrow && (
        <div className="mt-4 text-xs text-gray-500">
          <p>* 使用托管服务后，您的资金将由中间人安全托管，确认收货后释放给卖家</p>
          <p>* 如有争议，可申请仲裁，由中间人委员会投票决定</p>
        </div>
      )}
    </div>
  )
}
