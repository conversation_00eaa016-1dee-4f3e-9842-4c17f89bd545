'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  ShieldCheckIcon, 
  UserGroupIcon, 
  ChartBarIcon, 
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowRightIcon,
  StarIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface MediatorPanelProps {
  profile: {
    isMediator: boolean
    mediatorStatus: string
    mediatorFeeRate: number | null
    depositBalance: number // 使用统一的保证金字段
    mediatorReputation: number
    mediatorVerifiedAt: string | null
    bnbWalletVerified: boolean
    mediatorExperience: string | null
    mediatorIntroduction: string | null
    mediatorSuccessRate: number
    mediatorTotalOrders: number
  }
}

interface MediatorStats {
  activeOrders: number
  completedOrders: number
  totalOrders: number
  totalEarnings: number
  pendingDisputes: number
  totalDeposit: number
  usedDeposit: number
  availableDeposit: number
  totalVolume: number
  successRate: number
  monthly: {
    orders: number
    earnings: number
    disputes: number
  }
  arbitration: {
    totalVotes: number
    monthlyVotes: number
    rewardVouchers: number
  }
  recentOrders: Array<{
    id: string
    orderNumber: string
    amount: number
    status: string
    buyer: string
    seller: string
    createdAt: string
  }>
}

export default function MediatorPanel({ profile }: MediatorPanelProps) {
  const [stats, setStats] = useState<MediatorStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (profile.isMediator && profile.mediatorStatus === 'ACTIVE') {
      fetchMediatorStats()
    }
  }, [profile.isMediator, profile.mediatorStatus])

  const fetchMediatorStats = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/mediator/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data.data)
      }
    } catch (error) {
      console.error('获取中间人统计失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      INACTIVE: { 
        icon: XCircleIcon, 
        text: '未激活', 
        color: 'bg-gray-100 text-gray-800' 
      },
      PENDING: { 
        icon: ClockIcon, 
        text: '审核中', 
        color: 'bg-yellow-100 text-yellow-800' 
      },
      ACTIVE: { 
        icon: CheckCircleIcon, 
        text: '已激活', 
        color: 'bg-green-100 text-green-800' 
      },
      SUSPENDED: { 
        icon: ExclamationTriangleIcon, 
        text: '已暂停', 
        color: 'bg-red-100 text-red-800' 
      }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.INACTIVE
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const quickActions = [
    {
      title: '托管订单管理',
      description: '查看和管理您的托管订单',
      href: '/escrow/orders?role=mediator',
      icon: UserGroupIcon,
      color: 'bg-blue-600 hover:bg-blue-700',
      badge: stats?.activeOrders || 0
    },
    {
      title: '仲裁投票',
      description: '参与争议案例的仲裁投票',
      href: '/mediator/arbitration',
      icon: ChartBarIcon,
      color: 'bg-green-600 hover:bg-green-700',
      badge: stats?.pendingDisputes || 0
    },
    {
      title: '中间人控制台',
      description: '完整的中间人管理面板',
      href: '/mediator/dashboard',
      icon: CogIcon,
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: '奖励券管理',
      description: '查看和使用您的奖励券',
      href: '/rewards/coupons',
      icon: StarIcon,
      color: 'bg-yellow-600 hover:bg-yellow-700',
      badge: stats?.arbitration.rewardVouchers || 0
    }
  ]

  // 如果用户不是中间人，显示申请按钮
  if (!profile.isMediator) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
              <ShieldCheckIcon className="h-6 w-6 text-blue-600 mr-2" />
              中间人服务
            </h3>
          </div>
          
          <div className="text-center py-8">
            <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">成为认证中间人</h4>
            <p className="text-gray-600 mb-6">
              为用户提供安全的托管服务，获得稳定收益
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 text-sm">
              <div className="bg-blue-50 p-3 rounded-lg">
                <CurrencyDollarIcon className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <p className="font-medium text-blue-900">稳定收益</p>
                <p className="text-blue-700">1%-30% 服务费率</p>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <StarIcon className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <p className="font-medium text-green-900">信誉积累</p>
                <p className="text-green-700">建立专业声誉</p>
              </div>
              <div className="bg-purple-50 p-3 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <p className="font-medium text-purple-900">服务用户</p>
                <p className="text-purple-700">提供安全保障</p>
              </div>
            </div>
            
            <Link
              href="/mediator/apply"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              申请成为中间人
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
            <ShieldCheckIcon className="h-6 w-6 text-blue-600 mr-2" />
            中间人面板
          </h3>
          {getStatusBadge(profile.mediatorStatus)}
        </div>

        {/* 中间人基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">信誉评分</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {profile.mediatorReputation.toFixed(1)}
                </p>
              </div>
              <StarIcon className="h-8 w-8 text-yellow-500" />
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">服务费率</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {profile.mediatorFeeRate ? `${(profile.mediatorFeeRate * 100).toFixed(1)}%` : '未设置'}
                </p>
              </div>
              <CurrencyDollarIcon className="h-8 w-8 text-green-500" />
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">保证金</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {profile.depositBalance.toLocaleString()} USDT
                </p>
              </div>
              <ShieldCheckIcon className="h-8 w-8 text-blue-500" />
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        {stats && (
          <div className="space-y-6 mb-6">
            {/* 主要统计 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center bg-blue-50 p-3 rounded-lg">
                <p className="text-2xl font-semibold text-blue-600">{stats.activeOrders}</p>
                <p className="text-sm text-blue-700">活跃订单</p>
              </div>
              <div className="text-center bg-green-50 p-3 rounded-lg">
                <p className="text-2xl font-semibold text-green-600">{stats.completedOrders}</p>
                <p className="text-sm text-green-700">已完成</p>
              </div>
              <div className="text-center bg-purple-50 p-3 rounded-lg">
                <p className="text-2xl font-semibold text-purple-600">{stats.totalEarnings.toFixed(2)}</p>
                <p className="text-sm text-purple-700">总收益 (USDT)</p>
              </div>
              <div className="text-center bg-orange-50 p-3 rounded-lg">
                <p className="text-2xl font-semibold text-orange-600">{stats.pendingDisputes}</p>
                <p className="text-sm text-orange-700">待处理争议</p>
              </div>
            </div>

            {/* 保证金状态 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-gray-700 mb-3">保证金状态</h5>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-gray-900">{stats.totalDeposit.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">总保证金</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-red-600">{stats.usedDeposit.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">已占用</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-green-600">{stats.availableDeposit.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">可用余额</p>
                </div>
              </div>
              <div className="mt-3">
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((stats.usedDeposit / stats.totalDeposit) * 100, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  保证金使用率: {((stats.usedDeposit / stats.totalDeposit) * 100).toFixed(1)}%
                </p>
              </div>
            </div>

            {/* 本月统计 */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-gray-700 mb-3">本月表现</h5>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-blue-600">{stats.monthly.orders}</p>
                  <p className="text-xs text-gray-600">新订单</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-green-600">{stats.monthly.earnings.toFixed(2)}</p>
                  <p className="text-xs text-gray-600">收益 (USDT)</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-orange-600">{stats.monthly.disputes}</p>
                  <p className="text-xs text-gray-600">争议处理</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 快捷操作 */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-4">快捷操作</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon
              return (
                <Link
                  key={index}
                  href={action.href}
                  className={`${action.color} text-white p-4 rounded-lg transition-colors text-center group relative`}
                >
                  {action.badge !== undefined && action.badge > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold">
                      {action.badge > 99 ? '99+' : action.badge}
                    </span>
                  )}
                  <Icon className="h-6 w-6 mx-auto mb-2 group-hover:scale-110 transition-transform" />
                  <p className="font-medium text-sm">{action.title}</p>
                  <p className="text-xs opacity-90 mt-1">{action.description}</p>
                </Link>
              )
            })}
          </div>
        </div>

        {/* 最近订单 */}
        {stats && stats.recentOrders.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">最近订单</h4>
            <div className="bg-gray-50 rounded-lg overflow-hidden">
              <div className="divide-y divide-gray-200">
                {stats.recentOrders.map((order) => (
                  <div key={order.id} className="p-4 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">
                            #{order.orderNumber}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            order.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            order.status === 'SHIPPED' ? 'bg-blue-100 text-blue-800' :
                            order.status === 'FUNDED' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {order.status === 'COMPLETED' ? '已完成' :
                             order.status === 'SHIPPED' ? '已发货' :
                             order.status === 'FUNDED' ? '已托管' :
                             order.status === 'PENDING' ? '待处理' : order.status}
                          </span>
                        </div>
                        <div className="mt-1 text-sm text-gray-600">
                          买家: {order.buyer} → 卖家: {order.seller}
                        </div>
                        <div className="mt-1 text-xs text-gray-500">
                          {new Date(order.createdAt).toLocaleDateString('zh-CN')}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {order.amount.toLocaleString()} USDT
                        </div>
                        <Link
                          href={`/escrow/orders/${order.id}`}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          查看详情
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="p-3 bg-gray-100 text-center">
                <Link
                  href="/escrow/orders?role=mediator"
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  查看所有订单 →
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* 状态提示 */}
        {profile.mediatorStatus === 'PENDING' && (
          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <ClockIcon className="h-5 w-5 text-yellow-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  申请审核中
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>您的中间人申请正在审核中，通常需要1-3个工作日。审核通过后您将收到通知。</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {profile.mediatorStatus === 'SUSPENDED' && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  账户已暂停
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>您的中间人账户已被暂停，请联系客服了解详情。</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
