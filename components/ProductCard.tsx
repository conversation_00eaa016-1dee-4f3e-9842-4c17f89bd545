'use client'

import Link from 'next/link'
import FavoriteButton from './FavoriteButton'
import { formatUSDT, formatDeposit } from '@/lib/utils'

interface Product {
  id: string
  title: string
  description: string
  price: number
  images: string
  category: string
  condition: string
  city: string
  district: string
  status: string
  createdAt: string
  seller: {
    name: string | null
    userId: string
    creditScore: number
    depositBalance: number
  }
}

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  const getProductImage = (images: string) => {
    if (!images || images.trim() === '') return undefined
    const imageList = images.split(',')
    return imageList[0] || undefined
  }

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      'electronics': '电子产品',
      'clothing': '服装配饰',
      'books': '图书文具',
      'home': '家居用品',
      'sports': '运动户外',
      'beauty': '美妆护肤',
      'food': '食品饮料',
      'toys': '玩具游戏',
      'automotive': '汽车用品',
      'other': '其他'
    }
    return categoryMap[category] || category
  }

  const getConditionText = (condition: string) => {
    const conditionMap: Record<string, string> = {
      'new': '全新',
      'like_new': '几乎全新',
      'good': '良好',
      'fair': '一般',
      'poor': '较差'
    }
    return conditionMap[condition] || condition
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800'
      case 'SOLD':
        return 'bg-gray-100 text-gray-800'
      case 'INACTIVE':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return '在售'
      case 'SOLD':
        return '已售出'
      case 'INACTIVE':
        return '下架'
      default:
        return status
    }
  }

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 relative">
      {/* 收藏按钮 */}
      <div className="absolute top-3 right-3 z-10">
        <div className="bg-white rounded-full p-1 shadow-sm">
          <FavoriteButton productId={product.id} size="sm" />
        </div>
      </div>

      <Link href={`/products/${product.id}`}>
        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
          {getProductImage(product.images) ? (
            <img
              src={getProductImage(product.images)}
              alt={product.title}
              className="h-48 w-full object-cover object-center hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="h-48 w-full flex items-center justify-center bg-gray-100">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-2">📦</div>
                <div className="text-sm">暂无图片</div>
              </div>
            </div>
          )}
        </div>
      </Link>

      <div className="p-4">
        {/* 商品标题 */}
        <Link href={`/products/${product.id}`}>
          <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors mb-2 overflow-hidden" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {product.title}
          </h3>
        </Link>

        {/* 价格 */}
        <div className="text-xl font-bold text-blue-600 mb-2">
          {formatUSDT(product.price)}
        </div>

        {/* 商品信息 */}
        <div className="space-y-1 text-sm text-gray-600 mb-3">
          <div className="flex justify-between">
            <span>分类:</span>
            <span>{getCategoryText(product.category)}</span>
          </div>
          <div className="flex justify-between">
            <span>成色:</span>
            <span>{getConditionText(product.condition)}</span>
          </div>
          <div className="flex justify-between">
            <span>信誉:</span>
            <span>{product.seller.creditScore}分</span>
          </div>
          <div className="flex justify-between">
            <span>保证金:</span>
            <span>{formatDeposit(product.seller.depositBalance)}</span>
          </div>
          <div className="flex justify-between">
            <span>库存:</span>
            <span>{product.stock}件</span>
          </div>
          <div className="flex justify-between">
            <span>地区:</span>
            <span>{product.city} {product.district}</span>
          </div>
        </div>

        {/* 状态和卖家 */}
        <div className="flex items-center justify-between">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
            {getStatusText(product.status)}
          </span>
          <Link 
            href={`/products/user/${product.seller.userId}`}
            className="text-sm text-gray-500 hover:text-blue-600 transition-colors"
          >
            {product.seller.name || '匿名用户'}
          </Link>
        </div>

        {/* 发布时间 */}
        <div className="mt-2 text-xs text-gray-400">
          {new Date(product.createdAt).toLocaleDateString()}
        </div>
      </div>
    </div>
  )
}
