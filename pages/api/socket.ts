import type { NextApiRequest, NextApiResponse } from 'next'
import { Server as HTTPServer } from 'http'
import { Socket as NetSocket } from 'net'
import { initializeSocket } from '@/lib/socket'

interface SocketServer extends HTTPServer {
  io?: any
}

interface SocketWithIO extends NetSocket {
  server: SocketServer
}

interface NextApiResponseWithSocket extends NextApiResponse {
  socket: Socket<PERSON>ithIO
}

export default function handler(
  req: NextApiRequest,
  res: NextApiResponseWithSocket
) {
  if (res.socket.server.io) {
    console.log('Socket.IO server already running')
    res.end()
    return
  }

  console.log('Initializing Socket.IO server...')
  
  const io = initializeSocket(res.socket.server)
  res.socket.server.io = io

  res.end()
}
