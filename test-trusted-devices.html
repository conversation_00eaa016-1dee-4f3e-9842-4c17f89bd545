<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试删除信任设备功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .device-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .delete-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>删除信任设备功能测试</h1>
    
    <div id="status"></div>
    
    <h2>信任设备列表</h2>
    <div id="devices"></div>
    
    <h2>测试删除功能</h2>
    <button onclick="testDeleteDevice()">测试删除设备 (IP: ::1)</button>
    
    <script>
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${isError ? 'error' : 'success'}">${message}</div>`;
        }
        
        async function loadTrustedDevices() {
            try {
                const response = await fetch('/api/user/security/trusted-devices');
                if (response.ok) {
                    const data = await response.json();
                    displayDevices(data.trustedDevices);
                } else {
                    showStatus('获取信任设备列表失败', true);
                }
            } catch (error) {
                showStatus('网络错误：' + error.message, true);
            }
        }
        
        function displayDevices(devices) {
            const devicesDiv = document.getElementById('devices');
            if (devices.length === 0) {
                devicesDiv.innerHTML = '<p>暂无信任设备</p>';
                return;
            }
            
            devicesDiv.innerHTML = devices.map(device => `
                <div class="device-item">
                    <h3>${device.deviceName}</h3>
                    <p><strong>IP地址:</strong> ${device.ipAddress}</p>
                    <p><strong>最后登录:</strong> ${new Date(device.lastLogin).toLocaleString()}</p>
                    <p><strong>登录次数:</strong> ${device.loginCount}</p>
                    <p><strong>活跃会话:</strong> ${device.hasActiveSession ? '是' : '否'}</p>
                    <button class="delete-btn" onclick="deleteDevice('${device.ipAddress}', '${device.deviceName}')">
                        删除此设备
                    </button>
                </div>
            `).join('');
        }
        
        async function deleteDevice(ipAddress, deviceName) {
            if (!confirm(`确定要删除信任设备"${deviceName}"吗？\n\n删除后，该设备上的所有登录会话将被终止，用户需要重新登录。`)) {
                return;
            }
            
            try {
                const response = await fetch('/api/user/security/trusted-devices', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ipAddress: ipAddress,
                        deviceInfo: deviceName,
                        logId: 'test-' + Date.now()
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus(`成功删除信任设备！已终止 ${data.deletedSessions} 个相关会话。`);
                    loadTrustedDevices(); // 重新加载列表
                } else {
                    const error = await response.json();
                    showStatus(`删除失败：${error.error}`, true);
                }
            } catch (error) {
                showStatus('网络错误：' + error.message, true);
            }
        }
        
        async function testDeleteDevice() {
            await deleteDevice('::1', 'Chrome 138 - Windows 10 (桌面设备)');
        }
        
        // 页面加载时获取设备列表
        window.onload = function() {
            loadTrustedDevices();
        };
    </script>
</body>
</html>
