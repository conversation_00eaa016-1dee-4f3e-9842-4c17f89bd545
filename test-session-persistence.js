// 测试登录状态持久性
// 在浏览器控制台运行此脚本

console.log('🧪 测试登录状态持久性...')

// 检查当前会话
fetch('/api/auth/session')
  .then(res => res.json())
  .then(session => {
    console.log('当前会话:', session)
    
    if (session?.user) {
      console.log('✅ 用户已登录:', session.user.email)
      
      // 检查 cookie
      const cookies = document.cookie.split(';')
      const authCookies = cookies.filter(cookie => 
        cookie.includes('next-auth') || cookie.includes('session')
      )
      
      console.log('认证相关 cookies:', authCookies)
      
      if (authCookies.length > 0) {
        console.log('✅ 找到认证 cookies')
      } else {
        console.log('❌ 未找到认证 cookies')
      }
    } else {
      console.log('❌ 用户未登录')
    }
  })
  .catch(err => {
    console.error('❌ 会话检查失败:', err)
  })
