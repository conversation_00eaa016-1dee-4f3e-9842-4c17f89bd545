# 🤝 贡献指南 | Contributing Guide

欢迎为 BitMarket 项目做出贡献！本指南将帮助您了解如何参与项目开发。

## 📋 目录 | Table of Contents

- [🚀 快速开始](#-快速开始-quick-start)
- [🔧 开发环境设置](#-开发环境设置-development-setup)
- [📝 代码规范](#-代码规范-coding-standards)
- [🧪 测试指南](#-测试指南-testing-guide)
- [📦 提交规范](#-提交规范-commit-convention)
- [🔄 Pull Request 流程](#-pull-request-流程-pr-process)
- [🐛 问题报告](#-问题报告-bug-reports)
- [💡 功能建议](#-功能建议-feature-requests)

## 🚀 快速开始 | Quick Start

### 1. Fork 和克隆项目
```bash
# Fork 项目到您的 GitHub 账户
# 然后克隆您的 fork
git clone https://github.com/YOUR_USERNAME/bitmarket.git
cd bitmarket

# 添加上游仓库
git remote add upstream https://github.com/liusu-ally/bitmarket.git
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
```bash
cp .env.example .env.local
# 编辑 .env.local 文件，配置必要的环境变量
```

### 4. 启动开发服务器
```bash
npm run dev
```

## 🔧 开发环境设置 | Development Setup

### 系统要求
- Node.js >= 18.0.0
- npm >= 8.0.0
- Git >= 2.0.0

### 推荐工具
- **编辑器**: VS Code
- **扩展**: 
  - TypeScript and JavaScript Language Features
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense
  - Prisma

### 数据库设置
```bash
# 生成 Prisma Client
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# (可选) 填充测试数据
npx prisma db seed
```

## 📝 代码规范 | Coding Standards

### TypeScript 规范
- 使用 TypeScript 严格模式
- 为所有函数和变量提供类型注解
- 避免使用 `any` 类型
- 使用接口定义对象结构

```typescript
// ✅ 好的示例
interface User {
  id: string
  name: string
  email: string
}

const createUser = (userData: Omit<User, 'id'>): User => {
  return {
    id: generateId(),
    ...userData
  }
}

// ❌ 避免的写法
const createUser = (userData: any) => {
  // ...
}
```

### React 组件规范
- 使用函数组件和 Hooks
- 组件名使用 PascalCase
- 文件名与组件名保持一致
- 使用 TypeScript 接口定义 Props

```typescript
// ✅ 好的示例
interface UserCardProps {
  user: User
  onEdit: (user: User) => void
}

export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  return (
    <div className="p-4 border rounded">
      <h3>{user.name}</h3>
      <button onClick={() => onEdit(user)}>编辑</button>
    </div>
  )
}
```

### CSS 和样式规范
- 使用 Tailwind CSS 类名
- 避免内联样式
- 使用语义化的类名组合
- 响应式设计优先

```tsx
// ✅ 好的示例
<div className="flex flex-col space-y-4 p-6 bg-white rounded-lg shadow-md md:flex-row md:space-y-0 md:space-x-6">
  <div className="flex-1">
    <h2 className="text-xl font-semibold text-gray-900">标题</h2>
  </div>
</div>
```

### API 路由规范
- 使用 RESTful API 设计
- 统一的错误处理
- 输入验证和类型检查
- 适当的 HTTP 状态码

```typescript
// ✅ 好的示例
export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // 输入验证
    const validatedData = userSchema.parse(body)
    
    // 业务逻辑
    const user = await createUser(validatedData)
    
    return NextResponse.json(user, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入数据无效', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
```

## 🧪 测试指南 | Testing Guide

### 测试类型
- **单元测试**: 测试单个函数或组件
- **集成测试**: 测试组件间的交互
- **端到端测试**: 测试完整的用户流程

### 运行测试
```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test -- UserCard.test.tsx

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 测试示例
```typescript
// components/__tests__/UserCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { UserCard } from '../UserCard'

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>'
}

describe('UserCard', () => {
  it('应该显示用户信息', () => {
    const onEdit = jest.fn()
    
    render(<UserCard user={mockUser} onEdit={onEdit} />)
    
    expect(screen.getByText('张三')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })
  
  it('点击编辑按钮应该调用 onEdit', () => {
    const onEdit = jest.fn()
    
    render(<UserCard user={mockUser} onEdit={onEdit} />)
    
    fireEvent.click(screen.getByText('编辑'))
    
    expect(onEdit).toHaveBeenCalledWith(mockUser)
  })
})
```

## 📦 提交规范 | Commit Convention

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

### 提交格式
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建/工具相关
- `perf`: 性能优化
- `ci`: CI/CD 相关

### 提交示例
```bash
# 新功能
git commit -m "feat(auth): add user registration with email verification"

# 修复bug
git commit -m "fix(api): resolve user profile update error"

# 文档更新
git commit -m "docs: update installation guide for Windows users"

# 重构
git commit -m "refactor(components): extract common button component"
```

## 🔄 Pull Request 流程 | PR Process

### 1. 创建功能分支
```bash
# 确保主分支是最新的
git checkout main
git pull upstream main

# 创建新的功能分支
git checkout -b feature/user-profile-enhancement
```

### 2. 开发和测试
- 编写代码
- 添加或更新测试
- 确保所有测试通过
- 更新相关文档

### 3. 提交更改
```bash
git add .
git commit -m "feat(profile): add user avatar upload functionality"
```

### 4. 推送分支
```bash
git push origin feature/user-profile-enhancement
```

### 5. 创建 Pull Request
- 在 GitHub 上创建 PR
- 填写详细的 PR 描述
- 链接相关的 Issue
- 请求代码审查

### PR 描述模板
```markdown
## 📋 变更描述 | Change Description
简要描述这个 PR 的目的和实现的功能。

## 🔧 变更类型 | Type of Change
- [ ] 新功能 (feature)
- [ ] 修复bug (bugfix)
- [ ] 文档更新 (documentation)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (performance)

## 🧪 测试 | Testing
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成

## 📝 检查清单 | Checklist
- [ ] 代码遵循项目规范
- [ ] 已添加必要的测试
- [ ] 文档已更新
- [ ] 无 TypeScript 错误
- [ ] 无 ESLint 警告

## 📸 截图 | Screenshots
如果有UI变更，请提供截图。

## 🔗 相关Issue | Related Issues
Closes #123
```

## 🐛 问题报告 | Bug Reports

### 报告模板
使用 GitHub Issues 报告问题时，请包含以下信息：

1. **问题描述**: 清楚地描述遇到的问题
2. **复现步骤**: 详细的步骤说明
3. **预期行为**: 您期望发生什么
4. **实际行为**: 实际发生了什么
5. **环境信息**: 
   - 操作系统
   - 浏览器版本
   - Node.js 版本
   - 项目版本
6. **错误日志**: 相关的错误信息或截图

## 💡 功能建议 | Feature Requests

### 建议模板
1. **功能描述**: 详细描述建议的功能
2. **使用场景**: 说明什么情况下会用到这个功能
3. **预期效果**: 这个功能应该如何工作
4. **替代方案**: 是否有其他解决方案
5. **优先级**: 这个功能的重要程度

## 🎯 开发最佳实践 | Development Best Practices

### 代码审查
- 仔细审查每个 PR
- 提供建设性的反馈
- 关注代码质量、性能和安全性
- 确保代码符合项目规范

### 性能考虑
- 避免不必要的重新渲染
- 使用适当的缓存策略
- 优化数据库查询
- 压缩和优化静态资源

### 安全性
- 验证所有用户输入
- 使用参数化查询防止 SQL 注入
- 实施适当的身份验证和授权
- 保护敏感信息

## 📞 获取帮助 | Getting Help

如果您在贡献过程中遇到问题，可以通过以下方式获取帮助：

- 📧 发送邮件到 <EMAIL>
- 💬 在 GitHub Discussions 中提问
- 🐛 在 GitHub Issues 中报告问题

---

感谢您对 BitMarket 项目的贡献！🙏

Thank you for contributing to the BitMarket project! 🙏
