-- ============================================================================
-- BitMarket 数据库初始化脚本
-- MySQL Database Initialization Script for BitMarket
-- ============================================================================

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS bitmarket
  CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE bitmarket;

-- 创建用户并授权 (如果不存在)
-- 注意：在 Docker 环境中，用户已通过环境变量创建
-- 这里只是确保权限正确

-- 授予用户完整权限
GRANT ALL PRIVILEGES ON bitmarket.* TO 'bitmarket_user'@'%';
GRANT ALL PRIVILEGES ON bitmarket.* TO 'bitmarket_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 创建性能监控表 (可选)
CREATE TABLE IF NOT EXISTS `_performance_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
  `query_type` varchar(50) DEFAULT NULL,
  `execution_time` decimal(10,4) DEFAULT NULL,
  `affected_rows` int DEFAULT NULL,
  `query_hash` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_query_type` (`query_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 显示创建结果
SELECT 'BitMarket 数据库初始化完成 | Database initialization completed' AS message;
SELECT 'Database created successfully' AS status, DATABASE() AS current_database;

-- 显示数据库信息
SHOW DATABASES;
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 显示用户权限
SHOW GRANTS FOR 'bitmarket_user'@'%';
