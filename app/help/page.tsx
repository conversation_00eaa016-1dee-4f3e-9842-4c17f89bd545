'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import {
  MagnifyingGlassIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  ChartBarIcon,
  UserCircleIcon,
  ChevronUpIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline'
import FAQ, { FAQCategories } from '@/components/help/FAQ'

interface HelpArticle {
  id: string
  title: string
  category: string
  tags: string[]
  content: string
  keywords: string[]
}

interface SearchResult {
  results: HelpArticle[]
  total: number
  query: string
  category: string
}

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null)
  const [isSearching, setIsSearching] = useState(false)
  const [showBackToTop, setShowBackToTop] = useState(false)
  const [faqCategory, setFaqCategory] = useState('all')

  // 分类定义
  const categories = [
    { id: 'all', name: '全部', icon: QuestionMarkCircleIcon },
    { id: 'payment', name: '支付相关', icon: CreditCardIcon },
    { id: 'trading', name: '交易流程', icon: ChartBarIcon },
    { id: 'security', name: '安全提示', icon: ShieldCheckIcon },
    { id: 'account', name: '账户设置', icon: UserCircleIcon }
  ]

  // 监听滚动，显示返回顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // 搜索功能
  const handleSearch = async () => {
    if (!searchQuery.trim() && selectedCategory === 'all') return

    setIsSearching(true)
    try {
      const params = new URLSearchParams()
      if (searchQuery.trim()) params.append('q', searchQuery.trim())
      if (selectedCategory !== 'all') params.append('category', selectedCategory)

      const response = await fetch(`/api/help/search?${params}`)
      if (response.ok) {
        const data = await response.json()
        setSearchResults(data)
      }
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setIsSearching(false)
    }
  }

  // 回车搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // 返回顶部
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* 面包屑导航 */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-gray-700">
                  首页
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">帮助中心</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            比特市场帮助中心
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            欢迎来到比特市场帮助中心！这里为您提供详细的USDT交易指南和常见问题解答。
          </p>
        </div>

        {/* 搜索区域 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索帮助内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="md:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full py-3 px-4 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <button
              onClick={handleSearch}
              disabled={isSearching}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
            >
              {isSearching ? '搜索中...' : '搜索'}
            </button>
          </div>
        </div>

        {/* 搜索结果 */}
        {searchResults && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              搜索结果 ({searchResults.total} 条)
            </h2>
            {searchResults.results.length > 0 ? (
              <div className="space-y-4">
                {searchResults.results.map(article => (
                  <div key={article.id} className="border-l-4 border-blue-500 pl-4">
                    <h3 className="font-medium text-gray-900 mb-2">{article.title}</h3>
                    <p className="text-gray-600 mb-2">{article.content}</p>
                    <div className="flex flex-wrap gap-2">
                      {article.tags.map(tag => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">没有找到相关内容，请尝试其他关键词。</p>
            )}
          </div>
        )}

        {/* 分类导航 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {categories.slice(1).map(category => {
            const IconComponent = category.icon
            return (
              <button
                key={category.id}
                onClick={() => {
                  setSelectedCategory(category.id)
                  handleSearch()
                }}
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
              >
                <IconComponent className="h-8 w-8 text-blue-600 mx-auto mb-3 group-hover:text-blue-700" />
                <h3 className="font-medium text-gray-900 group-hover:text-blue-700">
                  {category.name}
                </h3>
              </button>
            )
          })}
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧内容 */}
          <div className="lg:col-span-2 space-y-8">

            {/* 币安哈希值获取教程 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-blue-50 px-6 py-4 border-b">
                <div className="flex items-center">
                  <CreditCardIcon className="h-6 w-6 text-blue-600 mr-3" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    📌 如何获取币安交易哈希值（TxID）
                  </h2>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  为了确保转账成功并完成托管验证，我们需要您提供币安转账的哈希值（TxID）
                </p>
              </div>

              <div className="p-6">
                {/* 步骤1 */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      1
                    </div>
                    <h3 className="ml-3 text-lg font-medium text-gray-900">
                      📱 打开币安App
                    </h3>
                  </div>
                  <div className="ml-11 space-y-3">
                    <p className="text-gray-700">• 打开币安App，点击右下角【资产】</p>
                    <p className="text-gray-700">• 点击页面上方【现货】按钮</p>
                    <p className="text-gray-700">• 点击右上角的【历史】图标</p>
                    <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                      <p className="text-center text-gray-500 text-sm">
                        📸 图示：进入历史记录位置
                      </p>
                      <p className="text-center text-xs text-gray-400 mt-1">
                        [此处需要币安App截图：资产→现货→历史的导航路径]
                      </p>
                    </div>
                  </div>
                </div>

                {/* 步骤2 */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      2
                    </div>
                    <h3 className="ml-3 text-lg font-medium text-gray-900">
                      📂 查找提现记录
                    </h3>
                  </div>
                  <div className="ml-11 space-y-3">
                    <p className="text-gray-700">• 在历史记录页面，切换到【提现】标签</p>
                    <p className="text-gray-700">• 找到您刚刚转账的记录（注意时间和币种）</p>
                    <p className="text-gray-700">• 点击该记录，进入详情页</p>
                    <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                      <p className="text-center text-gray-500 text-sm">
                        📸 图示：提现记录页面
                      </p>
                      <p className="text-center text-xs text-gray-400 mt-1">
                        [此处需要币安App截图：提现记录列表和详情入口]
                      </p>
                    </div>
                  </div>
                </div>

                {/* 步骤3 */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      3
                    </div>
                    <h3 className="ml-3 text-lg font-medium text-gray-900">
                      🔗 复制交易哈希
                    </h3>
                  </div>
                  <div className="ml-11 space-y-3">
                    <p className="text-gray-700">• 在提现详情中，找到一项叫【交易哈希】或【TxID】</p>
                    <p className="text-gray-700">• 点击右侧"复制"图标，完成复制</p>
                    <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                      <p className="text-center text-gray-500 text-sm">
                        📸 图示：交易哈希复制示意
                      </p>
                      <p className="text-center text-xs text-gray-400 mt-1">
                        [此处需要币安App截图：交易哈希字段和复制按钮]
                      </p>
                    </div>
                  </div>
                </div>

                {/* 可选验证 */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">🔍 可选验证（了解更多）</h4>
                  <p className="text-sm text-gray-700 mb-3">
                    您可以将交易哈希粘贴到 BSCscan.com 或其他区块链浏览器中，查询以下信息：
                  </p>
                  <ul className="text-sm text-gray-700 space-y-1 ml-4">
                    <li>• 转出地址</li>
                    <li>• 收款地址</li>
                    <li>• 转账金额</li>
                  </ul>
                  <p className="text-sm text-gray-600 mt-3">
                    这样可验证交易是否成功到达平台指定地址。
                  </p>
                </div>
              </div>
            </div>

            {/* USDT支付方式介绍 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-green-50 px-6 py-4 border-b">
                <div className="flex items-center">
                  <CreditCardIcon className="h-6 w-6 text-green-600 mr-3" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    💰 USDT支付方式介绍
                  </h2>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Binance Pay */}
                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Binance Pay</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      币安官方支付服务，支持扫码支付
                    </p>
                    <ul className="text-xs text-gray-500 space-y-1">
                      <li>✅ 操作简单</li>
                      <li>✅ 到账快速</li>
                      <li>✅ 手续费低</li>
                    </ul>
                  </div>

                  {/* BNB Chain */}
                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">BNB Chain</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      直接通过区块链网络转账
                    </p>
                    <ul className="text-xs text-gray-500 space-y-1">
                      <li>✅ 去中心化</li>
                      <li>✅ 透明可查</li>
                      <li>⚠️ 需要钱包</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 平台功能介绍 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-indigo-50 px-6 py-4 border-b">
                <div className="flex items-center">
                  <UserCircleIcon className="h-6 w-6 text-indigo-600 mr-3" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    🎯 平台核心功能
                  </h2>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  了解比特市场的主要功能模块，快速上手使用
                </p>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 交易功能 */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-900 text-lg mb-3">📦 交易功能</h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <span className="text-orange-500 mr-2">🟠</span>
                        <div>
                          <h4 className="font-medium text-gray-800">发布商品</h4>
                          <p className="text-sm text-gray-600">出售您的闲置物品，设置价格和描述</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <span className="text-yellow-500 mr-2">🟡</span>
                        <div>
                          <h4 className="font-medium text-gray-800">发布需求</h4>
                          <p className="text-sm text-gray-600">发布求购信息，等待卖家响应</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <span className="text-blue-500 mr-2">🔵</span>
                        <div>
                          <h4 className="font-medium text-gray-800">我的订单</h4>
                          <p className="text-sm text-gray-600">管理买卖订单，跟踪交易状态</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 沟通与管理 */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-900 text-lg mb-3">💬 沟通与管理</h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <span className="text-indigo-500 mr-2">🟣</span>
                        <div>
                          <h4 className="font-medium text-gray-800">聊天中心</h4>
                          <p className="text-sm text-gray-600">与买卖双方实时沟通，协商交易细节</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <span className="text-pink-500 mr-2">🩷</span>
                        <div>
                          <h4 className="font-medium text-gray-800">收藏夹</h4>
                          <p className="text-sm text-gray-600">收藏感兴趣的商品，方便后续查看</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <span className="text-purple-500 mr-2">🟣</span>
                        <div>
                          <h4 className="font-medium text-gray-800">反馈助手</h4>
                          <p className="text-sm text-gray-600">提交意见反馈，联系客服支持</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 账户与安全 */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-900 text-lg mb-3">🔐 账户与安全</h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <span className="text-emerald-500 mr-2">💚</span>
                        <div>
                          <h4 className="font-medium text-gray-800">保证金管理</h4>
                          <p className="text-sm text-gray-600">管理担保金，申请中间人身份</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <span className="text-cyan-500 mr-2">🔵</span>
                        <div>
                          <h4 className="font-medium text-gray-800">信用评级</h4>
                          <p className="text-sm text-gray-600">查看信用分数和交易历史记录</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <span className="text-gray-500 mr-2">⚫</span>
                        <div>
                          <h4 className="font-medium text-gray-800">账户设置</h4>
                          <p className="text-sm text-gray-600">修改密码、邮箱等安全设置</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 中间人系统 */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-900 text-lg mb-3">🛡️ 中间人系统</h3>
                    <div className="space-y-3">
                      <div className="bg-green-50 p-3 rounded-lg">
                        <h4 className="font-medium text-green-800 mb-1">什么是中间人？</h4>
                        <p className="text-sm text-green-700">
                          中间人是平台认证的高信誉用户，为交易双方提供担保服务，确保交易安全。
                        </p>
                      </div>
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <h4 className="font-medium text-blue-800 mb-1">申请条件</h4>
                        <ul className="text-sm text-blue-700 space-y-1">
                          <li>• 信用分数不低于80分</li>
                          <li>• 缴纳保证金不少于1000 USDT</li>
                          <li>• 账户状态正常，无违规记录</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 完整交易流程 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-purple-50 px-6 py-4 border-b">
                <div className="flex items-center">
                  <ChartBarIcon className="h-6 w-6 text-purple-600 mr-3" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    🔄 完整交易流程指南
                  </h2>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  从浏览商品到完成交易的详细步骤说明
                </p>
              </div>

              <div className="p-6">
                <div className="space-y-6">
                  {[
                    {
                      step: 1,
                      title: '浏览商品/发布需求',
                      desc: '在商品列表中找到心仪的商品，或发布求购需求等待卖家响应',
                      details: ['查看商品详情和卖家信用', '对比价格和商品描述', '可以收藏感兴趣的商品']
                    },
                    {
                      step: 2,
                      title: '沟通协商',
                      desc: '通过聊天中心与卖家沟通，确认商品细节和交易条件',
                      details: ['讨论商品状态和价格', '确认发货方式和时间', '协商支付方式']
                    },
                    {
                      step: 3,
                      title: '创建订单',
                      desc: '点击购买，填写收货地址，确认订单信息和总金额',
                      details: ['填写准确的收货地址', '确认商品数量和价格', '查看平台手续费']
                    },
                    {
                      step: 4,
                      title: '选择支付方式',
                      desc: '选择Binance Pay、BNB Chain等USDT支付方式进行付款',
                      details: ['Binance Pay：扫码支付', 'BNB Chain：链上转账', '确认收款地址正确']
                    },
                    {
                      step: 5,
                      title: '上传支付凭证',
                      desc: '完成支付后，上传交易哈希、订单号或支付截图',
                      details: ['获取币安交易哈希', '上传支付截图', '等待系统验证']
                    },
                    {
                      step: 6,
                      title: '托管确认',
                      desc: '卖家确认收款，资金进入平台托管状态，保障双方权益',
                      details: ['资金安全托管', '防止欺诈风险', '可申请中间人担保']
                    },
                    {
                      step: 7,
                      title: '卖家发货',
                      desc: '卖家安排发货，提供物流单号，买家可跟踪物流状态',
                      details: ['获取物流信息', '跟踪发货进度', '保持沟通联系']
                    },
                    {
                      step: 8,
                      title: '确认收货',
                      desc: '收到商品并验收无误后确认收货，资金自动释放给卖家',
                      details: ['检查商品质量', '确认与描述一致', '点击确认收货']
                    },
                    {
                      step: 9,
                      title: '互相评价',
                      desc: '交易完成后互相评价，建立信誉记录，提升信用分数',
                      details: ['评价交易体验', '提升信用分数', '建立良好声誉']
                    }
                  ].map((item, index) => (
                    <div key={index} className="border-l-4 border-purple-500 pl-4">
                      <div className="flex items-start mb-2">
                        <div className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                          {item.step}
                        </div>
                        <div className="ml-4">
                          <h3 className="font-medium text-gray-900">{item.title}</h3>
                          <p className="text-sm text-gray-600 mb-2">{item.desc}</p>
                        </div>
                      </div>
                      <div className="ml-12">
                        <ul className="text-xs text-gray-500 space-y-1">
                          {item.details.map((detail, idx) => (
                            <li key={idx}>• {detail}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 特殊提示 */}
                <div className="mt-8 bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">💡 交易小贴士</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• <strong>信用分数</strong>：完成交易可获得信用分数，提升交易权限</li>
                    <li>• <strong>中间人服务</strong>：大额交易建议使用中间人担保服务</li>
                    <li>• <strong>保证金</strong>：成为中间人需要缴纳保证金，享受担保费收入</li>
                    <li>• <strong>安全提醒</strong>：只向平台指定地址转账，谨防诈骗</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 安全提示 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-red-50 px-6 py-4 border-b">
                <div className="flex items-center">
                  <ShieldCheckIcon className="h-6 w-6 text-red-600 mr-3" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    🛡️ 交易安全提示
                  </h2>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-6">
                  {/* 验证收款地址 */}
                  <div className="border-l-4 border-red-500 pl-4">
                    <h3 className="font-medium text-gray-900 mb-2">验证收款地址</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 务必确认收款地址是平台提供的官方地址</li>
                      <li>• 不要向个人地址转账，避免资金损失</li>
                      <li>• 如有疑问，请联系客服确认</li>
                    </ul>
                  </div>

                  {/* 区块链浏览器 */}
                  <div className="border-l-4 border-yellow-500 pl-4">
                    <h3 className="font-medium text-gray-900 mb-2">使用区块链浏览器</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 推荐使用 BSCscan.com 查询BNB链交易</li>
                      <li>• 输入交易哈希可查看转账详情</li>
                      <li>• 确认交易状态和到账情况</li>
                    </ul>
                  </div>

                  {/* 防诈骗提醒 */}
                  <div className="border-l-4 border-orange-500 pl-4">
                    <h3 className="font-medium text-gray-900 mb-2">防诈骗提醒</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 不要相信"代付款"、"垫付"等要求</li>
                      <li>• 不要向陌生人透露私钥或助记词</li>
                      <li>• 遇到可疑情况立即联系客服</li>
                      <li>• 保持警惕，理性交易</li>
                    </ul>
                  </div>

                  {/* 账户安全 */}
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h3 className="font-medium text-gray-900 mb-2">账户安全管理</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 定期修改登录密码，使用强密码</li>
                      <li>• 绑定安全邮箱，及时接收安全通知</li>
                      <li>• 在账户设置中查看登录记录</li>
                      <li>• 发现异常登录立即修改密码</li>
                    </ul>
                  </div>

                  {/* 信用系统 */}
                  <div className="border-l-4 border-green-500 pl-4">
                    <h3 className="font-medium text-gray-900 mb-2">信用系统说明</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 初始信用分30分，完成交易可提升</li>
                      <li>• 信用分影响交易权限和中间人申请</li>
                      <li>• 违规行为会导致信用分扣减</li>
                      <li>• 保持良好交易记录提升信誉</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 常见问题 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-6 py-4 border-b">
                <div className="flex items-center">
                  <QuestionMarkCircleIcon className="h-6 w-6 text-gray-600 mr-3" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    ❓ 常见问题
                  </h2>
                </div>
              </div>

              <div className="p-6">
                <FAQCategories
                  selectedCategory={faqCategory}
                  onCategoryChange={setFaqCategory}
                />
                <FAQ category={faqCategory} maxItems={8} />
                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-600 mb-3">
                    没有找到您要的答案？
                  </p>
                  <Link
                    href="/radar"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                    联系客服
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧边栏 */}
          <div className="space-y-6">
            {/* 快速链接 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">快速链接</h3>
              <div className="space-y-3">
                <Link
                  href="/products/create"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🟠 发布商品</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/demands/create"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🟡 发布需求</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/chat"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🟣 聊天中心</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/favorites"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🩷 收藏夹</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/deposit"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>💚 保证金管理</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/credit"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🔵 信用评级</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/orders"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🔵 我的订单</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/radar"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>🟣 反馈助手</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/settings"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>⚫ 账户设置</span>
                  <span className="ml-auto">→</span>
                </Link>
                <Link
                  href="/profile"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <span>👤 个人中心</span>
                  <span className="ml-auto">→</span>
                </Link>
              </div>
            </div>

            {/* 联系客服 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">需要帮助？</h3>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  <p className="mb-2">如果您在使用过程中遇到问题，可以通过以下方式联系我们：</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <ChatBubbleLeftRightIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-700">在线客服：24小时在线</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="text-gray-700">邮箱：<EMAIL></span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="text-gray-700">电话：400-123-4567</span>
                  </div>
                </div>
                <Link
                  href="/radar"
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  提交反馈
                </Link>
              </div>
            </div>

            {/* 帮助反馈 */}
            <HelpFeedback />
          </div>
        </div>
      </div>

      {/* 返回顶部按钮 */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 z-50"
          aria-label="返回顶部"
        >
          <ChevronUpIcon className="h-6 w-6" />
        </button>
      )}
    </div>
  )
}

// 帮助反馈组件
function HelpFeedback() {
  const { data: session } = useSession()
  const [feedbackType, setFeedbackType] = useState('')
  const [feedbackContent, setFeedbackContent] = useState('')
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showFeedback, setShowFeedback] = useState(false)

  const handleSubmitFeedback = async () => {
    if (!feedbackType || !feedbackContent.trim()) {
      alert('请选择反馈类型并填写反馈内容')
      return
    }

    if (!session && !email.trim()) {
      alert('请提供联系邮箱')
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/help/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: feedbackType,
          content: feedbackContent,
          email: email,
          helpfulArticle: 'help-center-main'
        }),
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message)
        setFeedbackContent('')
        setFeedbackType('')
        setEmail('')
        setShowFeedback(false)
      } else {
        const errorData = await response.json()
        alert(errorData.error || '提交失败，请稍后重试')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">帮助反馈</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleSubmitFeedback()}
            className="flex items-center text-green-600 hover:text-green-700 text-sm"
          >
            <HandThumbUpIcon className="h-4 w-4 mr-1" />
            有帮助
          </button>
          <button
            onClick={() => setShowFeedback(true)}
            className="flex items-center text-red-600 hover:text-red-700 text-sm"
          >
            <HandThumbDownIcon className="h-4 w-4 mr-1" />
            需要改进
          </button>
        </div>
      </div>

      {showFeedback && (
        <div className="space-y-4 border-t pt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              反馈类型
            </label>
            <select
              value={feedbackType}
              onChange={(e) => setFeedbackType(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">请选择</option>
              <option value="not-helpful">内容不够清晰</option>
              <option value="suggestion">建议增加内容</option>
              <option value="error">发现错误</option>
              <option value="helpful">内容很有帮助</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              详细说明
            </label>
            <textarea
              value={feedbackContent}
              onChange={(e) => setFeedbackContent(e.target.value)}
              placeholder="请详细描述您的建议或遇到的问题..."
              rows={4}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {!session && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                联系邮箱
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          )}

          <div className="flex space-x-3">
            <button
              onClick={handleSubmitFeedback}
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {isSubmitting ? '提交中...' : '提交反馈'}
            </button>
            <button
              onClick={() => setShowFeedback(false)}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
