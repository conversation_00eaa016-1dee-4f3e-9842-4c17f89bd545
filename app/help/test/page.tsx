'use client'

import Link from 'next/link'
import Navbar from '@/components/Navbar'

export default function HelpTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            帮助中心测试页面
          </h1>
          
          <div className="space-y-4">
            <p className="text-gray-700">
              这是一个测试页面，用于验证帮助中心路由是否正常工作。
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">API测试</h3>
                <div className="space-y-2">
                  <button 
                    onClick={() => fetch('/api/help/search?q=币安').then(r => r.json()).then(console.log)}
                    className="block w-full text-left bg-blue-100 hover:bg-blue-200 px-3 py-2 rounded text-sm"
                  >
                    测试搜索API
                  </button>
                  <button 
                    onClick={() => fetch('/api/help/feedback', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ type: 'helpful', content: '测试反馈' })
                    }).then(r => r.json()).then(console.log)}
                    className="block w-full text-left bg-green-100 hover:bg-green-200 px-3 py-2 rounded text-sm"
                  >
                    测试反馈API
                  </button>
                </div>
              </div>
              
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">页面导航</h3>
                <div className="space-y-2">
                  <Link 
                    href="/help"
                    className="block bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm text-center"
                  >
                    返回帮助中心主页
                  </Link>
                  <Link 
                    href="/"
                    className="block bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm text-center"
                  >
                    返回首页
                  </Link>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-gray-100 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">功能检查清单</h3>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>✅ 帮助中心路由 (/help)</li>
                <li>✅ 搜索API (/api/help/search)</li>
                <li>✅ 反馈API (/api/help/feedback)</li>
                <li>✅ 导航栏集成</li>
                <li>✅ 响应式设计</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
