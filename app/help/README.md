# BitMarket 帮助中心

## 概述

BitMarket帮助中心是一个专门为新用户设计的USDT交易指导平台，提供详细的操作教程和常见问题解答。

## 功能特性

### 🎯 核心功能
- **币安哈希值获取教程** - 详细的图文指导
- **USDT支付方式介绍** - 三种支付方式对比
- **完整交易流程指南** - 8步交易流程
- **安全提示** - 防诈骗和安全验证指导
- **常见问题FAQ** - 分类问答系统

### 🔍 搜索功能
- 全文搜索支持
- 分类筛选
- 关键词匹配
- 实时搜索结果

### 💬 反馈系统
- 用户反馈收集
- 帮助评价（有用/需要改进）
- 匿名反馈支持
- 客服联系方式

### 📱 用户体验
- 响应式设计
- 移动端优化
- 返回顶部功能
- 面包屑导航
- 快速链接

## 页面结构

```
/help/
├── page.tsx          # 主帮助页面
├── layout.tsx        # 帮助中心布局
├── test/page.tsx     # 测试页面
└── README.md         # 说明文档
```

## API接口

### 搜索API
```
GET /api/help/search?q=关键词&category=分类
```

### 反馈API
```
POST /api/help/feedback
{
  "type": "helpful|not-helpful|suggestion|error",
  "content": "反馈内容",
  "email": "联系邮箱（可选）"
}
```

## 组件说明

### FAQ组件 (`/components/help/FAQ.tsx`)
- 可折叠的问答列表
- 分类筛选功能
- 动态内容加载

### HelpSection组件 (`/components/help/HelpSection.tsx`)
- 可重用的帮助内容容器
- 支持展开/折叠
- 自定义图标和颜色

## 内容管理

### 币安哈希值教程
包含三个主要步骤：
1. 打开币安App导航
2. 查找提现记录
3. 复制交易哈希

每个步骤都包含：
- 详细操作说明
- 图片占位符
- 注意事项

### 支付方式介绍
- Binance Pay
- Binance API
- BNB Chain

### 安全提示
- 地址验证
- 区块链浏览器使用
- 防诈骗提醒

## 导航集成

帮助中心已集成到主导航栏中：
- 登录用户：浏览商品 | 需求广场 | 帮助中心 | 个人中心
- 未登录用户：浏览商品 | 帮助中心 | 登录 | 注册

## 样式设计

遵循BitMarket设计规范：
- 蓝色主题色 (#2563eb)
- 白色卡片背景
- 阴影效果
- 圆角设计
- 响应式布局

## 使用方法

1. 访问 `/help` 查看帮助中心主页
2. 使用搜索功能查找特定内容
3. 点击分类快速筛选
4. 查看FAQ获取常见问题答案
5. 提交反馈改进内容

## 开发说明

### 添加新的帮助内容
1. 在 `/api/help/search/route.ts` 中添加内容数据
2. 在主页面中添加相应的UI组件
3. 更新FAQ数据（如需要）

### 自定义样式
所有样式使用Tailwind CSS，遵循现有设计系统。

### 测试
访问 `/help/test` 进行功能测试。

## 未来改进

- [ ] 添加视频教程支持
- [ ] 多语言支持
- [ ] 内容收藏功能
- [ ] 社交分享功能
- [ ] 用户评分系统
- [ ] 智能推荐相关内容
