'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import StatsCard from '@/components/admin/StatsCard'
import {
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface DashboardStats {
  totalUsers: number
  totalProducts: number
  totalOrders: number
  pendingOrders: number
  totalRevenue: number
  pendingPayments: number
  feedbacks?: number
  announcements?: number
  helpArticles?: number
  giftCards?: {
    total: number
    generated: number
    sold: number
    redeemed: number
    expired: number
    totalValue: number
  }
  redemptionCodes?: {
    total: number
    active: number
    used: number
    expired: number
    revoked: number
  }
}

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (status === 'loading') return // 等待会话加载完成
    checkAdminAccess()
  }, [session, status])

  const checkAdminAccess = async () => {
    if (status === 'loading') return // 会话还在加载中

    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      // 首先检查管理员权限
      const checkResponse = await fetch('/api/admin/check')
      const checkData = await checkResponse.json()

      console.log('管理员权限检查结果:', checkData)

      if (!checkResponse.ok) {
        if (checkResponse.status === 401) {
          router.push('/auth/signin')
          return
        } else if (checkResponse.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError(checkData.error || '权限验证失败')
        }
        setIsLoading(false)
        return
      }

      // 权限验证通过，获取仪表板数据
      const dashboardResponse = await fetch('/api/admin/dashboard')
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json()
        setStats(dashboardData)
      } else {
        setError('获取统计数据失败')
      }
    } catch (error) {
      console.error('管理员页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回首页
          </Link>
        </div>
      </div>
    )
  }

  return (
    <AdminLayout
      title="仪表板"
      subtitle="比特市场平台管理控制台"
    >

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="总用户数"
            value={stats.totalUsers}
            icon={<UsersIcon className="w-6 h-6" />}
            color="blue"
            onClick={() => router.push('/admin/users')}
          />
          <StatsCard
            title="总订单数"
            value={stats.totalOrders}
            icon={<ShoppingBagIcon className="w-6 h-6" />}
            color="green"
            onClick={() => router.push('/admin/orders')}
          />
          <StatsCard
            title="待处理订单"
            value={stats.pendingOrders}
            icon={<ExclamationTriangleIcon className="w-6 h-6" />}
            color="yellow"
            onClick={() => router.push('/admin/orders')}
          />
          <StatsCard
            title="总收入"
            value={`$${(stats.totalRevenue || 0).toLocaleString()}`}
            icon={<CurrencyDollarIcon className="w-6 h-6" />}
            color="purple"
            onClick={() => router.push('/admin/payments')}
          />
        </div>
      )}

      {/* 快捷操作 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">用户管理</h3>
          <div className="space-y-3">
            <Link href="/admin/users" className="block text-blue-600 hover:text-blue-800">
              • 查看所有用户
            </Link>
            <Link href="/admin/mediators" className="block text-blue-600 hover:text-blue-800">
              • 中间人管理
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">订单管理</h3>
          <div className="space-y-3">
            <Link href="/admin/orders" className="block text-blue-600 hover:text-blue-800">
              • 查看所有订单
            </Link>
            <Link href="/admin/escrow-orders" className="block text-blue-600 hover:text-blue-800">
              • 托管订单管理
            </Link>
            <Link href="/admin/disputes" className="block text-blue-600 hover:text-blue-800">
              • 争议处理
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">财务管理</h3>
          <div className="space-y-3">
            <Link href="/admin/payments" className="block text-blue-600 hover:text-blue-800">
              • 支付管理
            </Link>
            <Link href="/admin/deposits" className="block text-blue-600 hover:text-blue-800">
              • 保证金管理
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">内容管理</h3>
          <div className="space-y-3">
            <Link href="/admin/products" className="block text-blue-600 hover:text-blue-800">
              • 商品管理
            </Link>
            <Link href="/admin/announcements" className="block text-blue-600 hover:text-blue-800">
              • 公告管理
            </Link>
            <Link href="/admin/help" className="block text-blue-600 hover:text-blue-800">
              • 帮助中心
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统管理</h3>
          <div className="space-y-3">
            <Link href="/admin/settings" className="block text-blue-600 hover:text-blue-800">
              • 系统设置
            </Link>
            <Link href="/admin/reports" className="block text-blue-600 hover:text-blue-800">
              • 数据报告
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">礼品卡管理</h3>
          <div className="space-y-3">
            <Link href="/admin/giftcards" className="block text-blue-600 hover:text-blue-800">
              • 礼品卡管理
            </Link>
            <Link href="/admin/redemption-codes" className="block text-blue-600 hover:text-blue-800">
              • 兑换码管理
            </Link>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
