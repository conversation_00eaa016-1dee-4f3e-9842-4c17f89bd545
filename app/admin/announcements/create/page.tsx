'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

interface AnnouncementForm {
  title: string
  content: string
  summary: string
  category: string
  priority: string
  targetUsers: string
  status: string
  isSticky: boolean
  showOnHome: boolean
  publishAt: string
  expireAt: string
}

export default function CreateAnnouncementPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  const [formData, setFormData] = useState<AnnouncementForm>({
    title: '',
    content: '',
    summary: '',
    category: 'GENERAL',
    priority: 'NORMAL',
    targetUsers: 'ALL',
    status: 'DRAFT',
    isSticky: false,
    showOnHome: false,
    publishAt: '',
    expireAt: ''
  })

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空'
    } else if (formData.title.length > 100) {
      newErrors.title = '标题不能超过100个字符'
    }

    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空'
    }

    if (formData.summary && formData.summary.length > 200) {
      newErrors.summary = '摘要不能超过200个字符'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    
    try {
      const response = await fetch('/api/admin/announcements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        router.push('/admin/announcements')
      } else {
        const errorData = await response.json()
        setErrors({ submit: errorData.message || '创建失败' })
      }
    } catch (error) {
      console.error('创建公告失败:', error)
      setErrors({ submit: '网络错误，请重试' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePreview = () => {
    if (!formData.title || !formData.content) {
      alert('请先填写标题和内容')
      return
    }

    const previewContent = `标题: ${formData.title}\n内容: ${formData.content}`
    alert(previewContent)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center">
            <Link
              href="/admin/announcements"
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">创建公告</h1>
              <p className="text-gray-600">发布新的系统公告</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="bg-white shadow rounded-lg">
          <div className="px-6 py-6">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  标题 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className={`mt-1 w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="请输入公告标题"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                  内容 <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  rows={10}
                  className={`mt-1 w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.content ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="请输入公告详细内容"
                />
                {errors.content && (
                  <p className="mt-1 text-sm text-red-600">{errors.content}</p>
                )}
              </div>
            </div>
          </div>

          {errors.submit && (
            <div className="px-6 py-3 bg-red-50 border-t border-red-200">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
            <button
              type="button"
              onClick={handlePreview}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              预览
            </button>
            
            <div className="flex space-x-3">
              <Link
                href="/admin/announcements"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                取消
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? '创建中...' : '创建公告'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
