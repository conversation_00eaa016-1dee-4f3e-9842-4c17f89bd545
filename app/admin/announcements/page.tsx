'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  SpeakerWaveIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface Announcement {
  id: string
  title: string
  content: string
  summary: string | null
  category: string
  priority: string
  targetUsers: string
  status: string
  isSticky: boolean
  showOnHome: boolean
  publishAt: string | null
  expireAt: string | null
  viewCount: number
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string | null
    email: string | null
  }
  reviewer: {
    id: string
    name: string | null
    email: string | null
  } | null
}

interface AnnouncementListResponse {
  announcements: Announcement[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function AnnouncementsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 筛选和搜索状态
  const [currentPage, setCurrentPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // 检查管理员权限
  const checkAdminAccess = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/check')
      if (!response.ok) {
        if (response.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError('权限验证失败')
        }
        setIsLoading(false)
        return
      }
      
      // 权限验证通过，加载数据
      await fetchAnnouncements()
    } catch (error) {
      console.error('权限检查失败:', error)
      setError('网络错误')
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    checkAdminAccess()
  }, [session, status, router, checkAdminAccess])

  // 获取公告列表
  const fetchAnnouncements = async (page = 1) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      })

      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (categoryFilter !== 'all') params.append('category', categoryFilter)
      if (searchQuery.trim()) params.append('search', searchQuery.trim())

      const response = await fetch(`/api/admin/announcements?${params}`)
      if (response.ok) {
        const data: AnnouncementListResponse = await response.json()
        setAnnouncements(data.announcements)
        setPagination(data.pagination)
        setCurrentPage(page)
      } else {
        setError('获取公告列表失败')
      }
    } catch (error) {
      console.error('获取公告列表失败:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  // 删除公告
  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`确定要删除公告"${title}"吗？此操作不可恢复。`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/announcements/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('公告删除成功')
        fetchAnnouncements(currentPage)
      } else {
        const data = await response.json()
        alert(data.error || '删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1)
    fetchAnnouncements(1)
  }

  // 重置筛选
  const handleReset = () => {
    setStatusFilter('all')
    setCategoryFilter('all')
    setSearchQuery('')
    setCurrentPage(1)
    fetchAnnouncements(1)
  }

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const statusMap = {
      'DRAFT': { text: '草稿', color: 'bg-gray-100 text-gray-800' },
      'PUBLISHED': { text: '已发布', color: 'bg-green-100 text-green-800' },
      'ARCHIVED': { text: '已归档', color: 'bg-yellow-100 text-yellow-800' },
      'DELETED': { text: '已删除', color: 'bg-red-100 text-red-800' }
    }
    return statusMap[status as keyof typeof statusMap] || { text: status, color: 'bg-gray-100 text-gray-800' }
  }

  // 获取分类显示
  const getCategoryDisplay = (category: string) => {
    const categoryMap = {
      'GENERAL': { text: '一般', icon: InformationCircleIcon },
      'URGENT': { text: '紧急', icon: ExclamationTriangleIcon },
      'MAINTENANCE': { text: '维护', icon: SpeakerWaveIcon },
      'FEATURE': { text: '功能', icon: PlusIcon },
      'SECURITY': { text: '安全', icon: ExclamationTriangleIcon }
    }
    return categoryMap[category as keyof typeof categoryMap] || { text: category, icon: InformationCircleIcon }
  }

  // 获取优先级显示
  const getPriorityDisplay = (priority: string) => {
    const priorityMap = {
      'LOW': { text: '低', color: 'text-gray-600' },
      'NORMAL': { text: '普通', color: 'text-blue-600' },
      'HIGH': { text: '高', color: 'text-orange-600' },
      'URGENT': { text: '紧急', color: 'text-red-600' }
    }
    return priorityMap[priority as keyof typeof priorityMap] || { text: priority, color: 'text-gray-600' }
  }

  if (isLoading && announcements.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">访问受限</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理首页
          </Link>
        </div>
      </div>
    )
  }

  return (
    <AdminLayout
      title="公告管理"
      subtitle={`管理平台公告，包括创建、编辑、发布和删除操作，共 ${pagination?.total || 0} 个公告`}
    >
      {/* 页面内容 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div></div>
            <Link
              href="/admin/announcements/create"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              创建公告
            </Link>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* 状态筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部状态</option>
                <option value="DRAFT">草稿</option>
                <option value="PUBLISHED">已发布</option>
                <option value="ARCHIVED">已归档</option>
              </select>
            </div>

            {/* 分类筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分类
              </label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部分类</option>
                <option value="GENERAL">一般</option>
                <option value="URGENT">紧急</option>
                <option value="MAINTENANCE">维护</option>
                <option value="FEATURE">功能</option>
                <option value="SECURITY">安全</option>
              </select>
            </div>

            {/* 搜索 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索标题或内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-end space-x-2">
              <button
                onClick={handleSearch}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                搜索
              </button>
              <button
                onClick={handleReset}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                重置
              </button>
            </div>
          </div>
        </div>

        {/* 公告列表 */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">加载中...</p>
            </div>
          ) : announcements.length === 0 ? (
            <div className="p-8 text-center">
              <SpeakerWaveIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无公告</h3>
              <p className="text-gray-600 mb-4">还没有创建任何公告</p>
              <Link
                href="/admin/announcements/create"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                创建第一个公告
              </Link>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        公告信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        分类/优先级
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        统计
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {announcements.map((announcement) => {
                      const statusDisplay = getStatusDisplay(announcement.status)
                      const categoryDisplay = getCategoryDisplay(announcement.category)
                      const priorityDisplay = getPriorityDisplay(announcement.priority)
                      const CategoryIcon = categoryDisplay.icon

                      return (
                        <tr key={announcement.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="flex items-start">
                              <div className="flex-1">
                                <div className="flex items-center">
                                  <h3 className="text-sm font-medium text-gray-900">
                                    {announcement.title}
                                  </h3>
                                  {announcement.isSticky && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                      置顶
                                    </span>
                                  )}
                                  {announcement.showOnHome && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                      首页
                                    </span>
                                  )}
                                </div>
                                {announcement.summary && (
                                  <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                                    {announcement.summary}
                                  </p>
                                )}
                                <div className="mt-1 text-xs text-gray-500">
                                  作者: {announcement.author.name || announcement.author.email}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <CategoryIcon className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">{categoryDisplay.text}</span>
                            </div>
                            <div className={`text-sm font-medium ${priorityDisplay.color}`}>
                              {priorityDisplay.text}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                              {statusDisplay.text}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            <div>查看: {announcement.viewCount}</div>
                            <div className="text-xs text-gray-500">
                              目标: {announcement.targetUsers}
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            {new Date(announcement.createdAt).toLocaleDateString('zh-CN')}
                          </td>
                          <td className="px-6 py-4 text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link
                                href={`/admin/announcements/${announcement.id}`}
                                className="text-blue-600 hover:text-blue-900"
                                title="查看详情"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Link>
                              <Link
                                href={`/admin/announcements/${announcement.id}/edit`}
                                className="text-green-600 hover:text-green-900"
                                title="编辑"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>
                              <button
                                onClick={() => handleDelete(announcement.id, announcement.title)}
                                className="text-red-600 hover:text-red-900"
                                title="删除"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 flex justify-between sm:hidden">
                      <button
                        onClick={() => fetchAnnouncements(currentPage - 1)}
                        disabled={currentPage <= 1}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        上一页
                      </button>
                      <button
                        onClick={() => fetchAnnouncements(currentPage + 1)}
                        disabled={currentPage >= pagination.pages}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        下一页
                      </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          显示第 <span className="font-medium">{(currentPage - 1) * pagination.limit + 1}</span> 到{' '}
                          <span className="font-medium">
                            {Math.min(currentPage * pagination.limit, pagination.total)}
                          </span>{' '}
                          条，共 <span className="font-medium">{pagination.total}</span> 条记录
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                          <button
                            onClick={() => fetchAnnouncements(currentPage - 1)}
                            disabled={currentPage <= 1}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            上一页
                          </button>
                          {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                            const page = i + 1
                            return (
                              <button
                                key={page}
                                onClick={() => fetchAnnouncements(page)}
                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                  page === currentPage
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                }`}
                              >
                                {page}
                              </button>
                            )
                          })}
                          <button
                            onClick={() => fetchAnnouncements(currentPage + 1)}
                            disabled={currentPage >= pagination.pages}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            下一页
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
    </AdminLayout>
  )
}
