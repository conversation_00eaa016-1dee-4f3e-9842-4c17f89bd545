'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

interface AnnouncementForm {
  title: string
  content: string
  summary: string
  category: string
  priority: string
  targetUsers: string
  status: string
  isSticky: boolean
  showOnHome: boolean
  publishAt: string
  expireAt: string
}

interface Announcement {
  id: string
  title: string
  content: string
  summary: string | null
  category: string
  priority: string
  targetUsers: string
  status: string
  isSticky: boolean
  showOnHome: boolean
  publishAt: string | null
  expireAt: string | null
  viewCount: number
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string | null
    email: string | null
  }
  reviewer: {
    id: string
    name: string | null
    email: string | null
  } | null
}

export default function EditAnnouncementPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const [announcementId, setAnnouncementId] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  
  const [formData, setFormData] = useState<AnnouncementForm>({
    title: '',
    content: '',
    summary: '',
    category: 'GENERAL',
    priority: 'NORMAL',
    targetUsers: 'ALL',
    status: 'DRAFT',
    isSticky: false,
    showOnHome: false,
    publishAt: '',
    expireAt: ''
  })

  // 获取路由参数
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params // TODO: Add error handling
      setAnnouncementId(resolvedParams.id)
    }
    getParams()
  }, [params])

  // 加载公告数据
  useEffect(() => {
    if (announcementId) {
      fetchAnnouncement()
    }
  }, [announcementId])

  const fetchAnnouncement = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/admin/announcements/${announcementId}`)
      
      if (response.ok) {
        const data: Announcement = await response.json()
        setAnnouncement(data)
        
        // 填充表单数据
        setFormData({
          title: data.title,
          content: data.content,
          summary: data.summary || '',
          category: data.category,
          priority: data.priority,
          targetUsers: data.targetUsers,
          status: data.status,
          isSticky: data.isSticky,
          showOnHome: data.showOnHome,
          publishAt: data.publishAt ? new Date(data.publishAt).toISOString().slice(0, 16) : '',
          expireAt: data.expireAt ? new Date(data.expireAt).toISOString().slice(0, 16) : ''
        })
      } else if (response.status === 404) {
        alert('公告不存在')
        router.push('/admin/announcements')
      } else {
        alert('加载公告失败')
      }
    } catch (error) {
      console.error('加载公告失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空'
    } else if (formData.title.length > 100) {
      newErrors.title = '标题不能超过100个字符'
    }

    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空'
    }

    if (formData.summary && formData.summary.length > 200) {
      newErrors.summary = '摘要不能超过200个字符'
    }

    if (formData.publishAt && new Date(formData.publishAt) < new Date()) {
      newErrors.publishAt = '发布时间不能早于当前时间'
    }

    if (formData.expireAt && formData.publishAt && 
        new Date(formData.expireAt) <= new Date(formData.publishAt)) {
      newErrors.expireAt = '过期时间必须晚于发布时间'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/admin/announcements/${announcementId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          publishAt: formData.publishAt || null,
          expireAt: formData.expireAt || null
        }),
      })

      if (response.ok) {
        alert('公告更新成功！')
        router.push(`/admin/announcements/${announcementId}`)
      } else {
        const data = await response.json()
        alert(data.error || '更新失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  // 预览功能
  const handlePreview = () => {
    if (!formData.title || !formData.content) {
      alert('请先填写标题和内容')
      return
    }

    // 在新窗口中显示预览
    const previewWindow = window.open('', '_blank', 'width=800,height=600')
    if (previewWindow) {
      previewWindow.document.write(`
        <html>
          <head>
            <title>公告预览</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
              .header { border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 20px; }
              .title { font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 10px; }
              .meta { color: #6b7280; font-size: 14px; }
              .content { white-space: pre-wrap; }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="title">${formData.title}</div>
              <div class="meta">
                分类: ${formData.category} | 优先级: ${formData.priority} | 目标用户: ${formData.targetUsers}
              </div>
            </div>
            <div class="content">${formData.content}</div>
          </body>
        </html>
      `)
      previewWindow.document.close()
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!announcement) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">公告不存在</h2>
          <Link
            href="/admin/announcements"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回公告列表
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center">
            <Link
              href="/admin/announcements"
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">编辑公告</h1>
              <p className="mt-1 text-sm text-gray-600">
                编辑公告信息，可以修改内容、状态和发布设置
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            {/* 基本信息 */}
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 border-b pb-2">基本信息</h2>

              {/* 标题 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  公告标题 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="请输入公告标题"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* 摘要 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  公告摘要
                </label>
                <textarea
                  value={formData.summary}
                  onChange={(e) => setFormData({ ...formData, summary: e.target.value })}
                  rows={2}
                  className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.summary ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="简要描述公告内容（可选）"
                />
                {errors.summary && (
                  <p className="mt-1 text-sm text-red-600">{errors.summary}</p>
                )}
              </div>

              {/* 内容 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  公告内容 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  rows={10}
                  className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.content ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="请输入公告详细内容"
                />
                {errors.content && (
                  <p className="mt-1 text-sm text-red-600">{errors.content}</p>
                )}
              </div>
            </div>
          </div>

          {/* 分类和设置 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 border-b pb-2">分类和设置</h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 分类 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    公告分类
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="GENERAL">一般</option>
                    <option value="URGENT">紧急</option>
                    <option value="MAINTENANCE">维护</option>
                    <option value="FEATURE">功能</option>
                    <option value="SECURITY">安全</option>
                  </select>
                </div>

                {/* 优先级 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    优先级
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="LOW">低</option>
                    <option value="NORMAL">普通</option>
                    <option value="HIGH">高</option>
                    <option value="URGENT">紧急</option>
                  </select>
                </div>

                {/* 目标用户 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    目标用户
                  </label>
                  <select
                    value={formData.targetUsers}
                    onChange={(e) => setFormData({ ...formData, targetUsers: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="ALL">所有用户</option>
                    <option value="BUYERS">买家</option>
                    <option value="SELLERS">卖家</option>
                    <option value="VIP">VIP用户</option>
                    <option value="NEW_USERS">新用户</option>
                  </select>
                </div>
              </div>

              {/* 显示选项 */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isSticky"
                    checked={formData.isSticky}
                    onChange={(e) => setFormData({ ...formData, isSticky: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isSticky" className="ml-2 block text-sm text-gray-900">
                    置顶显示
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showOnHome"
                    checked={formData.showOnHome}
                    onChange={(e) => setFormData({ ...formData, showOnHome: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="showOnHome" className="ml-2 block text-sm text-gray-900">
                    在首页显示
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* 发布设置 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 border-b pb-2">发布设置</h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 状态 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    发布状态
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="DRAFT">草稿</option>
                    <option value="PUBLISHED">已发布</option>
                    <option value="ARCHIVED">已归档</option>
                  </select>
                </div>

                {/* 定时发布 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    定时发布
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.publishAt}
                    onChange={(e) => setFormData({ ...formData, publishAt: e.target.value })}
                    className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.publishAt ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.publishAt && (
                    <p className="mt-1 text-sm text-red-600">{errors.publishAt}</p>
                  )}
                </div>

                {/* 过期时间 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    过期时间
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.expireAt}
                    onChange={(e) => setFormData({ ...formData, expireAt: e.target.value })}
                    className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.expireAt ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.expireAt && (
                    <p className="mt-1 text-sm text-red-600">{errors.expireAt}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 公告信息 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-medium text-gray-900 border-b pb-2 mb-4">公告信息</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <span className="text-gray-500">创建时间：</span>
                <span className="text-gray-900">
                  {new Date(announcement.createdAt).toLocaleString('zh-CN')}
                </span>
              </div>
              <div>
                <span className="text-gray-500">更新时间：</span>
                <span className="text-gray-900">
                  {new Date(announcement.updatedAt).toLocaleString('zh-CN')}
                </span>
              </div>
              <div>
                <span className="text-gray-500">创建者：</span>
                <span className="text-gray-900">
                  {announcement.author.name || announcement.author.email}
                </span>
              </div>
              <div>
                <span className="text-gray-500">查看次数：</span>
                <span className="text-gray-900">{announcement.viewCount}</span>
              </div>
              {announcement.reviewer && (
                <>
                  <div>
                    <span className="text-gray-500">审核者：</span>
                    <span className="text-gray-900">
                      {announcement.reviewer.name || announcement.reviewer.email}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">审核时间：</span>
                    <span className="text-gray-900">
                      {announcement.reviewer ? '已审核' : '未审核'}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={handlePreview}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
              >
                预览
              </button>

              <div className="flex space-x-3">
                <Link
                  href="/admin/announcements"
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
                >
                  取消
                </Link>
                <Link
                  href={`/admin/announcements/${announcementId}`}
                  className="bg-blue-300 hover:bg-blue-400 text-blue-700 px-6 py-2 rounded-md text-sm font-medium"
                >
                  查看详情
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  {isSubmitting ? '保存中...' : '保存更改'}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
