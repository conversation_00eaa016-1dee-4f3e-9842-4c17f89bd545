'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  TrashIcon,
  EyeIcon,
  CalendarIcon,
  UserIcon,
  TagIcon
} from '@heroicons/react/24/outline'

interface Announcement {
  id: string
  title: string
  content: string
  summary: string | null
  category: string
  priority: string
  targetUsers: string
  status: string
  isSticky: boolean
  showOnHome: boolean
  publishAt: string | null
  expireAt: string | null
  viewCount: number
  createdAt: string
  updatedAt: string
  reviewedAt: string | null
  reviewNote: string | null
  author: {
    id: string
    name: string | null
    email: string | null
  }
  reviewer: {
    id: string
    name: string | null
    email: string | null
  } | null
}

export default function AnnouncementDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const [announcementId, setAnnouncementId] = useState<string>('')
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  // 获取路由参数
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params // TODO: Add error handling
      setAnnouncementId(resolvedParams.id)
    }
    getParams()
  }, [params])

  // 加载公告数据
  useEffect(() => {
    if (announcementId) {
      fetchAnnouncement()
    }
  }, [announcementId])

  const fetchAnnouncement = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/admin/announcements/${announcementId}`)
      
      if (response.ok) {
        const data: Announcement = await response.json()
        setAnnouncement(data)
      } else if (response.status === 404) {
        setError('公告不存在')
      } else {
        setError('加载公告失败')
      }
    } catch (error) {
      console.error('加载公告失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 删除公告
  const handleDelete = async () => {
    if (!announcement) return

    if (!confirm(`确定要删除公告"${announcement.title}"吗？此操作不可恢复。`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/announcements/${announcementId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('公告删除成功')
        router.push('/admin/announcements')
      } else {
        const data = await response.json()
        alert(data.error || '删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const statusMap = {
      'DRAFT': { text: '草稿', color: 'bg-gray-100 text-gray-800' },
      'PUBLISHED': { text: '已发布', color: 'bg-green-100 text-green-800' },
      'ARCHIVED': { text: '已归档', color: 'bg-yellow-100 text-yellow-800' },
      'DELETED': { text: '已删除', color: 'bg-red-100 text-red-800' }
    }
    return statusMap[status as keyof typeof statusMap] || { text: status, color: 'bg-gray-100 text-gray-800' }
  }

  // 获取分类显示
  const getCategoryDisplay = (category: string) => {
    const categoryMap = {
      'GENERAL': '一般',
      'URGENT': '紧急',
      'MAINTENANCE': '维护',
      'FEATURE': '功能',
      'SECURITY': '安全'
    }
    return categoryMap[category as keyof typeof categoryMap] || category
  }

  // 获取优先级显示
  const getPriorityDisplay = (priority: string) => {
    const priorityMap = {
      'LOW': { text: '低', color: 'text-gray-600' },
      'NORMAL': { text: '普通', color: 'text-blue-600' },
      'HIGH': { text: '高', color: 'text-orange-600' },
      'URGENT': { text: '紧急', color: 'text-red-600' }
    }
    return priorityMap[priority as keyof typeof priorityMap] || { text: priority, color: 'text-gray-600' }
  }

  // 获取目标用户显示
  const getTargetUsersDisplay = (targetUsers: string) => {
    const targetMap = {
      'ALL': '所有用户',
      'BUYERS': '买家',
      'SELLERS': '卖家',
      'VIP': 'VIP用户',
      'NEW_USERS': '新用户'
    }
    return targetMap[targetUsers as keyof typeof targetMap] || targetUsers
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error || !announcement) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {error || '公告不存在'}
          </h2>
          <Link
            href="/admin/announcements"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回公告列表
          </Link>
        </div>
      </div>
    )
  }

  const statusDisplay = getStatusDisplay(announcement.status)
  const priorityDisplay = getPriorityDisplay(announcement.priority)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和操作 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link
                href="/admin/announcements"
                className="mr-4 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">公告详情</h1>
                <p className="mt-1 text-sm text-gray-600">
                  查看和管理公告信息
                </p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Link
                href={`/admin/announcements/${announcementId}/edit`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                编辑
              </Link>
              <button
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                删除
              </button>
            </div>
          </div>
        </div>

        {/* 公告内容 */}
        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-start justify-between mb-6">
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {announcement.title}
                </h2>
                {announcement.summary && (
                  <p className="text-lg text-gray-600 mb-4">
                    {announcement.summary}
                  </p>
                )}
              </div>
              <div className="flex flex-col items-end space-y-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusDisplay.color}`}>
                  {statusDisplay.text}
                </span>
                {announcement.isSticky && (
                  <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                    置顶
                  </span>
                )}
                {announcement.showOnHome && (
                  <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    首页显示
                  </span>
                )}
              </div>
            </div>

            {/* 元信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 text-sm">
              <div className="flex items-center">
                <TagIcon className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-500">分类：</span>
                <span className="ml-1 text-gray-900">{getCategoryDisplay(announcement.category)}</span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-500">优先级：</span>
                <span className={`ml-1 font-medium ${priorityDisplay.color}`}>
                  {priorityDisplay.text}
                </span>
              </div>
              <div className="flex items-center">
                <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-500">目标用户：</span>
                <span className="ml-1 text-gray-900">{getTargetUsersDisplay(announcement.targetUsers)}</span>
              </div>
              <div className="flex items-center">
                <EyeIcon className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-500">查看次数：</span>
                <span className="ml-1 text-gray-900">{announcement.viewCount}</span>
              </div>
            </div>

            {/* 公告内容 */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">公告内容</h3>
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                  {announcement.content}
                </div>
              </div>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">时间信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-500">创建时间：</span>
                  <span className="ml-2 text-gray-900">
                    {new Date(announcement.createdAt).toLocaleString('zh-CN')}
                  </span>
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-500">更新时间：</span>
                  <span className="ml-2 text-gray-900">
                    {new Date(announcement.updatedAt).toLocaleString('zh-CN')}
                  </span>
                </div>
              </div>
              <div className="space-y-4">
                {announcement.publishAt && (
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-500">发布时间：</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(announcement.publishAt).toLocaleString('zh-CN')}
                    </span>
                  </div>
                )}
                {announcement.expireAt && (
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-500">过期时间：</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(announcement.expireAt).toLocaleString('zh-CN')}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 创建者和审核信息 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">创建者和审核信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">创建者信息</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-500">姓名：</span>
                    <span className="ml-2 text-gray-900">
                      {announcement.author.name || '未设置'}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">邮箱：</span>
                    <span className="ml-2 text-gray-900">
                      {announcement.author.email}
                    </span>
                  </div>
                </div>
              </div>

              {announcement.reviewer && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">审核信息</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-500">审核者：</span>
                      <span className="ml-2 text-gray-900">
                        {announcement.reviewer.name || announcement.reviewer.email}
                      </span>
                    </div>
                    {announcement.reviewedAt && (
                      <div>
                        <span className="text-gray-500">审核时间：</span>
                        <span className="ml-2 text-gray-900">
                          {new Date(announcement.reviewedAt).toLocaleString('zh-CN')}
                        </span>
                      </div>
                    )}
                    {announcement.reviewNote && (
                      <div>
                        <span className="text-gray-500">审核备注：</span>
                        <span className="ml-2 text-gray-900">
                          {announcement.reviewNote}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
