'use client'

import { useState } from 'react'

export default function TestTogglePage() {
  const [showNotes, setShowNotes] = useState(false)
  const [showLogs, setShowLogs] = useState(false)
  const [showCreditAdjust, setShowCreditAdjust] = useState(false)

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">测试展开/收起功能</h1>
      
      <div className="space-y-4">
        {/* 测试按钮 */}
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4">控制按钮</h2>
          <div className="space-y-2">
            <button
              onClick={() => setShowNotes(!showNotes)}
              className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              {showNotes ? '隐藏备注' : '显示备注'} (当前: {showNotes ? '显示' : '隐藏'})
            </button>
            
            <button
              onClick={() => setShowLogs(!showLogs)}
              className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              {showLogs ? '隐藏日志' : '显示日志'} (当前: {showLogs ? '显示' : '隐藏'})
            </button>
            
            <button
              onClick={() => setShowCreditAdjust(!showCreditAdjust)}
              className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
            >
              {showCreditAdjust ? '隐藏信用分' : '显示信用分'} (当前: {showCreditAdjust ? '显示' : '隐藏'})
            </button>
          </div>
        </div>

        {/* 备注区域 */}
        {showNotes && (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">管理员备注</h3>
            </div>
            <div className="p-6">
              <p className="text-green-600">✅ 备注区域正常显示！</p>
              <div className="mt-4">
                <textarea
                  placeholder="添加备注..."
                  className="w-full p-3 border border-gray-300 rounded-md resize-none"
                  rows={3}
                />
                <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  添加备注
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 日志区域 */}
        {showLogs && (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">操作日志</h3>
            </div>
            <div className="p-6">
              <p className="text-green-600">✅ 日志区域正常显示！</p>
              <div className="mt-4 space-y-3">
                <div className="p-3 border-l-4 border-blue-500 bg-blue-50">
                  <p className="font-medium">测试操作</p>
                  <div className="mt-1 text-sm text-gray-600">
                    操作者: 测试用户
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date().toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 信用分调整区域 */}
        {showCreditAdjust && (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">调整用户信用分</h3>
            </div>
            <div className="p-6">
              <p className="text-green-600">✅ 信用分调整区域正常显示！</p>
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">选择用户</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">请选择用户</option>
                    <option value="buyer">买家: 测试用户 (当前: 85分)</option>
                    <option value="seller">卖家: 测试卖家 (当前: 90分)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">调整分数</label>
                  <input
                    type="number"
                    min="-50"
                    max="50"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">调整原因</label>
                <textarea
                  placeholder="请输入调整信用分的原因..."
                  className="w-full p-3 border border-gray-300 rounded-md resize-none"
                  rows={3}
                />
              </div>
              <button className="mt-4 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700">
                确认调整
              </button>
            </div>
          </div>
        )}

        {/* 状态显示 */}
        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">当前状态:</h3>
          <ul className="space-y-1 text-sm">
            <li>备注区域: <span className={showNotes ? 'text-green-600' : 'text-red-600'}>{showNotes ? '显示' : '隐藏'}</span></li>
            <li>日志区域: <span className={showLogs ? 'text-green-600' : 'text-red-600'}>{showLogs ? '显示' : '隐藏'}</span></li>
            <li>信用分区域: <span className={showCreditAdjust ? 'text-green-600' : 'text-red-600'}>{showCreditAdjust ? '显示' : '隐藏'}</span></li>
          </ul>
        </div>
      </div>
    </div>
  )
}
