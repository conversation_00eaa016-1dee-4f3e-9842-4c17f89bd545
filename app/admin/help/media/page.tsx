'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  PhotoIcon,
  VideoCameraIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface MediaFile {
  id: string
  filename: string
  storedName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  mediaType: 'image' | 'video'
  metadata?: any
  isUsed: boolean
  status: string
  createdAt: string
  uploader: {
    id: string
    name: string | null
    email: string | null
  }
  article?: {
    id: string
    title: string
  } | null
}

interface MediaStats {
  totalFiles: number
  usedFiles: number
  unusedFiles: number
  orphanedFiles: number
  imageFiles: number
  videoFiles: number
}

export default function MediaManagementPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [files, setFiles] = useState<MediaFile[]>([])
  const [stats, setStats] = useState<MediaStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 筛选和搜索状态
  const [currentPage, setCurrentPage] = useState(1)
  const [typeFilter, setTypeFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // 清理状态
  const [isCleaningUp, setIsCleaningUp] = useState(false)
  const [cleanupResults, setCleanupResults] = useState<any>(null)

  // 检查管理员权限
  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    checkAdminAccess()
  }, [session, status])

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/admin/check')
      if (!response.ok) {
        if (response.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError('权限验证失败')
        }
        setIsLoading(false)
        return
      }
      
      // 权限验证通过，加载数据
      await Promise.all([fetchFiles(), fetchStats()])
    } catch (error) {
      console.error('权限检查失败:', error)
      setError('网络错误')
      setIsLoading(false)
    }
  }

  // 获取媒体文件列表
  const fetchFiles = async (page = 1) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      })

      if (typeFilter !== 'all') params.append('type', typeFilter)
      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (searchQuery.trim()) params.append('search', searchQuery.trim())

      const response = await fetch(`/api/admin/help/media?${params}`)
      if (response.ok) {
        const data = await response.json()
        setFiles(data.files)
        setPagination(data.pagination)
        setCurrentPage(page)
      } else {
        setError('获取媒体文件列表失败')
      }
    } catch (error) {
      console.error('获取媒体文件列表失败:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/help/media/cleanup')
      if (response.ok) {
        const data = await response.json()
        setStats(data.statistics)
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  // 删除文件
  const handleDelete = async (file: MediaFile) => {
    if (!confirm(`确定要删除文件"${file.filename}"吗？此操作不可恢复。`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/help/media/${file.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('文件删除成功')
        fetchFiles(currentPage)
        fetchStats()
      } else {
        const data = await response.json()
        alert(data.error || '删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  // 清理孤立文件
  const handleCleanup = async (dryRun = true) => {
    if (!dryRun && !confirm('确定要执行清理操作吗？这将删除所有孤立的媒体文件，此操作不可恢复。')) {
      return
    }

    try {
      setIsCleaningUp(true)
      const response = await fetch('/api/admin/help/media/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dryRun }),
      })

      if (response.ok) {
        const data = await response.json()
        setCleanupResults(data.results)
        
        if (!dryRun) {
          alert(`清理完成！删除了 ${data.results.summary.totalDeleted} 个文件`)
          fetchFiles(currentPage)
          fetchStats()
        }
      } else {
        const data = await response.json()
        alert(data.error || '清理失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsCleaningUp(false)
    }
  }

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1)
    fetchFiles(1)
  }

  // 重置筛选
  const handleReset = () => {
    setTypeFilter('all')
    setStatusFilter('all')
    setSearchQuery('')
    setCurrentPage(1)
    fetchFiles(1)
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (isLoading && files.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">访问受限</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理首页
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和操作 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">媒体文件管理</h1>
              <p className="mt-1 text-sm text-gray-600">
                管理帮助中心的图片和视频文件
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleCleanup(true)}
                disabled={isCleaningUp}
                className="bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                {isCleaningUp ? '检查中...' : '检查孤立文件'}
              </button>
              <button
                onClick={() => handleCleanup(false)}
                disabled={isCleaningUp}
                className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                {isCleaningUp ? '清理中...' : '清理孤立文件'}
              </button>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-2xl font-bold text-gray-900">{stats.totalFiles}</div>
              <div className="text-sm text-gray-600">总文件数</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-2xl font-bold text-green-600">{stats.usedFiles}</div>
              <div className="text-sm text-gray-600">已使用</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-2xl font-bold text-yellow-600">{stats.unusedFiles}</div>
              <div className="text-sm text-gray-600">未使用</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-2xl font-bold text-red-600">{stats.orphanedFiles}</div>
              <div className="text-sm text-gray-600">孤立文件</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.imageFiles}</div>
              <div className="text-sm text-gray-600">图片</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.videoFiles}</div>
              <div className="text-sm text-gray-600">视频</div>
            </div>
          </div>
        )}

        {/* 清理结果 */}
        {cleanupResults && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">清理结果</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-500">发现孤立文件：</span>
                <span className="text-gray-900 font-medium">{cleanupResults.summary.totalOrphaned} 个</span>
              </div>
              <div>
                <span className="text-gray-500">已删除文件：</span>
                <span className="text-gray-900 font-medium">{cleanupResults.summary.totalDeleted} 个</span>
              </div>
              <div>
                <span className="text-gray-500">错误数量：</span>
                <span className="text-gray-900 font-medium">{cleanupResults.summary.totalErrors} 个</span>
              </div>
            </div>
            {cleanupResults.orphanedFiles.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">孤立文件列表：</h4>
                <div className="max-h-32 overflow-y-auto">
                  {cleanupResults.orphanedFiles.map((file: string, index: number) => (
                    <div key={index} className="text-xs text-gray-600">{file}</div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 筛选和搜索 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* 类型筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文件类型
              </label>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部类型</option>
                <option value="image">图片</option>
                <option value="video">视频</option>
              </select>
            </div>

            {/* 状态筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                使用状态
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部状态</option>
                <option value="used">已使用</option>
                <option value="unused">未使用</option>
                <option value="orphaned">孤立文件</option>
              </select>
            </div>

            {/* 搜索 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索文件名..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-end space-x-2">
              <button
                onClick={handleSearch}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                搜索
              </button>
              <button
                onClick={handleReset}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                重置
              </button>
            </div>
          </div>
        </div>

        {/* 媒体文件列表 */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">加载中...</p>
            </div>
          ) : files.length === 0 ? (
            <div className="p-8 text-center">
              <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无媒体文件</h3>
              <p className="text-gray-600 mb-4">还没有上传任何媒体文件</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 p-6">
                {files.map((file) => (
                  <div
                    key={file.id}
                    className="group relative bg-gray-50 border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    {/* 预览区域 */}
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      {file.mediaType === 'image' ? (
                        <img
                          src={file.fileUrl}
                          alt={file.filename}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex flex-col items-center text-gray-400">
                          <VideoCameraIcon className="h-8 w-8 mb-1" />
                          <span className="text-xs">视频</span>
                        </div>
                      )}
                    </div>

                    {/* 文件信息 */}
                    <div className="p-2">
                      <p className="text-xs font-medium text-gray-900 truncate" title={file.filename}>
                        {file.filename}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.fileSize)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(file.createdAt).toLocaleDateString('zh-CN')}
                      </p>
                    </div>

                    {/* 状态指示器 */}
                    <div className="absolute top-2 left-2">
                      {file.isUsed ? (
                        <span className="bg-green-500 text-white text-xs px-1 py-0.5 rounded">
                          已使用
                        </span>
                      ) : file.status === 'ORPHANED' ? (
                        <span className="bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                          孤立
                        </span>
                      ) : (
                        <span className="bg-yellow-500 text-white text-xs px-1 py-0.5 rounded">
                          未使用
                        </span>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={() => handleDelete(file)}
                        className="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                        title="删除文件"
                      >
                        <TrashIcon className="h-3 w-3" />
                      </button>
                    </div>

                    {/* 关联文章信息 */}
                    {file.article && (
                      <div className="absolute bottom-2 left-2 right-2">
                        <Link
                          href={`/admin/help/${file.article.id}`}
                          className="block bg-black bg-opacity-75 text-white text-xs px-1 py-0.5 rounded truncate hover:bg-opacity-90"
                          title={file.article.title}
                        >
                          {file.article.title}
                        </Link>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 flex justify-between sm:hidden">
                      <button
                        onClick={() => fetchFiles(currentPage - 1)}
                        disabled={currentPage <= 1}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        上一页
                      </button>
                      <button
                        onClick={() => fetchFiles(currentPage + 1)}
                        disabled={currentPage >= pagination.pages}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        下一页
                      </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          显示第 <span className="font-medium">{(currentPage - 1) * pagination.limit + 1}</span> 到{' '}
                          <span className="font-medium">
                            {Math.min(currentPage * pagination.limit, pagination.total)}
                          </span>{' '}
                          条，共 <span className="font-medium">{pagination.total}</span> 条记录
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                          <button
                            onClick={() => fetchFiles(currentPage - 1)}
                            disabled={currentPage <= 1}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            上一页
                          </button>
                          {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                            const page = i + 1
                            return (
                              <button
                                key={page}
                                onClick={() => fetchFiles(page)}
                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                  page === currentPage
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                }`}
                              >
                                {page}
                              </button>
                            )
                          })}
                          <button
                            onClick={() => fetchFiles(currentPage + 1)}
                            disabled={currentPage >= pagination.pages}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            下一页
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
