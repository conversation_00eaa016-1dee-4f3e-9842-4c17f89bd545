'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline'

interface HelpArticle {
  id: string
  title: string
  content: string
  summary: string | null
  category: string
  subcategory: string | null
  tags: string
  keywords: string
  articleType: string
  status: string
  isFeatured: boolean
  sortOrder: number
  difficulty: string
  viewCount: number
  helpfulCount: number
  notHelpfulCount: number
  version: number
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string | null
    email: string | null
  }
  reviewer?: {
    id: string
    name: string | null
    email: string | null
  } | null
  mediaFiles?: Array<{
    id: string
    filename: string
    fileUrl: string
    mediaType: 'image' | 'video'
    fileSize: number
    createdAt: string
  }>
}

export default function HelpArticleDetailPage({ 
  params 
}: { 
  params: Promise<{ id: string }> 
}) {
  const router = useRouter()
  const [articleId, setArticleId] = useState<string>('')
  const [article, setArticle] = useState<HelpArticle | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  // 获取文章ID
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setArticleId(resolvedParams.id)
    }
    getParams()
  }, [params])

  // 加载文章数据
  useEffect(() => {
    if (articleId) {
      fetchArticle()
    }
  }, [articleId])

  const fetchArticle = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/admin/help/${articleId}`)
      
      if (response.ok) {
        const articleData = await response.json()
        setArticle(articleData)
      } else if (response.status === 404) {
        setError('文章不存在')
      } else {
        setError('获取文章信息失败')
      }
    } catch (error) {
      console.error('Error fetching article:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  // 删除文章
  const handleDelete = async () => {
    if (!article) return
    
    if (!confirm(`确定要删除文章"${article.title}"吗？此操作不可恢复。`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/help/${articleId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('文章删除成功')
        router.push('/admin/help')
      } else {
        const data = await response.json()
        alert(data.error || '删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const statusMap = {
      'DRAFT': { text: '草稿', color: 'bg-gray-100 text-gray-800' },
      'PUBLISHED': { text: '已发布', color: 'bg-green-100 text-green-800' },
      'ARCHIVED': { text: '已归档', color: 'bg-yellow-100 text-yellow-800' },
      'DELETED': { text: '已删除', color: 'bg-red-100 text-red-800' }
    }
    return statusMap[status as keyof typeof statusMap] || { text: status, color: 'bg-gray-100 text-gray-800' }
  }

  // 获取分类显示
  const getCategoryDisplay = (category: string) => {
    const categoryMap = {
      'payment': '支付相关',
      'trading': '交易流程',
      'security': '安全提示',
      'account': '账户设置',
      'general': '一般'
    }
    return categoryMap[category as keyof typeof categoryMap] || category
  }

  // 获取文章类型显示
  const getTypeDisplay = (type: string) => {
    const typeMap = {
      'GUIDE': '指南',
      'FAQ': 'FAQ',
      'TUTORIAL': '教程',
      'TIPS': '提示',
      'TROUBLESHOOTING': '故障排除'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  // 获取难度显示
  const getDifficultyDisplay = (difficulty: string) => {
    const difficultyMap = {
      'BEGINNER': { text: '初级', color: 'bg-green-100 text-green-800' },
      'INTERMEDIATE': { text: '中级', color: 'bg-yellow-100 text-yellow-800' },
      'ADVANCED': { text: '高级', color: 'bg-red-100 text-red-800' }
    }
    return difficultyMap[difficulty as keyof typeof difficultyMap] || { text: difficulty, color: 'bg-gray-100 text-gray-800' }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error || !article) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{error || '文章不存在'}</h2>
          <Link
            href="/admin/help"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回帮助管理
          </Link>
        </div>
      </div>
    )
  }

  const statusDisplay = getStatusDisplay(article.status)
  const difficultyDisplay = getDifficultyDisplay(article.difficulty)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link
                href="/admin/help"
                className="mr-4 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">帮助文章详情</h1>
                <p className="mt-1 text-sm text-gray-600">
                  查看和管理帮助文章内容
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Link
                href={`/admin/help/${article.id}/edit`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                编辑
              </Link>
              <button
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                删除
              </button>
            </div>
          </div>
        </div>

        {/* 文章信息 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <h2 className="text-xl font-semibold text-gray-900">{article.title}</h2>
                {article.isFeatured && (
                  <span className="ml-3 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    精选
                  </span>
                )}
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                  {statusDisplay.text}
                </span>
                <span>{getCategoryDisplay(article.category)}</span>
                <span>{getTypeDisplay(article.articleType)}</span>
                <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${difficultyDisplay.color}`}>
                  {difficultyDisplay.text}
                </span>
              </div>

              {article.summary && (
                <p className="text-gray-600 mb-4">{article.summary}</p>
              )}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">查看次数：</span>
              <span className="text-gray-900 font-medium">{article.viewCount}</span>
            </div>
            <div>
              <span className="text-gray-500">有用评价：</span>
              <span className="text-gray-900 font-medium">{article.helpfulCount}</span>
            </div>
            <div>
              <span className="text-gray-500">版本：</span>
              <span className="text-gray-900 font-medium">v{article.version}</span>
            </div>
            <div>
              <span className="text-gray-500">媒体文件：</span>
              <span className="text-gray-900 font-medium">{article.mediaFiles?.length || 0} 个</span>
            </div>
          </div>
        </div>

        {/* 媒体文件 */}
        {article.mediaFiles && article.mediaFiles.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">媒体文件</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {article.mediaFiles.map((file) => (
                <div key={file.id} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="aspect-square bg-gray-100 flex items-center justify-center">
                    {file.mediaType === 'image' ? (
                      <img
                        src={file.fileUrl}
                        alt={file.filename}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <VideoCameraIcon className="h-8 w-8 text-gray-400" />
                    )}
                  </div>
                  <div className="p-2">
                    <p className="text-xs font-medium text-gray-900 truncate" title={file.filename}>
                      {file.filename}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.fileSize)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 文章内容 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">文章内容</h3>
          <div 
            className="prose prose-blue max-w-none"
            dangerouslySetInnerHTML={{ __html: article.content }}
          />
        </div>

        {/* 元数据 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">元数据</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">创建时间：</span>
              <span className="text-gray-900">{new Date(article.createdAt).toLocaleString('zh-CN')}</span>
            </div>
            <div>
              <span className="text-gray-500">更新时间：</span>
              <span className="text-gray-900">{new Date(article.updatedAt).toLocaleString('zh-CN')}</span>
            </div>
            <div>
              <span className="text-gray-500">作者：</span>
              <span className="text-gray-900">{article.author.name || article.author.email}</span>
            </div>
            {article.reviewer && (
              <div>
                <span className="text-gray-500">审核者：</span>
                <span className="text-gray-900">{article.reviewer.name || article.reviewer.email}</span>
              </div>
            )}
            {article.tags && (
              <div className="md:col-span-2">
                <span className="text-gray-500">标签：</span>
                <div className="mt-1">
                  {article.tags.split(',').map((tag, index) => (
                    <span
                      key={index}
                      className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2 mb-1"
                    >
                      {tag.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}
            {article.keywords && (
              <div className="md:col-span-2">
                <span className="text-gray-500">关键词：</span>
                <span className="text-gray-900">{article.keywords}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
