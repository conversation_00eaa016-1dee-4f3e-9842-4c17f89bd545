'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'
import TinyMCEEditor from '@/components/admin/TinyMCEEditor'

interface HelpArticleForm {
  title: string
  content: string
  summary: string
  category: string
  subcategory: string
  tags: string
  keywords: string
  articleType: string
  status: string
  isFeatured: boolean
  sortOrder: number
  difficulty: string
}

export default function CreateHelpArticlePage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [createdArticleId, setCreatedArticleId] = useState<string | null>(null)

  const [formData, setFormData] = useState<HelpArticleForm>({
    title: '',
    content: '',
    summary: '',
    category: 'general',
    subcategory: '',
    tags: '',
    keywords: '',
    articleType: 'GUIDE',
    status: 'DRAFT',
    isFeatured: false,
    sortOrder: 0,
    difficulty: 'BEGINNER'
  })

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空'
    } else if (formData.title.length > 200) {
      newErrors.title = '标题不能超过200个字符'
    }

    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空'
    }

    if (!formData.category) {
      newErrors.category = '请选择分类'
    }

    if (formData.summary && formData.summary.length > 500) {
      newErrors.summary = '摘要不能超过500个字符'
    }

    if (formData.sortOrder < 0) {
      newErrors.sortOrder = '排序权重不能为负数'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/admin/help', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const article = await response.json()
        setCreatedArticleId(article.id)
        alert('帮助文章创建成功！')

        // 如果状态是发布，直接跳转到详情页
        if (formData.status === 'PUBLISHED') {
          router.push(`/admin/help/${article.id}`)
        } else {
          // 如果是草稿，可以继续编辑
          alert('文章已保存为草稿，您可以继续编辑或添加媒体内容')
        }
      } else {
        const data = await response.json()
        alert(data.error || '创建失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  // 预览功能
  const handlePreview = () => {
    if (!formData.title || !formData.content) {
      alert('请先填写标题和内容')
      return
    }

    // 在新窗口中显示预览
    const previewWindow = window.open('', '_blank', 'width=800,height=600')
    if (previewWindow) {
      previewWindow.document.write(`
        <html>
          <head>
            <title>帮助文章预览</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
              .header { border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 20px; }
              .title { font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 10px; }
              .meta { color: #6b7280; font-size: 14px; margin-bottom: 10px; }
              .summary { background: #f3f4f6; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
              .content { white-space: pre-wrap; }
              .tags { margin-top: 20px; }
              .tag { display: inline-block; background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-size: 12px; }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="title">${formData.title}</div>
              <div class="meta">
                分类: ${formData.category} | 类型: ${formData.articleType} | 难度: ${formData.difficulty}
              </div>
            </div>
            ${formData.summary ? `<div class="summary"><strong>摘要：</strong>${formData.summary}</div>` : ''}
            <div class="content">${formData.content}</div>
            ${formData.tags ? `<div class="tags">
              <strong>标签：</strong>
              ${formData.tags.split(',').map(tag => `<span class="tag">${tag.trim()}</span>`).join('')}
            </div>` : ''}
          </body>
        </html>
      `)
      previewWindow.document.close()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center">
            <Link
              href="/admin/help"
              className="mr-4 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">创建帮助文章</h1>
              <p className="mt-1 text-sm text-gray-600">
                创建新的帮助文章，可以选择立即发布或保存为草稿
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            {/* 基本信息 */}
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 border-b pb-2">基本信息</h2>
              
              {/* 标题 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章标题 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="请输入文章标题"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* 摘要 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章摘要
                </label>
                <textarea
                  value={formData.summary}
                  onChange={(e) => setFormData({ ...formData, summary: e.target.value })}
                  rows={3}
                  className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.summary ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="简要描述文章内容（可选）"
                />
                {errors.summary && (
                  <p className="mt-1 text-sm text-red-600">{errors.summary}</p>
                )}
              </div>

              {/* 内容 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章内容 <span className="text-red-500">*</span>
                </label>
                <div className={`${errors.content ? 'border-red-300' : ''}`}>
                  <TinyMCEEditor
                    value={formData.content}
                    onChange={(value) => setFormData({ ...formData, content: value })}
                    placeholder="请输入文章详细内容，支持插入图片和视频"
                    articleId={createdArticleId}
                    height={400}
                    className="border border-gray-300 rounded-md"
                  />
                </div>
                {errors.content && (
                  <p className="mt-1 text-sm text-red-600">{errors.content}</p>
                )}
                <p className="mt-1 text-sm text-gray-500">
                  支持富文本编辑，可以插入图片和视频。创建文章后可以上传媒体文件。
                </p>
              </div>
            </div>
          </div>

          {/* 分类和属性 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 border-b pb-2">分类和属性</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 主分类 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    主分类 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.category ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">请选择分类</option>
                    <option value="payment">支付相关</option>
                    <option value="trading">交易流程</option>
                    <option value="security">安全提示</option>
                    <option value="account">账户设置</option>
                    <option value="general">一般</option>
                  </select>
                  {errors.category && (
                    <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                  )}
                </div>

                {/* 子分类 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    子分类
                  </label>
                  <input
                    type="text"
                    value={formData.subcategory}
                    onChange={(e) => setFormData({ ...formData, subcategory: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="可选的子分类"
                  />
                </div>

                {/* 文章类型 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文章类型
                  </label>
                  <select
                    value={formData.articleType}
                    onChange={(e) => setFormData({ ...formData, articleType: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="GUIDE">指南</option>
                    <option value="FAQ">FAQ</option>
                    <option value="TUTORIAL">教程</option>
                    <option value="TIPS">提示</option>
                    <option value="TROUBLESHOOTING">故障排除</option>
                  </select>
                </div>

                {/* 难度级别 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    难度级别
                  </label>
                  <select
                    value={formData.difficulty}
                    onChange={(e) => setFormData({ ...formData, difficulty: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="BEGINNER">初级</option>
                    <option value="INTERMEDIATE">中级</option>
                    <option value="ADVANCED">高级</option>
                  </select>
                </div>
              </div>

              {/* 标签 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  标签
                </label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="用逗号分隔多个标签，如：币安,支付,USDT"
                />
                <p className="mt-1 text-sm text-gray-500">
                  标签用于帮助用户快速找到相关内容
                </p>
              </div>

              {/* 关键词 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  搜索关键词
                </label>
                <input
                  type="text"
                  value={formData.keywords}
                  onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="用逗号分隔多个关键词，如：哈希值,TxID,交易记录"
                />
                <p className="mt-1 text-sm text-gray-500">
                  关键词用于搜索功能，帮助用户找到这篇文章
                </p>
              </div>
            </div>
          </div>

          {/* 发布设置 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 border-b pb-2">发布设置</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 发布状态 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    发布状态
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="DRAFT">草稿</option>
                    <option value="PUBLISHED">立即发布</option>
                  </select>
                </div>

                {/* 排序权重 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    排序权重
                  </label>
                  <input
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) || 0 })}
                    className={`w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.sortOrder ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0"
                    min="0"
                  />
                  {errors.sortOrder && (
                    <p className="mt-1 text-sm text-red-600">{errors.sortOrder}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    数值越大排序越靠前，默认为0
                  </p>
                </div>
              </div>

              {/* 特殊设置 */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onChange={(e) => setFormData({ ...formData, isFeatured: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
                    设为精选文章
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={handlePreview}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
              >
                预览
              </button>

              <div className="flex space-x-3">
                <Link
                  href="/admin/help"
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
                >
                  取消
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  {isSubmitting ? '创建中...' : '创建文章'}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
