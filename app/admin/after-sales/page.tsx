'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import {
  ExclamationTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

interface AfterSalesRequest {
  id: string
  type: string
  reason: string
  description: string
  requestedAmount?: number
  status: string
  priority: string
  priorityScore: number
  daysSinceCreated: number
  createdAt: string
  lastMessage: any
  unreadMessagesCount: number
  order: {
    orderNumber: string
    totalAmount: number
    status: string
    product: {
      title: string
      images: string
      price: number
    }
  }
  buyer: {
    id: string
    name: string
    email: string
    creditScore: number
  }
  seller: {
    id: string
    name: string
    email: string
    creditScore: number
  }
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function AdminAfterSalesPage() {
  const router = useRouter()
  const [requests, setRequests] = useState<AfterSalesRequest[]>([])
  const [pagination, setPagination] = useState<Pagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState<string | null>(null)
  
  // 筛选条件
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    status: '',
    priority: '',
    page: 1
  })

  useEffect(() => {
    loadAfterSalesRequests()
  }, [filters])

  const loadAfterSalesRequests = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString())
      })

      const response = await fetch(`/api/admin/after-sales?${params}`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests)
        setPagination(data.pagination)
      } else {
        throw new Error('获取售后申请列表失败')
      }
    } catch (error) {
      console.error('加载售后申请列表失败:', error)
      alert('加载售后申请列表失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleQuickAction = async (requestId: string, action: string, reason?: string) => {
    if (!confirm(`确定要${action === 'approve' ? '同意' : '拒绝'}此售后申请吗？`)) {
      return
    }

    setProcessing(requestId)
    
    try {
      const response = await fetch('/api/admin/after-sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          requestId,
          action,
          reason
        })
      })
      
      if (response.ok) {
        alert('操作成功')
        loadAfterSalesRequests() // 重新加载数据
      } else {
        const error = await response.json()
        alert(error.error || '操作失败')
      }
    } catch (error) {
      console.error('操作失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setProcessing(null)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ ...filters, [key]: value, page: 1 })
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const getTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'REFUND': '退款',
      'EXCHANGE': '换货',
      'REPAIR': '维修'
    }
    return typeMap[type] || type
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'APPROVED': '已同意',
      'REJECTED': '已拒绝',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING': 'text-orange-600 bg-orange-100',
      'APPROVED': 'text-green-600 bg-green-100',
      'REJECTED': 'text-red-600 bg-red-100',
      'PROCESSING': 'text-blue-600 bg-blue-100',
      'COMPLETED': 'text-green-600 bg-green-100',
      'CANCELLED': 'text-gray-600 bg-gray-100'
    }
    return colorMap[status] || 'text-gray-600 bg-gray-100'
  }

  const getPriorityColor = (priority: string) => {
    const colorMap: Record<string, string> = {
      'HIGH': 'text-red-600 bg-red-100',
      'MEDIUM': 'text-yellow-600 bg-yellow-100',
      'LOW': 'text-green-600 bg-green-100'
    }
    return colorMap[priority] || 'text-gray-600 bg-gray-100'
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return <ExclamationTriangleIcon className="w-4 h-4" />
      case 'MEDIUM':
        return <ClockIcon className="w-4 h-4" />
      default:
        return <CheckCircleIcon className="w-4 h-4" />
    }
  }

  return (
    <AdminLayout
      title="售后管理"
      subtitle="管理和处理用户售后申请"
    >
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-2xl font-bold text-orange-600">
            {requests.filter(r => r.status === 'PENDING').length}
          </div>
          <div className="text-sm text-gray-600">待处理</div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-2xl font-bold text-blue-600">
            {requests.filter(r => r.status === 'PROCESSING').length}
          </div>
          <div className="text-sm text-gray-600">处理中</div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-2xl font-bold text-red-600">
            {requests.filter(r => r.priority === 'HIGH').length}
          </div>
          <div className="text-sm text-gray-600">高优先级</div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-2xl font-bold text-green-600">
            {requests.filter(r => r.status === 'COMPLETED').length}
          </div>
          <div className="text-sm text-gray-600">已完成</div>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              搜索
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="订单号、用户名..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              申请类型
            </label>
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部类型</option>
              <option value="REFUND">退款</option>
              <option value="EXCHANGE">换货</option>
              <option value="REPAIR">维修</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              申请状态
            </label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部状态</option>
              <option value="PENDING">待处理</option>
              <option value="APPROVED">已同意</option>
              <option value="REJECTED">已拒绝</option>
              <option value="PROCESSING">处理中</option>
              <option value="COMPLETED">已完成</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              优先级
            </label>
            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部优先级</option>
              <option value="HIGH">高优先级</option>
              <option value="MEDIUM">中优先级</option>
              <option value="LOW">低优先级</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({ search: '', type: '', status: '', priority: '', page: 1 })}
              className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md"
            >
              重置筛选
            </button>
          </div>
        </div>
      </div>

      {/* 申请列表 */}
      <div className="bg-white rounded-lg shadow">
        {loading ? (
          <div className="p-8 text-center">
            <div className="text-lg">加载中...</div>
          </div>
        ) : requests.length === 0 ? (
          <div className="p-8 text-center">
            <div className="text-gray-500">暂无售后申请</div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {requests.map((request) => (
              <div key={request.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* 基本信息 */}
                    <div className="flex items-center space-x-4 mb-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        request.type === 'REFUND' ? 'bg-red-100 text-red-800' :
                        request.type === 'EXCHANGE' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {getTypeText(request.type)}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                        {getStatusText(request.status)}
                      </span>
                      <span className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                        {getPriorityIcon(request.priority)}
                        <span className="ml-1">{request.priority}</span>
                      </span>
                      {request.unreadMessagesCount > 0 && (
                        <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs">
                          {request.unreadMessagesCount} 条未读
                        </span>
                      )}
                    </div>

                    {/* 商品和订单信息 */}
                    <div className="flex items-center space-x-4 mb-3">
                      <img
                        src={request.order.product.images?.split(',')[0] || '/placeholder.jpg'}
                        alt={request.order.product.title}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{request.order.product.title}</h4>
                        <div className="text-sm text-gray-600">
                          订单号: {request.order.orderNumber}
                        </div>
                        <div className="text-sm text-gray-600">
                          买家: {request.buyer.name} | 卖家: {request.seller.name}
                        </div>
                      </div>
                    </div>

                    {/* 申请信息 */}
                    <div className="text-sm text-gray-600 mb-2">
                      <strong>申请原因:</strong> {request.reason}
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      {request.description}
                    </div>

                    {/* 金额信息 */}
                    {request.type === 'REFUND' && request.requestedAmount && (
                      <div className="text-sm">
                        <strong>申请退款:</strong> 
                        <span className="text-red-600 font-medium ml-1">¥{request.requestedAmount.toFixed(2)}</span>
                      </div>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex flex-col space-y-2 ml-4">
                    <div className="text-xs text-gray-500 text-right">
                      {request.daysSinceCreated}天前
                    </div>
                    
                    <div className="flex space-x-2">
                      <Link
                        href={`/after-sales/${request.id}/status`}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center"
                      >
                        <EyeIcon className="w-4 h-4 mr-1" />
                        查看
                      </Link>
                      
                      <Link
                        href={`/after-sales/${request.id}/chat`}
                        className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm flex items-center"
                      >
                        <ChatBubbleLeftRightIcon className="w-4 h-4 mr-1" />
                        沟通
                      </Link>

                      {/* 快速操作按钮 */}
                      {request.status === 'PENDING' && (
                        <>
                          <button
                            onClick={() => handleQuickAction(request.id, 'approve')}
                            disabled={processing === request.id}
                            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center"
                          >
                            <CheckCircleIcon className="w-4 h-4 mr-1" />
                            同意
                          </button>
                          <button
                            onClick={() => {
                              const reason = prompt('请输入拒绝原因:')
                              if (reason) {
                                handleQuickAction(request.id, 'reject', reason)
                              }
                            }}
                            disabled={processing === request.id}
                            className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center"
                          >
                            <XCircleIcon className="w-4 h-4 mr-1" />
                            拒绝
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 分页 */}
        {pagination && pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                共 {pagination.total} 条
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrev}
                  className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  上一页
                </button>
                
                <span className="px-3 py-1 text-sm">
                  第 {pagination.page} / {pagination.totalPages} 页
                </span>
                
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNext}
                  className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
