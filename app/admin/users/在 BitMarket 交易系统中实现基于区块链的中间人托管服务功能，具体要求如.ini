在 BitMarket 交易系统中实现基于区块链的中间人托管服务功能，具体要求如下：

## 功能范围和触发条件
- 为超过 100 USDT 的商品交易提供可选的中间人托管服务
- 集成到现有的 USDT 支付流程中，作为可选的安全保障机制
- 与现有的订单管理、聊天系统和用户认证系统无缝集成
中间人担保的订单金额一定小于中间人的冻结和担保保证金的总和
## 中间人系统设计
1. **中间人注册和认证**
   - 中间人需要上传并验证其 BNB Chain 钱包地址
   - 建立中间人信誉评级和历史记录系统
   - 设置中间人服务费率（由中间人自定义，平台抽中间人自定义手续费费用的30%，中间人手续费不能低于1%，不得高于30%）

2. **托管交易流程**
   - 买家选择中间人托管服务并支付（商品金额 + 中间人手续费）
   - 资金转入指定中间人的 BNB Chain 地址
   - 中间人通知卖家发货并跟踪物流状态
   - 买家确认收货后，中间人将款项转给卖家
   - 平台从中间人手续费中抽取一定比例作为平台费用，由扣除中间人保证金实现

## 聊天系统集成
3. **多方聊天室**
   - 为每个托管订单创建包含四方的专用聊天室：
     * 平台管理员
     * 中间人
     * 买方
     * 卖方
   - 所有交易沟通和状态更新都在此聊天室中进行
   - 聊天记录作为争议处理的重要证据

## 争议处理和仲裁机制
4. **举报和申诉系统**
   - 在 /radar 页面实现举报功能
   - 买方或卖方可以提交：
     * 订单号
     * 相关聊天记录截图/导出
     * 争议描述和证据
   - 管理员可以查看完整的交易历史和聊天记录

5. **中间人仲裁投票系统**
   - 建立中间人委员会，由经验丰富的中间人组成
   - 对争议案例进行投票表决
   - 投票结果决定资金分配和责任认定
   - 实现多数决原则的自动化执行

## 激励机制
6. **中间人奖励系统**
   - 参与正常仲裁投票的中间人获得奖励
   - 奖励形式：7天有效期的 10 USDT 免手续费提现券
   - 限制：每个中间人每月最多获得一张奖励券，同时修改提现页面，支持使用和兑换提现卷或礼品卡，管理员后台可以发行停用礼品卡和提现卷
   - 建立积分系统跟踪中间人的参与度和准确性

## 技术实现要求
7. **数据库设计**
   - 扩展订单表添加托管相关字段
   - 创建中间人信息表和认证状态
   - 建立托管交易记录表
   - 添加仲裁投票记录表
   - 创建奖励券管理表

8. **区块链集成**
   - 集成 BNB Chain 钱包连接功能
   - 实现链上交易验证和监控
   - 添加智能合约交互

9. **用户界面组件**
   - 商品页面的托管服务选择界面
   - 中间人注册和管理面板
   - 托管订单状态跟踪页面
   - 争议举报页面（/radar）
   - 仲裁投票界面
   - 奖励券管理页面

10. **安全和合规**
    - 实现资金流向监控和异常检测
    - 添加反洗钱（AML）基础检查
    - 确保用户资金安全和隔离
    - 建立完整的审计日志系统

## 集成要求
- 与现有的 USDT 支付系统无缝对接
- 扩展现有的聊天模块支持多方对话
- 集成到订单管理流程中
- 保持与现有用户认证系统的兼容性
- 添加相应的测试用例和文档

请确保所有功能都符合相关金融法规要求，并提供完整的用户操作指南和管理员手册。