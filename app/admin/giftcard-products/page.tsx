'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  EyeIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface GiftCardProduct {
  id: string
  name: string
  description?: string
  productType: string
  faceValue: number
  salePrice: number
  stock: number
  isActive: boolean
  features?: any
  terms?: string
  validDays: number
  supportedPayments?: string[]
  createdAt: string
  updatedAt: string
  createdBy: {
    id: string
    name: string
  }
  _count?: {
    giftCards: number
    orders: number
  }
}

export default function GiftCardProductsPage() {
  const [products, setProducts] = useState<GiftCardProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<GiftCardProduct | null>(null)
  const [creating, setCreating] = useState(false)
  const [updating, setUpdating] = useState(false)

  const [productForm, setProductForm] = useState({
    name: '',
    description: '',
    productType: 'GIFT_CARD',
    faceValue: 10,
    salePrice: 10,
    stock: 100,
    isActive: true,
    terms: '',
    validDays: 365,
    supportedPayments: ['BALANCE']
  })

  const productTypes = [
    { value: 'GIFT_CARD', label: '礼品卡', description: '可兑换现金的礼品卡' },
    { value: 'RECHARGE_CARD', label: '充值卡', description: '直接充值到账户余额' }
  ]

  const paymentMethods = [
    { value: 'BALANCE', label: '账户余额', description: '使用平台保证金购买' },
    { value: 'BINANCE_PAY', label: '币安支付', description: '使用币安支付' },
    { value: 'CRYPTO', label: '加密货币', description: '直接转账加密货币' }
  ]

  // 获取商品列表
  const fetchProducts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/giftcard-products')
      const result = await response.json()

      if (response.ok && result.success) {
        setProducts(result.data)
      } else {
        alert(result.error || '获取商品列表失败')
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  // 创建商品
  const handleCreateProduct = async () => {
    try {
      setCreating(true)

      const response = await fetch('/api/admin/giftcard-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productForm)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert('商品创建成功')
        setShowCreateModal(false)
        resetForm()
        fetchProducts()
      } else {
        alert(result.error || '创建商品失败')
      }
    } catch (error) {
      console.error('创建商品失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setCreating(false)
    }
  }

  // 更新商品
  const handleUpdateProduct = async () => {
    if (!selectedProduct) return

    try {
      setUpdating(true)

      const response = await fetch(`/api/admin/giftcard-products/${selectedProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productForm)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert('商品更新成功')
        setShowEditModal(false)
        setSelectedProduct(null)
        resetForm()
        fetchProducts()
      } else {
        alert(result.error || '更新商品失败')
      }
    } catch (error) {
      console.error('更新商品失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setUpdating(false)
    }
  }

  // 删除商品
  const handleDeleteProduct = async (product: GiftCardProduct) => {
    if (!confirm(`确定要删除商品"${product.name}"吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/giftcard-products/${product.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert('商品删除成功')
        fetchProducts()
      } else {
        alert(result.error || '删除商品失败')
      }
    } catch (error) {
      console.error('删除商品失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  // 切换商品状态
  const toggleProductStatus = async (product: GiftCardProduct) => {
    try {
      const response = await fetch(`/api/admin/giftcard-products/${product.id}/toggle`, {
        method: 'POST'
      })

      const result = await response.json()

      if (response.ok && result.success) {
        fetchProducts()
      } else {
        alert(result.error || '切换状态失败')
      }
    } catch (error) {
      console.error('切换状态失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  // 重置表单
  const resetForm = () => {
    setProductForm({
      name: '',
      description: '',
      productType: 'GIFT_CARD',
      faceValue: 10,
      salePrice: 10,
      stock: 100,
      isActive: true,
      terms: '',
      validDays: 365,
      supportedPayments: ['BALANCE']
    })
  }

  // 编辑商品
  const editProduct = (product: GiftCardProduct) => {
    setSelectedProduct(product)
    setProductForm({
      name: product.name,
      description: product.description || '',
      productType: product.productType,
      faceValue: product.faceValue,
      salePrice: product.salePrice,
      stock: product.stock,
      isActive: product.isActive,
      terms: product.terms || '',
      validDays: product.validDays,
      supportedPayments: product.supportedPayments || ['BALANCE']
    })
    setShowEditModal(true)
  }

  const getProductTypeText = (type: string) => {
    const typeObj = productTypes.find(t => t.value === type)
    return typeObj ? typeObj.label : type
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'text-green-600' : 'text-red-600'
  }

  const getStatusText = (isActive: boolean) => {
    return isActive ? '上架中' : '已下架'
  }

  if (loading) {
    return (
      <AdminLayout title="礼品卡商品管理">
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">加载中...</div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout title="礼品卡商品管理">
      <div className="space-y-6">
        {/* 头部操作 */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">礼品卡商品管理</h2>
            <p className="text-sm text-gray-600 mt-1">管理礼品卡和充值卡商品</p>
          </div>
          <button
            onClick={() => {
              resetForm()
              setShowCreateModal(true)
            }}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            创建商品
          </button>
        </div>

        {/* 商品列表 */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商品信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  价格
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  库存
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  统计
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      {product.description && (
                        <div className="text-sm text-gray-500">{product.description}</div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {getProductTypeText(product.productType)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      面值: {product.faceValue} USDT
                    </div>
                    <div className="text-sm text-gray-500">
                      售价: {product.salePrice} USDT
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{product.stock}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${getStatusColor(product.isActive)}`}>
                      {getStatusText(product.isActive)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>卡片: {product._count?.giftCards || 0}</div>
                    <div>订单: {product._count?.orders || 0}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => editProduct(product)}
                        className="text-blue-600 hover:text-blue-900"
                        title="编辑"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => toggleProductStatus(product)}
                        className={product.isActive ? "text-orange-600 hover:text-orange-900" : "text-green-600 hover:text-green-900"}
                        title={product.isActive ? "下架" : "上架"}
                      >
                        {product.isActive ? (
                          <ExclamationTriangleIcon className="h-4 w-4" />
                        ) : (
                          <CheckIcon className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product)}
                        className="text-red-600 hover:text-red-900"
                        title="删除"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {products.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">暂无商品</div>
            </div>
          )}
        </div>

        {/* 创建商品模态框 */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">创建商品</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">商品名称 *</label>
                    <input
                      type="text"
                      value={productForm.name}
                      onChange={(e) => setProductForm(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="输入商品名称"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">商品类型 *</label>
                    <select
                      value={productForm.productType}
                      onChange={(e) => setProductForm(prev => ({ ...prev, productType: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {productTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">商品描述</label>
                  <textarea
                    value={productForm.description}
                    onChange={(e) => setProductForm(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="输入商品描述"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">面值 (USDT) *</label>
                    <input
                      type="number"
                      value={productForm.faceValue}
                      onChange={(e) => setProductForm(prev => ({ ...prev, faceValue: parseFloat(e.target.value) || 0 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">售价 (USDT) *</label>
                    <input
                      type="number"
                      value={productForm.salePrice}
                      onChange={(e) => setProductForm(prev => ({ ...prev, salePrice: parseFloat(e.target.value) || 0 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">库存数量 *</label>
                    <input
                      type="number"
                      value={productForm.stock}
                      onChange={(e) => setProductForm(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">有效期 (天) *</label>
                    <input
                      type="number"
                      value={productForm.validDays}
                      onChange={(e) => setProductForm(prev => ({ ...prev, validDays: parseInt(e.target.value) || 365 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">商品状态</label>
                    <select
                      value={productForm.isActive ? 'true' : 'false'}
                      onChange={(e) => setProductForm(prev => ({ ...prev, isActive: e.target.value === 'true' }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="true">上架</option>
                      <option value="false">下架</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">支持的支付方式</label>
                  <div className="space-y-2">
                    {paymentMethods.map(method => (
                      <label key={method.value} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={productForm.supportedPayments.includes(method.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProductForm(prev => ({
                                ...prev,
                                supportedPayments: [...prev.supportedPayments, method.value]
                              }))
                            } else {
                              setProductForm(prev => ({
                                ...prev,
                                supportedPayments: prev.supportedPayments.filter(p => p !== method.value)
                              }))
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{method.label}</span>
                        <span className="text-xs text-gray-500 ml-2">({method.description})</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">使用条款</label>
                  <textarea
                    value={productForm.terms}
                    onChange={(e) => setProductForm(prev => ({ ...prev, terms: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="输入使用条款"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                >
                  取消
                </button>
                <button
                  onClick={handleCreateProduct}
                  disabled={creating || !productForm.name || !productForm.faceValue || !productForm.salePrice}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {creating ? '创建中...' : '创建商品'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
