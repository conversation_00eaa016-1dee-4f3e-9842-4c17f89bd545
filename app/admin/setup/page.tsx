'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'

interface AdminStatus {
  hasAdmin: boolean
  adminCount: number
  totalUsers: number
  canCreateAdmin: boolean
}

interface CreateAdminResponse {
  success: boolean
  message: string
  data?: any
  verification?: {
    passwordTest: boolean
    roleVerified: boolean
    emailVerified: boolean
  }
  error?: string
}

export default function AdminSetupPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [adminStatus, setAdminStatus] = useState<AdminStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [success, setSuccess] = useState<string>('')

  // 获取管理员状态
  useEffect(() => {
    fetchAdminStatus()
  }, [])

  const fetchAdminStatus = async () => {
    try {
      const response = await fetch('/api/admin/create')
      const data = await response.json()
      setAdminStatus(data)
    } catch (error) {
      console.error('获取管理员状态失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.email) {
      newErrors.email = '邮箱不能为空'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '邮箱格式不正确'
    }

    if (!formData.password) {
      newErrors.password = '密码不能为空'
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少需要6位字符'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 创建管理员
  const handleCreateAdmin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setCreating(true)
    setErrors({})
    setSuccess('')

    try {
      const response = await fetch('/api/admin/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data: CreateAdminResponse = await response.json()

      if (response.ok && data.success) {
        setSuccess(data.message)
        setFormData({ email: '', password: '', name: '' })
        
        // 刷新管理员状态
        await fetchAdminStatus()
        
        // 3秒后跳转到登录页面
        setTimeout(() => {
          router.push('/auth/signin')
        }, 3000)
      } else {
        setErrors({ submit: data.error || '创建管理员失败' })
      }
    } catch (error) {
      setErrors({ submit: '网络错误，请稍后重试' })
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">正在加载...</p>
        </div>
      </div>
    )
  }

  // 如果已有管理员且当前用户不是管理员，显示提示
  if (adminStatus?.hasAdmin && (!session || session.user.role !== 'ADMIN')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="mb-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
              <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">系统已有管理员</h2>
          <p className="text-gray-600 mb-6">
            系统已经存在 {adminStatus.adminCount} 个管理员账号。
            只有现有管理员可以创建新的管理员账号。
          </p>
          <button
            onClick={() => router.push('/auth/signin')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200"
          >
            前往登录
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900">创建管理员账号</h1>
          <p className="mt-2 text-gray-600">
            {adminStatus?.hasAdmin ? '创建新的管理员账号' : '设置系统第一个管理员账号'}
          </p>
        </div>

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-800">{success}</p>
                <p className="text-xs text-green-600 mt-1">3秒后自动跳转到登录页面...</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleCreateAdmin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              邮箱地址
            </label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              密码
            </label>
            <input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="至少6位字符"
            />
            {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
          </div>

          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              姓名 (可选)
            </label>
            <input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="系统管理员"
            />
          </div>

          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-800">{errors.submit}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={creating}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {creating ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                创建中...
              </>
            ) : (
              '创建管理员账号'
            )}
          </button>
        </form>

        {adminStatus && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="text-sm text-gray-500 space-y-1">
              <p>系统状态:</p>
              <p>• 管理员数量: {adminStatus.adminCount}</p>
              <p>• 总用户数: {adminStatus.totalUsers}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
