'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'

export default function AdminDisputes() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [disputes, setDisputes] = useState([])
  const [pagination, setPagination] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user?.role !== 'ADMIN') {
      router.push('/')
      return
    }

    fetchDisputes()
  }, [session, status, router])

  const fetchDisputes = async () => {
    try {
      const response = await fetch('/api/admin/disputes')
      
      if (response.ok) {
        const data = await response.json()
        setDisputes(data.disputes || [])
        setPagination(data.pagination || null)
      } else {
        setError('获取争议列表失败')
      }
    } catch (error) {
      console.error('获取争议列表错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <AdminLayout 
      title="纠纷处理" 
      subtitle={`管理和处理平台争议案例，共 ${pagination?.total || 0} 个纠纷案例`}
    >
      <div className="space-y-6">
        {/* 搜索栏 */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex gap-4">
            <input
              type="text"
              placeholder="搜索订单ID、商品标题或用户..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium">
              搜索
            </button>
          </div>
        </div>

        {/* 争议列表 */}
        <div className="space-y-4">
          {disputes.length === 0 ? (
            <div className="bg-white shadow rounded-lg p-8 text-center">
              <p className="text-gray-500 text-lg">暂无纠纷需要处理</p>
            </div>
          ) : (
            disputes.map((dispute) => (
              <div key={dispute.id} className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 订单信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">订单信息</h3>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">订单ID:</span> {dispute.id?.slice(-8) || 'N/A'}</div>
                      <div><span className="font-medium">商品:</span> {dispute.product?.title || 'N/A'}</div>
                      <div><span className="font-medium">数量:</span> {dispute.quantity || 0}</div>
                      <div><span className="font-medium">总金额:</span> {dispute.totalAmount || 0} USDT</div>
                      <div><span className="font-medium">创建时间:</span> {new Date(dispute.createdAt).toLocaleDateString()}</div>
                    </div>
                  </div>

                  {/* 当事人信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">当事人信息</h3>
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm font-medium text-gray-700">买家</div>
                        <div className="text-sm text-gray-600">
                          {dispute.buyer?.name || '未设置'} ({dispute.buyer?.email || 'N/A'})
                        </div>
                        <div className="text-xs text-gray-500">
                          信用分: {dispute.buyer?.creditScore || 0}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-700">卖家</div>
                        <div className="text-sm text-gray-600">
                          {dispute.seller?.name || '未设置'} ({dispute.seller?.email || 'N/A'})
                        </div>
                        <div className="text-xs text-gray-500">
                          信用分: {dispute.seller?.creditScore || 0}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 处理操作 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">处理操作</h3>
                    <div className="space-y-2">
                      <button className="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm">
                        支持买家
                      </button>
                      <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm">
                        支持卖家
                      </button>
                      <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm">
                        部分退款
                      </button>
                      <button className="w-full bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm">
                        要求更多信息
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 分页 */}
        {pagination && pagination.pages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
            <div className="flex-1 flex justify-between sm:hidden">
              <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
              </button>
              <button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">1</span> 到 <span className="font-medium">{disputes.length}</span> 条，
                  共 <span className="font-medium">{pagination.total}</span> 条记录
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    上一页
                  </button>
                  <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    下一页
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
