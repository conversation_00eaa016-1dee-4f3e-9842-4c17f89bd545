"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  ExclamationTriangleIcon,
  EyeIcon,
  UserIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowPathIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  DocumentArrowDownIcon,
  ChatBubbleLeftRightIcon,
  ShieldCheckIcon,
  BellIcon
} from '@heroicons/react/24/outline'
import AdminLayout from '@/components/admin/AdminLayout'

interface Dispute {
  id: string
  disputeType: string
  description: string
  status: string
  priority: string
  reportedAt: string
  resolvedAt?: string
  evidence: any[]
  order: {
    id: string
    orderNumber: string
    totalAmount: number
    status: string
    useEscrow: boolean
    product: {
      title: string
      images: string[]
    }
    buyer: {
      id: string
      name: string
    }
    seller: {
      id: string
      name: string
    }
    mediator?: {
      id: string
      name: string
    }
  }
  reporter: {
    id: string
    name: string
  }
  assignedTo?: {
    id: string
    name: string
  }
  adminResponse?: string
  resolutionNotes?: string
}

interface DisputeStats {
  total: number
  pending: number
  investigating: number
  resolved: number
  rejected: number
  highPriority: number
}

export default function EnhancedAdminDisputesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [disputes, setDisputes] = useState<Dispute[]>([])
  const [stats, setStats] = useState<DisputeStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'investigating' | 'resolved'>('all')
  
  // 筛选和搜索
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    disputeType: '',
    search: '',
    assignedTo: ''
  })

  const disputeTypes = [
    { value: 'PRODUCT_QUALITY', label: '商品质量问题' },
    { value: 'DELIVERY_ISSUE', label: '物流配送问题' },
    { value: 'SELLER_FRAUD', label: '卖家欺诈' },
    { value: 'BUYER_FRAUD', label: '买家恶意' },
    { value: 'PAYMENT_ISSUE', label: '支付问题' },
    { value: 'COMMUNICATION_ISSUE', label: '沟通问题' },
    { value: 'MEDIATOR_ISSUE', label: '中间人问题' },
    { value: 'PLATFORM_ISSUE', label: '平台问题' },
    { value: 'OTHER', label: '其他问题' }
  ]

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/auth/signin')
      return
    }

    fetchDisputes()
    fetchDisputeStats()
  }, [session, status, activeTab])

  const fetchDisputes = async () => {
    try {
      setLoading(true)
      setError('')

      const queryParams = new URLSearchParams()
      if (activeTab !== 'all') queryParams.append('status', activeTab.toUpperCase())
      if (filters.priority) queryParams.append('priority', filters.priority)
      if (filters.disputeType) queryParams.append('disputeType', filters.disputeType)
      if (filters.search) queryParams.append('search', filters.search)
      if (filters.assignedTo) queryParams.append('assignedTo', filters.assignedTo)

      const response = await fetch(`/api/admin/disputes?${queryParams.toString()}`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setDisputes(result.data.disputes)
        } else {
          setError(result.error || '获取争议列表失败')
        }
      } else {
        setError('获取争议列表失败')
      }
    } catch (error) {
      console.error('获取争议列表失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const fetchDisputeStats = async () => {
    try {
      const response = await fetch('/api/admin/disputes/stats')
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setStats(result.data)
        }
      }
    } catch (error) {
      console.error('获取争议统计失败:', error)
    }
  }

  const handleDisputeAction = async (disputeId: string, action: string, data?: any) => {
    try {
      setActionLoading(true)
      
      const response = await fetch(`/api/admin/disputes/${disputeId}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, data })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(result.message)
        fetchDisputes() // 刷新列表
        fetchDisputeStats() // 刷新统计
        setShowModal(false)
        setSelectedDispute(null)
      } else {
        alert(result.error || '操作失败')
      }
    } catch (error) {
      console.error('争议操作失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setActionLoading(false)
    }
  }

  const openEscrowChat = (orderId: string) => {
    window.open(`/admin/orders/${orderId}/chat`, '_blank')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'INVESTIGATING':
        return 'bg-blue-100 text-blue-800'
      case 'RESOLVED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800'
      case 'LOW':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'INVESTIGATING': '调查中',
      'RESOLVED': '已解决',
      'REJECTED': '已拒绝'
    }
    return statusMap[status] || status
  }

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      'HIGH': '高',
      'MEDIUM': '中',
      'LOW': '低'
    }
    return priorityMap[priority] || priority
  }

  const getDisputeTypeText = (type: string) => {
    const typeObj = disputeTypes.find(t => t.value === type)
    return typeObj?.label || type
  }

  const exportDisputeReport = async (disputeId: string) => {
    try {
      const response = await fetch(`/api/admin/disputes/${disputeId}/export`, {
        method: 'POST'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `dispute-report-${disputeId}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const result = await response.json()
        alert(result.error || '导出失败')
      }
    } catch (error) {
      console.error('导出争议报告失败:', error)
      alert('导出失败，请稍后重试')
    }
  }

  const getTabCount = (tab: string) => {
    if (!stats) return 0
    switch (tab) {
      case 'all': return stats.total
      case 'pending': return stats.pending
      case 'investigating': return stats.investigating
      case 'resolved': return stats.resolved
      default: return 0
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题和统计 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">争议管理中心</h1>
            <p className="text-gray-600">处理用户提交的争议举报，维护平台交易秩序</p>
          </div>
          <div className="flex items-center space-x-4">
            {stats && stats.highPriority > 0 && (
              <div className="flex items-center px-3 py-2 bg-red-100 text-red-800 rounded-md">
                <BellIcon className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">{stats.highPriority} 个高优先级</span>
              </div>
            )}
            <button
              onClick={() => {
                fetchDisputes()
                fetchDisputeStats()
              }}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              刷新
            </button>
          </div>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-500">总争议数</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <div className="text-sm text-gray-500">待处理</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-blue-600">{stats.investigating}</div>
              <div className="text-sm text-gray-500">调查中</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
              <div className="text-sm text-gray-500">已解决</div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
              <div className="text-sm text-gray-500">已拒绝</div>
            </div>
          </div>
        )}

        {/* 标签页导航 */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all', name: '全部争议' },
              { id: 'pending', name: '待处理' },
              { id: 'investigating', name: '调查中' },
              { id: 'resolved', name: '已解决' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
                {stats && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {getTabCount(tab.id)}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* 筛选器 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
              <select
                value={filters.priority}
                onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部优先级</option>
                <option value="HIGH">高</option>
                <option value="MEDIUM">中</option>
                <option value="LOW">低</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">争议类型</label>
              <select
                value={filters.disputeType}
                onChange={(e) => setFilters(prev => ({ ...prev, disputeType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部类型</option>
                {disputeTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">处理人</label>
              <select
                value={filters.assignedTo}
                onChange={(e) => setFilters(prev => ({ ...prev, assignedTo: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部处理人</option>
                <option value="unassigned">未分配</option>
                <option value="self">我处理的</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">搜索</label>
              <div className="relative">
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  placeholder="订单号、用户名..."
                  className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <button
              onClick={fetchDisputes}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              应用筛选
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 争议列表 */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-20 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          ) : disputes.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      争议信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      订单信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态/优先级
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      处理人
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      举报时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {disputes.map((dispute) => (
                    <tr key={dispute.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {getDisputeTypeText(dispute.disputeType)}
                          </div>
                          <div className="text-sm text-gray-500">
                            举报人: {dispute.reporter.name}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {dispute.description.substring(0, 50)}...
                          </div>
                          {dispute.evidence && dispute.evidence.length > 0 && (
                            <div className="text-xs text-blue-600">
                              {dispute.evidence.length} 个证据文件
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {dispute.order.orderNumber}
                            {dispute.order.useEscrow && (
                              <ShieldCheckIcon className="inline h-4 w-4 ml-1 text-blue-500" />
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {dispute.order.product.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            {dispute.order.totalAmount} USDT
                          </div>
                          <div className="text-xs text-gray-400">
                            买家: {dispute.order.buyer.name} | 卖家: {dispute.order.seller.name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(dispute.status)}`}>
                            {getStatusText(dispute.status)}
                          </span>
                          <br />
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(dispute.priority)}`}>
                            {getPriorityText(dispute.priority)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {dispute.assignedTo ? dispute.assignedTo.name : '未分配'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(dispute.reportedAt).toLocaleDateString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedDispute(dispute)
                              setShowModal(true)
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {dispute.order.useEscrow && (
                            <button
                              onClick={() => openEscrowChat(dispute.order.id)}
                              className="text-purple-600 hover:text-purple-900"
                              title="查看聊天记录"
                            >
                              <ChatBubbleLeftRightIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => exportDisputeReport(dispute.id)}
                            className="text-green-600 hover:text-green-900"
                            title="导出报告"
                          >
                            <DocumentArrowDownIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-300" />
              <p className="mt-2 text-sm text-gray-500">暂无争议记录</p>
            </div>
          )}
        </div>

        {/* 争议详情模态框 */}
        {showModal && selectedDispute && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 shadow-lg rounded-md bg-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">争议详情处理</h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-96 overflow-y-auto">
                {/* 左侧：争议信息 */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">争议信息</h4>
                    <div className="bg-gray-50 p-3 rounded">
                      <p><strong>类型:</strong> {getDisputeTypeText(selectedDispute.disputeType)}</p>
                      <p><strong>优先级:</strong> <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(selectedDispute.priority)}`}>{getPriorityText(selectedDispute.priority)}</span></p>
                      <p><strong>状态:</strong> <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedDispute.status)}`}>{getStatusText(selectedDispute.status)}</span></p>
                      <p><strong>举报人:</strong> {selectedDispute.reporter.name}</p>
                      <p><strong>举报时间:</strong> {new Date(selectedDispute.reportedAt).toLocaleString('zh-CN')}</p>
                      {selectedDispute.assignedTo && (
                        <p><strong>处理人:</strong> {selectedDispute.assignedTo.name}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">问题描述</h4>
                    <div className="bg-gray-50 p-3 rounded">
                      <p className="text-sm">{selectedDispute.description}</p>
                    </div>
                  </div>

                  {/* 证据文件 */}
                  {selectedDispute.evidence && selectedDispute.evidence.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">证据文件</h4>
                      <div className="space-y-2">
                        {selectedDispute.evidence.map((evidence: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm">{evidence.name}</span>
                            <a
                              href={evidence.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                              查看
                            </a>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* 右侧：订单信息 */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">相关订单</h4>
                    <div className="bg-gray-50 p-3 rounded">
                      <p><strong>订单号:</strong> {selectedDispute.order.orderNumber}</p>
                      <p><strong>商品:</strong> {selectedDispute.order.product.title}</p>
                      <p><strong>金额:</strong> {selectedDispute.order.totalAmount} USDT</p>
                      <p><strong>订单状态:</strong> {selectedDispute.order.status}</p>
                      <p><strong>托管订单:</strong> {selectedDispute.order.useEscrow ? '是' : '否'}</p>
                      <p><strong>买家:</strong> {selectedDispute.order.buyer.name}</p>
                      <p><strong>卖家:</strong> {selectedDispute.order.seller.name}</p>
                      {selectedDispute.order.mediator && (
                        <p><strong>中间人:</strong> {selectedDispute.order.mediator.name}</p>
                      )}
                    </div>
                  </div>

                  {selectedDispute.adminResponse && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">管理员回复</h4>
                      <div className="bg-blue-50 p-3 rounded">
                        <p className="text-sm">{selectedDispute.adminResponse}</p>
                      </div>
                    </div>
                  )}

                  {selectedDispute.resolutionNotes && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">解决方案</h4>
                      <div className="bg-green-50 p-3 rounded">
                        <p className="text-sm">{selectedDispute.resolutionNotes}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="mt-6 flex justify-between">
                <div className="flex space-x-2">
                  {selectedDispute.order.useEscrow && (
                    <button
                      onClick={() => openEscrowChat(selectedDispute.order.id)}
                      className="flex items-center px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                    >
                      <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                      查看聊天
                    </button>
                  )}
                  <button
                    onClick={() => exportDisputeReport(selectedDispute.id)}
                    className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                    导出报告
                  </button>
                </div>

                <div className="flex space-x-3">
                  {selectedDispute.status === 'PENDING' && (
                    <>
                      <button
                        onClick={() => handleDisputeAction(selectedDispute.id, 'assign_to_self')}
                        disabled={actionLoading}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                      >
                        接受处理
                      </button>
                      <button
                        onClick={() => {
                          const reason = prompt('请输入拒绝原因:')
                          if (reason) {
                            handleDisputeAction(selectedDispute.id, 'reject', { reason })
                          }
                        }}
                        disabled={actionLoading}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                      >
                        拒绝处理
                      </button>
                    </>
                  )}

                  {selectedDispute.status === 'INVESTIGATING' && (
                    <button
                      onClick={() => {
                        const resolution = prompt('请输入解决方案:')
                        if (resolution) {
                          handleDisputeAction(selectedDispute.id, 'resolve', { resolution })
                        }
                      }}
                      disabled={actionLoading}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                    >
                      标记为已解决
                    </button>
                  )}

                  <button
                    onClick={() => setShowModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
