'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface EscrowPayment {
  id: string
  amount: number
  currency: string
  status: string
  paymentMethod: string
  txHash: string | null
  platformFee: number
  adminNotes: string | null
  fundedAt: Date | null
  releasedAt: Date | null
  refundedAt: Date | null
  createdAt: Date
  order: {
    id: string
    orderNumber: string
    status: string
    product: {
      id: string
      title: string
      price: number
    }
    buyer: {
      id: string
      userId: string | null
      name: string | null
      email: string | null
      creditScore: number
    }
    seller: {
      id: string
      userId: string | null
      name: string | null
      email: string | null
      creditScore: number
    }
  }
}

interface EscrowResponse {
  escrowPayments: EscrowPayment[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminEscrow() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [escrowPayments, setEscrowPayments] = useState<EscrowPayment[]>([])
  const [pagination, setPagination] = useState<EscrowResponse['pagination'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'loading') return
    checkAdminAccess()
  }, [session, status, currentPage, searchTerm, statusFilter])

  const checkAdminAccess = async () => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })
      
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)

      const response = await fetch(`/api/admin/escrow?${params}`)
      
      if (response.status === 401) {
        router.push('/auth/signin')
        return
      } else if (response.status === 403) {
        setError('您没有管理员权限')
      } else if (response.ok) {
        const data: EscrowResponse = await response.json()
        setEscrowPayments(data.escrowPayments)
        setPagination(data.pagination)
      } else {
        setError('获取托管记录失败')
      }
    } catch (error) {
      console.error('托管管理页面错误:', error)
      setError('网络错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEscrowAction = async (escrowId: string, action: string, data?: any) => {
    if (!confirm(`确定要执行此操作吗？`)) {
      return
    }

    setActionLoading(escrowId)
    try {
      const response = await fetch('/api/admin/escrow', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          escrowId,
          action,
          data
        })
      })

      if (response.ok) {
        // 刷新托管记录列表
        checkAdminAccess()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败')
      }
    } catch (error) {
      console.error('托管操作错误:', error)
      alert('网络错误')
    } finally {
      setActionLoading(null)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    checkAdminAccess()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} USDT`
  }

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'PENDING': '待确认',
      'FUNDED': '已到账',
      'RELEASED': '已释放',
      'REFUNDED': '已退款',
      'DISPUTED': '有纠纷'
    }
    return statusMap[status] || status
  }

  const getStatusBadgeColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'FUNDED': 'bg-blue-100 text-blue-800',
      'RELEASED': 'bg-green-100 text-green-800',
      'REFUNDED': 'bg-red-100 text-red-800',
      'DISPUTED': 'bg-purple-100 text-purple-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getPaymentMethodText = (method: string) => {
    const methodMap: { [key: string]: string } = {
      'BINANCE_PAY': 'Binance Pay',
      'BINANCE_API': 'Binance API',
      'BNB_CHAIN': 'BNB Smart Chain'
    }
    return methodMap[method] || method
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <Link
            href="/admin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回管理后台
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场 - 托管资金管理
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/admin" className="text-gray-700 hover:text-gray-900">
                返回后台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和搜索 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">托管资金管理</h1>
            
            {/* 搜索和筛选 */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="搜索托管ID、交易哈希或订单号..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">所有状态</option>
                    <option value="PENDING">待确认</option>
                    <option value="FUNDED">已到账</option>
                    <option value="RELEASED">已释放</option>
                    <option value="REFUNDED">已退款</option>
                    <option value="DISPUTED">有纠纷</option>
                  </select>
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  搜索
                </button>
              </form>
            </div>
          </div>

          {/* 统计信息 */}
          {pagination && (
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  共 {pagination.total} 个托管记录，第 {pagination.page} / {pagination.totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 托管记录列表 */}
          <div className="space-y-6">
            {escrowPayments.map((escrow) => (
              <div key={escrow.id} className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  {/* 托管信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">托管信息</h3>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">托管ID:</span> {escrow.id.slice(-8)}</div>
                      <div><span className="font-medium">金额:</span> {formatCurrency(escrow.amount)}</div>
                      <div><span className="font-medium">平台手续费:</span> {formatCurrency(escrow.platformFee)}</div>
                      <div><span className="font-medium">支付方式:</span> {getPaymentMethodText(escrow.paymentMethod)}</div>
                      <div><span className="font-medium">创建时间:</span> {formatDate(escrow.createdAt.toString())}</div>
                      {escrow.txHash && (
                        <div><span className="font-medium">交易哈希:</span>
                          <span className="text-xs font-mono block mt-1 break-all">{escrow.txHash}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 订单信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">订单信息</h3>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">订单号:</span> {escrow.order.orderNumber}</div>
                      <div><span className="font-medium">商品:</span> {escrow.order.product.title}</div>
                      <div><span className="font-medium">商品价格:</span> {formatCurrency(escrow.order.product.price)}</div>
                      <div><span className="font-medium">订单状态:</span>
                        <span className="ml-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                          {escrow.order.status}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 用户信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">交易双方</h3>
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm font-medium text-gray-700">买家</div>
                        <div className="text-sm text-gray-600">
                          {escrow.order.buyer.name || '未设置'} ({escrow.order.buyer.email})
                        </div>
                        <div className="text-xs text-gray-500">
                          信用分: {escrow.order.buyer.creditScore}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-700">卖家</div>
                        <div className="text-sm text-gray-600">
                          {escrow.order.seller.name || '未设置'} ({escrow.order.seller.email})
                        </div>
                        <div className="text-xs text-gray-500">
                          信用分: {escrow.order.seller.creditScore}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 操作区域 */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium text-gray-900">操作</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(escrow.status)}`}>
                        {getStatusText(escrow.status)}
                      </span>
                    </div>

                    <div className="space-y-2">
                      {escrow.status === 'PENDING' && (
                        <button
                          onClick={() => handleEscrowAction(escrow.id, 'confirmFunding', { notes: '管理员确认资金到账' })}
                          disabled={actionLoading === escrow.id}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                        >
                          {actionLoading === escrow.id ? '处理中...' : '确认到账'}
                        </button>
                      )}

                      {escrow.status === 'FUNDED' && (
                        <>
                          <button
                            onClick={() => handleEscrowAction(escrow.id, 'releaseFunds', { notes: '管理员释放资金给卖家' })}
                            disabled={actionLoading === escrow.id}
                            className="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                          >
                            {actionLoading === escrow.id ? '处理中...' : '释放资金'}
                          </button>
                          <button
                            onClick={() => {
                              const refundAmount = prompt('请输入退款金额 (USDT):', escrow.amount.toString())
                              if (refundAmount && parseFloat(refundAmount) > 0) {
                                handleEscrowAction(escrow.id, 'refundBuyer', {
                                  refundAmount: parseFloat(refundAmount),
                                  notes: '管理员处理退款'
                                })
                              }
                            }}
                            disabled={actionLoading === escrow.id}
                            className="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
                          >
                            {actionLoading === escrow.id ? '处理中...' : '退款买家'}
                          </button>
                        </>
                      )}

                      <button
                        onClick={() => {
                          const notes = prompt('请输入管理员备注:', escrow.adminNotes || '')
                          if (notes !== null) {
                            handleEscrowAction(escrow.id, 'addNotes', { notes })
                          }
                        }}
                        className="w-full bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-xs"
                      >
                        添加备注
                      </button>
                    </div>

                    {/* 时间戳信息 */}
                    <div className="mt-4 text-xs text-gray-500 space-y-1">
                      {escrow.fundedAt && (
                        <div>到账时间: {formatDate(escrow.fundedAt.toString())}</div>
                      )}
                      {escrow.releasedAt && (
                        <div>释放时间: {formatDate(escrow.releasedAt.toString())}</div>
                      )}
                      {escrow.refundedAt && (
                        <div>退款时间: {formatDate(escrow.refundedAt.toString())}</div>
                      )}
                    </div>

                    {/* 管理员备注 */}
                    {escrow.adminNotes && (
                      <div className="mt-3 p-2 bg-gray-100 rounded text-xs text-gray-700">
                        <strong>备注:</strong> {escrow.adminNotes}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {escrowPayments.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">暂无托管记录</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
