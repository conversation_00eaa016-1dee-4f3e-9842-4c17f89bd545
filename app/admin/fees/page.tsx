'use client'

import { useState, useEffect } from 'react'
import { 
  C<PERSON>rencyDollarIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  DocumentArrowUpIcon,
  Cog6ToothIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface FeeConfig {
  id: string
  type: string
  name: string
  description: string | null
  enabled: boolean
  feeType: string
  feeValue: number | null
  minFee: number | null
  maxFee: number | null
  tiers: any | null
  paymentMethod: string | null
  userType: string | null
  effectiveFrom: string
  effectiveTo: string | null
  version: number
  createdAt: string
  updatedAt: string
}

interface FeeConfigs {
  TRANSACTION: FeeConfig[]
  WITHDRAWAL: FeeConfig[]
}

export default function FeesManagementPage() {
  const [configs, setConfigs] = useState<FeeConfigs>({ TRANSACTION: [], WITHDRAWAL: [] })
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('TRANSACTION')
  
  // 模态框状态
  const [showConfigModal, setShowConfigModal] = useState(false)
  const [editingConfig, setEditingConfig] = useState<FeeConfig | null>(null)
  const [configForm, setConfigForm] = useState({
    type: 'TRANSACTION',
    name: '',
    description: '',
    enabled: true,
    feeType: 'PERCENTAGE',
    feeValue: '',
    minFee: '',
    maxFee: '',
    tiers: [{ min: 0, max: 100, rate: 0.01, type: 'PERCENTAGE' }],
    paymentMethod: '',
    userType: ''
  })
  const [configLoading, setConfigLoading] = useState(false)

  useEffect(() => {
    fetchConfigs()
  }, [])

  const fetchConfigs = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/fees')
      if (response.ok) {
        const data = await response.json()
        setConfigs(data.configs || { TRANSACTION: [], WITHDRAWAL: [] })
      }
    } catch (error) {
      console.error('获取手续费配置失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveConfig = async () => {
    if (!configForm.name || !configForm.feeType) {
      alert('请填写必要信息')
      return
    }

    if (configForm.feeType === 'TIERED' && (!configForm.tiers || configForm.tiers.length === 0)) {
      alert('分段配置不能为空')
      return
    }

    if (configForm.feeType !== 'TIERED' && !configForm.feeValue) {
      alert('请设置费率值')
      return
    }

    setConfigLoading(true)
    try {
      const payload = {
        ...configForm,
        feeValue: configForm.feeType === 'TIERED' ? null : parseFloat(configForm.feeValue),
        minFee: configForm.minFee ? parseFloat(configForm.minFee) : null,
        maxFee: configForm.maxFee ? parseFloat(configForm.maxFee) : null,
        paymentMethod: configForm.paymentMethod || null,
        userType: configForm.userType || null
      }

      const response = await fetch('/api/admin/fees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()
      if (response.ok) {
        alert(editingConfig ? '手续费配置已更新' : '手续费配置已创建')
        setShowConfigModal(false)
        setEditingConfig(null)
        resetConfigForm()
        fetchConfigs()
      } else {
        alert(result.error || '保存失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setConfigLoading(false)
    }
  }

  const handleDeleteConfig = async (configId: string) => {
    if (!confirm('确定要删除这个配置吗？')) return

    try {
      const response = await fetch(`/api/admin/fees?id=${configId}`, {
        method: 'DELETE'
      })

      const result = await response.json()
      if (response.ok) {
        alert('手续费配置已删除')
        fetchConfigs()
      } else {
        alert(result.error || '删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  const handleEditConfig = (config: FeeConfig) => {
    setEditingConfig(config)
    setConfigForm({
      type: config.type,
      name: config.name,
      description: config.description || '',
      enabled: config.enabled,
      feeType: config.feeType,
      feeValue: config.feeValue?.toString() || '',
      minFee: config.minFee?.toString() || '',
      maxFee: config.maxFee?.toString() || '',
      tiers: config.tiers || [{ min: 0, max: 100, rate: 0.01, type: 'PERCENTAGE' }],
      paymentMethod: config.paymentMethod || '',
      userType: config.userType || ''
    })
    setShowConfigModal(true)
  }

  const resetConfigForm = () => {
    setConfigForm({
      type: activeTab,
      name: '',
      description: '',
      enabled: true,
      feeType: 'PERCENTAGE',
      feeValue: '',
      minFee: '',
      maxFee: '',
      tiers: [{ min: 0, max: 100, rate: 0.01, type: 'PERCENTAGE' }],
      paymentMethod: '',
      userType: ''
    })
  }

  const handleExportConfigs = async () => {
    try {
      const response = await fetch('/api/admin/fees')
      if (response.ok) {
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data.configs, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `fee-configs-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      alert('导出失败')
    }
  }

  const handleImportConfigs = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const text = await file.text()
      const importedConfigs = JSON.parse(text)
      
      // 这里应该调用导入API
      console.log('导入配置:', importedConfigs)
      alert('导入功能开发中...')
    } catch (error) {
      alert('导入文件格式错误')
    }
  }

  const addTier = () => {
    const lastTier = configForm.tiers[configForm.tiers.length - 1]
    const newTier = {
      min: lastTier.max,
      max: lastTier.max + 100,
      rate: 0.01,
      type: 'PERCENTAGE'
    }
    setConfigForm({
      ...configForm,
      tiers: [...configForm.tiers, newTier]
    })
  }

  const removeTier = (index: number) => {
    if (configForm.tiers.length > 1) {
      const newTiers = configForm.tiers.filter((_, i) => i !== index)
      setConfigForm({
        ...configForm,
        tiers: newTiers
      })
    }
  }

  const updateTier = (index: number, field: string, value: any) => {
    const newTiers = [...configForm.tiers]
    newTiers[index] = { ...newTiers[index], [field]: value }
    setConfigForm({
      ...configForm,
      tiers: newTiers
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Cog6ToothIcon className="h-8 w-8 text-blue-600 mr-3" />
            平台手续费管理
          </h1>
          <p className="mt-2 text-gray-600">
            配置交易手续费和提现手续费，支持分段式费率设置
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex space-x-4">
            <button
              onClick={() => {
                setEditingConfig(null)
                resetConfigForm()
                setShowConfigModal(true)
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              新增配置
            </button>
            
            <button
              onClick={handleExportConfigs}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
              导出配置
            </button>
            
            <label className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 flex items-center cursor-pointer">
              <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
              导入配置
              <input
                type="file"
                accept=".json"
                onChange={handleImportConfigs}
                className="hidden"
              />
            </label>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'TRANSACTION', name: '交易手续费', icon: CurrencyDollarIcon },
              { id: 'WITHDRAWAL', name: '提现手续费', icon: DocumentArrowDownIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* 配置列表 */}
        <div className="bg-white shadow rounded-lg">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : (
            <FeeConfigTable
              configs={configs[activeTab as keyof FeeConfigs] || []}
              onEdit={handleEditConfig}
              onDelete={handleDeleteConfig}
            />
          )}
        </div>
      </div>

      {/* 配置模态框 */}
      {showConfigModal && (
        <FeeConfigModal
          config={configForm}
          setConfig={setConfigForm}
          editing={!!editingConfig}
          loading={configLoading}
          onSave={handleSaveConfig}
          onCancel={() => {
            setShowConfigModal(false)
            setEditingConfig(null)
            resetConfigForm()
          }}
          addTier={addTier}
          removeTier={removeTier}
          updateTier={updateTier}
        />
      )}
    </div>
  )
}

// 手续费配置表格组件
function FeeConfigTable({
  configs,
  onEdit,
  onDelete
}: {
  configs: FeeConfig[],
  onEdit: (config: FeeConfig) => void,
  onDelete: (id: string) => void
}) {
  const getFeeTypeDisplay = (config: FeeConfig) => {
    switch (config.feeType) {
      case 'FIXED':
        return `固定 ${config.feeValue} USDT`
      case 'PERCENTAGE':
        return `${(config.feeValue! * 100).toFixed(2)}%`
      case 'TIERED':
        return '分段计费'
      default:
        return config.feeType
    }
  }

  const getStatusBadge = (enabled: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {enabled ? '启用' : '禁用'}
      </span>
    )
  }

  if (configs.length === 0) {
    return (
      <div className="text-center py-12">
        <Cog6ToothIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无配置</h3>
        <p className="text-gray-500">点击"新增配置"按钮创建第一个手续费配置</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              配置名称
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              费率设置
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              限制条件
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              适用范围
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              版本
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {configs.map((config) => (
            <tr key={config.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">{config.name}</div>
                  {config.description && (
                    <div className="text-sm text-gray-500">{config.description}</div>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{getFeeTypeDisplay(config)}</div>
                {(config.minFee || config.maxFee) && (
                  <div className="text-xs text-gray-500">
                    {config.minFee && `最低: ${config.minFee} USDT`}
                    {config.minFee && config.maxFee && ' | '}
                    {config.maxFee && `最高: ${config.maxFee} USDT`}
                  </div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {config.feeType === 'TIERED' && config.tiers ? (
                  <div className="space-y-1">
                    {config.tiers.slice(0, 2).map((tier: any, index: number) => (
                      <div key={index} className="text-xs">
                        {tier.min}-{tier.max}: {tier.type === 'FIXED' ? `${tier.rate} USDT` : `${(tier.rate * 100).toFixed(2)}%`}
                      </div>
                    ))}
                    {config.tiers.length > 2 && (
                      <div className="text-xs text-gray-400">
                        +{config.tiers.length - 2} 更多...
                      </div>
                    )}
                  </div>
                ) : (
                  '-'
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>
                  {config.paymentMethod && (
                    <div className="text-xs">支付方式: {config.paymentMethod}</div>
                  )}
                  {config.userType && (
                    <div className="text-xs">用户类型: {config.userType}</div>
                  )}
                  {!config.paymentMethod && !config.userType && (
                    <span className="text-gray-400">全部</span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getStatusBadge(config.enabled)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                v{config.version}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex space-x-2">
                  <button
                    onClick={() => onEdit(config)}
                    className="text-blue-600 hover:text-blue-900 flex items-center"
                  >
                    <PencilIcon className="h-4 w-4 mr-1" />
                    编辑
                  </button>
                  <button
                    onClick={() => onDelete(config.id)}
                    className="text-red-600 hover:text-red-900 flex items-center"
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    删除
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 手续费配置模态框组件
function FeeConfigModal({
  config,
  setConfig,
  editing,
  loading,
  onSave,
  onCancel,
  addTier,
  removeTier,
  updateTier
}: {
  config: any
  setConfig: (config: any) => void
  editing: boolean
  loading: boolean
  onSave: () => void
  onCancel: () => void
  addTier: () => void
  removeTier: (index: number) => void
  updateTier: (index: number, field: string, value: any) => void
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              {editing ? '编辑手续费配置' : '新增手续费配置'}
            </h3>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-500"
            >
              <TrashIcon className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  配置类型 *
                </label>
                <select
                  value={config.type}
                  onChange={(e) => setConfig({ ...config, type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="TRANSACTION">交易手续费</option>
                  <option value="WITHDRAWAL">提现手续费</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  配置名称 *
                </label>
                <input
                  type="text"
                  value={config.name}
                  onChange={(e) => setConfig({ ...config, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入配置名称"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  配置描述
                </label>
                <input
                  type="text"
                  value={config.description}
                  onChange={(e) => setConfig({ ...config, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入配置描述"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  支付方式
                </label>
                <select
                  value={config.paymentMethod}
                  onChange={(e) => setConfig({ ...config, paymentMethod: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部支付方式</option>
                  <option value="deposit_balance">保证金支付</option>
                  <option value="binance_pay">币安支付</option>
                  <option value="bnb_chain">BNB链支付</option>
                  <option value="usdt">USDT支付</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  用户类型
                </label>
                <select
                  value={config.userType}
                  onChange={(e) => setConfig({ ...config, userType: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部用户</option>
                  <option value="NORMAL">普通用户</option>
                  <option value="VIP">VIP用户</option>
                  <option value="GUARANTOR">担保人</option>
                </select>
              </div>
            </div>

            {/* 启用状态 */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enabled"
                checked={config.enabled}
                onChange={(e) => setConfig({ ...config, enabled: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="enabled" className="text-sm font-medium text-gray-700">
                启用此配置
              </label>
            </div>

            {/* 费率设置 */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">费率设置</h4>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  费率类型 *
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="PERCENTAGE"
                      checked={config.feeType === 'PERCENTAGE'}
                      onChange={(e) => setConfig({ ...config, feeType: e.target.value })}
                      className="mr-2"
                    />
                    百分比
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="FIXED"
                      checked={config.feeType === 'FIXED'}
                      onChange={(e) => setConfig({ ...config, feeType: e.target.value })}
                      className="mr-2"
                    />
                    固定金额
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="TIERED"
                      checked={config.feeType === 'TIERED'}
                      onChange={(e) => setConfig({ ...config, feeType: e.target.value })}
                      className="mr-2"
                    />
                    分段计费
                  </label>
                </div>
              </div>

              {config.feeType !== 'TIERED' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {config.feeType === 'PERCENTAGE' ? '费率 (%)' : '固定金额 (USDT)'} *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={config.feeValue}
                      onChange={(e) => setConfig({ ...config, feeValue: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={config.feeType === 'PERCENTAGE' ? '如: 1.5' : '如: 5.00'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      最低手续费 (USDT)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={config.minFee}
                      onChange={(e) => setConfig({ ...config, minFee: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="可选"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      最高手续费 (USDT)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={config.maxFee}
                      onChange={(e) => setConfig({ ...config, maxFee: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="可选"
                    />
                  </div>
                </div>
              )}

              {config.feeType === 'TIERED' && (
                <TieredFeeConfig
                  tiers={config.tiers}
                  addTier={addTier}
                  removeTier={removeTier}
                  updateTier={updateTier}
                />
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3 pt-6 border-t">
              <button
                onClick={onSave}
                disabled={loading || !config.name || !config.feeType}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '保存中...' : editing ? '更新配置' : '创建配置'}
              </button>
              <button
                onClick={onCancel}
                className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 分段费率配置组件
function TieredFeeConfig({
  tiers,
  addTier,
  removeTier,
  updateTier
}: {
  tiers: any[]
  addTier: () => void
  removeTier: (index: number) => void
  updateTier: (index: number, field: string, value: any) => void
}) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h5 className="text-sm font-medium text-gray-700">分段费率设置</h5>
        <button
          onClick={addTier}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          <PlusIcon className="h-4 w-4 mr-1" />
          添加分段
        </button>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-yellow-800">分段费率说明</h4>
            <div className="mt-1 text-sm text-yellow-700">
              <p>• 分段范围不能重叠，系统会自动选择匹配的分段</p>
              <p>• 支持按百分比或固定金额收费</p>
              <p>• 建议按金额从小到大设置分段</p>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        {tiers.map((tier, index) => (
          <div key={index} className="border border-gray-200 rounded-md p-4">
            <div className="flex justify-between items-center mb-3">
              <h6 className="text-sm font-medium text-gray-700">
                分段 {index + 1}
              </h6>
              {tiers.length > 1 && (
                <button
                  onClick={() => removeTier(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  最小金额 (USDT)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={tier.min}
                  onChange={(e) => updateTier(index, 'min', parseFloat(e.target.value) || 0)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  最大金额 (USDT)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={tier.max}
                  onChange={(e) => updateTier(index, 'max', parseFloat(e.target.value) || 0)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  费率类型
                </label>
                <select
                  value={tier.type}
                  onChange={(e) => updateTier(index, 'type', e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="PERCENTAGE">百分比</option>
                  <option value="FIXED">固定金额</option>
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {tier.type === 'PERCENTAGE' ? '费率 (%)' : '金额 (USDT)'}
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={tier.rate}
                  onChange={(e) => updateTier(index, 'rate', parseFloat(e.target.value) || 0)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder={tier.type === 'PERCENTAGE' ? '如: 1.5' : '如: 5.00'}
                />
              </div>
            </div>

            <div className="mt-2 text-xs text-gray-500">
              范围: {tier.min} - {tier.max} USDT，
              费率: {tier.type === 'PERCENTAGE' ? `${tier.rate}%` : `${tier.rate} USDT`}
            </div>
          </div>
        ))}
      </div>

      {tiers.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <ExclamationTriangleIcon className="h-8 w-8 mx-auto mb-2" />
          <p>请至少添加一个分段配置</p>
        </div>
      )}
    </div>
  )
}
