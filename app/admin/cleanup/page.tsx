"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  TrashIcon,
  ClockIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ChartBarIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import AdminLayout from '@/components/admin/AdminLayout'

interface CleanupStats {
  expiredGiftCards: number
  expiredRedemptionCodes: number
  totalExpiredRecords: number
}

interface CleanupLog {
  id: string
  adminName: string
  description: string
  details: any
  createdAt: string
}

export default function AdminCleanupPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [stats, setStats] = useState<CleanupStats | null>(null)
  const [recentCleanups, setRecentCleanups] = useState<CleanupLog[]>([])
  const [loading, setLoading] = useState(true)
  const [cleaning, setCleaning] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/auth/signin')
      return
    }

    fetchCleanupStats()
  }, [session, status])

  const fetchCleanupStats = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/admin/cleanup')
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setStats(result.data.statistics)
          setRecentCleanups(result.data.recentCleanups)
        } else {
          setError(result.error || '获取清理统计失败')
        }
      } else {
        setError('获取清理统计失败')
      }
    } catch (error) {
      console.error('获取清理统计失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const executeCleanup = async (taskType: string) => {
    const confirmMessage = taskType === 'all_cleanup' 
      ? '确定要执行完整的数据清理吗？这将删除所有15天前的已兑换记录。'
      : `确定要执行${taskType === 'giftcard_cleanup' ? '礼品卡' : '兑换券'}清理吗？`

    if (!confirm(confirmMessage)) {
      return
    }

    try {
      setCleaning(true)
      setError('')
      setSuccess('')

      const response = await fetch('/api/admin/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ taskType })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess(result.message)
        
        // 显示清理结果详情
        const details = result.data
        let detailMessage = '清理完成:\n'
        
        if (taskType === 'all_cleanup') {
          detailMessage += `礼品卡: 删除 ${details.giftCard.deletedRecords} 条记录, ${details.giftCard.deletedTransactions} 条交易\n`
          detailMessage += `兑换券: 删除 ${details.redemption.deletedRecords} 条记录, ${details.redemption.deletedTransactions} 条交易\n`
          detailMessage += `总计: 删除 ${details.total.deletedRecords} 条记录, ${details.total.deletedTransactions} 条交易`
        } else {
          detailMessage += `删除 ${details.deletedRecords} 条记录, ${details.deletedTransactions} 条交易`
        }
        
        alert(detailMessage)
        
        // 刷新统计数据
        fetchCleanupStats()
      } else {
        setError(result.error || '清理失败')
      }
    } catch (error) {
      console.error('执行清理失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setCleaning(false)
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据清理管理</h1>
            <p className="text-gray-600">管理系统数据清理任务，自动删除过期记录</p>
          </div>
          <button
            onClick={fetchCleanupStats}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            刷新
          </button>
        </div>

        {/* 清理说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <InformationCircleIcon className="h-6 w-6 text-blue-400 mt-0.5" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">清理规则说明</h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>自动清理15天前已兑换的礼品卡记录</li>
                  <li>自动清理15天前已使用完毕的兑换券记录</li>
                  <li>清理操作会同时删除相关的交易记录</li>
                  <li>建议定期执行清理以优化数据库性能</li>
                  <li>可以设置定时任务自动执行清理</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 成功提示 */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* 清理统计 */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : stats ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrashIcon className="h-8 w-8 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">可清理礼品卡</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.expiredGiftCards}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrashIcon className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">可清理兑换券</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.expiredRedemptionCodes}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总计可清理</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.totalExpiredRecords}</p>
                </div>
              </div>
            </div>
          </div>
        ) : null}

        {/* 清理操作 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">执行清理操作</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => executeCleanup('giftcard_cleanup')}
              disabled={cleaning || !stats || stats.expiredGiftCards === 0}
              className="flex flex-col items-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-400 hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="h-8 w-8 text-red-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">清理礼品卡</span>
              <span className="text-xs text-gray-500 mt-1">
                {stats ? `${stats.expiredGiftCards} 条记录` : '加载中...'}
              </span>
            </button>

            <button
              onClick={() => executeCleanup('redemption_cleanup')}
              disabled={cleaning || !stats || stats.expiredRedemptionCodes === 0}
              className="flex flex-col items-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-400 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="h-8 w-8 text-orange-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">清理兑换券</span>
              <span className="text-xs text-gray-500 mt-1">
                {stats ? `${stats.expiredRedemptionCodes} 条记录` : '加载中...'}
              </span>
            </button>

            <button
              onClick={() => executeCleanup('all_cleanup')}
              disabled={cleaning || !stats || stats.totalExpiredRecords === 0}
              className="flex flex-col items-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="h-8 w-8 text-blue-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">完整清理</span>
              <span className="text-xs text-gray-500 mt-1">
                {stats ? `${stats.totalExpiredRecords} 条记录` : '加载中...'}
              </span>
            </button>
          </div>

          {cleaning && (
            <div className="mt-4 text-center">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-md">
                <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
                正在执行清理操作...
              </div>
            </div>
          )}
        </div>

        {/* 清理历史 */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">最近清理记录</h2>
          </div>
          
          <div className="divide-y divide-gray-200">
            {recentCleanups.length > 0 ? (
              recentCleanups.map((log) => (
                <div key={log.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-gray-400 mr-2" />
                        <h3 className="text-sm font-medium text-gray-900">
                          {log.description}
                        </h3>
                      </div>
                      <p className="mt-1 text-sm text-gray-600">
                        操作人: {log.adminName}
                      </p>
                      {log.details && (
                        <div className="mt-2 text-xs text-gray-500">
                          <pre className="whitespace-pre-wrap">
                            {JSON.stringify(log.details, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                    <div className="ml-4 text-right">
                      <p className="text-sm text-gray-500">
                        {new Date(log.createdAt).toLocaleString('zh-CN')}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <CalendarIcon className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-2 text-sm text-gray-500">暂无清理记录</p>
              </div>
            )}
          </div>
        </div>

        {/* 定时任务说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-400 mt-0.5" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">定时任务设置</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p className="mb-2">可以设置定时任务自动执行清理：</p>
                <code className="block bg-yellow-100 p-2 rounded text-xs">
                  # 每天凌晨2点执行清理<br/>
                  0 2 * * * cd /path/to/project && npx ts-node scripts/cleanup-cron.ts
                </code>
                <p className="mt-2 text-xs">
                  将上述命令添加到服务器的 crontab 中即可实现自动清理
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
