# BitMarket 管理后台新功能

## 概述

为BitMarket平台的管理后台添加了两个核心功能模块：公告管理和帮助中心内容管理。

## 🆕 新增功能

### 1. 公告管理功能 (`/admin/announcements`)

#### 功能特性
- **完整的CRUD操作**
  - 创建新公告 (`/admin/announcements/create`)
  - 编辑现有公告 (`/admin/announcements/[id]/edit`)
  - 查看公告详情 (`/admin/announcements/[id]`)
  - 删除公告（软删除）

- **公告属性管理**
  - 标题、内容、摘要
  - 分类：一般、紧急、维护、功能、安全
  - 优先级：低、普通、高、紧急
  - 目标用户群体：所有用户、买家、卖家、VIP用户、新用户
  - 状态管理：草稿、已发布、已归档、已删除

- **高级功能**
  - 置顶显示
  - 首页显示
  - 定时发布
  - 过期时间设置
  - 预览功能
  - 查看统计（浏览次数）

- **筛选和搜索**
  - 按状态筛选
  - 按分类筛选
  - 全文搜索（标题、内容、摘要）
  - 分页显示

#### API路由
- `GET /api/admin/announcements` - 获取公告列表
- `POST /api/admin/announcements` - 创建新公告
- `GET /api/admin/announcements/[id]` - 获取公告详情
- `PUT /api/admin/announcements/[id]` - 更新公告
- `DELETE /api/admin/announcements/[id]` - 删除公告

### 2. 帮助中心内容管理功能 (`/admin/help`)

#### 功能特性
- **文章管理**
  - 创建新帮助文章 (`/admin/help/create`)
  - 编辑现有文章 (`/admin/help/[id]/edit`)
  - 查看文章详情 (`/admin/help/[id]`)
  - 删除文章（软删除）

- **内容分类**
  - 分类：支付相关、交易流程、安全提示、账户设置、一般
  - 子分类支持
  - 文章类型：指南、FAQ、教程、提示、故障排除
  - 难度级别：初级、中级、高级

- **内容属性**
  - 标题、内容、摘要
  - 标签和关键词
  - 精选文章标记
  - 排序权重
  - 版本控制

- **统计和反馈**
  - 查看次数
  - 有用评价数
  - 无用评价数
  - 用户反馈收集

- **筛选和搜索**
  - 按状态筛选
  - 按分类筛选
  - 按文章类型筛选
  - 全文搜索（标题、内容、关键词）
  - 分页显示

#### API路由
- `GET /api/admin/help` - 获取帮助文章列表
- `POST /api/admin/help` - 创建新文章
- `GET /api/admin/help/[id]` - 获取文章详情
- `PUT /api/admin/help/[id]` - 更新文章
- `DELETE /api/admin/help/[id]` - 删除文章

## 🗄️ 数据库模型

### Announcement 模型
```prisma
model Announcement {
  id          String   @id @default(cuid())
  title       String   // 公告标题
  content     String   // 公告内容（支持富文本）
  summary     String?  // 公告摘要
  category    String   @default("GENERAL") // 分类
  priority    String   @default("NORMAL")  // 优先级
  targetUsers String   @default("ALL")     // 目标用户群体
  status      String   @default("DRAFT")   // 状态
  isSticky    Boolean  @default(false)    // 是否置顶
  showOnHome  Boolean  @default(false)    // 是否在首页显示
  publishAt   DateTime? // 定时发布时间
  expireAt    DateTime? // 过期时间
  viewCount   Int      @default(0)        // 查看次数
  authorId    String   // 创建者ID
  reviewerId  String?  // 审核者ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  // ... 关联关系和索引
}
```

### HelpArticle 模型
```prisma
model HelpArticle {
  id          String   @id @default(cuid())
  title       String   // 文章标题
  content     String   // 文章内容（支持富文本）
  summary     String?  // 文章摘要
  category    String   // 分类
  subcategory String?  // 子分类
  tags        String   // 标签，逗号分隔
  keywords    String   // 搜索关键词，逗号分隔
  articleType String   @default("GUIDE") // 文章类型
  status      String   @default("DRAFT") // 状态
  isFeatured  Boolean  @default(false)   // 是否为精选文章
  sortOrder   Int      @default(0)       // 排序权重
  difficulty  String   @default("BEGINNER") // 难度级别
  viewCount   Int      @default(0)        // 查看次数
  helpfulCount Int     @default(0)        // 有用评价数
  notHelpfulCount Int  @default(0)        // 无用评价数
  version     Int      @default(1)       // 版本号
  parentId    String?  // 父版本ID（用于版本历史）
  authorId    String   // 创建者ID
  reviewerId  String?  // 审核者ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  // ... 关联关系和索引
}
```

## 🔧 技术实现

### 前端技术
- **Next.js 15** - App Router
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Heroicons** - 图标库
- **React Hooks** - 状态管理

### 后端技术
- **Next.js API Routes** - RESTful API
- **Prisma ORM** - 数据库操作
- **SQLite** - 开发数据库
- **NextAuth.js** - 认证系统

### 权限控制
- 管理员权限验证
- 资源访问控制
- 审核流程支持

### 数据验证
- 前端表单验证
- 后端数据验证
- 错误处理机制

## 🎨 设计特性

### 一致的设计风格
- 遵循BitMarket设计规范
- 蓝色主题色 (#2563eb)
- 白色卡片背景
- 阴影和圆角效果
- 响应式布局

### 用户体验优化
- 直观的操作界面
- 清晰的状态指示
- 实时搜索和筛选
- 分页导航
- 预览功能

## 🔄 集成更新

### 管理后台导航
- 在管理首页添加了公告管理和帮助中心管理入口
- 更新了DashboardStats接口以支持新功能统计

### 帮助中心搜索
- 更新了 `/api/help/search` 路由
- 支持从数据库动态获取内容
- 保留静态内容作为后备方案

## 📊 功能状态

### ✅ 已完成
- [x] 数据库模型设计和迁移
- [x] 公告管理完整CRUD功能
- [x] 帮助中心内容管理功能
- [x] API路由实现
- [x] 前端界面开发
- [x] 权限验证集成
- [x] 搜索和筛选功能
- [x] 管理后台导航集成

### 🔄 待完善
- [ ] 富文本编辑器集成
- [ ] 批量操作功能
- [ ] 内容导入/导出
- [ ] 更详细的统计报表
- [ ] 内容版本对比
- [ ] 多语言支持

## 🚀 使用方法

### 访问管理后台
1. 登录管理员账户
2. 访问 `/admin` 管理首页
3. 点击"公告管理"或"帮助中心"卡片

### 创建公告
1. 进入 `/admin/announcements`
2. 点击"创建公告"按钮
3. 填写公告信息
4. 选择发布设置
5. 预览并提交

### 管理帮助文章
1. 进入 `/admin/help`
2. 点击"创建文章"按钮
3. 填写文章内容
4. 设置分类和属性
5. 发布或保存为草稿

## 🔍 测试验证

所有功能已通过开发环境测试：
- 数据库迁移成功
- API路由正常工作
- 前端界面正常显示
- 权限验证有效
- 搜索功能正常

新功能已完全集成到BitMarket平台中，可以立即投入使用！
