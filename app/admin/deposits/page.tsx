'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/admin/AdminLayout'

export default function DepositsManagementPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('users')
  const [users, setUsers] = useState([])
  const [deposits, setDeposits] = useState([])
  const [withdrawals, setWithdrawals] = useState([])
  const [operations, setOperations] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user?.role !== 'ADMIN') {
      router.push('/')
      return
    }

    fetchData()
  }, [session, status, activeTab])

  const fetchData = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/deposits?type=${activeTab}`)
      if (response.ok) {
        const data = await response.json()

        if (activeTab === 'users') {
          setUsers(data.users || [])
        } else if (activeTab === 'deposits') {
          setDeposits(data.deposits || [])
        } else if (activeTab === 'withdrawals') {
          setWithdrawals(data.withdrawals || [])
        } else if (activeTab === 'operations') {
          setOperations(data.operations || [])
        }
      }
    } catch (error) {
      console.error('获取数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理充值申请
  const handleDepositAction = async (depositId: string, action: 'approve' | 'reject') => {
    const confirmMessage = action === 'approve' ? '确定要批准这个充值申请吗？' : '确定要拒绝这个充值申请吗？'
    if (!confirm(confirmMessage)) return

    try {
      const response = await fetch(`/api/admin/deposits/${depositId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action })
      })

      if (response.ok) {
        alert(action === 'approve' ? '充值申请已批准' : '充值申请已拒绝')
        fetchData() // 刷新数据
      } else {
        const data = await response.json()
        alert('操作失败: ' + (data.error || '未知错误'))
      }
    } catch (error) {
      console.error('操作失败:', error)
      alert('操作失败，请重试')
    }
  }

  // 处理提现申请
  const handleWithdrawalAction = async (withdrawalId: string, action: 'approve' | 'reject') => {
    const confirmMessage = action === 'approve' ? '确定要批准这个提现申请吗？' : '确定要拒绝这个提现申请吗？'
    if (!confirm(confirmMessage)) return

    try {
      const response = await fetch(`/api/admin/withdrawals/${withdrawalId}/${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (response.ok) {
        alert(action === 'approve' ? '提现申请已批准' : '提现申请已拒绝')
        fetchData() // 刷新数据
      } else {
        const data = await response.json()
        alert('操作失败: ' + (data.error || '未知错误'))
      }
    } catch (error) {
      console.error('操作失败:', error)
      alert('操作失败，请重试')
    }
  }

  // 处理余额调整
  const handleBalanceAdjustment = async (userId: string, userName: string, currentBalance: number) => {
    const amount = prompt(`调整用户 "${userName}" 的保证金余额\n\n当前余额: ${currentBalance} USDT\n请输入调整金额 (正数为增加，负数为减少):`)

    if (amount === null) return // 用户取消

    const adjustmentAmount = parseFloat(amount)
    if (isNaN(adjustmentAmount) || adjustmentAmount === 0) {
      alert('请输入有效的调整金额')
      return
    }

    const newBalance = currentBalance + adjustmentAmount
    if (newBalance < 0) {
      alert('调整后余额不能为负数')
      return
    }

    const reason = prompt(`请输入调整原因:`)
    if (!reason || !reason.trim()) {
      alert('请输入调整原因')
      return
    }

    const confirmMessage = `确定要调整用户 "${userName}" 的保证金余额吗？\n\n当前余额: ${currentBalance} USDT\n调整金额: ${adjustmentAmount > 0 ? '+' : ''}${adjustmentAmount} USDT\n调整后余额: ${newBalance} USDT\n调整原因: ${reason}`

    if (!confirm(confirmMessage)) return

    try {
      const response = await fetch(`/api/admin/deposits/adjust-balance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          amount: adjustmentAmount,
          reason: reason.trim()
        })
      })

      if (response.ok) {
        alert('余额调整成功')
        fetchData() // 刷新数据
      } else {
        const data = await response.json()
        alert('调整失败: ' + (data.error || '未知错误'))
      }
    } catch (error) {
      console.error('调整失败:', error)
      alert('调整失败，请重试')
    }
  }

  return (
    <AdminLayout 
      title="用户保证金管理" 
      subtitle={`管理用户保证金余额、审核充值提现申请、查看操作历史，共 ${users.length} 个用户，${deposits.length} 个充值记录，${withdrawals.length} 个提现申请`}
    >
      {/* 标签页导航 */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { id: 'users', name: '用户余额' },
            { id: 'deposits', name: '充值申请' },
            { id: 'withdrawals', name: '提现申请' },
            { id: 'operations', name: '操作历史' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* 搜索和筛选 */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="搜索用户名、邮箱..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        {(activeTab === 'deposits' || activeTab === 'withdrawals') && (
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">所有状态</option>
            <option value="PENDING">待处理</option>
            <option value="APPROVED">已批准</option>
            <option value="COMPLETED">已完成</option>
            <option value="REJECTED">已拒绝</option>
            <option value="FAILED">失败</option>
          </select>
        )}
      </div>

      {/* 内容区域 */}
      <div className="bg-white shadow rounded-lg">
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        ) : (
          <div className="p-6">
            {activeTab === 'users' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">用户余额管理</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          用户信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          保证金余额
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          信用分数
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {user.depositBalance?.toFixed(2) || '0.00'} USDT
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.creditScore || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => handleBalanceAdjustment(user.id, user.name, user.depositBalance || 0)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              调整余额
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            
            {activeTab === 'deposits' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">充值申请</h3>
                {deposits.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 text-lg mb-2">💰</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无充值申请</h3>
                    <p className="text-gray-500">还没有用户提交充值申请</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            用户信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            充值信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            申请时间
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {deposits.map((deposit) => (
                          <tr key={deposit.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{deposit.user.name}</div>
                                <div className="text-sm text-gray-500">{deposit.user.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {deposit.amount} USDT
                                </div>
                                <div className="text-sm text-gray-500">
                                  {deposit.method === 'chain' ? '链上转账' : '币安扫码'}
                                </div>
                                {deposit.txHash && (
                                  <div className="text-xs text-gray-400">
                                    {deposit.txHash.slice(0, 10)}...
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                deposit.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                deposit.status === 'APPROVED' ? 'bg-blue-100 text-blue-800' :
                                deposit.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                deposit.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {deposit.status === 'PENDING' ? '待处理' :
                                 deposit.status === 'APPROVED' ? '已批准' :
                                 deposit.status === 'COMPLETED' ? '已完成' :
                                 deposit.status === 'REJECTED' ? '已拒绝' :
                                 deposit.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(deposit.createdAt).toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                              {deposit.status === 'PENDING' && (
                                <>
                                  <button
                                    onClick={() => handleDepositAction(deposit.id, 'approve')}
                                    className="text-green-600 hover:text-green-900"
                                  >
                                    批准
                                  </button>
                                  <button
                                    onClick={() => handleDepositAction(deposit.id, 'reject')}
                                    className="text-red-600 hover:text-red-900"
                                  >
                                    拒绝
                                  </button>
                                </>
                              )}
                              <button
                                onClick={() => window.open(`/admin/deposits/${deposit.id}`, '_blank')}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                查看详情
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
            
            {activeTab === 'withdrawals' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">提现申请</h3>
                {withdrawals.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 text-lg mb-2">💸</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无提现申请</h3>
                    <p className="text-gray-500">还没有用户提交提现申请</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            用户信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            提现信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            申请时间
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {withdrawals.map((withdrawal) => (
                          <tr key={withdrawal.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{withdrawal.user.name}</div>
                                <div className="text-sm text-gray-500">{withdrawal.user.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {withdrawal.amount} USDT
                                </div>
                                <div className="text-sm text-gray-500">
                                  {withdrawal.walletAddress?.slice(0, 10)}...
                                </div>
                                {withdrawal.fee && (
                                  <div className="text-xs text-gray-400">
                                    手续费: {withdrawal.fee} USDT
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                withdrawal.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                withdrawal.status === 'APPROVED' ? 'bg-blue-100 text-blue-800' :
                                withdrawal.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                withdrawal.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {withdrawal.status === 'PENDING' ? '待处理' :
                                 withdrawal.status === 'APPROVED' ? '已批准' :
                                 withdrawal.status === 'COMPLETED' ? '已完成' :
                                 withdrawal.status === 'REJECTED' ? '已拒绝' :
                                 withdrawal.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(withdrawal.createdAt).toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                              {withdrawal.status === 'PENDING' && (
                                <>
                                  <button
                                    onClick={() => handleWithdrawalAction(withdrawal.id, 'approve')}
                                    className="text-green-600 hover:text-green-900"
                                  >
                                    批准
                                  </button>
                                  <button
                                    onClick={() => handleWithdrawalAction(withdrawal.id, 'reject')}
                                    className="text-red-600 hover:text-red-900"
                                  >
                                    拒绝
                                  </button>
                                </>
                              )}
                              <button
                                onClick={() => window.open(`/admin/withdrawals/${withdrawal.id}`, '_blank')}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                查看详情
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
            
            {activeTab === 'operations' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">操作历史</h3>
                {operations.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 text-lg mb-2">📋</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无操作历史</h3>
                    <p className="text-gray-500">还没有保证金操作记录</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            用户信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作类型
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            金额变动
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作人
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作时间
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {operations.map((operation) => (
                          <tr key={operation.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{operation.user.name}</div>
                                <div className="text-sm text-gray-500">{operation.user.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {operation.type === 'DEPOSIT' ? '充值' :
                                   operation.type === 'WITHDRAWAL' ? '提现' :
                                   operation.type === 'ADJUSTMENT' ? '余额调整' :
                                   operation.type === 'FREEZE' ? '冻结' :
                                   operation.type === 'UNFREEZE' ? '解冻' :
                                   operation.type}
                                </div>
                                {operation.description && (
                                  <div className="text-sm text-gray-500">{operation.description}</div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className={`text-sm font-medium ${
                                operation.amount > 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {operation.amount > 0 ? '+' : ''}{operation.amount} USDT
                              </div>
                              <div className="text-sm text-gray-500">
                                余额: {operation.balanceAfter} USDT
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {operation.operator?.name || '系统'}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {operation.operator?.email || '<EMAIL>'}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(operation.createdAt).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
