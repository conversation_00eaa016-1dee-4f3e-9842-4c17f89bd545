'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import AdminLayout from '@/components/admin/AdminLayout'

interface DepositRecord {
  id: string
  amount: number
  method: string
  status: string
  txHash?: string
  notes?: string
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    email: string
    depositBalance: number
    creditScore: number
  }
}

export default function DepositDetailPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const depositId = params.id as string

  const [deposit, setDeposit] = useState<DepositRecord | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user?.role !== 'ADMIN') {
      router.push('/')
      return
    }

    fetchDepositDetail()
  }, [session, status, depositId])

  const fetchDepositDetail = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/deposits/${depositId}`)
      if (response.ok) {
        const data = await response.json()
        setDeposit(data)
      } else if (response.status === 404) {
        alert('充值记录不存在')
        router.push('/admin/deposits')
      } else {
        const errorData = await response.json()
        alert('获取充值详情失败: ' + (errorData.error || '未知错误'))
      }
    } catch (error) {
      console.error('获取充值详情失败:', error)
      alert('获取充值详情失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleAction = async (action: 'approve' | 'reject') => {
    const confirmMessage = action === 'approve' ? '确定要批准这个充值申请吗？' : '确定要拒绝这个充值申请吗？'
    if (!confirm(confirmMessage)) return

    setProcessing(true)
    try {
      const response = await fetch(`/api/admin/deposits/${depositId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action })
      })

      if (response.ok) {
        alert(action === 'approve' ? '充值申请已批准' : '充值申请已拒绝')
        fetchDepositDetail() // 刷新数据
      } else {
        const data = await response.json()
        alert('操作失败: ' + (data.error || '未知错误'))
      }
    } catch (error) {
      console.error('操作失败:', error)
      alert('操作失败，请重试')
    } finally {
      setProcessing(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
      case 'PENDING_APPROVAL':
        return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED':
        return 'bg-blue-100 text-blue-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      case 'FAILED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return '待处理'
      case 'PENDING_APPROVAL':
        return '待审核'
      case 'APPROVED':
        return '已批准'
      case 'COMPLETED':
        return '已完成'
      case 'REJECTED':
        return '已拒绝'
      case 'FAILED':
        return '失败'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <AdminLayout title="充值详情" subtitle="查看充值申请详细信息">
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      </AdminLayout>
    )
  }

  if (!deposit) {
    return (
      <AdminLayout title="充值详情" subtitle="查看充值申请详细信息">
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">❌</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">充值记录不存在</h3>
          <p className="text-gray-500 mb-4">该充值记录可能已被删除或不存在</p>
          <button
            onClick={() => router.push('/admin/deposits')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            返回充值管理
          </button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout 
      title="充值详情" 
      subtitle={`充值申请 #${deposit.id.slice(-8)}`}
    >
      {/* 返回按钮 */}
      <div className="mb-6">
        <button
          onClick={() => router.push('/admin/deposits')}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          返回充值管理
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 充值信息卡片 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">充值信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">充值金额</label>
                <p className="mt-1 text-lg font-semibold text-gray-900">{deposit.amount} USDT</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">充值方式</label>
                <p className="mt-1 text-sm text-gray-900">
                  {deposit.method === 'chain' ? '链上转账' : '币安扫码'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">状态</label>
                <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>
                  {getStatusText(deposit.status)}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">申请时间</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(deposit.createdAt).toLocaleString()}
                </p>
              </div>
              {deposit.txHash && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-500">交易哈希</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono break-all">{deposit.txHash}</p>
                </div>
              )}
              {deposit.notes && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-500">备注</label>
                  <p className="mt-1 text-sm text-gray-900">{deposit.notes}</p>
                </div>
              )}
            </div>
          </div>

          {/* 用户信息卡片 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">用户信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">用户名</label>
                <p className="mt-1 text-sm text-gray-900">{deposit.user.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">邮箱</label>
                <p className="mt-1 text-sm text-gray-900">{deposit.user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">当前保证金余额</label>
                <p className="mt-1 text-sm text-gray-900">{deposit.user.depositBalance?.toFixed(2) || '0.00'} USDT</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">信用分数</label>
                <p className="mt-1 text-sm text-gray-900">{deposit.user.creditScore || 0}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 操作面板 */}
        <div className="space-y-6">
          {/* 操作按钮 */}
          {(deposit.status === 'PENDING' || deposit.status === 'PENDING_APPROVAL') && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">操作</h3>
              <div className="space-y-3">
                <button
                  onClick={() => handleAction('approve')}
                  disabled={processing}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {processing ? '处理中...' : '批准充值'}
                </button>
                <button
                  onClick={() => handleAction('reject')}
                  disabled={processing}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {processing ? '处理中...' : '拒绝充值'}
                </button>
              </div>
            </div>
          )}

          {/* 状态历史 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">状态历史</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <div>
                  <p className="text-sm font-medium text-gray-900">申请提交</p>
                  <p className="text-xs text-gray-500">{new Date(deposit.createdAt).toLocaleString()}</p>
                </div>
              </div>
              {deposit.updatedAt !== deposit.createdAt && (
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">状态更新</p>
                    <p className="text-xs text-gray-500">{new Date(deposit.updatedAt).toLocaleString()}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
