'use client'

import { useState, useEffect } from 'react'

// Simple toast replacement
const toast = {
  success: (message: string) => alert(`成功: ${message}`),
  error: (message: string) => alert(`错误: ${message}`)
}

interface AdminManagementSectionsProps {
  orderId: string
  order: any
  onOrderUpdate: () => void
}

export default function AdminManagementSections({ orderId, order, onOrderUpdate }: AdminManagementSectionsProps) {
  const [showNotes, setShowNotes] = useState(false)
  const [showLogs, setShowLogs] = useState(false)
  const [showCreditAdjust, setShowCreditAdjust] = useState(false)
  const [notes, setNotes] = useState<any[]>([])
  const [logs, setLogs] = useState<any[]>([])
  const [newNote, setNewNote] = useState('')
  const [creditUserId, setCreditUserId] = useState('')
  const [creditChange, setCreditChange] = useState(0)
  const [creditReason, setCreditReason] = useState('')
  const [notesLoaded, setNotesLoaded] = useState(false)
  const [logsLoaded, setLogsLoaded] = useState(false)

  const fetchNotes = async () => {
    if (notesLoaded) return
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/notes`)
      if (response.ok) {
        const data = await response.json()
        setNotes(data)
        setNotesLoaded(true)
      }
    } catch (error) {
      console.error('获取备注失败:', error)
    }
  }

  const fetchLogs = async () => {
    if (logsLoaded) return
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/logs`)
      if (response.ok) {
        const data = await response.json()
        setLogs(data)
        setLogsLoaded(true)
      }
    } catch (error) {
      console.error('获取日志失败:', error)
    }
  }

  const handleAddNote = async () => {
    if (!newNote.trim()) return

    try {
      const response = await fetch(`/api/admin/orders/${orderId}/notes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: newNote.trim(),
          isPrivate: true
        })
      })

      if (response.ok) {
        setNewNote('')
        setNotesLoaded(false) // 重新加载备注
        fetchNotes()
        toast.success('备注添加成功')
      } else {
        const error = await response.json()
        toast.error(error.error || '添加备注失败')
      }
    } catch (error) {
      toast.error('添加备注失败')
    }
  }

  const handleCreditAdjust = async () => {
    if (!creditUserId || creditChange === 0 || !creditReason.trim()) {
      toast.error('请填写完整信息')
      return
    }

    try {
      const response = await fetch(`/api/admin/orders/${orderId}/credit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: creditUserId,
          creditChange,
          reason: creditReason.trim()
        })
      })

      if (response.ok) {
        setCreditUserId('')
        setCreditChange(0)
        setCreditReason('')
        setShowCreditAdjust(false)
        onOrderUpdate() // 刷新订单数据
        toast.success('信用分调整成功')
      } else {
        const error = await response.json()
        toast.error(error.error || '调整信用分失败')
      }
    } catch (error) {
      toast.error('调整信用分失败')
    }
  }

  const handleToggleNotes = () => {
    console.log('备注按钮被点击，当前状态:', showNotes)
    setShowNotes(!showNotes)
    if (!showNotes) fetchNotes()
  }

  const handleToggleLogs = () => {
    console.log('日志按钮被点击，当前状态:', showLogs)
    setShowLogs(!showLogs)
    if (!showLogs) fetchLogs()
  }

  const handleToggleCreditAdjust = () => {
    console.log('信用分按钮被点击，当前状态:', showCreditAdjust)
    setShowCreditAdjust(!showCreditAdjust)
  }

  return (
    <>
      {/* 管理功能按钮 */}
      <div className="space-y-2">
        <button
          onClick={handleToggleNotes}
          className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
        >
          {showNotes ? '隐藏备注' : '管理员备注'} {showNotes ? '✓' : ''}
        </button>

        <button
          onClick={handleToggleLogs}
          className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          {showLogs ? '隐藏日志' : '操作日志'} {showLogs ? '✓' : ''}
        </button>

        <button
          onClick={handleToggleCreditAdjust}
          className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
        >
          {showCreditAdjust ? '取消调整' : '调整信用分'} {showCreditAdjust ? '✓' : ''}
        </button>
      </div>

      {/* 管理员备注区域 */}
      {showNotes && (
        <div className="mt-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">管理员备注</h3>
          </div>
          <div className="p-6">
            {/* 添加新备注 */}
            <div className="mb-4">
              <textarea
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                placeholder="添加管理员备注..."
                className="w-full p-3 border border-gray-300 rounded-md resize-none"
                rows={3}
              />
              <button
                onClick={handleAddNote}
                disabled={!newNote.trim()}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
              >
                添加备注
              </button>
            </div>

            {/* 备注列表 */}
            <div className="space-y-3">
              {notes.length > 0 ? (
                notes.map((note) => (
                  <div key={note.id} className="p-3 bg-gray-50 rounded-md">
                    <p className="text-gray-800">{note.content}</p>
                    <div className="mt-2 text-sm text-gray-600">
                      <span>创建者: {note.admin.name || note.admin.email}</span>
                      <span className="ml-4">时间: {new Date(note.createdAt).toLocaleString()}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">暂无备注</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 操作日志区域 */}
      {showLogs && (
        <div className="mt-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">操作日志</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {logs.length > 0 ? (
                logs.map((log) => (
                  <div key={log.id} className="p-3 border-l-4 border-blue-500 bg-blue-50">
                    <p className="font-medium">{log.description}</p>
                    <div className="mt-1 text-sm text-gray-600">
                      <span>操作者: {log.operator ? (log.operator.name || log.operator.email) : '系统'}</span>
                      <span className="ml-4">操作: {log.action}</span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(log.createdAt).toLocaleString()}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">暂无操作日志</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 信用分调整区域 */}
      {showCreditAdjust && order && (
        <div className="mt-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">调整用户信用分</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">选择用户</label>
                <select
                  value={creditUserId}
                  onChange={(e) => setCreditUserId(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">请选择用户</option>
                  <option value={order.buyer.id}>
                    买家: {order.buyer.name || order.buyer.email} (当前: {order.buyer.creditScore}分)
                  </option>
                  <option value={order.seller.id}>
                    卖家: {order.seller.name || order.seller.email} (当前: {order.seller.creditScore}分)
                  </option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">调整分数 (-50 到 +50)</label>
                <input
                  type="number"
                  min="-50"
                  max="50"
                  value={creditChange}
                  onChange={(e) => setCreditChange(parseInt(e.target.value) || 0)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">调整原因</label>
              <textarea
                value={creditReason}
                onChange={(e) => setCreditReason(e.target.value)}
                placeholder="请输入调整信用分的原因..."
                className="w-full p-3 border border-gray-300 rounded-md resize-none"
                rows={3}
              />
            </div>
            <button
              onClick={handleCreditAdjust}
              disabled={!creditUserId || creditChange === 0 || !creditReason.trim()}
              className="mt-4 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:bg-gray-400"
            >
              确认调整
            </button>
          </div>
        </div>
      )}
    </>
  )
}
