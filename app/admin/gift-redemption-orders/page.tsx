"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  GiftIcon,
  TicketIcon,
  EyeIcon,
  ArrowPathIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  UserIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import AdminLayout from '@/components/admin/AdminLayout'

interface GiftRedemptionOrder {
  id: string
  type: 'giftcard' | 'redemption'
  code: string
  title: string
  amount: number
  status: string
  customer: {
    id: string
    name: string
    email: string
  } | null
  createdAt: string
  redeemedAt?: string
  usedAt?: string
  saleOrder?: {
    id: string
    orderNumber: string
    totalAmount: number
    status: string
  }
  relatedOrder?: {
    id: string
    orderNumber: string
    totalAmount: number
  }
  details: any
}

interface OrdersResponse {
  orders: GiftRedemptionOrder[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
  summary: {
    giftCards: number
    redemptions: number
    totalValue: number
  }
}

export default function GiftRedemptionOrdersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [orders, setOrders] = useState<GiftRedemptionOrder[]>([])
  const [summary, setSummary] = useState<any>({})
  const [pagination, setPagination] = useState<any>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 筛选和搜索
  const [filters, setFilters] = useState({
    type: 'all',
    status: '',
    search: ''
  })
  
  const [currentPage, setCurrentPage] = useState(1)

  useEffect(() => {
    if (status === 'loading') return
    if (!session) {
      router.push('/auth/signin')
      return
    }
    fetchOrders()
  }, [session, status, filters, currentPage])

  const fetchOrders = async () => {
    try {
      setLoading(true)
      setError('')

      const queryParams = new URLSearchParams()
      queryParams.append('page', currentPage.toString())
      queryParams.append('limit', '20')
      if (filters.type) queryParams.append('type', filters.type)
      if (filters.status) queryParams.append('status', filters.status)
      if (filters.search) queryParams.append('search', filters.search)

      const response = await fetch(`/api/admin/gift-redemption-orders?${queryParams.toString()}`)
      
      if (response.ok) {
        const result: { success: boolean; data: OrdersResponse } = await response.json()
        if (result.success) {
          setOrders(result.data.orders)
          setPagination(result.data.pagination)
          setSummary(result.data.summary)
        } else {
          setError('获取订单列表失败')
        }
      } else {
        setError('获取订单列表失败')
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'GENERATED': '已生成',
      'SOLD': '已售出',
      'REDEEMED': '已兑换',
      'EXPIRED': '已过期',
      'ACTIVE': '有效',
      'USED': '已使用',
      'REVOKED': '已撤销'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'GENERATED': 'bg-gray-100 text-gray-800',
      'SOLD': 'bg-blue-100 text-blue-800',
      'REDEEMED': 'bg-green-100 text-green-800',
      'EXPIRED': 'bg-red-100 text-red-800',
      'ACTIVE': 'bg-green-100 text-green-800',
      'USED': 'bg-blue-100 text-blue-800',
      'REVOKED': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getTypeText = (type: string) => {
    return type === 'giftcard' ? '礼品卡' : '兑换券'
  }

  const getTypeIcon = (type: string) => {
    return type === 'giftcard' ? GiftIcon : TicketIcon
  }

  return (
    <AdminLayout title="礼品卡和兑换券订单">
      <div className="space-y-6">
        {/* 页面标题和统计 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">礼品卡和兑换券订单</h1>
            <p className="text-gray-600">查看和管理礼品卡销售和兑换券使用记录</p>
          </div>
          <div className="flex items-center space-x-4">
            {summary && (
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center px-3 py-2 bg-blue-100 text-blue-800 rounded-md">
                  <GiftIcon className="h-4 w-4 mr-2" />
                  <span>礼品卡: {summary.giftCards}</span>
                </div>
                <div className="flex items-center px-3 py-2 bg-green-100 text-green-800 rounded-md">
                  <TicketIcon className="h-4 w-4 mr-2" />
                  <span>兑换券: {summary.redemptions}</span>
                </div>
                <div className="flex items-center px-3 py-2 bg-purple-100 text-purple-800 rounded-md">
                  <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                  <span>总价值: {summary.totalValue?.toFixed(2)} USDT</span>
                </div>
              </div>
            )}
            <button
              onClick={fetchOrders}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              刷新
            </button>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">类型</label>
              <select
                value={filters.type}
                onChange={(e) => {
                  setFilters({ ...filters, type: e.target.value })
                  setCurrentPage(1)
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">全部</option>
                <option value="giftcard">礼品卡</option>
                <option value="redemption">兑换券</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">状态</label>
              <select
                value={filters.status}
                onChange={(e) => {
                  setFilters({ ...filters, status: e.target.value })
                  setCurrentPage(1)
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">全部状态</option>
                <option value="SOLD">已售出</option>
                <option value="REDEEMED">已兑换</option>
                <option value="USED">已使用</option>
                <option value="ACTIVE">有效</option>
                <option value="EXPIRED">已过期</option>
              </select>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">搜索</label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索代码、用户名或邮箱..."
                  value={filters.search}
                  onChange={(e) => {
                    setFilters({ ...filters, search: e.target.value })
                    setCurrentPage(1)
                  }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 订单列表 */}
        <div className="bg-white shadow rounded-lg">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <p className="text-red-600">{error}</p>
              <button
                onClick={fetchOrders}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                重试
              </button>
            </div>
          ) : orders.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              暂无订单记录
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型/代码
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      标题/金额
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      客户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => {
                    const TypeIcon = getTypeIcon(order.type)
                    return (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <TypeIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {getTypeText(order.type)}
                              </div>
                              <div className="text-sm text-gray-500 font-mono">
                                {order.code}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{order.title}</div>
                          <div className="text-sm text-gray-500">
                            {order.amount.toFixed(2)} USDT
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {order.customer ? (
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {order.customer.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {order.customer.email}
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">未知用户</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>创建: {new Date(order.createdAt).toLocaleDateString('zh-CN')}</div>
                            {order.redeemedAt && (
                              <div>兑换: {new Date(order.redeemedAt).toLocaleDateString('zh-CN')}</div>
                            )}
                            {order.usedAt && (
                              <div>使用: {new Date(order.usedAt).toLocaleDateString('zh-CN')}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              // 查看详情功能
                              const details = `${getTypeText(order.type)}详情:
代码: ${order.code}
标题: ${order.title}
金额: ${order.amount} USDT
状态: ${getStatusText(order.status)}
客户: ${order.customer?.name || '未知'} (${order.customer?.email || '无邮箱'})
创建时间: ${new Date(order.createdAt).toLocaleString('zh-CN')}
${order.redeemedAt ? `兑换时间: ${new Date(order.redeemedAt).toLocaleString('zh-CN')}` : ''}
${order.usedAt ? `使用时间: ${new Date(order.usedAt).toLocaleString('zh-CN')}` : ''}
${order.saleOrder ? `关联订单: ${order.saleOrder.orderNumber}` : ''}
${order.relatedOrder ? `相关订单: ${order.relatedOrder.orderNumber}` : ''}`
                              alert(details)
                            }}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            查看详情
                          </button>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}

          {/* 分页 */}
          {pagination && pagination.pages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                  disabled={currentPage === pagination.pages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    显示第 <span className="font-medium">{(currentPage - 1) * 20 + 1}</span> 到{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * 20, pagination.total)}
                    </span>{' '}
                    条，共 <span className="font-medium">{pagination.total}</span> 条记录
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === page
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      )
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                      disabled={currentPage === pagination.pages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  )
}
