'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Feedback {
  id: string
  category: string
  title: string
  description: string
  status: string
  priority: string
  adminResponse?: string
  contactEmail?: string
  contactPhone?: string
  createdAt: string
  updatedAt: string
  resolvedAt?: string
  user: {
    id: string
    name: string
    email: string
    userId: string
    creditScore?: number
  }
  assignedTo?: {
    id: string
    name: string
    email: string
  }
}

interface FeedbackStats {
  overview: {
    total: number
    pending: number
    inProgress: number
    resolved: number
    closed: number
    urgent: number
  }
  categories: {
    bugReports: number
    improvements: number
    appeals: number
  }
}

const FEEDBACK_CATEGORIES = {
  BUG_REPORT: {
    label: '平台bug反馈',
    icon: '🐛',
    color: 'bg-red-50 border-red-200 text-red-800'
  },
  IMPROVEMENT: {
    label: '平台改进建议',
    icon: '💡',
    color: 'bg-blue-50 border-blue-200 text-blue-800'
  },
  APPEAL: {
    label: '平台申诉',
    icon: '⚖️',
    color: 'bg-yellow-50 border-yellow-200 text-yellow-800'
  }
}

const STATUS_CONFIG = {
  PENDING: { label: '待处理', color: 'bg-gray-100 text-gray-800' },
  IN_PROGRESS: { label: '处理中', color: 'bg-blue-100 text-blue-800' },
  RESOLVED: { label: '已解决', color: 'bg-green-100 text-green-800' },
  CLOSED: { label: '已关闭', color: 'bg-gray-100 text-gray-600' }
}

const PRIORITY_CONFIG = {
  LOW: { label: '低', color: 'bg-gray-100 text-gray-800' },
  MEDIUM: { label: '中', color: 'bg-blue-100 text-blue-800' },
  HIGH: { label: '高', color: 'bg-orange-100 text-orange-800' },
  URGENT: { label: '紧急', color: 'bg-red-100 text-red-800' }
}

export default function AdminRadarPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  // 状态管理
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([])
  const [stats, setStats] = useState<FeedbackStats | null>(null)
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 筛选和分页
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    priority: '',
    search: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)
  
  // 表单状态
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateForm, setUpdateForm] = useState({
    status: '',
    priority: '',
    adminResponse: ''
  })

  // 检查管理员权限
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    } else if (status === 'authenticated') {
      checkAdminPermission()
    }
  }, [status, router])

  const checkAdminPermission = async () => {
    try {
      const response = await fetch('/api/admin/check')
      if (!response.ok) {
        if (response.status === 403) {
          setError('您没有管理员权限')
        } else {
          setError('权限验证失败')
        }
        return
      }
      
      // 权限验证通过，加载数据
      await Promise.all([fetchFeedbacks(), fetchStats()])
    } catch (error) {
      console.error('权限检查失败:', error)
      setError('网络错误')
    }
  }

  // 获取反馈列表
  const fetchFeedbacks = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      })

      const response = await fetch(`/api/admin/feedback?${params}`)
      if (response.ok) {
        const data = await response.json()
        setFeedbacks(data.feedbacks)
        setTotalPages(data.pagination.pages)
        setTotal(data.pagination.total)
      } else {
        setError('获取反馈列表失败')
      }
    } catch (error) {
      console.error('获取反馈列表失败:', error)
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/feedback/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  // 重新加载数据
  useEffect(() => {
    if (status === 'authenticated') {
      fetchFeedbacks()
    }
  }, [currentPage, filters, status])

  // 处理筛选变化
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // 重置到第一页
  }

  // 选择反馈查看详情
  const handleSelectFeedback = async (feedback: Feedback) => {
    setSelectedFeedback(feedback)
    setUpdateForm({
      status: feedback.status,
      priority: feedback.priority,
      adminResponse: feedback.adminResponse || ''
    })
  }

  // 更新反馈
  const handleUpdateFeedback = async () => {
    if (!selectedFeedback) return

    try {
      setIsUpdating(true)
      const response = await fetch(`/api/admin/feedback/${selectedFeedback.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateForm),
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedFeedback(data.feedback)
        
        // 更新列表中的反馈
        setFeedbacks(prev => 
          prev.map(f => f.id === data.feedback.id ? data.feedback : f)
        )
        
        // 重新获取统计数据
        fetchStats()
        
        alert('更新成功')
      } else {
        const data = await response.json()
        alert(data.error || '更新失败')
      }
    } catch (error) {
      console.error('更新反馈失败:', error)
      alert('网络错误')
    } finally {
      setIsUpdating(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">加载中...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-lg mb-2">{error}</div>
            <Link href="/admin" className="text-blue-600 hover:text-blue-800">
              返回管理后台
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场 - 管理后台
              </Link>
              <span className="ml-4 text-gray-500">/</span>
              <span className="ml-2 text-gray-700">反馈管理</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">管理员: {session?.user?.name || session?.user?.email}</span>
              <Link href="/" className="text-gray-700 hover:text-gray-900">
                返回前台
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">反馈管理</h1>
            <p className="mt-2 text-gray-600">管理和处理用户反馈、建议和申诉</p>
          </div>

          {/* 统计卡片 */}
          {stats && (
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">📝</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">总反馈数</dt>
                        <dd className="text-lg font-medium text-gray-900">{stats.overview.total}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">⏳</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">待处理</dt>
                        <dd className="text-lg font-medium text-gray-900">{stats.overview.pending}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">🔄</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">处理中</dt>
                        <dd className="text-lg font-medium text-gray-900">{stats.overview.inProgress}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm">🚨</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">紧急</dt>
                        <dd className="text-lg font-medium text-gray-900">{stats.overview.urgent}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 主要内容区域 */}
          <div className="bg-white shadow rounded-lg">
            {selectedFeedback ? (
              /* 反馈详情视图 */
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">反馈详情</h2>
                  <button
                    onClick={() => setSelectedFeedback(null)}
                    className="text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 反馈信息 */}
                  <div className="lg:col-span-2 space-y-6">
                    {/* 基本信息 */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${FEEDBACK_CATEGORIES[selectedFeedback.category as keyof typeof FEEDBACK_CATEGORIES]?.color}`}>
                            {FEEDBACK_CATEGORIES[selectedFeedback.category as keyof typeof FEEDBACK_CATEGORIES]?.icon} {FEEDBACK_CATEGORIES[selectedFeedback.category as keyof typeof FEEDBACK_CATEGORIES]?.label}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${STATUS_CONFIG[selectedFeedback.status as keyof typeof STATUS_CONFIG]?.color}`}>
                            {STATUS_CONFIG[selectedFeedback.status as keyof typeof STATUS_CONFIG]?.label}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${PRIORITY_CONFIG[selectedFeedback.priority as keyof typeof PRIORITY_CONFIG]?.color}`}>
                            {PRIORITY_CONFIG[selectedFeedback.priority as keyof typeof PRIORITY_CONFIG]?.label}
                          </span>
                        </div>
                      </div>

                      <h3 className="text-lg font-medium text-gray-900 mb-2">{selectedFeedback.title}</h3>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>提交时间: {formatDate(selectedFeedback.createdAt)}</p>
                        <p>更新时间: {formatDate(selectedFeedback.updatedAt)}</p>
                        {selectedFeedback.resolvedAt && (
                          <p>解决时间: {formatDate(selectedFeedback.resolvedAt)}</p>
                        )}
                      </div>
                    </div>

                    {/* 用户信息 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">用户信息</h4>
                      <div className="bg-white border rounded-lg p-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">用户ID:</span>
                            <span className="ml-2 font-medium">{selectedFeedback.user.userId}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">用户名:</span>
                            <span className="ml-2 font-medium">{selectedFeedback.user.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">邮箱:</span>
                            <span className="ml-2 font-medium">{selectedFeedback.user.email}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">信用分:</span>
                            <span className="ml-2 font-medium">{selectedFeedback.user.creditScore || 'N/A'}</span>
                          </div>
                        </div>
                        {(selectedFeedback.contactEmail || selectedFeedback.contactPhone) && (
                          <div className="mt-4 pt-4 border-t">
                            <h5 className="text-sm font-medium text-gray-700 mb-2">联系方式</h5>
                            <div className="space-y-1 text-sm">
                              {selectedFeedback.contactEmail && (
                                <div>
                                  <span className="text-gray-500">联系邮箱:</span>
                                  <span className="ml-2">{selectedFeedback.contactEmail}</span>
                                </div>
                              )}
                              {selectedFeedback.contactPhone && (
                                <div>
                                  <span className="text-gray-500">联系电话:</span>
                                  <span className="ml-2">{selectedFeedback.contactPhone}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 详细描述 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">详细描述</h4>
                      <div className="bg-white border rounded-lg p-4">
                        <p className="text-gray-700 whitespace-pre-wrap">{selectedFeedback.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* 管理操作 */}
                  <div className="space-y-6">
                    {/* 状态管理 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">状态管理</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                          <select
                            value={updateForm.status}
                            onChange={(e) => setUpdateForm(prev => ({ ...prev, status: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="PENDING">待处理</option>
                            <option value="IN_PROGRESS">处理中</option>
                            <option value="RESOLVED">已解决</option>
                            <option value="CLOSED">已关闭</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                          <select
                            value={updateForm.priority}
                            onChange={(e) => setUpdateForm(prev => ({ ...prev, priority: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="LOW">低</option>
                            <option value="MEDIUM">中</option>
                            <option value="HIGH">高</option>
                            <option value="URGENT">紧急</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* 管理员回复 */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">管理员回复</h4>
                      <textarea
                        value={updateForm.adminResponse}
                        onChange={(e) => setUpdateForm(prev => ({ ...prev, adminResponse: e.target.value }))}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入回复内容..."
                      />
                    </div>

                    {/* 操作按钮 */}
                    <div className="space-y-3">
                      <button
                        onClick={handleUpdateFeedback}
                        disabled={isUpdating}
                        className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isUpdating ? '更新中...' : '更新反馈'}
                      </button>

                      <button
                        onClick={() => setSelectedFeedback(null)}
                        className="w-full px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                      >
                        返回列表
                      </button>
                    </div>

                    {/* 分配信息 */}
                    {selectedFeedback.assignedTo && (
                      <div className="pt-4 border-t">
                        <h5 className="text-sm font-medium text-gray-700 mb-2">分配给</h5>
                        <div className="text-sm text-gray-600">
                          <p>{selectedFeedback.assignedTo.name}</p>
                          <p className="text-xs">{selectedFeedback.assignedTo.email}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              /* 反馈列表视图 */
              <div>
                {/* 筛选和搜索 */}
                <div className="p-6 border-b">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
                      <select
                        value={filters.category}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">全部类型</option>
                        <option value="BUG_REPORT">Bug反馈</option>
                        <option value="IMPROVEMENT">改进建议</option>
                        <option value="APPEAL">申诉</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                      <select
                        value={filters.status}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">全部状态</option>
                        <option value="PENDING">待处理</option>
                        <option value="IN_PROGRESS">处理中</option>
                        <option value="RESOLVED">已解决</option>
                        <option value="CLOSED">已关闭</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                      <select
                        value={filters.priority}
                        onChange={(e) => handleFilterChange('priority', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">全部优先级</option>
                        <option value="LOW">低</option>
                        <option value="MEDIUM">中</option>
                        <option value="HIGH">高</option>
                        <option value="URGENT">紧急</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                      <input
                        type="text"
                        value={filters.search}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="搜索标题、描述或用户..."
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      共 {total} 条反馈
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setFilters({ category: '', status: '', priority: '', search: '' })
                          setCurrentPage(1)
                        }}
                        className="text-sm text-gray-600 hover:text-gray-800"
                      >
                        清除筛选
                      </button>
                    </div>
                  </div>
                </div>

                {/* 反馈列表 */}
                <div className="divide-y divide-gray-200">
                  {loading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-gray-500">加载中...</div>
                    </div>
                  ) : feedbacks.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 text-6xl mb-4">📝</div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无反馈</h3>
                      <p className="text-gray-600">没有找到符合条件的反馈</p>
                    </div>
                  ) : (
                    feedbacks.map((feedback) => (
                      <div
                        key={feedback.id}
                        onClick={() => handleSelectFeedback(feedback)}
                        className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className={`px-2 py-1 rounded text-xs font-medium ${FEEDBACK_CATEGORIES[feedback.category as keyof typeof FEEDBACK_CATEGORIES]?.color}`}>
                                {FEEDBACK_CATEGORIES[feedback.category as keyof typeof FEEDBACK_CATEGORIES]?.icon} {FEEDBACK_CATEGORIES[feedback.category as keyof typeof FEEDBACK_CATEGORIES]?.label}
                              </span>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${STATUS_CONFIG[feedback.status as keyof typeof STATUS_CONFIG]?.color}`}>
                                {STATUS_CONFIG[feedback.status as keyof typeof STATUS_CONFIG]?.label}
                              </span>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${PRIORITY_CONFIG[feedback.priority as keyof typeof PRIORITY_CONFIG]?.color}`}>
                                {PRIORITY_CONFIG[feedback.priority as keyof typeof PRIORITY_CONFIG]?.label}
                              </span>
                            </div>

                            <h3 className="font-medium text-gray-900 mb-1">{feedback.title}</h3>
                            <p className="text-sm text-gray-600 line-clamp-2 mb-2">{feedback.description}</p>

                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span>用户: {feedback.user.name} ({feedback.user.userId})</span>
                              <span>提交时间: {formatDate(feedback.createdAt)}</span>
                              {feedback.assignedTo && (
                                <span>分配给: {feedback.assignedTo.name}</span>
                              )}
                            </div>
                          </div>

                          <div className="ml-4">
                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* 分页 */}
                {totalPages > 1 && (
                  <div className="px-6 py-4 border-t">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        第 {currentPage} 页，共 {totalPages} 页
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                          disabled={currentPage === 1}
                          className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          上一页
                        </button>
                        <button
                          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                          disabled={currentPage === totalPages}
                          className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          下一页
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
