'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/admin/AdminLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { formatUSDT } from '@/lib/utils'
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Users, 
  DollarSign,
  TrendingUp,
  FileText,
  Search,
  Filter
} from 'lucide-react'

interface EscrowStats {
  totalOrders: number
  totalAmount: number
  totalFees: number
  activeOrders: number
  completedOrders: number
  disputedOrders: number
  activeMediators: number
}

interface EscrowOrder {
  id: string
  status: string
  amount: number
  mediatorFee: number
  createdAt: string
  fundedAt?: string
  completedAt?: string
  order: {
    orderNumber: string
    product: {
      title: string
    }
  }
  buyer: {
    name: string
    email: string
  }
  seller: {
    name: string
    email: string
  }
  mediator: {
    name: string
    email: string
  }
  disputes: any[]
}

interface Dispute {
  id: string
  reason: string
  status: string
  priority: string
  createdAt: string
  escrowOrder: {
    order: {
      orderNumber: string
    }
  }
  reporter: {
    name: string
  }
  reported: {
    name: string
  }
}

export default function AdminEscrowOrdersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<EscrowStats | null>(null)
  const [escrowOrders, setEscrowOrders] = useState<EscrowOrder[]>([])
  const [disputes, setDisputes] = useState<Dispute[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  
  // 筛选状态
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user?.role !== 'ADMIN') {
      router.push('/')
      return
    }

    if (session?.user) {
      fetchData()
    }
  }, [session, status])

  const fetchData = async () => {
    setLoading(true)
    try {
      const [statsRes, ordersRes, disputesRes] = await Promise.all([
        fetch('/api/admin/escrow-orders/stats'),
        fetch('/api/admin/escrow-orders'),
        fetch('/api/admin/escrow-orders/disputes')
      ])

      const [statsData, ordersData, disputesData] = await Promise.all([
        statsRes.json(),
        ordersRes.json(),
        disputesRes.json()
      ])

      if (statsData.success) setStats(statsData.data)
      if (ordersData.success) setEscrowOrders(ordersData.data.orders || [])
      if (disputesData.success) setDisputes(disputesData.data.disputes || [])

    } catch (error) {
      console.error('获取数据失败:', error)
      setError('获取数据失败')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="w-3 h-3 mr-1" />等待资金</Badge>
      case 'FUNDED':
        return <Badge variant="outline" className="text-blue-600">已到账</Badge>
      case 'SHIPPED':
        return <Badge variant="outline" className="text-purple-600">已发货</Badge>
      case 'DELIVERED':
        return <Badge variant="outline" className="text-green-600">已收货</Badge>
      case 'COMPLETED':
        return <Badge variant="outline" className="text-green-600"><CheckCircle className="w-3 h-3 mr-1" />已完成</Badge>
      case 'DISPUTED':
        return <Badge variant="outline" className="text-red-600"><AlertTriangle className="w-3 h-3 mr-1" />争议中</Badge>
      case 'VOTING':
        return <Badge variant="outline" className="text-purple-600">投票中</Badge>
      case 'RESOLVED':
        return <Badge variant="outline" className="text-green-600">已解决</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <Badge variant="destructive">紧急</Badge>
      case 'HIGH':
        return <Badge variant="destructive" className="bg-orange-500">高</Badge>
      case 'MEDIUM':
        return <Badge variant="secondary">中</Badge>
      case 'LOW':
        return <Badge variant="outline">低</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const filteredOrders = escrowOrders.filter(order => {
    const matchesSearch = !searchTerm || 
      order.order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.order.product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.buyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.seller.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.mediator.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = !statusFilter || order.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const filteredDisputes = disputes.filter(dispute => {
    const matchesSearch = !searchTerm || 
      dispute.escrowOrder.order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dispute.reporter.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dispute.reported.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesPriority = !priorityFilter || dispute.priority === priorityFilter
    
    return matchesSearch && matchesPriority
  })

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-escrow-orders-page">
      <AdminLayout
        title="托管订单管理"
        subtitle={`管理平台的托管订单、争议处理和中间人服务，共 ${escrowOrders.length} 个托管订单，${disputes.length} 个争议`}
      >

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">概览统计</TabsTrigger>
          <TabsTrigger value="orders">托管订单</TabsTrigger>
          <TabsTrigger value="disputes">争议管理</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {stats && (
            <>
              {/* 统计卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">总托管订单</CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalOrders}</div>
                    <p className="text-xs text-muted-foreground">
                      活跃: {stats.activeOrders} | 完成: {stats.completedOrders}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">托管总金额</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatUSDT(stats.totalAmount)}</div>
                    <p className="text-xs text-muted-foreground">
                      平台收入: {formatUSDT(stats.totalFees * 0.3)}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">活跃中间人</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.activeMediators}</div>
                    <p className="text-xs text-muted-foreground">
                      认证中间人数量
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">争议订单</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">{stats.disputedOrders}</div>
                    <p className="text-xs text-muted-foreground">
                      需要处理的争议
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* 快速操作 */}
              <Card>
                <CardHeader>
                  <CardTitle>快速操作</CardTitle>
                  <CardDescription>
                    常用的管理操作
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button
                      onClick={() => router.push('/admin/mediator/applications')}
                      variant="outline"
                      className="h-20 flex flex-col"
                    >
                      <Users className="w-6 h-6 mb-2" />
                      中间人申请审核
                    </Button>
                    <Button
                      onClick={() => router.push('/admin/withdrawal-vouchers')}
                      variant="outline"
                      className="h-20 flex flex-col"
                    >
                      <DollarSign className="w-6 h-6 mb-2" />
                      提现券管理
                    </Button>
                    <Button
                      onClick={() => setActiveTab('disputes')}
                      variant="outline"
                      className="h-20 flex flex-col"
                    >
                      <AlertTriangle className="w-6 h-6 mb-2" />
                      争议处理
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="orders" className="space-y-6">
          {/* 筛选器 */}
          <Card>
            <CardHeader>
              <CardTitle>托管订单列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4 mb-4">
                <div className="flex-1">
                  <Input
                    placeholder="搜索订单号、商品、用户..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="筛选状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    <SelectItem value="PENDING">等待资金</SelectItem>
                    <SelectItem value="FUNDED">已到账</SelectItem>
                    <SelectItem value="SHIPPED">已发货</SelectItem>
                    <SelectItem value="DELIVERED">已收货</SelectItem>
                    <SelectItem value="COMPLETED">已完成</SelectItem>
                    <SelectItem value="DISPUTED">争议中</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium">{order.order.orderNumber}</h4>
                          {getStatusBadge(order.status)}
                          {order.disputes.length > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {order.disputes.length}个争议
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{order.order.product.title}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">买家:</span>
                            <span className="ml-1">{order.buyer.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">卖家:</span>
                            <span className="ml-1">{order.seller.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">中间人:</span>
                            <span className="ml-1">{order.mediator.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">金额:</span>
                            <span className="ml-1">{formatUSDT(order.amount)}</span>
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => router.push(`/escrow/orders/${order.id}`)}
                        variant="outline"
                        size="sm"
                      >
                        查看详情
                      </Button>
                    </div>
                  </div>
                ))}

                {filteredOrders.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">没有找到匹配的托管订单</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="disputes" className="space-y-6">
          {/* 争议列表 */}
          <Card>
            <CardHeader>
              <CardTitle>争议管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4 mb-4">
                <div className="flex-1">
                  <Input
                    placeholder="搜索订单号、举报人..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="筛选优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部优先级</SelectItem>
                    <SelectItem value="URGENT">紧急</SelectItem>
                    <SelectItem value="HIGH">高</SelectItem>
                    <SelectItem value="MEDIUM">中</SelectItem>
                    <SelectItem value="LOW">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                {filteredDisputes.map((dispute) => (
                  <div key={dispute.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium">{dispute.escrowOrder.order.orderNumber}</h4>
                          {getStatusBadge(dispute.status)}
                          {getPriorityBadge(dispute.priority)}
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">举报人:</span>
                            <span className="ml-1">{dispute.reporter.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">被举报人:</span>
                            <span className="ml-1">{dispute.reported.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">创建时间:</span>
                            <span className="ml-1">{new Date(dispute.createdAt).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => router.push(`/admin/disputes/${dispute.id}`)}
                        variant="outline"
                        size="sm"
                      >
                        处理争议
                      </Button>
                    </div>
                  </div>
                ))}

                {filteredDisputes.length === 0 && (
                  <div className="text-center py-8">
                    <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">没有找到匹配的争议</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
    </div>
  )
}
