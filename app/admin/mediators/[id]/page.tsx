'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'

interface MediatorApplication {
  id: string
  userId: string
  feeRate: number
  depositAmount: number
  bnbWalletAddress: string
  experience: string
  reason: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  createdAt: string
  approvedAt?: string
  rejectedAt?: string
  reviewNotes?: string
  user: {
    id: string
    name: string
    email: string
    depositBalance: number
    creditScore: number
    createdAt: string
  }
  reviewer?: {
    name: string
    email: string
  }
}

export default function MediatorApplicationDetail() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [application, setApplication] = useState<MediatorApplication | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [reviewNotes, setReviewNotes] = useState('')
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve')

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user?.role !== 'ADMIN') {
      router.push('/')
      return
    }

    fetchApplication()
  }, [session, status, params.id])

  const fetchApplication = async () => {
    try {
      const response = await fetch(`/api/admin/mediator/applications/${params.id}`)
      const data = await response.json()
      
      if (data.success) {
        setApplication(data.data)
      } else {
        console.error('获取申请详情失败:', data.error)
      }
    } catch (error) {
      console.error('获取申请详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReview = async () => {
    if (!application || !reviewNotes.trim()) {
      alert('请填写审核意见')
      return
    }

    try {
      setProcessing(true)
      const response = await fetch('/api/admin/mediator/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          applicationId: application.id,
          action: reviewAction,
          reviewNotes: reviewNotes.trim()
        })
      })

      const data = await response.json()
      
      if (data.success) {
        alert(data.message)
        setShowReviewModal(false)
        fetchApplication() // 刷新数据
      } else {
        alert('操作失败: ' + data.error)
      }
    } catch (error) {
      console.error('审核申请失败:', error)
      alert('操作失败，请重试')
    } finally {
      setProcessing(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <AdminLayout title="中间人申请详情" subtitle="加载中...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!application) {
    return (
      <AdminLayout title="中间人申请详情" subtitle="申请不存在">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-4">申请不存在</h3>
          <Link
            href="/admin/mediators"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            返回申请列表
          </Link>
        </div>
      </AdminLayout>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED': return 'bg-green-100 text-green-800'
      case 'REJECTED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return '待审核'
      case 'APPROVED': return '已批准'
      case 'REJECTED': return '已拒绝'
      default: return '未知状态'
    }
  }

  return (
    <AdminLayout 
      title="中间人申请详情" 
      subtitle={`申请人：${application.user.name}`}
      actions={
        <Link
          href="/admin/mediators"
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          返回列表
        </Link>
      }
    >
      <div className="space-y-6">
        {/* 申请状态 */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">申请状态</h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
              {getStatusText(application.status)}
            </span>
          </div>
          
          {application.status === 'PENDING' && (
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setReviewAction('approve')
                  setShowReviewModal(true)
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                批准申请
              </button>
              <button
                onClick={() => {
                  setReviewAction('reject')
                  setShowReviewModal(true)
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
              >
                拒绝申请
              </button>
            </div>
          )}
        </div>

        {/* 申请人信息 */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">申请人信息</h3>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">姓名</dt>
              <dd className="mt-1 text-sm text-gray-900">{application.user.name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">邮箱</dt>
              <dd className="mt-1 text-sm text-gray-900">{application.user.email}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">信用分数</dt>
              <dd className="mt-1 text-sm text-gray-900">{application.user.creditScore}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">保证金余额</dt>
              <dd className="mt-1 text-sm text-gray-900">{application.user.depositBalance} USDT</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">注册时间</dt>
              <dd className="mt-1 text-sm text-gray-900">{new Date(application.user.createdAt).toLocaleDateString()}</dd>
            </div>
          </dl>
        </div>

        {/* 申请详情 */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">申请详情</h3>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">手续费率</dt>
              <dd className="mt-1 text-sm text-gray-900">{(application.feeRate * 100).toFixed(2)}%</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">保证金金额</dt>
              <dd className="mt-1 text-sm text-gray-900">{application.depositAmount} USDT</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">BNB钱包地址</dt>
              <dd className="mt-1 text-sm text-gray-900 break-all">{application.bnbWalletAddress}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">申请时间</dt>
              <dd className="mt-1 text-sm text-gray-900">{new Date(application.createdAt).toLocaleString()}</dd>
            </div>
            <div className="sm:col-span-2">
              <dt className="text-sm font-medium text-gray-500">相关经验</dt>
              <dd className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{application.experience}</dd>
            </div>
            <div className="sm:col-span-2">
              <dt className="text-sm font-medium text-gray-500">申请理由</dt>
              <dd className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{application.reason}</dd>
            </div>
          </dl>
        </div>

        {/* 审核信息 */}
        {(application.status === 'APPROVED' || application.status === 'REJECTED') && (
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">审核信息</h3>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              {application.reviewer && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">审核人</dt>
                  <dd className="mt-1 text-sm text-gray-900">{application.reviewer.name} ({application.reviewer.email})</dd>
                </div>
              )}
              <div>
                <dt className="text-sm font-medium text-gray-500">审核时间</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {application.approvedAt && new Date(application.approvedAt).toLocaleString()}
                  {application.rejectedAt && new Date(application.rejectedAt).toLocaleString()}
                </dd>
              </div>
              {application.reviewNotes && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">审核意见</dt>
                  <dd className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{application.reviewNotes}</dd>
                </div>
              )}
            </dl>
          </div>
        )}
      </div>

      {/* 审核模态框 */}
      {showReviewModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {reviewAction === 'approve' ? '批准申请' : '拒绝申请'}
              </h3>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  审核意见 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入审核意见..."
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                  disabled={processing}
                >
                  取消
                </button>
                <button
                  onClick={handleReview}
                  disabled={processing || !reviewNotes.trim()}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                    reviewAction === 'approve'
                      ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-300'
                      : 'bg-red-600 hover:bg-red-700 disabled:bg-red-300'
                  }`}
                >
                  {processing ? '处理中...' : (reviewAction === 'approve' ? '批准' : '拒绝')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
