"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/admin/AdminLayout'
import {
  GiftIcon,
  PlusIcon,
  EyeIcon,
  ArrowPathIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  DocumentArrowDownIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface GiftCard {
  id: string
  cardCode: string
  faceValue: number
  status: string
  validUntil: string
  soldAt?: string
  redeemedAt?: string
  batchId?: string
  notes?: string
  assignedToId?: string
  assignedAt?: string
  assignedById?: string
  soldTo?: {
    id: string
    name: string
    email: string
  }
  redeemedBy?: {
    id: string
    name: string
    email: string
  }
  assignedTo?: {
    id: string
    name: string
    email: string
  }
  assignedBy?: {
    id: string
    name: string
    email: string
  }
  createdBy: {
    id: string
    name: string
  }
  saleOrder?: {
    id: string
    orderNumber: string
  }
}

interface GiftCardStats {
  [key: string]: {
    count: number
    totalValue: number
  }
}

export default function AdminGiftCardsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [giftCards, setGiftCards] = useState<GiftCard[]>([])
  const [stats, setStats] = useState<GiftCardStats>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showGenerateModal, setShowGenerateModal] = useState(false)
  const [generating, setGenerating] = useState(false)
  
  // 筛选和搜索
  const [filters, setFilters] = useState({
    status: '',
    batchId: '',
    search: ''
  })

  // 生成表单
  const [generateForm, setGenerateForm] = useState({
    faceValue: 50,
    quantity: 10,
    validDays: 365,
    batchName: '',
    notes: ''
  })

  const supportedValues = [10, 20, 50, 100, 200, 500, 1000]

  // 新增状态
  const [showAssignModal, setShowAssignModal] = useState(false)
  const [selectedGiftCard, setSelectedGiftCard] = useState<GiftCard | null>(null)
  const [assignForm, setAssignForm] = useState({
    userEmail: '',
    reason: ''
  })

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/auth/signin')
      return
    }

    fetchGiftCards()
  }, [session, status])

  const fetchGiftCards = async () => {
    try {
      setLoading(true)
      setError('')

      const queryParams = new URLSearchParams()
      if (filters.status) queryParams.append('status', filters.status)
      if (filters.batchId) queryParams.append('batchId', filters.batchId)
      if (filters.search) queryParams.append('search', filters.search)

      const response = await fetch(`/api/admin/giftcards?${queryParams.toString()}`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setGiftCards(result.data.giftCards)
          setStats(result.data.stats)
        } else {
          setError(result.error || '获取礼品卡列表失败')
        }
      } else {
        setError('获取礼品卡列表失败')
      }
    } catch (error) {
      console.error('获取礼品卡列表失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateGiftCards = async () => {
    try {
      setGenerating(true)
      
      const response = await fetch('/api/admin/giftcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(generateForm)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(`成功生成 ${result.data.quantity} 张礼品卡`)
        setShowGenerateModal(false)
        setGenerateForm({
          faceValue: 50,
          quantity: 10,
          validDays: 365,
          batchName: '',
          notes: ''
        })
        fetchGiftCards() // 刷新列表
      } else {
        alert(result.error || '生成礼品卡失败')
      }
    } catch (error) {
      console.error('生成礼品卡失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setGenerating(false)
    }
  }

  const handleBatchAction = async (action: string, giftCardIds: string[]) => {
    if (giftCardIds.length === 0) {
      alert('请选择要操作的礼品卡')
      return
    }

    const reason = action === 'expire' ? prompt('请输入过期原因:') : null
    if (action === 'expire' && !reason) return

    try {
      const response = await fetch('/api/admin/giftcards', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ giftCardIds, action, reason })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(result.message)
        fetchGiftCards() // 刷新列表
      } else {
        alert(result.error || '操作失败')
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  // 查看礼品卡详情
  const viewGiftCardDetails = (giftCard: GiftCard) => {
    const details = `礼品卡详情:
卡码: ${giftCard.cardCode}
面值: ${giftCard.faceValue} USDT
状态: ${getStatusText(giftCard.status)}
有效期: ${new Date(giftCard.validUntil).toLocaleDateString('zh-CN')}
${giftCard.assignedTo ? `指定用户: ${giftCard.assignedTo.name} (${giftCard.assignedTo.email})` : ''}
${giftCard.soldTo ? `购买者: ${giftCard.soldTo.name} (${giftCard.soldTo.email})` : ''}
${giftCard.redeemedBy ? `兑换者: ${giftCard.redeemedBy.name} (${giftCard.redeemedBy.email})` : ''}
${giftCard.notes ? `备注: ${giftCard.notes}` : ''}`
    alert(details)
  }

  // 打开指定用户模态框
  const openAssignModal = (giftCard: GiftCard) => {
    setSelectedGiftCard(giftCard)
    setAssignForm({
      userEmail: '',
      reason: ''
    })
    setShowAssignModal(true)
  }

  // 指定用户
  const handleAssignUser = async () => {
    if (!selectedGiftCard || !assignForm.userEmail || !assignForm.reason) {
      alert('请填写完整信息')
      return
    }

    try {
      const response = await fetch(`/api/admin/giftcards/${selectedGiftCard.id}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userEmail: assignForm.userEmail,
          reason: assignForm.reason
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(result.message)
        setShowAssignModal(false)
        fetchGiftCards()
      } else {
        alert(result.error || '指定用户失败')
      }
    } catch (error) {
      console.error('指定用户失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  // 取消指定用户
  const unassignGiftCard = async (giftCard: GiftCard) => {
    const reason = prompt('请输入取消指定的原因:')
    if (!reason) return

    try {
      const response = await fetch(`/api/admin/giftcards/${giftCard.id}/assign?reason=${encodeURIComponent(reason)}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(result.message)
        fetchGiftCards()
      } else {
        alert(result.error || '取消指定失败')
      }
    } catch (error) {
      console.error('取消指定失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  // 撤回兑换权
  const revokeGiftCard = async (giftCard: GiftCard) => {
    const reason = prompt('请输入撤回原因:')
    if (!reason) return

    const refundToUser = confirm('是否退款给用户？')

    try {
      const response = await fetch(`/api/admin/giftcards/${giftCard.id}/revoke`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reason,
          refundToUser
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(result.message)
        fetchGiftCards()
      } else {
        alert(result.error || '撤回失败')
      }
    } catch (error) {
      console.error('撤回失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  // 删除礼品卡
  const deleteGiftCard = async (giftCard: GiftCard) => {
    const reason = prompt('请输入删除原因:')
    if (!reason) return

    let forceDelete = false
    if (giftCard.status === 'SOLD' || giftCard.status === 'REDEEMED') {
      forceDelete = confirm('该礼品卡已售出或已兑换，是否强制删除？')
      if (!forceDelete) return
    }

    if (!confirm(`确定要删除礼品卡 ${giftCard.cardCode} 吗？${forceDelete ? '这是强制删除操作，不可恢复！' : ''}`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/giftcards/${giftCard.id}?reason=${encodeURIComponent(reason)}&force=${forceDelete}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (response.ok && result.success) {
        alert(result.message)
        fetchGiftCards()
      } else {
        if (result.canForceDelete) {
          const forceConfirm = confirm(result.error + '\n\n是否强制删除？')
          if (forceConfirm) {
            deleteGiftCard(giftCard) // 递归调用，但这次会设置forceDelete
          }
        } else {
          alert(result.error || '删除失败')
        }
      }
    } catch (error) {
      console.error('删除失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'GENERATED':
        return 'bg-blue-100 text-blue-800'
      case 'SOLD':
        return 'bg-green-100 text-green-800'
      case 'REDEEMED':
        return 'bg-purple-100 text-purple-800'
      case 'EXPIRED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'GENERATED': '已生成',
      'SOLD': '已售出',
      'REDEEMED': '已兑换',
      'EXPIRED': '已过期'
    }
    return statusMap[status] || status
  }

  const exportGiftCards = async () => {
    try {
      const response = await fetch('/api/admin/giftcards/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(filters)
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `giftcards-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const result = await response.json()
        alert(result.error || '导出失败')
      }
    } catch (error) {
      console.error('导出礼品卡失败:', error)
      alert('导出失败，请稍后重试')
    }
  }

  return (
    <AdminLayout
      title="礼品卡管理"
      subtitle={`管理平台礼品卡的生成、销售和兑换，共 ${giftCards.length} 张礼品卡`}
    >
      <div className="space-y-6">
        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div></div>
          <div className="flex space-x-3">
            <button
              onClick={exportGiftCards}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
              导出
            </button>
            <button
              onClick={() => setShowGenerateModal(true)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              生成礼品卡
            </button>
            <button
              onClick={fetchGiftCards}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              刷新
            </button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Object.entries(stats).map(([status, data]) => (
            <div key={status} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <GiftIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{getStatusText(status)}</p>
                  <p className="text-2xl font-semibold text-gray-900">{data.count}</p>
                  <p className="text-sm text-gray-500">{data.totalValue.toFixed(2)} USDT</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 筛选器 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部状态</option>
                <option value="GENERATED">已生成</option>
                <option value="SOLD">已售出</option>
                <option value="REDEEMED">已兑换</option>
                <option value="EXPIRED">已过期</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">批次ID</label>
              <input
                type="text"
                value={filters.batchId}
                onChange={(e) => setFilters(prev => ({ ...prev, batchId: e.target.value }))}
                placeholder="输入批次ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">搜索</label>
              <div className="relative">
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  placeholder="礼品卡码、备注..."
                  className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <button
              onClick={fetchGiftCards}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              应用筛选
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 礼品卡列表 */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          ) : giftCards.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      礼品卡信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      面值/状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      销售信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      兑换信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      有效期
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {giftCards.map((giftCard) => (
                    <tr key={giftCard.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 font-mono">
                            {giftCard.cardCode}
                          </div>
                          {giftCard.batchId && (
                            <div className="text-xs text-gray-500">
                              批次: {giftCard.batchId}
                            </div>
                          )}
                          {giftCard.notes && (
                            <div className="text-xs text-gray-500 truncate max-w-32">
                              {giftCard.notes}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {giftCard.faceValue} USDT
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(giftCard.status)}`}>
                            {getStatusText(giftCard.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {giftCard.soldTo ? (
                          <div>
                            <div className="text-sm text-gray-900">{giftCard.soldTo.name}</div>
                            <div className="text-xs text-gray-500">
                              {giftCard.soldAt && new Date(giftCard.soldAt).toLocaleDateString('zh-CN')}
                            </div>
                            {giftCard.saleOrder && (
                              <div className="text-xs text-blue-600">
                                {giftCard.saleOrder.orderNumber}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">未售出</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {giftCard.redeemedBy ? (
                          <div>
                            <div className="text-sm text-gray-900">{giftCard.redeemedBy.name}</div>
                            <div className="text-xs text-gray-500">
                              {giftCard.redeemedAt && new Date(giftCard.redeemedAt).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                        ) : giftCard.assignedTo ? (
                          <div>
                            <div className="text-sm text-blue-600">
                              🎯 指定给: {giftCard.assignedTo.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {giftCard.assignedAt && new Date(giftCard.assignedAt).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">未兑换</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(giftCard.validUntil).toLocaleDateString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => viewGiftCardDetails(giftCard)}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>

                          {/* 指定用户按钮 */}
                          {(giftCard.status === 'GENERATED' || giftCard.status === 'SOLD') && (
                            <button
                              onClick={() => openAssignModal(giftCard)}
                              className="text-green-600 hover:text-green-900"
                              title={giftCard.assignedToId ? "重新指定用户" : "指定兑换用户"}
                            >
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </button>
                          )}

                          {/* 取消指定按钮 */}
                          {giftCard.assignedToId && giftCard.status !== 'REDEEMED' && (
                            <button
                              onClick={() => unassignGiftCard(giftCard)}
                              className="text-orange-600 hover:text-orange-900"
                              title="取消指定用户"
                            >
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7a4 4 0 11-8 0 4 4 0 018 0zM9 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          )}

                          {/* 撤回兑换权按钮 */}
                          {giftCard.status === 'REDEEMED' && (
                            <button
                              onClick={() => revokeGiftCard(giftCard)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="撤回兑换权"
                            >
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                              </svg>
                            </button>
                          )}

                          {/* 删除按钮 */}
                          <button
                            onClick={() => deleteGiftCard(giftCard)}
                            className="text-red-600 hover:text-red-900"
                            title="删除礼品卡"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <GiftIcon className="mx-auto h-12 w-12 text-gray-300" />
              <p className="mt-2 text-sm text-gray-500">暂无礼品卡记录</p>
            </div>
          )}
        </div>

        {/* 生成礼品卡模态框 */}
        {showGenerateModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">生成礼品卡</h3>
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">面值 (USDT)</label>
                  <select
                    value={generateForm.faceValue}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, faceValue: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {supportedValues.map(value => (
                      <option key={value} value={value}>{value} USDT</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">数量</label>
                  <input
                    type="number"
                    min="1"
                    max="1000"
                    value={generateForm.quantity}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, quantity: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">最多1000张</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">有效期 (天)</label>
                  <input
                    type="number"
                    min="1"
                    max="3650"
                    value={generateForm.validDays}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, validDays: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">批次名称</label>
                  <input
                    type="text"
                    value={generateForm.batchName}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, batchName: e.target.value }))}
                    placeholder="可选，用于标识此批次"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">备注</label>
                  <textarea
                    value={generateForm.notes}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                    placeholder="可选，生成说明"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleGenerateGiftCards}
                  disabled={generating}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {generating ? '生成中...' : '生成礼品卡'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 指定用户模态框 */}
        {showAssignModal && selectedGiftCard && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  指定兑换用户
                </h3>
                <button
                  onClick={() => setShowAssignModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">
                  礼品卡: {selectedGiftCard.cardCode} ({selectedGiftCard.faceValue} USDT)
                </div>
                {selectedGiftCard.assignedTo && (
                  <div className="text-sm text-blue-600 mb-2">
                    当前指定给: {selectedGiftCard.assignedTo.name} ({selectedGiftCard.assignedTo.email})
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    用户邮箱
                  </label>
                  <input
                    type="email"
                    value={assignForm.userEmail}
                    onChange={(e) => setAssignForm({ ...assignForm, userEmail: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入用户邮箱"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    指定原因
                  </label>
                  <textarea
                    value={assignForm.reason}
                    onChange={(e) => setAssignForm({ ...assignForm, reason: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="请输入指定原因"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowAssignModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                >
                  取消
                </button>
                <button
                  onClick={handleAssignUser}
                  disabled={!assignForm.userEmail || !assignForm.reason}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  确认指定
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
