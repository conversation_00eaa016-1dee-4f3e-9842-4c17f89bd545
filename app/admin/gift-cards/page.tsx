'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import AdminLayout from '@/components/admin/AdminLayout'
import GiftCardDashboard from '@/components/admin/GiftCardDashboard'
import GiftCardBatchManager from '@/components/admin/GiftCardBatchManager'
import {
  ChartBarIcon,
  CreditCardIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

export default function GiftCardsPage() {
  const { data: session, status } = useSession()
  const [activeTab, setActiveTab] = useState('dashboard')

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session?.user || session.user.role !== 'ADMIN') {
    redirect('/admin/login')
  }

  const tabs = [
    {
      id: 'dashboard',
      name: '数据概览',
      icon: ChartBarIcon,
      component: <GiftCardDashboard />
    },
    {
      id: 'batch',
      name: '批次管理',
      icon: CreditCardIcon,
      component: <GiftCardBatchManager />
    },
    {
      id: 'settings',
      name: '系统设置',
      icon: Cog6ToothIcon,
      component: <div className="p-6 text-center text-gray-500">系统设置功能开发中...</div>
    }
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <div className="border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">礼品卡管理</h1>
              <p className="mt-2 text-gray-600">
                管理礼品卡的生成、销售、兑换和统计
              </p>
            </div>
          </div>

          {/* 标签页导航 */}
          <div className="mt-6">
            <nav className="flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* 标签页内容 */}
        <div className="min-h-[600px]">
          {tabs.find(tab => tab.id === activeTab)?.component}
        </div>
      </div>
    </AdminLayout>
  )
}
