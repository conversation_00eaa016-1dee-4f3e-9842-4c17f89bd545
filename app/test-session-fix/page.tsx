'use client'

import { useSession, signIn, signOut } from 'next-auth/react'
import { useEffect, useState } from 'react'

export default function TestSessionFixPage() {
  const { data: session, status } = useSession()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 测试useSession是否正常工作
    try {
      console.log('Session status:', status)
      console.log('Session data:', session)
      setError(null)
    } catch (err) {
      console.error('Session error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }, [session, status])

  if (error) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">SessionProvider错误</h1>
          <p className="text-red-700 mb-4">{error}</p>
          <p className="text-sm text-gray-600">
            请检查SessionProvider是否正确配置在app/layout.tsx中
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">SessionProvider修复测试</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">会话状态</h2>
          <div className="space-y-2">
            <div><strong>状态:</strong> {status}</div>
            <div><strong>会话存在:</strong> {session ? '是' : '否'}</div>
            {session && (
              <div className="mt-4 space-y-2">
                <div><strong>用户ID:</strong> {session.user?.id}</div>
                <div><strong>邮箱:</strong> {session.user?.email}</div>
                <div><strong>姓名:</strong> {session.user?.name}</div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">操作</h2>
          <div className="space-x-4">
            {!session ? (
              <button
                onClick={() => signIn()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                登录
              </button>
            ) : (
              <button
                onClick={() => signOut()}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
              >
                退出登录
              </button>
            )}
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-green-800 mb-4">✅ SessionProvider修复成功</h2>
          <p className="text-green-700">
            如果您能看到这个页面并且没有错误，说明SessionProvider已经正确配置。
          </p>
          <div className="mt-4">
            <a 
              href="/settings" 
              className="text-blue-600 hover:text-blue-800 underline"
            >
              返回设置页面测试
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
