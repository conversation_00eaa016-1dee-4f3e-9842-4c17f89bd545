比特市场 —— 基于USDT的去中心化C2C网上超市与二手交易平台说明书
一、比特市场白皮书
比特市场 —— 一个点对点的 USDT 消费场景
BitMarket: A Peer-to-Peer Consumption Ecosystem for USDT
广告词：赚U花U，在比特市场
（一）引言
在《比特币白皮书》中，中本聪提出了一个“点对点的电子现金系统”，旨在绕过金融中介，实现无需许可的价值交换。然而在比特币与稳定币的发展过程中，我们逐渐意识到：虽然稳定币，尤其是 USDT，已成为加密世界的“主流货币”，但其在真实世界中的使用自由度，仍然受到结构性限制。
主权货币体系对虚拟货币的压制从未停止。 监管政策、防洗钱规则（AML）、出入金管制与跨境支付审查，使得法币与稳定币之间的兑换变得敏感、低效，甚至在多个司法辖区下已被视为灰色行为。大量的加密资产无法在无需受控的情况下进入现实消费场景，成为“被困在链上的财富”。
同时，洗钱与非法资金流动的风险，也被各国监管机构作为打压稳定币流通与场外交易的主要理由。 法币-USDT 的互换渠道频繁被打击，出入金平台受到严格审查，银行账户面临冻结风险。这种系统性的阻断，直接限制了稳定币作为“数字现金”真正走入日常生活的能力。
当前，USDT 虽已承担起数字世界中的“结算货币”角色，广泛应用于交易所、链上协议和跨境资金流动，但它与现实世界商品交换之间，仍然缺乏稳定、高效、可信的桥梁。一个自由流通的货币，若无法用于日常消费，将始终停留在金融工具层面，而难以成为经济活动的一部分。
虚拟货币的存在来源于共识，这种共识存在于使用、接受、储存虚拟货币的人，而不需要经过反对虚拟货币的人同意。要实现现实的商品和虚拟货币流通最直接的方式，就是找到同样认可虚拟货币的买房和卖方，这是现实难以实现的。在深入了解一个人前，我们无法知道他是否认同虚拟货币。但是只要我们把所有持有虚拟货币的人连接起来，就能自发产生一个商品交换的社群，这个社群足以满足所有虚拟货币持有者的所有消费需求。
比特市场（BitMarket）因此诞生。它不是一个受主权体系监管的“金融平台”，也不是交易所的附庸。它是一个原生于加密世界的商品市场——一个完全使用 USDT 进行点对点交易的消费生态入口。我们希望，它可以为稳定币开辟一条不依赖法币、不借助银行、不诉诸监管许可的真实使用路径。
（二）背景与愿景
USDT 的出现，确立了稳定币在加密世界中的结算地位。但在现实世界中，它的“使用权”依然牢牢掌握在法币体系手中。 每一次入金与出金，都要穿越银行审查、KYC认证和反洗钱机制；每一笔兑换行为，都面临地域政策、平台许可与资金监控的重重过滤。
这种机制，使得USDT虽然在账面上“稳定”，却在使用上极不自由。对持币用户而言，真正的障碍不在链上，而在链下 —— 他们被迫依赖一种受主权货币体系支配的“许可使用权”。
我们相信，加密货币不应只是一种“投资工具”，而应回归其货币本质 —— 成为无需信任中介、可以直接交换商品和服务的媒介。 比特市场（BitMarket）应运而生，目标是建立一个绕开法币互换路径的USDT消费场景。
在比特市场中：
• 用户不需要把USDT换回法币，就可以直接购买商品；
• 所有交易以USDT为支付媒介，无需接入银行、支付机构或监管清算通道；
• 整个平台跳过“出金”这一步，直接构建“使用”的闭环。
这不是对传统电商的加密化仿造，而是一种交易逻辑的革新。 比特市场是一个实验场，它验证的是：当稳定币不再依赖法币，它是否能真正成为一种可用的货币。
（三）比特市场的发展路径
比特市场的发展可划分为三个阶段：从冷启动的实验性生态起步，经历中间人驱动的信任裂变，最终迈向自动化与全球化的商品交换网络。每个阶段都围绕不同的核心目标展开，逐步扩大用户规模，提升交易效率，并推动平台信用体系与信任机制的自我进化。
1.阶段一：冷启动 —— 玩家试运营
在平台上线初期，为保障商品丰富度与交易体验，比特市场将采取“自营+自由上架”双轨模式启动。平台团队将主动采购商品，借助淘宝、拼多多、京东等主流电商渠道以人民币完成采购，并直接向以 USDT 支付的用户发货。通过这种“平台垫付法币、用户支付稳定币”的方式，初步构建起一个合法合规的资金闭环，也有效解决了初期的供货来源问题，为后续运营奠定了基础。
本阶段采用极简模式快速上线，目标是迅速形成真实交易场景，验证 USDT 商品交换的可行性。平台将放宽入驻门槛，允许所有用户自由上架商品，不限商品类型（包括新品、二手、虚拟与实物商品），鼓励玩家参与内容建设，带动生态活跃。
为了降低使用门槛，平台设定统一手续费为最低 0.5 USDT，吸引用户尝试交易体验。所有交易通过用户之间的币安账户直接转账完成，无需链上操作，从而大幅降低摩擦与延迟。早期积累的真实交易记录将用于建立用户的信用评级、评价体系与风控模型，为下一阶段的“信任裂变”打下基础。
比特市场冷启动的核心不在于技术，而在于“找到第一批愿意收 USDT 的卖家”。这类人往往已具备数字货币认知甚至交易经验，具备现实商品或服务的输出能力。例如，银河放映厅老板曾主动提出用 USDT 购买网飞服务，AppleP12 签名师接受 USDT 支付，“Andy｜点晴”等币圈博主经营民宿也可直接接入。这些币圈从业者本就拥有现实需求和接受能力，是冷启动阶段最具潜力的合作对象。平台的首要任务，是识别他们、连接他们，并将他们的商品与服务纳入平台体系，构建出首批“真实 USDT 消费场景”。
为营造交易活跃氛围与冷启动效应，平台可主动模拟交易行为，通过发布求购单或虚拟买入，制造交易繁荣的感知，打造“倒U”“低吸高卖”等典型案例，引导用户模仿参与。例如，可设置人设“倒爷”，在币安广场发布内容，展示如何倒卖游戏点卡、代购 Netflix 会员、远程充值话费等方式赚取 USDT，吸引用户跟单模仿。同时，鼓励用户之间进行自买自卖练习与互评，快速积累活跃记录与评价数据。围绕这些交易场景，可策划一系列统一风格的传播文案，如“刚在比特市场倒卖游戏点卡赚了15U”“帮人远程注册 Netflix 一小时轻松赚20U”“只要你肯接单，就有人发 USDT 给你”“别再只囤币，比特市场可以直接花 USDT 买东西”等，在币安广场、Telegram 群、推特、Reddit、小红书等社区渠道进行发布，形成一致性认知。平台也将鼓励用户参与内容创作与社区分享，激活“社区自传播”效应，如模拟个人卖家在社区发帖“今天在比特市场卖掉旧耳机，净赚8U”，营造轻松赚钱的真实感与代入感，带动币圈用户围观参与。随着社区热度提升，平台可适时推出邀请返佣、交易带货激励等机制，构建用户共建生态，实现裂变式增长与收益共享。
2.信用裂变 —— 中间人担保机制
随着用户数量和交易频次的增长，平台将逐步转向以用户自发上架的二手商品交易和本地服务市场为主，依托中间人驱动的信任裂变，激发C2C网络效应，降低平台供货压力，提升市场活跃度，逐步实现商品生态的自循环。
当早期交易形成基础信任与流量后，平台进入“去中心化信任”的扩展阶段，引入中间人机制。中间人是一类特殊用户，他们在交易中担任第三方担保人，协助监督资金托管与货物交付，从中赚取佣金。
平台将建立中间人申请机制，参与者需缴纳一定担保金，并接受信誉评级体系监管。每笔交易中，买卖双方可自选中间人介入，平台不再直接托管资金，而是由社区成员构建“自治信任网络”。
为了激励更多用户参与平台建设，比特市场还将上线信誉积分系统、担保排行榜与“社区荣誉”机制。信任不再来自中心化平台背书，而由用户之间的社会关系网驱动形成裂变式传播，实现信任体系的横向扩展。
3.指数扩张 —— 自动化与全球商品交换平台
在完成基础信任网络与中间人自治模型构建后，平台将进入指数级增长阶段，通过技术集成与供应链拓展实现交易效率与规模双重跃迁。同时，平台将扩展更多币种和支付方式，开发安卓App，结合链上技术实现去中心化功能，引入KOL及社区推广，提升知名度与用户活跃度，最终拓展境外市场，打造国际化的USDT商品交换生态。
此阶段的重点是接入币安API，实现自动识别交易、自动放款与到账通知。用户无需上传截图或手动确认，平台将通过智能识别转账信息自动完成托管与结算，显著提升交易速度并降低纠纷率。
同时，平台将使用历史数据训练AI内容审核系统，自动识别违规商品与诈骗风险，在保障合规的前提下，大幅缩减人工审核成本，提高审核处理规模，保障用户安全。
业务形态也将扩展至B2C 与自营商品模式，引入可信厂商与批发商资源，为用户提供低价新品供给。平台不再仅是C2C的“自由市场”，而成为涵盖“批发、零售、二手、服务”的全链条商品交易生态。
在这个阶段，随着交易规模扩大与效率提升，平台将进一步降低手续费标准，通过网络效应拉动用户基数与商品供给指数级增长，最终形成一个高流动性、可信任、低摩擦的USDT消费终端系统。
（四）比特市场的合规性和隐蔽性
比特市场的核心变革，在于让USDT从“兑换人民币”的中介工具，跃迁为“直接购物”的消费货币。这种模式彻底绕开了法币结算体系，将链上的加密货币流通，嵌入到现实世界的商品物流体系中。USDT成为最终支付工具，不再是法币的影子，而是真正参与实体交易的货币单位。更关键的是，这种USDT的流通路径极具隐蔽性。平台不自建物流体系，也不集中存储商品，所有交易交付通过用户自发发货、快递配送完成。买家收到的包裹，与淘宝、拼多多等平台的个人订单毫无二致，收发信息自由填写，行为轨迹自然地嵌入快递体系的日常流中，难以被独立识别或定性为特殊行为。商品来源也同样去中心化。用户既可以出售闲置物品，也可以通过传统电商平台下单转售。这意味着，商品的采购渠道与发货行为，与整个零售系统保持一致，无需依赖特定供应链，也无需额外说明交易性质。在监管视角下，整个交易链条未出现任何明显异常信号，平台行为不涉及敏感资金流，用户行为不涉及高频交易或大宗调拨。在这种架构下，USDT的流通已不再是金融行为，而是一种普遍的消费行为。这种“消费即合法”的模式天然具备合规性，既无非法集资之嫌，也无跨境汇兑之实。USDT的支付行为被包裹在真实的物流之中，交易行为被稀释在数亿次日常快递之内，合规且隐蔽，现实且去中心化。比特市场的本质，不是一个平台，而是一种机制的转变：它使数字货币真正嵌入人类生活，让虚拟货币实现与现实经济的自然耦合，让交易回归“交换”的本源，而非“兑付”的中介。这一转变，将重塑我们对合规、对流通、对金融监管边界的认知。
（五）核心功能与业务流程
1.用户身份与注册
注册时提供邮箱验证，邮箱作为通知渠道，并填写币安ID和BNB chain钱包地址作为收款信息。
交易资金转账在币安账户间进行，避免链上复杂操作，保障安全与稳定。
后续可能支持手机验证码验证，加强安全防护。
2.商品发布与展示
卖家注册并填写币安ID、bnb链钱包地址后，可发布商品，填写价格（USDT计价）、描述、图片等信息。
平台支持人民币标价自动换算功能。商家在发布商品或服务时，可选择输入人民币价格，平台将实时爬取USDT汇率（参考币安USDT/CNY交易对或主流OTC中间价），自动换算为USDT价格并展示给买家。
平台将要求用户填写所在城市信息，作为推荐与物流估算依据。该信息将用于本地商品优先推荐、邮费预估与“同城自提”功能匹配，有助于优化交易体验并推动区域用户活跃度的自然形成。
商品可分为新品超市商品和二手闲置商品。
商品标题中需明确支持的付款方式，仅限USDT。
所有商品发布前将经过平台AI自动审核，包括图片识别与文本敏感词过滤，以保障平台合规安全。在平台初期阶段，所有商品发布、线下服务挂单和评论内容将实行100%人工审核，以确保内容安全、合法合规，重点防控常见灰产类型，如虚假商品、代充值、博彩引流、账户买卖、数字货币兑换通道等。一经发现此类内容，平台将立即下架商品并对相关账号执行封禁处理，已缴纳的担保金将视情节严重程度予以部分或全部扣除。待平台商品数量和用户活跃度提升后，将逐步引入AI自动审核与人工抽检相结合的机制，实现效率与安全的平衡，并以早期积累的数据训练模型、优化风控体系，最终建立高效、可信的内容审核机制。
3.交易流程
本平台所有买卖双方的交易均基于平台托管账户进行，平台承担交易中介及纠纷协调责任，保障交易安全与效率。具体流程如下：
（1）发货地址填写与自动邮费估算机制 为提升交易效率与保障信息安全，平台在商品发布与下单流程中引入发货地与收货地邮费自动估算机制。流程如下：卖家在上架商品时，需填写实际发货地址（精确至省市区）。买家在点击“拍下”商品前，需输入收货地址（支持选择或填写），系统根据双方地址计算快递费用，调用快递API接口进行邮费预估。为保障公平交易，平台默认采取邮费由买卖双方各承担50%的规则。系统将在卖家填写的商品价格基础上，自动加上预估邮费的一半，得出买家最终应支付的USDT金额。
（2）买家拍下并支付后，平台生成该订单的唯一编号（OrderID）。
（3）买家需先完成支付，平台确认收款后，买家才能向卖家发起聊天，系统在对话窗口向买家披露卖家的联系方式（如联系邮箱），以保护卖家隐私，避免线下绕单风险。聊天消息同步至绑定邮箱 买卖双方的聊天消息会同步发送至用户绑定的邮箱，确保用户即使不常登录网站，也能及时收到沟通提醒和订单更新，从而提升响应效率和交易成功率。用户可在平台设置中自由开启或关闭该邮件通知功能。
（4）买家支付USDT至平台托管账户 买家通过币安账户向平台托管账户支付约定金额的USDT，需在备注中填写订单编号，或在平台页面上传转账截图及交易哈希（TxID）以供自动识别。平台通过币安API验证到账信息。
（5）平台确认到账并通知卖家发货 平台确认买家付款到账后，将买家收货地址发送给卖家，并提醒卖家尽快发货。卖家须在72小时内完成发货并上传快递单号。
（6）卖家上传物流单号并绑定物流信息 卖家填写快递公司及单号，系统自动绑定物流查询接口，实时跟踪发货状态。如超时未发货或无物流更新，系统将提示买家，并可启动“未发货退款”流程。
（7）买家确认收货或系统自动确认 买家收到货物后，可在订单详情页点击“确认收货”，并上传开箱视频作为凭证。若买家在收货后7天内未确认，系统会提醒；满30天未确认，系统将自动视为收货成功。
（8）平台结算USDT给卖家并扣除平台服务费 确认收货后，平台将扣除相应服务费后，将交易款项打至卖家绑定的币安账户。如发生纠纷，相关资金将被冻结，由平台介入仲裁处理。
（9）交易完成后的评价与信誉管理 交易结束后，买卖双方可在订单页面进行评价，上传图片或视频凭证。评论可设为公开或仅平台内部可见，并纳入信誉积分计算。好评将提升用户信誉积分，恶意差评将被平台稽核并记录。
4.收入类型、计算方式及收取方式
交易服务费（平台抽成） 类型说明：平台对每笔商品交易收取固定或按比例的服务费。
计算方式：
交易金额 ≤ 50 USDT：固定收取 0.5 USDT
交易金额 50–100 USDT：收取 1 USDT
交易金额 > 100 USDT：按1.5%比例收取
收取方式：从买家支付的USDT中自动扣除，平台在放款给卖家前扣除平台费，差额打至卖家币安账户。
中间人担保佣金分成 类型说明：中间人为交易担保可收取一定担保佣金，平台从中提取分成。
计算方式（示例）：
佣金=交易服务费-平台佣金=（1-20%）服务费
交易金额 ≤ 50 USDT：佣金是 0.4 USDT，平台费是0.1USDT
交易金额 50–100 USDT：佣金是 1 USDT，平台费是0.2USDT
交易金额 > 100 USDT：佣金是1.2%比例收取，平台费是0.3%
收取方式：
担保佣金由买家支付，平台在交易完成后从中间人的担保金中直接扣除分成，剩余佣金划归中间人。
被扣除的佣金将导致中间人担保金逐渐减少，若余额不足将无法继续担保交易，需补充保证金。
商品曝光/置顶增值服务 类型说明：卖家可付费获得商品首页推荐、分类置顶等推广位。
计算方式：
首页置顶：5 USDT/天
分类推荐：2 USDT/天
收取方式：卖家在开通推广服务时预付USDT，直接从其绑定的币安账户或担保金余额中扣除。
会员订阅服务（可选功能） 类型说明：提供VIP功能如更低交易费、优先审核、流量扶持等。
计算方式：
普通会员：免费
高级会员：10 USDT/月
VIP会员：50 USDT/月
收取方式：按月或年从用户USDT余额或担保金中扣费。
广告合作收入（外部项目或品牌） 类型说明：接受币圈项目或品牌合作广告，如Banner位、推广页。
计算方式：
按投放时长或展示位定价（如500 USDT/月）
收取方式：与广告主签约后，通过USDT支付，打入平台收入账户。
以上收入项目可实现平台可持续盈利，尤其担保机制可建立可控风险与自然流动的收入池。如需，我可以绘制收入流动图或撰写相应条款示意，是否需要我继续整理？
5.诚信与信用机制
双方在交易前均可查看对方的信誉值、担保金余额及历史评价，买方据此评估卖方的诚信与服务质量，卖方亦可参考买方的信誉情况以降低交易风险。信誉值体现交易双方的诚信记录，担保金余额显示其资金实力和风险承担能力，历史评价则提供真实的交易反馈和用户体验。透明的信息展示促进双方做出更明智的选择，增强交易安全感与互信，推动平台健康良性的发展。
用户初始信誉积分是30分，每成功完成一次交易可获得诚信点数，作为信用评价的参考。平台为每笔交易开放评论区，买卖双方可进行评价，评价内容将作为用户信用的重要参考依据，所有评论公开展示，提高交易透明度和社区监督效力。
平台鼓励上传开箱视频，作为货品质量与真实性的重要佐证材料。一旦发生纠纷，平台优先参考开箱视频及聊天记录判定责任归属，并作为交易信用的重要加分项。
引入等级与保证金机制，用户可缴纳保证金成为“中间人”（担保人），参与平台担保交易。中间人可获得佣金或提升信誉等级，负责担保交易安全，减轻平台运营压力。
信誉积分计算规则如下：成交一笔交易 +1 分，获得好评 +3分，获得差评 -3 分，被用户举报并核实属实信誉分减半
当用户信誉积分低于30，平台将冻结其发布与交易权限；多次被举报或涉及诈骗的用户将被列入黑名单，封禁账户。
修订后的信誉积分规则建议（价格映射）
事件	原积分变动	优化后变动方式（示例）
成功完成一笔交易	+1 分	+log₁₀(1+金额/10)，向上取整（例如：10U得1分，100U得2分，1000U得3分）
获得一次好评	+3 分	+25分，依金额分档（可映射为0.51%的金额积分）
获得差评	-3 分	同上，按金额反向扣分（与好评对称）
被举报并属实	信誉减半	保留
开箱视频佐证	无	+1～2 分，作为辅助加分项
担保交易成功	无
6.担保金和中间人
担保金的定义与作用 担保金是用户为保障交易安全，增强信用而预先充值到平台的资金余额。担保金可用于支持直接消费和担保交易，起到资金保障与风险防范的作用。
担保金充值与账户管理
用户可自主选择充值担保金，充值金额无下限，但建议满足常用交易额度需求。
充值的担保金存放于平台专属托管账户，与交易资金分开管理，确保安全。
用户可随时查询担保金余额及流水明细。
担保金余额可以用于直接购买商品或支付担保交易所需的资金担保。
担保金消费方式
买家消费担保金：
买家购买商品时，可选择使用担保金余额全额或部分支付商品及邮费金额，若余额不足，需额外转账补足差额。
使用担保金消费时，交易流程与普通支付流程一致，平台托管并确认资金后通知卖家发货。
中间人担保交易：
中间人指参与担保服务的用户，需缴纳担保金并达到信誉等级要求。
中间人进行担保交易时，担保金余额必须大于或等于被担保的交易金额，确保有足够资金覆盖担保风险。
中间人担保的资金由担保金账户冻结，直到交易完成或纠纷解决后解冻或结算。
担保金与信誉等级的关系
担保金余额和信誉等级是成为有效中间人的双重条件。
信誉等级通过交易次数、好评数、差评数及历史担保表现综合评定。
信誉等级较低或担保金余额不足者无法参与担保服务。
担保交易流程
买卖双方同意由中间人担保，平台确认中间人资格及担保金余额。
买家将货款支付至平台托管账户，中间人担保金相应金额被冻结。
卖家发货，上传物流信息，买家确认收货后，平台释放资金：卖家收到货款，中间人担保金解冻。
平台从担保金中扣取服务费，担保金不足以中间人继续进行担保的，中间人应该补充担保金。
如发生纠纷，平台介入仲裁，担保金可根据判决结果扣除赔偿或退还。
担保金提现与退款
用户可随时申请提现剩余担保金，平台扣除平台费用后转账到用户币安账户，但需保证当前担保交易资金冻结余额不受影响。
交易成功后，中间人被冻结的担保金部分自动解冻，用户可自由支配。
担保金风险控制
平台监控异常担保行为，防范恶意担保、欺诈等风险。
对违规或被举报的担保用户，平台有权冻结担保金并限制参与担保交易。
平台保留根据市场情况调整担保金要求的权利。
担保金佣金及激励机制
中间人为担保交易提供服务，平台按担保交易金额的一定比例发放佣金。
佣金与信誉等级挂钩，信誉越高，佣金比例越优。
通过积极担保服务，提升信誉等级，享受更多平台特权。
1. 平台技术架构
网站前端可参考咸鱼或淘宝，界面简洁实用，支持商品浏览、发布、搜索、聊天等功能。
后端需支持订单管理、支付确认、物流信息上传、用户信用积分等核心功能。
数据库选择关系型数据库（如MySQL）管理用户及订单数据。
服务器可采用海外云服务器（如AWS、亚马逊），避开国内备案和封锁风险。
反DDOS及安全措施将采用邮箱验证和后续手机验证码，初期避免高成本的防护方案。
8.售后处理流程
平台支持合理范围内的售后退换服务，保障买家权益，同时维护卖家正当利益。根据用户收到商品与否、问题性质及证据情况，售后流程细分如下：
（1）无理由退货（仅限已收货商品）
买家在确认收货或系统自动确认收货之日起 7天内，可发起无理由退货申请。需满足以下条件：
商品保持完好、未明显使用、不影响二次销售；
买家承担原交易中平台服务费及退货所产生的邮费；
平台审核通过后，买家寄回商品并填写退货运单号；
卖家确认收到退货商品并无明显损坏后，平台将扣除原平台费并将剩余USDT退回至买家账户；
如卖家未在5日内确认收货，平台可依据物流信息自动确认并完成退款。
此类退款不影响双方信誉积分，但滥用无理由退货行为将被平台记录，并可能限制后续申请。
（2）有问题退货（如商品损坏、描述不符、缺件等）
若买家在收货后发现商品存在质量问题、描述严重不符等情形，可申请问题退货，需满足以下条件：
必须提供连续、未剪辑的完整开箱视频，包含快递包裹外观、快递单号、拆封过程与商品状态全貌；
视频需能清晰识别商品与交易内容是否一致；
买家提交退款申请并上传视频及说明后，平台将通知卖家在48小时内回应；
若卖家认可问题或逾期未回应，平台判定为买家胜诉；
买家需退还商品并填写快递单号，待卖家确认收到后，平台退还USDT（扣除平台费，退还邮费视个案酌情）；
若卖家对退货提出异议，平台将介入仲裁，参考开箱视频与聊天记录判断责任。
（3）未收到货
若买家支付后72小时内卖家未发货，或物流单号无更新超过96小时，系统将提示买家申请退款：
买家可发起“未发货退款”；
经平台确认未发货属实，扣除平台费（0.5usdt）并将剩余USDT退回至买家账户；
卖家若多次未发货将被扣除信誉分，严重者封禁账户。
（4）恶意行为处理
如卖家寄出空包裹、砖头、报废品等恶意欺诈行为，买家必须提供连续、未剪辑的完整开箱视频，包含快递包裹外观、快递单号、拆封过程与商品状态全貌，平台一经核实将作出如下处理：
买家，扣除平台费（0.5usdt）并将剩余USDT退回至买家账户；
卖家立即封号处理，冻结担保金用于赔偿；
相关记录将同步至黑名单，禁止后续重新注册。
补充说明：
售后申请入口在订单详情页开放，系统将引导用户选择“无理由退货”或“问题退货”路径；
所有售后申请与对话记录将在后台备案，供平台仲裁参考；
退款处理周期自确认起3个工作日内完成；
若交易涉及中间人担保，平台将在退款完成后同时解冻担保金，并按规则处理佣金回滚。
2. 同城线下消费场景与流程
为拓展USDT在实体经济中的使用场景，比特市场特别开设“同城线下”服务板块，鼓励愿意接受USDT支付的本地商家入驻并挂单，用户可通过平台预约服务并线下到店消费，平台提供托管保障，确保交易可信可验。商家注册并通过实名认证，填写币安ID及所在城市信息。对于中间人、商家等高风险用户，启用KYC措施，如上传身份证照片和手持照。进入“同城线下”板块，商家发布可提供的商品或服务项目，如美发、按摩、餐饮、维修等，注明价格、服务时长、所在城市区级位置、可预约时间段及支持的USDT付款方式，但不允许披露具体地址和联系方式。所有挂单需通过平台AI与人工双重审核，禁止发布涉黄、涉赌、涉灰等违法内容，确保合规安全。
用户浏览同城商家的服务挂单时，可根据所在城市区筛选可消费项目并选择服务发起预约，平台生成订单编号。预约时平台明确提示详细地址及联系方式将在付款后显示，保护商户隐私。用户支付USDT至平台托管账户，备注订单号，并上传付款截图或交易哈希（TxID）。平台确认到账后，向商家推送预约信息，包括客户昵称、预约时间和服务内容，并提示商户做好服务准备。付款完成后，平台自动向买家推送商户的具体地址和联系方式，保障买家能顺利完成线下消费。用户按时前往商家实体门店享受服务。
为确保线下交易的简便性与可信度，平台采用“二维码核销”机制完成服务确认与资金结算。用户在线上预约并支付USDT后，平台生成唯一订单二维码，用户到店消费时向商家展示该二维码。商家通过平台提供的扫码工具或手机登录后台，扫描二维码后系统将自动核销订单，并视为服务完成。
核销即代表“确认发货”或“确认服务完成”，系统将自动发邮件通知平台或中间人进行放款操作。平台在扣除相应服务费后，将剩余USDT结算至商家绑定的币安账户。
平台在用户确认后，将扣除平台服务费后的交易款项打入商家绑定的币安账户。若用户在7天内未确认消费，系统会自动发送提醒，15天后默认视为完成，平台自动放款。
如用户对服务不满意或商家未履约，可通过平台提交申诉并上传相关图片或视频证据，平台客服介入核实，如确认属商家责任，则支持退款或赔偿，并计入商家信誉扣分。对多次违规的商家，平台将采取限制接单或封禁账户等措施，保障用户线下消费体验。
此外，为提升用户体验和商户价值，平台可支持团购券或套餐预购模式，用户提前购买服务券，平台生成二维码，用户到店扫码验证核销。对通过线下验真或评价优良的商家，授予“USDT友好商户”认证标识，并支持位置地图聚合展示，帮助用户更便捷地发现和选择本地商家，提升转化效率和活跃度。
10.管理员界面
包括封禁、查看商品组成、查看资金流动、查看c2c聊天页面是否违规
进行封禁和监管服务器正常运转
可显示服务器的工作情况
二、比特市场平台模块划分与搭建说明
用户端功能模块（用户系统与账户管理）
该模块是用户与平台交互的第一入口，承担账户创建、身份识别、初始信息绑定、安全接入等任务，需兼顾安全性、便捷性与拓展性。
MVP推荐开发功能清单
功能模块——是否纳入MVP——说明
邮箱注册与登录——✅——基础注册入口
填写邮箱、币安UID、USDT地址——✅——支撑交易收付款
用户城市选择——✅（简化）——同城筛选逻辑所需
商品发布（标题、描述、图片、USDT价格）——✅——商品展示基础
商品展示（列表页 + 城市筛选）——✅——浏览入口
买家下单 + 上传付款截图——✅——手动支付流程
卖家发货 + 上传物流信息——✅——支撑交易闭环
后台审核付款截图 + 放款——✅——替代托管自动结算
商品上下架 + 编辑——✅——卖家管理必备
买家确认收货——✅——手动收货流程
⚠️ 建议延后开发（非MVP）
功能模块——理由——信用积分、等级系统
数据积累后才能有效评分，前期无用——中间人机制——属于担保信任升级阶段
视频上传、自动汇率转换——属于体验优化
商品关键词搜索、分类推荐——需一定商品量基础
自动风控、地址伪装识别——前期风险低，手动足矣
私信系统、聊天窗口——可用邮箱联系或留言替代
（一）注册与登陆
3. 支持邮箱注册与登录，确保全球用户接入便利性
• 用户通过填写邮箱 + 密码完成注册，系统发送验证码至邮箱进行验证；
• 邮箱作为唯一登录凭据之一，用于找回密码、接收平台通知、交易凭证等；
• 登录时支持“记住账号”、“自动登录”与“邮箱验证码快捷登录”多种方式。
明白了，以下是对“注册应填写的信息”部分的细化描述，可整合进“用户注册与登录”模块的注册流程设计中：
2.用户注册应填写的信息（注册流程字段设计）
为确保用户身份完整、交易安全与平台功能顺利对接，注册流程中需收集以下关键信息：
（1）邮箱地址
• 用作登录凭证及通知通道，需通过验证码验证后方可提交；
• 邮箱唯一性校验，防止重复注册；
• 后续用于接收交易通知、申诉凭证、密码找回等。
（2）币安ID（Binance UID）
• 填写用户在币安账户中的唯一UID（纯数字），可用于建立信任机制；
• 系统进行格式校验，并提示用户前往币安后台复制UID；
• 高等级账户（如中间人、商家）绑定币安ID为强制项；
• 后续可通过币安OAuth接口实现自动获取UID。
（3）BNB Chain 钱包地址（USDT收款地址）
• 用户需填写一个有效的BEP20地址（BNB Chain），用于平台托管/放款/担保退还；
• 支持后续添加多个地址（含ERC20、TRC20等），注册阶段至少需绑定一个地址；
• 系统校验钱包地址格式合法性，并建议用户确认地址归属；
• 可设置“默认收款地址”，变更需通过验证码确认。
（4）地址信息（精确到区县）和默认收货地址
• 用户需填写当前所在城市及行政区（省/市/区/县）作为默认服务地址；
• 用于：
    ◦ 自动匹配同城自提与本地服务；
    ◦ 邮费模板估算；
    ◦ 用户群体城市画像与运营数据支持；
• 地址字段可手动修改，每月限制修改次数以防伪装刷单；
• 后续支持地图定位或自动识别当前IP所在城市作为初始建议值。
注册流程中应采用多步引导式填写（分页面或滑动面板），避免信息过载，提高填写完成率。所有字段均写入用户数据库，并开放后台导出与审核接口。
3.后续可以支持手机短信验证码登陆和OAuth授权登录（币安、Google等）
• 手机验证码登陆
• 提供OAuth第三方登录通道：
• 币安OAuth：通过官方授权页跳转，获取用户基础信息（如UID、邮箱）并自动完成注册与绑定；
• 后期可扩展支持Google OAuth：方便Web3用户一键注册登录；
• 第三方登录首次成功后，系统将提示绑定邮箱、设置密码、绑定USDT钱包等补全资料流程。
（二）基础信息展示与设置功能
1.基础信息展示与操作入口
“我”页面顶端展示用户头像、昵称、等级、信用积分、担保金余额等关键信息，并提供以下操作入口：
(1)头像与昵称
显示当前用户头像与昵称，点击任意一项可跳转至“编辑资料”页面； 用户可上传头像（支持裁剪、压缩处理），或使用系统默认头像； 昵称支持编辑，限制长度（如2~20字符），禁止重复与敏感词； 头像与昵称将在平台评论区、交易聊天、商品挂单中展示。
(2)信用积分 明确显示当前信用积分总分，例如“信用积分：87/100”，并附加图标表现（如星级、盾牌、绿色标识等）；
积分由系统自动计算，主要构成维度包括：历史成交完成率； 被投诉与仲裁次数； 用户评价中的好评比例； 账户注册完整度； 是否绑定币安ID与钱包地址； 积分过低将影响排序、展示、可挂单类型，系统会发出提示与限权警告。
(3)用户信用等级 显示当前等级，例如“Lv.2 普通用户”，下方显示成长进度条“完成3笔交易可升至Lv.3”； 等级影响范围包括可用功能权限、每日挂单数量、是否可申请中间人等； 等级系统建议采用5级结构（Lv1-Lv5），支持未来扩展“荣誉等级”或“信誉勋章”等玩法。
(4) 担保金余额
显示担保金余额（单位：USDT），例如“当前担保金余额：58.3 USDT”； 旁边提供“充值担保金”按钮，点击跳转至专门的充值界面； 担保金用于平台担保交易、订单押金、争议仲裁抵押等，余额不足时无法参与对应类型的交易； 系统支持担保金充值、冻结、扣除、退还等操作，并提供明细记录。
2.账户注册信息修改功能
点击“账户设置”按钮进入“注册信息管理”页面，用户可修改以下关键注册信息：
(1)币安UID
显示当前绑定的币安ID（可脱敏显示部分数字）； 提供“修改币安ID”按钮，用户可重新输入； 修改后需通过手机验证码验证，系统设定冷却期（如48小时内不可再次修改）； 修改币安ID会自动进入账户信誉复核阶段，避免频繁更换以伪造身份。
(2)绑定手机号
显示当前手机号（中间4位脱敏），提供“更换手机号”按钮；
更换流程：输入旧手机号验证码 → 输入新手机号 → 验证通过 → 替换绑定； 若原手机号无法接收验证码，提供“申诉更换”路径，由人工审核处理； 新手机号绑定后生效，用于登录验证、操作通知、风控核验等。
(3)绑定邮箱
显示当前邮箱地址（脱敏处理）； 点击“更换邮箱”按钮，执行类似更换手机号的验证流程； 新邮箱绑定后，用于接收平台通知、交易凭证、找回密码等核心功能。
(4)定位地址
显示当前定位地址（省、市、区县），如“广东省 深圳市 南山区”；
点击“修改地址”按钮，进入修改页面：可手动选择省/市/区，或使用IP智能识别初始建议； 可设置“常住城市”，用于匹配同城订单、自提服务、商家推荐等； 每月限制修改次数（建议：每月最多修改1-2次），防止频繁虚构所在地用于薅补贴/刷单。
(5) 默认收件地址
显示当前设定的默认收货地址（收件人、手机号、详细地址）； 支持添加多个地址、设置标签（如“家”、“公司”、“代收点”）； 用户可选择默认地址，每次下单时自动填充，减少操作； 提供地址编辑与删除功能，建议设地址长度、手机号格式与非法词限制。
3.中间人申请与接单管理功能
(1)申请成为中间人 若用户未申请中间人，将显示“申请成为中间人”按钮；
点击后进入申请页面，需满足以下条件：等级达到Lv3及以上； 绑定币安UID； 担保金余额达到指定数额（如 ≥ 100 USDT）； 完成实名认证或视频认证（可选）； 审核通过后，用户身份页添加“中间人”标识，并进入接单系统。
(2)“准备接单”按钮：进入活跃状态 成为中间人后，“我”页面出现“准备接单”按钮；
点击后进入“12小时活跃接单状态”：在此期间，用户将出现在担保订单候选列表中； 状态期间内可被系统指派接单，必须履约； 显示倒计时提示，例如：“接单中 · 剩余 9小时12分”； 用户可手动提前结束状态，设定冷却时间（如6小时内无法再次开启）； 若长时间不活跃或被多次拒单，系统将提示是否退出中间人身份或扣除担保金。
4.整体用户体验设计建议
统一视觉风格：头像、信用、等级等卡片式信息模块应保持高度统一，避免界面混乱；
分组折叠展示：如“账户资料”“接单设置”“担保信息”三组模块可折叠展开，提升移动端使用体验；
反馈机制明确：每次修改成功后应有确认提示，失败提示须说明原因（如格式错误、冷却期未满）；
信息完整度提示：提供个人资料完整度进度条（如85%），引导用户补全头像、地址、邮箱等内容；
安全设置入口：提供“安全设置”按钮，统一管理登录密码、双重验证、设备登录记录等。
（三）卖家商品发布与管理模块
该模块为用户提供商品上传、编辑、展示、上下架的全过程操作，支持商品属性细分、价格换算、多图上传与分类管理，是平台交易流转的基础功能。
1.商品发布流程设计
商品发布界面包含以下字段和交互逻辑：
（1）商品标题
字数限制（建议15~60字符），用于关键词搜索；
不可含敏感词，系统自动校验并提示。
（2）商品描述
文本框+图片插入（最多上传9张图）；
支持插入开箱图、使用视频（可选功能，适用于二手商品）；
（3）商品信息填写
引导用户明确标注商品关键信息。
成色：全新、九成新、有使用痕迹但不影响使用、影响使用但有收藏价值
品类：电子产品 / 衣物 / 数码配件 / 虚拟物品等（分类影响商品展示板块、推荐算法与平台审核路径。）
发货地址：用于支持同城交易或筛选
（4）商品价格输入与 USDT 自动换算
用户输入商品的原始定价（单位：人民币）；
系统自动调用实时汇率接口（或平台缓存汇率）换算为USDT并展示；
显示：“参考汇率：1 USDT = 7.25 RMB（更新时间 15:23）”；
用户可选择是否手动微调USDT价格，但建议保持同步（防止差价套利）；
系统每天定时同步主流平台（如币安）汇率，可配置波动阈值。
（5）库存数量与购买限制
支持设置库存数量；
（6）可以选择开启或者关闭商品私聊权限
可设置是否接受议价等选项（提高灵活性）
（7）物流设置
用户可选择：包邮 / 自提 / 买家付邮；
若为虚拟物品、线上服务等，可设为“无需物流”，只需确认交付截图或激活码。
支持设置可自提城市（与定位系统联动）。
（8）收款方式确认
选择平台担保收款或者中间人担保收款
默认使用用户绑定的币安id和USDT收款地址（支持修改）；
（四）商品与订单管理功能模块
4. 商品管理（我发布的 · 数量） 用户可在“我 > 我发布的”中查看所有上架与下架的商品，支持以下操作：
• 筛选功能：已上架/已下架筛选商品；
• 商品编辑：可修改标题、描述、价格、库存、分类、图片等基本信息；交易中商品限制编辑敏感字段（如价格、描述）；
• 图片管理：支持上传、删除、拖拽调整图片顺序；
• 上下架控制：
• 用户可手动上下架商品（交易中商品不可下架）；
• 平台可因违规、超时未发货等原因强制下架，并通过消息系统通知用户。
5. 卖家订单管理（我卖出的 · 数量） 展示用户作为卖家的所有订单
（1）分为'已付款待发货','已发货','已送达待收货','已收货待放款','申请退款中','退款待放款','已取消','异常'不同状态
每个订单可点击进入商品详情页，支持以下操作：
（2）查看买卖进度、订单状态、商品信息；
（3）联系买家（内嵌聊天或跳转私信）；
（4）发货操作（填写物流单号、确认发货）；
“卖家上传物流单号”这个功能本质上只是订单模块中的一个字段和操作接口，它不应单独作为独立模块划分。在MVP阶段，我们强调的是模块精简与功能闭环，因此这个功能应按以下方式处理：
✅ 卖家上传物流单号 —— 属于“订单管理模块”中的子功能
📌 功能定位
• 属于订单详情页中卖家的操作行为。
• 在“发货”环节，卖家填写物流公司与单号，便于买家跟踪。
✅ MVP实现要求
内容	实现方式
上传入口	卖家在订单详情页点击“上传物流信息”按钮
字段内容	输入框填写：① 物流公司名称（文本框）② 快递单号（文本框）
数据存储	绑定于该订单的字段，如：shipping_company 与tracking_number
状态变化	卖家上传后，订单状态可变更为“已发货”
展示位置	买家订单详情页显示物流公司与单号（仅文本展示）
后台功能	后台查看订单时能读取物流信息字段，便于售后处理
🚫 MVP阶段不做
❌ 不对接快递API（如顺丰/菜鸟）进行状态查询
❌ 不提供快递状态实时更新
❌ 不校验单号格式或物流公司名称合法性
❌ 不提供物流信息修改（上传后不可更改，后台人工介入）
可以在完成mvp阶段后优化
物流单号字段设计建议 可加在订单状态更新流程中：
字段名称：logisticsprovider、trackingnumber
状态同步：接入如快递100接口，状态写入 logistics_status
卖家填写后系统更新状态为“已发货”，买家端显示物流信息
📌 示例数据结构补充（订单表）
字段名	类型	说明
shipping_company	string	卖家填写的物流公司名称
tracking_number	string	卖家填写的快递单号
status	enum	状态变更为“已发货”后更新
总结：这是订单模块中的一个“发货字段”+操作接口”，无需独立模块设计，但需确保在订单详情页的操作链路中明确体现。
（5）查看/处理退款申请；
（6）订单评价（在“待评价”中出现）；
6. 买家订单管理（我买到的 · 数量） 展示用户作为买家的所有订单，分为：
• 分为'已付款待发货','已发货','已送达待收货','已收货待放款','申请退款中','退款待放款','已取消','异常'不同状态
每个订单可点击进入商品详情页，支持以下操作：
• 查看进度、订单状态、物流详情；
• 联系卖家；
• 确认收货；
• 提交退货申请并上传开箱视频；
• 订单评价（在“待评价”中出现）；
7. 评价管理功能（评价） 可查看并操作评价相关内容，分为：
• 我卖出的（数量）：对买家进行评价或查看对方评价；
• 我买到的（数量）：对卖家进行评价或查看对方评价； 支持星级评分、文字评价、图片上传，并记录评价时间、订单号等信息。
交易管理与资金托管模块
推荐的最小可行版本结构（精简后）
模块——是否保留——MVP说明
订单创建与编号——✅保留——基本状态+唯一编号
商品快照——✅保留——文本字段复制
平台直收托管——✅保留——币安API自动对账
支付确认机制——✅保留——自动确认+人工标记
交易状态管理——✅保留——简化版状态流
仲裁流程——✅保留——基础“申请-处理-放款”
平台手续费——✅保留——阶梯费率结构
中间人机制——❌移除——推迟至迭代版本
信用/积分系统——❌移除——后期引入风控模型
复杂退货细则——❌移除——先做人工干预
折价与调解机制——❌移除——后期补充
异常支付场景机制——⚠️保留关键场景——多笔支付/错地址可手动处理
✅ 四、建议的MVP开发顺序
8. 订单系统（编号+状态流+快照）
9. 商品系统（最简商品展示+冻结字段）
10. 平台收款托管系统（API拉账+订单绑定）
11. 买家付款 → 卖家发货 → 买家收货 → 平台放款
12. 后台订单管理（人工放款、确认收款、取消订单）
13. 简单仲裁系统（开箱视频+后台处理）
（一）订单创建与唯一编号生成机制
1.功能目的
在用户点击“立即购买”或“发起交易”后，平台需要：
（1）创建一条订单记录,每笔订单分配一个全局唯一、可追踪、易识别的编号；
（2）为订单分配唯一订单编号，用于后续所有交易追踪、沟通、仲裁；
（3）保存订单快照信息，防止商品修改后对订单内容造成影响；
（4）用于绑定整笔交易的所有状态与资金记录；
（5）前端展示、后台查询、仲裁追踪、资金流对账都依赖此编号。
2.建议格式：ODR20250702-134987-XY4D
字段——示例——含义
ODR——固定前缀——表示Order
20250702——年月日——表示订单生成日期
134987——用户ID后6位——增加可追踪性
XY4D——随机4位——保证同秒内不重复
3.每一笔订单用一个唯一的 order_id 绑定下表中的所有状态变更：
orderid（唯一不变)——状态——状态含义——状态切换条件
ODR20250702-134987-XY4D——PENDING_PAYMENT——订单已创建，等待付款——刚生成订单时自动设定
ODR20250702-134987-XY4D——TRANSFER_PENDING_CONFIRMATION——用户填写转账哈希，待平台确认到账——买家点击“我已付款”并提交 Tx Hash
ODR20250702-134987-XY4D—— ESCROWCONFIRMED——平台确认已到账，待发货 平台核验到账成功 ODR20250702-134987-XY4D—— SHIPPED——卖家已发货，等待买家确认收货 卖家上传物流信息或线下确认 ODR20250702-134987-XY4D—— RECEIVED_WAITING_RELEASE——买家已收货，等待平台放款 买家点击“确认收货”
ODR20250702-134987-XY4D—— COMPLETED——平台已放款给卖家 平台操作放款成功 ODR20250702-134987-XY4D—— REFUNDREQUESTED——买家申请退款，待平台判定 买家在发货前或收货后申请退款
ODR20250702-134987-XY4D—— REFUND_CONFIRMED——平台已同意退款，待退USDT 平台仲裁确认退款成立 ODR20250702-134987-XY4D—— CANCELLED——订单关闭 超时未付款 / 平台人工取消
（二）交易资金托管逻辑（USDT冻结、释放，币安API接入可选）
平台支持两种托管模式：
• 平台直接收款：用户将 USDT 转账至平台指定币安地址，无需上传交易哈希，平台通过自动对账或币安API确认到账，完成托管。
• 中间人担保交易：平台选派信誉等级达标的中间人托管本次交易，买家需向中间人转账，并提交交易哈希，由平台或中间人核验到账，确认后进入发货流程。平台在交易完成后控制放款或退币的方向。
✅ 核心区分逻辑：平台直收 vs 中间人担保收款
模式——支付方式——是否需要用户提交哈希——资金流路径——托管责任方
平台直收——用户直接转账到平台币安收款账户——❌ 不需要——用户 → 平台币安地址
中间人担保——用户转账至中间人账户——✅ 需要提交Tx Hash供平台核验——用户 → 中间人 → 平台——中间人托管，平台协调仲裁
1.平台直收机制（主流程）
适用于大多数普通商品交易，平台为唯一收款人。
（1）订单创建 → 平台分配币安收款地址（如统一地址或轮转地址）
（2）买家直接在币安或任意钱包向平台地址转账
（3）平台通过币安 API 或后台对账确认到账（无需Tx Hash）
（4）状态更新为“托管成功，待发货”
（5）后续发货、确认收货 → 平台从账户中放款给卖家
✅ 核心字段
字段——说明
（1）payment_method =PLATFORM_DIRECT——表示是平台直接收款
（2）usdt_amount——应收金额
（3）received = True / False——是否已到账
（4）binance_internal_id——币安 API 可返回的充值记录 ID（替代哈希）
（5）release_target_wallet——卖家收款地址，放款时使用
2.中间人担保机制（独立流程）
步骤为：
（1）订单创建 → 平台选定一个中间人账号（满足信誉/担保金要求）
（2）平台分配中间人钱包地址给买家付款
（3）买家转账后，必须提交交易哈希Tx Hash
（4）中间人或平台核实到账（金额、哈希、地址匹配）
（5）后续发货、确认收货 → 中间人放款给卖家
✅ 核心字段：
字段——说明
payment_method =ESCROW_BY_MIDDLEMAN——中间人担保付款模式
escrow_wallet_address——中间人提供的钱包地址
tx_hash_from_buyer——买家提交的付款哈希
escrow_received = True/False——是否确认到账
middleman_id——当前担保人身份
release_by =MIDDLEMAN——谁执行放款操作
escrow_release_hash——最终放款给卖家的Tx Hash
✅ 拓展建议：两种模式如何并行存在
在订单创建时，加一个字段标识使用哪种托管模式：
{
"order_id": "ODR20250702-XYZ",
"payment_method": "PLATFORM_DIRECT" | "ESCROW_BY_MIDDLEMAN",
...
}
后续流程与字段的逻辑依据此字段决定，状态流可以共用（如“待发货”、“待收货”、“待放款”等），但资金操作和验证机制完全分离。
（三）买卖双方支付确认流程与异常处理机制
• A. 平台直收模式下的流程与异常处理
• B. 中间人担保模式下的流程与异常处理
• C. 数据结构与字段设计建议（可供开发使用）
• D. 异常场景分类与处理措施（表格总结）
1.平台直收模式 —— 支付确认流程与异常处理
正常流程：
（1）用户点击“立即购买”；
（2）平台生成订单，分配收款地址（platform_wallet_address）；
（3）用户打开币安/钱包，扫描二维码，向平台地址转账指定金额；
（4）平台通过以下方式识别到账：
• 自动：API查询平台地址入账记录（如币安子账户、Webhook）
• 人工：运营后台输入 Tx Hash 或查账
（5）一旦到账：
• 状态变为托管成功，待发货
• 放入资金已托管列表，准备后续放款或退款
⚠️ 异常处理场景：
场景——描述——平台处理方式
用户忘记付款——用户未转账——订单设定超时取消（30分钟或1小时）
用户转账金额不足——少于应付款金额——标记“金额不足”，要求补差额；可人工或自动判断
用户转错地址——发给他人钱包——无法追回，提示“非平台地址，不负责赔偿”
入账金额无匹配订单——有转账，但找不到订单——生成异常交易记录，后台对账提示核对
短时间内多笔付款——重复支付——提示用户联系平台退款，需人工介入确认
2.中间人担保模式 —— 支付确认流程与异常处理
📌 正常流程：
（1）平台指定中间人，订单创建时分配其收款地址；
（2）买家将USDT转账至中间人地址；
（3）买家必须填写 Tx Hash 提交至平台；
（4）平台或中间人验证：
• 链上查找是否到账
• 是否金额正确
• 是否与订单匹配
（5）验证无误后 → 状态改为“托管成功，待发货”
⚠️ 异常处理场景：
场景——描述——平台/中间人处理
买家未付款也提交Tx Hash——虚假Tx Hash——查链无记录，判定欺诈，订单暂停、买家记录扣分
买家填写了错误的Tx Hash——查不到交易——提示重新提交
买家付款了但没提交Tx Hash——平台查不到订单——中间人或平台人工对账确认后人工绑定订单
买家转账金额不一致——多转/少转——通知补差或登记溢出，等待用户处理
14. 数据结构建议（新增字段）
无论是哪种模式，平台都应记录以下字段，用于处理支付确认逻辑：
字段名——类型——说明
payment_method——enum——PLATFORM_DIRECT /ESCROW_BY_MIDDLEMAN
payment_status——enum——UNPAID /PENDING_CONFIRMATION /CONFIRMED /FAILED
payment_timeout_at——datetime——自动取消订单的时间点
transfer_tx_hash——string——买家填写的哈希（中间人模式专用）
tx_verified_by——string——AUTO /ADMIN /MIDDLEMAN，谁确认的付款
tx_verified_at——datetime——确认时间
payment_error_reason——string——若失败，写明具体失败原因
4.异常情境处理机制（对照表）
异常编号——异常情况——检测方式——处理机制——前端提示
E1——买家超时未付款——当前时间 >payment_timeout_at 且payment_status=UNPAID——自动取消订单——“订单超时未支付，已关闭”
E2——提交伪造哈希——链上查无该哈希——标记异常订单，锁单，通知管理员——“转账未识别，请检查哈希”
E3——实际到账金额 ≠ 订单金额——对账差额 > 0.01USDT——通知用户补差，或后台操作退款——“金额不符，请联系客服”
E4——支付到账但无订单关联——钱包地址入账，但未绑定order_id——生成“无主交易”记录，人工处理
——不提示，后台处理
E5——用户误转错地址——付款地址非平台地址——平台无法处理，自动忽略——“未识别为平台收款地址”
✅ 建议平台后台支持的操作按钮
在后台“订单管理”中，应支持以下操作用于应对支付相关异常：
• 🟩 标记已付款（用于人工确认到账）
• 🟥 驳回付款哈希（伪造或不符）
• 🟧 添加链上交易记录 → 绑定到订单
• 🟦 延长支付时限（如用户联系客服申请延期）
以下是“交易纠纷仲裁流程”的详细细化设计，适用于你的比特市场平台，涵盖平台介入流程、中间人机制、证据要求、处理逻辑与信用影响，逻辑清晰，便于后续产品设计与技术开发。
（四）交易纠纷仲裁流程（细化版）
1.触发条件
交易中出现以下情况之一，即进入纠纷仲裁流程：
场景编号——触发场景——描述
A1——买家未收到货——超过约定时效仍未收货，卖家未提供有效物流证明
A2——买家收到货有严重问题——破损、货不对板、虚假商品等，买家提交开箱视频
A3——卖家认为买家恶意退货——买家提出无理由退款但物品损坏或调包
A4——支付/转账争议——买家表示已付款但卖家未收到（币安转账）
A5——双方长期未响应——任意一方超过48小时未处理关键步骤，如发货、确认收货等
2.申诉发起流程
（1）买卖双方皆可发起申诉
登录后在订单详情页点击【申请仲裁】
需选择纠纷类型，填写描述，上传证据（开箱视频、对话截图、快递单、转账记录等）
（2）平台或中间人收到申诉通知
平台系统生成仲裁单号，自动暂停该订单流程（如冻结托管金）
3.平台仲裁流程
步骤——操作内容——处理主体——时限
1——系统通知双方提交补充证据，双方提交补充材料，限期1日内完成——买家、卖家——24小时
2——平台/中间人审查所有证据——指定仲裁人（管理员或中间人）——48小时
3——发布仲裁结果并执行资金分配逻辑——平台系统执行（放款/退款）——24小时
4——系统记录仲裁结果并更新信用评分——平台系统——自动
4.中间人参与机制（担保交易专用）
• 谁担保谁负责：若该笔交易由中间人担保（担保金覆盖交易金额），则平台将首先通知中间人介入判定。
• 中间人权责：
• 必须在48小时内作出仲裁决定
• 仲裁过程中不得偏袒任何一方，需上传理由说明
• 若连续多次被平台判定“仲裁不公”，将被扣分/清退
5.仲裁依据与处理逻辑（样例）
场景——判定依据——仲裁逻辑
买家主张未收货——无物流单号或物流信息无更新——平台支持买家，退款
卖家上传有效物流单号，显示已签收——无签收截图，买家否认收货，未提供任何视频证据——平台支持卖家，释放资金
买家提供完整开箱视频，物品破损——视频含快递单、无剪辑、破损清晰可见——平台支持退货退款，卖家承担邮费
买家未提供完整开箱视频，物品破损——有物流单号，无视频或视频剪辑严重——平台支持卖家，资金释放给卖家
卖家提供出库与质检视频，物品无误——买家视频缺失或剪辑痕迹明显——平台支持卖家，资金释放给卖家
买家退回空盒或调包——视频显示退货与发货重量/品类严重不符——平台支持卖家，冻结买家账户，信用分大幅扣减
卖家发错商品（型号/颜色/数量错误）——买家提供完整开箱视频、对话记录可证明——平台支持退货退款，卖家承担来回邮费
卖家虚假发货（发空包或纸片）——买家视频显示包装异常、内无商品，配合物流重量不符——平台支持买家，全额退款，卖家封禁
买家主张商品为假货——买家提供鉴定报告、开箱视频等证据——平台支持买家退货退款，并视情节永久封禁卖家
买家恶意多次申请无理由退货——多次开箱无视频或虚构理由——平台记录异常行为，限制账户退款次数，提升信用风控等级
卖家迟迟不发货——超出发货时限48小时未上传物流单——平台自动取消订单并退款买家，卖家扣除信用分
买家收到货后超期未确认收货——商品已签收超过7天无反馈——平台自动确认收货，释放资金给卖家
买家提交的视频系伪造（非该订单）——视频日期与快递单号对不上，或平台系统查重属旧视频——平台支持卖家，冻结买家账户
买家撤销纠纷申请——买家手动取消申诉，或与卖家达成一致解决——系统终止仲裁，恢复正常结算流程
（五）退货处理完整逻辑
1.无理由退货（买家原因）
适用情形：买家在签收商品后7日内发起退货，理由为“不喜欢”“不合适”等非质量问题。
平台处理流程：
流程节点——处理方式
买家申请退货——卖家同意退货，买家承担来回运费
买家寄回商品——卖家签收确认无误
平台放款——扣除平台服务费后，将剩余金额退还买家，运费不退
2.卖家责任退货（质量问题、发错货、描述不符等）
• 适用情形：买家提供证据证明商品存在重大瑕疵或卖家责任问题，选择退货而非折价。
• 平台处理流程如下：
步骤——说明
15. 买家申请退货并提交证据——开箱视频、聊天记录、商品实拍图等
16. 卖家确认责任或平台初步认定责任归属——如发错货、严重损坏、描述严重不符等
17. 买家寄回商品——上传物流单号，平台暂不释放资金
18. 卖家签收商品后，平台提示如下：——“您须在24小时内补贴来回运费，否则平台将认定您拒绝履行责任”
补贴运费判定逻辑：
卖家行为——平台处理——最终结果
卖家24小时内补贴来回运费并上传凭证——平台核实后，扣除平台服务费，剩余款项退还买家——退货成功，交易完成
卖家未补贴运费或消极不回应——平台支持买家留置商品+退款——买家获得退款，商品不退还
卖家仅补贴部分运费或拒补贴——平台视为违约——可从其担保金中划扣、降低信用评分、暂停交易权限
⚠️ 平台有权根据商品价值、交易记录和责任判定酌情调解，但原则上在卖家责任明确时，运费也应由卖家承担。
3.折价处理（买卖双方协商一致）
• 适用情形：商品存在轻微问题，但买家愿意接受，双方协商折价处理。
• 平台处理流程：
步骤——说明
双方协商达成一致——确认折价金额
平台介入协助划拨——折价金额扣除平台服务费后退还给买家，剩余打给卖家
交易完成——商品不退回，纠纷关闭
（六） 平台手续费、担保佣金、结算系统（细化设计，适用于开发）
19. 平台手续费计算方式
（1）收取说明
交易金额范围（USDT）——计算方式——收取说明
≤ 50——固定收取 0.5 USDT——交易时从买家支付金额中自动扣除，结算时平台扣除手续费，剩余打给卖家
50 < 交易金额 ≤ 100——固定收取 1 USDT——同上
100——按 1.5% 比例收取——同上
（2）中间人担保佣金分成
交易金额范围（USDT）——交易服务费（平台总手续费）——中间人佣金（约80%）——平台佣金（约20%）
≤ 50——0.5 USDT——0.4 USDT——0.1 USDT
50 < 交易金额 ≤ 100——1.0 USDT——0.8 USDT——0.2 USDT
100——1.5% × 交易金额——1.2% × 交易金额——0.3% × 交易金额
2.平台手续费逻辑
交易场景——收费对象——收费方式——计算逻辑——特别说明
正常交易——卖家——从应收款中自动扣除——按交易金额阶梯计算平台费（见前文标准）——无特殊说明
无理由退货——买家——从退款中扣取平台手续费——退款金额 = 商品金额 – 平台费——买家承担平台费， 退货商品归还卖家；平台费不退；邮费由买家自行承担
卖家责任退货（质量问题）——卖家——卖家补充平台费与运费，卖家承担平台费——按交易金额阶梯计算平台费（见前文标准）——买家退款 = 商品金额全额，卖家需在24小时内补交运费和平台费至平台，否则视为违约，买家获得商品与退款
协商折价（轻微问题）——折价后的价格退款——价后的价格中扣除平台费，再打给买家——平台费 = 差价 × 平台费率（见前文）——差价部分仍按比例收取平台费，系统处理后分别划拨至买卖双方
3.中间人佣金逻辑
交易场景——收费对象——收费方式——计算逻辑——特别说明
正常交易——买家——从买家付款中直接扣除总服务费，平台按比例拆分——按交易金额阶梯计算服务费（见前文标准），其中佣金=总费×80%，平台费=总费×20%——中间人正常履约，系统自动结算：卖家到账 = 商品金额 – 总服务费
无理由退货——买家——从买家退款中扣除总服务费，平台按比例拆分——总服务费 = 按交易金额阶梯计算，佣金=总费×80%，平台费=总费×20%——担保履约完成，视为已服务；商品退还卖家，平台费与佣金不退；邮费由买家承担
卖家责任退货（质量问题）——卖家——卖家需补交平台费与佣金，按原服务费拆分——总服务费 = 按交易金额阶梯计算；卖家补交，佣金=总费×80%，平台费=总费×20%——卖家需在24小时内补交平台费与邮费；否则平台默认买家胜诉，商品与退款归买家，平台可扣违约金
协商折价（轻微问题）——买家（差价部分）——差价退款中扣除服务费，平台按比例拆分——平台费 = 差价×费率（按前文阶梯算法）；佣金=服务费×80%，平台费=服务费×20%——差价部分仍计服务，平台与担保人照常分成，系统按协商价退款
平台管理后台模块
以MVP需求优先级排序如下（前3项建议开发，后面为未来迭代顺序参考）：
✅ 订单管理后台（必须）
✅ 商品上下架管理（必须）
✅ 用户管理后台（封禁）
非MVP模块（建议按顺序迭代）：
• 内容合规处理与违规商品下架记录（建议）
• 商品审核系统（AI + 人工）
• 商品内容编辑
• 商品审核后台（合并上项）
• 商品展示、关键词搜索
• 黑名单与违规处理
• 担保金与平台资金管理
• 仲裁管理、举报处理
• 数据统计与运营报表
• 多角色权限体系
（一）订单管理后台
1.MVP阶段开发说明
不实现自动放款（需管理员操作确认放款）
不做聊天系统，只链接现有客服或聊天记录URL
仲裁仅人工判断，不做上传证据/投票机制
状态变更无审核，仅操作确认提示
用户评价系统、信用积分放入二期
2.功能列表（MVP版本）
功能模块——子功能——MVP开发要点
订单列表——多维筛选——可按订单号、状态、时间筛选
订单详情页——订单信息查看——显示买卖双方、商品、金额、状态、聊天记录链接、截图链接
状态修改——修改状态——管理员可人工修改为“已发货”“已收货”“取消订单”
放款与退款——手动执行——支持后台打款或手动标记为“已放款”“已退款”
上传凭证——管理员备注——支持上传TxID或填写退款说明
简易仲裁——强制退款 / 强制放款——不需要复杂证据，仅支持人工裁定
操作记录——显示变更历史——简易记录每条状态修改（状态前后+管理员）
3.字段简化说明（用于前后端接口与数据库），订单主要字段（后台页面展示用）：
字段名——类型——说明
order_id——字符串——唯一订单号
商品信息——JSON——名称、图、价格
买家信息——用户ID + 邮箱——用于识别与沟通
卖家信息——用户ID + 邮箱——用于识别与放款
订单状态——枚举值——待付款 / 待发货 / 已发货 / 待收货 / 已完成 / 已取消 / 异常
支付截图——图片链接——用户上传或系统记录
聊天记录链接——超链接——跳转至聊天后台
快递信息——文本——快递单号/公司（可选）
TxID——文本——后台手动填写放款TxID
管理员备注——文本——仲裁说明或特殊操作记录
4.页面与按钮设计（MVP精简）
（1）订单列表页（/admin/order/list）
支持筛选字段：订单号、状态（下拉选择）、创建时间段
显示字段：
• 订单号、商品名、金额
• 状态
• 买家邮箱 / 卖家邮箱
• 操作按钮：“查看详情”
（2）订单详情页（/admin/order/detail/:id）
页面信息块结构：
• 商品信息（标题+价格）
• 订单状态（当前状态 + 修改下拉菜单）
• 买家/卖家信息（含邮箱）
• 支付截图（可点击放大）
• 聊天链接（跳转至聊天后台）
• TxID输入框（放款后填写）
• 备注输入框（管理员说明）
操作按钮：
按钮	适用条件（订单状态）	功能说明
修改订单状态	所有状态	提供下拉菜单列出所有状态，管理员可手动切换，需弹出确认框防止误操作
放款	已收货待放款、退款待放款	放款完成后，点击“已放款”，弹窗填写TxID（或内部转账备注），变更为“已完成”
退款	申请退款中、异常	手动退款成功后，点击“已退款”，填写TxID（或转账备注），状态改为“退款成功”
取消订单	已付款待发货 或管理员手动（如异常未发货订单）	标记为已取消，填写TxID，并记录操作人与时间
查看交易明细	所有订单	展示订单创建时间、商品信息、交易金额、收付款方、物流、TxID等
修改物流信息	已发货、已送达待收货	支持编辑或补填物流单号、快递公司、发货时间
强制收货（这个按钮应该是到时间自动收货）	已送达待收货 且超时未确认收货	管理员可点击“强制收货”，更新为“已收货待放款”
强制退款	申请退款中 或 异常 状态下需特殊处理	直接跳过争议流程，标记为“退款待放款”
留言/备注	所有订单
5.数据库建议简化表结构（orders）
sql
CopyEdit
CREATE TABLE orders (
id BIGINT AUTO_INCREMENT PRIMARY KEY,
order_id VARCHAR(32) UNIQUE,
buyer_id VARCHAR(64),
seller_id VARCHAR(64),
product_info JSON,
amount DECIMAL(10,2),
status ENUM('已付款待发货','已发货','已送达待收货','已收货待放款','申请退款中','退款待放款','已取消','异常'),
pay_screenshot_url TEXT,
logistics_info TEXT,
txid TEXT,
admin_remark TEXT,
created_at DATETIME,
updated_at DATETIME
);
6.所有订单状态一览（供程序使用）
状态代码	状态说明
pending_payment	未付款（保留）
paid_pending_ship	已付款待发货
shipped	已发货
delivered	已送达待收货
received	已收货待放款
refund_requested	申请退款中
refund_pending	退款待放款
cancelled	已取消
exception	异常（需手动处理）
completed	已完成（平台放款完毕）
refund_success
（二） 商品上下架管理（必须）
1.商品上下架管理后台功能表格
按钮 / 功能项	适用条件	功能说明（开发说明）
查看商品详情	所有商品	展示商品图片、标题、价格、分类、库存、卖家ID、描述、发布来源IP、提交时间等
上架审核（审核通过）	待审核 状态商品	人工点击“审核通过”后，商品状态变为“已上架”，前台可见
上架审核（驳回）	待审核 状态商品	填写驳回理由（如涉嫌灰产、描述违规、图片不符等），商品状态变为“驳回”，前台不可见
强制下架	所有状态，尤指“已上架”商品	管理员可随时下架商品，记录下架原因，商品状态变为“已下架”，并通知商家
修改商品信息	待审核 或 驳回 状态商品	支持修改标题、描述、分类、价格等字段，仅管理员可见；记录操作人和修改时间
灰产标签标记	所有商品	用于标记灰产类型（如“虚拟号”、“走私电子烟”、“假物流”等），支持打标签并自动列入重点监控清单
搜索/筛选	全部商品	支持按关键词、商品ID、卖家ID、状态、发布时间、灰产标签等搜索与筛选
留言/备注	所有商品
2.商品状态设计（供前后端调用）
状态代码	状态说明	前台可见	说明
pending_review	待审核	否	商家发布后进入此状态，需管理员审核
approved	已上架	是	审核通过，商品可在前台展示与交易
rejected	审核驳回	否	管理员拒绝上架，填写驳回理由
force_down	已强制下架	否	商品违规、灰产、虚假信息等，后台强制下架
off_shelf	商家自主下架	否	商家自行撤销上架
3.人工审核流程建议（MVP可执行）
（1）商家发布商品
进入 pending_review 状态
（2）后台审核页面功能
审核通过 → 设为 approved
审核驳回 → 弹窗输入驳回理由，设为 rejected
标记为灰产 → 添加灰产标签，加入黑名单/重点关注商家列表
（3）已上架商品如被举报或AI识别异常
管理员进入后台页面 → 选择“强制下架” → 输入原因 → 状态设为 force_down
（三） 用户管理后台（封禁）
1.用户管理后台模块功能表（无实名认证版，MVP 适配）
功能项 / 按钮	适用对象 / 条件	功能说明（供程序开发使用）
查看用户列表	全部注册用户	展示用户ID、注册时间、绑定邮箱/手机号、币安UID、注册IP、最近登录IP、交易次数、评论数量
查看用户详情	任意用户	可查看该用户全部基本信息、历史订单、评价记录、登录日志（IP、时间、设备）等
封禁用户账号	任意用户	输入封禁原因与封禁时长（或永久），账号状态设为 banned，禁止登录、下单、评论等
解禁用户账号	被封禁用户	将账号状态恢复为 active，用户可正常登录与使用功能
灰产/风险标签标记	任意用户	给用户添加内部标签（如“涉嫌引流”、“倒卖”、“恶意退款”），供风控追踪与后台筛选
搜索/筛选用户	全部用户	支持按：用户ID、币安UID、注册时间、IP地址、封禁状态、灰产标签进行筛选
内部备注/留言	任意用户	管理员可对用户做内部备注，记录违规、申诉、投诉、客服沟通情况
非核心功能模块
（一）非核心功能模块的mvp审核
1.物流与收货凭证模块
功能	是否符合MVP标准	理由
城市定位功能（便于同城自提）即商品搜索yu	✅ 符合	和“选择城市”一致，简化版商品管理已有此字段
卖家物流单号上传接口	✅ 符合	可作为文本字段与订单关联，简单易实现
主流物流公司接口对接与状态查询	❌ 不符合	涉及外部API接入与维护，超出初期MVP范围
发货超时提醒机制与退款处理逻辑	❌ 不符合	自动化逻辑复杂，需定时任务/状态机控制，不符合MVP
买家上传连续无剪辑的开箱视频	❌ 不符合	视频处理要求高，内容较重，不适合初期实现
视频管理与订单绑定机制	❌ 不符合	同上，属于高级维权机制，非MVP必要闭环组件
✅ 仅保留“城市定位”“物流单号上传”作为MVP功能
2.评价体系与信用机制模块
功能	是否符合MVP标准	理由
交易完成后开启评价窗口（文字、图片、视频）	✅（部分）	文字评价可保留，图片、视频建议去除，前期简化
评论公开/私密选项	❌ 不符合	多层级控制逻辑，非必要
信用积分计算规则（好评率、纠纷记录）	❌ 不符合	涉及复杂逻辑与统计，不适合初期开发
举报机制与违规处理	❌ 不符合	涉及内容审核与仲裁，前期可暂不开放
用户等级与中间人资格挂钩机制
✅ 保留“交易后可写文字评价”
3.担保金与中间人系统模块
功能	是否符合MVP标准	理由
担保金账户管理（充值、提现、余额）	❌ 不符合	涉及用户钱包系统、资金冻结等，开发复杂
担保交易资金冻结与解冻逻辑	❌ 不符合	类似托管系统，已被排除在MVP外
中间人资格认证与信誉等级	❌ 不符合	需配套信用体系与审核系统，非初期重点
担保交易执行流程（纠纷介入）	❌ 不符合	执行复杂，含多角色、分歧处理机制，非必要闭环流程
中间人佣金结算与激励机制	❌ 不符合	依赖托管逻辑，不属于MVP范畴
中间人后台与转账提醒表	❌ 不符合	自动任务生成机制复杂，且中间人机制尚未落地
✅ 本模块整体建议 全部推迟，非MVP范围，暂不实现
4.消息通知与沟通模块
功能	是否符合MVP标准	理由
平台内私信系统（订单关联）	✅（建议保留）	核心沟通方式之一，可做简化版本：仅文本，关联订单ID
消息同步至绑定邮箱	❌ 不符合	邮件服务配置繁琐，非必需闭环
系统通知模块（订单、资金提醒）	❌ 不符合	涉及推送/轮询机制、定时器，初期复杂
投诉、举报与客服介入	❌ 不符合
✅ 仅保留“订单私信（纯文本）”作为MVP
5.安全与风控模块
功能	是否符合MVP标准	理由
地址黑名单系统	❌ 不符合	涉及判断标准与封锁逻辑，前期体量不足
多重身份验证机制（手机/邮箱）	❌ 不符合	多因子验证非MVP登录所需
异常交易监控、资金冻结	❌ 不符合	需托管与风控逻辑支撑，非初期能力
防DDOS攻击与恶意识别系统	❌ 不符合	运维级防护，部署成本高
高风险行为打标与自动封号	❌ 不符合
✅ 全部推迟开发，初期无流量无必要
6.技术运维与接口模块
功能	是否符合MVP标准	理由
系统架构部署（前后端分离）	✅ 必须	架构设计本身不复杂，是开发方式选择，不是功能
数据库备份与快速恢复	✅ 建议	可用现成脚本配置，保障开发安全，属于开发必备
日志记录与监控预警	❌ 不符合	成本高，前期流量小可人工处理问题日志
对接币安API、物流API	❌ 不符合	明确MVP中不涉及API接入
系统升级与模块迭代支持	✅（框架层支持）
✅ 保留“前后端分离+数据库备份”其余为非MVP需求
总结：符合MVP要求的功能点（建议保留开发）
• 城市定位字段（商品上传时选择）,即商品搜索与筛选模块
• 卖家上传物流单号（订单页添加字段），属于订单管理模块，详情见卖家订单管理模块的发货操作部分
• 交易后文字评价（订单完成后开放填写）
• 平台内文本私信系统（买家卖家沟通）
• 系统架构：前后端分离部署，数据库备份策略
(二）商品搜索与筛选模块（MVP精简版）
1.模块目标：帮助买家快速定位想要的商品，提高浏览效率，同时控制技术实现难度。
2.功能列表
功能	MVP实现方式	说明
20. 商品关键词搜索	模糊匹配商品标题	不支持全文搜索、不支持描述匹配，仅在商品标题中模糊查询。
21. 城市筛选	下拉框或选项卡形式，筛选“商品上传时填写的城市字段”	纯前端或数据库条件筛选，不涉及自动定位。
22. 商品状态筛选	显示“在售”状态商品（后台已审核通过）	默认筛选条件。
23. 商品排序	默认按上架时间倒序排列	暂不支持价格排序或综合排序逻辑。
24. 分页加载	前端分页或后端分页（按页码请求）	控制加载量，提升响应速度。
🚫 明确不做（MVP阶段不纳入）
❌ 不做智能推荐（如热度排序、猜你喜欢等）
❌ 不做分类搜索（如电子产品、服饰等分类体系）
❌ 不做商品属性筛选（如价格区间、品牌筛选等）
❌ 不做搜索纠错、拼写纠正
❌ 不做模糊描述全文检索
✅ 数据结构建议（用于商品列表查询）
商品表中需保留以下字段以支持搜索与筛选：
字段名	说明
title	商品标题，用于关键词模糊匹配
city	卖家手动填写的城市名，用于城市筛选
status	商品状态字段（在售/下架），用于状态过滤
created_at
✅ 接口建议（仅供程序员参考）
GET /api/products?keyword=xxx&city=xxx&page=1
返回值中仅含基本信息（图、价、标题、城市），避免带入冗余字段，提升性能。
（三）评价与信用体系模块（MVP精简版）
交易后星级评分模块说明（MVP版本）
1.触发条件
• 订单状态为“已完成”时，评价入口开放。
• 评价有效期为订单完成后7天内。
• 评价内容
• 仅支持1~5星评分（必填）。
• 不支持文字评价和图片上传。
2.用户权限
• 买卖双方均可对对方进行星级评分。
• 每笔订单每方仅能评价一次，评价提交后不可修改。
• 前端展示
• 提供简单星级选择界面。
• 展示对方的平均星级评分（满分100分，5星对应100分）。
• 不显示任何文字评价或图片。
3.后台统计
• 计算所有订单评分的平均分，用于信用评级展示。
• 支持导出包含订单ID、评分、评价时间的评分数据。
• 评价流程
• 订单完成后，自动推送评价提醒。
• 用户选择星级提交，评分即时生效。
• 超过7天未评价，评价入口自动关闭。
备注： 文字评价及图片上传功能暂未开发，计划纳入后续版本迭代中。
（四）通讯模块分阶段设计
当前MVP阶段，通讯模块提供简易的文本聊天窗口功能，实现买家与卖家之间的实时消息交流，界面支持消息的发送与接收，确保基础沟通需求。作为用户体验的贴心提升，计划在未来迭代中引入“聊天气泡发送邮件”按钮功能：用户可通过该按钮快速将最近10条聊天记录整理为邮件内容，并发送给对方邮箱或其他指定邮箱，方便提醒、存档或跟进沟通。此功能将在后续邮件服务模块完善后实现，届时支持邮件主题与收件人编辑、邮件发送确认以及多条聊天记录的灵活选取，进一步保障用户隐私和操作安全，同时提升沟通效率和可追溯性。
1.MVP阶段 — 基础文本聊天窗口
• 买家和卖家之间的即时文本消息发送与接收。
• 支持简单的消息状态提示（如已发送、已读）。
• 绑定买卖双方账号，确保消息私密和安全。
• 无附件、无图片、无表情包，仅支持纯文本交流。
2.后续开发阶段 — 邮件通知及高级消息功能
• 邮件通知服务，支持发送验证码、订单状态提醒（发货、收货确认、放款、退货等）。
• 用户可将聊天内容一键发送邮件，便于存档或提醒。
（五）比特市场系统架构与数据安全方案
1.MVP版本和后续开发
方案项目	复杂版	MVP简化版
架构	前后端分离，REST/GraphQL多技术栈	前后端分离，REST，单一技术栈
服务器	负载均衡、CDN加速	单台云服务器
备份频率	每小时增量 + binlog + 全量备份	每日全量备份
视频存储	分布式对象存储	第三方云存储
缓存	Redis等中间件	无缓存，直接数据库查询
资金托管逻辑	复杂分布式事务	冻结账户简单模型
安全措施	多层安全控制	基础HTTPS + JWT
2.比特市场 MVP 系统架构与数据安全方案
（1）整体架构设计
架构概览
前端
• 使用单页面应用（SPA）框架（React 或 Vue），运行于网页端或移动端WebView。
• 实现用户注册登录、商品浏览、发布、订单发起、简单聊天、评价等核心交互。
• 与后端通过REST API通信，接口设计简单明了。
后端
• 采用轻量级框架（Node.js + Express 或 Python + Flask），快速开发业务逻辑。
• 处理用户请求、订单管理、支付状态确认、基础权限控制。
• 资金托管实现“冻结账户”模型，保证买卖双方资金安全。
数据库
• 关系型数据库（MySQL）存储用户信息、商品、订单、聊天和评价数据。
• 后端和数据库部署在同一服务器或私有网络内，简化网络配置。
服务器部署
• 选用稳定的云服务器（如AWS EC2或阿里云海外节点），避开国内备案及审查限制。
• 简单部署模式，先不做复杂负载均衡。
（2）安全基础
• 全站启用HTTPS，保护数据传输安全。
• 采用JWT Token进行身份验证，简化登录鉴权。
• 用户敏感信息仅后端处理，前端不保存钱包私钥等信息。
2.数据备份策略（简化版）
（1）备份目的
防止数据丢失，保证交易记录和用户信息安全。
（2）备份频率和类型
每日全量备份：每日凌晨一次，备份整个数据库。
备份文件存储于云存储（如AWS S3）或另一台安全服务器，保证数据异地保存。
（3）备份管理
备份过程由自动化脚本执行，减少人工操作失误。
备份文件加密保存，访问权限限制给运维人员。
（4）恢复流程
简单恢复机制：发生故障时，从最近的备份文件导入恢复数据库。
恢复操作由技术人员执行，用户提前通知可能的服务中断。
3.其他建议
（1）视频及大文件处理
MVP阶段使用第三方存储服务（AWS S3、七牛云等）存储视频文件，数据库只存路径和元数据，避免复杂存储架构。
（2）缓存及性能优化
初期不必使用Redis或复杂缓存，直接依赖数据库查询。性能瓶颈后续再优化。
（3）资金托管简化
• 先实现简单的“冻结资金”账户逻辑，资金变动严格记录，确保买卖双方权益。
• 支付和解冻操作步骤清晰，避免复杂分布式事务。
（4）日志与监控
• 记录关键业务日志（订单变更、资金操作），便于后续排查。
• 简单的监控报警方案，保证服务稳定运行。
3.系统架构与数据安全优化方案
（1）整体架构设计 — 前后端分离部署
结构概览
• 前端（用户界面）
• 运行在用户浏览器（网页端）或移动端App。
• 负责展示商品列表、用户注册登录、商品发布、订单处理、聊天沟通、评价上传等界面。
• 与后端通过安全的API接口（HTTP/HTTPS协议）通信，发送用户请求，接收数据并渲染。
• 后端（业务逻辑与数据处理）
• 运行在服务器（建议选择海外云服务器如AWS，规避备案和封锁风险）。
• 负责处理用户请求、订单管理、支付确认、物流信息上传、信用积分计算、权限控制等业务功能。
• 提供RESTful或GraphQL API，供前端调用。
• 数据库
• 采用关系型数据库（如MySQL）存储用户信息、商品数据、订单记录、聊天记录、评价内容等结构化数据。
• 数据库与后端部署在同一私有网络内，保证数据安全与访问效率。
优点说明
便于开发与维护：
前后端职责分明，开发团队可以并行工作。前端专注用户体验与界面交互，后端专注业务逻辑与数据处理。
易于扩展：
日后可独立升级前端界面或后端服务，不影响另一方。
安全性高：
用户敏感信息（如币安ID、钱包地址）仅在后端处理，前端只展示有限信息，降低泄露风险。
性能优化：
通过API接口设计可实现缓存、负载均衡和CDN加速，提升访问速度与并发处理能力。
技术栈建议（可根据团队实际情况调整）
• 前端：React/Vue/Angular + TypeScript + REST API调用
• 后端：Node.js（Express/Koa）、Python（Django/Flask）、Java（Spring Boot）等
• 数据库：MySQL或PostgreSQL
• 服务器：AWS EC2实例或其他海外云主机
• 安全：HTTPS加密，身份验证Token（如JWT），API访问控制
（2）数据库备份策略
备份目的
保障平台数据安全，避免因硬件故障、软件错误、网络攻击或人为操作失误导致数据丢失，确保用户交易信息、资金流动记录、信用积分等核心数据可快速恢复。
备份类型及频率
• 全量备份
每日凌晨进行一次全量备份，完整复制数据库所有表结构和数据。
备份文件存储于安全的异地服务器或云存储（建议使用AWS S3或类似服务），并开启加密传输和存储。
• 增量备份
每隔1小时执行一次增量备份，仅保存自上次全量或增量备份后发生变化的数据。
大幅减少存储空间和备份时间。
• 日志备份（binlog）
开启数据库的事务日志备份，实时记录数据库操作，支持精确到某时间点的恢复。
备份管理
• 自动化备份脚本
备份过程由自动化脚本完成，定时启动并执行，减少人为失误。
• 备份验证
定期（每周）从备份中恢复测试环境，验证备份数据完整性和可用性。
• 备份存储冗余
备份数据保存多个副本，分布于不同地理位置，防止单点灾难。
• 备份保留策略
保留最近30天的备份数据，过期数据自动清理，平衡存储成本。
• 安全措施
备份文件加密存储，访问权限严格控制，仅限授权运维人员访问。
备份传输过程使用安全通道（如SFTP、HTTPS）。
恢复流程简述
• 在发生数据异常时，快速定位最近的可用备份版本。
• 按照预设流程，恢复数据库到指定时间点，确保用户数据和交易记录一致性。
• 恢复过程中，平台应通知用户可能的服务中断，确保透明度。
（3）结合平台特性的补充说明
• 订单与资金数据备份重点
订单编号、支付状态、买卖双方身份信息、物流状态等交易核心信息必须优先保证备份完整。
担保金账户余额及冻结状态数据须实时同步备份，防止资金异常。
• 聊天记录与评价数据备份
聊天消息、开箱视频链接、评价文本等数据也需定期备份，作为平台仲裁纠纷的重要依据。
视频类大文件建议存储于独立对象存储服务，数据库仅保存文件路径与元数据。
• 动态数据与缓存
平台可采用Redis等缓存中间件提升访问速度，但缓存数据非持久化主数据，不纳入备份范围。
重点在于确保数据库主数据的安全。
（4）对非技术人员的简洁解释
我们将平台分成两个部分：
1）用户界面（网页或手机APP），你看到和操作的地方；
2）后台服务，负责处理数据、订单和付款，确保一切顺利进行。
前后端分开，这样可以让开发更灵活，出问题时也更容易修复。
所有重要数据（用户资料、订单、支付信息）都会被安全保存和定时备份，就像定期存档，防止数据丢失。
备份工作是自动完成的，每天都会备份所有数据，每小时还会备份新增或变动的数据，确保数据安全。
如果发生故障，我们可以快速用备份数据恢复，减少用户影响。
（六）需求单系统模块
1.功能定位（MVP 目标）
核心目标：提供用户可发布“求购/雇佣”类需求的入口，允许其他用户主动响应并报价，最终买方选择响应者后生成一笔订单，进入托管交易流程。
MVP阶段只支持“发布 + 浏览 + 响应 + 生成订单”四个核心功能点，去除统计分析、举报、排序算法、多语言、后台审查等非必要机制。
2.模块划分与交互流程
（1）完整版模块组成
模块名	功能说明
25. 需求单创建模块	用户选择类型并填写对应字段
26. 需求广场展示模块	展示全平台所有需求单，支持筛选与排序
27. 响应与要约模块	支持用户针对需求单报价响应
28. 成交与订单生成模块	买家选定响应后自动生成标准交易订单流程
29. 用户保护与权限模块	提供隐藏发布者、举报、自动关闭等机制
（2）MVP阶段模块划分
模块名称	是否保留	功能说明
30. 创建需求单	✅	用户可发布标题、预算、描述、时限等基本信息
31. 浏览需求广场	✅	所有用户可浏览全平台已发布需求
32. 响应需求单	✅	用户提交报价与说明响应他人需求
33. 成交并生成订单	✅	发布方选择响应者后，进入订单交易流程
34. 聊天沟通模块联动	✅（借用现有）	用户响应后跳转至站内聊天进行沟通
35. 需求状态控制	✅	支持手动关闭、成交后自动关闭
36. 举报、屏蔽、排序算法	❌	暂不支持，留待后期迭代
37. 限频发布/匿名机制	❌	MVP阶段暂不实现控制类策略
38. 后台审查机制	❌
3.需求单字段结构（精简版）
表：demands（需求主表）
字段名	类型	必须	描述
id	UUID	✔️	主键
user_id	UUID	✔️	发布者ID
demand_type	ENUM	✔️	需求类型
subcategory	VARCHAR	❌	二级分类
title	VARCHAR(100)	✔️	简短标题
description	TEXT	✔️	需求说明
budget_min	DECIMAL	❌	最低预算
budget_max	DECIMAL	❌	最高预算
budget	DECIMAL	✔️	预算（单一字段）
delivery_method	ENUM	✔️	online / delivery
expiration_time	DATETIME	✔️	过期时间
status	ENUM	✔️	open / matched / closed
created_at / updated_at	DATETIME	✔️	时间戳
表：demand_offers（响应记录表）
字段名	类型	必须	描述
id	UUID	✔️	响应记录ID
demand_id	UUID	✔️	对应需求单
seller_id	UUID	✔️	响应者ID
offer_price	DECIMAL	✔️	响应报价
offer_note	TEXT	✔️	简要说明
is_accepted	BOOLEAN	✔️	是否被采纳
created_at	DATETIME	✔️	响应时间
❗ 注意：字段只保留“必要最少信息”，如预算不再细分为 min/max，响应仅支持一次报价。
4.分类系统设计（type 分类）
一级分类字段（demandtype）：
类型值	  中文说明	用途说明
buy_goods	求购商品	实物 / 虚拟商品购买
hire_service	雇佣服务	 剪辑、翻译、写作、设计等
digital_task	协作任务	网课协助、账号帮挂、信息处理等
request_info	求资料/知识	求解某问题、寻求特定资源
🧱 二级分类字段（subcategory）： 根据一级分类变化，例如：
• buy_goods → 手机 / 游戏点卡 / 二手配件
• hire_service → 翻译 / 剪辑 / 平面设计 / 营销脚本撰写
• digital_task → 网课协助 / 自动化小工具 / 账号注册
🏷️ 可选标签字段（tags，JSON）： 如：高预算 / 紧急 / 虚拟交付 / 本地优先 / 实名担保
5.用户流程（MVP阶段）
买方创建需求单
↓
展示至需求广场（待响应状态）
↓
卖方浏览后提交“响应报价”
↓
买方查看所有响应 → 选择其一
↓
支付
↓
生成订单 → 正常交易（交付/评价）
买家视角
39. 进入“发布需求”页面
40. 填写标题、说明、预算、交付方式、时限
41. 点击提交，发布成功，展示在“需求广场”
卖家视角
42. 浏览“需求广场”
43. 点击某条需求 → 填写响应报价与说明 → 提交
44. 等待买家选择是否采纳
成交流程
• 买家选择一个响应并点击“采纳”
• 买家点击采纳后生成支付二维码，买家支付后生成订单（buyer = 需求方，seller = 响应方）
• 进入平台现有交易流程（托管、放款、评价）
6.前端实现建议（Vue 简版）
📄 发布表单组件（ ）
字段	类型	说明
title	输入框	限100字
description	多行文本	支持简要说明
budget	数字输入	单一金额
delivery_method	单选项	线上 / 快递
expiration_time	日期选择器	默认7日后失效
📃 广场浏览组件（ ）
• 显示需求列表：标题 / 预算 / 发布时间
• 支持筛选：按分类或关键词搜索
📩 响应组件（ ）
• 显示响应表单：报价 + 附言
• 响应后自动跳转至聊天界面
7.与现有平台模块的接口集成
接入模块	说明
订单系统	响应被采纳后自动生成订单
托管与放款	与商品交易流程一致
聊天系统	响应后跳转私聊
评价系统	成交后可互评
7.MVP版本优先级建议
功能点	优先级	理由
发布需求	⭐⭐⭐⭐	MVP核心
浏览广场	⭐⭐⭐⭐	撮合基础
响应机制	⭐⭐⭐⭐	必须闭环
生成订单	⭐⭐⭐⭐	串联交易系统
聊天联动	⭐⭐⭐	增强信任度
状态控制（关闭）	⭐⭐⭐	保证数据一致性
分类系统	⭐⭐	如工期允许可加
预算上下限	⭐
✅ 结语
本版本的需求单系统：
• 在功能上围绕 **“买方发单、卖方响应、买方采纳、交易撮合”**四步流程构建
• 在结构上压缩字段、裁剪模块，减少开发复杂度
• 在接口上兼容已有交易/聊天/评价模块，节省资源 是一个可上线、可迭代、低成本实现的MVP模块。
三、平台搭建建议与开发规划
平台搭建应以“核心交易闭环优先，辅助功能后置”为基本原则，优先保障用户注册、商品浏览与交易支付流程的顺畅可用。
最小可行产品（MVP）开发阶段
第1周：用户注册模块。支持邮箱 + 币安ID注册，不做验证。
第2周：商品发布与商品展示模块。支持上传商品图片、填写USDT价格、选择所在城市，商品以列表形式展示，可按城市筛选。
第3周：订单交易与后台管理模块。买家下单后上传转账截图，后台人工确认转账并标记订单状态，通知发货。
第4周：结算模块。卖家填写钱包地址，后台完成USDT打款并记录TXID。
MVP涉及模块简要说明
45. 用户管理模块（精简版） 功能：邮箱注册、登录，输入并保存币安ID 简化内容：无身份认证、无密码找回、无实名认证 目的：建立用户身份，便于后续关联订单与钱包
46. 商品管理模块（精简版） 功能：卖家上传商品图、描述、价格（USDT）、选择城市。商品展示为列表页，支持城市筛选 简化内容：无商品审核、无分类搜索、无自动汇率转换 目的：完成商品上架与浏览
47. 交易管理模块（精简版） 功能：买家点击“购买”生成订单，上传转账截图。后台人工审核并确认收款 简化内容：无托管系统、无API对接、无自动识别哈希 目的：完成资金闭环的核心流程
48. 后台管理模块（精简版） 功能：查看用户、商品、订单。审核转账截图并修改订单状态。手动打USDT并记录TXID 简化内容：无数据分析、无权限管理、无内容审核 目的：支撑平台核心运营工作
49. 结算子模块（后台附属） 功能：卖家填写收款地址，后台打款后标记“已结算” 简化内容：无自动化转账、无合约控制、无记录查询 目的：完成最终打款，闭环交易
以下模块暂不纳入MVP（后续版本规划）
物流管理模块：由买卖双方私下协商发货，暂不处理物流单与轨迹。 评价与信用体系模块：无评论、无信誉积分，仅展示用户名。 担保金与中间人模块：不启用中间人机制与担保交易。 消息与沟通模块：无站内私信，联系通过订单备注或邮箱。 安全与风控模块：不启用AML/KYC，不处理风险识别。 运维与统计模块：不建设报表系统，仅保留操作日志。
MVP开发优先级
50. 用户管理模块
51. 商品管理模块
52. 交易管理模块
53. 后台管理模块（含结算功能）
模块完整开发优先级排序（适用于完整产品迭代）
54. 用户管理模块（注册、身份认证、钱包绑定等）
55. 商品管理模块（商品发布、审核、展示与搜索）
56. 交易管理模块（订单创建、资金托管、支付确认）
57. 物流管理模块（物流单上传、状态追踪、开箱视频）
58. 评价与信用体系模块（评论系统、信誉积分）
59. 担保金与中间人模块（担保流程、佣金结算、转账日程表）
60. 消息与沟通模块（私信系统、系统通知、举报通道）
61. 后台管理模块（商品、订单、用户审核，数据面板）
62. 安全与风控模块（反洗钱、交易风险识别）
63. 技术运维与运营支持模块（接口维护、日志、统计报表等）
模块并行开发建议
若技术团队成员充足，建议划分为前端、后端、后台三个方向并行推进：
前端组：负责用户界面、商品展示、交易流程 后端组：负责用户体系、订单系统、支付逻辑、数据库接口 后台组：负责管理员操作、中间人系统（预留）、后期AI审核接入
接口设计与系统解耦建议
各模块建议采用 RESTful API 或 GraphQL 接口标准，保持低耦合。 接口文档需统一格式，便于协作与后期扩展。 支付、物流、评价等系统建议作为独立服务模块部署，方便维护与替换。
测试策略与稳定性保障
各模块开发完成后需进行单元测试。 集成阶段应模拟多用户多订单并发场景。 采用自动化测试框架持续回归关键模块。 正式上线前应进行安全测试与渗透测试，保障资金安全。 所有敏感操作必须记录日志，确保审计追溯能力。