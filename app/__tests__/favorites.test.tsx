import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useSession } from 'next-auth/react'
import FavoriteButton from '../../components/FavoriteButton'
import '@testing-library/jest-dom'


// Mock next-auth/react specifically for this test
vi.mock('next-auth/react', () => ({
  useSession: vi.fn()
}))

const mockUseSession = vi.mocked(useSession)

// Mock fetch
global.fetch = vi.fn()

describe('FavoriteButton', () => {
  beforeEach(() => {
    // Reset fetch mock
    vi.mocked(fetch).mockClear()
  })

  it('should not render when user is not logged in', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
      update: vi.fn()
    })

    const { container } = render(<FavoriteButton productId="test-product-id" />)
    expect(container.firstChild).toBeNull()
  })

  it('should render heart icon when user is logged in', async () => {
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'user-1', email: '<EMAIL>' } as any,
        expires: '2024-12-31'
      },
      status: 'authenticated',
      update: vi.fn()
    } as any)

    // Mock the check favorite status API call
    vi.mocked(fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ favoriteProductIds: [] })
    } as Response)

    render(<FavoriteButton productId="test-product-id" />)

    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument()
    })
  })

  it('should show solid heart when product is favorited', async () => {
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'user-1', email: '<EMAIL>' } as any,
        expires: '2024-12-31'
      },
      status: 'authenticated',
      update: vi.fn()
    } as any)

    // Mock the check favorite status API call - product is favorited
    vi.mocked(fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ favoriteProductIds: ['test-product-id'] })
    } as Response)

    render(<FavoriteButton productId="test-product-id" />)

    await waitFor(() => {
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', '取消收藏')
    })
  })

  it('should toggle favorite status when clicked', async () => {
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'user-1', email: '<EMAIL>' } as any,
        expires: '2024-12-31'
      },
      status: 'authenticated',
      update: vi.fn()
    } as any)

    // Mock initial check - not favorited
    vi.mocked(fetch)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ favoriteProductIds: [] })
      } as Response)
      // Mock add favorite API call
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ message: '收藏成功' })
      } as Response)

    render(<FavoriteButton productId="test-product-id" />)

    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    const button = screen.getByRole('button')
    fireEvent.click(button)

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/user/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId: 'test-product-id'
        })
      })
    })
  })

  it('should show text when showText prop is true', async () => {
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'user-1', email: '<EMAIL>' } as any,
        expires: '2024-12-31'
      },
      status: 'authenticated',
      update: vi.fn()
    } as any)

    vi.mocked(fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ favoriteProductIds: [] })
    } as Response)

    render(<FavoriteButton productId="test-product-id" showText />)

    await waitFor(() => {
      expect(screen.getByText('收藏')).toBeInTheDocument()
    })
  })
})
