'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import Carousel from '@/components/Carousel'
import CompactProductCard from '@/components/CompactProductCard'

interface Product {
  id: string
  title: string
  description: string
  price: number
  images: string
  category: string
  condition: string
  city: string
  district: string
  stock: number
  status: string
  createdAt: string
  seller: {
    name: string | null
    userId: string
    avatar?: string | null
  }
  _count?: {
    favorites: number
  }
}

interface AnnouncementCarouselItem {
  id: string
  title: string
  description: string
  image: string
  link: string
  category: string
  priority: string
  isSticky: boolean
  viewCount: number
  publishAt: string | null
  createdAt: string
}

export default function Home() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [announcements, setAnnouncements] = useState<AnnouncementCarouselItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [, setIsLoadingAnnouncements] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // 获取首页公告
  const fetchAnnouncements = async () => {
    try {
      setIsLoadingAnnouncements(true)
      const response = await fetch('/api/announcements/home')

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAnnouncements(data.announcements)
        }
      }
    } catch (error) {
      console.error('获取公告失败:', error)
    } finally {
      setIsLoadingAnnouncements(false)
    }
  }

  // 默认轮播数据（当没有公告时显示）
  const defaultCarouselItems = [
    {
      id: 'default-1',
      title: '欢迎来到比特市场',
      description: '基于USDT的去中心化C2C交易平台',
      image: '',
      link: '/products',
      category: 'GENERAL',
      priority: 'NORMAL',
      isSticky: false,
      viewCount: 0,
      publishAt: null,
      createdAt: new Date().toISOString()
    },
    {
      id: 'default-2',
      title: '安全可靠的交易',
      description: '担保交易，保障买卖双方权益',
      image: '',
      link: '/auth/register',
      category: 'GENERAL',
      priority: 'NORMAL',
      isSticky: false,
      viewCount: 0,
      publishAt: null,
      createdAt: new Date().toISOString()
    },
    {
      id: 'default-3',
      title: '发布您的商品',
      description: '轻松发布，快速成交',
      image: '',
      link: '/products/create',
      category: 'GENERAL',
      priority: 'NORMAL',
      isSticky: false,
      viewCount: 0,
      publishAt: null,
      createdAt: new Date().toISOString()
    },
    {
      id: 'default-4',
      title: '需求广场',
      description: '发布需求，让卖家主动联系您',
      image: '',
      link: '/demands',
      category: 'GENERAL',
      priority: 'NORMAL',
      isSticky: false,
      viewCount: 0,
      publishAt: null,
      createdAt: new Date().toISOString()
    }
  ]

  // 轮播数据：优先显示公告，没有公告时显示默认内容
  const carouselItems = announcements.length > 0 ? announcements : defaultCarouselItems

  // 获取商品数据
  const fetchProducts = async (page: number = 1) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/products?page=${page}&limit=12&sortBy=newest`)
      if (response.ok) {
        const data = await response.json()
        if (page === 1) {
          setProducts(data.products)
        } else {
          setProducts(prev => [...prev, ...data.products])
        }
        setHasMore(data.hasMore)
      }
    } catch (error) {
      console.error('获取商品失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 加载更多商品
  const loadMore = () => {
    if (!isLoading && hasMore) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)
      fetchProducts(nextPage)
    }
  }

  // 检查用户状态（只在首次登录时重定向到banner）
  useEffect(() => {
    const checkUserStatus = async () => {
      if (session?.user?.id) {
        try {
          // 检查是否已经重定向过
          const hasRedirected = sessionStorage.getItem(`banned_redirect_${session.user.id}`)

          const response = await fetch('/api/user/status')
          if (response.ok) {
            const data = await response.json()
            if (data.status.isBanned && !hasRedirected) {
              // 标记已重定向，避免重复重定向
              sessionStorage.setItem(`banned_redirect_${session.user.id}`, 'true')
              router.push('/banner')
              return
            }
          }
        } catch (error) {
          console.error('检查用户状态失败:', error)
        }
      }
    }

    checkUserStatus()
  }, [session, router])

  // 页面加载时获取商品和公告
  useEffect(() => {
    fetchProducts(1)
    fetchAnnouncements()
  }, [])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 统一导航栏 */}
      <Navbar />

      {/* 轮播广告区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
        <Carousel items={carouselItems} height="280px" />
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 最新商品区域 */}
          <div>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">最新商品</h3>
              <Link
                href="/products"
                className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
              >
                查看全部 →
              </Link>
            </div>

            {/* 商品网格 */}
            {isLoading && products.length === 0 ? (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {[...Array(8)].map((_, index) => (
                  <div key={index} className="bg-white rounded-lg shadow animate-pulse">
                    <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                    <div className="p-4 space-y-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                          <div className="h-4 bg-gray-200 rounded w-16"></div>
                        </div>
                        <div className="h-4 bg-gray-200 rounded w-10"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : products.length > 0 ? (
              <>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {products.map((product) => (
                    <CompactProductCard key={product.id} product={product} />
                  ))}
                </div>

                {/* 加载更多按钮 */}
                {hasMore && (
                  <div className="text-center mt-8">
                    <button
                      onClick={loadMore}
                      disabled={isLoading}
                      className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
                    >
                      {isLoading ? '加载中...' : '加载更多'}
                    </button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📦</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无商品</h3>
                <p className="text-gray-600 mb-4">还没有商品发布，成为第一个发布者吧！</p>
                {session && (
                  <Link
                    href="/products/create"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
                  >
                    发布商品
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
