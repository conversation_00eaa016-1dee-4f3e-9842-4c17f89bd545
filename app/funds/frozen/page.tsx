'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeftIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface FrozenFundsData {
  balance: {
    total: number
    available: number
    locked: number
  }
  activeGuarantees: {
    id: string
    orderNumber: string
    amount: number
    status: string
    createdAt: string
  }[]
  transactions: {
    id: string
    type: 'DEPOSIT' | 'WITHDRAW'
    amount: number
    status: string
    createdAt: string
  }[]
}

export default function FrozenFundsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [data, setData] = useState<FrozenFundsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [operationType, setOperationType] = useState<'deposit' | 'withdraw' | null>(null)
  const [amount, setAmount] = useState('')
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user?.id) {
      fetchData()
    }
  }, [session, status, router])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/funds/frozen')
      if (response.ok) {
        const result = await response.json()
        setData(result)
      } else {
        setError('获取数据失败')
      }
    } catch (error) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const handleOperation = async () => {
    if (!operationType || !amount || !data) return

    const operationAmount = parseFloat(amount)
    if (isNaN(operationAmount) || operationAmount <= 0) {
      setError('请输入有效金额')
      return
    }

    // 验证操作限制
    if (operationType === 'withdraw' && operationAmount > data.balance.available) {
      setError(`可提现金额不足，最多可提现 ${data.balance.available.toFixed(2)} USDT`)
      return
    }

    try {
      setProcessing(true)
      setError('')

      const response = await fetch('/api/funds/frozen/operation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: operationType,
          amount: operationAmount
        })
      })

      const result = await response.json()

      if (response.ok) {
        alert(`${operationType === 'deposit' ? '充值' : '提现'}成功`)
        setAmount('')
        setOperationType(null)
        fetchData() // 刷新数据
      } else {
        setError(result.error || '操作失败')
      }
    } catch (error) {
      setError('网络错误')
    } finally {
      setProcessing(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600">数据加载失败</p>
            <button 
              onClick={fetchData}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Link href="/deposit" className="mr-4">
              <ArrowLeftIcon className="h-6 w-6 text-gray-600 hover:text-gray-900" />
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">冻结资金管理</h1>
          </div>
          <p className="text-gray-600">管理您的冻结资金，进行充值和提现操作</p>
        </div>

        {/* 资金概览卡片 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <InformationCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">总冻结资金</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{data.balance.total.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <ArrowUpIcon className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">可用金额</p>
                <p className="text-lg sm:text-2xl font-bold text-green-600 truncate">{data.balance.available.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 hover:shadow-md transition-shadow duration-200 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">已锁定金额</p>
                <p className="text-lg sm:text-2xl font-bold text-orange-600 truncate">{data.balance.locked.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>
        </div>

        {/* 操作区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-8">
          {/* 资金操作 */}
          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 sm:mb-6">资金操作</h2>
            
            {/* 操作类型选择 */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 mb-6">
              <button
                onClick={() => setOperationType('deposit')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                  operationType === 'deposit'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300 shadow-sm'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                }`}
              >
                <ArrowDownIcon className="h-4 w-4 inline mr-2" />
                <span className="hidden sm:inline">充值到冻结资金</span>
                <span className="sm:hidden">充值</span>
              </button>
              <button
                onClick={() => setOperationType('withdraw')}
                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                  operationType === 'withdraw'
                    ? 'bg-green-100 text-green-700 border border-green-300 shadow-sm'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                }`}
              >
                <ArrowUpIcon className="h-4 w-4 inline mr-2" />
                <span className="hidden sm:inline">提现到可用余额</span>
                <span className="sm:hidden">提现</span>
              </button>
            </div>

            {/* 金额输入 */}
            {operationType && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {operationType === 'deposit' ? '充值金额' : '提现金额'}
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      placeholder="请输入金额"
                      className="w-full px-3 py-2 pr-16 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      min="0"
                      step="0.01"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm font-medium pointer-events-none">
                      USDT
                    </span>
                  </div>
                </div>

                {/* 操作提示 */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div className="flex items-start">
                    <InformationCircleIcon className="h-5 w-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-yellow-700 leading-relaxed">
                      {operationType === 'deposit' ? (
                        <p>资金将从您的可用保证金余额转入冻结资金池</p>
                      ) : (
                        <p>最多可提现 <span className="font-semibold">{data.balance.available.toFixed(2)} USDT</span>（已扣除锁定金额）</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* 错误提示 */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex items-start">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-3 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-red-700 leading-relaxed">{error}</p>
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <button
                  onClick={handleOperation}
                  disabled={processing || !amount}
                  className={`w-full py-3 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${
                    operationType === 'deposit'
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md'
                      : 'bg-green-600 hover:bg-green-700 text-white shadow-sm hover:shadow-md'
                  } disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-sm`}
                >
                  {processing ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </span>
                  ) : (
                    operationType === 'deposit' ? '确认充值' : '确认提现'
                  )}
                </button>
              </div>
            )}
          </div>

          {/* 活跃担保订单 */}
          <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 sm:mb-6">活跃担保订单</h2>
            
            {data.activeGuarantees.length > 0 ? (
              <div className="space-y-3">
                {data.activeGuarantees.map((guarantee) => (
                  <div key={guarantee.id} className="border border-gray-200 rounded-md p-3 sm:p-4 hover:border-gray-300 transition-colors duration-200">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-2 sm:space-y-0">
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900 truncate">{guarantee.orderNumber}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(guarantee.createdAt).toLocaleDateString('zh-CN')}
                        </p>
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <p className="text-sm font-semibold text-orange-600 mb-1">
                          {guarantee.amount.toFixed(2)} USDT
                        </p>
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                          {guarantee.status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无活跃担保</h3>
                <p className="mt-1 text-sm text-gray-500">当前没有进行中的担保订单</p>
              </div>
            )}
          </div>
        </div>

        {/* 交易记录 */}
        <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 sm:mb-6">最近交易记录</h2>

          {data.transactions.length > 0 ? (
            <>
              {/* 桌面端表格 */}
              <div className="hidden sm:block overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        类型
                      </th>
                      <th className="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        金额
                      </th>
                      <th className="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        时间
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {data.transactions.map((transaction) => (
                      <tr key={transaction.id} className="hover:bg-gray-50 transition-colors duration-200">
                        <td className="px-4 lg:px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {transaction.type === 'DEPOSIT' ? (
                              <ArrowDownIcon className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
                            ) : (
                              <ArrowUpIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                            )}
                            <span className="text-sm text-gray-900">
                              {transaction.type === 'DEPOSIT' ? '充值' : '提现'}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 lg:px-6 py-4 whitespace-nowrap">
                          <span className={`text-sm font-medium ${
                            transaction.type === 'DEPOSIT' ? 'text-blue-600' : 'text-green-600'
                          }`}>
                            {transaction.type === 'DEPOSIT' ? '+' : '-'}{transaction.amount.toFixed(2)} USDT
                          </span>
                        </td>
                        <td className="px-4 lg:px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {transaction.status}
                          </span>
                        </td>
                        <td className="px-4 lg:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(transaction.createdAt).toLocaleString('zh-CN')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 移动端卡片 */}
              <div className="sm:hidden space-y-3">
                {data.transactions.map((transaction) => (
                  <div key={transaction.id} className="border border-gray-200 rounded-md p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {transaction.type === 'DEPOSIT' ? (
                          <ArrowDownIcon className="h-4 w-4 text-blue-500 mr-2" />
                        ) : (
                          <ArrowUpIcon className="h-4 w-4 text-green-500 mr-2" />
                        )}
                        <span className="text-sm font-medium text-gray-900">
                          {transaction.type === 'DEPOSIT' ? '充值' : '提现'}
                        </span>
                      </div>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        {transaction.status}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={`text-sm font-medium ${
                        transaction.type === 'DEPOSIT' ? 'text-blue-600' : 'text-green-600'
                      }`}>
                        {transaction.type === 'DEPOSIT' ? '+' : '-'}{transaction.amount.toFixed(2)} USDT
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(transaction.createdAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无交易记录</h3>
              <p className="mt-1 text-sm text-gray-500">您还没有进行过冻结资金操作</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
