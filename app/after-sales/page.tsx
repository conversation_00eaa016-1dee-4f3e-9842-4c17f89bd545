'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Navbar } from '@/components/Navbar'

interface AfterSalesRequest {
  id: string
  type: string
  reason: string
  description: string
  requestedAmount?: number
  status: string
  createdAt: string
  unreadCount: number
  order: {
    orderNumber: string
    totalAmount: number
    product: {
      title: string
      images: string
    }
  }
  buyer: {
    name: string
  }
  seller: {
    name: string
  }
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

function AfterSalesListContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()

  const [requests, setRequests] = useState<AfterSalesRequest[]>([])
  const [pagination, setPagination] = useState<Pagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<any>({})

  // 筛选条件
  const [filters, setFilters] = useState({
    role: searchParams?.get('role') || 'buyer',
    type: searchParams?.get('type') || '',
    status: searchParams?.get('status') || '',
    search: searchParams?.get('search') || '',
    page: parseInt(searchParams?.get('page') || '1')
  })

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    loadAfterSalesRequests()
    loadStats()
  }, [session, filters])

  const loadAfterSalesRequests = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString())
      })

      const response = await fetch(`/api/after-sales?${params}`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests)
        setPagination(data.pagination)
      } else {
        throw new Error('获取售后申请列表失败')
      }
    } catch (error) {
      console.error('加载售后申请列表失败:', error)
      alert('加载售后申请列表失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await fetch(`/api/after-sales?role=${filters.role}`, {
        method: 'HEAD',
        credentials: 'include'
      })
      
      if (response.ok) {
        setStats({
          total: parseInt(response.headers.get('X-Total-Count') || '0'),
          pending: parseInt(response.headers.get('X-Pending-Count') || '0'),
          processing: parseInt(response.headers.get('X-Processing-Count') || '0'),
          completed: parseInt(response.headers.get('X-Completed-Count') || '0'),
          refund: parseInt(response.headers.get('X-Refund-Count') || '0'),
          exchange: parseInt(response.headers.get('X-Exchange-Count') || '0'),
          repair: parseInt(response.headers.get('X-Repair-Count') || '0'),
          unreadMessages: parseInt(response.headers.get('X-Unread-Messages') || '0')
        })
      }
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value, page: 1 }
    setFilters(newFilters)
    
    // 更新URL
    const params = new URLSearchParams()
    Object.entries(newFilters).forEach(([k, v]) => {
      if (v) params.set(k, v.toString())
    })
    router.push(`/after-sales?${params}`)
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const getTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'REFUND': '退款',
      'EXCHANGE': '换货',
      'REPAIR': '维修'
    }
    return typeMap[type] || type
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'APPROVED': '已同意',
      'REJECTED': '已拒绝',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING': 'text-orange-600 bg-orange-100',
      'APPROVED': 'text-green-600 bg-green-100',
      'REJECTED': 'text-red-600 bg-red-100',
      'PROCESSING': 'text-blue-600 bg-blue-100',
      'COMPLETED': 'text-green-600 bg-green-100',
      'CANCELLED': 'text-gray-600 bg-gray-100'
    }
    return colorMap[status] || 'text-gray-600 bg-gray-100'
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">售后申请</h1>
          <p className="text-gray-600">管理您的售后申请和处理进度</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.total || 0}</div>
            <div className="text-sm text-gray-600">总申请数</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.pending || 0}</div>
            <div className="text-sm text-gray-600">待处理</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.processing || 0}</div>
            <div className="text-sm text-gray-600">处理中</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-green-600">{stats.completed || 0}</div>
            <div className="text-sm text-gray-600">已完成</div>
          </div>
        </div>

        {/* 筛选器 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* 角色切换 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                查看角色
              </label>
              <select
                value={filters.role}
                onChange={(e) => handleFilterChange('role', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="buyer">作为买家</option>
                <option value="seller">作为卖家</option>
              </select>
            </div>

            {/* 申请类型 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申请类型
              </label>
              <select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">全部类型</option>
                <option value="REFUND">退款</option>
                <option value="EXCHANGE">换货</option>
                <option value="REPAIR">维修</option>
              </select>
            </div>

            {/* 申请状态 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申请状态
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">全部状态</option>
                <option value="PENDING">待处理</option>
                <option value="APPROVED">已同意</option>
                <option value="REJECTED">已拒绝</option>
                <option value="PROCESSING">处理中</option>
                <option value="COMPLETED">已完成</option>
                <option value="CANCELLED">已取消</option>
              </select>
            </div>

            {/* 搜索 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索
              </label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="订单号、问题描述..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* 申请列表 */}
        <div className="bg-white rounded-lg shadow">
          {loading ? (
            <div className="p-8 text-center">
              <div className="text-lg">加载中...</div>
            </div>
          ) : requests.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">暂无售后申请</div>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {requests.map((request) => (
                <div key={request.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* 基本信息 */}
                      <div className="flex items-center space-x-4 mb-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          request.type === 'REFUND' ? 'bg-red-100 text-red-800' :
                          request.type === 'EXCHANGE' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {getTypeText(request.type)}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                          {getStatusText(request.status)}
                        </span>
                        {request.unreadCount > 0 && (
                          <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs">
                            {request.unreadCount} 条未读
                          </span>
                        )}
                      </div>

                      {/* 商品信息 */}
                      <div className="flex items-center space-x-4 mb-3">
                        <img
                          src={request.order.product.images?.split(',')[0] || '/placeholder.jpg'}
                          alt={request.order.product.title}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{request.order.product.title}</h4>
                          <div className="text-sm text-gray-600">
                            订单号: {request.order.orderNumber}
                          </div>
                          <div className="text-sm text-gray-600">
                            {filters.role === 'buyer' ? `卖家: ${request.seller.name}` : `买家: ${request.buyer.name}`}
                          </div>
                        </div>
                      </div>

                      {/* 申请信息 */}
                      <div className="text-sm text-gray-600 mb-2">
                        <strong>申请原因:</strong> {request.reason}
                      </div>
                      <div className="text-sm text-gray-600 mb-3">
                        {request.description}
                      </div>

                      {/* 金额信息 */}
                      {request.type === 'REFUND' && request.requestedAmount && (
                        <div className="text-sm">
                          <strong>申请退款:</strong> 
                          <span className="text-red-600 font-medium ml-1">¥{request.requestedAmount.toFixed(2)}</span>
                        </div>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex flex-col space-y-2 ml-4">
                      <div className="text-xs text-gray-500 text-right">
                        {new Date(request.createdAt).toLocaleDateString()}
                      </div>
                      
                      <div className="flex space-x-2">
                        <Link
                          href={`/after-sales/${request.id}/status`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                        >
                          查看详情
                        </Link>
                        
                        <Link
                          href={`/after-sales/${request.id}/chat`}
                          className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                        >
                          沟通
                        </Link>

                        {/* 处理按钮（仅卖家且待处理状态） */}
                        {filters.role === 'seller' && request.status === 'PENDING' && (
                          <Link
                            href={`/orders/${request.order.id}/after-sales/handle?requestId=${request.id}`}
                            className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm"
                          >
                            处理
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 分页 */}
          {pagination && pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                  共 {pagination.total} 条
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    上一页
                  </button>
                  
                  <span className="px-3 py-1 text-sm">
                    第 {pagination.page} / {pagination.totalPages} 页
                  </span>
                  
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AfterSalesListPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">
      <div className="text-lg">加载中...</div>
    </div>}>
      <AfterSalesListContent />
    </Suspense>
  )
}
