'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'

interface Message {
  id: string
  content: string
  images?: string[]
  messageType: string
  isRead: boolean
  createdAt: string
  sender: {
    id: string
    name: string
  }
}

interface AfterSalesRequest {
  id: string
  type: string
  status: string
  order: {
    orderNumber: string
    sellerId: string
    buyerId: string
  }
}

export default function AfterSalesChatPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [request, setRequest] = useState<AfterSalesRequest | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  
  // 消息输入
  const [messageInput, setMessageInput] = useState('')
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const requestId = params.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (requestId) {
      loadRequestData()
      loadMessages()
    }
  }, [session, requestId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const loadRequestData = async () => {
    try {
      const response = await fetch(`/api/after-sales/${requestId}/status`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        setRequest(data.request)
      }
    } catch (error) {
      console.error('加载售后申请失败:', error)
    }
  }

  const loadMessages = async () => {
    try {
      const response = await fetch(`/api/after-sales/${requestId}/messages`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('售后申请不存在')
          router.push('/orders')
          return
        }
        throw new Error('获取消息失败')
      }
      
      const data = await response.json()
      setMessages(data.messages)
      
    } catch (error) {
      console.error('加载消息失败:', error)
      alert('加载消息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length + selectedImages.length > 3) {
      alert('最多只能选择3张图片')
      return
    }
    
    setSelectedImages([...selectedImages, ...files])
  }

  const removeImage = (index: number) => {
    setSelectedImages(selectedImages.filter((_, i) => i !== index))
  }

  const handleSendMessage = async () => {
    if (!messageInput.trim() && selectedImages.length === 0) {
      return
    }

    setSending(true)
    
    try {
      let imageUrls: string[] = []
      
      // 上传图片
      if (selectedImages.length > 0) {
        const formData = new FormData()
        selectedImages.forEach((file, index) => {
          formData.append(`image_${index}`, file)
        })
        
        const uploadResponse = await fetch('/api/upload/after-sales-images', {
          method: 'POST',
          credentials: 'include',
          body: formData
        })
        
        if (uploadResponse.ok) {
          const uploadData = await uploadResponse.json()
          imageUrls = uploadData.urls
        }
      }

      // 发送消息
      const response = await fetch(`/api/after-sales/${requestId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          content: messageInput.trim(),
          images: imageUrls,
          messageType: imageUrls.length > 0 ? 'IMAGE' : 'TEXT'
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        setMessages([...messages, data.message])
        setMessageInput('')
        setSelectedImages([])
      } else {
        const error = await response.json()
        alert(error.error || '发送消息失败')
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' })
    }
  }

  const getTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'REFUND': '退款',
      'EXCHANGE': '换货',
      'REPAIR': '维修'
    }
    return typeMap[type] || type
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-lg">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!request) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">售后申请不存在</h2>
            <button
              onClick={() => router.push('/orders')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
            >
              返回订单列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  const isBuyer = request.order.buyerId === session.user.id
  const isSeller = request.order.sellerId === session.user.id

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">售后沟通</h1>
          </div>
          <div className="text-sm text-gray-600">
            订单号：{request.order.orderNumber} | 申请类型：{getTypeText(request.type)}
          </div>
        </div>

        {/* 聊天区域 */}
        <div className="bg-white rounded-lg shadow-md flex flex-col h-96">
          {/* 消息列表 */}
          <div className="flex-1 p-4 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                暂无消息，开始沟通吧
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => {
                  const isOwnMessage = message.sender.id === session.user.id
                  
                  return (
                    <div
                      key={message.id}
                      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                        <div className={`rounded-lg px-4 py-2 ${
                          isOwnMessage 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-gray-200 text-gray-900'
                        }`}>
                          {message.content && (
                            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                          )}
                          
                          {message.images && message.images.length > 0 && (
                            <div className="mt-2 space-y-2">
                              {message.images.map((image, index) => (
                                <img
                                  key={index}
                                  src={image}
                                  alt={`消息图片 ${index + 1}`}
                                  className="max-w-full h-auto rounded cursor-pointer"
                                  onClick={() => window.open(image, '_blank')}
                                />
                              ))}
                            </div>
                          )}
                        </div>
                        
                        <div className={`text-xs text-gray-500 mt-1 ${
                          isOwnMessage ? 'text-right' : 'text-left'
                        }`}>
                          {message.sender.name} · {formatTime(message.createdAt)}
                        </div>
                      </div>
                    </div>
                  )
                })}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* 输入区域 */}
          {!['COMPLETED', 'CANCELLED'].includes(request.status) && (
            <div className="border-t p-4">
              {/* 选中的图片预览 */}
              {selectedImages.length > 0 && (
                <div className="mb-3 flex space-x-2">
                  {selectedImages.map((file, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`预览 ${index + 1}`}
                        className="w-16 h-16 object-cover rounded"
                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="flex space-x-2">
                <div className="flex-1">
                  <textarea
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入消息..."
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 resize-none"
                  />
                </div>
                
                <div className="flex flex-col space-y-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageSelect}
                    className="hidden"
                  />
                  
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={selectedImages.length >= 3}
                    className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-2 rounded-md text-sm"
                  >
                    图片
                  </button>
                  
                  <button
                    onClick={handleSendMessage}
                    disabled={sending || (!messageInput.trim() && selectedImages.length === 0)}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-3 py-2 rounded-md text-sm"
                  >
                    {sending ? '发送中' : '发送'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 提示信息 */}
        {['COMPLETED', 'CANCELLED'].includes(request.status) && (
          <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
              </svg>
              <span className="text-yellow-800">
                该售后申请已{request.status === 'COMPLETED' ? '完成' : '取消'}，无法继续发送消息
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
