'use client'

import { useState } from 'react'
import { signIn, signOut, useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  User, 
  LogIn, 
  LogOut, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  RefreshCw
} from 'lucide-react'

export default function TestLoginPage() {
  const { data: session, status } = useSession()
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('password')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [profileData, setProfileData] = useState<any>(null)
  const [profileLoading, setProfileLoading] = useState(false)

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false
      })

      if (result?.error) {
        setMessage(`登录失败: ${result.error}`)
      } else if (result?.ok) {
        setMessage('登录成功!')
      }
    } catch (error) {
      setMessage('登录过程中出现错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    setIsLoading(true)
    try {
      await signOut({ redirect: false })
      setMessage('已退出登录')
      setProfileData(null)
    } catch (error) {
      setMessage('退出登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  const testProfileAPI = async () => {
    setProfileLoading(true)
    setMessage('')

    try {
      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      console.log('Profile API response:', response.status)

      if (response.ok) {
        const data = await response.json()
        setProfileData(data)
        setMessage('用户资料获取成功!')
      } else {
        const errorText = await response.text()
        setMessage(`获取用户资料失败: ${response.status} - ${errorText}`)
        setProfileData(null)
      }
    } catch (error) {
      setMessage(`网络错误: ${error}`)
      setProfileData(null)
    } finally {
      setProfileLoading(false)
    }
  }

  const fixUserRecord = async () => {
    setProfileLoading(true)
    setMessage('')

    try {
      const response = await fetch('/api/user/fix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setMessage('用户记录修复成功!')
        console.log('Fix result:', data)
        
        // 修复后重新获取资料
        setTimeout(() => {
          testProfileAPI()
        }, 1000)
      } else {
        const errorText = await response.text()
        setMessage(`修复失败: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      setMessage(`修复错误: ${error}`)
    } finally {
      setProfileLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">🧪 登录和资料测试</h1>
        <p className="text-gray-600">
          测试用户登录、会话管理和用户资料API功能
        </p>
      </div>

      {/* 会话状态 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>会话状态</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">状态:</span>
              <Badge variant={status === 'authenticated' ? 'default' : 'secondary'}>
                {status === 'loading' ? '加载中...' : 
                 status === 'authenticated' ? '已登录' : '未登录'}
              </Badge>
            </div>
            
            {session?.user && (
              <>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">用户ID:</span>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                    {session.user.id}
                  </code>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">邮箱:</span>
                  <span className="text-sm">{session.user.email}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">姓名:</span>
                  <span className="text-sm">{session.user.name || '未设置'}</span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 登录表单 */}
      {status !== 'authenticated' && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <LogIn className="h-5 w-5" />
              <span>登录测试</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <Label htmlFor="email">邮箱</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="password"
                />
              </div>
              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    登录中...
                  </>
                ) : (
                  <>
                    <LogIn className="h-4 w-4 mr-2" />
                    登录
                  </>
                )}
              </Button>
            </form>

            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-sm text-blue-800 font-medium mb-1">测试账户:</p>
              <div className="text-xs text-blue-700 space-y-1">
                <div>• <EMAIL> / password</div>
                <div>• <EMAIL> / password</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 已登录操作 */}
      {status === 'authenticated' && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>已登录操作</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button onClick={testProfileAPI} disabled={profileLoading}>
                {profileLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                测试获取资料
              </Button>
              
              <Button onClick={fixUserRecord} disabled={profileLoading} variant="outline">
                {profileLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                修复用户记录
              </Button>
              
              <Button onClick={handleLogout} disabled={isLoading} variant="destructive">
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <LogOut className="h-4 w-4 mr-2" />
                )}
                退出登录
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 用户资料数据 */}
      {profileData && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>用户资料数据</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">ID:</span>
                <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1">
                  {profileData.id}
                </div>
              </div>
              <div>
                <span className="font-medium">邮箱:</span>
                <div className="mt-1">{profileData.email}</div>
              </div>
              <div>
                <span className="font-medium">姓名:</span>
                <div className="mt-1">{profileData.name || '未设置'}</div>
              </div>
              <div>
                <span className="font-medium">信用分数:</span>
                <div className="mt-1">{profileData.creditScore}</div>
              </div>
              <div>
                <span className="font-medium">保证金余额:</span>
                <div className="mt-1">¥{profileData.depositBalance}</div>
              </div>
              <div>
                <span className="font-medium">状态:</span>
                <div className="mt-1">
                  <Badge variant={profileData.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {profileData.status}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 消息显示 */}
      {message && (
        <Alert className={message.includes('成功') ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {message.includes('成功') ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={message.includes('成功') ? 'text-green-800' : 'text-red-800'}>
            {message}
          </AlertDescription>
        </Alert>
      )}

      {/* 调试信息 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-xs">
            <div>
              <span className="font-medium">NextAuth状态:</span> {status}
            </div>
            <div>
              <span className="font-medium">会话数据:</span>
              <pre className="mt-1 bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
