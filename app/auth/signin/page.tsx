'use client'

import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { getDeviceInfo, getLocationInfo } from '@/lib/device-info'

export default function SignInPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    // 清除对应字段的错误信息
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.email) {
      newErrors.email = '邮箱为必填项'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '邮箱格式不正确'
    }

    if (!formData.password) {
      newErrors.password = '密码为必填项'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      const result = await signIn('credentials', {
        email: formData.email,
        password: formData.password,
        redirect: false,
      })

      if (result?.error) {
        setErrors({ submit: '邮箱或密码错误' })

        // 记录登录失败（异步，不阻塞UI）
        setTimeout(async () => {
          try {
            const deviceInfo = getDeviceInfo()
            // 地理位置获取是可选的，不阻塞登录流程
            let location: string | null = null
            try {
              location = await Promise.race([
                getLocationInfo(),
                new Promise<string | null>(resolve => setTimeout(() => resolve(null), 2000)) // 2秒超时
              ])
            } catch {
              // 忽略地理位置获取错误
            }

            await fetch('/api/auth/login-record', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                success: false,
                email: formData.email,
                deviceInfo: {
                  ...deviceInfo,
                  location
                }
              })
            })
          } catch (recordError) {
            console.error('记录登录失败:', recordError)
          }
        }, 0)
      } else {
        // 登录成功，记录登录活动（异步，不阻塞UI）
        setTimeout(async () => {
          try {
            const deviceInfo = getDeviceInfo()
            // 地理位置获取是可选的，不阻塞登录流程
            let location: string | null = null
            try {
              location = await Promise.race([
                getLocationInfo(),
                new Promise<string | null>(resolve => setTimeout(() => resolve(null), 2000)) // 2秒超时
              ])
            } catch {
              // 忽略地理位置获取错误
            }

            await fetch('/api/auth/login-record', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                success: true,
                email: formData.email,
                deviceInfo: {
                  ...deviceInfo,
                  location
                }
              })
            })
          } catch (recordError) {
            console.error('记录登录成功:', recordError)
          }
        }, 0)

        // 检查用户状态
        try {
          const statusResponse = await fetch('/api/user/status')
          if (statusResponse.ok) {
            const statusData = await statusResponse.json()
            if (statusData.status.isBanned) {
              // 用户被封禁，重定向到封禁页面
              router.push('/banner')
            } else {
              // 正常用户，重定向到首页
              router.push('/')
            }
          } else {
            // 状态检查失败，默认重定向到首页
            router.push('/')
          }
        } catch (error) {
          console.error('检查用户状态失败:', error)
          router.push('/')
        }
        router.refresh()
      }
    } catch (error) {
      setErrors({ submit: '登录失败，请稍后重试' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            登录比特市场
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            还没有账户？{' '}
            <Link href="/auth/register" className="font-medium text-blue-600 hover:text-blue-500">
              立即注册
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* 邮箱 */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                邮箱地址
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="请输入邮箱地址"
              />
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
            </div>

            {/* 密码 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                密码
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="请输入密码"
              />
              {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
            </div>
          </div>

          {errors.submit && (
            <div className="text-red-600 text-sm text-center">{errors.submit}</div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '登录中...' : '登录'}
            </button>
          </div>

          <div className="text-center">
            <Link href="/auth/forgot-password" className="text-sm text-blue-600 hover:text-blue-500">
              忘记密码？
            </Link>
          </div>
        </form>
      </div>
    </div>
  )
}
