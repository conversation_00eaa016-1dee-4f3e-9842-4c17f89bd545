'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'

interface PendingReview {
  orderId: string
  orderNumber: string
  product: {
    id: string
    title: string
    price: number
  }
  otherParty: {
    id: string
    name: string | null
    email: string | null
  }
  userRole: 'buyer' | 'seller'
  reviewType: string
  completedAt: string
  reviewDeadline: string
  canReview: boolean
}

interface Review {
  id: string
  rating: number
  type: string
  createdAt: string
  reviewer: {
    id: string
    name: string | null
    email: string | null
  }
  reviewee: {
    id: string
    name: string | null
    email: string | null
  }
  product: {
    id: string
    title: string
  }
}

export default function ReviewsPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'pending' | 'given' | 'received'>('pending')
  const [pendingReviews, setPendingReviews] = useState<PendingReview[]>([])
  const [givenReviews, setGivenReviews] = useState<Review[]>([])
  const [receivedReviews, setReceivedReviews] = useState<Review[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [submitLoading, setSubmitLoading] = useState<string | null>(null)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    fetchData()
  }, [session, activeTab])

  const fetchData = async () => {
    setIsLoading(true)
    try {
      if (activeTab === 'pending') {
        const response = await fetch('/api/reviews/pending')
        if (response.ok) {
          const data = await response.json()
          setPendingReviews(data.pendingReviews)
        }
      } else {
        const response = await fetch(`/api/reviews?type=${activeTab}`)
        if (response.ok) {
          const data = await response.json()
          if (activeTab === 'given') {
            setGivenReviews(data.reviews)
          } else {
            setReceivedReviews(data.reviews)
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitReview = async (orderId: string, rating: number, reviewType: string) => {
    setSubmitLoading(orderId)
    try {
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          rating,
          type: reviewType
        }),
      })

      if (response.ok) {
        alert('评价提交成功！')
        fetchData() // 刷新数据
      } else {
        const data = await response.json()
        alert(data.error || '评价提交失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitLoading(null)
    }
  }

  const getRatingStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => interactive && onRatingChange && onRatingChange(star)}
            className={`text-lg ${
              star <= rating 
                ? 'text-yellow-400' 
                : 'text-gray-300'
            } ${interactive ? 'hover:text-yellow-400 cursor-pointer' : 'cursor-default'}`}
            disabled={!interactive}
          >
            ★
          </button>
        ))}
      </div>
    )
  }

  const getTimeRemaining = (deadline: string) => {
    const now = new Date()
    const deadlineDate = new Date(deadline)
    const diff = deadlineDate.getTime() - now.getTime()
    
    if (diff <= 0) return '已过期'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days}天${hours}小时`
    return `${hours}小时`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/orders" className="text-gray-700 hover:text-gray-900">
                我的订单
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">评价管理</h1>
            <p className="mt-2 text-gray-600">管理您的交易评价</p>
          </div>

          {/* 标签页 */}
          <div className="mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('pending')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'pending'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                待评价
              </button>
              <button
                onClick={() => setActiveTab('given')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'given'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                我的评价
              </button>
              <button
                onClick={() => setActiveTab('received')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'received'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                收到的评价
              </button>
            </nav>
          </div>

          {/* 内容区域 */}
          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 待评价 */}
              {activeTab === 'pending' && (
                <>
                  {pendingReviews.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-lg text-gray-600">暂无待评价的订单</div>
                      <Link
                        href="/orders"
                        className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                      >
                        查看我的订单
                      </Link>
                    </div>
                  ) : (
                    pendingReviews.map((review) => (
                      <PendingReviewCard
                        key={review.orderId}
                        review={review}
                        onSubmit={handleSubmitReview}
                        isLoading={submitLoading === review.orderId}
                        getRatingStars={getRatingStars}
                        getTimeRemaining={getTimeRemaining}
                      />
                    ))
                  )}
                </>
              )}

              {/* 我的评价 */}
              {activeTab === 'given' && (
                <>
                  {givenReviews.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-lg text-gray-600">您还没有发表过评价</div>
                    </div>
                  ) : (
                    givenReviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                        type="given"
                        getRatingStars={getRatingStars}
                      />
                    ))
                  )}
                </>
              )}

              {/* 收到的评价 */}
              {activeTab === 'received' && (
                <>
                  {receivedReviews.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-lg text-gray-600">您还没有收到评价</div>
                    </div>
                  ) : (
                    receivedReviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                        type="received"
                        getRatingStars={getRatingStars}
                      />
                    ))
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// 待评价卡片组件
function PendingReviewCard({ 
  review, 
  onSubmit, 
  isLoading, 
  getRatingStars, 
  getTimeRemaining 
}: {
  review: PendingReview
  onSubmit: (orderId: string, rating: number, reviewType: string) => void
  isLoading: boolean
  getRatingStars: (rating: number, interactive?: boolean, onRatingChange?: (rating: number) => void) => React.JSX.Element
  getTimeRemaining: (deadline: string) => string
}) {
  const [selectedRating, setSelectedRating] = useState(5)

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{review.product.title}</h3>
            <p className="text-sm text-gray-600">
              订单号: {review.orderNumber} | {formatUSDT(review.product.price)}
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">
              剩余时间: {getTimeRemaining(review.reviewDeadline)}
            </div>
            <div className="text-xs text-gray-400">
              {review.userRole === 'buyer' ? '评价卖家' : '评价买家'}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 mb-2">
              {review.userRole === 'buyer' ? '卖家' : '买家'}: {review.otherParty.name || '匿名用户'}
            </p>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">评分:</span>
              {getRatingStars(selectedRating, true, setSelectedRating)}
            </div>
          </div>
          <button
            onClick={() => onSubmit(review.orderId, selectedRating, review.reviewType)}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
          >
            {isLoading ? '提交中...' : '提交评价'}
          </button>
        </div>
      </div>
    </div>
  )
}

// 评价卡片组件
function ReviewCard({ 
  review, 
  type, 
  getRatingStars 
}: {
  review: Review
  type: 'given' | 'received'
  getRatingStars: (rating: number, interactive?: boolean) => React.JSX.Element
}) {
  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{review.product.title}</h3>
            <p className="text-sm text-gray-600">
              {type === 'given' ? '您评价了' : '评价来自'}: {
                type === 'given' 
                  ? (review.reviewee.name || '匿名用户')
                  : (review.reviewer.name || '匿名用户')
              }
            </p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2 mb-1">
              {getRatingStars(review.rating)}
              <span className="text-sm text-gray-600">({review.rating}/5)</span>
            </div>
            <div className="text-xs text-gray-400">
              {new Date(review.createdAt).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
