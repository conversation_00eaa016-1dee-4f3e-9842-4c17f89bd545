'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function DebugProfile() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user?.id) {
      fetchProfile()
    }
  }, [session, status, router])

  const fetchProfile = async () => {
    setLoading(true)
    setError(null)
    try {
      console.log('开始获取用户资料...')
      
      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      console.log('API响应状态:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('获取到的用户数据:', data)
        setProfile(data)
      } else {
        const errorData = await response.text()
        console.error('API错误:', errorData)
        setError(`API错误: ${response.status} - ${errorData}`)
      }
    } catch (error) {
      console.error('网络错误:', error)
      setError(`网络错误: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg font-semibold">错误</div>
          <p className="mt-2 text-gray-600">{error}</p>
          <button
            onClick={fetchProfile}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">用户资料调试页面</h1>
          
          {/* Session 信息 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Session 信息</h2>
            <div className="bg-gray-50 rounded p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
          </div>

          {/* Profile 数据 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Profile 数据</h2>
            <div className="bg-gray-50 rounded p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(profile, null, 2)}
              </pre>
            </div>
          </div>

          {/* 中间人字段检查 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">中间人字段检查</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded p-4">
                <h3 className="font-medium text-blue-900 mb-2">基础字段</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">isMediator:</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      profile?.isMediator 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {String(profile?.isMediator)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">mediatorStatus:</span>
                    <span className="ml-2 font-mono">{profile?.mediatorStatus || 'null'}</span>
                  </div>
                  <div>
                    <span className="font-medium">mediatorFeeRate:</span>
                    <span className="ml-2 font-mono">{profile?.mediatorFeeRate || 'null'}</span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded p-4">
                <h3 className="font-medium text-green-900 mb-2">统计字段</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">mediatorReputation:</span>
                    <span className="ml-2 font-mono">{profile?.mediatorReputation || 0}</span>
                  </div>
                  <div>
                    <span className="font-medium">mediatorSuccessRate:</span>
                    <span className="ml-2 font-mono">{profile?.mediatorSuccessRate || 0}%</span>
                  </div>
                  <div>
                    <span className="font-medium">mediatorTotalOrders:</span>
                    <span className="ml-2 font-mono">{profile?.mediatorTotalOrders || 0}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 条件渲染测试 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">条件渲染测试</h2>
            <div className="space-y-4">
              <div className="bg-yellow-50 rounded p-4">
                <h3 className="font-medium text-yellow-900 mb-2">条件检查结果</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">profile 存在:</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      profile ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {String(!!profile)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">profile?.isMediator:</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      profile?.isMediator ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {String(!!profile?.isMediator)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">应显示中间人控制台:</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      profile?.isMediator ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {String(!!profile?.isMediator)}
                    </span>
                  </div>
                </div>
              </div>

              {/* 模拟中间人控制台卡片 */}
              {profile?.isMediator && (
                <div className="bg-orange-50 border-2 border-orange-200 rounded p-4">
                  <h3 className="font-medium text-orange-900 mb-2">🛡️ 中间人控制台 (模拟)</h3>
                  <p className="text-sm text-orange-800 mb-2">
                    管理调解服务
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      {profile.mediatorStatus === 'ACTIVE' ? '已认证' : '待认证'}
                    </span>
                  </p>
                  <div className="grid grid-cols-3 gap-4 text-center text-sm">
                    <div>
                      <div className="font-semibold text-gray-900">{profile.mediatorTotalOrders || 0}</div>
                      <div className="text-xs text-gray-500">调解订单</div>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{profile.mediatorSuccessRate || 0}%</div>
                      <div className="text-xs text-gray-500">成功率</div>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{profile.mediatorReputation || 0}</div>
                      <div className="text-xs text-gray-500">信誉值</div>
                    </div>
                  </div>
                </div>
              )}

              {!profile?.isMediator && (
                <div className="bg-gray-50 rounded p-4">
                  <p className="text-sm text-gray-600">
                    ❌ 不是中间人用户，不显示中间人控制台
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={fetchProfile}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              刷新数据
            </button>
            <button
              onClick={() => router.push('/profile')}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              返回个人资料页面
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
