'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import {
  ArrowLeftIcon,
  CalendarIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SpeakerWaveIcon
} from '@heroicons/react/24/outline'

interface Announcement {
  id: string
  title: string
  content: string
  summary: string | null
  category: string
  priority: string
  targetUsers: string
  status: string
  isSticky: boolean
  showOnHome: boolean
  publishAt: string | null
  expireAt: string | null
  viewCount: number
  createdAt: string
  updatedAt: string
}

export default function AnnouncementDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const [announcementId, setAnnouncementId] = useState<string>('')
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  // 获取路由参数
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setAnnouncementId(resolvedParams.id)
    }
    getParams()
  }, [params])

  // 加载公告数据
  useEffect(() => {
    if (announcementId) {
      fetchAnnouncement()
    }
  }, [announcementId])

  const fetchAnnouncement = async () => {
    try {
      setIsLoading(true)
      
      // 先增加查看次数
      await fetch('/api/announcements/home', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ announcementId }),
      }).catch(() => {}) // 忽略错误，不影响主要功能

      // 获取公告详情（使用管理员API，但只获取已发布的公告）
      const response = await fetch(`/api/admin/announcements/${announcementId}`)
      
      if (response.ok) {
        const data: Announcement = await response.json()
        // 只显示已发布的公告
        if (data.status === 'PUBLISHED') {
          setAnnouncement(data)
        } else {
          setError('公告不存在或未发布')
        }
      } else if (response.status === 404) {
        setError('公告不存在')
      } else {
        setError('加载公告失败')
      }
    } catch (error) {
      console.error('加载公告失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 获取分类显示
  const getCategoryDisplay = (category: string) => {
    const categoryMap = {
      'GENERAL': { text: '一般', icon: InformationCircleIcon, color: 'text-blue-600' },
      'URGENT': { text: '紧急', icon: ExclamationTriangleIcon, color: 'text-red-600' },
      'MAINTENANCE': { text: '维护', icon: SpeakerWaveIcon, color: 'text-orange-600' },
      'FEATURE': { text: '功能', icon: InformationCircleIcon, color: 'text-green-600' },
      'SECURITY': { text: '安全', icon: ExclamationTriangleIcon, color: 'text-purple-600' }
    }
    return categoryMap[category as keyof typeof categoryMap] || { 
      text: category, 
      icon: InformationCircleIcon, 
      color: 'text-gray-600' 
    }
  }

  // 获取优先级显示
  const getPriorityDisplay = (priority: string) => {
    const priorityMap = {
      'LOW': { text: '低', color: 'bg-gray-100 text-gray-800' },
      'NORMAL': { text: '普通', color: 'bg-blue-100 text-blue-800' },
      'HIGH': { text: '高', color: 'bg-orange-100 text-orange-800' },
      'URGENT': { text: '紧急', color: 'bg-red-100 text-red-800' }
    }
    return priorityMap[priority as keyof typeof priorityMap] || { 
      text: priority, 
      color: 'bg-gray-100 text-gray-800' 
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-lg">加载中...</div>
        </div>
      </div>
    )
  }

  if (error || !announcement) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-20">
            <div className="text-6xl mb-4">📢</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {error || '公告不存在'}
            </h2>
            <p className="text-gray-600 mb-6">
              您访问的公告可能已被删除或未发布
            </p>
            <Link
              href="/"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const categoryDisplay = getCategoryDisplay(announcement.category)
  const priorityDisplay = getPriorityDisplay(announcement.priority)
  const CategoryIcon = categoryDisplay.icon

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            返回首页
          </Link>
        </div>

        {/* 公告内容 */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* 公告头部 */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <CategoryIcon className={`h-5 w-5 mr-2 ${categoryDisplay.color}`} />
                  <span className={`text-sm font-medium ${categoryDisplay.color}`}>
                    {categoryDisplay.text}
                  </span>
                  <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityDisplay.color}`}>
                    {priorityDisplay.text}
                  </span>
                  {announcement.isSticky && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                      置顶
                    </span>
                  )}
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {announcement.title}
                </h1>
                {announcement.summary && (
                  <p className="text-lg text-gray-600">
                    {announcement.summary}
                  </p>
                )}
              </div>
            </div>

            {/* 元信息 */}
            <div className="flex items-center text-sm text-gray-500 mt-4 space-x-6">
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 mr-1" />
                <span>
                  {new Date(announcement.publishAt || announcement.createdAt).toLocaleDateString('zh-CN')}
                </span>
              </div>
              <div className="flex items-center">
                <EyeIcon className="h-4 w-4 mr-1" />
                <span>{announcement.viewCount} 次查看</span>
              </div>
            </div>
          </div>

          {/* 公告正文 */}
          <div className="px-6 py-6">
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed text-base">
                {announcement.content}
              </div>
            </div>
          </div>

          {/* 公告底部信息 */}
          {announcement.expireAt && (
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
              <div className="flex items-center text-sm text-gray-600">
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span>
                  此公告有效期至：{new Date(announcement.expireAt).toLocaleString('zh-CN')}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* 相关链接 */}
        <div className="mt-8 text-center">
          <div className="space-x-4">
            <Link
              href="/products"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors"
            >
              浏览商品
            </Link>
            <Link
              href="/demands"
              className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors"
            >
              需求广场
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
