'use client'

import { useSession, signOut } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import ImageUpload from '@/components/ImageUpload'


interface UserProfile {
  id: string
  name: string | null
  email: string | null
  avatar: string | null
  binanceUid: string | null
  bnbWalletAddress: string | null
  city: string | null
  district: string | null
  creditScore: number
  depositBalance: number
  status: string
  isGuarantor: boolean
  averageRating: number
  totalReviews: number
  // 中间人相关字段
  isMediator: boolean
  mediatorStatus: string
  mediatorFeeRate: number | null
  mediatorReputation: number
  mediatorVerifiedAt: string | null
  bnbWalletVerified: boolean
  mediatorExperience: string | null
  mediatorIntroduction: string | null
  mediatorSuccessRate: number
  mediatorTotalOrders: number
}

export default function ProfilePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    avatar: '',
    binanceUid: '',
    bnbWalletAddress: '',
    city: '',
    district: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user?.id) {
      fetchProfile()
    }
  }, [session, status, router])

  const fetchProfile = async () => {
    setIsLoading(true)
    try {
      console.log('Fetching profile for session:', session?.user?.id)

      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      console.log('Profile API response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Profile data received:', data)
        setProfile(data)
        setFormData({
          name: data.name || '',
          avatar: data.avatar || '',
          binanceUid: data.binanceUid || '',
          bnbWalletAddress: data.bnbWalletAddress || '',
          city: data.city || '',
          district: data.district || ''
        })
      } else if (response.status === 401) {
        console.log('Unauthorized, redirecting to signin')
        router.push('/auth/signin')
      } else if (response.status === 404) {
        console.error('User not found in database, attempting to fix...')

        // 尝试自动修复用户记录
        try {
          const fixResponse = await fetch('/api/user/fix', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include'
          })

          if (fixResponse.ok) {
            const fixData = await fixResponse.json()
            console.log('User record fixed:', fixData)

            // 重新获取用户资料
            const retryResponse = await fetch('/api/user/profile', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include'
            })

            if (retryResponse.ok) {
              const retryData = await retryResponse.json()
              setProfile(retryData)
              setFormData({
                name: retryData.name || '',
                avatar: retryData.avatar || '',
                binanceUid: retryData.binanceUid || '',
                bnbWalletAddress: retryData.bnbWalletAddress || '',
                city: retryData.city || '',
                district: retryData.district || ''
              })
              return
            }
          }
        } catch (fixError) {
          console.error('Failed to fix user record:', fixError)
        }

        // 如果修复失败，使用会话信息创建默认资料
        const defaultProfile = {
          id: session?.user?.id || '',
          name: session?.user?.name || null,
          email: session?.user?.email || '',
          avatar: null,
          binanceUid: null,
          bnbWalletAddress: null,
          city: null,
          district: null,
          creditScore: 30,
          depositBalance: 0,
          status: 'ACTIVE',
          isGuarantor: false,
          averageRating: 0,
          totalReviews: 0,
          // 中间人相关字段
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          mediatorFeeRate: null,
          mediatorReputation: 0,
          mediatorVerifiedAt: null,
          bnbWalletVerified: false,
          mediatorExperience: null,
          mediatorIntroduction: null,
          mediatorSuccessRate: 0,
          mediatorTotalOrders: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        setProfile(defaultProfile)
        setFormData({
          name: defaultProfile.name || '',
          avatar: defaultProfile.avatar || '',
          binanceUid: defaultProfile.binanceUid || '',
          bnbWalletAddress: defaultProfile.bnbWalletAddress || '',
          city: defaultProfile.city || '',
          district: defaultProfile.district || ''
        })
      } else {
        console.error('Failed to fetch profile:', response.status)
        const errorData = await response.text()
        console.error('Error details:', errorData)

        // 其他错误，设置默认资料
        const defaultProfile = {
          id: session?.user?.id || '',
          name: session?.user?.name || null,
          email: session?.user?.email || '',
          avatar: null,
          binanceUid: null,
          bnbWalletAddress: null,
          city: null,
          district: null,
          creditScore: 30,
          depositBalance: 0,
          status: 'ACTIVE',
          isGuarantor: false,
          averageRating: 0,
          totalReviews: 0,
          // 中间人相关字段
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          mediatorFeeRate: null,
          mediatorReputation: 0,
          mediatorVerifiedAt: null,
          bnbWalletVerified: false,
          mediatorExperience: null,
          mediatorIntroduction: null,
          mediatorSuccessRate: 0,
          mediatorTotalOrders: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        setProfile(defaultProfile)
        setFormData({
          name: defaultProfile.name || '',
          avatar: defaultProfile.avatar || '',
          binanceUid: defaultProfile.binanceUid || '',
          bnbWalletAddress: defaultProfile.bnbWalletAddress || '',
          city: defaultProfile.city || '',
          district: defaultProfile.district || ''
        })
      }
    } catch (error) {
      console.error('Fetch profile error:', error)
      // 网络错误时也设置默认资料
      const defaultProfile = {
        id: session?.user?.id || '',
        name: session?.user?.name || null,
        email: session?.user?.email || '',
        avatar: null,
        binanceUid: null,
        bnbWalletAddress: null,
        city: null,
        district: null,
        creditScore: 30,
        depositBalance: 0,
        status: 'ACTIVE',
        isGuarantor: false,
        averageRating: 0,
        totalReviews: 0,
        // 中间人相关字段
        isMediator: false,
        mediatorStatus: 'INACTIVE',
        mediatorFeeRate: null,
        mediatorReputation: 0,
        mediatorVerifiedAt: null,
        bnbWalletVerified: false,
        mediatorExperience: null,
        mediatorIntroduction: null,
        mediatorSuccessRate: 0,
        mediatorTotalOrders: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      setProfile(defaultProfile)
      setFormData({
        name: defaultProfile.name || '',
        avatar: defaultProfile.avatar || '',
        binanceUid: defaultProfile.binanceUid || '',
        bnbWalletAddress: defaultProfile.bnbWalletAddress || '',
        city: defaultProfile.city || '',
        district: defaultProfile.district || ''
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleAvatarUpload = (url: string) => {
    setFormData(prev => ({ ...prev, avatar: url }))
    saveAvatar(url)
  }

  const saveAvatar = async (avatarUrl: string) => {
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ avatar: avatarUrl }),
      })

      if (response.ok) {
        const updatedProfile = await response.json()
        setProfile(updatedProfile)
        alert('头像更新成功！')
      } else {
        const data = await response.json()
        alert(data.error || '头像更新失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const updatedProfile = await response.json()
        setProfile(updatedProfile)
        setIsEditing(false)
        alert('个人信息更新成功！')
      } else {
        const data = await response.json()
        if (data.errors) {
          setErrors(data.errors)
        } else {
          alert(data.error || '更新失败，请稍后重试')
        }
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    if (confirm('确定要退出登录吗？')) {
      await signOut({ callbackUrl: '/' })
    }
  }

  if (status === 'loading' || !profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">

          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  个人信息
                </h3>
                {!isEditing && (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                  >
                    编辑资料
                  </button>
                )}
              </div>

              {isEditing ? (
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* 头像上传区域 */}
                  <div className="flex flex-col items-center space-y-4">
                    <div className="text-center">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">头像</h4>
                      {profile?.avatar ? (
                        <div className="mb-4">
                          <img
                            src={profile.avatar}
                            alt="当前头像"
                            className="w-20 h-20 rounded-full object-cover mx-auto border-2 border-gray-200"
                          />
                        </div>
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-4">
                          <span className="text-gray-500 text-sm">无头像</span>
                        </div>
                      )}
                    </div>
                    <div className="w-full max-w-md">
                      <ImageUpload
                        type="avatar"
                        onUpload={handleAvatarUpload}
                        currentImage={profile?.avatar || ''}
                        className="w-full"
                      />
                      <p className="text-xs text-gray-500 mt-2 text-center">
                        支持JPG、PNG、WEBP格式，200x200到500x500像素，最大2MB
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        昵称
                      </label>
                      <input
                        type="text"
                        name="name"
                        id="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入昵称"
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="binanceUid" className="block text-sm font-medium text-gray-700">
                        币安UID
                      </label>
                      <input
                        type="text"
                        name="binanceUid"
                        id="binanceUid"
                        value={formData.binanceUid}
                        onChange={handleChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入币安UID"
                      />
                      {errors.binanceUid && (
                        <p className="mt-1 text-sm text-red-600">{errors.binanceUid}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="bnbWalletAddress" className="block text-sm font-medium text-gray-700">
                        BNB钱包地址
                      </label>
                      <input
                        type="text"
                        name="bnbWalletAddress"
                        id="bnbWalletAddress"
                        value={formData.bnbWalletAddress}
                        onChange={handleChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入BNB钱包地址"
                      />
                      {errors.bnbWalletAddress && (
                        <p className="mt-1 text-sm text-red-600">{errors.bnbWalletAddress}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                        城市
                      </label>
                      <input
                        type="text"
                        name="city"
                        id="city"
                        value={formData.city}
                        onChange={handleChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入城市"
                      />
                      {errors.city && (
                        <p className="mt-1 text-sm text-red-600">{errors.city}</p>
                      )}
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="district" className="block text-sm font-medium text-gray-700">
                        区域
                      </label>
                      <input
                        type="text"
                        name="district"
                        id="district"
                        value={formData.district}
                        onChange={handleChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="请输入区域"
                      />
                      {errors.district && (
                        <p className="mt-1 text-sm text-red-600">{errors.district}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setIsEditing(false)}
                      className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {isLoading ? '保存中...' : '保存'}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-6">
                  {/* 头像显示区域 */}
                  <div className="flex flex-col items-center space-y-2">
                    <h4 className="text-sm font-medium text-gray-500">头像</h4>
                    {profile.avatar ? (
                      <img
                        src={profile.avatar}
                        alt="用户头像"
                        className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                      />
                    ) : (
                      <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-500 text-sm">无头像</span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">邮箱</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.email}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">昵称</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.name || '未设置'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">币安UID</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.binanceUid || '未设置'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">BNB钱包地址</dt>
                      <dd className="mt-1 text-sm text-gray-900 break-all">{profile.bnbWalletAddress || '未设置'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">城市</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.city || '未设置'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">区域</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.district || '未设置'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">信用分</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.creditScore}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">担保金余额</dt>
                      <dd className="mt-1 text-sm text-gray-900">{profile.depositBalance} USDT</dd>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>



          {/* 功能键区域 - 下方排布 */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mt-6">



            {/* 发布商品 */}
            <Link
              href="/products/create"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">发布商品</dt>
                      <dd className="text-lg font-medium text-gray-900">添加商品</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 发布需求 */}
            <Link
              href="/demands/create"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">发布需求</dt>
                      <dd className="text-lg font-medium text-gray-900">求购商品</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 聊天中心 */}
            <Link
              href="/chat"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">聊天中心</dt>
                      <dd className="text-lg font-medium text-gray-900">买卖沟通</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 收藏夹 */}
            <Link
              href="/favorites"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-pink-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">收藏夹</dt>
                      <dd className="text-lg font-medium text-gray-900">我的收藏</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 保证金管理 */}
            <Link
              href="/deposit"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-emerald-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">保证金管理</dt>
                      <dd className="text-lg font-medium text-gray-900">担保金管理</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 信用评级 */}
            <Link
              href="/credit"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-cyan-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">信用评级</dt>
                      <dd className="text-lg font-medium text-gray-900">信用记录</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 我的订单 */}
            <Link
              href="/orders"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">我的订单</dt>
                      <dd className="text-lg font-medium text-gray-900">查看订单</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 售后申请 */}
            <Link
              href="/after-sales"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">售后申请</dt>
                      <dd className="text-lg font-medium text-gray-900">售后服务</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 我的商品 */}
            <Link
              href={`/products/user/${profile?.id || ''}`}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">我的商品</dt>
                      <dd className="text-lg font-medium text-gray-900">管理商品</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 中间人管理器 - 仅对中间人显示 */}
            {profile?.isMediator && (
              <Link
                href="/mediator/dashboard"
                className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">中间人管理器</dt>
                        <dd className="text-lg font-medium text-gray-900">调解服务</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </Link>
            )}

            {/* 反馈助手 */}
            <Link
              href="/radar"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">反馈助手</dt>
                      <dd className="text-lg font-medium text-gray-900">意见反馈</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 账户设置 */}
            <Link
              href="/settings"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gray-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">账户设置</dt>
                      <dd className="text-lg font-medium text-gray-900">安全设置</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 帮助中心 */}
            <Link
              href="/help"
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-amber-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">帮助中心</dt>
                      <dd className="text-lg font-medium text-gray-900">使用指南</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>

            {/* 退出登录 */}
            <button
              onClick={handleLogout}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow text-left"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">退出登录</dt>
                      <dd className="text-lg font-medium text-gray-900">安全退出</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
