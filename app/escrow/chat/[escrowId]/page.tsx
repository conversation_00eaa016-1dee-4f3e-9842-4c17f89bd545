'use client'

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { formatUSDT } from '@/lib/utils'
import { 
  Send, 
  Shield, 
  User, 
  Crown, 
  MessageCircle,
  ArrowLeft,
  Clock,
  CheckCircle
} from 'lucide-react'

interface ChatMessage {
  id: string
  content: string
  senderId: string
  senderName: string
  senderRole: 'buyer' | 'seller' | 'mediator' | 'admin'
  timestamp: string
  messageType: 'text' | 'system'
}

interface EscrowChatData {
  escrowOrder: {
    id: string
    status: string
    amount: number
    order: {
      orderNumber: string
      product: {
        title: string
        images: string[]
      }
    }
    buyer: {
      id: string
      name: string
    }
    seller: {
      id: string
      name: string
    }
    mediator: {
      id: string
      name: string
    }
  }
  chatRoom: {
    id: string
    roomCode: string
    isActive: boolean
  }
  messages: ChatMessage[]
  participants: {
    id: string
    name: string
    role: 'buyer' | 'seller' | 'mediator' | 'admin'
    isOnline: boolean
  }[]
}

export default function EscrowChatPage({ params }: { params: { escrowId: string } }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [chatData, setChatData] = useState<EscrowChatData | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user) {
      fetchChatData()
    }
  }, [session, status, params.escrowId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const fetchChatData = async () => {
    try {
      const response = await fetch(`/api/chat/escrow/${params.escrowId}`)
      const data = await response.json()

      if (data.success) {
        setChatData(data.data)
        setMessages(data.data.messages || [])
      } else {
        console.error('获取聊天数据失败:', data.error)
      }
    } catch (error) {
      console.error('获取聊天数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return

    setSending(true)
    try {
      const response = await fetch(`/api/chat/escrow/${params.escrowId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: newMessage.trim(),
          messageType: 'text'
        })
      })

      const data = await response.json()

      if (data.success) {
        setNewMessage('')
        // 添加新消息到本地状态
        const newMsg: ChatMessage = {
          id: data.data.id,
          content: newMessage.trim(),
          senderId: session?.user?.id || '',
          senderName: session?.user?.name || '',
          senderRole: getUserRole(),
          timestamp: new Date().toISOString(),
          messageType: 'text'
        }
        setMessages(prev => [...prev, newMsg])
      } else {
        console.error('发送消息失败:', data.error)
      }
    } catch (error) {
      console.error('发送消息失败:', error)
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const getUserRole = (): 'buyer' | 'seller' | 'mediator' | 'admin' => {
    if (!session?.user?.id || !chatData) return 'buyer'
    
    if (session.user.id === chatData.escrowOrder.buyer.id) return 'buyer'
    if (session.user.id === chatData.escrowOrder.seller.id) return 'seller'
    if (session.user.id === chatData.escrowOrder.mediator.id) return 'mediator'
    if (session.user.role === 'ADMIN') return 'admin'
    
    return 'buyer'
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'buyer':
        return <User className="w-4 h-4 text-blue-500" />
      case 'seller':
        return <User className="w-4 h-4 text-green-500" />
      case 'mediator':
        return <Shield className="w-4 h-4 text-purple-500" />
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />
      default:
        return <User className="w-4 h-4 text-gray-500" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'buyer':
        return '买家'
      case 'seller':
        return '卖家'
      case 'mediator':
        return '中间人'
      case 'admin':
        return '管理员'
      default:
        return '用户'
    }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'buyer':
        return 'bg-blue-100 text-blue-800'
      case 'seller':
        return 'bg-green-100 text-green-800'
      case 'mediator':
        return 'bg-purple-100 text-purple-800'
      case 'admin':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="w-3 h-3 mr-1" />等待资金</Badge>
      case 'FUNDED':
        return <Badge variant="outline" className="text-blue-600">已到账</Badge>
      case 'SHIPPED':
        return <Badge variant="outline" className="text-purple-600">已发货</Badge>
      case 'DELIVERED':
        return <Badge variant="outline" className="text-green-600">已收货</Badge>
      case 'COMPLETED':
        return <Badge variant="outline" className="text-green-600"><CheckCircle className="w-3 h-3 mr-1" />已完成</Badge>
      case 'DISPUTED':
        return <Badge variant="outline" className="text-red-600">争议中</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (!chatData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">聊天室不存在</h1>
          <Button onClick={() => router.back()}>返回</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-12rem)]">
        {/* 左侧：订单信息和参与者 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 返回按钮 */}
          <Button
            variant="outline"
            onClick={() => router.push(`/escrow/orders/${params.escrowId}`)}
            className="w-full"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回订单详情
          </Button>

          {/* 订单信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">订单信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-2">
                {chatData.escrowOrder.order.product.images?.[0] && (
                  <img
                    src={chatData.escrowOrder.order.product.images[0]}
                    alt={chatData.escrowOrder.order.product.title}
                    className="w-12 h-12 object-cover rounded"
                  />
                )}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {chatData.escrowOrder.order.product.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatUSDT(chatData.escrowOrder.amount)}
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">状态:</span>
                {getStatusBadge(chatData.escrowOrder.status)}
              </div>
            </CardContent>
          </Card>

          {/* 参与者列表 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">参与者 ({chatData.participants.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {chatData.participants.map((participant) => (
                  <div key={participant.id} className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="text-xs">
                        {participant.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium truncate">
                          {participant.name}
                        </p>
                        {participant.id === session?.user?.id && (
                          <span className="text-xs text-blue-600">(您)</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getRoleBadgeColor(participant.role)}`}
                        >
                          {getRoleIcon(participant.role)}
                          <span className="ml-1">{getRoleLabel(participant.role)}</span>
                        </Badge>
                        <div className={`w-2 h-2 rounded-full ${participant.isOnline ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧：聊天区域 */}
        <div className="lg:col-span-3 flex flex-col">
          <Card className="flex-1 flex flex-col">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <MessageCircle className="w-5 h-5 mr-2" />
                托管订单沟通
                <Badge variant="outline" className="ml-2">
                  {chatData.escrowOrder.order.orderNumber}
                </Badge>
              </CardTitle>
            </CardHeader>
            
            {/* 消息列表 */}
            <CardContent className="flex-1 flex flex-col">
              <div className="flex-1 overflow-y-auto space-y-4 mb-4 max-h-96">
                {messages.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>还没有消息，开始对话吧！</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div key={message.id} className="flex items-start space-x-3">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="text-xs">
                          {message.senderName.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium">
                            {message.senderName}
                          </span>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getRoleBadgeColor(message.senderRole)}`}
                          >
                            {getRoleIcon(message.senderRole)}
                            <span className="ml-1">{getRoleLabel(message.senderRole)}</span>
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {new Date(message.timestamp).toLocaleString()}
                          </span>
                        </div>
                        <div className={`p-3 rounded-lg ${
                          message.senderId === session?.user?.id 
                            ? 'bg-blue-100 text-blue-900' 
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* 消息输入 */}
              <div className="flex space-x-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="输入消息..."
                  disabled={sending || !chatData.chatRoom.isActive}
                  className="flex-1"
                />
                <Button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || sending || !chatData.chatRoom.isActive}
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>

              {!chatData.chatRoom.isActive && (
                <p className="text-xs text-gray-500 mt-2 text-center">
                  聊天室已关闭
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
