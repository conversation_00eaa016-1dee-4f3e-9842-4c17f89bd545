'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ShieldCheckIcon, 
  ClockIcon, 
  CurrencyDollarIcon,
  TruckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import Navbar from '@/components/Navbar'
import EscrowOrderStatus from '@/components/escrow/EscrowOrderStatus'

interface EscrowOrder {
  id: string
  orderNumber: string
  status: string
  escrowStatus: string
  totalAmount: number
  escrowFee?: number
  createdAt: string
  product: {
    title: string
    images?: string
  }
  buyer: {
    id: string
    name: string
    avatar?: string
  }
  seller: {
    id: string
    name: string
    avatar?: string
  }
  mediator?: {
    id: string
    name: string
    avatar?: string
    mediatorReputation: number
  }
}

export default function EscrowOrdersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [orders, setOrders] = useState<EscrowOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'buyer' | 'seller' | 'mediator'>('buyer')
  const [statusFilter, setStatusFilter] = useState<string>('')

  useEffect(() => {
    if (status === 'loading') return
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    fetchOrders()
  }, [session, status, activeTab, statusFilter])

  const fetchOrders = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        role: activeTab
      })
      
      if (statusFilter) {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/escrow/orders?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setOrders(data.data.orders)
      }
    } catch (error) {
      console.error('获取托管订单失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleOrderAction = async (orderId: string, action: string, data?: any) => {
    try {
      const response = await fetch(`/api/escrow/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, data })
      })

      const result = await response.json()

      if (result.success) {
        alert('操作成功')
        fetchOrders()
      } else {
        alert(result.error || '操作失败')
      }
    } catch (error) {
      alert('网络错误，请重试')
    }
  }

  const getUserRole = (order: EscrowOrder): 'buyer' | 'seller' | 'mediator' => {
    if (order.buyer.id === session?.user?.id) return 'buyer'
    if (order.seller.id === session?.user?.id) return 'seller'
    if (order.mediator?.id === session?.user?.id) return 'mediator'
    return 'buyer' // 默认
  }

  const getStatusIcon = (escrowStatus: string) => {
    switch (escrowStatus) {
      case 'PENDING':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />
      case 'FUNDED':
        return <CurrencyDollarIcon className="h-5 w-5 text-blue-500" />
      case 'SHIPPED':
        return <TruckIcon className="h-5 w-5 text-purple-500" />
      case 'RELEASED':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'DISPUTED':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (escrowStatus: string) => {
    const statusMap = {
      PENDING: '等待支付',
      FUNDED: '资金已托管',
      SHIPPED: '已发货',
      RELEASED: '已完成',
      DISPUTED: '争议中',
      REFUNDED: '已退款'
    }
    return statusMap[escrowStatus as keyof typeof statusMap] || escrowStatus
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">托管订单管理</h1>
              <p className="text-gray-600">管理您的托管交易订单</p>
            </div>
          </div>
        </div>

        {/* 标签页和筛选 */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <div className="flex items-center justify-between">
              <nav className="-mb-px flex">
                <button
                  onClick={() => setActiveTab('buyer')}
                  className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'buyer'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  我的购买
                </button>
                <button
                  onClick={() => setActiveTab('seller')}
                  className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'seller'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  我的销售
                </button>
                {session?.user?.isMediator && (
                  <button
                    onClick={() => setActiveTab('mediator')}
                    className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'mediator'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    我的托管
                  </button>
                )}
              </nav>
              
              <div className="px-6 py-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="text-sm border border-gray-300 rounded-md px-3 py-1"
                >
                  <option value="">所有状态</option>
                  <option value="PENDING">等待支付</option>
                  <option value="FUNDED">资金已托管</option>
                  <option value="SHIPPED">已发货</option>
                  <option value="RELEASED">已完成</option>
                  <option value="DISPUTED">争议中</option>
                </select>
              </div>
            </div>
          </div>

          {/* 订单列表 */}
          <div className="p-6">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">加载中...</p>
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12">
                <ShieldCheckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无托管订单</h3>
                <p className="text-gray-500 mb-4">
                  {activeTab === 'buyer' && '您还没有使用托管服务购买商品'}
                  {activeTab === 'seller' && '您还没有通过托管服务销售商品'}
                  {activeTab === 'mediator' && '您还没有托管订单需要处理'}
                </p>
                <Link
                  href="/products"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  浏览商品
                </Link>
              </div>
            ) : (
              <div className="space-y-6">
                {orders.map((order) => (
                  <div
                    key={order.id}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {order.product.title}
                          </h3>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(order.escrowStatus)}
                            <span className="text-sm font-medium text-gray-600">
                              {getStatusText(order.escrowStatus)}
                            </span>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                          <div>
                            <span className="font-medium">订单号:</span>
                            <span className="ml-1 font-mono">{order.orderNumber}</span>
                          </div>
                          <div>
                            <span className="font-medium">金额:</span>
                            <span className="ml-1">{order.totalAmount.toFixed(2)} USDT</span>
                          </div>
                          <div>
                            <span className="font-medium">托管费:</span>
                            <span className="ml-1">{(order.escrowFee || 0).toFixed(2)} USDT</span>
                          </div>
                          <div>
                            <span className="font-medium">创建时间:</span>
                            <span className="ml-1">{new Date(order.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-6 text-sm text-gray-600">
                          <div className="flex items-center">
                            <span className="font-medium">买家:</span>
                            <span className="ml-1">{order.buyer.name}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium">卖家:</span>
                            <span className="ml-1">{order.seller.name}</span>
                          </div>
                          {order.mediator && (
                            <div className="flex items-center">
                              <span className="font-medium">中间人:</span>
                              <span className="ml-1">{order.mediator.name}</span>
                              <span className="ml-1 text-xs text-blue-600">
                                (信誉: {order.mediator.mediatorReputation.toFixed(1)})
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 ml-4">
                        <Link
                          href={`/escrow/orders/${order.id}`}
                          className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                        >
                          查看详情
                        </Link>
                        
                        <Link
                          href={`/chat/${order.id}`}
                          className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
                        >
                          聊天
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
