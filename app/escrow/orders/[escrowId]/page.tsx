'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { formatUSDT } from '@/lib/utils'
import { 
  Shield, 
  Clock, 
  CheckCircle, 
  Truck, 
  Package, 
  AlertTriangle,
  MessageCircle,
  User,
  Wallet,
  FileText
} from 'lucide-react'

interface EscrowOrder {
  id: string
  status: string
  amount: number
  mediatorFee: number
  platformFee: number
  bnbTransactionHash?: string
  mediatorWalletAddress: string
  fundedAt?: string
  shippedAt?: string
  deliveredAt?: string
  completedAt?: string
  disputedAt?: string
  createdAt: string
  order: {
    orderNumber: string
    totalAmount: number
    product: {
      title: string
      images: string[]
    }
  }
  buyer: {
    id: string
    name: string
    email: string
  }
  seller: {
    id: string
    name: string
    email: string
  }
  mediator: {
    id: string
    name: string
    email: string
    mediatorReputation?: number
    mediatorSuccessRate?: number
  }
  chatRoom?: {
    roomCode: string
    isActive: boolean
  }
  disputes: any[]
  blockchainTransactions: any[]
}

export default function EscrowOrderPage({ params }: { params: { escrowId: string } }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [escrowOrder, setEscrowOrder] = useState<EscrowOrder | null>(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  // 操作相关状态
  const [txHash, setTxHash] = useState('')
  const [trackingNumber, setTrackingNumber] = useState('')
  const [shippingCompany, setShippingCompany] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user) {
      fetchEscrowOrder()
    }
  }, [session, status, params.escrowId])

  const fetchEscrowOrder = async () => {
    try {
      const response = await fetch(`/api/escrow/${params.escrowId}`)
      const data = await response.json()

      if (data.success) {
        setEscrowOrder(data.data)
      } else {
        setError(data.error)
      }
    } catch (error) {
      console.error('获取托管订单失败:', error)
      setError('获取托管订单失败')
    } finally {
      setLoading(false)
    }
  }

  const handleAction = async (action: string, actionData?: any) => {
    setActionLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`/api/escrow/${params.escrowId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          data: actionData
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess(data.message)
        await fetchEscrowOrder()
        
        // 清空表单
        setTxHash('')
        setTrackingNumber('')
        setShippingCompany('')
      } else {
        setError(data.error)
      }
    } catch (error) {
      console.error('操作失败:', error)
      setError('操作失败，请稍后重试')
    } finally {
      setActionLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="w-3 h-3 mr-1" />等待资金</Badge>
      case 'FUNDED':
        return <Badge variant="outline" className="text-blue-600"><Wallet className="w-3 h-3 mr-1" />已到账</Badge>
      case 'SHIPPED':
        return <Badge variant="outline" className="text-purple-600"><Truck className="w-3 h-3 mr-1" />已发货</Badge>
      case 'DELIVERED':
        return <Badge variant="outline" className="text-green-600"><Package className="w-3 h-3 mr-1" />已收货</Badge>
      case 'COMPLETED':
        return <Badge variant="outline" className="text-green-600"><CheckCircle className="w-3 h-3 mr-1" />已完成</Badge>
      case 'DISPUTED':
        return <Badge variant="outline" className="text-red-600"><AlertTriangle className="w-3 h-3 mr-1" />争议中</Badge>
      case 'CANCELLED':
        return <Badge variant="outline" className="text-gray-600">已取消</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getUserRole = () => {
    if (!session?.user?.id || !escrowOrder) return null
    
    if (session.user.id === escrowOrder.buyer.id) return 'buyer'
    if (session.user.id === escrowOrder.seller.id) return 'seller'
    if (session.user.id === escrowOrder.mediator.id) return 'mediator'
    if (session.user.role === 'ADMIN') return 'admin'
    
    return null
  }

  const canPerformAction = (action: string) => {
    const userRole = getUserRole()
    const status = escrowOrder?.status

    switch (action) {
      case 'fund':
        return userRole === 'mediator' && status === 'PENDING'
      case 'ship':
        return userRole === 'seller' && status === 'FUNDED'
      case 'deliver':
        return userRole === 'buyer' && status === 'SHIPPED'
      case 'complete':
        return userRole === 'mediator' && status === 'DELIVERED'
      default:
        return false
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (error && !escrowOrder) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!escrowOrder) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">托管订单不存在</h1>
          <Button onClick={() => router.back()}>返回</Button>
        </div>
      </div>
    )
  }

  const userRole = getUserRole()

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <Shield className="w-8 h-8 mr-3 text-blue-500" />
              托管订单详情
            </h1>
            <p className="text-gray-600">订单号: {escrowOrder.order.orderNumber}</p>
          </div>
          {getStatusBadge(escrowOrder.status)}
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-6">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 订单信息 */}
          <Card>
            <CardHeader>
              <CardTitle>订单信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start space-x-4">
                {escrowOrder.order.product.images?.[0] && (
                  <img
                    src={escrowOrder.order.product.images[0]}
                    alt={escrowOrder.order.product.title}
                    className="w-20 h-20 object-cover rounded-lg"
                  />
                )}
                <div className="flex-1">
                  <h3 className="font-medium text-lg">{escrowOrder.order.product.title}</h3>
                  <div className="mt-2 space-y-1 text-sm text-gray-600">
                    <div>订单金额: {formatUSDT(escrowOrder.amount)}</div>
                    <div>中间人费用: {formatUSDT(escrowOrder.mediatorFee)}</div>
                    <div>平台费用: {formatUSDT(escrowOrder.platformFee)}</div>
                    <div className="font-medium">总计: {formatUSDT(escrowOrder.amount + escrowOrder.mediatorFee)}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 参与方信息 */}
          <Card>
            <CardHeader>
              <CardTitle>参与方信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <User className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                  <h4 className="font-medium">买家</h4>
                  <p className="text-sm text-gray-600">{escrowOrder.buyer.name}</p>
                  {userRole === 'buyer' && <Badge variant="outline" className="mt-1">您</Badge>}
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <User className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <h4 className="font-medium">卖家</h4>
                  <p className="text-sm text-gray-600">{escrowOrder.seller.name}</p>
                  {userRole === 'seller' && <Badge variant="outline" className="mt-1">您</Badge>}
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Shield className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                  <h4 className="font-medium">中间人</h4>
                  <p className="text-sm text-gray-600">{escrowOrder.mediator.name}</p>
                  <p className="text-xs text-gray-500">
                    信誉: {escrowOrder.mediator.mediatorReputation || 0}分
                  </p>
                  {userRole === 'mediator' && <Badge variant="outline" className="mt-1">您</Badge>}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作区域 */}
          {userRole && (
            <Card>
              <CardHeader>
                <CardTitle>操作</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 中间人确认资金到账 */}
                  {canPerformAction('fund') && (
                    <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
                      <h4 className="font-medium mb-3">确认资金到账</h4>
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="txHash">BNB链交易哈希 (可选)</Label>
                          <Input
                            id="txHash"
                            value={txHash}
                            onChange={(e) => setTxHash(e.target.value)}
                            placeholder="0x..."
                          />
                        </div>
                        <Button
                          onClick={() => handleAction('fund', { txHash })}
                          disabled={actionLoading}
                          className="w-full"
                        >
                          {actionLoading ? '处理中...' : '确认资金到账'}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* 卖家发货 */}
                  {canPerformAction('ship') && (
                    <div className="p-4 border border-green-200 rounded-lg bg-green-50">
                      <h4 className="font-medium mb-3">确认发货</h4>
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="trackingNumber">快递单号</Label>
                          <Input
                            id="trackingNumber"
                            value={trackingNumber}
                            onChange={(e) => setTrackingNumber(e.target.value)}
                            placeholder="请输入快递单号"
                          />
                        </div>
                        <div>
                          <Label htmlFor="shippingCompany">快递公司</Label>
                          <Input
                            id="shippingCompany"
                            value={shippingCompany}
                            onChange={(e) => setShippingCompany(e.target.value)}
                            placeholder="如：顺丰、圆通等"
                          />
                        </div>
                        <Button
                          onClick={() => handleAction('ship', { trackingNumber, shippingCompany })}
                          disabled={actionLoading}
                          className="w-full"
                        >
                          {actionLoading ? '处理中...' : '确认发货'}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* 买家确认收货 */}
                  {canPerformAction('deliver') && (
                    <div className="p-4 border border-purple-200 rounded-lg bg-purple-50">
                      <h4 className="font-medium mb-3">确认收货</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        请确认您已收到商品且商品符合描述
                      </p>
                      <Button
                        onClick={() => handleAction('deliver')}
                        disabled={actionLoading}
                        className="w-full"
                      >
                        {actionLoading ? '处理中...' : '确认收货'}
                      </Button>
                    </div>
                  )}

                  {/* 中间人完成托管 */}
                  {canPerformAction('complete') && (
                    <div className="p-4 border border-green-200 rounded-lg bg-green-50">
                      <h4 className="font-medium mb-3">完成托管</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        买家已确认收货，将向卖家打款并完成托管
                      </p>
                      <Button
                        onClick={() => handleAction('complete')}
                        disabled={actionLoading}
                        className="w-full"
                      >
                        {actionLoading ? '处理中...' : '完成托管'}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧边栏 */}
        <div className="space-y-6">
          {/* 聊天室 */}
          {escrowOrder.chatRoom && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  多方沟通
                </CardTitle>
                <CardDescription>
                  与买家、卖家、中间人和管理员实时沟通
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={() => router.push(`/escrow/chat/${escrowOrder.id}`)}
                  className="w-full"
                  variant="outline"
                >
                  进入聊天室
                </Button>
              </CardContent>
            </Card>
          )}

          {/* 时间线 */}
          <Card>
            <CardHeader>
              <CardTitle>进度时间线</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium">托管订单创建</p>
                    <p className="text-xs text-gray-500">
                      {new Date(escrowOrder.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                
                {escrowOrder.fundedAt && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium">资金到账确认</p>
                      <p className="text-xs text-gray-500">
                        {new Date(escrowOrder.fundedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
                
                {escrowOrder.shippedAt && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium">卖家发货</p>
                      <p className="text-xs text-gray-500">
                        {new Date(escrowOrder.shippedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
                
                {escrowOrder.deliveredAt && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium">买家确认收货</p>
                      <p className="text-xs text-gray-500">
                        {new Date(escrowOrder.deliveredAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
                
                {escrowOrder.completedAt && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium">托管完成</p>
                      <p className="text-xs text-gray-500">
                        {new Date(escrowOrder.completedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 争议举报 */}
          {(userRole === 'buyer' || userRole === 'seller') && escrowOrder.status !== 'COMPLETED' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-red-600">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  争议举报
                </CardTitle>
                <CardDescription>
                  如果遇到问题，可以向管理员举报
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={() => router.push(`/radar?escrowId=${escrowOrder.id}`)}
                  variant="outline"
                  className="w-full border-red-200 text-red-600 hover:bg-red-50"
                >
                  提交争议举报
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
