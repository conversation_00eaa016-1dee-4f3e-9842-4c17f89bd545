'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
  shippingAddress: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
}

interface UserBalance {
  totalBalance: number
  availableBalance: number
  frozenBalance: number
}

export default function BalancePayPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null)
  const [loading, setLoading] = useState(true)
  const [paymentProcessing, setPaymentProcessing] = useState(false)
  const [paymentPin, setPaymentPin] = useState<string>('')
  const [showPinInput, setShowPinInput] = useState(false)

  const orderId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
      loadUserBalance()
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查订单状态
      if (orderData.status !== 'PENDING_PAYMENT') {
        alert('订单状态异常')
        router.push(`/order/${orderId}/info-collect`)
        return
      }
      
      // 检查支付方式
      if (orderData.paymentMethod !== 'balance-pay') {
        alert('支付方式不匹配')
        router.push(`/order/${orderId}/info-collect`)
        return
      }
      
      setOrder(orderData)
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const loadUserBalance = async () => {
    try {
      const response = await fetch('/api/user/balance', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const balanceData = await response.json()
        setUserBalance(balanceData)
      }
    } catch (error) {
      console.error('加载用户余额失败:', error)
    }
  }

  const handlePayment = async () => {
    if (!userBalance || !order) return

    // 检查余额是否足够
    if (userBalance.availableBalance < order.totalAmount) {
      // 余额不足，显示混合支付选项
      const useBalance = userBalance.availableBalance > 0
      const remainingAmount = order.totalAmount - (useBalance ? userBalance.availableBalance : 0)

      const confirmed = confirm(
        `您的余额不足。\n` +
        `可用余额：${formatUSDT(userBalance.availableBalance)}\n` +
        `订单金额：${formatUSDT(order.totalAmount)}\n` +
        `${useBalance ? `将扣除余额：${formatUSDT(userBalance.availableBalance)}\n` : ''}` +
        `还需支付：${formatUSDT(remainingAmount)}\n\n` +
        `是否选择其他支付方式补齐差额？`
      )

      if (confirmed) {
        // 跳转到支付方式选择页面，传递需要补齐的金额
        router.push(`/order/${order.id}/info-collect?supplementAmount=${remainingAmount}&useBalance=${useBalance}`)
      }
      return
    }

    setShowPinInput(true)
  }

  const handlePaymentConfirm = async () => {
    if (!paymentPin.trim()) {
      alert('请输入支付密码')
      return
    }
    
    setPaymentProcessing(true)
    
    try {
      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          paymentMethod: 'balance-pay',
          paymentPin: paymentPin,
          paymentConfirmed: true
        })
      })
      
      if (response.ok) {
        // 支付成功，跳转到成功页面
        router.push(`/order/${orderId}/payment/successful`)
      } else {
        const error = await response.json()
        alert(error.error || '支付失败')
        setPaymentPin('')
      }
    } catch (error) {
      console.error('支付失败:', error)
      alert('网络错误，请稍后重试')
      setPaymentPin('')
    } finally {
      setPaymentProcessing(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  const isBalanceSufficient = userBalance && userBalance.availableBalance >= order.totalAmount
  const shortfall = userBalance ? Math.max(0, order.totalAmount - userBalance.availableBalance) : 0

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold mb-2">余额支付</h1>
            <div className="text-lg text-blue-600 font-semibold">
              支付金额：{formatUSDT(order.totalAmount)}
            </div>
            <div className="text-sm text-gray-600 mt-2">
              订单号：{order.orderNumber}
            </div>
          </div>

          {/* 余额信息 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">账户余额</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {formatUSDT(userBalance?.availableBalance || 0)}
                  </div>
                  <div className="text-sm text-gray-600">可用余额</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">
                    {formatUSDT(userBalance?.frozenBalance || 0)}
                  </div>
                  <div className="text-sm text-gray-600">冻结余额</div>
                </div>
              </div>
              
              {!isBalanceSufficient && (
                <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center text-amber-800">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">余额不足，可选择混合支付</span>
                  </div>
                  <div className="mt-2 text-sm text-amber-700">
                    {userBalance && userBalance.availableBalance > 0 ? (
                      <>
                        <p>将扣除余额：{formatUSDT(userBalance.availableBalance)}</p>
                        <p>还需支付：{formatUSDT(shortfall)}</p>
                        <p className="mt-1 font-medium">可选择BSC支付或币安支付补齐差额</p>
                      </>
                    ) : (
                      <>
                        <p>当前余额为：{formatUSDT(0)}</p>
                        <p>需要支付：{formatUSDT(order.totalAmount)}</p>
                        <p className="mt-1 font-medium">请选择BSC支付或币安支付</p>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 订单信息 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">订单信息</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-4 mb-4">
                <img
                  src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
                  alt={order.product.title}
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1">
                  <h4 className="font-medium">{order.product.title}</h4>
                  <p className="text-gray-600 text-sm">卖家：{order.product.seller.name}</p>
                  <p className="text-gray-600 text-sm">数量：{order.metadata.quantity}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatUSDT(order.productPrice)}</p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>商品金额</span>
                  <span>{formatUSDT(order.productPrice)}</span>
                </div>
                <div className="flex justify-between text-sm mb-1">
                  <span>运费</span>
                  <span>{formatUSDT(order.shippingFee)}</span>
                </div>
                <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                  <span>总计</span>
                  <span className="text-blue-600">{formatUSDT(order.totalAmount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 支付密码输入 */}
          {showPinInput && (
            <div className="mb-8">
              <h3 className="font-medium mb-3">输入支付密码</h3>
              <div className="bg-blue-50 rounded-lg p-4">
                <input
                  type="password"
                  value={paymentPin}
                  onChange={(e) => setPaymentPin(e.target.value)}
                  placeholder="请输入6位支付密码"
                  maxLength={6}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 text-center text-lg tracking-widest"
                />
                <p className="text-xs text-gray-600 mt-2 text-center">
                  请输入您的6位数字支付密码
                </p>
              </div>
            </div>
          )}

          {/* 支付说明 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付说明</h3>
            <div className="bg-blue-50 rounded-lg p-4">
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                <li>仅支持使用自由保证金进行支付</li>
                <li>冻结余额（担保资金）不可用于支付</li>
                <li>支付成功后金额将从可用余额中扣除</li>
                <li>如余额不足，请先充值或选择其他支付方式</li>
              </ul>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.push(`/order/${orderId}/info-collect`)}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回
            </button>
            
            {!showPinInput ? (
              <button
                onClick={handlePayment}
                className={`flex-1 px-6 py-3 rounded-md text-lg font-medium ${
                  isBalanceSufficient
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-amber-600 hover:bg-amber-700 text-white'
                }`}
              >
                {isBalanceSufficient ? '立即支付' : '选择其他支付方式'}
              </button>
            ) : (
              <button
                onClick={handlePaymentConfirm}
                disabled={paymentProcessing || paymentPin.length !== 6}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                {paymentProcessing ? '支付中...' : '确认支付'}
              </button>
            )}
          </div>
          
          {showPinInput && (
            <div className="mt-4">
              <button
                onClick={() => {
                  setShowPinInput(false)
                  setPaymentPin('')
                }}
                className="w-full text-gray-600 hover:text-gray-800 text-sm"
              >
                取消支付
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
