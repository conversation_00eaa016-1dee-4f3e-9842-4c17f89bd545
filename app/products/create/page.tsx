'use client'

import { useSession } from 'next-auth/react'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import ImageUpload from '@/components/ImageUpload'
import VariantManager from '@/components/variants/VariantManager'
import CategorySelector from '@/components/product/CategorySelector'
import ConditionSelector from '@/components/product/ConditionSelector'
import FixedLocationSelector from '@/components/location/FixedLocationSelector'
import { ProductCategory } from '@/lib/product-constants'

export default function CreateProductPage() {
  const { status } = useSession()
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    category: 'GENERAL',
    condition: 'NEW',
    city: '',
    district: '',
    shippingFrom: '',
    stock: '1',
    images: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [hasVariants, setHasVariants] = useState(false)
  const [variants, setVariants] = useState<any[]>([])
  const [showVariantManager, setShowVariantManager] = useState(false)

  // 如果未登录，重定向到登录页
  if (status === 'unauthenticated') {
    router.push('/auth/signin')
    return null
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    // 清除对应字段的错误信息
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // 图片上传改为可选，不再验证
    // if (!formData.images.trim()) {
    //   newErrors.images = '请至少上传一张商品图片'
    // }

    if (!formData.title.trim()) {
      newErrors.title = '商品标题为必填项'
    } else if (formData.title.length < 5 || formData.title.length > 60) {
      newErrors.title = '商品标题应为5-60个字符'
    }

    if (!formData.description.trim()) {
      newErrors.description = '商品描述为必填项'
    } else if (formData.description.length < 10) {
      newErrors.description = '商品描述至少10个字符'
    }

    if (!formData.price) {
      newErrors.price = '商品价格为必填项'
    } else {
      const price = parseFloat(formData.price)
      if (isNaN(price) || price <= 0) {
        newErrors.price = '请输入有效的价格'
      } else if (price > 100000) {
        newErrors.price = '价格不能超过100,000 USDT'
      }
    }

    const stock = parseInt(formData.stock)
    if (isNaN(stock) || stock < 1 || stock > 999) {
      newErrors.stock = '库存数量应为1-999之间的整数'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // 验证变体数据
      if (hasVariants && variants.length === 0) {
        setErrors({ submit: '启用变体时必须至少添加一个变体' })
        setIsLoading(false)
        return
      }

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          stock: parseInt(formData.stock),
          hasVariants,
          variants: hasVariants ? variants : []
        }),
      })

      const data = await response.json()

      if (response.ok) {
        alert('商品发布成功！')
        router.push('/products')
      } else {
        setErrors({ submit: data.error || '发布失败' })
      }
    } catch (error) {
      setErrors({ submit: '网络错误，请稍后重试' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/products" className="text-gray-700 hover:text-gray-900">
                商品列表
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-3xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-6">
                发布商品
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 商品图片 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    商品图片 <span className="text-gray-500 text-xs">(可选)</span>
                  </label>
                  <ImageUpload
                    type="product"
                    multiple={true}
                    maxFiles={5}
                    onUpload={(urls) => setFormData(prev => ({ ...prev, images: urls }))}
                  />
                  {errors.images && <p className="mt-1 text-sm text-red-600">{errors.images}</p>}
                </div>

                {/* 商品标题 */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                    商品标题 *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    required
                    value={formData.title}
                    onChange={handleChange}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                    placeholder="请输入商品标题（5-60个字符）"
                  />
                  {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
                </div>

                {/* 商品描述 */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    商品描述 *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    required
                    rows={4}
                    value={formData.description}
                    onChange={handleChange}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                    placeholder="请详细描述商品信息、规格、成色等"
                  />
                  {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                </div>

                {/* 价格和库存 */}
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                      价格 (USDT) *
                    </label>
                    <input
                      type="number"
                      id="price"
                      name="price"
                      required
                      step="0.01"
                      min="0"
                      value={formData.price}
                      onChange={handleChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                      placeholder="0.00"
                    />
                    {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
                  </div>

                  <div>
                    <label htmlFor="stock" className="block text-sm font-medium text-gray-700">
                      库存数量 *
                    </label>
                    <input
                      type="number"
                      id="stock"
                      name="stock"
                      required
                      min="1"
                      max="999"
                      value={formData.stock}
                      onChange={handleChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                      placeholder="1"
                    />
                    {errors.stock && <p className="mt-1 text-sm text-red-600">{errors.stock}</p>}
                  </div>
                </div>

                {/* 变体管理 */}
                <div className="border-t pt-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">商品规格</h3>
                      <p className="text-sm text-gray-500">
                        如果商品有不同的规格（如颜色、尺寸等），可以启用变体管理
                      </p>
                    </div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={hasVariants}
                        onChange={(e) => {
                          setHasVariants(e.target.checked)
                          if (!e.target.checked) {
                            setVariants([])
                            setShowVariantManager(false)
                          } else {
                            setShowVariantManager(true)
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">启用变体管理</span>
                    </label>
                  </div>

                  {hasVariants && showVariantManager && (
                    <VariantManager
                      onVariantsChange={setVariants}
                      disabled={isLoading}
                    />
                  )}
                </div>

                {/* 商品分类和成色 */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <CategorySelector
                    selectedCategory={formData.category}
                    onCategoryChange={(category) => setFormData(prev => ({ ...prev, category }))}
                    required
                    layout="dropdown"
                  />

                  <ConditionSelector
                    selectedCondition={formData.condition}
                    onConditionChange={(condition) => setFormData(prev => ({ ...prev, condition }))}
                    category={formData.category as ProductCategory}
                    required
                    layout="dropdown"
                  />
                </div>

                {/* 地理位置信息 */}
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FixedLocationSelector
                    selectedCity={formData.city}
                    selectedDistrict={formData.district}
                    onLocationChange={(city, district) => {
                      setFormData(prev => ({
                        ...prev,
                        city,
                        district: district || '',
                        shippingFrom: city // 自动设置发货地址为选择的城市
                      }))
                    }}
                    required
                    showSearch
                  />

                  <div>
                    <label htmlFor="shippingFrom" className="block text-sm font-medium text-gray-700">
                      发货地址
                    </label>
                    <input
                      type="text"
                      id="shippingFrom"
                      name="shippingFrom"
                      value={formData.shippingFrom}
                      onChange={handleChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                      placeholder="请输入发货地址"
                    />
                  </div>
                </div>

                {errors.submit && (
                  <div className="text-red-600 text-sm">{errors.submit}</div>
                )}

                <div className="flex justify-end space-x-3">
                  <Link
                    href="/products"
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
                  >
                    取消
                  </Link>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                  >
                    {isLoading ? '发布中...' : '发布商品'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
