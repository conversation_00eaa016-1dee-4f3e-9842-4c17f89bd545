'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'
import Navbar from '@/components/Navbar'
import StockAlert from '@/components/inventory/StockAlert'

interface Product {
  id: string
  title: string
  description: string
  images: string
  price: number
  category: string
  condition: string
  city: string
  district: string
  status: string
  createdAt: string
  seller: {
    id: string
    name: string
    creditScore: number
  }
}

interface Demand {
  id: string
  title: string
  description: string
  demandType: string
  subcategory: string | null
  budget: number
  deliveryMethod: string
  status: string
  expirationTime: string
  createdAt: string
  user: {
    id: string
    name: string
    creditScore: number
  }
  _count: {
    offers: number
  }
  offers?: Array<{
    id: string
    offerPrice: number
    offerNote: string
    isAccepted: boolean
    createdAt: string
    seller: {
      id: string
      name: string
      creditScore: number
    }
  }>
}

interface User {
  id: string
  userId: string
  name: string
  email: string
  creditScore: number
  city: string
  district: string
  createdAt: string
}

export default function UserProductsPage() {
  const { data: session } = useSession()
  const params = useParams()

  const [products, setProducts] = useState<Product[]>([])
  const [demands, setDemands] = useState<Demand[]>([])
  const [user, setUser] = useState<User | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [demandPagination, setDemandPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isDemandLoading, setIsDemandLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'products' | 'demands'>('products')
  const [productStatus, setProductStatus] = useState<'AVAILABLE' | 'SOLD' | 'INACTIVE' | 'SOLD_OUT'>('AVAILABLE')
  const [demandStatus, setDemandStatus] = useState<'ALL' | 'OPEN' | 'CLOSED' | 'COMPLETED'>('ALL')
  const [deletingProductId, setDeletingProductId] = useState<string | null>(null)
  const [deletingDemandId, setDeletingDemandId] = useState<string | null>(null)

  const userId = params?.userId as string
  const isOwnProfile = session?.user?.id === userId

  useEffect(() => {
    if (userId) {
      fetchUserInfo()
      if (activeTab === 'products') {
        fetchProducts()
      } else {
        fetchDemands()
      }
    }
  }, [userId, activeTab, productStatus, demandStatus, pagination.page, demandPagination.page])

  const fetchUserInfo = async () => {
    try {
      const response = await fetch(`/api/users/${userId}`)
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  const fetchProducts = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sellerId: userId,
        status: productStatus,
        sortBy: 'newest'
      })

      const response = await fetch(`/api/products?${params}`)
      if (response.ok) {
        const data = await response.json()
        setProducts(data.products)
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total,
          pages: data.pagination.pages
        }))
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchDemands = async () => {
    setIsDemandLoading(true)
    try {
      const params = new URLSearchParams({
        page: demandPagination.page.toString(),
        limit: demandPagination.limit.toString(),
        status: demandStatus
      })

      const response = await fetch(`/api/users/${userId}/demands?${params}`)
      if (response.ok) {
        const data = await response.json()
        setDemands(data.demands)
        setDemandPagination(prev => ({
          ...prev,
          total: data.pagination.total,
          pages: data.pagination.pages
        }))
      }
    } catch (error) {
      console.error('获取需求列表失败:', error)
    } finally {
      setIsDemandLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return '在售'
      case 'SOLD': return '已售出'
      case 'INACTIVE': return '已下架'
      case 'SOLD_OUT': return '已下架'
      case 'DRAFT': return '草稿'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'text-green-600 bg-green-100'
      case 'SOLD': return 'text-gray-600 bg-gray-100'
      case 'INACTIVE': return 'text-red-600 bg-red-100'
      case 'SOLD_OUT': return 'text-orange-600 bg-orange-100'
      case 'DRAFT': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getProductImage = (images: string) => {
    if (!images || images.trim() === '') return null
    const imageList = images.split(',')
    return imageList[0] || null
  }

  const deleteProduct = async (productId: string) => {
    setDeletingProductId(productId)
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        alert('商品删除成功')
        // 重新获取商品列表
        fetchProducts()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '删除失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请重试')
    } finally {
      setDeletingProductId(null)
    }
  }

  const handleDeleteProduct = (productId: string, productTitle: string) => {
    if (confirm(`确定要删除商品"${productTitle}"吗？删除后无法恢复！`)) {
      deleteProduct(productId)
    }
  }

  // 需求相关工具函数
  const getDemandTypeText = (type: string) => {
    switch (type) {
      case 'buy_goods': return '求购商品'
      case 'hire_service': return '雇佣服务'
      case 'digital_task': return '数字任务'
      case 'request_info': return '求助信息'
      default: return type
    }
  }

  const getDemandStatusText = (status: string) => {
    switch (status) {
      case 'OPEN': return '开放中'
      case 'CLOSED': return '已关闭'
      case 'COMPLETED': return '已完成'
      default: return status
    }
  }

  const getDemandStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'text-green-600 bg-green-100'
      case 'CLOSED': return 'text-gray-600 bg-gray-100'
      case 'COMPLETED': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const deleteDemand = async (demandId: string) => {
    setDeletingDemandId(demandId)
    try {
      const response = await fetch(`/api/demands/${demandId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        alert('需求删除成功')
        // 重新获取需求列表
        fetchDemands()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '删除失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请重试')
    } finally {
      setDeletingDemandId(null)
    }
  }

  const handleDeleteDemand = (demandId: string, demandTitle: string) => {
    if (confirm(`确定要删除需求"${demandTitle}"吗？删除后无法恢复！`)) {
      deleteDemand(demandId)
    }
  }

  if (isLoading && !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">用户不存在</p>
          <Link href="/products" className="text-blue-600 hover:text-blue-500 mt-2 inline-block">
            返回商品列表
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 统一导航栏 */}
      <Navbar />

      {/* 用户信息头部 */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {isOwnProfile ? '我的商品与需求' : `${user.name}的商品与需求`}
              </h1>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                <span>用户ID: {user.userId}</span>
                <span>信用积分: {user.creditScore}</span>
                <span>所在地: {user.city} {user.district}</span>
                <span>加入时间: {new Date(user.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
            {isOwnProfile && (
              <div className="flex space-x-3">
                <Link
                  href="/products/create"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  发布商品
                </Link>
                <Link
                  href="/demands/create"
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  发布需求
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 库存提醒 - 仅对自己的商品显示 */}
        {isOwnProfile && activeTab === 'products' && (
          <StockAlert showGlobalAlerts={true} />
        )}

        {/* 主标签页导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('products')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'products'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                商品管理
              </button>
              <button
                onClick={() => setActiveTab('demands')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'demands'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                需求管理
              </button>
            </nav>
          </div>
        </div>

        {/* 商品管理标签页 */}
        {activeTab === 'products' && (
          <>
            {/* 商品状态筛选标签 */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {(['AVAILABLE', 'SOLD', 'INACTIVE', 'SOLD_OUT'] as const).map((status) => (
                    <button
                      key={status}
                      onClick={() => setProductStatus(status)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        productStatus === status
                          ? 'border-green-500 text-green-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {getStatusText(status)}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </>
        )}

        {/* 需求管理标签页 */}
        {activeTab === 'demands' && (
          <>
            {/* 需求状态筛选标签 */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {(['ALL', 'OPEN', 'CLOSED', 'COMPLETED'] as const).map((status) => (
                    <button
                      key={status}
                      onClick={() => setDemandStatus(status)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        demandStatus === status
                          ? 'border-green-500 text-green-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {status === 'ALL' ? '全部' : getDemandStatusText(status)}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </>
        )}

        {/* 商品列表 */}
        {activeTab === 'products' && (
          <>
            {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600">
              {isOwnProfile ? '您还没有发布任何商品' : '该用户还没有发布任何商品'}
            </p>
            {isOwnProfile && (
              <Link
                href="/products/create"
                className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                发布第一个商品
              </Link>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <div key={product.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <Link href={`/products/${product.id}`}>
                  <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
                    {getProductImage(product.images) ? (
                      <img
                        src={getProductImage(product.images) || ''}
                        alt={product.title}
                        className="h-48 w-full object-cover object-center"
                      />
                    ) : (
                      <div className="h-48 w-full flex items-center justify-center bg-gray-100">
                        <div className="text-center text-gray-500">
                          <div className="text-4xl mb-2">📦</div>
                          <div className="text-sm">暂无图片</div>
                        </div>
                      </div>
                    )}
                  </div>
                </Link>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                      {getStatusText(product.status)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(product.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <Link href={`/products/${product.id}`}>
                    <h3 className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2">
                      {product.title}
                    </h3>
                  </Link>
                  <p className="mt-1 text-lg font-semibold text-blue-600">
                    {formatUSDT(product.price)}
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    {product.city} {product.district}
                  </p>
                  {isOwnProfile && (
                    <div className="mt-3 space-y-2">
                      <div className="flex space-x-2">
                        <Link
                          href={`/products/${product.id}/edit`}
                          className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs text-center"
                        >
                          编辑
                        </Link>
                        <Link
                          href={`/products/${product.id}`}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs text-center"
                        >
                          查看
                        </Link>
                      </div>
                      <button
                        onClick={() => handleDeleteProduct(product.id, product.title)}
                        disabled={deletingProductId === product.id}
                        className="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {deletingProductId === product.id ? '删除中...' : '删除商品'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
            </>
          )}

        {/* 需求列表 */}
        {activeTab === 'demands' && (
          <>
            {isDemandLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">加载中...</p>
              </div>
            ) : demands.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-600">
                  {isOwnProfile ? '您还没有发布任何需求' : '该用户还没有发布任何需求'}
                </p>
                {isOwnProfile && (
                  <Link
                    href="/demands/create"
                    className="mt-4 inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    发布第一个需求
                  </Link>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {demands.map((demand) => (
                  <div key={demand.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            <Link href={`/demands/${demand.id}`} className="hover:text-blue-600">
                              {demand.title}
                            </Link>
                          </h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDemandStatusColor(demand.status)}`}>
                            {getDemandStatusText(demand.status)}
                          </span>
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            {getDemandTypeText(demand.demandType)}
                          </span>
                        </div>
                        <p className="text-gray-600 mb-3 line-clamp-2">{demand.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>预算: {formatUSDT(demand.budget)}</span>
                          <span>报价: {demand._count.offers}个</span>
                          <span>发布时间: {new Date(demand.createdAt).toLocaleDateString()}</span>
                          <span>截止时间: {new Date(demand.expirationTime).toLocaleDateString()}</span>
                        </div>
                      </div>
                      {isOwnProfile && (
                        <div className="ml-4 flex flex-col space-y-2">
                          <Link
                            href={`/demands/${demand.id}`}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs text-center"
                          >
                            查看详情
                          </Link>
                          <button
                            onClick={() => handleDeleteDemand(demand.id, demand.title)}
                            disabled={deletingDemandId === demand.id}
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {deletingDemandId === demand.id ? '删除中...' : '删除'}
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* 分页 */}
        {((activeTab === 'products' && pagination.pages > 1) || (activeTab === 'demands' && demandPagination.pages > 1)) && (
          <div className="mt-8 flex justify-center">
            <nav className="flex items-center space-x-2">
              {activeTab === 'products' ? (
                <>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={pagination.page === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    上一页
                  </button>
                  <span className="px-3 py-2 text-sm text-gray-700">
                    第 {pagination.page} 页，共 {pagination.pages} 页
                  </span>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                    disabled={pagination.page === pagination.pages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    下一页
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setDemandPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={demandPagination.page === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    上一页
                  </button>
                  <span className="px-3 py-2 text-sm text-gray-700">
                    第 {demandPagination.page} 页，共 {demandPagination.pages} 页
                  </span>
                  <button
                    onClick={() => setDemandPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                    disabled={demandPagination.page === demandPagination.pages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    下一页
                  </button>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </div>
  )
}
