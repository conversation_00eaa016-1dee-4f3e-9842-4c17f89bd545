'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'
import ImageUpload from '@/components/ImageUpload'
import VariantManager from '@/components/variants/VariantManager'

interface VariantAttribute {
  id: string
  name: string
  value: string
}

interface ProductVariant {
  id: string
  sku?: string
  price: number
  stock: number
  status: string
  isDefault: boolean
  attributes: VariantAttribute[]
}

interface Product {
  id: string
  title: string
  description: string
  images: string
  price: number
  category: string
  condition: string
  city: string
  district: string
  shippingFrom: string
  stock: number
  status: string
  hasVariants: boolean
  variants: ProductVariant[]
  sellerId: string
}

export default function EditProductPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [product, setProduct] = useState<Product | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    images: '',
    price: '',
    category: 'ELECTRONICS',
    condition: 'NEW',
    city: '',
    district: '',
    shippingFrom: '',
    stock: '1',
    status: 'AVAILABLE'
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingProduct, setIsLoadingProduct] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [hasVariants, setHasVariants] = useState(false)
  const [variants, setVariants] = useState<any[]>([])
  const [showVariantManager, setShowVariantManager] = useState(false)

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // 获取商品信息
  useEffect(() => {
    if (params?.id && session?.user?.id) {
      fetchProduct()
    }
  }, [params?.id, session?.user?.id])

  const fetchProduct = async () => {
    if (!params?.id) {
      router.push('/products')
      return
    }

    try {
      const response = await fetch(`/api/products/${params.id}`)
      if (response.ok) {
        const data = await response.json()

        // 检查是否是商品的卖家
        if (data.sellerId !== session?.user?.id) {
          router.push('/products')
          return
        }

        setProduct(data)
        setFormData({
          title: data.title,
          description: data.description || '',
          images: data.images || '',
          price: data.price.toString(),
          category: data.category,
          condition: data.condition,
          city: data.city,
          district: data.district,
          shippingFrom: data.shippingFrom,
          stock: data.stock.toString(),
          status: data.status
        })
        setHasVariants(data.hasVariants || false)
        setVariants(data.variants || [])
        setShowVariantManager(data.hasVariants || false)
      } else {
        router.push('/products')
      }
    } catch (error) {
      console.error('获取商品信息失败:', error)
      router.push('/products')
    } finally {
      setIsLoadingProduct(false)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = '商品标题为必填项'
    } else if (formData.title.length < 5 || formData.title.length > 60) {
      newErrors.title = '商品标题应为5-60个字符'
    }

    if (!formData.description.trim()) {
      newErrors.description = '商品描述为必填项'
    } else if (formData.description.length < 10) {
      newErrors.description = '商品描述至少10个字符'
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = '请输入有效的商品价格'
    }

    if (!formData.city.trim()) {
      newErrors.city = '所在城市为必填项'
    }

    if (!formData.district.trim()) {
      newErrors.district = '所在区域为必填项'
    }

    if (!formData.shippingFrom.trim()) {
      newErrors.shippingFrom = '发货地址为必填项'
    }

    if (!hasVariants && (!formData.stock || parseInt(formData.stock) <= 0)) {
      newErrors.stock = '请输入有效的库存数量'
    }

    // 验证变体数据
    if (hasVariants && variants.length === 0) {
      newErrors.variants = '启用变体时必须至少添加一个变体'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    if (!params?.id) {
      setErrors({ submit: '商品ID无效' })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/products/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          stock: parseInt(formData.stock),
          hasVariants,
          variants: hasVariants ? variants : []
        }),
      })

      if (response.ok) {
        router.push(`/products/${params.id}`)
      } else {
        const errorData = await response.json()
        setErrors({ submit: errorData.error || '更新失败，请重试' })
      }
    } catch (error) {
      setErrors({ submit: '网络错误，请重试' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const deleteProduct = async () => {
    if (!product) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/products/${product.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        alert('商品删除成功')
        router.push('/products')
      } else {
        const errorData = await response.json()
        alert(errorData.error || '删除失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteProduct = () => {
    if (!product) return

    if (confirm(`确定要删除商品"${product.title}"吗？删除后无法恢复！`)) {
      deleteProduct()
    }
  }

  if (status === 'loading' || isLoadingProduct) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">商品不存在或您没有权限编辑</p>
          <Link href="/products" className="text-blue-600 hover:text-blue-500 mt-2 inline-block">
            返回商品列表
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">编辑商品</h1>
            <p className="mt-1 text-sm text-gray-600">
              修改您的商品信息
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 商品图片 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                商品图片
              </label>
              <ImageUpload
                type="product"
                multiple={true}
                maxFiles={5}
                currentImage={formData.images}
                onUpload={(urls) => setFormData(prev => ({ ...prev, images: urls }))}
              />
              {errors.images && <p className="mt-1 text-sm text-red-600">{errors.images}</p>}
            </div>

            {/* 商品标题 */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                商品标题 *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="请输入商品标题"
              />
              {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
            </div>

            {/* 商品描述 */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                商品描述 *
              </label>
              <textarea
                id="description"
                name="description"
                rows={4}
                value={formData.description}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="请详细描述您的商品"
              />
              {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
            </div>

            {/* 价格和库存 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                  价格 (USDT) *
                </label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="0.00"
                />
                {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
              </div>

              <div>
                <label htmlFor="stock" className="block text-sm font-medium text-gray-700">
                  库存数量 *
                </label>
                <input
                  type="number"
                  id="stock"
                  name="stock"
                  min="0"
                  value={formData.stock}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="1"
                />
                {errors.stock && <p className="mt-1 text-sm text-red-600">{errors.stock}</p>}
              </div>
            </div>

            {/* 变体管理 */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">商品规格</h3>
                  <p className="text-sm text-gray-500">
                    如果商品有不同的规格（如颜色、尺寸等），可以启用变体管理
                  </p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={hasVariants}
                    onChange={(e) => {
                      setHasVariants(e.target.checked)
                      setShowVariantManager(e.target.checked)
                      if (!e.target.checked) {
                        setVariants([])
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">启用变体管理</span>
                </label>
              </div>

              {hasVariants && showVariantManager && params?.id && (
                <VariantManager
                  productId={params.id as string}
                  initialVariants={variants}
                  onVariantsChange={setVariants}
                  disabled={isLoading}
                />
              )}
              {errors.variants && <p className="mt-1 text-sm text-red-600">{errors.variants}</p>}
            </div>

            {/* 商品状态 */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                商品状态
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="AVAILABLE">在售</option>
                <option value="SOLD">已售出</option>
                <option value="INACTIVE">下架</option>
              </select>
            </div>

            {errors.submit && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{errors.submit}</div>
              </div>
            )}

            <div className="flex justify-between">
              <button
                type="button"
                onClick={handleDeleteProduct}
                disabled={isDeleting}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              >
                {isDeleting ? '删除中...' : '删除商品'}
              </button>

              <div className="flex space-x-4">
                <Link
                  href={params?.id ? `/products/${params.id}` : '/products'}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  取消
                </Link>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isLoading ? '保存中...' : '保存修改'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
