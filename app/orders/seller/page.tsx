'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'
import { Navbar } from '@/components/Navbar'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  createdAt: string
  shippedAt?: string
  receivedAt?: string
  product: {
    id: string
    title: string
    price: number
    images: any
  }
  buyer: {
    id: string
    name: string | null
    email: string | null
  }
  shippingAddress?: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata?: {
    quantity: number
  }
}

interface OrdersResponse {
  orders: Order[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
  statistics: {
    totalOrders: number
    pendingPayment: number
    paid: number
    shipped: number
    completed: number
    cancelled: number
    refundRequested: number
  }
}

export default function SellerOrdersPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [statistics, setStatistics] = useState<OrdersResponse['statistics'] | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    dateFrom: '',
    dateTo: ''
  })
  const [isLoading, setIsLoading] = useState(true)
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showBatchShippingModal, setShowBatchShippingModal] = useState(false)
  const [batchShippingData, setBatchShippingData] = useState<{
    shippingCompany: string
    trackingNumbers: { [orderId: string]: string }
    estimatedDelivery: string
    shippingNotes: string
  }>({
    shippingCompany: '',
    trackingNumbers: {},
    estimatedDelivery: '',
    shippingNotes: ''
  })
  const [shippingCompanies, setShippingCompanies] = useState<any[]>([])

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    fetchOrders()
    loadShippingCompanies()
  }, [session, pagination.page, filters])

  const fetchOrders = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        type: 'seller',
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.status && { status: filters.status }),
        ...(filters.search && { search: filters.search }),
        ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
        ...(filters.dateTo && { dateTo: filters.dateTo })
      })

      const response = await fetch(`/api/orders?${params}`)
      if (response.ok) {
        const data: OrdersResponse = await response.json()
        setOrders(data.orders)
        setPagination(data.pagination)
        if (data.statistics) {
          setStatistics(data.statistics)
        }
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadShippingCompanies = async () => {
    try {
      const response = await fetch('/api/shipping-companies', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setShippingCompanies(data.companies)
      }
    } catch (error) {
      console.error('加载快递公司列表失败:', error)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'DRAFT': '草稿',
      'PENDING_PAYMENT': '待付款',
      'PAID': '已付款',
      'SHIPPED': '已发货',
      'DELIVERED': '已送达',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消',
      'REFUND_REQUESTED': '申请退款中',
      'REFUND_APPROVED': '退款已批准',
      'REFUNDED': '已退款'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'DRAFT': 'bg-gray-100 text-gray-800',
      'PENDING_PAYMENT': 'bg-yellow-100 text-yellow-800',
      'PAID': 'bg-blue-100 text-blue-800',
      'SHIPPED': 'bg-purple-100 text-purple-800',
      'DELIVERED': 'bg-indigo-100 text-indigo-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800',
      'REFUND_REQUESTED': 'bg-orange-100 text-orange-800',
      'REFUND_APPROVED': 'bg-orange-100 text-orange-800',
      'REFUNDED': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId) 
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    )
  }

  const handleSelectAll = () => {
    if (selectedOrders.length === orders.length) {
      setSelectedOrders([])
    } else {
      setSelectedOrders(orders.map(order => order.id))
    }
  }

  const handleBatchShip = () => {
    if (selectedOrders.length === 0) {
      alert('请选择要发货的订单')
      return
    }

    // 初始化批量发货数据
    const initialTrackingNumbers: { [orderId: string]: string } = {}
    selectedOrders.forEach(orderId => {
      initialTrackingNumbers[orderId] = ''
    })

    setBatchShippingData({
      shippingCompany: '',
      trackingNumbers: initialTrackingNumbers,
      estimatedDelivery: '',
      shippingNotes: ''
    })

    setShowBatchShippingModal(true)
  }

  const handleBatchShippingSubmit = async () => {
    // 验证数据
    if (!batchShippingData.shippingCompany) {
      alert('请选择快递公司')
      return
    }

    const emptyTrackingNumbers = Object.entries(batchShippingData.trackingNumbers)
      .filter(([_, trackingNumber]) => !trackingNumber.trim())

    if (emptyTrackingNumbers.length > 0) {
      alert('请填写所有订单的运单号')
      return
    }

    // 检查运单号重复
    const trackingNumbers = Object.values(batchShippingData.trackingNumbers)
    const uniqueNumbers = new Set(trackingNumbers)
    if (uniqueNumbers.size !== trackingNumbers.length) {
      alert('运单号不能重复，请检查后重新输入')
      return
    }

    // 详细确认信息
    const orderDetails = selectedOrders.map(orderId => {
      const order = orders.find(o => o.id === orderId)
      const trackingNumber = batchShippingData.trackingNumbers[orderId]
      return `${order?.orderNumber}: ${trackingNumber}`
    }).join('\n')

    const confirmMessage = `请确认批量发货信息：

快递公司：${getCompanyName(batchShippingData.shippingCompany)}
订单数量：${selectedOrders.length} 个

订单详情：
${orderDetails}

确定要批量发货吗？发货后将无法撤销。`

    if (!confirm(confirmMessage)) return

    // 防误操作：要求输入"批量发货"确认
    const userInput = prompt('为防止误操作，请输入"批量发货"确认：')

    if (userInput !== '批量发货') {
      alert('确认文字不正确，批量发货已取消')
      return
    }

    try {
      const response = await fetch('/api/orders/batch-ship-with-tracking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          orderIds: selectedOrders,
          shippingCompany: getCompanyName(batchShippingData.shippingCompany),
          trackingNumbers: batchShippingData.trackingNumbers,
          estimatedDelivery: batchShippingData.estimatedDelivery || null,
          shippingNotes: batchShippingData.shippingNotes.trim() || null
        })
      })

      if (response.ok) {
        alert('批量发货成功')
        setSelectedOrders([])
        setShowBatchShippingModal(false)
        fetchOrders()
      } else {
        const error = await response.json()
        alert(error.error || '批量发货失败')
      }
    } catch (error) {
      console.error('批量发货失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const getCompanyName = (code: string): string => {
    const company = shippingCompanies.find(c => c.code === code)
    return company ? company.name : code
  }

  const handleContactBuyer = (buyerId: string) => {
    router.push(`/chat?userId=${buyerId}`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">卖家订单管理</h1>
          </div>

          {/* 统计信息 */}
          {statistics && (
            <div className="grid grid-cols-2 gap-4 sm:grid-cols-4 lg:grid-cols-7 mb-6">
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-blue-600">{statistics.totalOrders}</div>
                <div className="text-sm text-gray-600">总订单</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-yellow-600">{statistics.pendingPayment}</div>
                <div className="text-sm text-gray-600">待付款</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-blue-600">{statistics.paid}</div>
                <div className="text-sm text-gray-600">已付款</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-purple-600">{statistics.shipped}</div>
                <div className="text-sm text-gray-600">已发货</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-green-600">{statistics.completed}</div>
                <div className="text-sm text-gray-600">已完成</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-red-600">{statistics.cancelled}</div>
                <div className="text-sm text-gray-600">已取消</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow text-center">
                <div className="text-2xl font-bold text-orange-600">{statistics.refundRequested}</div>
                <div className="text-sm text-gray-600">退款中</div>
              </div>
            </div>
          )}

          {/* 筛选器 */}
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  订单状态
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">全部状态</option>
                  <option value="PENDING_PAYMENT">待付款</option>
                  <option value="PAID">已付款</option>
                  <option value="SHIPPED">已发货</option>
                  <option value="DELIVERED">已送达</option>
                  <option value="COMPLETED">已完成</option>
                  <option value="CANCELLED">已取消</option>
                  <option value="REFUND_REQUESTED">申请退款中</option>
                  <option value="REFUNDED">已退款</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  搜索订单
                </label>
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  placeholder="订单号、买家名称或商品名称"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  开始日期
                </label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  结束日期
                </label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => setFilters({ status: '', search: '', dateFrom: '', dateTo: '' })}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  清空筛选
                </button>
              </div>
            </div>
          </div>

          {/* 批量操作 */}
          {selectedOrders.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-800">
                  已选择 {selectedOrders.length} 个订单
                </span>
                <div className="space-x-2">
                  <button
                    onClick={handleBatchShip}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    批量发货
                  </button>
                  <button
                    onClick={() => setSelectedOrders([])}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    取消选择
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 订单列表 */}
          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">暂无订单</div>
            </div>
          ) : (
            <>
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedOrders.length === orders.length && orders.length > 0}
                      onChange={handleSelectAll}
                      className="mr-3 text-blue-600"
                    />
                    <span className="text-sm font-medium text-gray-700">全选</span>
                  </div>
                </div>

                <div className="divide-y divide-gray-200">
                  {orders.map((order) => (
                    <div key={order.id} className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <input
                            type="checkbox"
                            checked={selectedOrders.includes(order.id)}
                            onChange={() => handleSelectOrder(order.id)}
                            className="text-blue-600"
                          />
                          <div>
                            <div className="flex items-center space-x-4 mb-2">
                              <span className="text-sm font-medium text-gray-900">
                                订单号: {order.orderNumber}
                              </span>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                                {getStatusText(order.status)}
                              </span>
                            </div>
                            <div className="text-sm text-gray-600">
                              下单时间: {new Date(order.createdAt).toLocaleString()}
                            </div>
                            {order.shippedAt && (
                              <div className="text-sm text-gray-600">
                                发货时间: {new Date(order.shippedAt).toLocaleString()}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">
                            {formatUSDT(order.totalAmount)}
                          </div>
                          <div className="text-sm text-gray-600">
                            商品: {formatUSDT(order.productPrice)}
                            {order.shippingFee > 0 && ` + 运费: ${formatUSDT(order.shippingFee)}`}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-6 mb-4">
                        <div className="flex items-center space-x-4">
                          <img
                            src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
                            alt={order.product.title}
                            className="w-16 h-16 object-cover rounded"
                          />
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 mb-1">
                              {order.product.title}
                            </h4>
                            <div className="text-sm text-gray-600">
                              数量: {order.metadata?.quantity || 1}
                            </div>
                          </div>
                        </div>

                        <div className="flex-1">
                          <div className="text-sm text-gray-600 mb-1">
                            <strong>买家:</strong> {order.buyer.name || '匿名用户'}
                          </div>
                          {order.shippingAddress && (
                            <div className="text-sm text-gray-600">
                              <strong>收货地址:</strong> {order.shippingAddress.name} {order.shippingAddress.phone}
                              <br />
                              {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district} {order.shippingAddress.detail}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex justify-end space-x-2">
                        <Link
                          href={`/orders/${order.id}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                        >
                          查看详情
                        </Link>

                        {order.status === 'PAID' && (
                          <Link
                            href={`/orders/${order.id}/shipping`}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                          >
                            发货
                          </Link>
                        )}

                        {order.status === 'REFUND_REQUESTED' && (
                          <Link
                            href={`/orders/${order.id}/refund`}
                            className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                          >
                            处理退款
                          </Link>
                        )}

                        <button
                          onClick={() => handleContactBuyer(order.buyer.id)}
                          className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                        >
                          联系买家
                        </button>

                        {['SHIPPED', 'DELIVERED', 'COMPLETED'].includes(order.status) && (
                          <Link
                            href={`/orders/print/${order.id}`}
                            target="_blank"
                            className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                          >
                            打印
                          </Link>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="mt-8 flex justify-center">
                  <nav className="flex space-x-2">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      上一页
                    </button>

                    <span className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md">
                      {pagination.page} / {pagination.pages}
                    </span>

                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 批量发货模态框 */}
      {showBatchShippingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  批量发货 ({selectedOrders.length} 个订单)
                </h3>
                <button
                  onClick={() => setShowBatchShippingModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                {/* 快递公司选择 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    快递公司 *
                  </label>
                  <select
                    value={batchShippingData.shippingCompany}
                    onChange={(e) => setBatchShippingData({
                      ...batchShippingData,
                      shippingCompany: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">请选择快递公司</option>
                    {shippingCompanies.map((company) => (
                      <option key={company.code} value={company.code}>
                        {company.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 运单号录入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    运单号录入 *
                  </label>
                  <div className="space-y-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4">
                    {selectedOrders.map((orderId) => {
                      const order = orders.find(o => o.id === orderId)
                      return (
                        <div key={orderId} className="flex items-center space-x-4">
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">
                              {order?.orderNumber}
                            </div>
                            <div className="text-xs text-gray-500">
                              {order?.product.title}
                            </div>
                          </div>
                          <div className="flex-1">
                            <input
                              type="text"
                              value={batchShippingData.trackingNumbers[orderId] || ''}
                              onChange={(e) => setBatchShippingData({
                                ...batchShippingData,
                                trackingNumbers: {
                                  ...batchShippingData.trackingNumbers,
                                  [orderId]: e.target.value
                                }
                              })}
                              placeholder="请输入运单号"
                              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                              required
                            />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* 预计送达时间 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    预计送达时间
                  </label>
                  <input
                    type="date"
                    value={batchShippingData.estimatedDelivery}
                    onChange={(e) => setBatchShippingData({
                      ...batchShippingData,
                      estimatedDelivery: e.target.value
                    })}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* 发货备注 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    发货备注
                  </label>
                  <textarea
                    value={batchShippingData.shippingNotes}
                    onChange={(e) => setBatchShippingData({
                      ...batchShippingData,
                      shippingNotes: e.target.value
                    })}
                    placeholder="可填写特殊说明或注意事项"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-4 mt-6">
                <button
                  onClick={() => setShowBatchShippingModal(false)}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
                >
                  取消
                </button>
                <button
                  onClick={handleBatchShippingSubmit}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium"
                >
                  确认批量发货
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
