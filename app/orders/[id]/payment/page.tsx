'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import PaymentMethodSelector, { type PaymentMethod } from '@/components/payment/PaymentMethodSelector'
import BinancePayment from '@/components/payment/BinancePayment'
import BNBChainPayment from '@/components/payment/BNBChainPayment'
import DepositPayment from '@/components/payment/DepositPayment'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  paymentMethod: string | null
  escrowStatus: string
  product: {
    id: string
    title: string
    price: number
    images: string[]
  }
  seller: {
    id: string
    name: string | null
    email: string | null
  }
}

export default function OrderPaymentPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const orderId = params?.id as string

  const [order, setOrder] = useState<Order | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null)
  const [currentStep, setCurrentStep] = useState<'select' | 'payment' | 'processing' | 'success'>('select')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    fetchOrder()
  }, [session, status, orderId])

  const fetchOrder = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/orders/${orderId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('订单不存在')
        } else if (response.status === 403) {
          setError('无权限访问此订单')
        } else {
          setError('获取订单信息失败')
        }
        return
      }
      
      const orderData = await response.json()
      setOrder(orderData)
      
      // 检查订单状态
      if (orderData.status !== 'PENDING_PAYMENT') {
        let errorMessage = '此订单不需要支付'

        switch (orderData.status) {
          case 'PAID':
            errorMessage = '此订单已支付，正在等待发货'
            break
          case 'SHIPPED':
            errorMessage = '此订单已发货，正在配送中'
            break
          case 'COMPLETED':
            errorMessage = '此订单已完成，无需再次支付'
            break
          case 'CANCELLED':
            errorMessage = '此订单已取消，无法支付'
            break
          case 'REFUND_REQUESTED':
            errorMessage = '此订单正在申请退款，无法支付'
            break
          default:
            errorMessage = `此订单状态为 ${orderData.status}，无法支付`
        }

        setError(errorMessage)
        return
      }
      
      // 检查是否为买家
      if (orderData.buyer.id !== session?.user?.id) {
        setError('只有买家可以支付订单')
        return
      }
      
    } catch (error) {
      console.error('Failed to fetch order:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedPaymentMethod(method)
    setCurrentStep('payment')
  }

  const handlePaymentSuccess = async (paymentData: any) => {
    setCurrentStep('processing')
    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: selectedPaymentMethod?.id,
          ...paymentData
        }),
      })

      if (response.ok) {
        // 支付成功，显示成功提示并延迟跳转
        setCurrentStep('success')

        // 2秒后跳转到订单详情页
        setTimeout(() => {
          router.push(`/orders/${orderId}`)
        }, 2000)
      } else {
        const errorData = await response.json()
        setError(errorData.error || '支付处理失败')
        setCurrentStep('payment')
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setError('网络错误，请稍后重试')
      setCurrentStep('payment')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePaymentError = (error: string) => {
    setError(error)
    setCurrentStep('payment')
  }

  const formatUSDT = (amount: number) => {
    return `${amount.toFixed(2)} USDT`
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md w-full bg-white p-8 rounded-lg shadow-md">
          <div className="mb-4 text-red-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">无法支付订单</h2>
          <p className="text-gray-600 mb-6 text-lg">{error}</p>
          <div className="space-y-3">
            <Link
              href={`/orders/${orderId}`}
              className="block w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-sm font-medium"
            >
              返回订单详情
            </Link>
            <Link
              href="/orders"
              className="block w-full bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md text-sm font-medium"
            >
              返回订单列表
            </Link>
            <Link
              href="/"
              className="block text-blue-600 hover:text-blue-800 text-sm mt-4"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">订单不存在</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900 hover:text-blue-600">
                比特市场
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/orders" className="text-gray-700 hover:text-gray-900">
                我的订单
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题 */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">订单支付</h1>
            <p className="text-gray-600 mt-2">订单号：{order.orderNumber}</p>
          </div>

          {/* 进度指示器 */}
          <div className="mb-8">
            <div className="flex items-center">
              <div className={`flex items-center ${currentStep === 'select' ? 'text-blue-600' : 'text-green-600'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === 'select' ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'
                }`}>
                  {currentStep === 'select' ? '1' : '✓'}
                </div>
                <span className="ml-2 text-sm font-medium">选择支付方式</span>
              </div>
              <div className={`flex-1 h-0.5 mx-4 ${currentStep !== 'select' ? 'bg-green-600' : 'bg-gray-300'}`}></div>
              <div className={`flex items-center ${
                currentStep === 'payment' ? 'text-blue-600' :
                (currentStep === 'processing' || currentStep === 'success') ? 'text-green-600' : 'text-gray-400'
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === 'payment' ? 'bg-blue-600 text-white' :
                  (currentStep === 'processing' || currentStep === 'success') ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>
                  {(currentStep === 'processing' || currentStep === 'success') ? '✓' : '2'}
                </div>
                <span className="ml-2 text-sm font-medium">完成支付</span>
              </div>
            </div>
          </div>

          {/* 订单信息 */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                订单信息
              </h3>
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500">📦</span>
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-medium text-gray-900">{order.product.title}</h4>
                  <p className="text-sm text-gray-600">卖家：{order.seller.name || order.seller.email}</p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品价格</span>
                  <span>{formatUSDT(order.productPrice)}</span>
                </div>
                <div className="flex justify-between text-sm mt-2">
                  <span className="text-gray-600">平台手续费</span>
                  <span>{formatUSDT(order.platformFee)}</span>
                </div>
                <div className="flex justify-between text-sm mt-2">
                  <span className="text-gray-600">邮费</span>
                  <span>{formatUSDT(order.shippingFee)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold mt-4 pt-4 border-t">
                  <span>总计</span>
                  <span className="text-blue-600">{formatUSDT(order.totalAmount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 支付内容 */}
          {currentStep === 'select' && (
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  选择支付方式
                </h3>
                <PaymentMethodSelector
                  amount={order.totalAmount}
                  selectedMethod={selectedPaymentMethod}
                  onSelect={handlePaymentMethodSelect}
                />
              </div>
            </div>
          )}

          {currentStep === 'payment' && selectedPaymentMethod && (
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {selectedPaymentMethod.name}
                  </h3>
                  <button
                    onClick={() => setCurrentStep('select')}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    更换支付方式
                  </button>
                </div>
                
                {selectedPaymentMethod.id === 'deposit_balance' && (
                  <DepositPayment
                    orderId={orderId}
                    amount={order.totalAmount}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={() => setCurrentStep('select')}
                  />
                )}

                {selectedPaymentMethod.id === 'binance_pay' && (
                  <BinancePayment
                    orderId={orderId}
                    amount={order.totalAmount}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                  />
                )}

                {selectedPaymentMethod.id === 'bnb_chain' && (
                  <BNBChainPayment
                    orderId={orderId}
                    amount={order.totalAmount}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                  />
                )}
              </div>
            </div>
          )}

          {currentStep === 'processing' && (
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-2">
                  正在处理支付...
                </h3>
                <p className="text-gray-600">
                  请稍候，我们正在确认您的支付信息
                </p>
              </div>
            </div>
          )}

          {currentStep === 'success' && (
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6 text-center">
                <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-xl leading-6 font-medium text-gray-900 mb-2">
                  支付完成！
                </h3>
                <p className="text-gray-600 mb-4">
                  正在跳转到订单页面...
                </p>
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
                  <span className="text-sm text-green-600">2秒后自动跳转</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
