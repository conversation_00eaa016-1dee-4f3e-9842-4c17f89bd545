'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import Link from 'next/link'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  createdAt: string
  product: {
    id: string
    title: string
    images: string
    price: number
  }
  buyer: {
    id: string
    name: string
    email: string
  }
  seller: {
    id: string
    name: string
  }
  shippingAddress: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
  trackingNumber?: string
  shippingCompany?: string
}

interface ShippingCompany {
  code: string
  name: string
  website?: string
}

export default function ShippingPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  // 发货表单数据
  const [shippingForm, setShippingForm] = useState({
    shippingCompany: '',
    trackingNumber: '',
    estimatedDelivery: '',
    shippingNotes: ''
  })

  // 运单号验证状态
  const [trackingValidation, setTrackingValidation] = useState<{
    isValid: boolean
    message: string
    suggestions: string[]
    isDuplicate: boolean
    duplicateInfo?: any
  } | null>(null)

  // 发货历史记录
  const [recentCompanies, setRecentCompanies] = useState<any[]>([])
  const [trackingHistory, setTrackingHistory] = useState<any[]>([])
  const [showTrackingHistory, setShowTrackingHistory] = useState(false)

  // 异常处理
  const [showIssueForm, setShowIssueForm] = useState(false)
  const [issueForm, setIssueForm] = useState({
    issueType: '',
    reason: '',
    estimatedResolutionDate: '',
    alternativeSolution: '',
    notifyBuyer: true
  })

  // 发货模板
  const [shippingTemplates, setShippingTemplates] = useState<any[]>([])
  const [showTemplateForm, setShowTemplateForm] = useState(false)
  const [templateForm, setTemplateForm] = useState({
    name: '',
    shippingCompany: '',
    estimatedDeliveryDays: '',
    shippingNotes: '',
    isDefault: false
  })

  // 快递公司列表
  const [shippingCompanies, setShippingCompanies] = useState<ShippingCompany[]>([])
  const [showAddCompanyForm, setShowAddCompanyForm] = useState(false)
  const [newCompanyForm, setNewCompanyForm] = useState({
    code: '',
    name: '',
    website: '',
    trackingUrl: ''
  })

  const orderId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (orderId) {
      loadOrderData()
      loadShippingCompanies()
      loadShippingHistory()
      loadShippingTemplates()
    } else {
      setLoading(false)
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/orders/seller')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查权限：只有卖家可以访问发货页面
      if (orderData.seller.id !== session?.user?.id) {
        alert('无权限访问此页面')
        router.push('/orders')
        return
      }
      
      // 检查订单状态
      if (orderData.status !== 'PAID') {
        alert('订单状态不允许发货')
        router.push(`/orders/${orderId}`)
        return
      }
      
      setOrder(orderData)
      
      // 如果已有物流信息，填充表单
      if (orderData.trackingNumber) {
        setShippingForm({
          shippingCompany: orderData.shippingCompany || '',
          trackingNumber: orderData.trackingNumber,
          estimatedDelivery: '',
          shippingNotes: ''
        })
      }
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const loadShippingCompanies = async () => {
    try {
      const response = await fetch('/api/shipping-companies', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setShippingCompanies(data.companies)
      }
    } catch (error) {
      console.error('加载快递公司列表失败:', error)
    }
  }

  const loadShippingHistory = async () => {
    try {
      // 加载最近使用的快递公司
      const companiesResponse = await fetch('/api/shipping-history?type=companies&limit=5', {
        credentials: 'include'
      })
      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json()
        setRecentCompanies(companiesData.companies)
      }

      // 加载运单号历史
      const trackingResponse = await fetch('/api/shipping-history?type=tracking&limit=10', {
        credentials: 'include'
      })
      if (trackingResponse.ok) {
        const trackingData = await trackingResponse.json()
        setTrackingHistory(trackingData.trackingHistory)
      }
    } catch (error) {
      console.error('加载发货历史失败:', error)
    }
  }

  const validateTrackingNumberAsync = async (trackingNumber: string, company: string) => {
    if (!trackingNumber.trim() || !company) {
      setTrackingValidation(null)
      return
    }

    try {
      const response = await fetch('/api/shipping-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          trackingNumber: trackingNumber.trim(),
          shippingCompany: getCompanyName(company)
        })
      })

      if (response.ok) {
        const validation = await response.json()
        setTrackingValidation(validation)
      }
    } catch (error) {
      console.error('验证运单号失败:', error)
    }
  }

  const handleTrackingNumberChange = (value: string) => {
    setShippingForm({...shippingForm, trackingNumber: value})

    // 防抖验证
    const timeoutId = setTimeout(() => {
      validateTrackingNumberAsync(value, shippingForm.shippingCompany)
    }, 500)

    return () => clearTimeout(timeoutId)
  }

  const handleCompanyChange = (company: string) => {
    setShippingForm({...shippingForm, shippingCompany: company})
    setTrackingValidation(null)

    // 如果已有运单号，重新验证
    if (shippingForm.trackingNumber.trim()) {
      setTimeout(() => {
        validateTrackingNumberAsync(shippingForm.trackingNumber, company)
      }, 100)
    }
  }

  const handleAddCompany = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newCompanyForm.code || !newCompanyForm.name) {
      alert('请填写快递公司代码和名称')
      return
    }

    try {
      const response = await fetch('/api/shipping-companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(newCompanyForm)
      })

      if (response.ok) {
        const data = await response.json()
        setShippingCompanies([...shippingCompanies, data.company])
        setNewCompanyForm({ code: '', name: '', website: '', trackingUrl: '' })
        setShowAddCompanyForm(false)
        alert('快递公司添加成功')
      } else {
        const error = await response.json()
        alert(error.error || '添加快递公司失败')
      }
    } catch (error) {
      console.error('添加快递公司失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const loadShippingTemplates = async () => {
    try {
      const response = await fetch('/api/shipping-templates', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setShippingTemplates(data.templates)

        // 自动应用默认模板
        const defaultTemplate = data.templates.find((t: any) => t.isDefault)
        if (defaultTemplate && !shippingForm.shippingCompany) {
          applyTemplate(defaultTemplate)
        }
      }
    } catch (error) {
      console.error('加载发货模板失败:', error)
    }
  }

  const applyTemplate = (template: any) => {
    const companyCode = shippingCompanies.find(c => c.name === template.shippingCompany)?.code || template.shippingCompany

    setShippingForm({
      ...shippingForm,
      shippingCompany: companyCode,
      shippingNotes: template.shippingNotes || '',
      estimatedDelivery: template.estimatedDeliveryDays ?
        new Date(Date.now() + template.estimatedDeliveryDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0] :
        ''
    })
  }

  const handleSaveAsTemplate = async () => {
    if (!shippingForm.shippingCompany) {
      alert('请先选择快递公司')
      return
    }

    const templateName = prompt('请输入模板名称：')
    if (!templateName) return

    try {
      const response = await fetch('/api/shipping-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: templateName,
          shippingCompany: getCompanyName(shippingForm.shippingCompany),
          estimatedDeliveryDays: shippingForm.estimatedDelivery ?
            Math.ceil((new Date(shippingForm.estimatedDelivery).getTime() - Date.now()) / (24 * 60 * 60 * 1000)) :
            null,
          shippingNotes: shippingForm.shippingNotes,
          isDefault: false
        })
      })

      if (response.ok) {
        alert('模板保存成功')
        loadShippingTemplates()
      } else {
        const error = await response.json()
        alert(error.error || '保存模板失败')
      }
    } catch (error) {
      console.error('保存模板失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const validateTrackingNumber = (trackingNumber: string, company: string): boolean => {
    if (!trackingNumber.trim()) return false
    
    // 基本长度检查
    if (trackingNumber.length < 8 || trackingNumber.length > 30) {
      return false
    }
    
    // 根据快递公司进行格式验证
    switch (company) {
      case 'SF': // 顺丰：12位数字
        return /^\d{12}$/.test(trackingNumber)
      case 'YTO': // 圆通：10位数字或YT开头
        return /^(\d{10}|YT\d{10})$/.test(trackingNumber)
      case 'ZTO': // 中通：12位数字
        return /^\d{12}$/.test(trackingNumber)
      case 'STO': // 申通：12位数字
        return /^\d{12}$/.test(trackingNumber)
      case 'YD': // 韵达：13位数字
        return /^\d{13}$/.test(trackingNumber)
      case 'EMS': // EMS：13位字母数字组合
        return /^[A-Z]{2}\d{9}[A-Z]{2}$/.test(trackingNumber)
      default:
        // 其他快递公司，基本格式检查
        return /^[A-Za-z0-9]+$/.test(trackingNumber)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 基础验证
    if (!shippingForm.shippingCompany) {
      alert('请选择快递公司')
      return
    }

    if (!shippingForm.trackingNumber.trim()) {
      alert('请输入运单号')
      return
    }

    if (!validateTrackingNumber(shippingForm.trackingNumber, shippingForm.shippingCompany)) {
      alert('运单号格式不正确，请检查后重新输入')
      return
    }

    // 运单号验证状态检查
    if (trackingValidation && !trackingValidation.isValid) {
      if (!confirm('运单号验证未通过，确定要继续发货吗？')) {
        return
      }
    }

    // 重复运单号警告
    if (trackingValidation && trackingValidation.isDuplicate) {
      if (!confirm('此运单号已被使用，确定要继续发货吗？这可能导致物流跟踪混乱。')) {
        return
      }
    }

    // 二次确认对话框
    const confirmMessage = `请确认发货信息：

快递公司：${getCompanyName(shippingForm.shippingCompany)}
运单号：${shippingForm.trackingNumber.trim()}
${shippingForm.estimatedDelivery ? `预计送达：${shippingForm.estimatedDelivery}` : ''}
${shippingForm.shippingNotes ? `备注：${shippingForm.shippingNotes}` : ''}

确定要发货吗？发货后将无法撤销。`

    if (!confirm(confirmMessage)) {
      return
    }

    // 防误操作：要求输入订单号后四位
    const orderSuffix = order?.orderNumber.slice(-4)
    const userInput = prompt(`为防止误操作，请输入订单号后四位数字：${orderSuffix}`)

    if (userInput !== orderSuffix) {
      alert('输入的订单号后四位不正确，发货已取消')
      return
    }

    setSubmitting(true)
    
    try {
      // 记录发货操作安全日志
      await fetch('/api/shipping-security', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          orderId,
          action: 'SHIP_ORDER',
          details: `发货：${getCompanyName(shippingForm.shippingCompany)} - ${shippingForm.trackingNumber.trim()}`,
          ipAddress: window.location.hostname,
          userAgent: navigator.userAgent,
          riskLevel: trackingValidation?.isDuplicate ? 'HIGH' : 'LOW'
        })
      })

      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'ship',
          trackingNumber: shippingForm.trackingNumber.trim(),
          shippingCompany: getCompanyName(shippingForm.shippingCompany),
          estimatedDelivery: shippingForm.estimatedDelivery || null,
          shippingNotes: shippingForm.shippingNotes.trim() || null
        })
      })
      
      if (response.ok) {
        alert('发货成功！')
        router.push(`/orders/${orderId}`)
      } else {
        const error = await response.json()
        alert(error.error || '发货失败')
      }
    } catch (error) {
      console.error('发货失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  const getCompanyName = (code: string): string => {
    const company = shippingCompanies.find(c => c.code === code)
    return company ? company.name : code
  }

  const handleReportIssue = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!issueForm.issueType || !issueForm.reason.trim()) {
      alert('请填写异常类型和原因')
      return
    }

    try {
      const response = await fetch(`/api/orders/${orderId}/shipping-issues`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(issueForm)
      })

      if (response.ok) {
        alert('发货异常已记录，我们会尽快处理')
        setShowIssueForm(false)
        setIssueForm({
          issueType: '',
          reason: '',
          estimatedResolutionDate: '',
          alternativeSolution: '',
          notifyBuyer: true
        })
      } else {
        const error = await response.json()
        alert(error.error || '记录异常失败')
      }
    } catch (error) {
      console.error('记录异常失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/orders/seller')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回订单列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href={`/orders/${orderId}`}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回订单详情
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">订单发货</h1>
          </div>
        </div>

        {/* 订单信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">订单信息</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600">订单号:</span>
                  <span className="ml-2 font-mono text-sm">{order.orderNumber}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">下单时间:</span>
                  <span className="ml-2 text-sm">{new Date(order.createdAt).toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">订单金额:</span>
                  <span className="ml-2 text-lg font-semibold text-blue-600">¥{order.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
            <div>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600">买家:</span>
                  <span className="ml-2 text-sm">{order.buyer.name || '匿名用户'}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">联系方式:</span>
                  <span className="ml-2 text-sm">{order.shippingAddress.phone}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">商品数量:</span>
                  <span className="ml-2 text-sm">{order.metadata.quantity}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 商品信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">商品清单</h3>
          <div className="flex items-center space-x-4">
            <img
              src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
              alt={order.product.title}
              className="w-20 h-20 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{order.product.title}</h4>
              <div className="text-sm text-gray-600 mt-1">
                单价: ¥{order.metadata.itemPrice.toFixed(2)} × {order.metadata.quantity}
              </div>
              <div className="text-sm font-medium text-blue-600 mt-1">
                小计: ¥{order.productPrice.toFixed(2)}
              </div>
            </div>
          </div>
        </div>

        {/* 收货地址 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">收货地址</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-4 mb-2">
              <span className="font-medium">{order.shippingAddress.name}</span>
              <span className="text-gray-600">{order.shippingAddress.phone}</span>
            </div>
            <div className="text-gray-700">
              {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district}
            </div>
            <div className="text-gray-700 mt-1">
              {order.shippingAddress.detail}
            </div>
          </div>
        </div>

        {/* 发货表单 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">发货信息</h3>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 发货模板选择 */}
            {shippingTemplates.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  使用发货模板
                </label>
                <div className="flex space-x-2">
                  <select
                    onChange={(e) => {
                      if (e.target.value) {
                        const template = shippingTemplates.find(t => t.id === e.target.value)
                        if (template) applyTemplate(template)
                      }
                    }}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">选择模板快速填充</option>
                    {shippingTemplates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name} {template.isDefault ? '(默认)' : ''}
                      </option>
                    ))}
                  </select>
                  <button
                    type="button"
                    onClick={handleSaveAsTemplate}
                    className="px-4 py-2 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md"
                  >
                    保存为模板
                  </button>
                </div>
              </div>
            )}

            {/* 快递公司选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                快递公司 *
              </label>
              <select
                value={shippingForm.shippingCompany}
                onChange={(e) => handleCompanyChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">请选择快递公司</option>
                {/* 最近使用的快递公司 */}
                {recentCompanies.length > 0 && (
                  <optgroup label="最近使用">
                    {recentCompanies.map((company) => {
                      const companyCode = shippingCompanies.find(c => c.name === company.name)?.code || company.name
                      return (
                        <option key={`recent-${companyCode}`} value={companyCode}>
                          {company.name} (使用{company.count}次)
                        </option>
                      )
                    })}
                  </optgroup>
                )}
                {/* 所有快递公司 */}
                <optgroup label="所有快递公司">
                  {shippingCompanies.map((company) => (
                    <option key={company.code} value={company.code}>
                      {company.name}
                    </option>
                  ))}
                </optgroup>
              </select>
              <div className="mt-2">
                <button
                  type="button"
                  onClick={() => setShowAddCompanyForm(!showAddCompanyForm)}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  + 添加自定义快递公司
                </button>
              </div>

              {/* 添加快递公司表单 */}
              {showAddCompanyForm && (
                <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <h4 className="font-medium text-gray-900 mb-3">添加自定义快递公司</h4>
                  <form onSubmit={handleAddCompany} className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          公司代码 *
                        </label>
                        <input
                          type="text"
                          value={newCompanyForm.code}
                          onChange={(e) => setNewCompanyForm({...newCompanyForm, code: e.target.value.toUpperCase()})}
                          placeholder="如: SF, YTO"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          公司名称 *
                        </label>
                        <input
                          type="text"
                          value={newCompanyForm.name}
                          onChange={(e) => setNewCompanyForm({...newCompanyForm, name: e.target.value})}
                          placeholder="如: 顺丰速运"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        官方网站
                      </label>
                      <input
                        type="url"
                        value={newCompanyForm.website}
                        onChange={(e) => setNewCompanyForm({...newCompanyForm, website: e.target.value})}
                        placeholder="https://www.example.com"
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="flex space-x-2">
                      <button
                        type="submit"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                      >
                        添加
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowAddCompanyForm(false)}
                        className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm"
                      >
                        取消
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>

            {/* 运单号输入 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  运单号 *
                </label>
                {trackingHistory.length > 0 && (
                  <button
                    type="button"
                    onClick={() => setShowTrackingHistory(!showTrackingHistory)}
                    className="text-xs text-blue-600 hover:text-blue-700"
                  >
                    {showTrackingHistory ? '隐藏' : '查看'}历史记录
                  </button>
                )}
              </div>

              <input
                type="text"
                value={shippingForm.trackingNumber}
                onChange={(e) => handleTrackingNumberChange(e.target.value)}
                placeholder="请输入运单号"
                className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                  trackingValidation?.isValid === false ? 'border-red-300' :
                  trackingValidation?.isValid === true ? 'border-green-300' :
                  'border-gray-300'
                }`}
                required
              />

              {/* 验证结果显示 */}
              {trackingValidation && (
                <div className={`mt-2 text-sm ${
                  trackingValidation.isValid ? 'text-green-600' : 'text-red-600'
                }`}>
                  <div className="flex items-center">
                    {trackingValidation.isValid ? (
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                      </svg>
                    ) : (
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                      </svg>
                    )}
                    {trackingValidation.message}
                  </div>

                  {trackingValidation.suggestions.length > 0 && (
                    <div className="mt-1 text-xs">
                      建议格式: {trackingValidation.suggestions.join(', ')}
                    </div>
                  )}

                  {trackingValidation.isDuplicate && (
                    <div className="mt-1 text-orange-600 text-xs">
                      ⚠️ 此运单号已被使用
                      {trackingValidation.duplicateInfo?.isOwnOrder ?
                        `(您的订单: ${trackingValidation.duplicateInfo.orderNumber})` :
                        '(其他订单)'
                      }
                    </div>
                  )}
                </div>
              )}

              {/* 历史记录 */}
              {showTrackingHistory && trackingHistory.length > 0 && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">最近使用的运单号</h5>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {trackingHistory.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between text-xs cursor-pointer hover:bg-gray-100 p-1 rounded"
                        onClick={() => {
                          setShippingForm({
                            ...shippingForm,
                            trackingNumber: item.trackingNumber,
                            shippingCompany: shippingCompanies.find(c => c.name === item.shippingCompany)?.code || ''
                          })
                          setShowTrackingHistory(false)
                        }}
                      >
                        <span className="font-mono">{item.trackingNumber}</span>
                        <span className="text-gray-500">{item.shippingCompany}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <p className="text-xs text-gray-500 mt-1">
                请确保运单号准确无误，发货后买家将通过此运单号跟踪物流
              </p>
            </div>

            {/* 预计送达时间 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                预计送达时间
              </label>
              <input
                type="date"
                value={shippingForm.estimatedDelivery}
                onChange={(e) => setShippingForm({...shippingForm, estimatedDelivery: e.target.value})}
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 发货备注 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                发货备注
              </label>
              <textarea
                value={shippingForm.shippingNotes}
                onChange={(e) => setShippingForm({...shippingForm, shippingNotes: e.target.value})}
                placeholder="可填写特殊说明或注意事项"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
              >
                取消
              </button>
              <button
                type="button"
                onClick={() => setShowIssueForm(true)}
                className="flex-1 bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                报告异常
              </button>
              <button
                type="submit"
                disabled={submitting}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                {submitting ? '发货中...' : '确认发货'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* 异常报告模态框 */}
      {showIssueForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  报告发货异常
                </h3>
                <button
                  onClick={() => setShowIssueForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleReportIssue} className="space-y-6">
                {/* 异常类型 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    异常类型 *
                  </label>
                  <select
                    value={issueForm.issueType}
                    onChange={(e) => setIssueForm({...issueForm, issueType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">请选择异常类型</option>
                    <option value="OUT_OF_STOCK">商品缺货</option>
                    <option value="SHIPPING_DELAY">发货延迟</option>
                    <option value="DAMAGED_GOODS">商品损坏</option>
                    <option value="LOGISTICS_ISSUE">物流问题</option>
                    <option value="OTHER">其他异常</option>
                  </select>
                </div>

                {/* 异常原因 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    异常原因 *
                  </label>
                  <textarea
                    value={issueForm.reason}
                    onChange={(e) => setIssueForm({...issueForm, reason: e.target.value})}
                    placeholder="请详细描述遇到的问题"
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                {/* 预计解决时间 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    预计解决时间
                  </label>
                  <input
                    type="date"
                    value={issueForm.estimatedResolutionDate}
                    onChange={(e) => setIssueForm({...issueForm, estimatedResolutionDate: e.target.value})}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* 替代解决方案 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    替代解决方案
                  </label>
                  <textarea
                    value={issueForm.alternativeSolution}
                    onChange={(e) => setIssueForm({...issueForm, alternativeSolution: e.target.value})}
                    placeholder="如有替代方案，请说明"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* 通知买家 */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={issueForm.notifyBuyer}
                    onChange={(e) => setIssueForm({...issueForm, notifyBuyer: e.target.checked})}
                    className="mr-2 text-blue-600"
                  />
                  <label className="text-sm text-gray-700">
                    立即通知买家此异常情况
                  </label>
                </div>

                {/* 操作按钮 */}
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() => setShowIssueForm(false)}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="flex-1 bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-md text-lg font-medium"
                  >
                    提交异常报告
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
