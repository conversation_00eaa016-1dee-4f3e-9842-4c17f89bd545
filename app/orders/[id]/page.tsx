'use client'

import { useState, useEffect, use } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  paymentMethod: string | null
  paymentScreenshot: string | null
  paymentTxHash: string | null
  paymentConfirmed: boolean
  escrowStatus: string
  escrowAmount: number | null
  escrowFundedAt: string | null
  escrowReleasedAt: string | null
  trackingNumber: string | null
  shippingCompany: string | null
  shippedAt: string | null
  receivedAt: string | null
  refundReason: string | null
  shippingAddress: any
  createdAt: string
  updatedAt: string
  product: {
    id: string
    title: string
    description: string
    price: number
    images: any
    category: string
    condition: string
  }
  buyer: {
    id: string
    name: string | null
    email: string | null
  }
  seller: {
    id: string
    name: string | null
    email: string | null
  }
}

export default function OrderDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const { data: session } = useSession()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [orderLogs, setOrderLogs] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const [paymentData, setPaymentData] = useState({
    paymentTxHash: '',
    paymentScreenshot: ''
  })
  const [shippingData, setShippingData] = useState({
    trackingNumber: '',
    shippingCompany: ''
  })
  const [reviewData, setReviewData] = useState({
    rating: 5,
    canReview: false,
    hasReviewed: false
  })

  // 售后申请数据
  const [afterSalesRequests, setAfterSalesRequests] = useState<any[]>([])
  const [loadingAfterSales, setLoadingAfterSales] = useState(false)
  const [reviewLoading, setReviewLoading] = useState(false)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    fetchOrder()
    loadAfterSalesRequests()
  }, [session, resolvedParams.id])

  const fetchOrder = async () => {
    try {
      const [orderResponse, logsResponse] = await Promise.all([
        fetch(`/api/orders/${resolvedParams.id}`),
        fetch(`/api/orders/${resolvedParams.id}/logs`)
      ])

      if (orderResponse.ok) {
        const data = await orderResponse.json()
        setOrder(data)

        // 检查评价状态
        if (data.status === 'COMPLETED') {
          checkReviewStatus(data)
        }
      } else if (orderResponse.status === 404) {
        router.push('/orders')
      }

      if (logsResponse.ok) {
        const logsData = await logsResponse.json()
        setOrderLogs(logsData.logs)
      }
    } catch (error) {
      console.error('Failed to fetch order:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkReviewStatus = async (orderData: Order) => {
    try {
      const response = await fetch(`/api/reviews?orderId=${orderData.id}`)
      if (response.ok) {
        const data = await response.json()
        const userReview = data.reviews.find((review: any) => review.reviewerId === session?.user?.id)

        const completedAt = new Date(orderData.receivedAt || orderData.updatedAt)
        const reviewDeadline = new Date(completedAt.getTime() + 7 * 24 * 60 * 60 * 1000)
        const canReview = new Date() <= reviewDeadline && !userReview

        setReviewData({
          rating: 5,
          canReview,
          hasReviewed: !!userReview
        })
      }
    } catch (error) {
      console.error('Failed to check review status:', error)
    }
  }

  const handleOrderAction = async (action: string, data?: any) => {
    setActionLoading(true)
    try {
      const response = await fetch(`/api/orders/${resolvedParams.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          ...data
        }),
      })

      if (response.ok) {
        const updatedOrder = await response.json()
        setOrder(updatedOrder)
        alert('操作成功！')
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setActionLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING_PAYMENT': '待付款',
      'PAID': '已付款',
      'SHIPPED': '已发货',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消',
      'REFUND_REQUESTED': '申请退款中'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING_PAYMENT': 'bg-yellow-100 text-yellow-800',
      'PAID': 'bg-blue-100 text-blue-800',
      'SHIPPED': 'bg-purple-100 text-purple-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800',
      'REFUND_REQUESTED': 'bg-orange-100 text-orange-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const loadAfterSalesRequests = async () => {
    if (!resolvedParams.id) return

    setLoadingAfterSales(true)
    try {
      const response = await fetch(`/api/orders/${resolvedParams.id}/after-sales`, {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setAfterSalesRequests(data.requests)
      }
    } catch (error) {
      console.error('加载售后申请失败:', error)
    } finally {
      setLoadingAfterSales(false)
    }
  }

  const handleSubmitReview = async () => {
    if (!order) return

    setReviewLoading(true)
    try {
      const isBuyer = session?.user?.id === order.buyer.id
      const reviewType = isBuyer ? 'BUYER_TO_SELLER' : 'SELLER_TO_BUYER'

      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: order.id,
          rating: reviewData.rating,
          type: reviewType
        }),
      })

      if (response.ok) {
        alert('评价提交成功！')
        setReviewData(prev => ({ ...prev, hasReviewed: true, canReview: false }))
      } else {
        const data = await response.json()
        alert(data.error || '评价提交失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setReviewLoading(false)
    }
  }

  const getRatingStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => interactive && onRatingChange && onRatingChange(star)}
            className={`text-lg ${
              star <= rating
                ? 'text-yellow-400'
                : 'text-gray-300'
            } ${interactive ? 'hover:text-yellow-400 cursor-pointer' : 'cursor-default'}`}
            disabled={!interactive}
          >
            ★
          </button>
        ))}
      </div>
    )
  }

  const getOrderStatusSteps = () => {
    if (!order) return []

    const steps = [
      {
        status: 'PENDING_PAYMENT',
        title: '待付款',
        completed: !['PENDING_PAYMENT', 'DRAFT'].includes(order.status),
        current: order.status === 'PENDING_PAYMENT',
        time: order.createdAt
      },
      {
        status: 'PAID',
        title: '已付款',
        completed: !['PENDING_PAYMENT', 'DRAFT', 'PAID'].includes(order.status),
        current: order.status === 'PAID',
        time: order.status !== 'PENDING_PAYMENT' && order.status !== 'DRAFT' ? order.updatedAt : null
      },
      {
        status: 'SHIPPED',
        title: '已发货',
        completed: ['DELIVERED', 'COMPLETED'].includes(order.status),
        current: order.status === 'SHIPPED',
        time: order.shippedAt
      },
      {
        status: 'COMPLETED',
        title: '已完成',
        completed: order.status === 'COMPLETED',
        current: order.status === 'COMPLETED',
        time: order.receivedAt
      }
    ]

    // 如果订单被取消或退款，显示特殊状态
    if (['CANCELLED', 'REFUNDED'].includes(order.status)) {
      return [
        ...steps.slice(0, 2),
        {
          status: order.status,
          title: order.status === 'CANCELLED' ? '已取消' : '已退款',
          completed: true,
          current: true,
          time: order.updatedAt
        }
      ]
    }

    return steps
  }

  const getActionText = (action: string) => {
    const actionMap: Record<string, string> = {
      'CREATE_ORDER': '创建订单',
      'PAYMENT_CONFIRMED': '支付确认',
      'SHIP_ORDER': '发货',
      'CONFIRM_RECEIPT': '确认收货',
      'REQUEST_REFUND': '申请退款',
      'APPROVE_REFUND': '批准退款',
      'REJECT_REFUND': '拒绝退款',
      'CANCEL_ORDER': '取消订单',
      'EXTEND_RECEIPT_TIME': '延长收货时间'
    }
    return actionMap[action] || action
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">订单不存在</h2>
          <Link
            href="/orders"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回订单列表
          </Link>
        </div>
      </div>
    )
  }

  const isBuyer = session?.user?.id === order.buyer.id
  const isSeller = session?.user?.id === order.seller.id

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/orders" className="text-gray-700 hover:text-gray-900">
                我的订单
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 订单基本信息 */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  订单详情
                </h3>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(order.status)}`}>
                  {getStatusText(order.status)}
                </span>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">订单号</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.orderNumber}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">创建时间</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(order.createdAt).toLocaleString()}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">商品价格</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatUSDT(order.productPrice)}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">平台手续费</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatUSDT(order.platformFee)}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">邮费</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatUSDT(order.shippingFee)}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">总金额</dt>
                  <dd className="mt-1 text-lg font-bold text-blue-600">{formatUSDT(order.totalAmount)}</dd>
                </div>
              </div>
            </div>
          </div>

          {/* 商品信息 */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                商品信息
              </h3>
              <div className="flex items-center space-x-4">
                <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500">📦</span>
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-medium text-gray-900">{order.product.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{order.product.description}</p>
                  <p className="text-lg font-bold text-blue-600 mt-2">{formatUSDT(order.product.price)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 收货地址 */}
          {order.shippingAddress && (
            <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  收货地址
                </h3>
                <div className="text-sm text-gray-900">
                  <p>收货人：{order.shippingAddress.name}</p>
                  <p>电话：{order.shippingAddress.phone}</p>
                  <p>地址：{order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district} {order.shippingAddress.detail}</p>
                </div>
              </div>
            </div>
          )}

          {/* 物流跟踪 */}
          {order.trackingNumber && (
            <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  物流跟踪
                </h3>
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        运单号: <span className="font-mono">{order.trackingNumber}</span>
                      </div>
                      {order.shippingCompany && (
                        <div className="text-sm text-gray-600 mt-1">
                          快递公司: {order.shippingCompany}
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      {order.shippedAt && (
                        <div className="text-sm text-gray-600">
                          发货时间: {new Date(order.shippedAt).toLocaleString()}
                        </div>
                      )}
                      <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${
                        order.status === 'SHIPPED' ? 'bg-purple-100 text-purple-800' :
                        order.status === 'DELIVERED' ? 'bg-indigo-100 text-indigo-800' :
                        order.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status === 'SHIPPED' ? '运输中' :
                         order.status === 'DELIVERED' ? '已送达' :
                         order.status === 'COMPLETED' ? '已完成' : '未知状态'}
                      </div>
                    </div>
                  </div>

                  {/* 物流状态提示 */}
                  <div className="text-sm text-gray-600">
                    {order.status === 'SHIPPED' && (
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                          <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                        </svg>
                        商品正在运输中，请耐心等待
                      </div>
                    )}
                    {order.status === 'DELIVERED' && (
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                        </svg>
                        商品已送达，请确认收货
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 操作区域 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                订单操作
              </h3>

              {/* 调试信息 - 显示给所有用户 */}
              <div className="border rounded-lg p-4 bg-yellow-50 mb-4">
                <h4 className="font-medium text-gray-900 mb-3">🔧 调试信息</h4>
                <p className="text-sm text-gray-600">
                  订单状态: {order.status} | 是否买家: {isBuyer ? '是' : '否'} | 用户ID: {session?.user?.id} | 买家ID: {order.buyer.id}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  快递单号: {order.trackingNumber || '未填写'} | 快递公司: {order.shippingCompany || '未填写'}
                </p>
              </div>

              {/* 买家操作 */}
              {isBuyer && (
                <div className="space-y-4">
                  {order.status === 'PENDING_PAYMENT' && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">订单支付</h4>
                      <div className="space-y-3">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <div className="flex items-start space-x-2">
                            <svg className="w-4 h-4 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                            <div className="text-xs text-blue-700">
                              <p className="font-medium mb-1">托管支付保护</p>
                              <p>您的资金将安全托管在平台，确认收货后才会释放给卖家。</p>
                            </div>
                          </div>
                        </div>
                        <Link
                          href={`/orders/${order.id}/payment`}
                          className="block w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md text-center font-medium"
                        >
                          立即支付 ({formatUSDT(order.totalAmount)})
                        </Link>
                      </div>
                    </div>
                  )}

                  {order.status === 'PAID' && order.escrowStatus === 'PENDING' && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">支付状态</h4>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex items-start space-x-2">
                          <svg className="w-4 h-4 text-yellow-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                          </svg>
                          <div className="text-xs text-yellow-700">
                            <p className="font-medium mb-1">支付确认中</p>
                            <p>您的支付正在确认中，资金将在确认后托管到平台账户。</p>
                            {order.paymentTxHash && (
                              <p className="mt-2 font-mono text-xs break-all">
                                交易哈希: {order.paymentTxHash}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {order.status === 'PAID' && order.escrowStatus === 'FUNDED' && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">资金托管中</h4>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-start space-x-2">
                          <svg className="w-4 h-4 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <div className="text-xs text-green-700">
                            <p className="font-medium mb-1">资金已托管</p>
                            <p>您的资金已安全托管在平台，等待卖家发货。确认收货后资金将释放给卖家。</p>
                            {order.escrowFundedAt && (
                              <p className="mt-2">
                                托管时间: {new Date(order.escrowFundedAt).toLocaleString('zh-CN')}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {order.status === 'SHIPPED' && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">确认收货</h4>
                      {order.trackingNumber && (
                        <p className="text-sm text-gray-600 mb-3">
                          快递单号：{order.trackingNumber} ({order.shippingCompany})
                        </p>
                      )}
                      <button
                        onClick={() => handleOrderAction('confirm_received')}
                        disabled={actionLoading}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                      >
                        {actionLoading ? '确认中...' : '确认收货'}
                      </button>
                    </div>
                  )}

                  {['SHIPPED', 'DELIVERED', 'COMPLETED'].includes(order.status) && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">售后服务</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        如果商品有质量问题或不符合描述，可以申请售后服务
                      </p>
                      <Link
                        href={`/orders/${order.id}/after-sales`}
                        className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium inline-block"
                      >
                        申请售后
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* 卖家操作 */}
              {isSeller && (
                <div className="space-y-4">
                  {order.status === 'PAID' && !order.paymentConfirmed && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">确认收款</h4>
                      {order.paymentTxHash && (
                        <p className="text-sm text-gray-600 mb-3">
                          买家提交的交易哈希：{order.paymentTxHash}
                        </p>
                      )}
                      <button
                        onClick={() => handleOrderAction('confirm_payment')}
                        disabled={actionLoading}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                      >
                        {actionLoading ? '确认中...' : '确认收款'}
                      </button>
                    </div>
                  )}

                  {order.status === 'PAID' && order.paymentConfirmed && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">发货</h4>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            快递公司
                          </label>
                          <input
                            type="text"
                            value={shippingData.shippingCompany}
                            onChange={(e) => setShippingData(prev => ({ ...prev, shippingCompany: e.target.value }))}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                            placeholder="请输入快递公司"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            快递单号
                          </label>
                          <input
                            type="text"
                            value={shippingData.trackingNumber}
                            onChange={(e) => setShippingData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm px-3 py-2 border"
                            placeholder="请输入快递单号"
                          />
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleOrderAction('ship', shippingData)}
                            disabled={actionLoading || !shippingData.trackingNumber}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                          >
                            {actionLoading ? '发货中...' : '确认发货'}
                          </button>
                          <Link
                            href={`/orders/print/${order.id}`}
                            target="_blank"
                            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                          >
                            打印发货单
                          </Link>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 通用操作 */}
              {order.status === 'PENDING_PAYMENT' && (
                <div className="border rounded-lg p-4 mt-4">
                  <h4 className="font-medium text-gray-900 mb-3">取消订单</h4>
                  <button
                    onClick={() => handleOrderAction('cancel')}
                    disabled={actionLoading}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                  >
                    {actionLoading ? '取消中...' : '取消订单'}
                  </button>
                </div>
              )}

              {/* 售后申请处理 */}
              {afterSalesRequests.length > 0 && (
                <div className="border rounded-lg p-4 mt-4">
                  <h4 className="font-medium text-gray-900 mb-3">售后申请</h4>
                  <div className="space-y-3">
                    {afterSalesRequests.map((request) => (
                      <div key={request.id} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              request.type === 'REFUND' ? 'bg-red-100 text-red-800' :
                              request.type === 'EXCHANGE' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {request.type === 'REFUND' ? '退款' :
                               request.type === 'EXCHANGE' ? '换货' : '维修'}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              request.status === 'PENDING' ? 'bg-orange-100 text-orange-800' :
                              request.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                              request.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {request.status === 'PENDING' ? '待处理' :
                               request.status === 'APPROVED' ? '已同意' :
                               request.status === 'REJECTED' ? '已拒绝' : request.status}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(request.createdAt).toLocaleString()}
                          </div>
                        </div>
                        <div className="text-sm text-gray-700 mb-2">
                          <strong>原因:</strong> {request.reason}
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                          {request.description}
                        </div>
                        <div className="flex space-x-2">
                          <Link
                            href={`/after-sales/${request.id}/chat`}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                          >
                            沟通协商
                          </Link>
                          {request.status === 'PENDING' && isSeller && (
                            <Link
                              href={`/orders/${order.id}/after-sales/handle?requestId=${request.id}`}
                              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                            >
                              处理申请
                            </Link>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 沟通区域 */}
              <div className="border rounded-lg p-4 mt-4">
                <h4 className="font-medium text-gray-900 mb-3">订单沟通</h4>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    与{isBuyer ? '卖家' : '买家'}沟通交易详情
                  </div>
                  <Link
                    href={`/chat/${order.id}`}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    打开聊天
                  </Link>
                </div>
              </div>

              {/* 评价区域 */}
              {order.status === 'COMPLETED' && (
                <div className="border rounded-lg p-4 mt-4">
                  <h4 className="font-medium text-gray-900 mb-3">交易评价</h4>
                  {reviewData.hasReviewed ? (
                    <div className="text-green-600 text-sm">
                      ✓ 您已完成评价
                    </div>
                  ) : reviewData.canReview ? (
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          评价{isBuyer ? '卖家' : '买家'}
                        </label>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">评分:</span>
                          {getRatingStars(reviewData.rating, true, (rating) =>
                            setReviewData(prev => ({ ...prev, rating }))
                          )}
                          <span className="text-sm text-gray-600">({reviewData.rating}/5)</span>
                        </div>
                      </div>
                      <button
                        onClick={handleSubmitReview}
                        disabled={reviewLoading}
                        className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                      >
                        {reviewLoading ? '提交中...' : '提交评价'}
                      </button>
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm">
                      评价时间已过期（订单完成后7天内有效）
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 订单状态流程 */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">订单状态</h3>
            <div className="flex items-center justify-between">
              {getOrderStatusSteps().map((step, index) => (
                <div key={step.status} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    step.completed ? 'bg-green-500 text-white' :
                    step.current ? 'bg-blue-500 text-white' :
                    'bg-gray-300 text-gray-600'
                  }`}>
                    {step.completed ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <div className="ml-3 text-sm">
                    <div className={`font-medium ${step.current ? 'text-blue-600' : step.completed ? 'text-green-600' : 'text-gray-500'}`}>
                      {step.title}
                    </div>
                    {step.time && (
                      <div className="text-gray-500 text-xs">
                        {new Date(step.time).toLocaleString('zh-CN')}
                      </div>
                    )}
                  </div>
                  {index < getOrderStatusSteps().length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${step.completed ? 'bg-green-500' : 'bg-gray-300'}`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 操作历史 */}
          {orderLogs.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">操作历史</h3>
              <div className="space-y-4">
                {orderLogs.map((log, index) => (
                  <div key={log.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium text-gray-900">
                          {getActionText(log.action)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(log.createdAt).toLocaleString('zh-CN')}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {log.description}
                      </div>
                      {log.operator && (
                        <div className="text-xs text-gray-500 mt-1">
                          操作人: {log.operator.name || '系统'}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 快捷链接 */}
          <div className="mt-6 text-center">
            <Link
              href="/reviews"
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              查看所有评价 →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
