'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Navbar from '@/components/Navbar'
import {
  ChartBarIcon,
  TrophyIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface CreditInfo {
  user: {
    id: string
    name: string | null
    creditScore: number
    creditLevel: {
      level: string
      name: string
      color: string
      bgColor: string
      icon: string
      description: string
    }
    memberSince: string
  }
  statistics: {
    totalOrders: number
    completedOrders: number
    totalReviews: number
    averageRating: number
    goodReviewRate: number
    completionRate: number
  }
  creditHistories: CreditHistory[]
  recentChanges: RecentChange[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

interface CreditHistory {
  id: string
  changeType: string
  changeScore: number
  reason: string
  beforeScore: number
  afterScore: number
  createdAt: string
  orderId?: string
  reviewId?: string
}

interface RecentChange {
  changeScore: number
  afterScore: number
  createdAt: string
  changeType: string
}

export default function CreditPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [creditInfo, setCreditInfo] = useState<CreditInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)

  // 检查登录状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // 获取信用信息
  const fetchCreditInfo = async (page: number = 1) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/user/credit?page=${page}&limit=20`)
      if (response.ok) {
        const data = await response.json()
        setCreditInfo(data)
        setCurrentPage(page)
      } else {
        console.error('获取信用信息失败')
      }
    } catch (error) {
      console.error('获取信用信息失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 页面加载时获取信用信息
  useEffect(() => {
    if (session?.user?.id) {
      fetchCreditInfo(1)
    }
  }, [session])

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 获取变动类型显示文本
  const getChangeTypeText = (changeType: string) => {
    const typeMap: { [key: string]: string } = {
      ORDER_COMPLETE: '订单完成',
      REVIEW_RECEIVED: '收到评价',
      ADMIN_ADJUST: '管理员调整',
      PENALTY: '违规扣分',
      BONUS: '奖励加分',
      FIRST_ORDER: '首次交易奖励',
      MONTHLY_ACTIVE: '月度活跃奖励'
    }
    return typeMap[changeType] || changeType
  }

  // 获取变动类型颜色
  const getChangeTypeColor = (changeType: string) => {
    const colorMap: { [key: string]: string } = {
      ORDER_COMPLETE: 'text-blue-600',
      REVIEW_RECEIVED: 'text-green-600',
      ADMIN_ADJUST: 'text-purple-600',
      PENALTY: 'text-red-600',
      BONUS: 'text-green-600',
      FIRST_ORDER: 'text-yellow-600',
      MONTHLY_ACTIVE: 'text-indigo-600'
    }
    return colorMap[changeType] || 'text-gray-600'
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题 */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">信用记录</h1>
            <p className="text-gray-600 mt-1">查看您的信用积分和交易记录</p>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 加载骨架屏 */}
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow animate-pulse">
                  <div className="p-6 space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : creditInfo ? (
            <div className="space-y-6">
              {/* 信用概览卡片 */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* 信用分数卡片 */}
                <CreditScoreCard creditInfo={creditInfo} />
                
                {/* 交易统计卡片 */}
                <TransactionStatsCard statistics={creditInfo.statistics} />
                
                {/* 信用等级卡片 */}
                <CreditLevelCard 
                  creditLevel={creditInfo.user.creditLevel}
                  creditScore={creditInfo.user.creditScore}
                />
              </div>

              {/* 信用变动历史 */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">信用变动历史</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    共 {creditInfo.pagination.total} 条记录
                  </p>
                </div>
                
                <div className="divide-y divide-gray-200">
                  {creditInfo.creditHistories.length > 0 ? (
                    creditInfo.creditHistories.map((history) => (
                      <CreditHistoryItem 
                        key={history.id} 
                        history={history}
                        formatDate={formatDate}
                        getChangeTypeText={getChangeTypeText}
                        getChangeTypeColor={getChangeTypeColor}
                      />
                    ))
                  ) : (
                    <div className="p-6 text-center text-gray-500">
                      暂无信用变动记录
                    </div>
                  )}
                </div>

                {/* 分页 */}
                {creditInfo.pagination.pages > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <button
                        onClick={() => fetchCreditInfo(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        上一页
                      </button>
                      
                      <span className="text-sm text-gray-700">
                        第 {currentPage} 页，共 {creditInfo.pagination.pages} 页
                      </span>
                      
                      <button
                        onClick={() => fetchCreditInfo(currentPage + 1)}
                        disabled={currentPage === creditInfo.pagination.pages}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        下一页
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* 信用提升建议 */}
              <CreditImprovementTips />
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500">获取信用信息失败，请刷新页面重试</div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

// 信用分数卡片组件
function CreditScoreCard({ creditInfo }: { creditInfo: CreditInfo }) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <TrophyIcon className="h-8 w-8 text-blue-600" />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-medium text-gray-900">信用积分</h3>
          <div className="text-3xl font-bold text-blue-600 mt-2">
            {creditInfo.user.creditScore}
          </div>
          <p className="text-sm text-gray-500 mt-1">
            成为会员 {new Date(creditInfo.user.memberSince).getFullYear()} 年
          </p>
        </div>
      </div>

      {/* 信用分数进度条 */}
      <div className="mt-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>0</span>
          <span>1000</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(creditInfo.user.creditScore / 1000) * 100}%` }}
          ></div>
        </div>
      </div>
    </div>
  )
}

// 交易统计卡片组件
function TransactionStatsCard({ statistics }: { statistics: CreditInfo['statistics'] }) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <ChartBarIcon className="h-8 w-8 text-green-600" />
        <h3 className="text-lg font-medium text-gray-900 ml-3">交易统计</h3>
      </div>

      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">总订单数</span>
          <span className="text-sm font-medium text-gray-900">{statistics.totalOrders}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">完成订单</span>
          <span className="text-sm font-medium text-gray-900">{statistics.completedOrders}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">完成率</span>
          <span className="text-sm font-medium text-green-600">{statistics.completionRate}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">平均评分</span>
          <span className="text-sm font-medium text-yellow-600">
            {statistics.averageRating.toFixed(1)} ⭐
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">好评率</span>
          <span className="text-sm font-medium text-green-600">{statistics.goodReviewRate}%</span>
        </div>
      </div>
    </div>
  )
}

// 信用等级卡片组件
function CreditLevelCard({
  creditLevel,
  creditScore
}: {
  creditLevel: CreditInfo['user']['creditLevel']
  creditScore: number
}) {
  // 计算到下一等级的进度
  const getProgressToNextLevel = () => {
    if (creditScore >= 90) return 100
    if (creditScore >= 70) return ((creditScore - 70) / 20) * 100
    if (creditScore >= 50) return ((creditScore - 50) / 20) * 100
    if (creditScore >= 30) return ((creditScore - 30) / 20) * 100
    return (creditScore / 30) * 100
  }

  const getNextLevelName = () => {
    if (creditScore >= 90) return null
    if (creditScore >= 70) return '优秀'
    if (creditScore >= 50) return '良好'
    if (creditScore >= 30) return '一般'
    return '较差'
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <div className="text-2xl mr-3">{creditLevel.icon}</div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">信用等级</h3>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${creditLevel.bgColor} ${creditLevel.color}`}>
            {creditLevel.name}
          </span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-4">{creditLevel.description}</p>

      {getNextLevelName() && (
        <div>
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>距离 {getNextLevelName()} 等级</span>
            <span>{Math.round(getProgressToNextLevel())}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                creditLevel.level === 'EXCELLENT' ? 'bg-green-500' :
                creditLevel.level === 'GOOD' ? 'bg-blue-500' :
                creditLevel.level === 'FAIR' ? 'bg-yellow-500' :
                creditLevel.level === 'POOR' ? 'bg-orange-500' : 'bg-red-500'
              }`}
              style={{ width: `${getProgressToNextLevel()}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  )
}

// 信用历史记录项组件
function CreditHistoryItem({
  history,
  formatDate,
  getChangeTypeText,
  getChangeTypeColor
}: {
  history: CreditHistory
  formatDate: (date: string) => string
  getChangeTypeText: (type: string) => string
  getChangeTypeColor: (type: string) => string
}) {
  return (
    <div className="p-6 hover:bg-gray-50 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className={`flex-shrink-0 ${history.changeScore > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {history.changeScore > 0 ? (
              <ArrowUpIcon className="h-5 w-5" />
            ) : (
              <ArrowDownIcon className="h-5 w-5" />
            )}
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-medium ${getChangeTypeColor(history.changeType)}`}>
                {getChangeTypeText(history.changeType)}
              </span>
              <span className={`text-sm font-bold ${history.changeScore > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {history.changeScore > 0 ? '+' : ''}{history.changeScore}
              </span>
            </div>
            <p className="text-sm text-gray-600 mt-1">{history.reason}</p>
            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
              <span>变动前: {history.beforeScore}</span>
              <span>变动后: {history.afterScore}</span>
              <span>{formatDate(history.createdAt)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 信用提升建议组件
function CreditImprovementTips() {
  const tips = [
    {
      icon: '🎯',
      title: '完成更多交易',
      description: '每完成一笔订单可获得 +2 信用积分',
      color: 'text-blue-600'
    },
    {
      icon: '⭐',
      title: '获得好评',
      description: '5星好评 +3分，4星好评 +2分，提供优质服务',
      color: 'text-yellow-600'
    },
    {
      icon: '⚡',
      title: '及时响应',
      description: '快速回复消息，及时处理订单，提升用户体验',
      color: 'text-green-600'
    },
    {
      icon: '🛡️',
      title: '诚信交易',
      description: '避免违规行为，保持良好的交易记录',
      color: 'text-purple-600'
    },
    {
      icon: '📈',
      title: '保持活跃',
      description: '定期登录平台，参与平台活动获得奖励积分',
      color: 'text-indigo-600'
    },
    {
      icon: '💎',
      title: '完善资料',
      description: '完善个人资料，绑定币安账户，提升信任度',
      color: 'text-pink-600'
    }
  ]

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">信用提升建议</h3>
        </div>
        <p className="text-sm text-gray-500 mt-1">
          遵循以下建议，快速提升您的信用积分
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {tips.map((tip, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">{tip.icon}</div>
                <div className="flex-1">
                  <h4 className={`font-medium ${tip.color} mb-1`}>{tip.title}</h4>
                  <p className="text-sm text-gray-600">{tip.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start space-x-3">
            <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">信用积分规则</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 完成订单：+2分</li>
                <li>• 5星好评：+3分，4星好评：+2分，3星评价：+1分</li>
                <li>• 2星差评：-1分，1星差评：-3分</li>
                <li>• 首次交易奖励：+5分</li>
                <li>• 取消订单：-2分，争议处罚：-5分</li>
                <li>• 信用积分范围：0-1000分</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
