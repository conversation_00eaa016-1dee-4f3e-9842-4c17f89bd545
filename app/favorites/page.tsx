'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import { HeartIcon, TrashIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

interface Favorite {
  id: string
  createdAt: string
  product: {
    id: string
    title: string
    description: string
    price: number
    images: string
    category: string
    condition: string
    city: string
    district: string
    status: string
    createdAt: string
    seller: {
      id: string
      name: string | null
      userId: string
      creditScore: number
      city: string
      district: string
    }
  }
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}

export default function FavoritesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [favorites, setFavorites] = useState<Favorite[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [isDeleting, setIsDeleting] = useState(false)

  // 检查登录状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // 获取收藏列表
  const fetchFavorites = async (page: number = 1) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/user/favorites?page=${page}&limit=20`)
      if (response.ok) {
        const data = await response.json()
        setFavorites(data.favorites)
        setPagination(data.pagination)
      } else {
        console.error('获取收藏列表失败')
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 页面加载时获取收藏列表
  useEffect(() => {
    if (session?.user?.id) {
      fetchFavorites(1)
    }
  }, [session])

  // 格式化USDT价格
  const formatUSDT = (price: number) => {
    return `${price.toFixed(2)} USDT`
  }

  // 获取商品图片
  const getProductImage = (images: string) => {
    if (!images) return null
    const imageList = images.split(',').filter(img => img.trim())
    return imageList.length > 0 ? imageList[0] : null
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800'
      case 'SOLD_OUT':
        return 'bg-red-100 text-red-800'
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return '可购买'
      case 'SOLD_OUT':
        return '已售完'
      case 'DRAFT':
        return '草稿'
      default:
        return '未知'
    }
  }

  // 移除单个收藏
  const removeFavorite = async (productId: string) => {
    try {
      const response = await fetch(`/api/user/favorites/${productId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setFavorites(prev => prev.filter(fav => fav.product.id !== productId))
        setSelectedItems(prev => {
          const newSet = new Set(prev)
          newSet.delete(productId)
          return newSet
        })
      } else {
        alert('取消收藏失败')
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      alert('取消收藏失败')
    }
  }

  // 批量删除收藏
  const batchDeleteFavorites = async () => {
    if (selectedItems.size === 0) {
      alert('请选择要删除的商品')
      return
    }

    if (!confirm(`确定要删除选中的 ${selectedItems.size} 个收藏吗？`)) {
      return
    }

    try {
      setIsDeleting(true)
      const response = await fetch('/api/user/favorites/batch', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productIds: Array.from(selectedItems)
        })
      })

      if (response.ok) {
        setFavorites(prev => prev.filter(fav => !selectedItems.has(fav.product.id)))
        setSelectedItems(new Set())
      } else {
        alert('批量删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      alert('批量删除失败')
    } finally {
      setIsDeleting(false)
    }
  }

  // 清空收藏夹
  const clearAllFavorites = async () => {
    if (!confirm('确定要清空收藏夹吗？此操作不可恢复。')) {
      return
    }

    try {
      setIsDeleting(true)
      const response = await fetch('/api/user/favorites/batch', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deleteAll: true
        })
      })

      if (response.ok) {
        setFavorites([])
        setSelectedItems(new Set())
      } else {
        alert('清空收藏夹失败')
      }
    } catch (error) {
      console.error('清空收藏夹失败:', error)
      alert('清空收藏夹失败')
    } finally {
      setIsDeleting(false)
    }
  }

  // 切换选择状态
  const toggleSelection = (productId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(productId)) {
        newSet.delete(productId)
      } else {
        newSet.add(productId)
      }
      return newSet
    })
  }

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedItems.size === favorites.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(favorites.map(fav => fav.product.id)))
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和操作按钮 */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">我的收藏</h1>
              <p className="text-gray-600 mt-1">共 {pagination.total} 个收藏</p>
            </div>

            {favorites.length > 0 && (
              <div className="flex space-x-3">
                <button
                  onClick={toggleSelectAll}
                  className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  {selectedItems.size === favorites.length ? '取消全选' : '全选'}
                </button>

                {selectedItems.size > 0 && (
                  <button
                    onClick={batchDeleteFavorites}
                    disabled={isDeleting}
                    className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
                  >
                    {isDeleting ? '删除中...' : `删除选中 (${selectedItems.size})`}
                  </button>
                )}

                <button
                  onClick={clearAllFavorites}
                  disabled={isDeleting}
                  className="text-red-600 hover:text-red-700 font-medium transition-colors"
                >
                  清空收藏夹
                </button>
              </div>
            )}
          </div>

          {/* 收藏列表 */}
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow animate-pulse">
                  <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : favorites.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {favorites.map((favorite) => (
                <FavoriteCard
                  key={favorite.id}
                  favorite={favorite}
                  isSelected={selectedItems.has(favorite.product.id)}
                  onToggleSelection={() => toggleSelection(favorite.product.id)}
                  onRemove={() => removeFavorite(favorite.product.id)}
                  formatUSDT={formatUSDT}
                  getProductImage={getProductImage}
                  getStatusColor={getStatusColor}
                  getStatusText={getStatusText}
                />
              ))}
            </div>
          ) : (
            <EmptyFavorites />
          )}
        </div>
      </main>
    </div>
  )
}

// 收藏卡片组件
interface FavoriteCardProps {
  favorite: Favorite
  isSelected: boolean
  onToggleSelection: () => void
  onRemove: () => void
  formatUSDT: (price: number) => string
  getProductImage: (images: string) => string | null
  getStatusColor: (status: string) => string
  getStatusText: (status: string) => string
}

function FavoriteCard({
  favorite,
  isSelected,
  onToggleSelection,
  onRemove,
  formatUSDT,
  getProductImage,
  getStatusColor,
  getStatusText
}: FavoriteCardProps) {
  const { product } = favorite

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 relative">
      {/* 选择框 */}
      <div className="absolute top-3 left-3 z-10">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onToggleSelection}
          className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
        />
      </div>

      {/* 收藏按钮 */}
      <div className="absolute top-3 right-3 z-10">
        <button
          onClick={onRemove}
          className="p-1 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow"
        >
          <HeartSolidIcon className="w-5 h-5 text-red-500" />
        </button>
      </div>

      <Link href={`/products/${product.id}`}>
        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
          {getProductImage(product.images) ? (
            <img
              src={getProductImage(product.images) || ''}
              alt={product.title}
              className="h-48 w-full object-cover object-center hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="h-48 w-full flex items-center justify-center bg-gray-100">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-2">📦</div>
                <div className="text-sm">暂无图片</div>
              </div>
            </div>
          )}
        </div>
      </Link>

      <div className="p-4">
        {/* 商品标题 */}
        <Link href={`/products/${product.id}`}>
          <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors mb-2 overflow-hidden" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {product.title}
          </h3>
        </Link>

        {/* 价格 */}
        <div className="text-xl font-bold text-blue-600 mb-2">
          {formatUSDT(product.price)}
        </div>

        {/* 位置信息 */}
        {(product.city || product.district) && (
          <div className="text-sm text-gray-500 mb-2">
            📍 {product.city}{product.district && ` ${product.district}`}
          </div>
        )}

        {/* 状态和卖家 */}
        <div className="flex items-center justify-between mb-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
            {getStatusText(product.status)}
          </span>
          <Link
            href={`/products/user/${product.seller.userId}`}
            className="text-sm text-gray-500 hover:text-blue-600 transition-colors"
          >
            {product.seller.name || '匿名用户'}
          </Link>
        </div>

        {/* 收藏时间 */}
        <div className="text-xs text-gray-400">
          收藏于 {new Date(favorite.createdAt).toLocaleDateString()}
        </div>
      </div>
    </div>
  )
}

// 空收藏夹组件
function EmptyFavorites() {
  return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">💝</div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">收藏夹是空的</h3>
      <p className="text-gray-600 mb-4">还没有收藏任何商品，去发现一些好商品吧！</p>
      <Link
        href="/products"
        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
      >
        浏览商品
      </Link>
    </div>
  )
}
