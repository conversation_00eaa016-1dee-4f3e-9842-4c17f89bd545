'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import CouponSelector from '@/components/coupons/CouponSelector'
import GiftCardExchange from '@/components/giftcards/GiftCardExchange'

import RedemptionCodeExchange from '@/components/redemption/RedemptionCodeExchange'
import {
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ShieldCheckIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  EyeIcon,
  EyeSlashIcon,
  QrCodeIcon,
  XMarkIcon,
  GiftIcon,
  TicketIcon
} from '@heroicons/react/24/outline'

interface DepositData {
  user: {
    id: string
    name: string
    email: string
    creditScore: number
    isGuarantor: boolean
    trustLevel: number
    guaranteeCount: number
    fulfillmentRate: number
    // 新增字段用于计算
    totalOrders: number
    successfulOrders: number
    positiveReviews: number
  }
  balance: {
    total: number
    available: number
    guaranteePool: number
    pendingWithdrawal: number
  }
  statistics: {
    totalGuaranteeAmount: number
    totalTransactions: number
    monthlyVolume: number
    trustScore: number
  }
  recentTransactions: any[]
}

interface WithdrawalRecord {
  id: string
  amount: number
  walletAddress: string
  status: string
  withdrawalFee: number
  actualAmount: number
  createdAt: string
  processedAt?: string
  txHash?: string
}

interface DepositRecord {
  id: string
  amount: number
  originalAmount: number
  method: string
  status: string
  txHash?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export default function DepositPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [data, setData] = useState<DepositData | null>(null)
  const [withdrawals, setWithdrawals] = useState<WithdrawalRecord[]>([])
  const [depositRecords, setDepositRecords] = useState<DepositRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [showSensitiveInfo, setShowSensitiveInfo] = useState<{[key: string]: boolean}>({})

  // 切换敏感信息显示/隐藏
  const toggleSensitiveInfo = (withdrawalId: string) => {
    setShowSensitiveInfo(prev => ({
      ...prev,
      [withdrawalId]: !prev[withdrawalId]
    }))
  }
  
  // 充值表单状态
  const [depositAmount, setDepositAmount] = useState('')
  const [depositMethod, setDepositMethod] = useState('chain') // chain, binance_qr
  const [depositNotes, setDepositNotes] = useState('')
  const [depositLoading, setDepositLoading] = useState(false)

  // PIN码验证状态
  const [showPinModal, setShowPinModal] = useState(false)
  const [depositInfo, setDepositInfo] = useState<any>(null)
  const [pinCode, setPinCode] = useState('')
  const [txHash, setTxHash] = useState('')
  const [orderNumber, setOrderNumber] = useState('')
  const [pinLoading, setPinLoading] = useState(false)
  
  // 提现表单状态
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [withdrawWallet, setWithdrawWallet] = useState('')
  const [withdrawNotes, setWithdrawNotes] = useState('')
  const [withdrawLoading, setWithdrawLoading] = useState(false)
  const [selectedCoupon, setSelectedCoupon] = useState<any>(null)

  // 中间人申请状态
  const [guarantorReason, setGuarantorReason] = useState('')
  const [guarantorExperience, setGuarantorExperience] = useState('')
  const [guarantorLoading, setGuarantorLoading] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user?.id) {
      fetchData()
      fetchWithdrawals()
      fetchDepositRecords()
    }
  }, [session, status, router])

  const fetchData = async () => {
    try {
      const response = await fetch('/api/funds/balance')
      if (response.ok) {
        const result = await response.json()
        setData(result)
      } else {
        console.error('获取保证金数据失败')
      }
    } catch (error) {
      console.error('获取保证金数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchWithdrawals = async () => {
    try {
      const response = await fetch('/api/funds/withdrawal')
      if (response.ok) {
        const result = await response.json()
        setWithdrawals(result.withdrawals || [])
      }
    } catch (error) {
      console.error('获取提现记录失败:', error)
    }
  }

  const fetchDepositRecords = async () => {
    try {
      const response = await fetch('/api/funds/deposit')
      if (response.ok) {
        const result = await response.json()
        setDepositRecords(result.records || [])
      }
    } catch (error) {
      console.error('获取充值记录失败:', error)
    }
  }

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!depositAmount || parseFloat(depositAmount) <= 0) {
      alert('请输入有效的充值金额')
      return
    }

    setDepositLoading(true)
    try {
      const response = await fetch('/api/funds/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(depositAmount),
          method: depositMethod,
          notes: depositNotes
        })
      })

      const result = await response.json()
      if (response.ok) {
        // 保存充值信息并显示PIN码验证模态框
        setDepositInfo(result)
        setShowPinModal(true)

        // 清空表单
        setDepositAmount('')
        setDepositNotes('')
      } else {
        alert(result.error || '充值失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setDepositLoading(false)
    }
  }

  // PIN码验证处理
  const handlePinVerification = async () => {
    if (depositInfo.method === 'chain' && !txHash.trim()) {
      alert('请输入交易哈希')
      return
    }

    if (depositInfo.method === 'binance_qr' && !orderNumber.trim()) {
      alert('请输入币安订单号')
      return
    }

    setPinLoading(true)
    try {
      const response = await fetch(`/api/funds/deposit/${depositInfo.depositId}/verify-pin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: depositInfo.paymentPin, // 直接使用显示的PIN码
          txHash: txHash.trim(),
          orderNumber: orderNumber.trim()
        })
      })

      const result = await response.json()
      if (response.ok) {
        alert(result.message || '充值申请已提交，请等待管理员确认到账')
        setShowPinModal(false)
        setPinCode('')
        setTxHash('')
        setOrderNumber('')
        setDepositInfo(null)
        fetchData()
        fetchDepositRecords()
      } else {
        alert(result.error || 'PIN码验证失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setPinLoading(false)
    }
  }

  const handleWithdraw = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      alert('请输入有效的提现金额')
      return
    }
    if (!withdrawWallet) {
      alert('请输入钱包地址')
      return
    }

    setWithdrawLoading(true)
    try {
      // 如果使用减免券，先使用减免券
      let couponUsed = false
      if (selectedCoupon) {
        try {
          const couponResponse = await fetch(`/api/rewards/coupons/${selectedCoupon.id}/use`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              context: 'withdrawal'
            })
          })

          const couponResult = await couponResponse.json()
          if (couponResponse.ok && couponResult.success) {
            couponUsed = true
          } else {
            alert(couponResult.error || '减免券使用失败')
            return
          }
        } catch (error) {
          alert('减免券使用失败，请稍后重试')
          return
        }
      }

      const response = await fetch('/api/funds/withdrawal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(withdrawAmount),
          walletAddress: withdrawWallet,
          notes: withdrawNotes,
          couponId: selectedCoupon?.id,
          useCoupon: couponUsed
        })
      })

      const result = await response.json()
      if (response.ok) {
        alert(selectedCoupon ? '提现申请已提交，减免券已使用，请等待审核' : '提现申请已提交，请等待审核')
        setWithdrawAmount('')
        setWithdrawWallet('')
        setWithdrawNotes('')
        setSelectedCoupon(null)
        fetchWithdrawals()
        fetchData()
      } else {
        alert(result.error || '提现申请失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setWithdrawLoading(false)
    }
  }

  const handleGuarantorApply = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!guarantorReason.trim()) {
      alert('请填写申请理由')
      return
    }

    setGuarantorLoading(true)
    try {
      const response = await fetch('/api/guarantor/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: guarantorReason,
          experience: guarantorExperience
        })
      })

      const result = await response.json()
      if (response.ok) {
        alert('申请已提交，请等待管理员审核')
        setGuarantorReason('')
        setGuarantorExperience('')
      } else {
        alert(result.error || '申请失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setGuarantorLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100'
      case 'APPROVED': return 'text-blue-600 bg-blue-100'
      case 'PROCESSING': return 'text-purple-600 bg-purple-100'
      case 'COMPLETED': return 'text-green-600 bg-green-100'
      case 'REJECTED': return 'text-red-600 bg-red-100'
      case 'CANCELLED': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return '待审核'
      case 'APPROVED': return '已批准'
      case 'PROCESSING': return '处理中'
      case 'COMPLETED': return '已完成'
      case 'REJECTED': return '已拒绝'
      case 'CANCELLED': return '已取消'
      default: return status
    }
  }

  // 计算提现手续费（阶梯制）
  const calculateWithdrawalFee = (amount: number, coupon?: any) => {
    let baseFee = 0
    let rate = ''
    let description = ''

    if (amount <= 100) {
      baseFee = 0.5
      rate = 'fixed'
      description = '固定手续费'
    } else if (amount <= 500) {
      baseFee = amount * 0.005
      rate = '0.5%'
      description = '按比例收费'
    } else if (amount <= 2000) {
      baseFee = amount * 0.003
      rate = '0.3%'
      description = '按比例收费'
    } else {
      baseFee = 0
      rate = '0%'
      description = '免手续费'
    }

    // 应用减免券
    if (coupon && coupon.type === 'WITHDRAWAL_FEE_WAIVER') {
      return {
        fee: 0,
        rate: '0% (减免券)',
        description: '使用减免券免手续费',
        originalFee: baseFee,
        discount: baseFee
      }
    }

    return { fee: baseFee, rate, description, originalFee: baseFee, discount: 0 }
  }

  // 获取信誉等级显示
  const getTrustLevelDisplay = (level: number, hasTransactionData: boolean) => {
    if (!hasTransactionData) {
      return '无'
    }
    const stars = '⭐'.repeat(Math.min(level, 5))
    const emptyStars = '☆'.repeat(Math.max(0, 5 - level))
    return stars + emptyStars
  }

  // 获取履约率显示
  const getFulfillmentRateDisplay = (successfulOrders: number, totalOrders: number) => {
    if (totalOrders === 0) {
      return '暂无'
    }
    const rate = (successfulOrders / totalOrders) * 100
    return `${rate.toFixed(1)}%`
  }

  // 获取综合信任评分显示
  const getTrustScoreDisplay = (positiveReviews: number, totalOrders: number) => {
    if (totalOrders === 0) {
      return '无'
    }
    const score = (positiveReviews / totalOrders) * 100
    return `${score.toFixed(1)}`
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg text-red-600">加载失败，请刷新页面</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">保证金管理</h1>
          <p className="mt-2 text-gray-600">管理您的保证金余额，进行充值提现操作</p>
        </div>

        {/* 余额概览卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总余额</p>
                <p className="text-2xl font-bold text-gray-900">{data.balance.total.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">可用余额</p>
                <p className="text-2xl font-bold text-green-600">{data.balance.available.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>

          <Link href="/funds/frozen" className="block">
            <div className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center">
                <ShieldCheckIcon className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">冻结资金</p>
                  <p className="text-2xl font-bold text-orange-600">{data.balance.guaranteePool.toFixed(2)} USDT</p>
                </div>
              </div>
            </div>
          </Link>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">待提现</p>
                <p className="text-2xl font-bold text-purple-600">{data.balance.pendingWithdrawal.toFixed(2)} USDT</p>
              </div>
            </div>
          </div>
        </div>

        {/* 信誉等级卡片 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl mb-2">
                {getTrustLevelDisplay(data.user.trustLevel, data.user.totalOrders > 0)}
              </div>
              <p className="text-sm font-medium text-gray-700">信誉等级</p>
              <p className="text-xs text-gray-500">
                {data.user.totalOrders > 0 ? `等级 ${data.user.trustLevel}/5` : '暂无交易数据'}
              </p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {getFulfillmentRateDisplay(data.user.successfulOrders, data.user.totalOrders)}
              </div>
              <p className="text-sm font-medium text-gray-700">履约率</p>
              <p className="text-xs text-gray-500">
                {data.user.totalOrders > 0 ? `共${data.user.totalOrders}笔` : '暂无交易数据'}
              </p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 mb-2">
                {getTrustScoreDisplay(data.user.positiveReviews, data.user.totalOrders)}
              </div>
              <p className="text-sm font-medium text-gray-700">综合信任评分</p>
              <p className="text-xs text-gray-500">
                {data.user.totalOrders > 0 ? '基于用户评价' : '暂无评价数据'}
              </p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 mb-2">{data.statistics.monthlyVolume.toFixed(0)}</div>
              <p className="text-sm font-medium text-gray-700">月交易量</p>
              <p className="text-xs text-gray-500">USDT</p>
            </div>
          </div>
        </div>

        {/* 中间人状态卡片 */}
        {data.user.isGuarantor && (
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-8">
            <div className="flex items-center">
              <ShieldCheckIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">🛡️ 中间人身份</h3>
                <p className="text-gray-600">您已获得中间人资格，可以为交易提供担保服务并获得佣金收入</p>
              </div>
            </div>
          </div>
        )}

        {/* 标签页导航 */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'overview', name: '概览', icon: InformationCircleIcon },
                { id: 'deposit', name: '充值', icon: ArrowUpIcon },
                { id: 'withdraw', name: '提现', icon: ArrowDownIcon },
                { id: 'history', name: '历史记录', icon: ClockIcon },
                { id: 'giftcard', name: '礼品卡', icon: GiftIcon },
                { id: 'redemption', name: '兑换码', icon: TicketIcon },
                { id: 'guarantor', name: '中间人', icon: ShieldCheckIcon }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* 标签页内容 */}
          <div className="p-6">
            {/* 概览标签页 */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* 改革亮点展示 */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">🚀 比特市场改革亮点</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl mb-2">🆓</div>
                      <h4 className="font-medium text-gray-900">零交易手续费</h4>
                      <p className="text-sm text-gray-600">所有交易免手续费，提升交易体验</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl mb-2">🏦</div>
                      <h4 className="font-medium text-gray-900">冻结资金机制</h4>
                      <p className="text-sm text-gray-600">担保金进入冻结池，可随时管理和提现</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl mb-2">📊</div>
                      <h4 className="font-medium text-gray-900">阶梯手续费</h4>
                      <p className="text-sm text-gray-600">大额提现免手续费，鼓励集中提现</p>
                    </div>
                  </div>
                </div>

                {/* 保证金流动历史记录 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">保证金流动历史记录</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">最近交易记录</h4>
                    {data.recentTransactions.length > 0 ? (
                      <div className="space-y-2">
                        {data.recentTransactions.slice(0, 5).map((record, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-gray-600 truncate">
                              {record.type === 'PURCHASE' ? '商品购买' :
                               record.type === 'GUARANTEE' ? '担保服务' :
                               record.type === 'COMMISSION' ? '佣金收入' : record.type}
                            </span>
                            <span className={`font-medium ${record.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {record.amount > 0 ? '+' : ''}{record.amount.toFixed(2)} USDT
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">暂无交易记录</p>
                    )}
                  </div>
                </div>

                {/* 智能托管机制说明 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">智能托管机制说明</h3>
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-medium text-blue-900 mb-3">🤖 智能自动分配</h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                          <li>• 系统根据信誉度、成功率自动分配最优中间人</li>
                          <li>• 负载均衡确保订单平均分配</li>
                          <li>• 无需用户手动选择，简化操作流程</li>
                          <li>• 实时验证中间人资金充足性</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-900 mb-3">💰 灵活资金管理</h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                          <li>• 冻结资金可随时充值和提现</li>
                          <li>• 无需等待，资金使用更加灵活</li>
                          <li>• 总余额 = 可用余额 + 冻结资金</li>
                          <li>• 操作过程中总余额保持不变</li>
                        </ul>
                      </div>
                    </div>

                    <div className="mt-6 pt-6 border-t border-blue-200">
                      <h4 className="font-medium text-blue-900 mb-3">🔒 智能锁定机制</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="font-medium text-blue-800 mb-2">自动锁定</h5>
                          <ul className="text-sm text-blue-700 space-y-1">
                            <li>• 活跃担保订单自动锁定相应金额</li>
                            <li>• 确保担保资金充足可用</li>
                            <li>• 防止超额提现风险</li>
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-medium text-blue-800 mb-2">自动释放</h5>
                          <ul className="text-sm text-blue-700 space-y-1">
                            <li>• 交易完成后立即释放锁定资金</li>
                            <li>• 全程自动化管理，无需人工干预</li>
                            <li>• 提升资金使用效率</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 pt-6 border-t border-blue-200">
                      <div className="flex items-center justify-center space-x-2 text-sm text-blue-700">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>点击上方"冻结资金"卡片可进入详细管理页面进行充值和提现操作</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 充值标签页 */}
            {activeTab === 'deposit' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">保证金充值</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* 充值表单 */}
                    <div>
                      <form onSubmit={handleDeposit} className="space-y-4">
                        {/* 支付方式选择 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3">
                            选择支付方式
                          </label>
                          <div className="grid grid-cols-2 gap-3">
                            <button
                              type="button"
                              onClick={() => setDepositMethod('chain')}
                              className={`p-3 border rounded-lg text-center transition-colors deposit-payment-method ${
                                depositMethod === 'chain'
                                  ? 'border-blue-500 bg-blue-50 selected-chain'
                                  : 'border-gray-300 hover:border-gray-400'
                              }`}
                            >
                              <div className="text-lg mb-1">⛓️</div>
                              <div className="text-sm font-medium payment-method-title">链上转账</div>
                              <div className="text-xs payment-method-description">USDT-TRC20</div>
                            </button>

                            <button
                              type="button"
                              onClick={() => setDepositMethod('binance_qr')}
                              className={`p-3 border rounded-lg text-center transition-colors deposit-payment-method ${
                                depositMethod === 'binance_qr'
                                  ? 'border-yellow-500 bg-yellow-50 selected-binance'
                                  : 'border-gray-300 hover:border-gray-400'
                              }`}
                            >
                              <div className="text-lg mb-1">🟡</div>
                              <div className="text-sm font-medium payment-method-title">币安扫码</div>
                              <div className="text-xs payment-method-description">扫码支付</div>
                            </button>


                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            充值金额 (USDT)
                          </label>
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={depositAmount}
                            onChange={(e) => setDepositAmount(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入充值金额 (USDT)"
                            required
                          />
                        </div>

                        {/* 交易哈希在PIN码验证时提供 */}

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            备注信息 (可选)
                          </label>
                          <textarea
                            value={depositNotes}
                            onChange={(e) => setDepositNotes(e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入备注信息"
                          />
                        </div>

                        <button
                          type="submit"
                          disabled={depositLoading}
                          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                          {depositLoading ? '处理中...' :
                           depositMethod === 'binance_qr' ? '获取币安支付二维码' :
                           depositMethod === 'chain' ? '获取钱包地址和PIN码' : '确认充值'}
                        </button>
                      </form>
                    </div>

                    {/* 充值说明 */}
                    <div>
                      <div className="bg-green-50 rounded-lg p-4 mb-4">
                        <div className="flex">
                          <CheckCircleIcon className="h-5 w-5 text-green-400 mt-0.5" />
                          <div className="ml-3">
                            <h4 className="text-sm font-medium text-green-800">安全支付方式</h4>
                            <div className="mt-2 text-sm text-green-700">
                              <p>支持链上支付和币安扫码支付，采用PIN码验证确保安全。</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 支付方式详细说明 */}
                      <div className="space-y-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                            💰 链上支付
                          </h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            <li>• 支持USDT-TRC20网络</li>
                            <li>• 使用PIN码验证交易</li>
                            <li>• 需要提供交易哈希</li>
                            <li>• 手续费低，到账快</li>
                          </ul>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                            🟡 币安扫码支付
                          </h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            <li>• 使用币安APP扫描二维码</li>
                            <li>• 支持多种币种支付</li>
                            <li>• 使用PIN码验证订单</li>
                            <li>• 便捷安全，适合币安用户</li>
                          </ul>
                        </div>

                        <div className="bg-yellow-50 rounded-lg p-4">
                          <h4 className="font-medium text-yellow-800 mb-3 flex items-center">
                            🔐 PIN码验证系统
                          </h4>
                          <ul className="text-sm text-yellow-700 space-y-1">
                            <li>• 每笔充值生成唯一PIN码</li>
                            <li>• 有效期30分钟</li>
                            <li>• 支付后输入PIN码和交易号</li>
                            <li>• 验证成功后立即到账</li>
                          </ul>
                        </div>
                      </div>

                      <div className="bg-blue-50 rounded-lg p-4 mt-4">
                        <h4 className="font-medium text-blue-900 mb-3">充值优势</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <h5 className="text-sm font-medium text-blue-800">🆓 零手续费交易</h5>
                            <p className="text-xs text-blue-700">充值后所有交易免手续费</p>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-blue-800">⚡ 即时到账</h5>
                            <p className="text-xs text-blue-700">确认后立即可用于交易</p>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-blue-800">🏦 担保池机制</h5>
                            <p className="text-xs text-blue-700">智能担保，提升交易安全</p>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-blue-800">📊 信誉积累</h5>
                            <p className="text-xs text-blue-700">交易越多信誉等级越高</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 历史记录标签页 */}
            {activeTab === 'history' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">充值提现历史</h3>

                  {/* 充值记录 */}
                  <div className="bg-white rounded-lg border border-gray-200 mb-6">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <h4 className="text-md font-medium text-gray-900">充值记录</h4>
                    </div>
                    <div className="p-6">
                      <div className="space-y-4">
                        {depositRecords.length > 0 ? (
                          depositRecords.map((record) => (
                            <div key={record.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <span className="font-medium text-gray-900">
                                      {record.method === 'chain' ? '链上转账' : '币安支付'}
                                    </span>
                                    <span className={`px-2 py-1 text-xs rounded-full ${
                                      record.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                      record.status === 'PENDING_APPROVAL' ? 'bg-orange-100 text-orange-800' :
                                      record.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }`}>
                                      {record.status === 'COMPLETED' ? '已完成' :
                                       record.status === 'PENDING_APPROVAL' ? '等待确认' :
                                       record.status === 'PENDING' ? '待验证' : '已拒绝'}
                                    </span>
                                  </div>
                                  <div className="text-sm text-gray-600 space-y-1">
                                    <p>金额: {record.amount} USDT</p>
                                    {record.txHash && (
                                      <p>交易凭证: {record.txHash.substring(0, 20)}...</p>
                                    )}
                                    {record.notes && (
                                      <p>备注: {record.notes}</p>
                                    )}
                                    <p>申请时间: {new Date(record.createdAt).toLocaleString()}</p>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-lg font-semibold text-gray-900">
                                    +{record.amount} USDT
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <ClockIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>暂无充值记录</p>
                            <p className="text-sm">您的充值申请将在此显示</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 提现记录 */}
                  <div className="bg-white rounded-lg border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <h4 className="text-md font-medium text-gray-900">提现记录</h4>
                    </div>
                    <div className="p-6">
                      <div className="space-y-4">
                        {withdrawals.length > 0 ? (
                          withdrawals.map((withdrawal) => (
                            <div key={withdrawal.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                              <div className="flex items-center space-x-4">
                                <div className="flex-shrink-0">
                                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                    <ArrowUpIcon className="w-5 h-5 text-red-600" />
                                  </div>
                                </div>
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-gray-900">链上转账</span>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(withdrawal.status)}`}>
                                      {getStatusText(withdrawal.status)}
                                    </span>
                                  </div>
                                  <div className="text-sm text-gray-500 space-y-1">
                                    <p>金额: {withdrawal.amount} USDT</p>
                                    {withdrawal.txHash && (
                                      <div>
                                        <p className="font-mono break-all">
                                          交易哈希: {showSensitiveInfo[withdrawal.id] ? withdrawal.txHash : '••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••'}
                                        </p>
                                      </div>
                                    )}
                                    <div>
                                      <p className="font-mono break-all">
                                        钱包地址: {showSensitiveInfo[withdrawal.id] ? withdrawal.walletAddress : withdrawal.walletAddress.slice(0, 6) + '••••••••••••••••••••••••••••••••••••' + withdrawal.walletAddress.slice(-4)}
                                      </p>
                                    </div>
                                    <p>申请时间: {new Date(withdrawal.createdAt).toLocaleString()}</p>
                                    <button
                                      onClick={() => toggleSensitiveInfo(withdrawal.id)}
                                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                                    >
                                      {showSensitiveInfo[withdrawal.id] ? (
                                        <>
                                          <EyeSlashIcon className="w-3 h-3 mr-1" />
                                          隐藏详情
                                        </>
                                      ) : (
                                        <>
                                          <EyeIcon className="w-3 h-3 mr-1" />
                                          显示详情
                                        </>
                                      )}
                                    </button>
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-lg font-semibold text-red-600">-{withdrawal.amount} USDT</div>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <ClockIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>暂无提现记录</p>
                            <p className="text-sm">您的提现申请将在此显示</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 提现标签页 */}
            {activeTab === 'withdraw' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">保证金提现</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* 提现表单 */}
                    <div>
                      <form onSubmit={handleWithdraw} className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            提现金额 (USDT)
                          </label>
                          <input
                            type="number"
                            step="0.01"
                            min="10"
                            max={data.balance.available}
                            value={withdrawAmount}
                            onChange={(e) => setWithdrawAmount(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="最低提现金额：10 USDT"
                            required
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            可用余额：{data.balance.available.toFixed(2)} USDT
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            钱包地址
                          </label>
                          <input
                            type="text"
                            value={withdrawWallet}
                            onChange={(e) => setWithdrawWallet(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入USDT钱包地址"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            备注信息 (可选)
                          </label>
                          <textarea
                            value={withdrawNotes}
                            onChange={(e) => setWithdrawNotes(e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入备注信息"
                          />
                        </div>

                        {/* 减免券选择器 */}
                        <CouponSelector
                          context="withdrawal"
                          onCouponSelect={setSelectedCoupon}
                          selectedCouponId={selectedCoupon?.id}
                        />

                        {withdrawAmount && parseFloat(withdrawAmount) > 0 && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="text-sm space-y-1">
                              <div className="flex justify-between">
                                <span className="text-gray-600">提现金额：</span>
                                <span>{parseFloat(withdrawAmount).toFixed(2)} USDT</span>
                              </div>
                              {(() => {
                                const feeInfo = calculateWithdrawalFee(parseFloat(withdrawAmount), selectedCoupon)
                                return (
                                  <>
                                    {selectedCoupon && feeInfo.originalFee > 0 && (
                                      <div className="flex justify-between text-gray-400 line-through">
                                        <span>原手续费：</span>
                                        <span>{feeInfo.originalFee.toFixed(2)} USDT</span>
                                      </div>
                                    )}
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        手续费 ({feeInfo.rate})：
                                      </span>
                                      <span className={feeInfo.fee === 0 ? 'text-green-600 font-medium' : ''}>
                                        {feeInfo.fee.toFixed(2)} USDT
                                        {selectedCoupon && feeInfo.discount > 0 && (
                                          <span className="text-green-600 text-xs ml-1">
                                            (省 {feeInfo.discount.toFixed(2)})
                                          </span>
                                        )}
                                      </span>
                                    </div>
                                  </>
                                )
                              })()}
                              <div className="flex justify-between">
                                <span className="text-gray-600">担保池扣除：</span>
                                <span>{Math.min(data.balance.guaranteePool, parseFloat(withdrawAmount) * 0.001).toFixed(2)} USDT</span>
                              </div>
                              <div className="flex justify-between font-medium border-t pt-1">
                                <span>实际到账：</span>
                                <span className="text-green-600">
                                  {(() => {
                                    const feeInfo = calculateWithdrawalFee(parseFloat(withdrawAmount), selectedCoupon)
                                    return (parseFloat(withdrawAmount) - feeInfo.fee - Math.min(data.balance.guaranteePool, parseFloat(withdrawAmount) * 0.001)).toFixed(2)
                                  })()} USDT
                                </span>
                              </div>
                              <div className="text-xs text-gray-500 mt-2">
                                {calculateWithdrawalFee(parseFloat(withdrawAmount)).description}
                                {parseFloat(withdrawAmount) > 2000 && ' 🎉 大额提现免手续费！'}
                              </div>
                            </div>
                          </div>
                        )}

                        <button
                          type="submit"
                          disabled={withdrawLoading || !data.balance.available || data.balance.available < 10}
                          className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                        >
                          {withdrawLoading ? '提交中...' : '申请提现'}
                        </button>
                      </form>
                    </div>

                    {/* 提现说明 */}
                    <div>
                      <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4 mb-4">
                        <div className="flex">
                          <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                          <div className="ml-3">
                            <h4 className="text-sm font-medium text-blue-800">阶梯手续费优势</h4>
                            <div className="mt-2 text-sm text-blue-700">
                              <p>大额提现更优惠，鼓励集中提现，降低平台成本！</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 阶梯手续费表格 */}
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4">
                        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                          <h4 className="font-medium text-gray-900">📊 阶梯手续费标准</h4>
                        </div>
                        <div className="divide-y divide-gray-200">
                          <div className="px-4 py-3 flex justify-between items-center">
                            <span className="text-sm text-gray-600">≤ 100 USDT</span>
                            <span className="text-sm font-medium text-orange-600">固定 0.5 USDT</span>
                          </div>
                          <div className="px-4 py-3 flex justify-between items-center">
                            <span className="text-sm text-gray-600">101 - 500 USDT</span>
                            <span className="text-sm font-medium text-blue-600">0.5% 手续费</span>
                          </div>
                          <div className="px-4 py-3 flex justify-between items-center">
                            <span className="text-sm text-gray-600">501 - 2000 USDT</span>
                            <span className="text-sm font-medium text-green-600">0.3% 手续费</span>
                          </div>
                          <div className="px-4 py-3 flex justify-between items-center bg-green-50">
                            <span className="text-sm text-gray-600">{'>'}2000 USDT</span>
                            <span className="text-sm font-bold text-green-600">🎉 免手续费</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">提现流程</h4>
                        <ol className="text-sm text-gray-600 space-y-2">
                          <li className="flex items-start">
                            <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">1</span>
                            填写提现金额和钱包地址
                          </li>
                          <li className="flex items-start">
                            <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">2</span>
                            系统计算阶梯手续费和担保池扣除
                          </li>
                          <li className="flex items-start">
                            <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">3</span>
                            统一时间批量处理 (每日12:00和20:00)
                          </li>
                          <li className="flex items-start">
                            <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">4</span>
                            审核通过后转账到钱包
                          </li>
                        </ol>
                      </div>

                      <div className="bg-yellow-50 rounded-lg p-4 mt-4">
                        <h4 className="font-medium text-yellow-800 mb-2">💡 省钱小贴士</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• 集中提现更省钱：2000+ USDT免手续费</li>
                          <li>• 担保池余额会在提现时自动结算</li>
                          <li>• 建议在交易完成后统一提现</li>
                          <li>• 大额用户享受VIP待遇</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 提现记录 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">提现记录</h3>
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    {withdrawals.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                申请时间
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                提现金额
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                手续费
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                实际到账
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                钱包地址
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {withdrawals.map((withdrawal) => (
                              <tr key={withdrawal.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {new Date(withdrawal.createdAt).toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {withdrawal.amount.toFixed(2)} USDT
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {withdrawal.withdrawalFee.toFixed(2)} USDT
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {withdrawal.actualAmount.toFixed(2)} USDT
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(withdrawal.status)}`}>
                                    {getStatusText(withdrawal.status)}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  <span className="font-mono">
                                    {withdrawal.walletAddress.slice(0, 6)}...{withdrawal.walletAddress.slice(-4)}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500">暂无提现记录</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 礼品卡标签页 */}
            {activeTab === 'giftcard' && (
              <div className="space-y-6">
                <GiftCardExchange
                  userBalance={{
                    available: data.balance.available,
                    total: data.balance.total
                  }}
                  onExchange={(giftCard) => {
                    alert(`成功购买 ${giftCard.name}！`)
                    // 刷新页面数据
                    fetchData()
                    fetchDepositRecords()
                  }}
                />
              </div>
            )}

            {/* 兑换码标签页 */}
            {activeTab === 'redemption' && (
              <div className="space-y-6">
                <RedemptionCodeExchange
                  userBalance={{
                    available: data.balance.available,
                    total: data.balance.total
                  }}
                  onExchange={(result) => {
                    // 刷新数据
                    fetchData()
                  }}
                />
              </div>
            )}

            {/* 中间人标签页 */}
            {activeTab === 'guarantor' && (
              <div className="space-y-6">
                {data.user.isGuarantor ? (
                  /* 已是中间人 */
                  <div>
                    <div className="bg-green-50 rounded-lg p-6 mb-6">
                      <div className="flex items-center">
                        <ShieldCheckIcon className="h-8 w-8 text-green-600" />
                        <div className="ml-4">
                          <h3 className="text-lg font-semibold text-green-900">🎉 恭喜！您已是认证中间人</h3>
                          <p className="text-green-700">您可以为交易提供担保服务并获得佣金收入</p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h4 className="font-semibold text-gray-900 mb-4">中间人权益</h4>
                        <ul className="space-y-2 text-sm text-gray-600">
                          <li className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                            为交易提供担保服务
                          </li>
                          <li className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                            获得担保服务佣金
                          </li>
                          <li className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                            提升平台信誉等级
                          </li>
                          <li className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                            享受优先客服支持
                          </li>
                        </ul>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h4 className="font-semibold text-gray-900 mb-4">担保服务统计</h4>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">担保次数：</span>
                            <span className="font-medium">0 次</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">成功率：</span>
                            <span className="font-medium">100%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">累计佣金：</span>
                            <span className="font-medium">0.00 USDT</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">信誉评分：</span>
                            <span className="font-medium">{data.user.creditScore}/100</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* 申请成为中间人 */
                  <div>
                    <div className="bg-blue-50 rounded-lg p-6 mb-6">
                      <div className="flex items-center">
                        <ShieldCheckIcon className="h-8 w-8 text-blue-600" />
                        <div className="ml-4">
                          <h3 className="text-lg font-semibold text-blue-900">申请成为中间人</h3>
                          <p className="text-blue-700">成为中间人，为交易提供担保服务并获得佣金收入</p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      {/* 申请条件 */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-4">申请条件</h4>
                        <div className="space-y-4">
                          <div className="flex items-start">
                            <div className={`w-5 h-5 rounded-full flex items-center justify-center mt-0.5 mr-3 ${
                              data.user.creditScore >= 80 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                            }`}>
                              {data.user.creditScore >= 80 ? '✓' : '✗'}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">信用分数不低于80分</p>
                              <p className="text-sm text-gray-600">当前分数：{data.user.creditScore}/1000</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <div className={`w-5 h-5 rounded-full flex items-center justify-center mt-0.5 mr-3 ${
                              data.balance.total >= 50 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                            }`}>
                              {data.balance.total >= 50 ? '✓' : '✗'}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">保证金不少于50 USDT</p>
                              <p className="text-sm text-gray-600">当前余额：{data.balance.total.toFixed(2)} USDT</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <div className="w-5 h-5 rounded-full flex items-center justify-center mt-0.5 mr-3 bg-green-100 text-green-600">
                              ✓
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">账户状态正常</p>
                              <p className="text-sm text-gray-600">无违规记录</p>
                            </div>
                          </div>
                        </div>

                        {data.user.creditScore >= 80 && data.balance.total >= 50 ? (
                          <form onSubmit={handleGuarantorApply} className="mt-6 space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                申请理由 *
                              </label>
                              <textarea
                                value={guarantorReason}
                                onChange={(e) => setGuarantorReason(e.target.value)}
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="请说明您申请成为中间人的理由..."
                                required
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                相关经验 (可选)
                              </label>
                              <textarea
                                value={guarantorExperience}
                                onChange={(e) => setGuarantorExperience(e.target.value)}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="请描述您的相关经验或优势..."
                              />
                            </div>

                            <button
                              type="submit"
                              disabled={guarantorLoading}
                              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                            >
                              {guarantorLoading ? '提交中...' : '申请成为中间人'}
                            </button>
                          </form>
                        ) : (
                          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
                            <p className="text-sm text-yellow-800">
                              {data.user.creditScore < 80 && '请先提升信用分数至80分以上。'}
                              {data.balance.total < 50 && '请先充值保证金至50 USDT以上。'}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* 中间人介绍 */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-4">中间人服务介绍</h4>
                        <div className="space-y-4">
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h5 className="font-medium text-gray-900 mb-2">🛡️ 担保服务</h5>
                            <p className="text-sm text-gray-600">
                              为买卖双方提供第三方担保，确保交易资金安全，降低交易风险。
                            </p>
                          </div>

                          <div className="bg-gray-50 rounded-lg p-4">
                            <h5 className="font-medium text-gray-900 mb-2">💰 佣金收入</h5>
                            <p className="text-sm text-gray-600">
                              每笔担保交易可获得0.5%-2%的佣金收入，根据信誉等级浮动。
                            </p>
                          </div>

                          <div className="bg-gray-50 rounded-lg p-4">
                            <h5 className="font-medium text-gray-900 mb-2">⭐ 信誉提升</h5>
                            <p className="text-sm text-gray-600">
                              成功完成担保服务可提升信誉等级，享受更多平台特权。
                            </p>
                          </div>

                          <div className="bg-gray-50 rounded-lg p-4">
                            <h5 className="font-medium text-gray-900 mb-2">🔒 风险控制</h5>
                            <p className="text-sm text-gray-600">
                              平台提供完善的风险控制机制，保障中间人权益。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 中间人规则说明 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">中间人规则说明</h3>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">📋 服务规则</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• 担保金额不能超过保证金余额</li>
                          <li>• 担保期间相应金额将被冻结</li>
                          <li>• 交易完成后自动解冻并发放佣金</li>
                          <li>• 发生纠纷时需要协助处理</li>
                          <li>• 恶意担保将被取消资格</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">💸 佣金标准</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• 信用分80-89：0.5%佣金</li>
                          <li>• 信用分90-94：1.0%佣金</li>
                          <li>• 信用分95-99：1.5%佣金</li>
                          <li>• 信用分100：2.0%佣金</li>
                          <li>• 佣金实时结算到保证金账户</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* PIN码验证模态框 */}
      {showPinModal && depositInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                支付验证
              </h3>
              <button
                onClick={() => setShowPinModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* PIN码信息 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-start">
                  <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-blue-800">PIN码验证</h4>
                    <p className="mt-1 text-sm text-blue-700">
                      请使用以下PIN码完成支付验证，有效期30分钟
                    </p>
                    <div className="mt-2 bg-white p-3 rounded border border-blue-200">
                      <span className="font-mono text-lg font-bold tracking-wider text-blue-800">
                        {depositInfo.paymentPin}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 支付方式说明 */}
              {depositInfo.method === 'chain' && (
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-green-800 mb-2">链上支付说明</h4>
                  <p className="text-sm text-green-700 mb-2">
                    请向以下地址转账 {depositInfo.amount} USDT (TRC20网络)
                  </p>
                  <div className="bg-white p-2 rounded border border-green-200 break-all font-mono text-xs">
                    {depositInfo.walletAddress}
                  </div>
                  <p className="text-xs text-green-600 mt-2">
                    完成转账后，请输入交易哈希和PIN码进行验证
                  </p>
                </div>
              )}

              {depositInfo.method === 'binance_qr' && (
                <div className="bg-yellow-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-yellow-800 mb-2">币安扫码支付</h4>
                  <div className="flex justify-center mb-3">
                    <img
                      src="/binance-qr-code.png"
                      alt="币安支付二维码"
                      className="w-48 h-48 border border-yellow-200 rounded"
                    />
                  </div>
                  <p className="text-xs text-yellow-600 text-center">
                    请使用币安APP扫描上方二维码完成支付
                  </p>
                </div>
              )}

              {/* 验证表单 */}
              <div className="space-y-3">


                {depositInfo.method === 'chain' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      交易哈希
                    </label>
                    <input
                      type="text"
                      value={txHash}
                      onChange={(e) => setTxHash(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入交易哈希"
                    />
                  </div>
                )}

                {depositInfo.method === 'binance_qr' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      币安订单号
                    </label>
                    <input
                      type="text"
                      value={orderNumber}
                      onChange={(e) => setOrderNumber(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入币安订单号"
                    />
                  </div>
                )}

                <button
                  type="button"
                  onClick={handlePinVerification}
                  disabled={pinLoading}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {pinLoading ? '验证中...' : '确认支付'}
                </button>
              </div>

              <div className="text-xs text-gray-500 mt-2">
                <p>• PIN码有效期至: {new Date(depositInfo.paymentPinExpiry).toLocaleString()}</p>
                <p>• 请确保在有效期内完成支付和确认</p>
                <p>• 系统将自动使用上方显示的PIN码进行验证</p>
                <p>• 确认成功后余额将立即更新</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
