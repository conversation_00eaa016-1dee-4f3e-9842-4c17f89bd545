'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Navbar from '@/components/Navbar'
import {
  ShieldCheckIcon,
  KeyIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

interface SecurityData {
  user: {
    id: string
    email: string
    binanceUid: string | null
    emailVerified: boolean
  }
  securityLevel: {
    level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXCELLENT'
    score: number
    description: string
  }
  stats: {
    totalLogs: number
    recentLogs: number
    loginAttempts: number
    successfulLogins: number
    failedLogins: number
    passwordChanges: number
    emailChanges: number
    recentIPs: string[]
    loginSuccessRate: number
  }
  recommendations: string[]
}

export default function SecuritySettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [securityData, setSecurityData] = useState<SecurityData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  // 检查登录状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // 获取安全数据
  const fetchSecurityData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/user/security/overview')
      if (response.ok) {
        const data = await response.json()
        setSecurityData(data)
      } else {
        console.error('获取安全数据失败')
      }
    } catch (error) {
      console.error('获取安全数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 页面加载时获取数据
  useEffect(() => {
    if (session?.user?.id) {
      fetchSecurityData()
    }
  }, [session])

  // 获取安全等级颜色
  const getSecurityLevelColor = (level: string) => {
    switch (level) {
      case 'EXCELLENT':
        return 'text-green-600 bg-green-100'
      case 'HIGH':
        return 'text-blue-600 bg-blue-100'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100'
      case 'LOW':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  // 获取安全等级图标
  const getSecurityLevelIcon = (level: string) => {
    switch (level) {
      case 'EXCELLENT':
        return <CheckCircleIcon className="h-6 w-6 text-green-600" />
      case 'HIGH':
        return <ShieldCheckIcon className="h-6 w-6 text-blue-600" />
      case 'MEDIUM':
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
      case 'LOW':
        return <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
      default:
        return <ShieldCheckIcon className="h-6 w-6 text-gray-600" />
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题 */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">安全设置</h1>
            <p className="text-gray-600 mt-1">管理您的账户安全和隐私设置</p>
          </div>

          {/* 标签页导航 */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: '安全概览', icon: ShieldCheckIcon },
                { id: 'password', name: '密码管理', icon: KeyIcon },
                { id: 'account', name: '账户绑定', icon: EnvelopeIcon },
                { id: 'sessions', name: '登录设备', icon: DevicePhoneMobileIcon },
                { id: 'logs', name: '安全日志', icon: ClockIcon }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* 标签页内容 */}
          {isLoading ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 加载骨架屏 */}
              {[...Array(4)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow animate-pulse">
                  <div className="p-6 space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : securityData ? (
            <div>
              {activeTab === 'overview' && (
                <SecurityOverview
                  securityData={securityData}
                  getSecurityLevelColor={getSecurityLevelColor}
                  getSecurityLevelIcon={getSecurityLevelIcon}
                  setActiveTab={setActiveTab}
                />
              )}
              {activeTab === 'password' && (
                <PasswordManagement onUpdate={fetchSecurityData} />
              )}
              {activeTab === 'account' && (
                <AccountBinding 
                  user={securityData.user}
                  onUpdate={fetchSecurityData}
                />
              )}
              {activeTab === 'sessions' && (
                <SessionManagement />
              )}
              {activeTab === 'logs' && (
                <SecurityLogs />
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500">获取安全数据失败，请刷新页面重试</div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

// 安全概览组件
function SecurityOverview({
  securityData,
  getSecurityLevelColor,
  getSecurityLevelIcon,
  setActiveTab
}: {
  securityData: SecurityData
  getSecurityLevelColor: (level: string) => string
  getSecurityLevelIcon: (level: string) => React.JSX.Element
  setActiveTab: (tab: string) => void
}) {
  return (
    <div className="space-y-6">
      {/* 安全等级卡片 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">安全等级</h3>
          {getSecurityLevelIcon(securityData.securityLevel.level)}
        </div>

        <div className="flex items-center space-x-4 mb-4">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getSecurityLevelColor(securityData.securityLevel.level)}`}>
            {securityData.securityLevel.level}
          </span>
          <span className="text-2xl font-bold text-gray-900">
            {securityData.securityLevel.score}/100
          </span>
        </div>

        <p className="text-gray-600 mb-4">{securityData.securityLevel.description}</p>

        {/* 安全分数进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              securityData.securityLevel.level === 'EXCELLENT' ? 'bg-green-500' :
              securityData.securityLevel.level === 'HIGH' ? 'bg-blue-500' :
              securityData.securityLevel.level === 'MEDIUM' ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${securityData.securityLevel.score}%` }}
          ></div>
        </div>
      </div>

      {/* 安全统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-blue-600">{securityData.stats.successfulLogins}</div>
          <div className="text-sm text-gray-600">登录次数</div>
          <div className="text-xs text-gray-500">最近30天</div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-green-600">{securityData.stats.loginSuccessRate}%</div>
          <div className="text-sm text-gray-600">登录成功率</div>
          <div className="text-xs text-gray-500">最近30天</div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-purple-600">{securityData.stats.passwordChanges}</div>
          <div className="text-sm text-gray-600">密码修改</div>
          <div className="text-xs text-gray-500">最近30天</div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-orange-600">{securityData.stats.recentIPs.length}</div>
          <div className="text-sm text-gray-600">登录IP</div>
          <div className="text-xs text-gray-500">最近使用</div>
        </div>
      </div>

      {/* 安全建议 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">安全建议</h3>
        <div className="space-y-3">
          {securityData.recommendations.map((recommendation, index) => {
            // 检查是否是需要添加链接的建议
            const isPasswordRecommendation = recommendation.includes('建议定期更换密码以提高账户安全性')
            const isBinanceRecommendation = recommendation.includes('建议绑定币安UID以提高交易安全性')

            return (
              <div key={index} className="flex items-start space-x-3">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div className="text-gray-700">
                  {isPasswordRecommendation ? (
                    <span>
                      建议定期更换密码以提高账户安全性
                      <button
                        onClick={() => setActiveTab('password')}
                        className="ml-2 text-blue-600 hover:text-blue-800 underline text-sm"
                      >
                        立即修改
                      </button>
                    </span>
                  ) : isBinanceRecommendation ? (
                    <span>
                      建议绑定币安UID以提高交易安全性
                      <button
                        onClick={() => setActiveTab('account')}
                        className="ml-2 text-blue-600 hover:text-blue-800 underline text-sm"
                      >
                        立即绑定
                      </button>
                    </span>
                  ) : (
                    <span>{recommendation}</span>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// 密码管理组件
function PasswordManagement({ onUpdate }: { onUpdate: () => void }) {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    feedback: [] as string[],
    isStrong: false
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  // 检查密码强度
  const checkPasswordStrength = (password: string) => {
    const feedback: string[] = []
    let score = 0

    if (password.length >= 8) score += 1
    else feedback.push('至少8个字符')

    if (/[a-z]/.test(password)) score += 1
    else feedback.push('包含小写字母')

    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('包含大写字母')

    if (/\d/.test(password)) score += 1
    else feedback.push('包含数字')

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1

    const isStrong = score >= 4 && password.length >= 8

    setPasswordStrength({ score, feedback, isStrong })
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    if (name === 'newPassword') {
      checkPasswordStrength(value)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')

    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      setError('请填写所有字段')
      return
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError('新密码和确认密码不匹配')
      return
    }

    if (!passwordStrength.isStrong) {
      setError('新密码强度不够')
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/user/security/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword
        })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage('密码修改成功')
        setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' })
        setPasswordStrength({ score: 0, feedback: [], isStrong: false })
        onUpdate()
      } else {
        setError(data.error || '密码修改失败')
      }
    } catch (error) {
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">修改密码</h3>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 当前密码 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前密码
          </label>
          <div className="relative">
            <input
              type={showPasswords.current ? 'text' : 'password'}
              name="currentPassword"
              value={formData.currentPassword}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入当前密码"
            />
            <button
              type="button"
              onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPasswords.current ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* 新密码 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            新密码
          </label>
          <div className="relative">
            <input
              type={showPasswords.new ? 'text' : 'password'}
              name="newPassword"
              value={formData.newPassword}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入新密码"
            />
            <button
              type="button"
              onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPasswords.new ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>

          {/* 密码强度指示器 */}
          {formData.newPassword && (
            <div className="mt-2">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-sm text-gray-600">密码强度:</span>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((level) => (
                    <div
                      key={level}
                      className={`h-2 w-4 rounded ${
                        level <= passwordStrength.score
                          ? passwordStrength.score <= 2 ? 'bg-red-500'
                          : passwordStrength.score <= 3 ? 'bg-yellow-500'
                          : 'bg-green-500'
                          : 'bg-gray-200'
                      }`}
                    />
                  ))}
                </div>
              </div>
              {passwordStrength.feedback.length > 0 && (
                <ul className="text-xs text-gray-600 space-y-1">
                  {passwordStrength.feedback.map((item, index) => (
                    <li key={index}>• {item}</li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </div>

        {/* 确认密码 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            确认新密码
          </label>
          <div className="relative">
            <input
              type={showPasswords.confirm ? 'text' : 'password'}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请再次输入新密码"
            />
            <button
              type="button"
              onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPasswords.confirm ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* 错误和成功消息 */}
        {error && (
          <div className="text-red-600 text-sm">{error}</div>
        )}
        {message && (
          <div className="text-green-600 text-sm">{message}</div>
        )}

        {/* 提交按钮 */}
        <button
          type="submit"
          disabled={isLoading || !passwordStrength.isStrong}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium transition-colors"
        >
          {isLoading ? '修改中...' : '修改密码'}
        </button>
      </form>
    </div>
  )
}

// 账户绑定组件
function AccountBinding({
  user,
  onUpdate
}: {
  user: SecurityData['user']
  onUpdate: () => void
}) {
  const [emailForm, setEmailForm] = useState({
    newEmail: '',
    password: ''
  })
  const [binanceForm, setBinanceForm] = useState({
    binanceUid: '',
    password: '',
    action: 'bind' as 'bind' | 'unbind' | 'update'
  })
  const [isLoading, setIsLoading] = useState({
    email: false,
    binance: false
  })
  const [messages, setMessages] = useState({
    email: '',
    binance: ''
  })
  const [errors, setErrors] = useState({
    email: '',
    binance: ''
  })

  // 处理邮箱修改
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors(prev => ({ ...prev, email: '' }))
    setMessages(prev => ({ ...prev, email: '' }))

    if (!emailForm.newEmail || !emailForm.password) {
      setErrors(prev => ({ ...prev, email: '请填写所有字段' }))
      return
    }

    setIsLoading(prev => ({ ...prev, email: true }))

    try {
      const response = await fetch('/api/user/security/email', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(emailForm)
      })

      const data = await response.json()

      if (response.ok) {
        setMessages(prev => ({ ...prev, email: '邮箱修改成功' }))
        setEmailForm({ newEmail: '', password: '' })
        onUpdate()
      } else {
        setErrors(prev => ({ ...prev, email: data.error || '邮箱修改失败' }))
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, email: '网络错误，请稍后重试' }))
    } finally {
      setIsLoading(prev => ({ ...prev, email: false }))
    }
  }

  // 处理币安UID操作
  const handleBinanceSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors(prev => ({ ...prev, binance: '' }))
    setMessages(prev => ({ ...prev, binance: '' }))

    if (!binanceForm.password) {
      setErrors(prev => ({ ...prev, binance: '请输入密码' }))
      return
    }

    if ((binanceForm.action === 'bind' || binanceForm.action === 'update') && !binanceForm.binanceUid) {
      setErrors(prev => ({ ...prev, binance: '请输入币安UID' }))
      return
    }

    setIsLoading(prev => ({ ...prev, binance: true }))

    try {
      const response = await fetch('/api/user/security/binance', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(binanceForm)
      })

      const data = await response.json()

      if (response.ok) {
        setMessages(prev => ({ ...prev, binance: data.message }))
        setBinanceForm({ binanceUid: '', password: '', action: 'bind' })
        onUpdate()
      } else {
        setErrors(prev => ({ ...prev, binance: data.error || '操作失败' }))
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, binance: '网络错误，请稍后重试' }))
    } finally {
      setIsLoading(prev => ({ ...prev, binance: false }))
    }
  }

  return (
    <div className="space-y-6">
      {/* 邮箱管理 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">邮箱管理</h3>

        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-gray-600">当前邮箱:</span>
              <span className="ml-2 font-medium">{user.email}</span>
            </div>
            <div className="flex items-center space-x-2">
              {user.emailVerified ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  已验证
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  未验证
                </span>
              )}
            </div>
          </div>
        </div>

        <form onSubmit={handleEmailSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              新邮箱地址
            </label>
            <input
              type="email"
              value={emailForm.newEmail}
              onChange={(e) => setEmailForm(prev => ({ ...prev, newEmail: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入新的邮箱地址"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              确认密码
            </label>
            <input
              type="password"
              value={emailForm.password}
              onChange={(e) => setEmailForm(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入当前密码"
            />
          </div>

          {errors.email && (
            <div className="text-red-600 text-sm">{errors.email}</div>
          )}
          {messages.email && (
            <div className="text-green-600 text-sm">{messages.email}</div>
          )}

          <button
            type="submit"
            disabled={isLoading.email}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium transition-colors"
          >
            {isLoading.email ? '修改中...' : '修改邮箱'}
          </button>
        </form>
      </div>

      {/* 币安UID管理 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">币安UID管理</h3>

        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-gray-600">当前币安UID:</span>
              <span className="ml-2 font-medium">
                {user.binanceUid || '未绑定'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {user.binanceUid ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  已绑定
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  未绑定
                </span>
              )}
            </div>
          </div>
        </div>

        <form onSubmit={handleBinanceSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              操作类型
            </label>
            <select
              value={binanceForm.action}
              onChange={(e) => setBinanceForm(prev => ({ ...prev, action: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="bind">绑定币安UID</option>
              {user.binanceUid && (
                <>
                  <option value="update">更新币安UID</option>
                  <option value="unbind">解绑币安UID</option>
                </>
              )}
            </select>
          </div>

          {(binanceForm.action === 'bind' || binanceForm.action === 'update') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                币安UID
              </label>
              <input
                type="text"
                value={binanceForm.binanceUid}
                onChange={(e) => setBinanceForm(prev => ({ ...prev, binanceUid: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入币安UID"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              确认密码
            </label>
            <input
              type="password"
              value={binanceForm.password}
              onChange={(e) => setBinanceForm(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入当前密码"
            />
          </div>

          {errors.binance && (
            <div className="text-red-600 text-sm">{errors.binance}</div>
          )}
          {messages.binance && (
            <div className="text-green-600 text-sm">{messages.binance}</div>
          )}

          <button
            type="submit"
            disabled={isLoading.binance}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium transition-colors"
          >
            {isLoading.binance ? '处理中...' :
             binanceForm.action === 'bind' ? '绑定' :
             binanceForm.action === 'update' ? '更新' : '解绑'}
          </button>
        </form>
      </div>
    </div>
  )
}

// 会话管理组件
function SessionManagement() {
  const [sessions, setSessions] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [message, setMessage] = useState('')

  // 获取会话列表
  const fetchSessions = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/user/security/sessions')
      if (response.ok) {
        const data = await response.json()
        setSessions(data.sessions)
      }
    } catch (error) {
      console.error('获取会话列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 删除会话
  const deleteSession = async (sessionId: string) => {
    if (!confirm('确定要删除这个会话吗？')) return

    try {
      const response = await fetch(`/api/user/security/sessions/${sessionId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setMessage('会话删除成功')
        fetchSessions()
      } else {
        const data = await response.json()
        alert(data.error || '删除会话失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  // 批量删除所有会话
  const deleteAllSessions = async () => {
    if (!confirm('确定要删除所有其他会话吗？这将强制其他设备重新登录。')) return

    try {
      const response = await fetch('/api/user/security/sessions/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'deleteAll',
          excludeCurrentSession: true
        })
      })

      if (response.ok) {
        const data = await response.json()
        setMessage(`成功删除 ${data.deletedCount} 个会话`)
        fetchSessions()
      } else {
        const data = await response.json()
        alert(data.error || '批量删除失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  useEffect(() => {
    fetchSessions()
  }, [])

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 获取设备图标
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return '📱'
      case 'tablet':
        return '📱'
      default:
        return '💻'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">登录设备管理</h3>
          {sessions.length > 1 && (
            <button
              onClick={deleteAllSessions}
              className="text-red-600 hover:text-red-700 font-medium text-sm"
            >
              删除所有其他会话
            </button>
          )}
        </div>
        {message && (
          <div className="mt-2 text-green-600 text-sm">{message}</div>
        )}
      </div>

      <div className="divide-y divide-gray-200">
        {isLoading ? (
          <div className="p-6 text-center text-gray-500">加载中...</div>
        ) : sessions.length > 0 ? (
          sessions.map((session) => (
            <div key={session.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">
                    {getDeviceIcon(session.deviceType)}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {session.deviceName || `${session.browser} on ${session.os}`}
                    </div>
                    <div className="text-sm text-gray-600">
                      {session.ipAddress} • {session.location || '未知位置'}
                    </div>
                    <div className="text-xs text-gray-500">
                      最后活动: {formatTime(session.lastActivity)}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {session.isActive && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      活跃
                    </span>
                  )}
                  <button
                    onClick={() => deleteSession(session.id)}
                    className="text-red-600 hover:text-red-700 text-sm font-medium"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="p-6 text-center text-gray-500">
            暂无活跃会话
          </div>
        )}
      </div>
    </div>
  )
}

// 安全日志组件
function SecurityLogs() {
  const [logs, setLogs] = useState<any[]>([])
  const [stats, setStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [deletingDevices, setDeletingDevices] = useState<Set<string>>(new Set())
  const [filters, setFilters] = useState({
    action: '',
    status: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // 获取安全日志
  const fetchLogs = async (page: number = 1) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      })

      if (filters.action) params.append('action', filters.action)
      if (filters.status) params.append('status', filters.status)

      const response = await fetch(`/api/user/security/logs?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLogs(data.logs)
        setStats(data.stats)
        setPagination(data.pagination)
        setCurrentPage(page)
      }
    } catch (error) {
      console.error('获取安全日志失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 删除信任设备
  const handleDeleteTrustedDevice = async (logId: string, ipAddress: string, deviceInfo: string) => {
    if (!confirm(`确定要删除信任设备"${deviceInfo}"吗？\n\n删除后，该设备上的所有登录会话将被终止，用户需要重新登录。`)) {
      return
    }

    try {
      setDeletingDevices(prev => new Set(prev).add(logId))

      const response = await fetch('/api/user/security/trusted-devices', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ipAddress,
          deviceInfo,
          logId
        })
      })

      if (response.ok) {
        const data = await response.json()
        alert(`成功删除信任设备！已终止 ${data.deletedSessions} 个相关会话。`)
        // 刷新日志列表
        fetchLogs(currentPage)
      } else {
        const error = await response.json()
        alert(`删除失败：${error.error}`)
      }
    } catch (error) {
      console.error('删除信任设备失败:', error)
      alert('删除失败，请稍后重试')
    } finally {
      setDeletingDevices(prev => {
        const newSet = new Set(prev)
        newSet.delete(logId)
        return newSet
      })
    }
  }

  useEffect(() => {
    fetchLogs(1)
  }, [filters])

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 获取操作类型显示文本
  const getActionText = (action: string) => {
    const actionMap: { [key: string]: string } = {
      LOGIN: '登录',
      LOGOUT: '登出',
      PASSWORD_CHANGE: '密码修改',
      EMAIL_CHANGE: '邮箱修改',
      BINANCE_BIND: '币安UID操作',
      PROFILE_UPDATE: '资料更新',
      SECURITY_SETTINGS: '安全设置',
      SESSION_CREATE: '会话创建',
      SESSION_DELETE: '会话删除',
      SUSPICIOUS_ACTIVITY: '可疑活动'
    }
    return actionMap[action] || action
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return 'text-green-600 bg-green-100'
      case 'FAILED':
        return 'text-red-600 bg-red-100'
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.loginAttempts}</div>
            <div className="text-sm text-gray-600">登录尝试</div>
            <div className="text-xs text-gray-500">最近30天</div>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-red-600">{stats.failedLogins}</div>
            <div className="text-sm text-gray-600">失败登录</div>
            <div className="text-xs text-gray-500">最近30天</div>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-green-600">{stats.loginSuccessRate}%</div>
            <div className="text-sm text-gray-600">成功率</div>
            <div className="text-xs text-gray-500">最近30天</div>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-purple-600">{stats.recentLogs}</div>
            <div className="text-sm text-gray-600">安全事件</div>
            <div className="text-xs text-gray-500">最近30天</div>
          </div>
        </div>
      )}

      {/* 过滤器 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              操作类型
            </label>
            <select
              value={filters.action}
              onChange={(e) => setFilters(prev => ({ ...prev, action: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">全部</option>
              <option value="LOGIN">登录</option>
              <option value="LOGOUT">登出</option>
              <option value="PASSWORD_CHANGE">密码修改</option>
              <option value="EMAIL_CHANGE">邮箱修改</option>
              <option value="BINANCE_BIND">币安UID操作</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              状态
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">全部</option>
              <option value="SUCCESS">成功</option>
              <option value="FAILED">失败</option>
              <option value="PENDING">待处理</option>
            </select>
          </div>
        </div>
      </div>

      {/* 日志列表 */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">安全日志</h3>
          <p className="text-sm text-gray-500 mt-1">
            共 {pagination.total} 条记录
          </p>
        </div>

        <div className="divide-y divide-gray-200">
          {isLoading ? (
            <div className="p-6 text-center text-gray-500">加载中...</div>
          ) : logs.length > 0 ? (
            logs.map((log) => (
              <div key={log.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="font-medium text-gray-900">
                        {getActionText(log.action)}
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(log.status)}`}>
                        {log.status === 'SUCCESS' ? '成功' :
                         log.status === 'FAILED' ? '失败' : '待处理'}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-2">{log.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>{formatTime(log.createdAt)}</span>
                      {log.ipAddress && <span>IP: {log.ipAddress}</span>}
                      {log.location && <span>{log.location}</span>}
                    </div>
                  </div>

                  {/* 删除信任设备按钮 - 仅对成功登录记录显示 */}
                  {log.action === 'LOGIN' && log.status === 'SUCCESS' && log.ipAddress && (
                    <div className="ml-4">
                      <button
                        onClick={() => handleDeleteTrustedDevice(
                          log.id,
                          log.ipAddress,
                          log.description.includes('设备：')
                            ? log.description.split('设备：')[1]
                            : `来自 ${log.ipAddress} 的设备`
                        )}
                        disabled={deletingDevices.has(log.id)}
                        className="inline-flex items-center px-3 py-1.5 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {deletingDevices.has(log.id) ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-red-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            删除中...
                          </>
                        ) : (
                          <>
                            <TrashIcon className="h-4 w-4 mr-1" />
                            删除信任设备
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="p-6 text-center text-gray-500">
              暂无安全日志
            </div>
          )}
        </div>

        {/* 分页 */}
        {pagination.pages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <button
                onClick={() => fetchLogs(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>

              <span className="text-sm text-gray-700">
                第 {currentPage} 页，共 {pagination.pages} 页
              </span>

              <button
                onClick={() => fetchLogs(currentPage + 1)}
                disabled={currentPage === pagination.pages}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
