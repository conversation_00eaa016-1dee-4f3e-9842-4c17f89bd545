'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function CreateDemandPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    demandType: 'buy_goods',
    subcategory: '',
    budget: '',
    deliveryMethod: 'online',
    expirationDays: 7
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/demands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          budget: parseFloat(formData.budget)
        }),
      })

      if (response.ok) {
        const demand = await response.json()
        alert('需求发布成功！')
        router.push(`/demands/${demand.id}`)
      } else {
        const data = await response.json()
        alert(data.error || '发布失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">请先登录</h2>
          <Link
            href="/auth/signin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            去登录
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/demands" className="text-gray-700 hover:text-gray-900">
                需求广场
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">发布需求</h1>
            <p className="mt-2 text-gray-600">描述您的需求，让卖家主动联系您</p>
          </div>

          <form onSubmit={handleSubmit} className="bg-white shadow rounded-lg p-6 space-y-6">
            {/* 需求标题 */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                需求标题 *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="简洁描述您的需求（5-100字符）"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
                minLength={5}
                maxLength={100}
              />
              <div className="text-xs text-gray-500 mt-1">
                {formData.title.length}/100 字符
              </div>
            </div>

            {/* 需求类型 */}
            <div>
              <label htmlFor="demandType" className="block text-sm font-medium text-gray-700 mb-2">
                需求类型 *
              </label>
              <select
                id="demandType"
                name="demandType"
                value={formData.demandType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="buy_goods">求购商品</option>
                <option value="hire_service">雇佣服务</option>
                <option value="digital_task">协作任务</option>
                <option value="request_info">求资料/知识</option>
              </select>
            </div>

            {/* 二级分类 */}
            <div>
              <label htmlFor="subcategory" className="block text-sm font-medium text-gray-700 mb-2">
                二级分类
              </label>
              <input
                type="text"
                id="subcategory"
                name="subcategory"
                value={formData.subcategory}
                onChange={handleInputChange}
                placeholder="如：手机、翻译、网课协助等"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                maxLength={50}
              />
            </div>

            {/* 需求描述 */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                需求描述 *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={6}
                placeholder="详细描述您的需求，包括具体要求、质量标准、交付时间等（至少10个字符）"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
                minLength={10}
              />
              <div className="text-xs text-gray-500 mt-1">
                {formData.description.length} 字符
              </div>
            </div>

            {/* 预算 */}
            <div>
              <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">
                预算 (USDT) *
              </label>
              <input
                type="number"
                id="budget"
                name="budget"
                value={formData.budget}
                onChange={handleInputChange}
                placeholder="0.00"
                min="0.01"
                max="100000"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
              <div className="text-xs text-gray-500 mt-1">
                设置合理的预算有助于吸引更多响应
              </div>
            </div>

            {/* 交付方式 */}
            <div>
              <label htmlFor="deliveryMethod" className="block text-sm font-medium text-gray-700 mb-2">
                交付方式 *
              </label>
              <select
                id="deliveryMethod"
                name="deliveryMethod"
                value={formData.deliveryMethod}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="online">线上交付</option>
                <option value="delivery">快递发货</option>
              </select>
            </div>

            {/* 有效期 */}
            <div>
              <label htmlFor="expirationDays" className="block text-sm font-medium text-gray-700 mb-2">
                需求有效期
              </label>
              <select
                id="expirationDays"
                name="expirationDays"
                value={formData.expirationDays}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>1天</option>
                <option value={3}>3天</option>
                <option value={7}>7天</option>
                <option value={14}>14天</option>
                <option value={30}>30天</option>
              </select>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4">
              <Link
                href="/demands"
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
              >
                取消
              </Link>
              <button
                type="submit"
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50"
              >
                {isLoading ? '发布中...' : '发布需求'}
              </button>
            </div>
          </form>

          {/* 发布提示 */}
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">发布提示</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 详细描述需求有助于获得更精准的响应</li>
              <li>• 设置合理的预算范围</li>
              <li>• 需求发布后，卖家可以响应并报价</li>
              <li>• 您可以选择最合适的响应者进行交易</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
