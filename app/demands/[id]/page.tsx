'use client'

import { useState, useEffect, use } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'

interface DemandOffer {
  id: string
  offerPrice: number
  offerNote: string
  isAccepted: boolean
  createdAt: string
  seller: {
    id: string
    name: string | null
    creditScore: number
  }
  orderId?: string
}

interface Demand {
  id: string
  title: string
  description: string
  demandType: string
  subcategory: string | null
  budget: number
  deliveryMethod: string
  expirationTime: string
  status: string
  createdAt: string
  user: {
    id: string
    name: string | null
    creditScore: number
  }
  offers: DemandOffer[]
}

export default function DemandDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const { data: session } = useSession()
  const router = useRouter()
  const [demand, setDemand] = useState<Demand | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showOfferForm, setShowOfferForm] = useState(false)
  const [offerData, setOfferData] = useState({
    offerPrice: '',
    offerNote: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchDemand()
  }, [resolvedParams.id])

  const fetchDemand = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/demands/${resolvedParams.id}`)
      if (response.ok) {
        const data = await response.json()
        setDemand(data)
      } else if (response.status === 404) {
        router.push('/demands')
      }
    } catch (error) {
      console.error('Failed to fetch demand:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitOffer = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/demands/${resolvedParams.id}/offers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          offerPrice: parseFloat(offerData.offerPrice),
          offerNote: offerData.offerNote
        }),
      })

      if (response.ok) {
        alert('响应提交成功！')
        setShowOfferForm(false)
        setOfferData({ offerPrice: '', offerNote: '' })
        fetchDemand() // 刷新数据
      } else {
        const data = await response.json()
        alert(data.error || '响应提交失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAcceptOffer = async (offerId: string) => {
    if (!confirm('确定接受此响应吗？接受后将生成订单。')) {
      return
    }

    try {
      const response = await fetch(`/api/demands/${resolvedParams.id}/offers`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          offerId,
          shippingAddress: demand?.deliveryMethod === 'delivery' ? {
            name: '收货人',
            phone: '手机号',
            detail: '详细地址'
          } : null
        }),
      })

      if (response.ok) {
        const data = await response.json()
        alert('响应已接受，订单已生成！')
        router.push(`/orders/${data.orderId}`)
      } else {
        const data = await response.json()
        alert(data.error || '接受响应失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    }
  }

  const getDemandTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'buy_goods': '求购商品',
      'hire_service': '雇佣服务',
      'digital_task': '协作任务',
      'request_info': '求资料/知识'
    }
    return typeMap[type] || type
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'OPEN': '开放中',
      'MATCHED': '已匹配',
      'CLOSED': '已关闭'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'OPEN': 'bg-green-100 text-green-800',
      'MATCHED': 'bg-blue-100 text-blue-800',
      'CLOSED': 'bg-gray-100 text-gray-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const isExpired = demand ? new Date() > new Date(demand.expirationTime) : false
  const isOwner = demand && session?.user?.id === demand.user.id
  const canRespond = demand && session && !isOwner && demand.status === 'OPEN' && !isExpired
  const hasResponded = demand?.offers.some(offer => offer.seller.id === session?.user?.id)

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!demand) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">需求不存在</h2>
          <Link
            href="/demands"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回需求广场
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/demands" className="text-gray-700 hover:text-gray-900">
                需求广场
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 需求信息 */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(demand.status)}`}>
                  {getStatusText(demand.status)}
                </span>
                <span className="text-sm text-gray-500">
                  {getDemandTypeText(demand.demandType)}
                  {demand.subcategory && ` - ${demand.subcategory}`}
                </span>
              </div>
              {isOwner && demand.status === 'OPEN' && (
                <button
                  onClick={() => {
                    // 关闭需求的逻辑
                  }}
                  className="text-red-600 hover:text-red-800 text-sm font-medium"
                >
                  关闭需求
                </button>
              )}
            </div>

            <h1 className="text-2xl font-bold text-gray-900 mb-4">{demand.title}</h1>

            <div className="prose max-w-none mb-6">
              <p className="text-gray-700 whitespace-pre-wrap">{demand.description}</p>
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6">
              <div>
                <dt className="text-sm font-medium text-gray-500">预算</dt>
                <dd className="text-lg font-bold text-green-600">{formatUSDT(demand.budget)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">交付方式</dt>
                <dd className="text-sm text-gray-900">
                  {demand.deliveryMethod === 'online' ? '线上交付' : '快递发货'}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">发布时间</dt>
                <dd className="text-sm text-gray-900">
                  {new Date(demand.createdAt).toLocaleDateString()}
                </dd>
              </div>
            </div>

            <div className="border-t pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm text-gray-500">发布者: </span>
                  <span className="font-medium">{demand.user.name || '匿名用户'}</span>
                  <span className="text-sm text-gray-500 ml-2">
                    (信誉: {demand.user.creditScore}分)
                  </span>
                </div>
                {canRespond && !hasResponded && (
                  <button
                    onClick={() => setShowOfferForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                  >
                    响应需求
                  </button>
                )}
                {hasResponded && (
                  <span className="text-green-600 text-sm font-medium">您已响应此需求</span>
                )}
              </div>
            </div>
          </div>

          {/* 响应表单 */}
          {showOfferForm && (
            <div className="bg-white shadow rounded-lg p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">响应需求</h3>
              <form onSubmit={handleSubmitOffer} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    报价 (USDT) *
                  </label>
                  <input
                    type="number"
                    value={offerData.offerPrice}
                    onChange={(e) => setOfferData(prev => ({ ...prev, offerPrice: e.target.value }))}
                    placeholder="0.00"
                    min="0.01"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    响应说明 *
                  </label>
                  <textarea
                    value={offerData.offerNote}
                    onChange={(e) => setOfferData(prev => ({ ...prev, offerNote: e.target.value }))}
                    rows={4}
                    placeholder="详细说明您的方案、经验、交付时间等"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                    minLength={10}
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowOfferForm(false)}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
                  >
                    {isSubmitting ? '提交中...' : '提交响应'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* 响应列表 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              响应列表 ({demand.offers.length})
            </h3>
            
            {demand.offers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                暂无响应
              </div>
            ) : (
              <div className="space-y-4">
                {demand.offers.map((offer) => (
                  <div key={offer.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="font-medium">{offer.seller.name || '匿名用户'}</span>
                        <span className="text-sm text-gray-500">
                          信誉: {offer.seller.creditScore}分
                        </span>
                        {offer.isAccepted && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            已接受
                          </span>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-600">
                          {formatUSDT(offer.offerPrice)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(offer.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{offer.offerNote}</p>
                    
                    {isOwner && demand.status === 'OPEN' && !offer.isAccepted && (
                      <div className="flex justify-end">
                        <button
                          onClick={() => handleAcceptOffer(offer.id)}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          接受响应
                        </button>
                      </div>
                    )}
                    
                    {offer.isAccepted && offer.orderId && (
                      <div className="flex justify-end">
                        <Link
                          href={`/orders/${offer.orderId}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          查看订单
                        </Link>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
