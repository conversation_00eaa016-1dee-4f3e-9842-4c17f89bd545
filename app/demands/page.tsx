'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface Demand {
  id: string
  title: string
  description: string
  demandType: string
  subcategory: string | null
  budget: number
  deliveryMethod: string
  expirationTime: string
  status: string
  createdAt: string
  user: {
    id: string
    name: string | null
    creditScore: number
  }
  _count: {
    offers: number
  }
}

interface DemandsResponse {
  demands: Demand[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function DemandsPage() {
  const { data: session } = useSession()
  const [demands, setDemands] = useState<Demand[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [filters, setFilters] = useState({
    keyword: '',
    demandType: 'ALL'
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchInput, setSearchInput] = useState('')

  useEffect(() => {
    fetchDemands()
  }, [pagination.page, filters])

  const fetchDemands = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.keyword && { keyword: filters.keyword }),
        ...(filters.demandType !== 'ALL' && { demandType: filters.demandType })
      })

      const response = await fetch(`/api/demands?${params}`)
      if (response.ok) {
        const data: DemandsResponse = await response.json()
        setDemands(data.demands)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Failed to fetch demands:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setFilters(prev => ({ ...prev, keyword: searchInput.trim() }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleClearSearch = () => {
    setSearchInput('')
    setFilters(prev => ({ ...prev, keyword: '' }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const getDemandTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'buy_goods': '求购商品',
      'hire_service': '雇佣服务',
      'digital_task': '协作任务',
      'request_info': '求资料/知识'
    }
    return typeMap[type] || type
  }

  const getDemandTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'buy_goods': 'bg-blue-100 text-blue-800',
      'hire_service': 'bg-green-100 text-green-800',
      'digital_task': 'bg-purple-100 text-purple-800',
      'request_info': 'bg-yellow-100 text-yellow-800'
    }
    return colorMap[type] || 'bg-gray-100 text-gray-800'
  }

  const getTimeRemaining = (expirationTime: string) => {
    const now = new Date()
    const expiration = new Date(expirationTime)
    const diff = expiration.getTime() - now.getTime()
    
    if (diff <= 0) return '已过期'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days}天${hours}小时`
    return `${hours}小时`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 统一导航栏 */}
      <Navbar />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6 flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">需求广场</h1>
              <p className="mt-2 text-gray-600">发布您的需求，让卖家主动联系您</p>
            </div>
            {session && (
              <Link
                href="/demands/create"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                发布需求
              </Link>
            )}
          </div>

          {/* 搜索和筛选 */}
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <form onSubmit={handleSearch} className="space-y-4">
              {/* 搜索框 */}
              <div className="flex gap-2">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="搜索需求标题或描述..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  搜索
                </button>
                {filters.keyword && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    清除
                  </button>
                )}
              </div>

              {/* 筛选器 */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">需求类型</label>
                  <select
                    value={filters.demandType}
                    onChange={(e) => setFilters(prev => ({ ...prev, demandType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="ALL">所有类型</option>
                    <option value="buy_goods">求购商品</option>
                    <option value="hire_service">雇佣服务</option>
                    <option value="digital_task">协作任务</option>
                    <option value="request_info">求资料/知识</option>
                  </select>
                </div>
              </div>
            </form>
          </div>

          {/* 需求列表 */}
          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : demands.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">
                {filters.keyword || filters.demandType !== 'ALL' 
                  ? '没有找到符合条件的需求' 
                  : '暂无需求'
                }
              </div>
              {session && (
                <Link
                  href="/demands/create"
                  className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  发布第一个需求
                </Link>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {demands.map((demand) => (
                  <div key={demand.id} className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDemandTypeColor(demand.demandType)}`}>
                          {getDemandTypeText(demand.demandType)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {getTimeRemaining(demand.expirationTime)}
                        </span>
                      </div>

                      <h3 className="text-lg font-medium text-gray-900 mb-2 line-clamp-2">
                        {demand.title}
                      </h3>

                      <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                        {demand.description}
                      </p>

                      <div className="flex items-center justify-between mb-4">
                        <div className="text-lg font-bold text-green-600">
                          预算: {formatUSDT(demand.budget)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {demand._count.offers} 个响应
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600">
                          发布者: {demand.user.name || '匿名用户'}
                          <span className="ml-2 text-xs text-gray-500">
                            信誉: {demand.user.creditScore}分
                          </span>
                        </div>
                        <Link
                          href={`/demands/${demand.id}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          查看详情
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="mt-8 flex justify-center">
                  <nav className="flex items-center space-x-2">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      首页
                    </button>
                    
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      上一页
                    </button>
                    
                    <span className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md">
                      {pagination.page} / {pagination.pages}
                    </span>
                    
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      下一页
                    </button>
                    
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.pages }))}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      末页
                    </button>
                  </nav>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
