// 静态生成配置 - 构建时预编译
export const dynamic = 'force-static'
export const revalidate = false

import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'BitMarket - 隐私政策',
  description: 'BitMarket隐私政策，了解我们如何保护您的个人信息',
}

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          {/* 返回按钮 */}
          <div className="mb-8">
            <a
              href="/about"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              返回关于
            </a>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-6">隐私政策</h1>
          <p className="text-gray-600 mb-8">
            最后更新时间：2025年1月18日
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. 信息收集</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                我们收集以下类型的信息：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>账户信息：用户名、邮箱地址、手机号码</li>
                <li>身份验证信息：KYC文档、身份证明</li>
                <li>交易信息：交易记录、钱包地址</li>
                <li>技术信息：IP地址、设备信息、浏览器类型</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. 信息使用</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                我们使用收集的信息用于：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>提供和维护我们的服务</li>
                <li>处理交易和验证身份</li>
                <li>防止欺诈和确保平台安全</li>
                <li>改进我们的服务和用户体验</li>
                <li>遵守法律法规要求</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. 信息保护</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                我们采取以下措施保护您的信息：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>使用SSL加密传输敏感数据</li>
                <li>实施多重身份验证</li>
                <li>定期安全审计和漏洞扫描</li>
                <li>限制员工访问权限</li>
                <li>使用安全的数据存储设施</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. 信息共享</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                我们不会出售、交易或转让您的个人信息给第三方，除非：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>获得您的明确同意</li>
                <li>法律法规要求</li>
                <li>保护我们的权利和财产</li>
                <li>与可信的服务提供商合作（在严格保密协议下）</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. 您的权利</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                您有权：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>访问和更新您的个人信息</li>
                <li>删除您的账户和相关数据</li>
                <li>限制或反对某些数据处理</li>
                <li>数据可携带性</li>
                <li>撤回同意</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. 联系我们</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                如果您对此隐私政策有任何疑问，请联系我们：
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-600">
                  <strong>邮箱：</strong> <EMAIL><br />
                  <strong>地址：</strong> 中国上海市浦东新区<br />
                  <strong>电话：</strong> +86 400-123-4567
                </p>
              </div>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              本隐私政策受中华人民共和国法律管辖
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
