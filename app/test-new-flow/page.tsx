'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'

export default function TestNewFlowPage() {
  const { data: session } = useSession()
  const [testResults, setTestResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const addResult = (test: string, success: boolean, data: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      data,
      timestamp: new Date().toISOString()
    }])
  }

  const testAddressAPI = async () => {
    setLoading(true)
    try {
      // 1. 获取地址列表
      const response = await fetch('/api/addresses', {
        credentials: 'include'
      })
      
      const data = await response.json()
      addResult('获取地址列表', response.ok, data)

      if (response.ok && data.data.length === 0) {
        // 2. 创建测试地址
        const createResponse = await fetch('/api/addresses', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            name: '测试收件人',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detail: '科技园南区测试地址456号',
            isDefault: true
          })
        })

        const createData = await createResponse.json()
        addResult('创建地址', createResponse.ok, createData)
      }

    } catch (error: any) {
      addResult('地址API测试', false, { error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testOrderFlow = async () => {
    setLoading(true)
    try {
      const productId = 'cmdr1z4vv00078oqqm2k4gq7v'
      
      // 1. 获取地址列表
      const addressResponse = await fetch('/api/addresses', {
        credentials: 'include'
      })
      
      if (!addressResponse.ok) {
        addResult('获取地址失败', false, await addressResponse.json())
        return
      }

      const addressData = await addressResponse.json()
      if (addressData.data.length === 0) {
        addResult('订单流程测试', false, { error: '没有可用地址' })
        return
      }

      const address = addressData.data[0]
      addResult('获取地址成功', true, { addressCount: addressData.data.length })

      // 2. 获取中间人
      const mediatorResponse = await fetch('/api/mediator/auto-assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          orderAmount: 999
        })
      })

      const mediatorData = await mediatorResponse.json()
      addResult('中间人分配', mediatorResponse.ok, mediatorData)

      if (!mediatorResponse.ok) {
        return
      }

      // 3. 创建订单
      const orderResponse = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          productId: productId,
          quantity: 1,
          shippingAddress: {
            name: address.name,
            phone: address.phone,
            province: address.province,
            city: address.city,
            district: address.district,
            detail: address.detail
          }
        })
      })

      const orderData = await orderResponse.json()
      addResult('创建订单', orderResponse.ok, orderData)

      if (!orderResponse.ok) {
        return
      }

      // 4. 创建托管订单
      const escrowResponse = await fetch('/api/escrow/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          orderId: orderData.id,
          mediatorId: mediatorData.data.mediator.id,
          escrowAmount: 999,
          escrowFee: mediatorData.data.escrowFee
        })
      })

      const escrowData = await escrowResponse.json()
      addResult('创建托管订单', escrowResponse.ok, escrowData)

    } catch (error: any) {
      addResult('订单流程测试', false, { error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testShippingAPI = async () => {
    setLoading(true)
    try {
      // 查找一个可以发货的订单
      const ordersResponse = await fetch('/api/orders?type=seller&status=PAID', {
        credentials: 'include'
      })

      if (!ordersResponse.ok) {
        addResult('获取订单失败', false, await ordersResponse.json())
        return
      }

      const ordersData = await ordersResponse.json()
      if (ordersData.orders.length === 0) {
        addResult('发货测试', false, { error: '没有可发货的订单' })
        return
      }

      const order = ordersData.orders[0]
      
      // 测试直接发货
      const shippingResponse = await fetch(`/api/orders/${order.id}/shipping`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          type: 'direct',
          trackingNumber: 'TEST123456789',
          shippingCompany: '测试快递'
        })
      })

      const shippingData = await shippingResponse.json()
      addResult('发货测试', shippingResponse.ok, shippingData)

    } catch (error: any) {
      addResult('发货API测试', false, { error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <a
            href="/auth/signin"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">新流程功能测试</h1>
          
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              当前用户: {session.user?.email} ({session.user?.name})
            </p>
            
            <div className="flex gap-4 mb-4">
              <button
                onClick={testAddressAPI}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                测试地址管理API
              </button>
              
              <button
                onClick={testOrderFlow}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                测试完整订单流程
              </button>
              
              <button
                onClick={testShippingAPI}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                测试发货API
              </button>
              
              <button
                onClick={clearResults}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
              >
                清空结果
              </button>
            </div>
          </div>

          {/* 测试结果 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">测试结果</h3>
            
            {testResults.length === 0 && (
              <p className="text-gray-500">暂无测试结果</p>
            )}
            
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 ${
                  result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`font-medium ${
                    result.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {result.success ? '✅' : '❌'} {result.test}
                  </h4>
                  <span className="text-xs text-gray-500">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-40">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
