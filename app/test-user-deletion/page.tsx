'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Trash2, 
  AlertTriangle, 
  CheckCircle, 
  User,
  Mail,
  Shield
} from 'lucide-react'

export default function TestUserDeletionPage() {
  const { data: session } = useSession()
  const [testEmail, setTestEmail] = useState('')
  const [testName, setTestName] = useState('')
  const [createdUserId, setCreatedUserId] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const createTestUser = async () => {
    if (!testEmail || !testName) {
      setError('请填写邮箱和姓名')
      return
    }

    setLoading(true)
    setError('')
    setMessage('')

    try {
      const response = await fetch('/api/test/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: testEmail,
          name: testName
        })
      })

      const data = await response.json()

      if (data.success) {
        setCreatedUserId(data.data.id)
        setMessage(`测试用户创建成功！用户ID: ${data.data.id}`)
      } else {
        setError(data.error || '创建失败')
      }
    } catch (error) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const deleteTestUser = async () => {
    if (!createdUserId) {
      setError('请先创建测试用户')
      return
    }

    const confirm1 = window.confirm(
      `确定要删除测试用户吗？\n\n` +
      `用户ID: ${createdUserId}\n` +
      `邮箱: ${testEmail}\n` +
      `姓名: ${testName}\n\n` +
      `这是永久删除操作，不可撤销！`
    )

    if (!confirm1) return

    const confirm2 = window.prompt(`请输入邮箱 "${testEmail}" 来确认删除：`)
    if (confirm2 !== testEmail) {
      setError('邮箱输入不匹配，删除取消')
      return
    }

    setLoading(true)
    setError('')
    setMessage('')

    try {
      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: createdUserId,
          action: 'deleteUser',
          data: {}
        })
      })

      const data = await response.json()

      if (data.success) {
        setMessage(`用户删除成功！用户 "${testName}" 已被永久删除`)
        setCreatedUserId('')
        setTestEmail('')
        setTestName('')
      } else {
        setError(data.error || '删除失败')
      }
    } catch (error) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const checkUserExists = async () => {
    if (!testEmail) {
      setError('请输入邮箱')
      return
    }

    setLoading(true)
    setError('')
    setMessage('')

    try {
      const response = await fetch(`/api/test/check-user?email=${encodeURIComponent(testEmail)}`)
      const data = await response.json()

      if (data.exists) {
        setMessage(`用户存在！用户ID: ${data.user.id}, 姓名: ${data.user.name}`)
        setCreatedUserId(data.user.id)
        setTestName(data.user.name)
      } else {
        setMessage('用户不存在，可以创建新用户')
      }
    } catch (error) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  if (session?.user?.role !== 'ADMIN') {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            此页面仅限管理员访问
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center">
          <Trash2 className="w-8 h-8 mr-3 text-red-500" />
          用户删除功能测试
        </h1>
        <p className="text-gray-600">
          测试永久删除用户功能，验证删除操作是否正确执行
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {message && (
        <Alert className="mb-6">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 创建测试用户 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              创建测试用户
            </CardTitle>
            <CardDescription>
              创建一个测试用户用于删除测试
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址
              </label>
              <Input
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                disabled={loading}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                用户姓名
              </label>
              <Input
                type="text"
                value={testName}
                onChange={(e) => setTestName(e.target.value)}
                placeholder="测试用户"
                disabled={loading}
              />
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={createTestUser}
                disabled={loading || !testEmail || !testName}
                className="flex-1"
              >
                {loading ? '创建中...' : '创建用户'}
              </Button>
              
              <Button
                onClick={checkUserExists}
                disabled={loading || !testEmail}
                variant="outline"
              >
                检查用户
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 删除测试用户 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Trash2 className="w-5 h-5 mr-2 text-red-500" />
              删除测试用户
            </CardTitle>
            <CardDescription>
              永久删除创建的测试用户
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {createdUserId ? (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">当前测试用户</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>ID: {createdUserId}</div>
                  <div>邮箱: {testEmail}</div>
                  <div>姓名: {testName}</div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-yellow-50 rounded-lg">
                <p className="text-sm text-yellow-800">
                  请先创建测试用户
                </p>
              </div>
            )}

            <Button
              onClick={deleteTestUser}
              disabled={loading || !createdUserId}
              variant="destructive"
              className="w-full"
            >
              {loading ? '删除中...' : '永久删除用户'}
            </Button>

            <div className="text-xs text-gray-500 space-y-1">
              <p>⚠️ 这是真实的删除操作</p>
              <p>• 用户记录将从数据库中完全移除</p>
              <p>• 相关数据将被删除</p>
              <p>• 邮箱可以重新注册</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 功能说明 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            功能说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">修改前（旧版本）</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 显示"已删除用户"复选框</li>
                <li>• 删除时创建匿名用户记录</li>
                <li>• 匿名化处理相关数据</li>
                <li>• 保留已删除用户的记录</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">修改后（新版本）</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 移除"已删除用户"复选框</li>
                <li>• 真正的永久删除操作</li>
                <li>• 直接删除用户和相关数据</li>
                <li>• 邮箱可以重新注册使用</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
