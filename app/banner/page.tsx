'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatRemainingBanTime, type UserStatusCheck } from '@/lib/user-status'

export default function BannerPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [userStatus, setUserStatus] = useState<UserStatusCheck | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session?.user?.id) {
      router.push('/auth/signin')
      return
    }

    fetchUserStatus()
  }, [session, status, router])

  const fetchUserStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/user/status')
      
      if (response.ok) {
        const data = await response.json()
        setUserStatus(data.status)
        
        // 如果用户没有被封禁，重定向到主页
        if (!data.status.isBanned) {
          router.push('/')
          return
        }
      } else {
        setError('获取用户状态失败')
      }
    } catch (error) {
      console.error('获取用户状态失败:', error)
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-600">{error}</div>
      </div>
    )
  }

  if (!userStatus?.isBanned) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">重定向中...</div>
      </div>
    )
  }

  const banInfo = userStatus.banInfo!
  const remainingTime = banInfo.remainingTime || 0
  const isExpired = banInfo.isExpired

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">用户: {session?.user?.name || session?.user?.email}</span>
              <button
                onClick={() => {
                  // 清除重定向标记，允许用户正常访问主页
                  if (session?.user?.id) {
                    sessionStorage.removeItem(`banned_redirect_${session.user.id}`)
                  }
                  router.push('/')
                }}
                className="text-gray-700 hover:text-gray-900"
              >
                返回主页
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          {/* 封禁通知头部 */}
          <div className="bg-red-600 px-6 py-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <h1 className="text-xl font-bold text-white">
                  账户已被封禁
                </h1>
                <p className="text-red-100">
                  您的账户因违反平台规定而被暂时限制使用
                </p>
              </div>
            </div>
          </div>

          {/* 封禁详情 */}
          <div className="px-6 py-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 封禁信息 */}
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-900">封禁详情</h2>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-600">封禁原因：</span>
                      <p className="text-sm text-gray-900 mt-1">
                        {banInfo.banReason || '未提供具体原因'}
                      </p>
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium text-gray-600">封禁开始时间：</span>
                      <p className="text-sm text-gray-900">
                        {new Date(banInfo.bannedAt).toLocaleString('zh-CN')}
                      </p>
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium text-gray-600">封禁类型：</span>
                      <p className="text-sm text-gray-900">
                        {banInfo.bannedUntil ? '临时封禁' : '永久封禁'}
                      </p>
                    </div>
                    
                    {banInfo.bannedUntil && (
                      <div>
                        <span className="text-sm font-medium text-gray-600">解封时间：</span>
                        <p className="text-sm text-gray-900">
                          {new Date(banInfo.bannedUntil).toLocaleString('zh-CN')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 剩余时间和状态 */}
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-900">当前状态</h2>
                
                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                  {isExpired ? (
                    <div className="text-center">
                      <p className="text-green-600 font-medium">封禁已过期</p>
                      <p className="text-sm text-gray-600 mt-1">
                        请刷新页面或重新登录
                      </p>
                      <button
                        onClick={() => window.location.reload()}
                        className="mt-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                      >
                        刷新页面
                      </button>
                    </div>
                  ) : banInfo.bannedUntil ? (
                    <div className="text-center">
                      <p className="text-red-600 font-medium text-lg">
                        剩余封禁时间
                      </p>
                      <p className="text-2xl font-bold text-red-700 mt-2">
                        {formatRemainingBanTime(remainingTime)}
                      </p>
                      <p className="text-sm text-gray-600 mt-1">
                        封禁将于 {new Date(banInfo.bannedUntil).toLocaleString('zh-CN')} 自动解除
                      </p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p className="text-red-600 font-medium text-lg">
                        永久封禁
                      </p>
                      <p className="text-sm text-gray-600 mt-1">
                        如需申诉，请联系客服
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 限制说明 */}
            <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-yellow-800 mb-3">使用限制</h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 无法创建新的订单或购买商品</li>
                <li>• 无法继续处理已创建的订单</li>
                <li>• 无法发布新的商品或需求</li>
                <li>• 可以浏览商品和平台内容</li>
                <li>• 可以查看个人订单历史</li>
              </ul>
            </div>

            {/* 申诉按钮 */}
            <div className="mt-8 text-center">
              <Link
                href="/radar"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.524A11.956 11.956 0 010 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                </svg>
                提交申诉
              </Link>
              <p className="text-sm text-gray-500 mt-2">
                如果您认为此封禁有误，可以提交申诉
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
