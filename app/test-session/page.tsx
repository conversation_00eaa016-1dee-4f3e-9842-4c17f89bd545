'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'

export default function TestSessionPage() {
  const { data: session, status } = useSession()
  const [apiSession, setApiSession] = useState<any>(null)

  useEffect(() => {
    // 直接调用API检查会话
    fetch('/api/auth/session')
      .then(res => res.json())
      .then(data => setApiSession(data))
      .catch(err => console.error('API会话检查失败:', err))
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">会话状态测试</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* useSession Hook 结果 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">useSession Hook</h2>
            <div className="space-y-2">
              <div><strong>状态:</strong> {status}</div>
              <div><strong>会话存在:</strong> {session ? '是' : '否'}</div>
              {session && (
                <div className="mt-4 space-y-2">
                  <div><strong>用户ID:</strong> {session.user?.id}</div>
                  <div><strong>自定义ID:</strong> {session.user?.userId}</div>
                  <div><strong>邮箱:</strong> {session.user?.email}</div>
                  <div><strong>姓名:</strong> {session.user?.name}</div>
                  <div><strong>角色:</strong> {session.user?.role}</div>
                </div>
              )}
            </div>
          </div>

          {/* API 直接调用结果 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">API 直接调用</h2>
            <div className="space-y-2">
              <div><strong>API会话存在:</strong> {apiSession ? '是' : '否'}</div>
              {apiSession && apiSession.user && (
                <div className="mt-4 space-y-2">
                  <div><strong>用户ID:</strong> {apiSession.user?.id}</div>
                  <div><strong>自定义ID:</strong> {apiSession.user?.userId}</div>
                  <div><strong>邮箱:</strong> {apiSession.user?.email}</div>
                  <div><strong>姓名:</strong> {apiSession.user?.name}</div>
                  <div><strong>角色:</strong> {apiSession.user?.role}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 原始数据 */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">原始数据</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">useSession 数据:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify({ status, session }, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="font-medium mb-2">API 会话数据:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(apiSession, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            刷新页面
          </button>
        </div>
      </div>
    </div>
  )
}
