'use client'

import { useSession, signIn, signOut } from 'next-auth/react'
import { useEffect, useState } from 'react'

export default function TestSessionPage() {
  const { data: session, status, update } = useSession()
  const [apiSession, setApiSession] = useState<any>(null)
  const [testResults, setTestResults] = useState<any>({})
  const [isRunningTests, setIsRunningTests] = useState(false)

  useEffect(() => {
    // 直接调用API检查会话
    fetch('/api/auth/session')
      .then(res => res.json())
      .then(data => setApiSession(data))
      .catch(err => console.error('API会话检查失败:', err))
  }, [])

  const runPersistenceTests = async () => {
    setIsRunningTests(true)
    const results: any = {}

    try {
      // 测试 1: 检查 cookies
      const cookies = document.cookie.split(';')
      const authCookies = cookies.filter(cookie =>
        cookie.includes('next-auth') || cookie.includes('session')
      )
      results.cookies = {
        total: cookies.length,
        authCookies: authCookies.length,
        authCookieNames: authCookies.map(c => c.split('=')[0].trim())
      }

      // 测试 2: 测试会话刷新
      try {
        await update()
        results.sessionRefresh = { success: true }
      } catch (error) {
        results.sessionRefresh = {
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        }
      }

      // 测试 3: 测试本地存储
      results.localStorage = {
        hasNextAuthItems: Object.keys(localStorage).filter(key =>
          key.includes('next-auth')
        ).length > 0
      }

      setTestResults(results)
    } catch (error) {
      console.error('测试失败:', error)
      results.error = error instanceof Error ? error.message : '未知错误'
      setTestResults(results)
    } finally {
      setIsRunningTests(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">会话状态测试</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* useSession Hook 结果 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">useSession Hook</h2>
            <div className="space-y-2">
              <div><strong>状态:</strong> {status}</div>
              <div><strong>会话存在:</strong> {session ? '是' : '否'}</div>
              {session && (
                <div className="mt-4 space-y-2">
                  <div><strong>用户ID:</strong> {session.user?.id}</div>
                  <div><strong>自定义ID:</strong> {session.user?.userId}</div>
                  <div><strong>邮箱:</strong> {session.user?.email}</div>
                  <div><strong>姓名:</strong> {session.user?.name}</div>
                  <div><strong>角色:</strong> {session.user?.role}</div>
                </div>
              )}
            </div>
          </div>

          {/* API 直接调用结果 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">API 直接调用</h2>
            <div className="space-y-2">
              <div><strong>API会话存在:</strong> {apiSession ? '是' : '否'}</div>
              {apiSession && apiSession.user && (
                <div className="mt-4 space-y-2">
                  <div><strong>用户ID:</strong> {apiSession.user?.id}</div>
                  <div><strong>自定义ID:</strong> {apiSession.user?.userId}</div>
                  <div><strong>邮箱:</strong> {apiSession.user?.email}</div>
                  <div><strong>姓名:</strong> {apiSession.user?.name}</div>
                  <div><strong>角色:</strong> {apiSession.user?.role}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 原始数据 */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">原始数据</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">useSession 数据:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify({ status, session }, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="font-medium mb-2">API 会话数据:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(apiSession, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        {/* 测试结果 */}
        {Object.keys(testResults).length > 0 && (
          <div className="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">持久性测试结果</h2>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-8 text-center space-x-4">
          {!session ? (
            <button
              onClick={() => signIn()}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md"
            >
              登录
            </button>
          ) : (
            <button
              onClick={() => signOut()}
              className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md"
            >
              登出
            </button>
          )}

          <button
            onClick={runPersistenceTests}
            disabled={isRunningTests}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md"
          >
            {isRunningTests ? '测试中...' : '运行持久性测试'}
          </button>

          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            刷新页面
          </button>
        </div>

        {/* 测试说明 */}
        <div className="mt-8 bg-yellow-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">会话持久性测试指南</h3>
          <ol className="list-decimal list-inside space-y-2 text-yellow-700">
            <li>登录后点击"运行持久性测试"检查当前状态</li>
            <li>刷新页面，检查登录状态是否保持</li>
            <li>关闭标签页重新打开此页面，检查登录状态</li>
            <li>在新标签页中打开此页面，检查会话同步</li>
            <li>检查浏览器开发者工具 → Application → Cookies</li>
            <li>查看控制台中的会话相关日志</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
