'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatUSDT } from '@/lib/utils'
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  Users, 
  DollarSign,
  FileText,
  MessageCircle
} from 'lucide-react'

export default function TestEscrowPage() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<any>(null)
  const [escrowOrders, setEscrowOrders] = useState([])
  const [mediators, setMediators] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (session?.user) {
      fetchTestData()
    }
  }, [session])

  const fetchTestData = async () => {
    try {
      const [statsRes, ordersRes, mediatorsRes] = await Promise.all([
        fetch('/api/admin/escrow-orders/stats').catch(() => ({ json: () => ({ success: false }) })),
        fetch('/api/user/escrow-orders').catch(() => ({ json: () => ({ success: false }) })),
        fetch('/api/escrow/create?amount=100').catch(() => ({ json: () => ({ success: false }) }))
      ])

      const [statsData, ordersData, mediatorsData] = await Promise.all([
        statsRes.json(),
        ordersRes.json(),
        mediatorsRes.json()
      ])

      if (statsData.success) setStats(statsData.data)
      if (ordersData.success) setEscrowOrders(ordersData.data.orders || [])
      if (mediatorsData.success) setMediators(mediatorsData.data || [])

    } catch (error) {
      console.error('获取测试数据失败:', error)
      setError('获取数据失败')
    } finally {
      setLoading(false)
    }
  }

  const testCreateEscrow = async () => {
    try {
      const response = await fetch('/api/test/create-escrow-order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productTitle: '测试商品 - MacBook Pro',
          amount: 150,
          buyerEmail: '<EMAIL>',
          sellerEmail: '<EMAIL>'
        })
      })

      const data = await response.json()
      if (data.success) {
        alert('测试托管订单创建成功！')
        fetchTestData()
      } else {
        alert('创建失败: ' + data.error)
      }
    } catch (error) {
      alert('创建失败: ' + error)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center">
          <Shield className="w-8 h-8 mr-3 text-blue-500" />
          托管系统测试页面
        </h1>
        <p className="text-gray-600">
          测试和验证托管系统的各项功能
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 系统状态 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据库连接</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">正常</div>
            <p className="text-xs text-muted-foreground">
              数据库连接正常
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API状态</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">正常</div>
            <p className="text-xs text-muted-foreground">
              所有API接口正常
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">托管订单</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalOrders || 0}</div>
            <p className="text-xs text-muted-foreground">
              总托管订单数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃中间人</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mediators.length}</div>
            <p className="text-xs text-muted-foreground">
              可用中间人数量
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 功能测试 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>功能测试</CardTitle>
            <CardDescription>
              测试托管系统的核心功能
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testCreateEscrow}
              className="w-full"
              variant="outline"
            >
              创建测试托管订单
            </Button>
            
            <Button 
              onClick={() => window.open('/mediator/apply', '_blank')}
              className="w-full"
              variant="outline"
            >
              测试中间人申请
            </Button>
            
            <Button 
              onClick={() => window.open('/radar?tab=dispute', '_blank')}
              className="w-full"
              variant="outline"
            >
              测试争议举报
            </Button>
            
            <Button 
              onClick={() => window.open('/mediator/arbitration', '_blank')}
              className="w-full"
              variant="outline"
            >
              测试仲裁投票
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>管理功能</CardTitle>
            <CardDescription>
              管理员功能测试（需要管理员权限）
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={() => window.open('/admin/mediator/applications', '_blank')}
              className="w-full"
              variant="outline"
              disabled={session?.user?.role !== 'ADMIN'}
            >
              中间人申请审核
            </Button>
            
            <Button 
              onClick={() => window.open('/admin/escrow-orders', '_blank')}
              className="w-full"
              variant="outline"
              disabled={session?.user?.role !== 'ADMIN'}
            >
              托管订单管理
            </Button>
            
            <Button 
              onClick={() => window.open('/admin/withdrawal-vouchers', '_blank')}
              className="w-full"
              variant="outline"
              disabled={session?.user?.role !== 'ADMIN'}
            >
              提现券管理
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 可用中间人列表 */}
      {mediators.length > 0 && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>可用中间人</CardTitle>
            <CardDescription>
              当前平台上的活跃中间人
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mediators.map((mediator: any) => (
                <div key={mediator.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{mediator.name}</h4>
                    <Badge variant="outline" className="text-green-600">
                      活跃
                    </Badge>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>费率: {(mediator.mediatorFeeRate * 100).toFixed(1)}%</div>
                    <div>信誉: {mediator.mediatorReputation || 0}分</div>
                    <div>成功率: {((mediator.mediatorSuccessRate || 0) * 100).toFixed(1)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 最近的托管订单 */}
      {escrowOrders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>最近的托管订单</CardTitle>
            <CardDescription>
              您参与的托管订单
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {escrowOrders.slice(0, 5).map((order: any) => (
                <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium">{order.order.orderNumber}</h4>
                        <Badge variant="outline" className={
                          order.status === 'COMPLETED' ? 'text-green-600' :
                          order.status === 'DISPUTED' ? 'text-red-600' :
                          'text-yellow-600'
                        }>
                          {order.status}
                        </Badge>
                        <Badge variant="outline" className={
                          order.userRole === 'buyer' ? 'text-blue-600' :
                          order.userRole === 'seller' ? 'text-green-600' :
                          'text-purple-600'
                        }>
                          {order.userRole === 'buyer' ? '买家' : 
                           order.userRole === 'seller' ? '卖家' : '中间人'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{order.order.product.title}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>金额: {formatUSDT(order.amount)}</span>
                        <span>创建: {new Date(order.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => window.open(`/escrow/orders/${order.id}`, '_blank')}
                        variant="outline"
                        size="sm"
                      >
                        查看详情
                      </Button>
                      {order.chatRoom && (
                        <Button
                          onClick={() => window.open(`/escrow/chat/${order.id}`, '_blank')}
                          variant="outline"
                          size="sm"
                        >
                          <MessageCircle className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 系统信息 */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>系统信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">当前用户:</span>
              <span className="ml-2">{session?.user?.name || '未登录'}</span>
            </div>
            <div>
              <span className="font-medium">用户角色:</span>
              <span className="ml-2">{session?.user?.role || 'USER'}</span>
            </div>
            <div>
              <span className="font-medium">是否中间人:</span>
              <span className="ml-2">{session?.user?.isMediator ? '是' : '否'}</span>
            </div>
            <div>
              <span className="font-medium">测试时间:</span>
              <span className="ml-2">{new Date().toLocaleString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
