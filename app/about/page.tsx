// 静态生成配置 - 构建时预编译
export const dynamic = 'force-static'
export const revalidate = false

import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'BitMarket - 关于我们',
  description: '了解BitMarket的使命愿景，访问隐私政策、服务条款和技术白皮书',
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">关于 BitMarket</h1>
          <p className="text-xl text-gray-600">
            了解我们的使命愿景、政策条款和技术文档
          </p>
        </div>

        {/* 导航卡片网格 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* 关于我们卡片 */}
          <a
            href="#about-section"
            className="group bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">关于我们</h3>
              <p className="text-gray-600 text-sm">
                了解BitMarket的使命、愿景和核心价值观
              </p>
              <div className="mt-4 text-blue-600 text-sm font-medium group-hover:text-blue-700">
                查看详情 →
              </div>
            </div>
          </a>

          {/* 隐私政策卡片 */}
          <a
            href="/privacy"
            className="group bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">隐私政策</h3>
              <p className="text-gray-600 text-sm">
                了解我们如何保护和处理您的个人信息
              </p>
              <div className="mt-4 text-green-600 text-sm font-medium group-hover:text-green-700">
                查看详情 →
              </div>
            </div>
          </a>

          {/* 服务条款卡片 */}
          <a
            href="/terms"
            className="group bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">服务条款</h3>
              <p className="text-gray-600 text-sm">
                查看使用我们平台的规则和条件
              </p>
              <div className="mt-4 text-purple-600 text-sm font-medium group-hover:text-purple-700">
                查看详情 →
              </div>
            </div>
          </a>

          {/* 技术白皮书卡片 */}
          <a
            href="/whitebook"
            className="group bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">技术白皮书</h3>
              <p className="text-gray-600 text-sm">
                深入了解我们的技术架构和发展愿景
              </p>
              <div className="mt-4 text-orange-600 text-sm font-medium group-hover:text-orange-700">
                查看详情 →
              </div>
            </div>
          </a>
        </div>

        {/* 关于我们详细内容 */}
        <div id="about-section" className="bg-white p-8 rounded-lg shadow-lg mb-12">
          <h2 className="text-3xl font-semibold text-gray-900 mb-8 text-center">关于 BitMarket</h2>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-blue-900 mb-4">我们的使命</h3>
              <p className="text-blue-800 leading-relaxed">
                BitMarket致力于为全球用户提供安全、透明、高效的数字资产交易服务。
                我们相信区块链技术将重塑金融世界，让每个人都能平等地参与数字经济。
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-green-900 mb-4">我们的愿景</h3>
              <p className="text-green-800 leading-relaxed">
                成为全球领先的数字资产交易平台，通过创新技术和优质服务，
                推动数字经济的发展，让区块链技术惠及更多人群。
              </p>
            </div>
          </div>

          <h3 className="text-2xl font-semibold text-gray-900 mb-6">核心价值观</h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">安全第一</h4>
              <p className="text-gray-600">
                采用银行级安全标准，保护用户资产和隐私安全
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">高效创新</h4>
              <p className="text-gray-600">
                持续技术创新，提供快速、稳定的交易体验
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">用户至上</h4>
              <p className="text-gray-600">
                以用户需求为中心，提供贴心的客户服务
              </p>
            </div>
          </div>
        </div>

        {/* 快速导航提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-8 rounded-lg shadow-lg text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">需要更多信息？</h2>
          <p className="text-gray-600 mb-6">
            点击上方卡片快速访问相关页面，各页面都有返回按钮方便导航
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <a
              href="/contact"
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              联系我们
            </a>
            <a
              href="/help"
              className="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              帮助中心
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
