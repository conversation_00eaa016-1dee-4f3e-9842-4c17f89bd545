"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  GiftIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'
import Navbar from '@/components/Navbar'

interface GiftCardOption {
  faceValue: number
  stock: number
  available: boolean
}

interface PurchaseResult {
  orderId: string
  orderNumber: string
  totalAmount: number
  giftCards: Array<{
    cardCode: string
    faceValue: number
    validUntil: string
  }>
}

export default function GiftCardPurchasePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [options, setOptions] = useState<GiftCardOption[]>([])
  const [userBalance, setUserBalance] = useState(0)
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [purchaseResult, setPurchaseResult] = useState<PurchaseResult | null>(null)
  
  const [selectedValue, setSelectedValue] = useState(0)
  const [quantity, setQuantity] = useState(1)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    fetchGiftCardOptions()
  }, [session, status])

  const fetchGiftCardOptions = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/giftcards/purchase')
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setOptions(result.data.options)
          setUserBalance(result.data.userBalance)
          
          // 设置默认选择第一个可用的面值
          const firstAvailable = result.data.options.find((opt: GiftCardOption) => opt.available)
          if (firstAvailable) {
            setSelectedValue(firstAvailable.faceValue)
          }
        } else {
          setError(result.error || '获取礼品卡选项失败')
        }
      } else {
        setError('获取礼品卡选项失败')
      }
    } catch (error) {
      console.error('获取礼品卡选项失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    if (!selectedValue || quantity <= 0) {
      setError('请选择有效的面值和数量')
      return
    }

    const totalAmount = selectedValue * quantity
    if (userBalance < totalAmount) {
      setError(`余额不足，需要 ${totalAmount} USDT，当前余额 ${userBalance.toFixed(2)} USDT`)
      return
    }

    const selectedOption = options.find(opt => opt.faceValue === selectedValue)
    if (!selectedOption || !selectedOption.available) {
      setError('所选面值暂时缺货')
      return
    }

    if (selectedOption.stock < quantity) {
      setError(`库存不足，仅剩 ${selectedOption.stock} 张`)
      return
    }

    try {
      setPurchasing(true)
      setError('')

      const response = await fetch('/api/giftcards/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          faceValue: selectedValue,
          quantity,
          paymentMethod: 'BALANCE'
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess(result.message)
        setPurchaseResult(result.data)
        setUserBalance(prev => prev - totalAmount)
        fetchGiftCardOptions() // 刷新库存
      } else {
        setError(result.error || '购买失败')
      }
    } catch (error) {
      console.error('购买礼品卡失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setPurchasing(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('已复制到剪贴板')
    }).catch(() => {
      alert('复制失败，请手动复制')
    })
  }

  const totalAmount = selectedValue * quantity
  const selectedOption = options.find(opt => opt.faceValue === selectedValue)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            返回
          </button>
          <div className="flex items-center">
            <GiftIcon className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">购买礼品卡</h1>
              <p className="text-gray-600">使用余额购买礼品卡，送给朋友或自己使用</p>
            </div>
          </div>
        </div>

        {/* 用户余额 */}
        <div className="mb-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">当前余额</p>
              <p className="text-2xl font-bold">{userBalance.toFixed(2)} USDT</p>
            </div>
            <CurrencyDollarIcon className="h-12 w-12 text-blue-200" />
          </div>
        </div>

        {/* 成功提示 */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
                <button
                  onClick={() => setError('')}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：购买表单 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">选择礼品卡</h2>

              {/* 面值选择 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">选择面值</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {options.map((option) => (
                    <button
                      key={option.faceValue}
                      onClick={() => setSelectedValue(option.faceValue)}
                      disabled={!option.available}
                      className={`p-4 border-2 rounded-lg text-center transition-colors ${
                        selectedValue === option.faceValue
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : option.available
                            ? 'border-gray-300 hover:border-gray-400'
                            : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <div className="text-lg font-bold">{option.faceValue} USDT</div>
                      <div className="text-xs text-gray-500">
                        {option.available ? `库存: ${option.stock}` : '缺货'}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 数量选择 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">购买数量</label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-10 h-10 border border-gray-300 rounded-md flex items-center justify-center hover:bg-gray-50"
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min="1"
                    max={selectedOption?.stock || 10}
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, Math.min(selectedOption?.stock || 10, parseInt(e.target.value) || 1)))}
                    className="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    onClick={() => setQuantity(Math.min(selectedOption?.stock || 10, quantity + 1))}
                    className="w-10 h-10 border border-gray-300 rounded-md flex items-center justify-center hover:bg-gray-50"
                  >
                    +
                  </button>
                  <span className="text-sm text-gray-500">
                    (最多 {selectedOption?.stock || 0} 张)
                  </span>
                </div>
              </div>

              {/* 订单总计 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">单价:</span>
                  <span className="font-medium">{selectedValue} USDT</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">数量:</span>
                  <span className="font-medium">{quantity} 张</span>
                </div>
                <div className="border-t border-gray-200 pt-2">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium text-gray-900">总计:</span>
                    <span className="text-lg font-bold text-blue-600">{totalAmount} USDT</span>
                  </div>
                </div>
              </div>

              {/* 购买按钮 */}
              <button
                onClick={handlePurchase}
                disabled={purchasing || !selectedValue || !selectedOption?.available || userBalance < totalAmount}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {purchasing ? '购买中...' : `购买 ${quantity} 张礼品卡`}
              </button>
            </div>

            {/* 购买说明 */}
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-blue-800">购买说明</h4>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>使用账户余额购买礼品卡</li>
                      <li>购买后立即生成16位礼品卡码</li>
                      <li>礼品卡有效期为1年</li>
                      <li>可以转赠给他人使用</li>
                      <li>单次最多购买10张</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧：购买结果 */}
          <div className="space-y-6">
            {purchaseResult && (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-4">
                  <CheckCircleIcon className="h-6 w-6 text-green-500 mr-2" />
                  <h3 className="text-lg font-medium text-gray-900">购买成功</h3>
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600">订单号</p>
                    <p className="font-mono text-sm">{purchaseResult.orderNumber}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">礼品卡码</p>
                    <div className="space-y-2">
                      {purchaseResult.giftCards.map((card, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-mono text-sm font-medium">{card.cardCode}</p>
                              <p className="text-xs text-gray-500">
                                {card.faceValue} USDT | 有效期至 {new Date(card.validUntil).toLocaleDateString('zh-CN')}
                              </p>
                            </div>
                            <button
                              onClick={() => copyToClipboard(card.cardCode)}
                              className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                              复制
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <button
                      onClick={() => router.push('/redeem')}
                      className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      去兑换中心
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 推荐面值 */}
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg p-6 text-white">
              <div className="flex items-center mb-3">
                <SparklesIcon className="h-6 w-6 mr-2" />
                <h3 className="text-lg font-semibold">推荐面值</h3>
              </div>
              <p className="text-sm text-purple-100 mb-4">
                根据用户喜好，推荐以下面值的礼品卡
              </p>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>50 USDT</span>
                  <span className="text-purple-200">适合小额赠送</span>
                </div>
                <div className="flex justify-between">
                  <span>100 USDT</span>
                  <span className="text-purple-200">最受欢迎</span>
                </div>
                <div className="flex justify-between">
                  <span>500 USDT</span>
                  <span className="text-purple-200">商务礼品</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
