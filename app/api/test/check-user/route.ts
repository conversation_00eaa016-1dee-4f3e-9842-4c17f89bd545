import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 检查用户是否存在
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json(
        { error: '邮箱不能为空' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        userId: true,
        status: true,
        role: true,
        createdAt: true
      }
    })

    return NextResponse.json({
      success: true,
      exists: !!user,
      user: user || null
    })

  } catch (error) {
    console.error('检查用户失败:', error)
    return NextResponse.json(
      { error: '检查用户失败' },
      { status: 500 }
    )
  }
}
