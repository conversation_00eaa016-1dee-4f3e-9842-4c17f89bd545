import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建测试托管订单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { productTitle, amount, buyerEmail, sellerEmail } = body

    if (!productTitle || !amount || amount < 100) {
      return NextResponse.json(
        { error: '参数错误，金额必须大于100 USDT' },
        { status: 400 }
      )
    }

    // 查找或创建测试用户
    let buyer, seller
    
    if (buyerEmail) {
      buyer = await prisma.user.findUnique({
        where: { email: buyerEmail }
      })
      
      if (!buyer) {
        buyer = await prisma.user.create({
          data: {
            email: buyerEmail,
            name: `测试买家_${Date.now()}`,
            password: 'test123',
            emailVerified: new Date(),
            depositBalance: 5000,
            creditPoints: 100
          }
        })
      }
    } else {
      buyer = await prisma.user.findUnique({
        where: { id: session.user.id }
      })
    }

    if (sellerEmail) {
      seller = await prisma.user.findUnique({
        where: { email: sellerEmail }
      })
      
      if (!seller) {
        seller = await prisma.user.create({
          data: {
            email: sellerEmail,
            name: `测试卖家_${Date.now()}`,
            password: 'test123',
            emailVerified: new Date(),
            depositBalance: 1000,
            creditPoints: 150
          }
        })
      }
    } else {
      // 创建一个临时卖家
      seller = await prisma.user.create({
        data: {
          email: `test_seller_${Date.now()}@test.com`,
          name: `测试卖家_${Date.now()}`,
          password: 'test123',
          emailVerified: new Date(),
          depositBalance: 1000,
          creditPoints: 150
        }
      })
    }

    // 查找可用的中间人
    const availableMediator = await prisma.user.findFirst({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        mediatorDeposit: { gte: amount }
      },
      orderBy: {
        mediatorReputation: 'desc'
      }
    })

    if (!availableMediator) {
      return NextResponse.json(
        { error: '暂无可用的中间人' },
        { status: 400 }
      )
    }

    // 创建测试商品
    const product = await prisma.product.create({
      data: {
        title: productTitle,
        description: '这是一个测试商品，用于验证托管系统功能',
        price: amount,
        category: 'ELECTRONICS',
        condition: 'NEW',
        city: '测试城市',
        district: '测试区域',
        address: '测试地址',
        sellerId: seller.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        images: 'https://via.placeholder.com/400x300?text=Test+Product'
      }
    })

    // 创建订单
    const order = await prisma.order.create({
      data: {
        orderNumber: `TEST_${Date.now()}`,
        buyerId: buyer.id,
        sellerId: seller.id,
        productId: product.id,
        totalAmount: amount,
        productPrice: amount,
        status: 'PENDING_PAYMENT',
        paymentMethod: 'BALANCE',
        shippingAddress: {
          name: buyer.name,
          phone: '13800138000',
          province: '测试省',
          city: '测试市',
          district: '测试区',
          detail: '测试详细地址'
        }
      }
    })

    // 计算费用
    const mediatorFee = amount * (availableMediator.mediatorFeeRate || 0.02)
    const platformFee = mediatorFee * 0.3
    const totalAmount = amount + mediatorFee

    // 检查买家余额
    if (buyer.depositBalance < totalAmount) {
      // 给买家充值
      await prisma.user.update({
        where: { id: buyer.id },
        data: {
          depositBalance: { increment: totalAmount + 1000 }
        }
      })
    }

    // 创建托管订单
    const escrowOrder = await prisma.$transaction(async (tx) => {
      // 创建托管记录
      const escrow = await tx.escrowOrder.create({
        data: {
          orderId: order.id,
          mediatorId: availableMediator.id,
          buyerId: buyer.id,
          sellerId: seller.id,
          amount,
          mediatorFee,
          platformFee,
          status: 'PENDING',
          mediatorWalletAddress: availableMediator.bnbWalletAddress || `0x${Date.now().toString(16)}`
        }
      })

      // 扣除买家余额
      await tx.user.update({
        where: { id: buyer.id },
        data: { depositBalance: { decrement: totalAmount } }
      })

      // 创建资金冻结记录
      await tx.fundFreeze.create({
        data: {
          userId: buyer.id,
          amount: totalAmount,
          purpose: '托管订单资金',
          status: 'FROZEN',
          relatedId: escrow.id,
          relatedType: 'ESCROW_ORDER'
        }
      })

      // 更新订单状态
      await tx.order.update({
        where: { id: order.id },
        data: {
          status: 'PAID',
          paymentConfirmed: true,
          useEscrow: true,
          mediatorId: availableMediator.id,
          escrowFee: mediatorFee,
          escrowFeeRate: availableMediator.mediatorFeeRate ?? undefined
        }
      })

      // 创建聊天室
      await tx.escrowChatRoom.create({
        data: {
          escrowOrderId: escrow.id,
          roomCode: `TEST_ESCROW_${escrow.id}_${Date.now()}`,
          isActive: true
        }
      })

      // 创建交易记录
      await tx.fundTransaction.create({
        data: {
          userId: buyer.id,
          type: 'ESCROW_PAYMENT',
          amount: -totalAmount,
          description: '托管订单支付',
          relatedId: escrow.id,
          metadata: { relatedType: 'ESCROW_ORDER' }
        }
      })

      return escrow
    })

    return NextResponse.json({
      success: true,
      message: '测试托管订单创建成功',
      data: {
        escrowId: escrowOrder.id,
        orderId: order.id,
        orderNumber: order.orderNumber,
        amount,
        mediatorFee,
        totalAmount,
        buyer: { id: buyer.id, name: buyer.name, email: buyer.email },
        seller: { id: seller.id, name: seller.name, email: seller.email },
        mediator: { id: availableMediator.id, name: availableMediator.name, email: availableMediator.email },
        product: { id: product.id, title: product.title }
      }
    })

  } catch (error) {
    console.error('创建测试托管订单失败:', error)
    return NextResponse.json(
      { error: '创建测试托管订单失败' },
      { status: 500 }
    )
  }
}
