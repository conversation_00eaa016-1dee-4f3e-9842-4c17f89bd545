import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// 创建测试用户
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { email, name } = body

    if (!email || !name) {
      return NextResponse.json(
        { error: '邮箱和姓名不能为空' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '邮箱已存在' },
        { status: 400 }
      )
    }

    // 创建测试用户
    const hashedPassword = await bcrypt.hash('test123456', 12)
    
    const user = await prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
        userId: `test_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
        emailVerified: new Date(),
        status: 'ACTIVE',
        role: 'USER',
        creditScore: 100,
        depositBalance: 0
      },
      select: {
        id: true,
        email: true,
        name: true,
        userId: true,
        status: true,
        role: true,
        createdAt: true
      }
    })

    return NextResponse.json({
      success: true,
      message: '测试用户创建成功',
      data: user
    })

  } catch (error) {
    console.error('创建测试用户失败:', error)
    return NextResponse.json(
      { error: '创建测试用户失败' },
      { status: 500 }
    )
  }
}
