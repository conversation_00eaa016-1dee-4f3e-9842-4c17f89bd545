import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 提交争议举报
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      orderNumber, 
      escrowOrderId, 
      reason, 
      description, 
      evidence, 
      reportedUserId 
    } = body

    if (!orderNumber && !escrowOrderId) {
      return NextResponse.json(
        { error: '请提供订单号或托管订单ID' },
        { status: 400 }
      )
    }

    if (!reason || !description) {
      return NextResponse.json(
        { error: '请填写举报原因和详细描述' },
        { status: 400 }
      )
    }

    let escrowOrder: any = null
    let order: any = null

    // 查找托管订单
    if (escrowOrderId) {
      escrowOrder = await prisma.escrowOrder.findUnique({
        where: { id: escrowOrderId },
        include: {
          order: true,
          buyer: true,
          seller: true,
          mediator: true
        }
      })

      if (!escrowOrder) {
        return NextResponse.json(
          { error: '托管订单不存在' },
          { status: 404 }
        )
      }

      order = escrowOrder.order
    } else if (orderNumber) {
      // 通过订单号查找
      order = await prisma.order.findUnique({
        where: { orderNumber },
        include: {
          escrowOrder: {
            include: {
              buyer: true,
              seller: true,
              mediator: true
            }
          }
        }
      })

      if (!order) {
        return NextResponse.json(
          { error: '订单不存在' },
          { status: 404 }
        )
      }

      escrowOrder = order.escrowOrder
    }

    // 验证举报权限
    const allowedUsers = [order.buyerId, order.sellerId]
    if (escrowOrder) {
      allowedUsers.push(escrowOrder.mediatorId)
    }

    if (!allowedUsers.includes(session.user.id)) {
      return NextResponse.json(
        { error: '您没有权限举报此订单' },
        { status: 403 }
      )
    }

    // 确定被举报人
    let reportedId = reportedUserId
    if (!reportedId) {
      // 自动确定被举报人
      if (session.user.id === order.buyerId) {
        reportedId = order.sellerId
      } else if (session.user.id === order.sellerId) {
        reportedId = order.buyerId
      } else if (escrowOrder && session.user.id === escrowOrder.mediatorId) {
        // 中间人可以举报买家或卖家，需要明确指定
        return NextResponse.json(
          { error: '中间人举报需要明确指定被举报人' },
          { status: 400 }
        )
      }
    }

    // 检查是否已有相同争议
    const existingDispute = await prisma.escrowDispute.findFirst({
      where: {
        escrowOrderId: escrowOrder?.id || '',
        reporterId: session.user.id,
        status: { in: ['PENDING', 'UNDER_REVIEW', 'VOTING'] }
      }
    })

    if (existingDispute) {
      return NextResponse.json(
        { error: '您已对此订单提交过争议举报' },
        { status: 400 }
      )
    }

    // 创建争议举报
    const dispute = await prisma.$transaction(async (tx) => {
      const newDispute = await tx.escrowDispute.create({
        data: {
          escrowOrderId: escrowOrder?.id || '',
          reporterId: session.user.id,
          reportedId,
          reason,
          description,
          evidence: evidence || {},
          status: 'PENDING',
          priority: determinePriority(reason, escrowOrder?.amount || order.totalAmount)
        }
      })

      // 如果是托管订单，更新状态
      if (escrowOrder) {
        await tx.escrowOrder.update({
          where: { id: escrowOrder.id },
          data: {
            status: 'DISPUTED',
            disputedAt: new Date()
          }
        })
      }

      // 更新订单状态
      await tx.order.update({
        where: { id: order.id },
        data: {
          disputeStatus: 'REPORTED',
          disputeReportedAt: new Date(),
          disputeReason: reason
        }
      })

      return newDispute
    })

    // 发送通知给管理员
    await prisma.notification.create({
      data: {
        userId: 'admin', // 系统管理员
        type: 'DISPUTE',
        title: '新的争议举报',
        message: `订单 ${order.orderNumber} 收到争议举报`,
        data: {
          disputeId: dispute.id,
          orderId: order.id,
          escrowOrderId: escrowOrder?.id,
          reason,
          amount: escrowOrder?.amount || order.totalAmount
        },
        priority: 'HIGH'
      }
    }).catch(() => {})

    // 通知被举报人
    await prisma.notification.create({
      data: {
        userId: reportedId,
        type: 'DISPUTE',
        title: '您收到一个争议举报',
        message: `订单 ${order.orderNumber} 收到争议举报，请及时处理`,
        data: {
          disputeId: dispute.id,
          orderId: order.id,
          reason
        },
        priority: 'HIGH'
      }
    }).catch(() => {})

    return NextResponse.json({
      success: true,
      message: '争议举报已提交',
      data: {
        disputeId: dispute.id,
        status: dispute.status,
        priority: dispute.priority
      }
    })

  } catch (error) {
    console.error('提交争议举报失败:', error)
    return NextResponse.json(
      { error: '提交举报失败' },
      { status: 500 }
    )
  }
}

// 获取用户的争议举报列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const skip = (page - 1) * limit

    const where: any = {
      OR: [
        { reporterId: session.user.id },
        { reportedId: session.user.id }
      ]
    }

    if (status) {
      where.status = status
    }

    const [disputes, total] = await Promise.all([
      prisma.escrowDispute.findMany({
        where,
        include: {
          escrowOrder: {
            include: {
              order: {
                select: {
                  orderNumber: true,
                  totalAmount: true,
                  product: {
                    select: { title: true }
                  }
                }
              }
            }
          },
          reporter: {
            select: { name: true, email: true }
          },
          reported: {
            select: { name: true, email: true }
          },
          adminUser: {
            select: { name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.escrowDispute.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        disputes,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取争议举报列表失败:', error)
    return NextResponse.json(
      { error: '获取举报列表失败' },
      { status: 500 }
    )
  }
}

// 确定争议优先级
function determinePriority(reason: string, amount: number): string {
  // 高金额订单优先级更高
  if (amount > 1000) {
    return 'HIGH'
  }

  // 特定原因的优先级
  const highPriorityReasons = [
    'FRAUD',
    'SCAM',
    'FAKE_PRODUCT',
    'NO_DELIVERY'
  ]

  if (highPriorityReasons.includes(reason)) {
    return 'HIGH'
  }

  const urgentReasons = [
    'SAFETY_CONCERN',
    'ILLEGAL_ACTIVITY'
  ]

  if (urgentReasons.includes(reason)) {
    return 'URGENT'
  }

  return 'MEDIUM'
}
