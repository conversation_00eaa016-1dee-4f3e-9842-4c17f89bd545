import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的发货历史记录
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type') // 'companies' | 'tracking'

    if (type === 'companies') {
      // 获取最近使用的快递公司
      const recentCompanies = await prisma.order.findMany({
        where: {
          sellerId: session.user.id,
          shippingCompany: { not: null },
          status: { in: ['SHIPPED', 'DELIVERED', 'COMPLETED'] }
        },
        select: {
          shippingCompany: true,
          shippedAt: true
        },
        orderBy: {
          shippedAt: 'desc'
        },
        take: limit * 2 // 取更多数据以便去重
      })

      // 去重并统计使用频率
      const companyStats = new Map<string, { name: string, count: number, lastUsed: Date }>()
      
      recentCompanies.forEach(order => {
        if (order.shippingCompany && order.shippedAt) {
          const existing = companyStats.get(order.shippingCompany)
          if (existing) {
            existing.count++
            if (order.shippedAt > existing.lastUsed) {
              existing.lastUsed = order.shippedAt
            }
          } else {
            companyStats.set(order.shippingCompany, {
              name: order.shippingCompany,
              count: 1,
              lastUsed: order.shippedAt
            })
          }
        }
      })

      // 按使用频率和最近使用时间排序
      const sortedCompanies = Array.from(companyStats.values())
        .sort((a, b) => {
          if (a.count !== b.count) {
            return b.count - a.count // 使用频率高的在前
          }
          return b.lastUsed.getTime() - a.lastUsed.getTime() // 最近使用的在前
        })
        .slice(0, limit)

      return NextResponse.json({ companies: sortedCompanies })
    }

    if (type === 'tracking') {
      // 获取最近使用的运单号格式（用于智能提示）
      const recentTracking = await prisma.order.findMany({
        where: {
          sellerId: session.user.id,
          trackingNumber: { not: null },
          status: { in: ['SHIPPED', 'DELIVERED', 'COMPLETED'] }
        },
        select: {
          trackingNumber: true,
          shippingCompany: true,
          shippedAt: true
        },
        orderBy: {
          shippedAt: 'desc'
        },
        take: limit
      })

      return NextResponse.json({ trackingHistory: recentTracking })
    }

    // 默认返回综合历史记录
    const shippingHistory = await prisma.order.findMany({
      where: {
        sellerId: session.user.id,
        status: { in: ['SHIPPED', 'DELIVERED', 'COMPLETED'] }
      },
      select: {
        id: true,
        orderNumber: true,
        trackingNumber: true,
        shippingCompany: true,
        shippedAt: true,
        status: true,
        product: {
          select: {
            title: true
          }
        },
        buyer: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        shippedAt: 'desc'
      },
      take: limit
    })

    return NextResponse.json({ history: shippingHistory })

  } catch (error) {
    console.error('获取发货历史失败:', error)
    return NextResponse.json(
      { error: '获取发货历史失败' },
      { status: 500 }
    )
  }
}

// 验证运单号格式
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { trackingNumber, shippingCompany } = body

    if (!trackingNumber || !shippingCompany) {
      return NextResponse.json(
        { error: '请提供运单号和快递公司' },
        { status: 400 }
      )
    }

    // 运单号格式验证
    const validation = validateTrackingNumber(trackingNumber, shippingCompany)
    
    // 检查运单号是否已被使用
    const existingOrder = await prisma.order.findFirst({
      where: {
        trackingNumber: trackingNumber.trim(),
        status: { in: ['SHIPPED', 'DELIVERED', 'COMPLETED'] }
      },
      select: {
        id: true,
        orderNumber: true,
        sellerId: true
      }
    })

    let isDuplicate = false
    let duplicateInfo = null

    if (existingOrder) {
      isDuplicate = true
      duplicateInfo = {
        orderId: existingOrder.id,
        orderNumber: existingOrder.orderNumber,
        isOwnOrder: existingOrder.sellerId === session.user.id
      }
    }

    return NextResponse.json({
      isValid: validation.isValid,
      message: validation.message,
      suggestions: validation.suggestions,
      isDuplicate,
      duplicateInfo
    })

  } catch (error) {
    console.error('验证运单号失败:', error)
    return NextResponse.json(
      { error: '验证运单号失败' },
      { status: 500 }
    )
  }
}

// 运单号格式验证函数
function validateTrackingNumber(trackingNumber: string, company: string) {
  const trimmedNumber = trackingNumber.trim()
  
  if (!trimmedNumber) {
    return { isValid: false, message: '运单号不能为空', suggestions: [] }
  }

  if (trimmedNumber.length < 8 || trimmedNumber.length > 30) {
    return { isValid: false, message: '运单号长度应在8-30位之间', suggestions: [] }
  }

  // 根据快递公司进行格式验证
  const validationRules: Record<string, { pattern: RegExp; message: string; example: string }> = {
    'SF': { 
      pattern: /^\d{12}$/, 
      message: '顺丰运单号应为12位数字', 
      example: '123456789012' 
    },
    '顺丰速运': { 
      pattern: /^\d{12}$/, 
      message: '顺丰运单号应为12位数字', 
      example: '123456789012' 
    },
    'YTO': { 
      pattern: /^(\d{10}|YT\d{10})$/, 
      message: '圆通运单号应为10位数字或YT开头的12位', 
      example: '1234567890 或 YT1234567890' 
    },
    '圆通速递': { 
      pattern: /^(\d{10}|YT\d{10})$/, 
      message: '圆通运单号应为10位数字或YT开头的12位', 
      example: '1234567890 或 YT1234567890' 
    },
    'ZTO': { 
      pattern: /^\d{12}$/, 
      message: '中通运单号应为12位数字', 
      example: '123456789012' 
    },
    '中通快递': { 
      pattern: /^\d{12}$/, 
      message: '中通运单号应为12位数字', 
      example: '123456789012' 
    },
    'EMS': { 
      pattern: /^[A-Z]{2}\d{9}[A-Z]{2}$/, 
      message: 'EMS运单号应为2位字母+9位数字+2位字母', 
      example: 'EA123456789CN' 
    },
    '中国邮政EMS': { 
      pattern: /^[A-Z]{2}\d{9}[A-Z]{2}$/, 
      message: 'EMS运单号应为2位字母+9位数字+2位字母', 
      example: 'EA123456789CN' 
    }
  }

  const rule = validationRules[company]
  if (rule) {
    const isValid = rule.pattern.test(trimmedNumber)
    return {
      isValid,
      message: isValid ? '运单号格式正确' : rule.message,
      suggestions: isValid ? [] : [rule.example]
    }
  }

  // 其他快递公司的通用验证
  if (!/^[A-Za-z0-9]+$/.test(trimmedNumber)) {
    return { 
      isValid: false, 
      message: '运单号只能包含字母和数字', 
      suggestions: ['请检查运单号是否包含特殊字符'] 
    }
  }

  return { 
    isValid: true, 
    message: '运单号格式通过基本验证', 
    suggestions: [] 
  }
}
