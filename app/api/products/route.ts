import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { optimizedQueries, optimizedTransactions } from '@/lib/prisma-optimized'
import { withApiCache, cacheConfigs, CacheInvalidator } from '@/lib/api-cache-middleware'
import { monitor, monitorDbQuery } from '@/lib/performance-monitor'
import { cache, CACHE_TTL } from '@/lib/cache-fallback'
import { prisma } from '@/lib/prisma'

// 获取商品列表（带缓存和性能监控）
export const GET = withApiCache(cacheConfigs.products)(async function(request: NextRequest) {
  const endTimer = performance.now()

  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const city = searchParams.get('city')
    const category = searchParams.get('category')
    const condition = searchParams.get('condition')
    const keyword = searchParams.get('keyword')
    const sortBy = searchParams.get('sortBy') || 'newest'
    const sellerId = searchParams.get('sellerId')
    const status = searchParams.get('status')
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')

    // 生成缓存键
    const cacheKey = `products:${JSON.stringify({
      page, limit, city, category, condition, keyword, sortBy, sellerId, status, minPrice, maxPrice
    })}`

    // 限制分页参数
    const validatedPage = Math.max(1, page)
    const validatedLimit = Math.min(Math.max(1, limit), 50) // 最大50条
    const skip = (validatedPage - 1) * validatedLimit

    // 构建查询条件
    const where: any = {
      reviewStatus: 'APPROVED'
    }

    // 状态筛选 - 如果指定了status则使用，否则默认为AVAILABLE
    if (status) {
      if (status === 'INACTIVE') {
        // INACTIVE状态包括SOLD_OUT和INACTIVE
        where.status = {
          in: ['INACTIVE', 'SOLD_OUT']
        }
      } else {
        where.status = status
      }
    } else if (!sellerId) {
      // 只有在不是查看特定用户商品时才默认筛选AVAILABLE
      where.status = 'AVAILABLE'
    }

    // 城市筛选 - 精确匹配
    if (city && city.trim() !== '') {
      where.city = city.trim()
    }

    if (category && category !== 'ALL') {
      where.category = category
    }

    if (condition && condition !== 'ALL') {
      where.condition = condition
    }

    // 关键词搜索 - 仅搜索标题
    if (keyword && keyword.trim() !== '') {
      where.title = {
        contains: keyword.trim(),
        mode: 'insensitive' // 不区分大小写
      }
    }

    // 按卖家ID筛选
    if (sellerId) {
      where.sellerId = sellerId
    }

    // 构建排序条件
    let orderBy: any = { createdAt: 'desc' } // 默认按创建时间降序

    switch (sortBy) {
      case 'newest':
        orderBy = { createdAt: 'desc' }
        break
      case 'oldest':
        orderBy = { createdAt: 'asc' }
        break
      case 'price_low':
        orderBy = { price: 'asc' }
        break
      case 'price_high':
        orderBy = { price: 'desc' }
        break
      default:
        orderBy = { createdAt: 'desc' }
    }

    // 尝试从缓存获取
    const cachedResult = await cache.get(cacheKey)
    if (cachedResult) {
      return NextResponse.json(cachedResult)
    }

    // 使用优化的查询方法
    const result = await monitorDbQuery(
      () => optimizedQueries.getProducts(validatedPage, validatedLimit, {
        city,
        category: category !== 'ALL' ? category : undefined,
        condition: condition !== 'ALL' ? condition : undefined,
        keyword,
        sellerId,
        status,
        sortBy,
        minPrice,
        maxPrice,
      }),
      'getProducts',
      { page: validatedPage, limit: validatedLimit }
    )

    // 缓存结果
    await cache.set(cacheKey, result, CACHE_TTL.SHORT)

    return NextResponse.json(result)

  } catch (error) {
    console.error('Get products error:', error)
    return NextResponse.json(
      { error: '获取商品列表失败' },
      { status: 500 }
    )
  }
})

// 创建商品
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      images,
      price,
      category,
      condition,
      city,
      district,
      shippingFrom,
      stock,
      hasVariants,
      variants
    } = body

    // 验证必填字段
    if (!title || !description || !price) {
      return NextResponse.json(
        { error: '标题、描述和价格为必填项' },
        { status: 400 }
      )
    }

    // 验证标题长度
    if (title.length < 5 || title.length > 60) {
      return NextResponse.json(
        { error: '商品标题应为5-60个字符' },
        { status: 400 }
      )
    }

    // 验证描述长度
    if (description.length < 10) {
      return NextResponse.json(
        { error: '商品描述至少10个字符' },
        { status: 400 }
      )
    }

    // 验证价格
    if (typeof price !== 'number' || price <= 0 || price > 100000) {
      return NextResponse.json(
        { error: '价格应为0-100000之间的数字' },
        { status: 400 }
      )
    }

    // 验证库存（仅在没有变体时验证）
    if (!hasVariants && (typeof stock !== 'number' || stock < 1 || stock > 999)) {
      return NextResponse.json(
        { error: '库存数量应为1-999之间的整数' },
        { status: 400 }
      )
    }

    // 验证变体数据
    if (hasVariants) {
      if (!Array.isArray(variants) || variants.length === 0) {
        return NextResponse.json(
          { error: '启用变体时必须至少添加一个变体' },
          { status: 400 }
        )
      }

      // 验证每个变体
      for (const variant of variants) {
        if (!variant.attributes || !Array.isArray(variant.attributes) || variant.attributes.length === 0) {
          return NextResponse.json(
            { error: '每个变体必须至少有一个属性' },
            { status: 400 }
          )
        }

        if (typeof variant.price !== 'number' || variant.price <= 0 || variant.price > 100000) {
          return NextResponse.json(
            { error: '变体价格应为0-100000之间的数字' },
            { status: 400 }
          )
        }

        if (typeof variant.stock !== 'number' || variant.stock < 0 || variant.stock > 999) {
          return NextResponse.json(
            { error: '变体库存应为0-999之间的整数' },
            { status: 400 }
          )
        }

        // 验证属性
        for (const attr of variant.attributes) {
          if (!attr.name || !attr.value) {
            return NextResponse.json(
              { error: '变体属性名称和值不能为空' },
              { status: 400 }
            )
          }
        }
      }

      // 检查是否有默认变体
      const defaultVariants = variants.filter(v => v.isDefault)
      if (defaultVariants.length === 0) {
        // 如果没有指定默认变体，将第一个设为默认
        variants[0].isDefault = true
      } else if (defaultVariants.length > 1) {
        return NextResponse.json(
          { error: '只能有一个默认变体' },
          { status: 400 }
        )
      }
    }

    // 使用事务创建商品和变体
    const result = await prisma.$transaction(async (tx) => {
      // 创建商品
      const product = await tx.product.create({
        data: {
          title,
          description,
          images,
          price,
          category: category || 'GENERAL',
          condition: condition || 'NEW',
          city,
          district,
          shippingFrom,
          stock: hasVariants ? 0 : stock, // 如果有变体，商品本身库存为0
          hasVariants: hasVariants || false,
          sellerId: session.user.id,
          status: 'AVAILABLE',
          reviewStatus: 'APPROVED' // MVP测试环境直接审核通过
        }
      })

      // 如果有变体，创建变体
      if (hasVariants && variants && variants.length > 0) {
        for (const variantData of variants) {
          // 创建变体
          const variant = await tx.productVariant.create({
            data: {
              productId: product.id,
              sku: variantData.sku || null,
              price: variantData.price,
              stock: variantData.stock,
              status: variantData.stock > 0 ? 'AVAILABLE' : 'SOLD_OUT',
              isDefault: variantData.isDefault || false
            }
          })

          // 创建变体属性
          if (variantData.attributes && variantData.attributes.length > 0) {
            await tx.variantAttribute.createMany({
              data: variantData.attributes.map((attr: any) => ({
                variantId: variant.id,
                name: attr.name,
                value: attr.value
              }))
            })
          }
        }
      }

      // 获取完整的商品信息
      const fullProduct = await tx.product.findUnique({
        where: { id: product.id },
        include: {
          seller: {
            select: {
              id: true,
              name: true,
              creditScore: true,
              city: true,
              district: true
            }
          },
          variants: {
            include: {
              attributes: true
            },
            orderBy: [
              { isDefault: 'desc' },
              { createdAt: 'asc' }
            ]
          }
        }
      })

      // 清理数据以确保JSON序列化
      const cleanProduct = {
        ...fullProduct,
        createdAt: fullProduct?.createdAt?.toISOString(),
        updatedAt: fullProduct?.updatedAt?.toISOString(),
        seller: fullProduct?.seller ? {
          ...fullProduct.seller,
          createdAt: undefined,
          updatedAt: undefined
        } : null,
        variants: fullProduct?.variants?.map(variant => ({
          ...variant,
          createdAt: variant.createdAt?.toISOString(),
          updatedAt: variant.updatedAt?.toISOString(),
          attributes: variant.attributes?.map((attr: any) => ({
            ...attr,
            createdAt: attr.createdAt?.toISOString()
          }))
        }))
      }

      return cleanProduct
    })

    return NextResponse.json(result, { status: 201 })

  } catch (error) {
    console.error('Create product error:', error)
    return NextResponse.json(
      { error: '创建商品失败' },
      { status: 500 }
    )
  }
}
