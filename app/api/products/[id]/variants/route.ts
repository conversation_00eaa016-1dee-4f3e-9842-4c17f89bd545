import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createProductVariant, getProductVariants } from '@/lib/variants'

// 获取商品的所有变体
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params

    const result = await getProductVariants(productId)
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result.variants)
  } catch (error) {
    console.error('获取商品变体失败:', error)
    return NextResponse.json(
      { error: '获取变体失败' },
      { status: 500 }
    )
  }
}

// 创建商品变体
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: productId } = await params
    const body = await request.json()

    // 验证商品所有权
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const product = await prisma.product.findFirst({
      where: {
        id: productId,
        sellerId: user.id
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在或无权限' },
        { status: 404 }
      )
    }

    // 验证请求数据
    const { sku, price, stock, status, isDefault, attributes } = body

    if (typeof price !== 'number' || price <= 0) {
      return NextResponse.json(
        { error: '价格必须为正数' },
        { status: 400 }
      )
    }

    if (typeof stock !== 'number' || stock < 0) {
      return NextResponse.json(
        { error: '库存必须为非负数' },
        { status: 400 }
      )
    }

    if (!Array.isArray(attributes) || attributes.length === 0) {
      return NextResponse.json(
        { error: '变体属性不能为空' },
        { status: 400 }
      )
    }

    // 验证属性格式
    for (const attr of attributes) {
      if (!attr.name || !attr.value) {
        return NextResponse.json(
          { error: '属性名称和值不能为空' },
          { status: 400 }
        )
      }
    }

    // 检查SKU是否重复
    if (sku) {
      const existingVariant = await prisma.productVariant.findFirst({
        where: {
          productId,
          sku
        }
      })

      if (existingVariant) {
        return NextResponse.json(
          { error: 'SKU已存在' },
          { status: 400 }
        )
      }
    }

    // 创建变体
    const result = await createProductVariant(productId, {
      sku,
      price,
      stock,
      status: status || 'AVAILABLE',
      isDefault: isDefault || false,
      attributes
    })

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    // 获取完整的变体信息
    const createdVariant = await prisma.productVariant.findUnique({
      where: { id: result.variant?.id },
      include: {
        attributes: true
      }
    })

    return NextResponse.json(createdVariant, { status: 201 })
  } catch (error) {
    console.error('创建商品变体失败:', error)
    return NextResponse.json(
      { error: '创建变体失败' },
      { status: 500 }
    )
  }
}
