import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取单个商品变体详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; variantId: string }> }
) {
  try {
    const { id, variantId } = await params
    
    const variant = await prisma.productVariant.findUnique({
      where: {
        id: variantId,
        productId: id
      },
      include: {
        attributes: true
      }
    })

    if (!variant) {
      return NextResponse.json(
        { error: '商品变体不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(variant)

  } catch (error) {
    console.error('Get product variant error:', error)
    return NextResponse.json(
      { error: '获取商品变体详情失败' },
      { status: 500 }
    )
  }
}
