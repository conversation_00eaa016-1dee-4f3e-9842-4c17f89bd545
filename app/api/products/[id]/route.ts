import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { checkAndUpdateProductStock, createStockNotification } from '@/lib/inventory'

// 获取单个商品详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const product = await prisma.product.findUnique({
      where: {
        id: id
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            creditScore: true,
            city: true,
            district: true
          }
        },
        variants: {
          include: {
            attributes: true
          },
          orderBy: [
            { isDefault: 'desc' },
            { createdAt: 'asc' }
          ]
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(product)

  } catch (error) {
    console.error('Get product error:', error)
    return NextResponse.json(
      { error: '获取商品详情失败' },
      { status: 500 }
    )
  }
}

// 更新商品信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    // 严格的身份验证
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录，请先登录' },
        { status: 401 }
      )
    }

    // 验证用户状态
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { status: true, bannedAt: true, bannedUntil: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 401 }
      )
    }

    if (user.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: '账户状态异常，无法编辑商品' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      images,
      price,
      category,
      condition,
      city,
      district,
      shippingFrom,
      stock,
      status,
      hasVariants,
      variants
    } = body

    // 验证商品是否存在且属于当前用户
    const existingProduct = await prisma.product.findUnique({
      where: { id: id },
      select: {
        id: true,
        sellerId: true,
        title: true,
        status: true
      }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    // 严格的所有权验证
    if (existingProduct.sellerId !== session.user.id) {
      console.warn(`用户 ${session.user.id} 尝试编辑不属于自己的商品 ${id}`)
      return NextResponse.json(
        { error: '无权限编辑此商品' },
        { status: 403 }
      )
    }

    // 验证变体数据
    if (hasVariants) {
      if (!Array.isArray(variants) || variants.length === 0) {
        return NextResponse.json(
          { error: '启用变体时必须至少添加一个变体' },
          { status: 400 }
        )
      }

      // 验证每个变体
      for (const variant of variants) {
        if (!variant.attributes || !Array.isArray(variant.attributes) || variant.attributes.length === 0) {
          return NextResponse.json(
            { error: '每个变体必须至少有一个属性' },
            { status: 400 }
          )
        }

        if (typeof variant.price !== 'number' || variant.price <= 0) {
          return NextResponse.json(
            { error: '变体价格必须为正数' },
            { status: 400 }
          )
        }

        if (typeof variant.stock !== 'number' || variant.stock < 0) {
          return NextResponse.json(
            { error: '变体库存不能为负数' },
            { status: 400 }
          )
        }
      }
    }

    // 使用事务更新商品和变体
    const result = await prisma.$transaction(async (tx) => {
      // 更新商品基本信息
      const updatedProduct = await tx.product.update({
        where: { id: id },
        data: {
          title,
          description,
          images,
          price,
          category: category || 'GENERAL',
          condition: condition || 'NEW',
          city,
          district,
          shippingFrom,
          stock: hasVariants ? 0 : stock, // 如果有变体，商品本身库存为0
          status: status || 'AVAILABLE',
          hasVariants: hasVariants || false,
          updatedAt: new Date()
        }
      })

      // 处理变体
      if (hasVariants && variants && variants.length > 0) {
        // 删除现有变体（如果有的话）
        await tx.productVariant.deleteMany({
          where: { productId: id }
        })

        // 创建新变体
        for (const variantData of variants) {
          const variant = await tx.productVariant.create({
            data: {
              productId: id,
              sku: variantData.sku || null,
              price: variantData.price,
              stock: variantData.stock,
              status: variantData.stock > 0 ? 'AVAILABLE' : 'SOLD_OUT',
              isDefault: variantData.isDefault || false
            }
          })

          // 创建变体属性
          if (variantData.attributes && variantData.attributes.length > 0) {
            await tx.variantAttribute.createMany({
              data: variantData.attributes.map((attr: any) => ({
                variantId: variant.id,
                name: attr.name,
                value: attr.value
              }))
            })
          }
        }
      } else if (!hasVariants) {
        // 如果禁用了变体，删除所有变体
        await tx.productVariant.deleteMany({
          where: { productId: id }
        })
      }

      // 获取完整的商品信息
      const fullProduct = await tx.product.findUnique({
        where: { id: id },
        include: {
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          variants: {
            include: {
              attributes: true
            },
            orderBy: [
              { isDefault: 'desc' },
              { createdAt: 'asc' }
            ]
          }
        }
      })

      return fullProduct
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Update product error:', error)
    return NextResponse.json(
      { error: '更新商品失败' },
      { status: 500 }
    )
  }
}

// 删除商品
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    // 严格的身份验证
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录，请先登录' },
        { status: 401 }
      )
    }

    // 验证用户状态
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { status: true, bannedAt: true, bannedUntil: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 401 }
      )
    }

    if (user.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: '账户状态异常，无法删除商品' },
        { status: 403 }
      )
    }

    // 验证商品是否存在且属于当前用户
    const existingProduct = await prisma.product.findUnique({
      where: { id: id },
      include: {
        orders: {
          where: {
            status: {
              in: ['PENDING', 'PAID', 'SHIPPED']
            }
          }
        }
      }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    // 严格的所有权验证
    if (existingProduct.sellerId !== session.user.id) {
      console.warn(`用户 ${session.user.id} 尝试删除不属于自己的商品 ${id}`)
      return NextResponse.json(
        { error: '无权限删除此商品' },
        { status: 403 }
      )
    }

    // 检查是否有进行中的订单
    if (existingProduct.orders.length > 0) {
      return NextResponse.json(
        { error: '该商品有进行中的订单，无法删除' },
        { status: 400 }
      )
    }

    // 删除商品
    await prisma.product.delete({
      where: { id: id }
    })

    return NextResponse.json({
      message: '商品删除成功'
    })

  } catch (error) {
    console.error('Delete product error:', error)
    return NextResponse.json(
      { error: '删除商品失败' },
      { status: 500 }
    )
  }
}
