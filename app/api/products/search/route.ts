import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 专门的商品搜索API
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const keyword = searchParams.get('keyword')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!keyword || keyword.trim() === '') {
      return NextResponse.json({ suggestions: [] })
    }

    // 搜索建议 - 基于商品标题的模糊匹配
    const suggestions = await prisma.product.findMany({
      where: {
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        title: {
          contains: keyword.trim()
        }
      },
      select: {
        id: true,
        title: true,
        price: true,
        city: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: Math.min(limit, 20) // 最多返回20个建议
    })

    // 提取关键词建议（基于已有商品标题）
    const titleKeywords = suggestions
      .map(product => product.title)
      .flatMap(title => title.split(/\s+/))
      .filter(word => word.length > 1 && word.toLowerCase().includes(keyword.toLowerCase()))
      .slice(0, 5) // 最多5个关键词建议

    return NextResponse.json({
      suggestions,
      keywords: [...new Set(titleKeywords)] // 去重
    })

  } catch (error) {
    console.error('Search suggestions error:', error)
    return NextResponse.json(
      { error: '搜索建议获取失败' },
      { status: 500 }
    )
  }
}

// 获取热门搜索关键词
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { keyword, resultCount = 0 } = body

    // 如果提供了关键词，记录搜索
    if (keyword && keyword.trim()) {
      await recordSearchKeyword(keyword.trim(), resultCount)
    }

    // 获取热门关键词
    const hotKeywords = await getHotKeywords()

    return NextResponse.json({
      success: true,
      hotKeywords,
      total: hotKeywords.length
    })

  } catch (error) {
    console.error('Hot keywords error:', error)
    return NextResponse.json(
      { error: '热门关键词获取失败' },
      { status: 500 }
    )
  }
}

/**
 * 记录搜索关键词
 */
async function recordSearchKeyword(keyword: string, resultCount: number) {
  try {
    // 尝试使用数据库记录
    await prisma.searchKeyword.upsert({
      where: { keyword },
      update: {
        searchCount: { increment: 1 },
        resultCount: Math.max(resultCount, 0),
        updatedAt: new Date()
      },
      create: {
        keyword,
        searchCount: 1,
        resultCount: Math.max(resultCount, 0),
        category: categorizeKeyword(keyword)
      }
    })

    // 异步更新热门状态
    updateHotKeywordStatus().catch(error => {
      console.error('更新热门关键词状态失败:', error)
    })
  } catch (error) {
    // 如果数据库操作失败，记录到日志
    console.log(`搜索关键词记录: ${keyword} (结果数: ${resultCount})`)
  }
}

/**
 * 获取热门关键词
 */
async function getHotKeywords(limit: number = 10) {
  try {
    // 首先尝试获取标记为热门的关键词
    const hotKeywords = await prisma.searchKeyword.findMany({
      where: { isHot: true },
      orderBy: [
        { searchCount: 'desc' },
        { updatedAt: 'desc' }
      ],
      take: limit,
      select: {
        keyword: true,
        searchCount: true,
        category: true
      }
    })

    // 如果热门关键词不足，补充搜索次数最多的关键词
    if (hotKeywords.length < limit) {
      const additionalKeywords = await prisma.searchKeyword.findMany({
        where: {
          isHot: false,
          searchCount: { gt: 0 }
        },
        orderBy: [
          { searchCount: 'desc' },
          { updatedAt: 'desc' }
        ],
        take: limit - hotKeywords.length,
        select: {
          keyword: true,
          searchCount: true,
          category: true
        }
      })

      hotKeywords.push(...additionalKeywords)
    }

    // 如果仍然不足，返回默认关键词
    if (hotKeywords.length === 0) {
      return getDefaultKeywords()
    }

    return hotKeywords.map(item => ({
      keyword: item.keyword,
      count: item.searchCount,
      category: item.category
    }))
  } catch (error) {
    console.log('数据库查询失败，返回默认热门关键词')
    return getDefaultKeywords()
  }
}

/**
 * 更新热门关键词状态
 */
async function updateHotKeywordStatus() {
  try {
    // 获取搜索次数统计
    const stats = await prisma.searchKeyword.aggregate({
      _avg: { searchCount: true },
      _max: { searchCount: true }
    })

    const avgSearchCount = stats._avg.searchCount || 0
    const maxSearchCount = stats._max.searchCount || 0

    // 热门阈值：平均搜索次数的2倍或最大搜索次数的30%，取较小值
    const hotThreshold = Math.min(
      avgSearchCount * 2,
      maxSearchCount * 0.3
    )

    // 更新热门状态
    await prisma.$transaction([
      // 先清除所有热门标记
      prisma.searchKeyword.updateMany({
        where: { isHot: true },
        data: { isHot: false }
      }),
      // 标记新的热门关键词
      prisma.searchKeyword.updateMany({
        where: {
          searchCount: { gte: Math.max(hotThreshold, 3) } // 至少3次搜索
        },
        data: { isHot: true }
      })
    ])

    console.log(`热门关键词状态已更新，阈值: ${hotThreshold}`)
  } catch (error) {
    console.error('更新热门关键词状态失败:', error)
  }
}

/**
 * 关键词分类
 */
function categorizeKeyword(keyword: string): string {
  const categories = {
    '电子产品': ['手机', '电脑', '笔记本', '平板', '耳机', '音响', '相机', '摄像头', '键盘', '鼠标', '显示器', '电视'],
    '服装配饰': ['衣服', '裤子', '裙子', '鞋子', '包包', '手表', '首饰', '帽子', '围巾'],
    '图书文具': ['书籍', '小说', '教材', '笔', '本子', '文具', '办公用品'],
    '运动健身': ['运动鞋', '健身器材', '球类', '户外用品', '运动服'],
    '家居生活': ['家具', '装饰', '厨具', '清洁用品', '床上用品'],
    '美妆护肤': ['化妆品', '护肤品', '香水', '美容工具'],
    '食品饮料': ['零食', '饮料', '茶叶', '咖啡', '保健品'],
    '游戏娱乐': ['游戏', '玩具', '模型', '收藏品', '乐器']
  }

  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(k => keyword.includes(k) || k.includes(keyword))) {
      return category
    }
  }

  return '其他'
}

/**
 * 获取默认关键词
 */
function getDefaultKeywords() {
  return [
    { keyword: '手机', count: 0, category: '电子产品' },
    { keyword: '电脑', count: 0, category: '电子产品' },
    { keyword: '耳机', count: 0, category: '电子产品' },
    { keyword: '键盘', count: 0, category: '电子产品' },
    { keyword: '鼠标', count: 0, category: '电子产品' },
    { keyword: '显示器', count: 0, category: '电子产品' },
    { keyword: '相机', count: 0, category: '电子产品' },
    { keyword: '游戏', count: 0, category: '游戏娱乐' },
    { keyword: '书籍', count: 0, category: '图书文具' },
    { keyword: '衣服', count: 0, category: '服装配饰' }
  ]
}
