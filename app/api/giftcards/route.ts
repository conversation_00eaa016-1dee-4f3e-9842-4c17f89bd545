import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 礼品卡配置已移至数据库，此API现在从数据库获取礼品卡商品

// 获取可用礼品卡列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 获取用户余额
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { depositBalance: true }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 从数据库获取可用的礼品卡商品
    const giftCardProducts = await (prisma as any).giftCardProduct.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        name: true,
        description: true,
        faceValue: true,
        salePrice: true,
        stock: true,
        productType: true
      },
      orderBy: {
        salePrice: 'asc'
      }
    })

    // 转换为前端需要的格式
    const giftCards = giftCardProducts.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      value: product.faceValue,
      cost: product.salePrice,
      type: product.productType,
      category: 'giftcard',
      available: product.stock > 0,
      stock: product.stock
    }))

    return NextResponse.json({
      success: true,
      data: {
        giftCards: giftCards,
        userBalance: {
          available: user.depositBalance,
          total: user.depositBalance
        }
      }
    })
  } catch (error) {
    console.error('获取礼品卡列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取礼品卡列表失败' },
      { status: 500 }
    )
  }
}

// 兑换礼品卡
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { giftCardId, deliveryMethod = 'redemption_code', quantity = 1 } = await request.json()

    if (!giftCardId) {
      return NextResponse.json(
        { success: false, error: '请选择要兑换的礼品卡' },
        { status: 400 }
      )
    }

    // 验证发货方式
    const validDeliveryMethods = ['redemption_code', 'gift_card', 'direct_recharge']
    if (!validDeliveryMethods.includes(deliveryMethod)) {
      return NextResponse.json(
        { success: false, error: '无效的发货方式' },
        { status: 400 }
      )
    }

    // 验证数量
    if (quantity <= 0 || quantity > 10) {
      return NextResponse.json(
        { success: false, error: '购买数量必须在1-10之间' },
        { status: 400 }
      )
    }

    // 从数据库查找礼品卡商品
    const giftCardProduct = await (prisma as any).giftCardProduct.findFirst({
      where: {
        id: giftCardId,
        isActive: true
      }
    })

    if (!giftCardProduct) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在或已下架' },
        { status: 404 }
      )
    }

    if (giftCardProduct.stock <= 0) {
      return NextResponse.json(
        { success: false, error: '该礼品卡库存不足' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { 
        id: true, 
        depositBalance: true,
        email: true,
        name: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 计算总费用
    const totalCost = giftCardProduct.salePrice * quantity

    // 检查余额是否足够
    if (user.depositBalance < totalCost) {
      return NextResponse.json(
        { success: false, error: `余额不足，需要 ${totalCost} USDT，当前余额 ${user.depositBalance} USDT` },
        { status: 400 }
      )
    }

    // 使用事务处理兑换
    const result = await prisma.$transaction(async (tx) => {
      // 扣除保证金
      const updatedUser = await tx.user.update({
        where: { id: session.user.id },
        data: {
          depositBalance: {
            decrement: totalCost
          }
        }
      })

      // 减少商品库存
      await (tx as any).giftCardProduct.update({
        where: { id: giftCardProduct.id },
        data: {
          stock: {
            decrement: quantity
          }
        }
      })

      // 根据发货方式生成相应的礼品卡
      let deliveryData: any = {}
      let giftCardIds: string[] = []

      if (deliveryMethod === 'redemption_code') {
        // 生成兑换码
        const redemptionCodes = []
        for (let i = 0; i < quantity; i++) {
          // 生成16位随机字母数字组合
          const code = Array.from({length: 16}, () =>
            '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 36)]
          ).join('')
          const redemptionCode = await (tx as any).redemptionCode.create({
            data: {
              codeValue: code,
              codeType: 'GIFT_CARD',
              rewardType: 'BALANCE',
              rewardValue: giftCardProduct.faceValue,
              rewardUnit: 'USDT',
              distributionType: 'MANUAL',
              validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年有效期
              status: 'ACTIVE',
              createdById: session.user.id,
              title: `${giftCardProduct.name} 兑换码`,
              description: `面值 ${giftCardProduct.faceValue} USDT 的礼品卡兑换码`
            }
          })
          redemptionCodes.push({
            code: redemptionCode.codeValue,
            value: redemptionCode.rewardValue,
            expiresAt: redemptionCode.validUntil
          })
        }
        deliveryData.redemptionCodes = redemptionCodes

      } else if (deliveryMethod === 'gift_card') {
        // 生成礼品卡
        const giftCards = []
        for (let i = 0; i < quantity; i++) {
          // 生成16位随机字母数字组合
          const cardCode = Array.from({length: 16}, () =>
            '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 36)]
          ).join('')
          const giftCard = await (tx as any).giftCard.create({
            data: {
              cardCode,
              faceValue: giftCardProduct.faceValue,
              status: 'SOLD',
              validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年有效期
              soldToId: session.user.id,
              soldAt: new Date(),
              createdById: session.user.id,
              productId: giftCardProduct.id
            }
          })
          giftCards.push({
            id: giftCard.id,
            cardCode: giftCard.cardCode,
            faceValue: giftCard.faceValue,
            validUntil: giftCard.validUntil
          })
          giftCardIds.push(giftCard.id)
        }
        deliveryData.giftCards = giftCards

      } else if (deliveryMethod === 'direct_recharge') {
        // 直接充值到账户
        const rechargeAmount = giftCardProduct.faceValue * quantity
        await tx.user.update({
          where: { id: session.user.id },
          data: {
            depositBalance: {
              increment: rechargeAmount
            }
          }
        })
        deliveryData.rechargedAmount = rechargeAmount
      }

      // 记录交易流水
      await tx.fundTransaction.create({
        data: {
          userId: session.user.id,
          type: 'GIFT_CARD_EXCHANGE',
          amount: -totalCost,
          description: `兑换礼品卡: ${giftCardProduct.name} x${quantity} (${deliveryMethod})`,
          metadata: {
            giftCardProductId: giftCardProduct.id,
            giftCardName: giftCardProduct.name,
            giftCardValue: giftCardProduct.faceValue,
            quantity,
            deliveryMethod,
            totalCost,
            balanceAfter: updatedUser.depositBalance,
            giftCardIds: giftCardIds
          }
        }
      })

      return {
        deliveryData,
        newBalance: updatedUser.depositBalance,
        quantity,
        deliveryMethod
      }
    })

    // 根据发货方式生成不同的成功消息
    let successMessage = `成功购买 ${quantity} 张 ${giftCardProduct.name}！`
    if (result.deliveryMethod === 'redemption_code') {
      successMessage += ' 兑换码已生成，请妥善保管。'
    } else if (result.deliveryMethod === 'gift_card') {
      successMessage += ' 礼品卡已生成，请妥善保管。'
    } else if (result.deliveryMethod === 'direct_recharge') {
      successMessage += ` 已直接充值 ${result.deliveryData.rechargedAmount} USDT 到您的账户。`
    }

    return NextResponse.json({
      success: true,
      message: successMessage,
      data: {
        ...result.deliveryData,
        newBalance: result.newBalance,
        quantity: result.quantity,
        deliveryMethod: result.deliveryMethod,
        giftCard: {
          id: giftCardProduct.id,
          name: giftCardProduct.name,
          value: giftCardProduct.faceValue,
          cost: giftCardProduct.salePrice,
          totalCost: totalCost
        }
      }
    })

  } catch (error) {
    console.error('兑换礼品卡失败:', error)
    return NextResponse.json(
      { success: false, error: '兑换礼品卡失败' },
      { status: 500 }
    )
  }
}

// 获取用户的礼品卡兑换历史
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // 获取兑换历史
    const transactions = await prisma.fundTransaction.findMany({
      where: {
        userId: session.user.id,
        type: 'GIFT_CARD_EXCHANGE'
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.fundTransaction.count({
      where: {
        userId: session.user.id,
        type: 'GIFT_CARD_EXCHANGE'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取兑换历史失败:', error)
    return NextResponse.json(
      { success: false, error: '获取兑换历史失败' },
      { status: 500 }
    )
  }
}
