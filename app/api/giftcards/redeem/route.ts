import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 查询礼品卡信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const cardCode = searchParams.get('cardCode')

    if (!cardCode || typeof cardCode !== 'string') {
      return NextResponse.json(
        { success: false, error: '请输入有效的礼品卡码' },
        { status: 400 }
      )
    }

    // 验证卡码格式（16位字母数字组合）
    const normalizedCardCode = cardCode.toUpperCase().trim()
    if (!/^[A-Z0-9]{16}$/.test(normalizedCardCode)) {
      return NextResponse.json(
        { success: false, error: '礼品卡码格式不正确，应为16位字母数字组合' },
        { status: 400 }
      )
    }

    // 查找礼品卡
    const giftCard = await (prisma as any).giftCard.findUnique({
      where: { cardCode: normalizedCardCode },
      include: {
        soldTo: {
          select: { id: true, name: true }
        },
        redeemedBy: {
          select: { id: true, name: true }
        },
        assignedTo: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    if (!giftCard) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在' },
        { status: 404 }
      )
    }

    // 检查礼品卡状态
    let statusMessage = ''
    let canRedeem = false

    if (giftCard.status === 'GENERATED') {
      statusMessage = '未售出'
      canRedeem = false
    } else if (giftCard.status === 'SOLD') {
      if (giftCard.redeemedAt) {
        statusMessage = `已兑换 (${new Date(giftCard.redeemedAt).toLocaleDateString('zh-CN')})`
        canRedeem = false
      } else if (new Date() > new Date(giftCard.validUntil)) {
        statusMessage = '已过期'
        canRedeem = false
      } else {
        statusMessage = '可兑换'
        canRedeem = true
      }
    } else {
      statusMessage = '状态异常'
      canRedeem = false
    }

    return NextResponse.json({
      success: true,
      data: {
        id: giftCard.id,
        cardCode: giftCard.cardCode,
        faceValue: giftCard.faceValue,
        status: giftCard.status,
        validUntil: giftCard.validUntil,
        soldAt: giftCard.soldAt,
        redeemedAt: giftCard.redeemedAt,
        redeemedValue: giftCard.redeemedValue,
        canRedeem,
        statusMessage,
        soldTo: giftCard.soldTo,
        redeemedBy: giftCard.redeemedBy,
        assignedTo: giftCard.assignedTo
      }
    })

  } catch (error) {
    console.error('查询礼品卡失败:', error)
    return NextResponse.json(
      { success: false, error: '查询礼品卡失败' },
      { status: 500 }
    )
  }
}

// 兑换礼品卡
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { cardCode } = await request.json()

    if (!cardCode || typeof cardCode !== 'string') {
      return NextResponse.json(
        { success: false, error: '请输入有效的礼品卡码' },
        { status: 400 }
      )
    }

    // 验证卡码格式（支持多种格式）
    const normalizedCardCode = cardCode.toUpperCase().trim()
    if (normalizedCardCode.length < 10) {
      return NextResponse.json(
        { success: false, error: '礼品卡码格式不正确' },
        { status: 400 }
      )
    }

    // 使用事务处理兑换
    const result = await prisma.$transaction(async (tx) => {
      // 查找礼品卡
      const giftCard = await (tx as any).giftCard.findUnique({
        where: { cardCode: normalizedCardCode },
        include: {
          soldTo: {
            select: { id: true, name: true }
          },
          redeemedBy: {
            select: { id: true, name: true }
          },
          assignedTo: {
            select: { id: true, name: true, email: true }
          }
        }
      })

      if (!giftCard) {
        throw new Error('礼品卡不存在')
      }

      // 检查礼品卡状态
      if (giftCard.status === 'REDEEMED') {
        throw new Error('礼品卡已被兑换')
      }

      if (giftCard.status === 'EXPIRED') {
        throw new Error('礼品卡已过期')
      }

      if (giftCard.status !== 'SOLD' && giftCard.status !== 'GENERATED') {
        throw new Error('礼品卡状态异常，无法兑换')
      }

      // 检查有效期
      if (new Date() > giftCard.validUntil) {
        // 自动过期
        await (tx as any).giftCard.update({
          where: { id: giftCard.id },
          data: { status: 'EXPIRED' }
        })
        throw new Error('礼品卡已过期')
      }

      // 检查是否有指定用户限制
      if (giftCard.assignedToId && giftCard.assignedToId !== session.user.id) {
        throw new Error('该礼品卡已指定给其他用户，您无权兑换')
      }

      // 检查是否为自己购买的礼品卡（可选限制）
      // if (giftCard.soldToId === session.user.id) {
      //   throw new Error('不能兑换自己购买的礼品卡')
      // }

      // 更新礼品卡状态
      const updatedGiftCard = await (tx as any).giftCard.update({
        where: { id: giftCard.id },
        data: {
          status: 'REDEEMED',
          redeemedAt: new Date(),
          redeemedById: session.user.id,
          redeemedValue: giftCard.faceValue
        }
      })

      // 增加用户余额
      await (tx as any).user.update({
        where: { id: session.user.id },
        data: {
          depositBalance: {
            increment: giftCard.faceValue
          }
        }
      })

      // 创建交易记录
      await (tx as any).giftCardTransaction.create({
        data: {
          giftCardId: giftCard.id,
          transactionType: 'REDEMPTION',
          amount: giftCard.faceValue,
          userId: session.user.id,
          description: `用户兑换礼品卡，获得 ${giftCard.faceValue} USDT`,
          metadata: {
            cardCode: giftCard.cardCode,
            originalFaceValue: giftCard.faceValue,
            redeemedAt: new Date().toISOString()
          }
        }
      })

      // 创建用户余额变动记录
      await (tx as any).fundTransaction.create({
        data: {
          userId: session.user.id,
          type: 'GIFT_CARD_REDEMPTION',
          amount: giftCard.faceValue,
          description: `兑换礼品卡: ${giftCard.cardCode}`,
          relatedId: giftCard.id,
          metadata: {
            relatedType: 'GIFT_CARD',
            cardCode: giftCard.cardCode,
            faceValue: giftCard.faceValue
          }
        }
      })

      return {
        giftCard: updatedGiftCard,
        redeemedAmount: giftCard.faceValue
      }
    })

    return NextResponse.json({
      success: true,
      message: `成功兑换礼品卡，获得 ${result.redeemedAmount} USDT`,
      data: {
        cardCode: result.giftCard.cardCode,
        redeemedAmount: result.redeemedAmount,
        redeemedAt: result.giftCard.redeemedAt
      }
    })

  } catch (error) {
    console.error('兑换礼品卡失败:', error)
    
    // 根据错误类型返回不同的错误信息
    const errorMessage = error instanceof Error ? error.message : '兑换礼品卡失败'
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 400 }
    )
  }
}

