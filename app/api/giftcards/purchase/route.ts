import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 生成16位随机字母数字组合
function generateCode(): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  return Array.from({length: 16}, () =>
    chars[Math.floor(Math.random() * chars.length)]
  ).join('')
}

// 购买礼品卡
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { faceValue, quantity = 1, paymentMethod = 'BALANCE', deliveryMethod = 'redemption_code' } = await request.json()

    if (!faceValue || faceValue <= 0) {
      return NextResponse.json(
        { success: false, error: '请选择有效的面值' },
        { status: 400 }
      )
    }

    if (quantity <= 0 || quantity > 10) {
      return NextResponse.json(
        { success: false, error: '单次最多购买10张礼品卡' },
        { status: 400 }
      )
    }

    // 验证发货方式
    const validDeliveryMethods = ['redemption_code', 'gift_card', 'direct_recharge']
    if (!validDeliveryMethods.includes(deliveryMethod)) {
      return NextResponse.json(
        { success: false, error: '无效的发货方式' },
        { status: 400 }
      )
    }

    // 支持的面值
    const supportedValues = [10, 20, 50, 100, 200, 500, 1000]
    if (!supportedValues.includes(faceValue)) {
      return NextResponse.json(
        { success: false, error: '不支持的面值' },
        { status: 400 }
      )
    }

    const totalAmount = faceValue * quantity

    // 使用事务处理购买
    const result = await prisma.$transaction(async (tx) => {
      // 获取用户信息
      const user = await tx.user.findUnique({
        where: { id: session.user.id },
        select: { depositBalance: true, name: true, email: true }
      })

      if (!user) {
        throw new Error('用户不存在')
      }

      // 检查余额
      if (paymentMethod === 'BALANCE') {
        if (user.depositBalance < totalAmount) {
          throw new Error('余额不足')
        }
      }

      // 查找可用的礼品卡
      const availableGiftCards = await tx.giftCard.findMany({
        where: {
          faceValue,
          status: 'GENERATED'
        },
        take: quantity,
        orderBy: {
          createdAt: 'asc'
        }
      })

      if (availableGiftCards.length < quantity) {
        throw new Error(`库存不足，仅有 ${availableGiftCards.length} 张可用`)
      }

      // 查找或创建礼品卡虚拟产品
      let virtualProduct = await tx.product.findFirst({
        where: {
          title: '礼品卡购买',
          category: 'VIRTUAL_DIGITAL'
        }
      })

      if (!virtualProduct) {
        virtualProduct = await tx.product.create({
          data: {
            title: '礼品卡购买',
            description: '平台礼品卡购买服务',
            price: 0, // 价格由礼品卡面值决定
            category: 'VIRTUAL_DIGITAL',
            condition: 'NOT_APPLICABLE',
            status: 'AVAILABLE',
            reviewStatus: 'APPROVED',
            sellerId: session.user.id,
            images: null,
            city: '平台',
            district: '虚拟'
          }
        })
      }

      // 创建订单
      const order = await tx.order.create({
        data: {
          orderNumber: `GC${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
          productId: virtualProduct.id,
          buyerId: session.user.id,
          sellerId: session.user.id, // 礼品卡由平台销售
          status: 'PENDING_PAYMENT',
          totalAmount,
          productPrice: totalAmount,
          shippingFee: 0,
          platformFee: 0,
          paymentMethod,
          metadata: {
            deliveryMethod,
            quantity,
            faceValue,
            purchaseType: 'GIFT_CARD'
          }
        }
      })

      // 更新礼品卡状态
      const giftCardIds = availableGiftCards.map(card => card.id)
      await tx.giftCard.updateMany({
        where: {
          id: { in: giftCardIds }
        },
        data: {
          status: 'SOLD',
          soldAt: new Date(),
          soldToId: session.user.id,
          saleOrderId: order.id
        }
      })

      // 处理支付
      if (paymentMethod === 'BALANCE') {
        // 扣除用户余额
        await tx.user.update({
          where: { id: session.user.id },
          data: {
            depositBalance: {
              decrement: totalAmount
            }
          }
        })

        // 创建余额变动记录
        await tx.fundTransaction.create({
          data: {
            userId: session.user.id,
            type: 'GIFT_CARD_PURCHASE',
            amount: -totalAmount,
            description: `购买礼品卡 ${quantity} 张，面值 ${faceValue} USDT`,
            relatedId: order.id,
            metadata: {
              relatedType: 'ORDER',
              quantity,
              faceValue,
              totalAmount
            }
          }
        })

        // 更新订单状态为已支付
        await tx.order.update({
          where: { id: order.id },
          data: {
            status: 'COMPLETED',
            paymentConfirmed: true
          }
        })

        // 根据发货方式处理
        if (deliveryMethod === 'direct_recharge') {
          // 直充到账户：直接增加用户余额
          await tx.user.update({
            where: { id: session.user.id },
            data: {
              depositBalance: {
                increment: faceValue * quantity
              }
            }
          })

          // 创建直充记录
          await tx.fundTransaction.create({
            data: {
              userId: session.user.id,
              type: 'GIFT_CARD_RECHARGE',
              amount: faceValue * quantity,
              description: `礼品卡直充到账户，面值 ${faceValue} USDT × ${quantity}`,
              relatedId: order.id,
              metadata: {
                relatedType: 'GIFT_CARD_ORDER',
                deliveryMethod: 'direct_recharge',
                faceValue,
                quantity
              }
            }
          })

          // 更新礼品卡状态为已兑换
          await tx.giftCard.updateMany({
            where: { id: { in: giftCardIds } },
            data: {
              status: 'REDEEMED',
              redeemedAt: new Date(),
              redeemedById: session.user.id,
              redeemedValue: faceValue
            }
          })
        } else if (deliveryMethod === 'redemption_code') {
          // 生成兑换码
          for (let i = 0; i < quantity; i++) {
            const redemptionCode = generateCode()
            await tx.redemptionCode.create({
              data: {
                code: redemptionCode,
                type: 'GIFT_CARD',
                value: faceValue,
                isActive: true,
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年有效期
                createdById: session.user.id,
                metadata: {
                  orderId: order.id,
                  giftCardId: giftCardIds[i],
                  deliveryMethod: 'redemption_code'
                }
              }
            })
          }

          // 更新礼品卡状态为已发放
          await tx.giftCard.updateMany({
            where: { id: { in: giftCardIds } },
            data: {
              status: 'ISSUED',
              issuedAt: new Date(),
              issuedToId: session.user.id
            }
          })
        } else if (deliveryMethod === 'gift_card') {
          // 生成礼品卡记录
          for (let i = 0; i < quantity; i++) {
            const cardCode = generateCode()
            await tx.giftCard.update({
              where: { id: giftCardIds[i] },
              data: {
                cardCode: cardCode,
                status: 'ISSUED',
                issuedAt: new Date(),
                issuedToId: session.user.id,
                metadata: {
                  ...availableGiftCards[i].metadata,
                  orderId: order.id,
                  deliveryMethod: 'gift_card',
                  cardCode: cardCode
                }
              }
            })
          }
        }
      }

      // 创建礼品卡交易记录
      for (const giftCard of availableGiftCards) {
        await tx.giftCardTransaction.create({
          data: {
            giftCardId: giftCard.id,
            transactionType: 'SALE',
            amount: faceValue,
            userId: session.user.id,
            orderId: order.id,
            description: `用户购买礼品卡，面值 ${faceValue} USDT`,
            metadata: {
              cardCode: giftCard.cardCode,
              paymentMethod,
              purchasedAt: new Date().toISOString()
            }
          }
        })
      }

      // 根据发货方式获取相应的数据
      let deliveryData: any = {}

      if (deliveryMethod === 'redemption_code') {
        // 获取生成的兑换码
        const redemptionCodes = await tx.redemptionCode.findMany({
          where: {
            createdById: session.user.id,
            metadata: {
              path: ['orderId'],
              equals: order.id
            }
          },
          select: {
            code: true,
            value: true,
            expiresAt: true
          }
        })
        deliveryData.redemptionCodes = redemptionCodes
      } else if (deliveryMethod === 'gift_card') {
        // 获取更新后的礼品卡信息
        const purchasedCards = await tx.giftCard.findMany({
          where: {
            id: { in: giftCardIds }
          },
          select: {
            id: true,
            cardCode: true,
            faceValue: true,
            validUntil: true
          }
        })
        deliveryData.giftCards = purchasedCards
      } else if (deliveryMethod === 'direct_recharge') {
        // 直充不需要额外数据
        deliveryData.rechargedAmount = faceValue * quantity
      }

      return {
        order,
        deliveryData
      }
    })

    // 根据发货方式返回不同的消息和数据
    let message = ''
    let responseData: any = {
      orderId: result.order.id,
      orderNumber: result.order.orderNumber,
      totalAmount,
      deliveryMethod,
      quantity,
      faceValue
    }

    switch (deliveryMethod) {
      case 'direct_recharge':
        message = `成功购买并直充 ${faceValue * quantity} USDT 到您的账户`
        responseData.rechargedAmount = result.deliveryData.rechargedAmount
        break
      case 'redemption_code':
        message = `成功购买 ${quantity} 张礼品卡，已生成兑换码`
        responseData.redemptionCodes = result.deliveryData.redemptionCodes?.map((code: any) => ({
          code: code.code,
          value: code.value,
          expiresAt: code.expiresAt,
          type: 'redemption_code'
        })) || []
        break
      case 'gift_card':
        message = `成功购买 ${quantity} 张礼品卡，已生成礼品卡`
        responseData.giftCards = result.deliveryData.giftCards?.map((card: any) => ({
          cardCode: card.cardCode,
          faceValue: card.faceValue,
          validUntil: card.validUntil,
          type: 'gift_card'
        })) || []
        break
      default:
        message = `成功购买 ${quantity} 张礼品卡`
    }

    return NextResponse.json({
      success: true,
      message,
      data: responseData
    })

  } catch (error) {
    console.error('购买礼品卡失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '购买礼品卡失败'
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 400 }
    )
  }
}

// 获取可购买的礼品卡面值和库存
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    // 获取各面值的库存情况
    const inventory = await prisma.giftCard.groupBy({
      by: ['faceValue'],
      where: {
        status: 'GENERATED'
      },
      _count: {
        id: true
      },
      orderBy: {
        faceValue: 'asc'
      }
    })

    // 支持的面值
    const supportedValues = [10, 20, 50, 100, 200, 500, 1000]
    
    const availableOptions = supportedValues.map(value => {
      const stock = inventory.find(item => item.faceValue === value)?._count.id || 0
      return {
        faceValue: value,
        stock,
        available: stock > 0
      }
    })

    // 获取用户余额
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { depositBalance: true }
    })

    return NextResponse.json({
      success: true,
      data: {
        options: availableOptions,
        userBalance: user?.depositBalance || 0,
        maxQuantityPerPurchase: 10
      }
    })

  } catch (error) {
    console.error('获取礼品卡选项失败:', error)
    return NextResponse.json(
      { success: false, error: '获取礼品卡选项失败' },
      { status: 500 }
    )
  }
}
