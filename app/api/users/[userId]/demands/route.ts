import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的需求单列表
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || 'ALL'
    const demandType = searchParams.get('demandType')

    const session = await getServerSession(authOptions)
    const isOwnProfile = session?.user?.id === userId

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {
      userId: userId
    }

    // 如果不是自己的资料，只显示公开的需求
    if (!isOwnProfile) {
      where.status = 'OPEN'
      where.expirationTime = {
        gt: new Date()
      }
    } else {
      // 如果是自己的资料，可以按状态筛选
      if (status !== 'ALL') {
        where.status = status
      }
    }

    if (demandType && demandType !== 'ALL') {
      where.demandType = demandType
    }

    // 获取需求列表
    const [demands, total] = await Promise.all([
      prisma.demand.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              creditScore: true
            }
          },
          _count: {
            select: {
              offers: true
            }
          },
          offers: isOwnProfile ? {
            include: {
              seller: {
                select: {
                  id: true,
                  name: true,
                  creditScore: true
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            }
          } : false
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.demand.count({ where })
    ])

    // 检查过期需求并自动关闭
    const expiredDemands = demands.filter(demand => 
      demand.status === 'OPEN' && new Date() > demand.expirationTime
    )

    if (expiredDemands.length > 0) {
      await prisma.demand.updateMany({
        where: {
          id: {
            in: expiredDemands.map(d => d.id)
          }
        },
        data: {
          status: 'CLOSED'
        }
      })

      // 更新本地数据
      expiredDemands.forEach(demand => {
        demand.status = 'CLOSED'
      })
    }

    return NextResponse.json({
      demands,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      isOwnProfile
    })

  } catch (error) {
    console.error('获取用户需求列表失败:', error)
    return NextResponse.json(
      { error: '获取需求列表失败' },
      { status: 500 }
    )
  }
}
