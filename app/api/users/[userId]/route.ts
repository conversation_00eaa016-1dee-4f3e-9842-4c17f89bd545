import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 根据用户ID获取用户信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params
    const user = await prisma.user.findUnique({
      where: {
        id: userId
      },
      select: {
        id: true,
        userId: true,
        name: true,
        email: true,
        creditScore: true,
        city: true,
        district: true,
        createdAt: true,
        // 不返回敏感信息如密码等
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { error: '获取用户信息失败' },
      { status: 500 }
    )
  }
}
