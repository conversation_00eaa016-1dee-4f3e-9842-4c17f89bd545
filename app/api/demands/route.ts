import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取需求列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const demandType = searchParams.get('demandType')
    const keyword = searchParams.get('keyword')
    const status = searchParams.get('status') || 'OPEN'

    const validatedPage = Math.max(1, page)
    const validatedLimit = Math.min(Math.max(1, limit), 50)
    const skip = (validatedPage - 1) * validatedLimit

    // 构建查询条件
    const where: any = {
      status,
      expirationTime: {
        gt: new Date() // 只显示未过期的需求
      }
    }

    if (demandType && demandType !== 'ALL') {
      where.demandType = demandType
    }

    if (keyword && keyword.trim() !== '') {
      where.OR = [
        { title: { contains: keyword.trim(), mode: 'insensitive' } },
        { description: { contains: keyword.trim(), mode: 'insensitive' } }
      ]
    }

    // 获取需求列表
    const demands = await prisma.demand.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        },
        _count: {
          select: {
            offers: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: validatedLimit
    })

    // 获取总数
    const total = await prisma.demand.count({ where })

    return NextResponse.json({
      demands,
      pagination: {
        page: validatedPage,
        limit: validatedLimit,
        total,
        pages: Math.ceil(total / validatedLimit)
      }
    })

  } catch (error) {
    console.error('Get demands error:', error)
    return NextResponse.json(
      { error: '获取需求列表失败' },
      { status: 500 }
    )
  }
}

// 创建需求单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      demandType,
      subcategory,
      budget,
      deliveryMethod,
      expirationDays = 7
    } = body

    // 验证必填字段
    if (!title || title.length < 5 || title.length > 100) {
      return NextResponse.json(
        { error: '标题应为5-100个字符' },
        { status: 400 }
      )
    }

    if (!description || description.length < 10) {
      return NextResponse.json(
        { error: '描述至少10个字符' },
        { status: 400 }
      )
    }

    if (!demandType || !['buy_goods', 'hire_service', 'digital_task', 'request_info'].includes(demandType)) {
      return NextResponse.json(
        { error: '无效的需求类型' },
        { status: 400 }
      )
    }

    if (!budget || typeof budget !== 'number' || budget <= 0 || budget > 100000) {
      return NextResponse.json(
        { error: '预算应为0-100000之间的数字' },
        { status: 400 }
      )
    }

    if (!deliveryMethod || !['online', 'delivery'].includes(deliveryMethod)) {
      return NextResponse.json(
        { error: '无效的交付方式' },
        { status: 400 }
      )
    }

    // 计算过期时间
    const expirationTime = new Date()
    expirationTime.setDate(expirationTime.getDate() + Math.min(Math.max(1, expirationDays), 30))

    // 创建需求单
    const demand = await prisma.demand.create({
      data: {
        title: title.trim(),
        description: description.trim(),
        demandType,
        subcategory: subcategory?.trim() || null,
        budget,
        deliveryMethod,
        expirationTime,
        userId: session.user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        },
        _count: {
          select: {
            offers: true
          }
        }
      }
    })

    return NextResponse.json(demand, { status: 201 })

  } catch (error) {
    console.error('Create demand error:', error)
    return NextResponse.json(
      { error: '创建需求失败' },
      { status: 500 }
    )
  }
}
