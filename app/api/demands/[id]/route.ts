import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个需求详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const demand = await prisma.demand.findUnique({
      where: {
        id: params.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        },
        offers: {
          include: {
            seller: {
              select: {
                id: true,
                name: true,
                creditScore: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    })

    if (!demand) {
      return NextResponse.json(
        { error: '需求不存在' },
        { status: 404 }
      )
    }

    // 检查是否过期
    if (new Date() > demand.expirationTime && demand.status === 'OPEN') {
      // 自动关闭过期需求
      await prisma.demand.update({
        where: { id: params.id },
        data: { status: 'CLOSED' }
      })
      demand.status = 'CLOSED'
    }

    return NextResponse.json(demand)

  } catch (error) {
    console.error('Get demand error:', error)
    return NextResponse.json(
      { error: '获取需求详情失败' },
      { status: 500 }
    )
  }
}

// 更新需求状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action } = body

    // 获取需求信息
    const demand = await prisma.demand.findUnique({
      where: { id: params.id },
      include: {
        user: true
      }
    })

    if (!demand) {
      return NextResponse.json(
        { error: '需求不存在' },
        { status: 404 }
      )
    }

    if (demand.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此需求' },
        { status: 403 }
      )
    }

    let updateData: any = {}

    switch (action) {
      case 'close':
        if (demand.status !== 'OPEN') {
          return NextResponse.json(
            { error: '只能关闭开放状态的需求' },
            { status: 400 }
          )
        }
        updateData = { status: 'CLOSED' }
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    // 更新需求
    const updatedDemand = await prisma.demand.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        },
        offers: {
          include: {
            seller: {
              select: {
                id: true,
                name: true,
                creditScore: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    })

    return NextResponse.json(updatedDemand)

  } catch (error) {
    console.error('Update demand error:', error)
    return NextResponse.json(
      { error: '更新需求失败' },
      { status: 500 }
    )
  }
}

// 删除需求
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 获取需求信息
    const demand = await prisma.demand.findUnique({
      where: { id: params.id },
      include: {
        offers: true
      }
    })

    if (!demand) {
      return NextResponse.json(
        { error: '需求不存在' },
        { status: 404 }
      )
    }

    if (demand.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限删除此需求' },
        { status: 403 }
      )
    }

    // 检查是否有已接受的报价
    const acceptedOffer = demand.offers.find(offer => offer.isAccepted)
    if (acceptedOffer) {
      return NextResponse.json(
        { error: '已有接受的报价，无法删除需求' },
        { status: 400 }
      )
    }

    // 删除需求（级联删除相关报价）
    await prisma.demand.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: '需求删除成功' })

  } catch (error) {
    console.error('Delete demand error:', error)
    return NextResponse.json(
      { error: '删除需求失败' },
      { status: 500 }
    )
  }
}
