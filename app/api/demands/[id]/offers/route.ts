import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateOrderNumber } from '@/lib/utils'

// 创建需求响应
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { offerPrice, offerNote } = body

    // 验证必填字段
    if (!offerPrice || typeof offerPrice !== 'number' || offerPrice <= 0) {
      return NextResponse.json(
        { error: '报价金额必须大于0' },
        { status: 400 }
      )
    }

    if (!offerNote || offerNote.length < 10) {
      return NextResponse.json(
        { error: '报价说明至少10个字符' },
        { status: 400 }
      )
    }

    // 获取需求信息
    const demand = await prisma.demand.findUnique({
      where: { id: params.id }
    })

    if (!demand) {
      return NextResponse.json(
        { error: '需求不存在' },
        { status: 404 }
      )
    }

    if (demand.userId === session.user.id) {
      return NextResponse.json(
        { error: '不能响应自己的需求' },
        { status: 400 }
      )
    }

    if (demand.status !== 'OPEN') {
      return NextResponse.json(
        { error: '需求已关闭，无法响应' },
        { status: 400 }
      )
    }

    if (new Date() > demand.expirationTime) {
      return NextResponse.json(
        { error: '需求已过期，无法响应' },
        { status: 400 }
      )
    }

    // 检查是否已经响应过
    const existingOffer = await prisma.demandOffer.findUnique({
      where: {
        demandId_sellerId: {
          demandId: params.id,
          sellerId: session.user.id
        }
      }
    })

    if (existingOffer) {
      return NextResponse.json(
        { error: '您已经响应过此需求' },
        { status: 400 }
      )
    }

    // 创建响应
    const offer = await prisma.demandOffer.create({
      data: {
        demandId: params.id,
        sellerId: session.user.id,
        offerPrice,
        offerNote: offerNote.trim()
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        },
        demand: {
          select: {
            id: true,
            title: true,
            budget: true
          }
        }
      }
    })

    return NextResponse.json(offer, { status: 201 })

  } catch (error) {
    console.error('Create offer error:', error)
    return NextResponse.json(
      { error: '创建响应失败' },
      { status: 500 }
    )
  }
}

// 接受需求响应（生成订单）
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { offerId, shippingAddress } = body

    if (!offerId) {
      return NextResponse.json(
        { error: '响应ID为必填项' },
        { status: 400 }
      )
    }

    // 获取需求和响应信息
    const offer = await prisma.demandOffer.findUnique({
      where: { id: offerId },
      include: {
        demand: {
          include: {
            user: true
          }
        },
        seller: true
      }
    })

    if (!offer) {
      return NextResponse.json(
        { error: '响应不存在' },
        { status: 404 }
      )
    }

    if (offer.demand.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限接受此响应' },
        { status: 403 }
      )
    }

    if (offer.demand.status !== 'OPEN') {
      return NextResponse.json(
        { error: '需求已关闭，无法接受响应' },
        { status: 400 }
      )
    }

    if (offer.isAccepted) {
      return NextResponse.json(
        { error: '响应已被接受' },
        { status: 400 }
      )
    }

    // 验证收货地址（如果是实物交付）
    if (offer.demand.deliveryMethod === 'delivery') {
      if (!shippingAddress || !shippingAddress.name || !shippingAddress.phone || !shippingAddress.detail) {
        return NextResponse.json(
          { error: '实物交付需要完整的收货地址' },
          { status: 400 }
        )
      }
    }

    // 开始事务：接受响应并生成订单
    const result = await prisma.$transaction(async (tx) => {
      // 1. 创建虚拟商品（基于需求）
      const virtualProduct = await tx.product.create({
        data: {
          title: offer.demand.title,
          description: offer.demand.description,
          price: offer.offerPrice,
          category: 'VIRTUAL', // 需求生成的都是虚拟商品
          condition: 'NEW',
          city: '线上交易',
          district: '',
          shippingFrom: offer.demand.deliveryMethod === 'online' ? '线上交付' : '快递发货',
          stock: 1,
          status: 'SOLD', // 直接标记为已售
          reviewStatus: 'APPROVED',
          sellerId: offer.sellerId,
          isDemandGenerated: true // 标记为需求生成的商品
        }
      })

      // 2. 生成订单
      const orderNumber = generateOrderNumber()
      const platformFee = offer.offerPrice <= 50 ? 0.5 : offer.offerPrice <= 100 ? 1 : offer.offerPrice * 0.015
      const totalAmount = offer.offerPrice + platformFee

      const order = await tx.order.create({
        data: {
          orderNumber,
          productId: virtualProduct.id,
          buyerId: offer.demand.userId,
          sellerId: offer.sellerId,
          status: 'PENDING_PAYMENT',
          totalAmount,
          productPrice: offer.offerPrice,
          shippingFee: 0,
          platformFee,
          shippingAddress: shippingAddress || null,
          autoConfirmAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      })

      // 3. 更新响应状态
      const updatedOffer = await tx.demandOffer.update({
        where: { id: offerId },
        data: {
          isAccepted: true,
          orderId: order.id
        }
      })

      // 4. 更新需求状态
      await tx.demand.update({
        where: { id: offer.demandId },
        data: { status: 'MATCHED' }
      })

      return { order, offer: updatedOffer, product: virtualProduct }
    })

    return NextResponse.json({
      message: '响应已接受，订单已生成',
      orderId: result.order.id,
      orderNumber: result.order.orderNumber
    })

  } catch (error) {
    console.error('Accept offer error:', error)
    return NextResponse.json(
      { error: '接受响应失败' },
      { status: 500 }
    )
  }
}
