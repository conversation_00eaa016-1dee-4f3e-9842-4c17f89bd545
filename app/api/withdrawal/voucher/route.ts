import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的提现券列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') // 'used', 'unused', 'expired'
    const skip = (page - 1) * limit

    const where: any = { usedBy: session.user.id }

    if (status === 'used') {
      where.isUsed = true
    } else if (status === 'unused') {
      where.isUsed = false
      where.validUntil = { gt: new Date() }
    } else if (status === 'expired') {
      where.isUsed = false
      where.validUntil = { lte: new Date() }
    }

    const [vouchers, total] = await Promise.all([
      prisma.withdrawalVoucher.findMany({
        where,
        include: {
          issuer: {
            select: { name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.withdrawalVoucher.count({ where })
    ])

    // 统计信息
    const stats = await prisma.withdrawalVoucher.aggregate({
      where: { usedBy: session.user.id },
      _sum: { amount: true },
      _count: {
        _all: true,
        isUsed: true
      }
    })

    const unusedVouchers = await prisma.withdrawalVoucher.findMany({
      where: {
        usedBy: session.user.id,
        isUsed: false,
        validUntil: { gt: new Date() }
      },
      select: { amount: true }
    })

    const availableAmount = unusedVouchers.reduce((sum, v) => sum + v.amount, 0)

    return NextResponse.json({
      success: true,
      data: {
        vouchers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        statistics: {
          totalVouchers: stats._count._all,
          usedVouchers: stats._count.isUsed,
          totalValue: stats._sum.amount || 0,
          availableAmount
        }
      }
    })

  } catch (error) {
    console.error('获取提现券列表失败:', error)
    return NextResponse.json(
      { error: '获取提现券列表失败' },
      { status: 500 }
    )
  }
}

// 使用提现券
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { voucherCode, withdrawalAmount } = body

    if (!voucherCode || !withdrawalAmount) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    if (withdrawalAmount <= 0) {
      return NextResponse.json(
        { error: '提现金额必须大于0' },
        { status: 400 }
      )
    }

    // 查找提现券
    const voucher = await prisma.withdrawalVoucher.findUnique({
      where: { code: voucherCode }
    })

    if (!voucher) {
      return NextResponse.json(
        { error: '提现券不存在' },
        { status: 404 }
      )
    }

    // 验证提现券状态
    if (voucher.isUsed) {
      return NextResponse.json(
        { error: '提现券已使用' },
        { status: 400 }
      )
    }

    if (voucher.validUntil < new Date()) {
      return NextResponse.json(
        { error: '提现券已过期' },
        { status: 400 }
      )
    }

    if (voucher.usedBy && voucher.usedBy !== session.user.id) {
      return NextResponse.json(
        { error: '提现券不属于您' },
        { status: 403 }
      )
    }

    // 验证提现金额是否超过券面额
    if (withdrawalAmount > voucher.amount) {
      return NextResponse.json(
        { error: `提现金额不能超过券面额 ${voucher.amount} USDT` },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { depositBalance: true, bnbWalletAddress: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    if (user.depositBalance < withdrawalAmount) {
      return NextResponse.json(
        { error: '账户余额不足' },
        { status: 400 }
      )
    }

    if (!user.bnbWalletAddress) {
      return NextResponse.json(
        { error: '请先绑定BNB钱包地址' },
        { status: 400 }
      )
    }

    // 执行免手续费提现
    const result = await prisma.$transaction(async (tx) => {
      // 标记提现券为已使用
      const updatedVoucher = await tx.withdrawalVoucher.update({
        where: { id: voucher.id },
        data: {
          isUsed: true,
          usedBy: session.user.id,
          usedAt: new Date()
        }
      })

      // 扣除用户余额
      await tx.user.update({
        where: { id: session.user.id },
        data: {
          depositBalance: { decrement: withdrawalAmount }
        }
      })

      // 创建提现记录
      const withdrawal = await tx.withdrawal.create({
        data: {
          userId: session.user.id,
          amount: withdrawalAmount,
          withdrawalFee: 0, // 使用提现券免手续费
          actualAmount: withdrawalAmount,
          walletAddress: user.bnbWalletAddress!,
          status: 'PENDING',
          metadata: {
            voucherUsed: voucherCode,
            method: 'BNB_CHAIN'
          }
        }
      })

      // 创建交易记录
      await tx.fundTransaction.create({
        data: {
          userId: session.user.id,
          type: 'WITHDRAWAL',
          amount: -withdrawalAmount,
          description: `免手续费提现 (使用提现券: ${voucherCode})`,
          relatedId: withdrawal.id,
          metadata: {
            relatedType: 'WITHDRAWAL',
            voucherCode,
            feeWaived: true
          }
        }
      })

      return { voucher: updatedVoucher, withdrawal }
    })

    // 发送通知
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        type: 'WITHDRAWAL',
        title: '免手续费提现申请已提交',
        message: `您已使用提现券申请提现 ${withdrawalAmount} USDT，免收手续费`,
        data: {
          withdrawalId: result.withdrawal.id,
          amount: withdrawalAmount,
          voucherCode
        },
        priority: 'NORMAL'
      }
    }).catch(() => {})

    return NextResponse.json({
      success: true,
      message: '免手续费提现申请已提交',
      data: {
        withdrawalId: result.withdrawal.id,
        amount: withdrawalAmount,
        fee: 0,
        voucherUsed: voucherCode
      }
    })

  } catch (error) {
    console.error('使用提现券失败:', error)
    return NextResponse.json(
      { error: '使用提现券失败' },
      { status: 500 }
    )
  }
}

// 验证提现券
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { voucherCode } = body

    if (!voucherCode) {
      return NextResponse.json(
        { error: '请提供提现券代码' },
        { status: 400 }
      )
    }

    const voucher = await prisma.withdrawalVoucher.findUnique({
      where: { code: voucherCode },
      include: {
        issuer: {
          select: { name: true, email: true }
        }
      }
    })

    if (!voucher) {
      return NextResponse.json({
        success: true,
        valid: false,
        message: '提现券不存在'
      })
    }

    const isExpired = voucher.validUntil < new Date()
    const isUsed = voucher.isUsed
    const isOwned = !voucher.usedBy || voucher.usedBy === session.user.id

    const valid = !isExpired && !isUsed && isOwned

    return NextResponse.json({
      success: true,
      valid,
      voucher: valid ? {
        id: voucher.id,
        code: voucher.code,
        amount: voucher.amount,
        description: voucher.description,
        validUntil: voucher.validUntil,
        issuer: voucher.issuer
      } : null,
      message: !valid ? (
        isUsed ? '提现券已使用' :
        isExpired ? '提现券已过期' :
        !isOwned ? '提现券不属于您' : '提现券无效'
      ) : '提现券有效'
    })

  } catch (error) {
    console.error('验证提现券失败:', error)
    return NextResponse.json(
      { error: '验证提现券失败' },
      { status: 500 }
    )
  }
}
