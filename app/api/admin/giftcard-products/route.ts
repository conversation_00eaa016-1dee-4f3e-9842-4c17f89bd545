import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取礼品卡商品列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const products = await (prisma as any).giftCardProduct.findMany({
      include: {
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        updatedBy: {
          select: { id: true, name: true, email: true }
        },
        _count: {
          select: {
            giftCards: true,
            orders: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: products
    })

  } catch (error) {
    console.error('获取礼品卡商品列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取商品列表失败' },
      { status: 500 }
    )
  }
}

// 创建礼品卡商品
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      productType,
      faceValue,
      salePrice,
      stock,
      isActive,
      terms,
      validDays,
      supportedPayments
    } = body

    // 验证必填字段
    if (!name || !productType || !faceValue || !salePrice) {
      return NextResponse.json(
        { success: false, error: '请填写所有必填字段' },
        { status: 400 }
      )
    }

    // 验证数值
    if (faceValue <= 0 || salePrice <= 0 || stock < 0 || validDays <= 0) {
      return NextResponse.json(
        { success: false, error: '数值必须大于0' },
        { status: 400 }
      )
    }

    // 验证商品类型
    if (!['GIFT_CARD', 'RECHARGE_CARD'].includes(productType)) {
      return NextResponse.json(
        { success: false, error: '无效的商品类型' },
        { status: 400 }
      )
    }

    // 验证支付方式
    const validPayments = ['BALANCE', 'BINANCE_PAY', 'CRYPTO']
    if (!supportedPayments || !Array.isArray(supportedPayments) || 
        !supportedPayments.every(p => validPayments.includes(p))) {
      return NextResponse.json(
        { success: false, error: '无效的支付方式' },
        { status: 400 }
      )
    }

    const product = await (prisma as any).giftCardProduct.create({
      data: {
        name,
        description,
        productType,
        faceValue,
        salePrice,
        stock,
        isActive: isActive !== false, // 默认为true
        terms,
        validDays,
        supportedPayments,
        createdById: session.user.id
      },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // 记录操作日志
    await (prisma as any).securityLog.create({
      data: {
        userId: session.user.id,
        action: 'CREATE_GIFT_CARD_PRODUCT',
        description: `创建礼品卡商品: ${name}`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: {
          productId: product.id,
          productType,
          faceValue,
          salePrice,
          stock
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '商品创建成功',
      data: product
    })

  } catch (error) {
    console.error('创建礼品卡商品失败:', error)
    return NextResponse.json(
      { success: false, error: '创建商品失败' },
      { status: 500 }
    )
  }
}
