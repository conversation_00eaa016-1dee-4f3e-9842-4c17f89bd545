import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个礼品卡商品详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params

    const product = await (prisma as any).giftCardProduct.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        updatedBy: {
          select: { id: true, name: true, email: true }
        },
        _count: {
          select: {
            giftCards: true,
            orders: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: '商品不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: product
    })

  } catch (error) {
    console.error('获取商品详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取商品详情失败' },
      { status: 500 }
    )
  }
}

// 更新礼品卡商品
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const {
      name,
      description,
      productType,
      faceValue,
      salePrice,
      stock,
      isActive,
      terms,
      validDays,
      supportedPayments
    } = body

    // 验证商品是否存在
    const existingProduct = await (prisma as any).giftCardProduct.findUnique({
      where: { id }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: '商品不存在' },
        { status: 404 }
      )
    }

    // 验证必填字段
    if (!name || !productType || !faceValue || !salePrice) {
      return NextResponse.json(
        { success: false, error: '请填写所有必填字段' },
        { status: 400 }
      )
    }

    // 验证数值
    if (faceValue <= 0 || salePrice <= 0 || stock < 0 || validDays <= 0) {
      return NextResponse.json(
        { success: false, error: '数值必须大于0' },
        { status: 400 }
      )
    }

    // 验证商品类型
    if (!['GIFT_CARD', 'RECHARGE_CARD'].includes(productType)) {
      return NextResponse.json(
        { success: false, error: '无效的商品类型' },
        { status: 400 }
      )
    }

    // 验证支付方式
    const validPayments = ['BALANCE', 'BINANCE_PAY', 'CRYPTO']
    if (!supportedPayments || !Array.isArray(supportedPayments) || 
        !supportedPayments.every(p => validPayments.includes(p))) {
      return NextResponse.json(
        { success: false, error: '无效的支付方式' },
        { status: 400 }
      )
    }

    const updatedProduct = await (prisma as any).giftCardProduct.update({
      where: { id },
      data: {
        name,
        description,
        productType,
        faceValue,
        salePrice,
        stock,
        isActive,
        terms,
        validDays,
        supportedPayments,
        updatedById: session.user.id
      },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        updatedBy: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // 记录操作日志
    await (prisma as any).securityLog.create({
      data: {
        userId: session.user.id,
        action: 'UPDATE_GIFT_CARD_PRODUCT',
        description: `更新礼品卡商品: ${name}`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: {
          productId: id,
          productType,
          faceValue,
          salePrice,
          stock,
          isActive
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '商品更新成功',
      data: updatedProduct
    })

  } catch (error) {
    console.error('更新商品失败:', error)
    return NextResponse.json(
      { success: false, error: '更新商品失败' },
      { status: 500 }
    )
  }
}

// 删除礼品卡商品
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params

    // 验证商品是否存在
    const product = await (prisma as any).giftCardProduct.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            giftCards: true,
            orders: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: '商品不存在' },
        { status: 404 }
      )
    }

    // 检查是否有关联的礼品卡或订单
    if (product._count.giftCards > 0 || product._count.orders > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: '该商品已有关联的礼品卡或订单，无法删除',
          details: {
            giftCards: product._count.giftCards,
            orders: product._count.orders
          }
        },
        { status: 400 }
      )
    }

    await (prisma as any).giftCardProduct.delete({
      where: { id }
    })

    // 记录操作日志
    await (prisma as any).securityLog.create({
      data: {
        userId: session.user.id,
        action: 'DELETE_GIFT_CARD_PRODUCT',
        description: `删除礼品卡商品: ${product.name}`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: {
          productId: id,
          productName: product.name,
          productType: product.productType
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '商品删除成功'
    })

  } catch (error) {
    console.error('删除商品失败:', error)
    return NextResponse.json(
      { success: false, error: '删除商品失败' },
      { status: 500 }
    )
  }
}
