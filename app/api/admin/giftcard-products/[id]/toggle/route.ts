import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 切换礼品卡商品状态（上架/下架）
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params

    // 验证商品是否存在
    const product = await (prisma as any).giftCardProduct.findUnique({
      where: { id }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: '商品不存在' },
        { status: 404 }
      )
    }

    // 切换状态
    const updatedProduct = await (prisma as any).giftCardProduct.update({
      where: { id },
      data: {
        isActive: !product.isActive,
        updatedById: session.user.id
      }
    })

    // 记录操作日志
    await (prisma as any).securityLog.create({
      data: {
        userId: session.user.id,
        action: 'TOGGLE_GIFT_CARD_PRODUCT_STATUS',
        description: `${updatedProduct.isActive ? '上架' : '下架'}礼品卡商品: ${product.name}`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: {
          productId: id,
          productName: product.name,
          previousStatus: product.isActive,
          newStatus: updatedProduct.isActive
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `商品已${updatedProduct.isActive ? '上架' : '下架'}`,
      data: {
        id: updatedProduct.id,
        isActive: updatedProduct.isActive
      }
    })

  } catch (error) {
    console.error('切换商品状态失败:', error)
    return NextResponse.json(
      { success: false, error: '切换状态失败' },
      { status: 500 }
    )
  }
}
