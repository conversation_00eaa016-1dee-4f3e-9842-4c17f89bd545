import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取中间人申请列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const skip = (page - 1) * limit

    const where: any = {}
    if (status && status !== 'all') {
      where.status = status
    }

    // 添加搜索条件
    if (search) {
      where.user = {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      }
    }

    const [applications, total] = await Promise.all([
      prisma.mediatorApplication.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              depositBalance: true,
              creditScore: true,
              createdAt: true
            }
          },
          reviewer: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.mediatorApplication.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        applications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取中间人申请列表失败:', error)
    return NextResponse.json(
      { error: '获取申请列表失败' },
      { status: 500 }
    )
  }
}

// 审核中间人申请
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { applicationId, action, reviewNotes } = body

    if (!applicationId || !action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    const application = await prisma.mediatorApplication.findUnique({
      where: { id: applicationId },
      include: {
        user: true
      }
    })

    if (!application) {
      return NextResponse.json(
        { error: '申请不存在' },
        { status: 404 }
      )
    }

    if (application.status !== 'PENDING') {
      return NextResponse.json(
        { error: '申请已被处理' },
        { status: 400 }
      )
    }

    const result = await prisma.$transaction(async (tx) => {
      if (action === 'approve') {
        // 批准申请
        const updatedApplication = await tx.mediatorApplication.update({
          where: { id: applicationId },
          data: {
            status: 'APPROVED',
            reviewedBy: session.user.id,
            reviewNotes,
            approvedAt: new Date()
          }
        })

        // 更新用户为中间人
        await tx.user.update({
          where: { id: application.userId },
          data: {
            isMediator: true,
            mediatorStatus: 'ACTIVE',
            mediatorFeeRate: application.feeRate,
            mediatorDeposit: application.depositAmount,
            mediatorVerifiedAt: new Date(),
            bnbWalletAddress: application.bnbWalletAddress,
            bnbWalletVerified: true,
            bnbWalletVerifiedAt: new Date()
          }
        })

        // 保证金转为中间人保证金（保持冻结状态）
        await tx.fundFreeze.updateMany({
          where: {
            userId: application.userId,
            relatedId: applicationId,
            relatedType: 'MEDIATOR_APPLICATION',
            status: 'ACTIVE'
          },
          data: {
            purpose: '中间人保证金',
            relatedType: 'MEDIATOR_DEPOSIT'
          }
        })

        // 发送通知给申请人
        await tx.notification.create({
          data: {
            userId: application.userId,
            type: 'SYSTEM',
            title: '中间人申请已通过',
            message: '恭喜！您的中间人申请已通过审核，现在可以提供托管服务了。',
            data: {
              applicationId,
              feeRate: application.feeRate * 100,
              depositAmount: application.depositAmount
            },
            priority: 'HIGH'
          }
        })

        return updatedApplication

      } else {
        // 拒绝申请
        const updatedApplication = await tx.mediatorApplication.update({
          where: { id: applicationId },
          data: {
            status: 'REJECTED',
            reviewedBy: session.user.id,
            reviewNotes,
            rejectedAt: new Date()
          }
        })

        // 释放保证金
        await tx.fundFreeze.updateMany({
          where: {
            userId: application.userId,
            relatedId: applicationId,
            relatedType: 'MEDIATOR_APPLICATION',
            status: 'ACTIVE'
          },
          data: {
            status: 'CANCELLED',
            cancelledAt: new Date()
          }
        })

        // 返还用户余额
        await tx.user.update({
          where: { id: application.userId },
          data: {
            depositBalance: { increment: application.depositAmount }
          }
        })

        // 创建退还记录
        await tx.fundTransaction.create({
          data: {
            userId: application.userId,
            type: 'MEDIATOR_DEPOSIT_REFUND',
            amount: application.depositAmount,
            description: '中间人申请被拒绝，保证金退还',
            relatedId: applicationId,
            metadata: {
              relatedType: 'MEDIATOR_APPLICATION',
              reviewNotes
            }
          }
        })

        // 发送通知给申请人
        await tx.notification.create({
          data: {
            userId: application.userId,
            type: 'SYSTEM',
            title: '中间人申请未通过',
            message: `很抱歉，您的中间人申请未通过审核。${reviewNotes ? '原因：' + reviewNotes : ''}`,
            data: {
              applicationId,
              reviewNotes,
              refundAmount: application.depositAmount
            },
            priority: 'NORMAL'
          }
        })

        return updatedApplication
      }
    })

    return NextResponse.json({
      success: true,
      message: action === 'approve' ? '申请已批准' : '申请已拒绝',
      data: result
    })

  } catch (error) {
    console.error('审核中间人申请失败:', error)
    return NextResponse.json(
      { error: '审核申请失败' },
      { status: 500 }
    )
  }
}

// 暂停/恢复中间人
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { userId, action, reason } = body

    if (!userId || !action || !['suspend', 'activate'].includes(action)) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isMediator: true, mediatorStatus: true }
    })

    if (!user || !user.isMediator) {
      return NextResponse.json(
        { error: '用户不是中间人' },
        { status: 400 }
      )
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        mediatorStatus: action === 'suspend' ? 'SUSPENDED' : 'ACTIVE'
      }
    })

    // 发送通知
    await prisma.notification.create({
      data: {
        userId,
        type: 'SYSTEM',
        title: action === 'suspend' ? '中间人服务已暂停' : '中间人服务已恢复',
        message: action === 'suspend' 
          ? `您的中间人服务已被暂停。${reason ? '原因：' + reason : ''}`
          : '您的中间人服务已恢复正常。',
        data: { reason },
        priority: 'HIGH'
      }
    })

    return NextResponse.json({
      success: true,
      message: action === 'suspend' ? '中间人已暂停' : '中间人已恢复',
      data: { userId, status: updatedUser.mediatorStatus }
    })

  } catch (error) {
    console.error('操作中间人状态失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}
