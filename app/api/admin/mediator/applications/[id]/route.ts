import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个中间人申请详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = params

    const application = await prisma.mediatorApplication.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            depositBalance: true,
            creditScore: true,
            createdAt: true
          }
        },
        reviewer: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    if (!application) {
      return NextResponse.json(
        { error: '申请不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: application
    })

  } catch (error) {
    console.error('获取中间人申请详情失败:', error)
    return NextResponse.json(
      { error: '获取申请详情失败' },
      { status: 500 }
    )
  }
}

// 更新申请状态（单个申请的快速操作）
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { action, reviewNotes } = body

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    const application = await prisma.mediatorApplication.findUnique({
      where: { id },
      include: {
        user: true
      }
    })

    if (!application) {
      return NextResponse.json(
        { error: '申请不存在' },
        { status: 404 }
      )
    }

    if (application.status !== 'PENDING') {
      return NextResponse.json(
        { error: '申请已被处理' },
        { status: 400 }
      )
    }

    const result = await prisma.$transaction(async (tx) => {
      if (action === 'approve') {
        // 批准申请
        const updatedApplication = await tx.mediatorApplication.update({
          where: { id },
          data: {
            status: 'APPROVED',
            reviewedBy: session.user.id,
            reviewNotes,
            approvedAt: new Date()
          }
        })

        // 更新用户为中间人
        await tx.user.update({
          where: { id: application.userId },
          data: {
            isMediator: true,
            mediatorStatus: 'ACTIVE',
            mediatorFeeRate: application.feeRate,
            mediatorDeposit: application.depositAmount,
            mediatorVerifiedAt: new Date(),
            bnbWalletAddress: application.bnbWalletAddress,
            bnbWalletVerified: true,
            bnbWalletVerifiedAt: new Date()
          }
        })

        // 保证金转为中间人保证金（保持冻结状态）
        await tx.fundFreeze.updateMany({
          where: {
            userId: application.userId,
            relatedId: id,
            relatedType: 'MEDIATOR_APPLICATION',
            status: 'ACTIVE'
          },
          data: {
            purpose: '中间人保证金',
            relatedType: 'MEDIATOR_DEPOSIT'
          }
        })

        // 发送通知给申请人
        await tx.notification.create({
          data: {
            userId: application.userId,
            type: 'SYSTEM',
            title: '中间人申请已通过',
            message: '恭喜！您的中间人申请已通过审核，现在可以提供托管服务了。',
            data: {
              applicationId: id,
              feeRate: application.feeRate * 100,
              depositAmount: application.depositAmount
            },
            priority: 'HIGH'
          }
        })

        return updatedApplication

      } else {
        // 拒绝申请
        const updatedApplication = await tx.mediatorApplication.update({
          where: { id },
          data: {
            status: 'REJECTED',
            reviewedBy: session.user.id,
            reviewNotes,
            rejectedAt: new Date()
          }
        })

        // 释放保证金
        await tx.fundFreeze.updateMany({
          where: {
            userId: application.userId,
            relatedId: id,
            relatedType: 'MEDIATOR_APPLICATION',
            status: 'ACTIVE'
          },
          data: {
            status: 'CANCELLED',
            cancelledAt: new Date()
          }
        })

        // 返还用户余额
        await tx.user.update({
          where: { id: application.userId },
          data: {
            depositBalance: { increment: application.depositAmount }
          }
        })

        // 创建退还记录
        await tx.fundTransaction.create({
          data: {
            userId: application.userId,
            type: 'MEDIATOR_DEPOSIT_REFUND',
            amount: application.depositAmount,
            description: '中间人申请被拒绝，保证金退还',
            relatedId: id,
            metadata: {
              relatedType: 'MEDIATOR_APPLICATION',
              reviewNotes
            }
          }
        })

        // 发送通知给申请人
        await tx.notification.create({
          data: {
            userId: application.userId,
            type: 'SYSTEM',
            title: '中间人申请未通过',
            message: `很抱歉，您的中间人申请未通过审核。${reviewNotes ? '原因：' + reviewNotes : ''}`,
            data: {
              applicationId: id,
              reviewNotes,
              refundAmount: application.depositAmount
            },
            priority: 'NORMAL'
          }
        })

        return updatedApplication
      }
    })

    return NextResponse.json({
      success: true,
      message: action === 'approve' ? '申请已批准' : '申请已拒绝',
      data: result
    })

  } catch (error) {
    console.error('处理中间人申请失败:', error)
    return NextResponse.json(
      { error: '处理申请失败' },
      { status: 500 }
    )
  }
}
