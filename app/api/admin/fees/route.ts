import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取手续费配置
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // TRANSACTION, WITHDRAWAL
    const includeHistory = searchParams.get('history') === 'true'

    let whereClause: any = {}
    
    if (type) {
      whereClause.type = type
    }

    if (!includeHistory) {
      whereClause.effectiveTo = null // 只获取当前有效的配置
    }

    const configs = await prisma.feeConfig.findMany({
      where: whereClause,
      orderBy: [
        { type: 'asc' },
        { createdAt: 'desc' }
      ]
    })

    // 按类型分组
    const groupedConfigs = configs.reduce((acc, config) => {
      if (!acc[config.type]) {
        acc[config.type] = []
      }
      acc[config.type].push(config)
      return acc
    }, {} as Record<string, any[]>)

    return NextResponse.json({
      configs: groupedConfigs,
      total: configs.length
    })

  } catch (error) {
    console.error('获取手续费配置失败:', error)
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    )
  }
}

// 创建或更新手续费配置
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const {
      type,
      name,
      description,
      enabled,
      feeType,
      feeValue,
      minFee,
      maxFee,
      tiers,
      paymentMethod,
      userType,
      effectiveFrom
    } = await request.json()

    // 验证必要字段
    if (!type || !name || !feeType) {
      return NextResponse.json(
        { error: '缺少必要字段' },
        { status: 400 }
      )
    }

    if (!['TRANSACTION', 'WITHDRAWAL'].includes(type)) {
      return NextResponse.json(
        { error: '无效的配置类型' },
        { status: 400 }
      )
    }

    if (!['PERCENTAGE', 'FIXED', 'TIERED'].includes(feeType)) {
      return NextResponse.json(
        { error: '无效的费率类型' },
        { status: 400 }
      )
    }

    // 验证费率配置
    if (feeType === 'TIERED') {
      if (!tiers || !Array.isArray(tiers) || tiers.length === 0) {
        return NextResponse.json(
          { error: '分段配置不能为空' },
          { status: 400 }
        )
      }

      // 验证分段配置格式
      for (const tier of tiers) {
        if (typeof tier.min !== 'number' || typeof tier.max !== 'number' || typeof tier.rate !== 'number') {
          return NextResponse.json(
            { error: '分段配置格式错误' },
            { status: 400 }
          )
        }
        if (tier.min >= tier.max) {
          return NextResponse.json(
            { error: '分段配置范围错误' },
            { status: 400 }
          )
        }
      }
    } else {
      if (typeof feeValue !== 'number' || feeValue < 0) {
        return NextResponse.json(
          { error: '费率值必须为非负数' },
          { status: 400 }
        )
      }
    }

    // 查找是否存在相同配置
    const existingConfig = await prisma.feeConfig.findFirst({
      where: {
        type,
        name,
        paymentMethod: paymentMethod || null,
        userType: userType || null,
        effectiveTo: null
      }
    })

    let newConfig
    if (existingConfig) {
      // 更新现有配置：先将旧配置设为过期，再创建新版本
      await prisma.$transaction(async (tx) => {
        // 设置旧配置过期
        await tx.feeConfig.update({
          where: { id: existingConfig.id },
          data: {
            effectiveTo: new Date(),
            updatedBy: admin.id
          }
        })

        // 创建新版本
        newConfig = await tx.feeConfig.create({
          data: {
            type,
            name,
            description,
            enabled: enabled ?? true,
            feeType,
            feeValue: feeType === 'TIERED' ? null : feeValue,
            minFee,
            maxFee,
            tiers: feeType === 'TIERED' ? tiers : null,
            paymentMethod,
            userType,
            effectiveFrom: effectiveFrom ? new Date(effectiveFrom) : new Date(),
            createdBy: admin.id,
            updatedBy: admin.id,
            version: existingConfig.version + 1,
            parentId: existingConfig.parentId || existingConfig.id
          }
        })
      })
    } else {
      // 创建新配置
      newConfig = await prisma.feeConfig.create({
        data: {
          type,
          name,
          description,
          enabled: enabled ?? true,
          feeType,
          feeValue: feeType === 'TIERED' ? null : feeValue,
          minFee,
          maxFee,
          tiers: feeType === 'TIERED' ? tiers : null,
          paymentMethod,
          userType,
          effectiveFrom: effectiveFrom ? new Date(effectiveFrom) : new Date(),
          createdBy: admin.id,
          updatedBy: admin.id
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: existingConfig ? '手续费配置已更新' : '手续费配置已创建',
      config: newConfig
    })

  } catch (error) {
    console.error('保存手续费配置失败:', error)
    return NextResponse.json(
      { error: '保存配置失败' },
      { status: 500 }
    )
  }
}

// 删除手续费配置
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const configId = searchParams.get('id')

    if (!configId) {
      return NextResponse.json(
        { error: '缺少配置ID' },
        { status: 400 }
      )
    }

    // 软删除：设置过期时间
    const updatedConfig = await prisma.feeConfig.update({
      where: { id: configId },
      data: {
        effectiveTo: new Date(),
        updatedBy: admin.id
      }
    })

    return NextResponse.json({
      success: true,
      message: '手续费配置已删除',
      config: updatedConfig
    })

  } catch (error) {
    console.error('删除手续费配置失败:', error)
    return NextResponse.json(
      { error: '删除配置失败' },
      { status: 500 }
    )
  }
}
