import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 调整用户信用分
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const adminUser = await requireAdmin()
    const { id } = await params
    const body = await request.json()
    const { userId, creditChange, reason } = body

    if (!userId || typeof creditChange !== 'number' || !reason) {
      return NextResponse.json(
        { error: '参数不完整' },
        { status: 400 }
      )
    }

    if (creditChange < -50 || creditChange > 50) {
      return NextResponse.json(
        { error: '信用分调整范围应在-50到+50之间' },
        { status: 400 }
      )
    }

    // 检查订单是否存在
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        buyer: true,
        seller: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否是订单的买家或卖家
    if (userId !== order.buyerId && userId !== order.sellerId) {
      return NextResponse.json(
        { error: '只能调整订单相关用户的信用分' },
        { status: 400 }
      )
    }

    // 获取用户当前信用分
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const oldCreditScore = user.creditScore
    const newCreditScore = Math.max(0, Math.min(100, oldCreditScore + creditChange))

    // 更新用户信用分
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { creditScore: newCreditScore }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: adminUser?.id || '',
        action: 'CREDIT_ADJUSTED',
        description: `管理员调整用户信用分: ${user.name || user.email} (${oldCreditScore} → ${newCreditScore})，原因: ${reason}`,
        oldValue: JSON.stringify({ userId, creditScore: oldCreditScore }),
        newValue: JSON.stringify({ userId, creditScore: newCreditScore, reason })
      }
    })

    return NextResponse.json({
      success: true,
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        oldCreditScore,
        newCreditScore: updatedUser.creditScore
      }
    })

  } catch (error) {
    console.error('Adjust credit score error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '调整信用分失败' },
      { status: 500 }
    )
  }
}
