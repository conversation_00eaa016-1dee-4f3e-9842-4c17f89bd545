import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 管理员订单操作
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const adminUser = await requireAdmin()

    const { id } = await params
    const body = await request.json()
    const { action } = body

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        product: true,
        buyer: true,
        seller: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    let updateData: any = {}
    let logDescription = ''

    switch (action) {
      case 'confirm_payment':
        // 管理员确认支付
        if (order.status !== 'PAID') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          paymentConfirmed: true
        }
        logDescription = '管理员确认支付'
        break

      case 'manual_ship':
        // 管理员手动确认发货
        if (order.status !== 'PAID') {
          return NextResponse.json(
            { error: '只能对已支付的订单确认发货' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'SHIPPED',
          shippedAt: new Date()
        }
        logDescription = '管理员手动确认发货'
        break

      case 'manual_receive':
        // 管理员手动确认收货
        if (order.status !== 'SHIPPED') {
          return NextResponse.json(
            { error: '只能对已发货的订单确认收货' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'COMPLETED',
          receivedAt: new Date()
        }
        logDescription = '管理员手动确认收货'
        break

      case 'approve_refund':
        // 管理员同意退款
        if (order.status !== 'REFUND_REQUESTED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'CANCELLED',
          refundAmount: order.totalAmount - order.platformFee // 退款金额扣除平台手续费
        }
        logDescription = '管理员同意退款'
        // 恢复商品库存
        await prisma.product.update({
          where: { id: order.productId },
          data: {
            stock: {
              increment: 1 // 假设每个订单数量为1
            }
          }
        })
        break

      case 'reject_refund':
        // 管理员拒绝退款
        if (order.status !== 'REFUND_REQUESTED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'SHIPPED', // 恢复到发货状态
          refundReason: null
        }
        logDescription = '管理员拒绝退款'
        break

      case 'force_complete':
        // 管理员强制完成订单
        if (!['PAID', 'SHIPPED'].includes(order.status)) {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'COMPLETED',
          receivedAt: new Date()
        }
        logDescription = '管理员强制完成订单'
        break

      case 'force_cancel':
        // 管理员强制取消订单
        if (order.status === 'COMPLETED') {
          return NextResponse.json(
            { error: '已完成的订单无法取消' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'CANCELLED'
        }
        logDescription = '管理员强制取消订单'
        // 恢复商品库存
        await prisma.product.update({
          where: { id: order.productId },
          data: {
            stock: {
              increment: 1
            }
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: adminUser?.id || '',
        action: action.toUpperCase(),
        description: logDescription,
        oldValue: JSON.stringify({ status: order.status }),
        newValue: JSON.stringify(updateData)
      }
    })

    return NextResponse.json(updatedOrder)

  } catch (error) {
    console.error('Admin update order error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '更新订单失败' },
      { status: 500 }
    )
  }
}

// 获取单个订单详情（管理员视图）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        product: true,
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
            binanceUid: true,
            bnbWalletAddress: true,
            creditScore: true,
            city: true,
            district: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
            binanceUid: true,
            bnbWalletAddress: true,
            creditScore: true,
            city: true,
            district: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(order)

  } catch (error) {
    console.error('Admin get order error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取订单详情失败' },
      { status: 500 }
    )
  }
}
