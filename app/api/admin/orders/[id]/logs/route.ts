import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取订单操作日志
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()
    const { id } = await params

    const logs = await prisma.orderLog.findMany({
      where: { orderId: id },
      include: {
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(logs)

  } catch (error) {
    console.error('Get order logs error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取操作日志失败' },
      { status: 500 }
    )
  }
}
