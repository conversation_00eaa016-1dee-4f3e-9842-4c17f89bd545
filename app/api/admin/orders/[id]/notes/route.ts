import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取订单的管理员备注
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()
    const { id } = await params

    const notes = await prisma.adminNote.findMany({
      where: { orderId: id },
      include: {
        admin: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(notes)

  } catch (error) {
    console.error('Get admin notes error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取备注失败' },
      { status: 500 }
    )
  }
}

// 添加管理员备注
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const adminUser = await requireAdmin()
    const { id } = await params
    const body = await request.json()
    const { content, isPrivate = true } = body

    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { error: '备注内容不能为空' },
        { status: 400 }
      )
    }

    // 检查订单是否存在
    const order = await prisma.order.findUnique({
      where: { id }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 创建备注
    const note = await prisma.adminNote.create({
      data: {
        orderId: id,
        adminId: adminUser?.id || '',
        content: content.trim(),
        isPrivate
      },
      include: {
        admin: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: adminUser?.id || '',
        action: 'ADMIN_NOTE_ADDED',
        description: `管理员添加备注: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
        newValue: JSON.stringify({ noteId: note.id, content, isPrivate })
      }
    })

    return NextResponse.json(note)

  } catch (error) {
    console.error('Add admin note error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '添加备注失败' },
      { status: 500 }
    )
  }
}
