import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const paymentStatus = searchParams.get('paymentStatus') || ''

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { product: { title: { contains: search, mode: 'insensitive' } } },
        { buyer: { name: { contains: search, mode: 'insensitive' } } },
        { buyer: { email: { contains: search, mode: 'insensitive' } } },
        { seller: { name: { contains: search, mode: 'insensitive' } } },
        { seller: { email: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (paymentStatus) {
      where.paymentStatus = paymentStatus
    }

    // 获取订单列表
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              title: true,
              price: true,
              status: true
            }
          },
          buyer: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          seller: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.order.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      orders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Admin get orders error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取订单列表失败' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 检查管理员权限并获取session
    const session = await requireAdmin()

    const body = await request.json()
    const { orderId, action, data } = body

    if (!orderId || !action) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    let result

    switch (action) {
      case 'updateStatus':
        if (!data.status || !['PENDING', 'CONFIRMED', 'SHIPPED', 'DELIVERED', 'COMPLETED', 'CANCELLED', 'DISPUTED'].includes(data.status)) {
          return NextResponse.json(
            { error: '无效的订单状态' },
            { status: 400 }
          )
        }

        result = await prisma.order.update({
          where: { id: orderId },
          data: { status: data.status },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'updatePaymentStatus':
        if (!data.paymentStatus || !['PENDING', 'PAID', 'CONFIRMED', 'REFUNDED'].includes(data.paymentStatus)) {
          return NextResponse.json(
            { error: '无效的支付状态' },
            { status: 400 }
          )
        }

        result = await prisma.order.update({
          where: { id: orderId },
          data: { paymentConfirmed: data.paymentStatus === 'confirmed' },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'addNotes':
        // 创建管理员备注
        if (session) {
          await prisma.adminNote.create({
            data: {
              orderId: orderId,
              adminId: session.id,
              content: data.notes,
              isPrivate: true
            }
          })
        }

        result = await prisma.order.findUnique({
          where: { id: orderId },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      order: result
    })

  } catch (error) {
    console.error('Admin orders update error:', error)

    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: '更新订单失败' },
      { status: 500 }
    )
  }
}
