import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 导航配置接口
interface NavigationItem {
  id: string
  name: string
  href: string
  icon: string
  badge?: string | number
  children?: NavigationItem[]
  permissions?: string[]
  enabled: boolean
  order: number
}

// 基础导航配置
const baseNavigation: NavigationItem[] = [
  {
    id: 'dashboard',
    name: '仪表板',
    href: '/admin',
    icon: 'HomeIcon',
    enabled: true,
    order: 1
  },
  {
    id: 'users',
    name: '用户管理',
    href: '/admin/users',
    icon: 'UsersIcon',
    enabled: true,
    order: 2
  },
  {
    id: 'orders',
    name: '订单管理',
    href: '/admin/orders',
    icon: 'ShoppingBagIcon',
    enabled: true,
    order: 3
  },
  {
    id: 'products',
    name: '商品管理',
    href: '/admin/products',
    icon: 'ShoppingBagIcon',
    enabled: true,
    order: 4
  },
  {
    id: 'mediators',
    name: '中间人管理',
    href: '/admin/mediators',
    icon: 'ShieldCheckIcon',
    enabled: true,
    order: 5
  },
  {
    id: 'escrow-orders',
    name: '托管订单',
    href: '/admin/escrow-orders',
    icon: 'ShieldCheckIcon',
    enabled: true,
    order: 6
  },
  {
    id: 'payments',
    name: '财务管理',
    href: '/admin/payments',
    icon: 'CurrencyDollarIcon',
    enabled: true,
    order: 7
  },
  {
    id: 'deposits',
    name: '保证金管理',
    href: '/admin/deposits',
    icon: 'CurrencyDollarIcon',
    enabled: true,
    order: 8
  },
  {
    id: 'disputes',
    name: '争议处理',
    href: '/admin/disputes',
    icon: 'ExclamationTriangleIcon',
    enabled: true,
    order: 9
  },
  {
    id: 'giftcards',
    name: '礼品卡管理',
    href: '/admin/giftcards',
    icon: 'GiftIcon',
    enabled: true,
    order: 10
  },
  {
    id: 'redemption-codes',
    name: '兑换码管理',
    href: '/admin/redemption-codes',
    icon: 'GiftIcon',
    enabled: true,
    order: 11
  },
  {
    id: 'gift-cards-enhanced',
    name: '礼品卡系统',
    href: '/admin/gift-cards',
    icon: 'CreditCardIcon',
    enabled: true,
    order: 11.5
  },
  {
    id: 'vouchers',
    name: '兑换券管理',
    href: '/admin/vouchers',
    icon: 'TicketIcon',
    enabled: true,
    order: 11.6
  },
  {
    id: 'announcements',
    name: '公告管理',
    href: '/admin/announcements',
    icon: 'SpeakerWaveIcon',
    enabled: true,
    order: 12
  },
  {
    id: 'help',
    name: '帮助中心',
    href: '/admin/help',
    icon: 'QuestionMarkCircleIcon',
    enabled: true,
    order: 13
  },
  {
    id: 'reports',
    name: '数据报告',
    href: '/admin/reports',
    icon: 'ChartBarIcon',
    enabled: true,
    order: 14
  },
  {
    id: 'settings',
    name: '系统设置',
    href: '/admin/settings',
    icon: 'Cog6ToothIcon',
    enabled: true,
    order: 15
  }
]

// 获取动态导航数据
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    // 这里可以根据用户权限、系统配置等动态调整导航
    const dynamicNavigation = await getDynamicNavigation(session.user.id)

    return NextResponse.json({
      success: true,
      data: {
        navigation: dynamicNavigation,
        user: {
          id: session.user.id,
          name: session.user.name,
          email: session.user.email,
          role: session.user.role
        }
      }
    })

  } catch (error) {
    console.error('获取导航配置失败:', error)
    return NextResponse.json(
      { error: '获取导航配置失败' },
      { status: 500 }
    )
  }
}

// 动态获取导航配置
async function getDynamicNavigation(userId: string): Promise<NavigationItem[]> {
  try {
    // 这里可以从数据库获取用户特定的导航配置
    // 也可以根据系统设置、功能开关等动态调整
    
    const navigation = [...baseNavigation]

    // 添加动态徽章（未读通知、待处理项目等）
    await addDynamicBadges(navigation)

    // 根据权限过滤导航项
    const filteredNavigation = navigation.filter(item => item.enabled)

    // 按顺序排序
    return filteredNavigation.sort((a, b) => a.order - b.order)

  } catch (error) {
    console.error('获取动态导航失败:', error)
    return baseNavigation
  }
}

// 添加动态徽章
async function addDynamicBadges(navigation: NavigationItem[]) {
  try {
    // 导入 prisma 来查询实际数据
    const { prisma } = require('@/lib/prisma')

    // 1. 中间人管理 - 只显示待审核的申请数量
    const mediatorItem = navigation.find(item => item.id === 'mediators')
    if (mediatorItem) {
      try {
        const pendingMediatorApplications = await prisma.mediatorApplication.count({
          where: { status: 'PENDING' }
        })
        if (pendingMediatorApplications > 0) {
          mediatorItem.badge = pendingMediatorApplications.toString()
        }
      } catch (error) {
        console.log('MediatorApplication表不存在，跳过徽章设置')
      }
    }

    // 2. 争议处理 - 只显示待处理的争议数量
    const disputeItem = navigation.find(item => item.id === 'disputes')
    if (disputeItem) {
      try {
        const pendingDisputes = await prisma.dispute.count({
          where: {
            status: {
              in: ['PENDING', 'INVESTIGATING', 'WAITING_RESPONSE']
            }
          }
        })
        if (pendingDisputes > 0) {
          disputeItem.badge = pendingDisputes.toString()
        }
      } catch (error) {
        console.log('Dispute表不存在，跳过徽章设置')
      }
    }

    // 3. 订单管理 - 只显示需要处理的订单（异常状态）
    const ordersItem = navigation.find(item => item.id === 'orders')
    if (ordersItem) {
      try {
        const problemOrders = await prisma.order.count({
          where: {
            status: {
              in: ['DISPUTED', 'CANCELLED', 'REFUND_REQUESTED', 'PAYMENT_FAILED']
            }
          }
        })
        if (problemOrders > 0) {
          ordersItem.badge = problemOrders.toString()
        }
      } catch (error) {
        console.log('Order表查询失败，跳过徽章设置')
      }
    }

    // 4. 托管订单 - 只显示需要处理的托管订单
    const escrowItem = navigation.find(item => item.id === 'escrow-orders')
    if (escrowItem) {
      try {
        const problemEscrowOrders = await prisma.escrowOrder.count({
          where: {
            status: {
              in: ['DISPUTED', 'EXPIRED', 'CANCELLED']
            }
          }
        })
        if (problemEscrowOrders > 0) {
          escrowItem.badge = problemEscrowOrders.toString()
        }
      } catch (error) {
        console.log('EscrowOrder表不存在，跳过徽章设置')
      }
    }

    // 5. 财务管理 - 显示待审核的提现申请
    const paymentsItem = navigation.find(item => item.id === 'payments')
    if (paymentsItem) {
      try {
        const pendingWithdrawals = await prisma.withdrawal.count({
          where: { status: 'PENDING' }
        })
        if (pendingWithdrawals > 0) {
          paymentsItem.badge = pendingWithdrawals.toString()
        }
      } catch (error) {
        console.log('Withdrawal表不存在，跳过徽章设置')
      }
    }

    // 6. 保证金管理 - 显示异常的保证金记录
    const depositsItem = navigation.find(item => item.id === 'deposits')
    if (depositsItem) {
      try {
        const problemDeposits = await prisma.fundFreeze.count({
          where: {
            status: 'DISPUTED',
            relatedType: { in: ['MEDIATOR_DEPOSIT', 'TRADE_DEPOSIT'] }
          }
        })
        if (problemDeposits > 0) {
          depositsItem.badge = problemDeposits.toString()
        }
      } catch (error) {
        console.log('FundFreeze表不存在，跳过徽章设置')
      }
    }

  } catch (error) {
    console.error('添加动态徽章失败:', error)
    // 如果查询失败，不显示徽章，避免显示错误信息
  }
}

// 更新导航配置
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { navigationConfig } = body

    // 这里可以保存用户自定义的导航配置到数据库
    // 例如：隐藏某些菜单项、调整顺序等

    return NextResponse.json({
      success: true,
      message: '导航配置已更新'
    })

  } catch (error) {
    console.error('更新导航配置失败:', error)
    return NextResponse.json(
      { error: '更新导航配置失败' },
      { status: 500 }
    )
  }
}
