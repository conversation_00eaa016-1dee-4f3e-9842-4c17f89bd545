import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 导出礼品卡数据
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { 
      format = 'csv', // csv, json, excel
      filters = {},
      fields = ['cardCode', 'faceValue', 'status', 'createdAt', 'soldAt', 'redeemedAt']
    } = body

    // 构建查询条件
    const whereCondition: any = {}
    
    if (filters.status) {
      whereCondition.status = filters.status
    }
    
    if (filters.batchId) {
      whereCondition.batchId = filters.batchId
    }
    
    if (filters.dateRange) {
      const { start, end } = filters.dateRange
      whereCondition.createdAt = {
        gte: new Date(start),
        lte: new Date(end)
      }
    }

    if (filters.faceValueRange) {
      const { min, max } = filters.faceValueRange
      whereCondition.faceValue = {
        gte: min,
        lte: max
      }
    }

    // 获取礼品卡数据
    const giftCards = await (prisma as any).giftCard.findMany({
      where: whereCondition,
      include: {
        soldTo: {
          select: { id: true, name: true, email: true }
        },
        redeemedBy: {
          select: { id: true, name: true, email: true }
        },
        saleOrder: {
          select: { id: true, orderNumber: true, totalAmount: true }
        },
        transactions: {
          select: {
            id: true,
            transactionType: true,
            amount: true,
            createdAt: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 格式化数据
    const formattedData = giftCards.map((card: any) => {
      const baseData: any = {}
      
      if (fields.includes('cardCode')) baseData.cardCode = card.cardCode
      if (fields.includes('faceValue')) baseData.faceValue = card.faceValue
      if (fields.includes('status')) baseData.status = card.status
      if (fields.includes('batchId')) baseData.batchId = card.batchId
      if (fields.includes('createdAt')) baseData.createdAt = card.createdAt
      if (fields.includes('soldAt')) baseData.soldAt = card.soldAt
      if (fields.includes('redeemedAt')) baseData.redeemedAt = card.redeemedAt
      if (fields.includes('validUntil')) baseData.validUntil = card.validUntil
      if (fields.includes('notes')) baseData.notes = card.notes
      
      if (fields.includes('soldToName')) baseData.soldToName = card.soldTo?.name || ''
      if (fields.includes('soldToEmail')) baseData.soldToEmail = card.soldTo?.email || ''
      if (fields.includes('redeemedByName')) baseData.redeemedByName = card.redeemedBy?.name || ''
      if (fields.includes('redeemedByEmail')) baseData.redeemedByEmail = card.redeemedBy?.email || ''
      if (fields.includes('saleOrderNumber')) baseData.saleOrderNumber = card.saleOrder?.orderNumber || ''
      if (fields.includes('transactionCount')) baseData.transactionCount = card.transactions.length
      
      return baseData
    })

    // 根据格式生成响应
    if (format === 'json') {
      return NextResponse.json({
        success: true,
        data: formattedData,
        total: formattedData.length,
        exportedAt: new Date().toISOString()
      })
    }

    if (format === 'csv') {
      // 生成CSV格式
      const headers = fields.map(field => {
        const headerMap: Record<string, string> = {
          cardCode: '礼品卡代码',
          faceValue: '面值(USDT)',
          status: '状态',
          batchId: '批次ID',
          createdAt: '创建时间',
          soldAt: '售出时间',
          redeemedAt: '兑换时间',
          validUntil: '有效期至',
          notes: '备注',
          soldToName: '购买者姓名',
          soldToEmail: '购买者邮箱',
          redeemedByName: '兑换者姓名',
          redeemedByEmail: '兑换者邮箱',
          saleOrderNumber: '销售订单号',
          transactionCount: '交易次数'
        }
        return headerMap[field] || field
      })

      const csvRows = [
        headers.join(','),
        ...formattedData.map(row => 
          fields.map(field => {
            const value = row[field]
            if (value === null || value === undefined) return ''
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value.replace(/"/g, '""')}"`
            }
            if (value instanceof Date) {
              return value.toISOString()
            }
            return String(value)
          }).join(',')
        )
      ]

      const csvContent = csvRows.join('\n')
      
      // 记录导出操作
      await (prisma as any).securityLog.create({
        data: {
          userId: session.user.id,
          action: 'EXPORT_GIFT_CARDS',
          description: `导出礼品卡数据 ${formattedData.length} 条，格式: ${format}`,
          status: 'SUCCESS',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
          userAgent: request.headers.get('user-agent') || 'Admin Panel',
          metadata: {
            format,
            filters,
            fields,
            recordCount: formattedData.length
          }
        }
      })

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="giftcards_export_${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    return NextResponse.json(
      { success: false, error: '不支持的导出格式' },
      { status: 400 }
    )

  } catch (error) {
    console.error('导出礼品卡数据失败:', error)
    return NextResponse.json(
      { success: false, error: '导出数据失败' },
      { status: 500 }
    )
  }
}
