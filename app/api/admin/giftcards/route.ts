import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 生成16位礼品卡码
function generateGiftCardCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 批量生成礼品卡
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { 
      faceValue, 
      quantity, 
      validDays = 365, 
      batchName,
      notes 
    } = await request.json()

    if (!faceValue || !quantity || faceValue <= 0 || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: '参数无效' },
        { status: 400 }
      )
    }

    if (quantity > 1000) {
      return NextResponse.json(
        { success: false, error: '单次最多生成1000张礼品卡' },
        { status: 400 }
      )
    }

    const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`
    const validUntil = new Date(Date.now() + validDays * 24 * 60 * 60 * 1000)

    // 生成唯一的礼品卡码
    const giftCards: {
      cardCode: string;
      faceValue: number;
      status: 'GENERATED';
      validUntil: Date;
      createdById: string;
      batchId: string;
      notes: string | null;
    }[] = []
    const generatedCodes = new Set<string>()

    for (let i = 0; i < quantity; i++) {
      let cardCode
      do {
        cardCode = generateGiftCardCode()
      } while (generatedCodes.has(cardCode))
      
      generatedCodes.add(cardCode)
      
      giftCards.push({
        cardCode,
        faceValue,
        status: 'GENERATED',
        validUntil,
        createdById: session.user.id,
        batchId,
        notes: notes || `批量生成 - ${batchName || '未命名批次'}`
      })
    }

    // 检查数据库中是否有重复的卡码
    const existingCodes = await prisma.giftCard.findMany({
      where: {
        cardCode: {
          in: Array.from(generatedCodes)
        }
      },
      select: { cardCode: true }
    })

    if (existingCodes.length > 0) {
      return NextResponse.json(
        { success: false, error: '生成的卡码与现有卡码冲突，请重试' },
        { status: 400 }
      )
    }

    // 批量创建礼品卡
    const result = await (prisma as any).giftCard.createMany({
      data: giftCards
    })

    // 记录操作日志（使用SecurityLog模型）
    await (prisma as any).securityLog.create({
      data: {
        userId: session.user.id,
        action: 'GENERATE_GIFT_CARDS',
        description: `批量生成礼品卡 ${quantity} 张，面值 ${faceValue} USDT`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: {
          batchId,
          quantity,
          faceValue,
          validDays,
          batchName
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `成功生成 ${quantity} 张礼品卡`,
      data: {
        batchId,
        quantity: result.count,
        faceValue,
        validUntil,
        codes: Array.from(generatedCodes) // 返回生成的卡码供管理员查看
      }
    })

  } catch (error) {
    console.error('生成礼品卡失败:', error)
    return NextResponse.json(
      { success: false, error: '生成礼品卡失败' },
      { status: 500 }
    )
  }
}

// 获取礼品卡列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const batchId = searchParams.get('batchId')
    const search = searchParams.get('search')

    // 构建查询条件
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (batchId) {
      where.batchId = batchId
    }

    if (search) {
      where.OR = [
        { cardCode: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } }
      ]
    }

    const giftCards = await prisma.giftCard.findMany({
      where,
      include: {
        soldTo: {
          select: { id: true, name: true, email: true }
        },
        redeemedBy: {
          select: { id: true, name: true, email: true }
        },
        createdBy: {
          select: { id: true, name: true }
        },
        saleOrder: {
          select: { id: true, orderNumber: true }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.giftCard.count({ where })

    // 获取统计信息
    const stats = await prisma.giftCard.groupBy({
      by: ['status'],
      _count: {
        id: true
      },
      _sum: {
        faceValue: true
      }
    })

    const statsMap = stats.reduce((acc, stat) => {
      acc[stat.status] = {
        count: stat._count.id,
        totalValue: stat._sum.faceValue || 0
      }
      return acc
    }, {} as Record<string, { count: number; totalValue: number }>)

    return NextResponse.json({
      success: true,
      data: {
        giftCards,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        stats: statsMap
      }
    })

  } catch (error) {
    console.error('获取礼品卡列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取礼品卡列表失败' },
      { status: 500 }
    )
  }
}

// 更新礼品卡状态
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { giftCardIds, action, reason } = await request.json()

    if (!giftCardIds || !Array.isArray(giftCardIds) || giftCardIds.length === 0) {
      return NextResponse.json(
        { success: false, error: '请选择要操作的礼品卡' },
        { status: 400 }
      )
    }

    let updateData: any = {}
    let logAction = ''

    switch (action) {
      case 'expire':
        updateData = { status: 'EXPIRED' }
        logAction = 'EXPIRE_GIFT_CARDS'
        break
      case 'activate':
        updateData = { status: 'GENERATED' }
        logAction = 'ACTIVATE_GIFT_CARDS'
        break
      default:
        return NextResponse.json(
          { success: false, error: '无效的操作' },
          { status: 400 }
        )
    }

    // 批量更新礼品卡状态
    const result = await prisma.giftCard.updateMany({
      where: {
        id: { in: giftCardIds }
      },
      data: updateData
    })

    // 记录操作日志
    await (prisma as any).securityLog.create({
      data: {
        userId: session.user.id,
        action: logAction,
        description: `批量${action === 'expire' ? '过期' : '激活'}礼品卡 ${result.count} 张`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: {
          giftCardIds,
          action,
          reason,
          affectedCount: result.count
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `成功${action === 'expire' ? '过期' : '激活'} ${result.count} 张礼品卡`,
      data: {
        affectedCount: result.count
      }
    })

  } catch (error) {
    console.error('更新礼品卡状态失败:', error)
    return NextResponse.json(
      { success: false, error: '更新礼品卡状态失败' },
      { status: 500 }
    )
  }
}
