import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取礼品卡统计数据
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // 默认30天
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000)

    // 基础统计
    const [
      totalCards,
      totalValue,
      statusStats,
      faceValueStats,
      recentActivity,
      salesStats,
      redemptionStats
    ] = await Promise.all([
      // 总卡数
      prisma.giftCard.count(),
      
      // 总价值
      prisma.giftCard.aggregate({
        _sum: { faceValue: true }
      }),
      
      // 状态统计
      prisma.giftCard.groupBy({
        by: ['status'],
        _count: { id: true },
        _sum: { faceValue: true }
      }),
      
      // 面值分布
      prisma.giftCard.groupBy({
        by: ['faceValue'],
        _count: { id: true },
        _sum: { faceValue: true },
        orderBy: { faceValue: 'asc' }
      }),
      
      // 最近活动
      prisma.giftCard.findMany({
        where: {
          OR: [
            { soldAt: { gte: startDate } },
            { redeemedAt: { gte: startDate } },
            { createdAt: { gte: startDate } }
          ]
        },
        include: {
          soldTo: { select: { id: true, name: true, email: true } },
          redeemedBy: { select: { id: true, name: true, email: true } },
          createdBy: { select: { id: true, name: true, email: true } }
        },
        orderBy: { updatedAt: 'desc' },
        take: 20
      }),
      
      // 销售统计
      prisma.giftCard.groupBy({
        by: ['soldAt'],
        where: {
          soldAt: { gte: startDate, not: null }
        },
        _count: { id: true },
        _sum: { faceValue: true }
      }),
      
      // 兑换统计
      prisma.giftCard.groupBy({
        by: ['redeemedAt'],
        where: {
          redeemedAt: { gte: startDate, not: null }
        },
        _count: { id: true },
        _sum: { faceValue: true }
      })
    ])

    // 处理日期统计数据
    const dailySales = processDailyStats(salesStats, period)
    const dailyRedemptions = processDailyStats(redemptionStats, period)

    // 计算转换率
    const soldCards = statusStats.find(s => s.status === 'SOLD')?._count.id || 0
    const redeemedCards = statusStats.find(s => s.status === 'REDEEMED')?._count.id || 0
    const conversionRate = soldCards > 0 ? (redeemedCards / soldCards * 100) : 0

    // 计算平均面值
    const avgFaceValue = totalCards > 0 ? (totalValue._sum.faceValue || 0) / totalCards : 0

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalCards,
          totalValue: totalValue._sum.faceValue || 0,
          avgFaceValue: Math.round(avgFaceValue * 100) / 100,
          conversionRate: Math.round(conversionRate * 100) / 100
        },
        statusDistribution: statusStats.map(stat => ({
          status: stat.status,
          count: stat._count.id,
          value: stat._sum.faceValue || 0,
          percentage: Math.round((stat._count.id / totalCards) * 100 * 100) / 100
        })),
        faceValueDistribution: faceValueStats.map(stat => ({
          faceValue: stat.faceValue,
          count: stat._count.id,
          totalValue: stat._sum.faceValue || 0,
          percentage: Math.round((stat._count.id / totalCards) * 100 * 100) / 100
        })),
        trends: {
          dailySales,
          dailyRedemptions
        },
        recentActivity: recentActivity.map(card => ({
          id: card.id,
          cardCode: card.cardCode,
          faceValue: card.faceValue,
          status: card.status,
          createdAt: card.createdAt,
          soldAt: card.soldAt,
          redeemedAt: card.redeemedAt,
          soldTo: card.soldTo,
          redeemedBy: card.redeemedBy,
          createdBy: card.createdBy
        }))
      }
    })

  } catch (error) {
    console.error('获取礼品卡统计失败:', error)
    return NextResponse.json(
      { success: false, error: '获取统计失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 处理日期统计数据
function processDailyStats(stats: any[], period: string) {
  const days = parseInt(period)
  const result = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
    const dateStr = date.toISOString().split('T')[0]
    
    const dayStats = stats.filter(stat => {
      if (!stat.soldAt && !stat.redeemedAt) return false
      const statDate = new Date(stat.soldAt || stat.redeemedAt)
      return statDate.toISOString().split('T')[0] === dateStr
    })
    
    const count = dayStats.reduce((sum, stat) => sum + stat._count.id, 0)
    const value = dayStats.reduce((sum, stat) => sum + (stat._sum.faceValue || 0), 0)
    
    result.push({
      date: dateStr,
      count,
      value
    })
  }
  
  return result
}
