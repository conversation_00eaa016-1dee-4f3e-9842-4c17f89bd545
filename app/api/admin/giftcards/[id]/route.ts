import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个礼品卡详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params

    const giftCard = await (prisma as any).giftCard.findUnique({
      where: { id },
      include: {
        soldTo: {
          select: { id: true, name: true, email: true }
        },
        redeemedBy: {
          select: { id: true, name: true, email: true }
        },
        assignedTo: {
          select: { id: true, name: true, email: true }
        },
        assignedBy: {
          select: { id: true, name: true, email: true }
        },
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        saleOrder: {
          select: { id: true, orderNumber: true, totalAmount: true, status: true }
        },
        transactions: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!giftCard) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: giftCard
    })

  } catch (error) {
    console.error('获取礼品卡详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取礼品卡详情失败' },
      { status: 500 }
    )
  }
}

// 删除礼品卡
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params
    const url = new URL(request.url)
    const reason = url.searchParams.get('reason')
    const forceDelete = url.searchParams.get('force') === 'true'

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '请提供删除原因' },
        { status: 400 }
      )
    }

    // 查找礼品卡
    const giftCard = await (prisma as any).giftCard.findUnique({
      where: { id },
      include: {
        soldTo: {
          select: { id: true, name: true, email: true }
        },
        redeemedBy: {
          select: { id: true, name: true, email: true }
        },
        saleOrder: {
          select: { id: true, orderNumber: true, status: true }
        },
        transactions: true
      }
    })

    if (!giftCard) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在' },
        { status: 404 }
      )
    }

    // 检查是否可以删除
    if (!forceDelete) {
      if (giftCard.status === 'SOLD' || giftCard.status === 'REDEEMED') {
        return NextResponse.json(
          { 
            success: false, 
            error: '已售出或已兑换的礼品卡不能删除，请使用强制删除',
            canForceDelete: true
          },
          { status: 400 }
        )
      }

      if (giftCard.saleOrder && giftCard.saleOrder.status !== 'CANCELLED') {
        return NextResponse.json(
          { 
            success: false, 
            error: '有关联订单的礼品卡不能删除，请先处理相关订单',
            relatedOrder: giftCard.saleOrder
          },
          { status: 400 }
        )
      }
    }

    // 执行删除操作
    const result = await (prisma as any).$transaction(async (tx: any) => {
      // 1. 如果是强制删除已兑换的礼品卡，需要处理用户余额
      if (forceDelete && giftCard.status === 'REDEEMED' && giftCard.redeemedValue && giftCard.redeemedById) {
        // 从用户余额中扣除
        await tx.user.update({
          where: { id: giftCard.redeemedById },
          data: {
            depositBalance: {
              decrement: giftCard.redeemedValue
            }
          }
        })

        // 创建余额变动记录
        await tx.fundTransaction.create({
          data: {
            userId: giftCard.redeemedById,
            type: 'GIFT_CARD_DELETE',
            amount: -giftCard.redeemedValue,
            description: `管理员删除已兑换礼品卡: ${giftCard.cardCode}`,
            relatedId: giftCard.id,
            metadata: {
              adminId: session.user.id,
              adminReason: reason
            }
          }
        })
      }

      // 2. 删除相关交易记录
      await tx.giftCardTransaction.deleteMany({
        where: { giftCardId: id }
      })

      // 3. 如果有关联订单，需要更新订单状态
      if (giftCard.saleOrderId && forceDelete) {
        await tx.order.update({
          where: { id: giftCard.saleOrderId },
          data: {
            status: 'CANCELLED',
            notes: giftCard.saleOrder?.notes ? 
              `${giftCard.saleOrder.notes}\n[管理员删除礼品卡] ${new Date().toLocaleString()}: ${reason}` :
              `[管理员删除礼品卡] ${new Date().toLocaleString()}: ${reason}`
          }
        })
      }

      // 4. 删除礼品卡
      await tx.giftCard.delete({
        where: { id }
      })

      // 5. 记录操作日志
      await tx.securityLog.create({
        data: {
          userId: session.user.id,
          action: 'DELETE_GIFT_CARD',
          description: `删除礼品卡: ${giftCard.cardCode}`,
          status: 'SUCCESS',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
          userAgent: request.headers.get('user-agent') || 'Admin Panel',
          metadata: {
            giftCardId: id,
            cardCode: giftCard.cardCode,
            status: giftCard.status,
            faceValue: giftCard.faceValue,
            soldToId: giftCard.soldToId,
            redeemedById: giftCard.redeemedById,
            redeemedValue: giftCard.redeemedValue,
            forceDelete,
            reason,
            relatedOrderId: giftCard.saleOrderId
          }
        }
      })

      return {
        deletedGiftCard: giftCard,
        refundAmount: (forceDelete && giftCard.status === 'REDEEMED') ? giftCard.redeemedValue : 0
      }
    })

    return NextResponse.json({
      success: true,
      message: '礼品卡已删除',
      data: result
    })

  } catch (error) {
    console.error('删除礼品卡失败:', error)
    return NextResponse.json(
      { success: false, error: '删除礼品卡失败' },
      { status: 500 }
    )
  }
}
