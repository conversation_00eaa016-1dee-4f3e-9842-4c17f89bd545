import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 撤回礼品卡兑换权
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { reason, refundToUser = true } = body

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '请提供撤回原因' },
        { status: 400 }
      )
    }

    // 查找礼品卡
    const giftCard = await (prisma as any).giftCard.findUnique({
      where: { id },
      include: {
        redeemedBy: {
          select: { id: true, name: true, email: true }
        },
        transactions: {
          where: { transactionType: 'REDEMPTION' },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    if (!giftCard) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在' },
        { status: 404 }
      )
    }

    if (giftCard.status !== 'REDEEMED') {
      return NextResponse.json(
        { success: false, error: '只能撤回已兑换的礼品卡' },
        { status: 400 }
      )
    }

    if (!giftCard.redeemedById) {
      return NextResponse.json(
        { success: false, error: '礼品卡没有兑换者信息' },
        { status: 400 }
      )
    }

    // 执行撤回操作
    const result = await (prisma as any).$transaction(async (tx: any) => {
      // 1. 更新礼品卡状态
      const updatedGiftCard = await tx.giftCard.update({
        where: { id },
        data: {
          status: 'SOLD', // 恢复为已售出状态
          redeemedAt: null,
          redeemedById: null,
          redeemedValue: null,
          notes: giftCard.notes ? 
            `${giftCard.notes}\n[管理员撤回] ${new Date().toLocaleString()}: ${reason}` :
            `[管理员撤回] ${new Date().toLocaleString()}: ${reason}`
        }
      })

      // 2. 如果需要退款给用户
      if (refundToUser && giftCard.redeemedValue && giftCard.redeemedById) {
        // 从用户余额中扣除
        await tx.user.update({
          where: { id: giftCard.redeemedById },
          data: {
            depositBalance: {
              decrement: giftCard.redeemedValue // 从用户余额中扣除（因为之前兑换时增加了）
            }
          }
        })

        // 创建余额变动记录
        await tx.fundTransaction.create({
          data: {
            userId: giftCard.redeemedById,
            type: 'GIFT_CARD_REVOKE',
            amount: -giftCard.redeemedValue,
            description: `管理员撤回礼品卡兑换: ${giftCard.cardCode}`,
            relatedId: giftCard.id,
            metadata: {
              adminId: session.user.id,
              adminReason: reason
            }
          }
        })
      }

      // 3. 创建撤回交易记录
      await tx.giftCardTransaction.create({
        data: {
          giftCardId: id,
          transactionType: 'REVOKE',
          amount: -(giftCard.redeemedValue || 0),
          userId: giftCard.redeemedById,
          description: `管理员撤回兑换权: ${reason}`,
          adminId: session.user.id,
          metadata: {
            originalRedemptionDate: giftCard.redeemedAt,
            refundToUser,
            reason
          }
        }
      })

      // 4. 记录操作日志
      await tx.securityLog.create({
        data: {
          userId: session.user.id,
          action: 'REVOKE_GIFT_CARD',
          description: `撤回礼品卡兑换权: ${giftCard.cardCode}`,
          status: 'SUCCESS',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
          userAgent: request.headers.get('user-agent') || 'Admin Panel',
          metadata: {
            giftCardId: id,
            cardCode: giftCard.cardCode,
            originalRedeemedBy: giftCard.redeemedById,
            redeemedValue: giftCard.redeemedValue,
            refundToUser,
            reason
          }
        }
      })

      return updatedGiftCard
    })

    return NextResponse.json({
      success: true,
      message: '礼品卡兑换权已撤回',
      data: {
        giftCard: result,
        refundAmount: refundToUser ? giftCard.redeemedValue : 0,
        refundToUser: giftCard.redeemedBy
      }
    })

  } catch (error) {
    console.error('撤回礼品卡兑换权失败:', error)
    return NextResponse.json(
      { success: false, error: '撤回操作失败' },
      { status: 500 }
    )
  }
}
