import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 指定礼品卡兑换账户
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { userEmail, userId, reason } = body

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '请提供指定原因' },
        { status: 400 }
      )
    }

    if (!userEmail && !userId) {
      return NextResponse.json(
        { success: false, error: '请提供用户邮箱或用户ID' },
        { status: 400 }
      )
    }

    // 查找礼品卡
    const giftCard = await (prisma as any).giftCard.findUnique({
      where: { id },
      include: {
        assignedTo: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    if (!giftCard) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在' },
        { status: 404 }
      )
    }

    if (giftCard.status === 'REDEEMED') {
      return NextResponse.json(
        { success: false, error: '已兑换的礼品卡不能重新指定' },
        { status: 400 }
      )
    }

    if (giftCard.status === 'EXPIRED') {
      return NextResponse.json(
        { success: false, error: '已过期的礼品卡不能指定' },
        { status: 400 }
      )
    }

    // 查找目标用户
    const whereCondition = userId ? { id: userId } : { email: userEmail }
    const targetUser = await (prisma as any).user.findUnique({
      where: whereCondition,
      select: { id: true, name: true, email: true, role: true }
    })

    if (!targetUser) {
      return NextResponse.json(
        { success: false, error: '目标用户不存在' },
        { status: 404 }
      )
    }

    if (targetUser.role === 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '不能将礼品卡指定给管理员账户' },
        { status: 400 }
      )
    }

    // 检查是否已经指定给同一用户
    if (giftCard.assignedToId === targetUser.id) {
      return NextResponse.json(
        { success: false, error: '礼品卡已经指定给该用户' },
        { status: 400 }
      )
    }

    // 执行指定操作
    const result = await (prisma as any).$transaction(async (tx: any) => {
      // 1. 更新礼品卡指定信息
      const updatedGiftCard = await tx.giftCard.update({
        where: { id },
        data: {
          assignedToId: targetUser.id,
          assignedAt: new Date(),
          assignedById: session.user.id,
          notes: giftCard.notes ? 
            `${giftCard.notes}\n[管理员指定] ${new Date().toLocaleString()}: 指定给 ${targetUser.email} - ${reason}` :
            `[管理员指定] ${new Date().toLocaleString()}: 指定给 ${targetUser.email} - ${reason}`
        },
        include: {
          assignedTo: {
            select: { id: true, name: true, email: true }
          },
          assignedBy: {
            select: { id: true, name: true, email: true }
          }
        }
      })

      // 2. 创建指定交易记录
      await tx.giftCardTransaction.create({
        data: {
          giftCardId: id,
          transactionType: 'ASSIGN',
          amount: 0, // 指定操作不涉及金额变动
          userId: targetUser.id,
          description: `管理员指定兑换用户: ${reason}`,
          adminId: session.user.id,
          metadata: {
            assignedToEmail: targetUser.email,
            assignedToName: targetUser.name,
            reason,
            previousAssignedTo: giftCard.assignedToId
          }
        }
      })

      // 3. 记录操作日志
      await tx.securityLog.create({
        data: {
          userId: session.user.id,
          action: 'ASSIGN_GIFT_CARD',
          description: `指定礼品卡兑换用户: ${giftCard.cardCode} -> ${targetUser.email}`,
          status: 'SUCCESS',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
          userAgent: request.headers.get('user-agent') || 'Admin Panel',
          metadata: {
            giftCardId: id,
            cardCode: giftCard.cardCode,
            assignedToId: targetUser.id,
            assignedToEmail: targetUser.email,
            previousAssignedTo: giftCard.assignedToId,
            reason
          }
        }
      })

      return updatedGiftCard
    })

    return NextResponse.json({
      success: true,
      message: `礼品卡已指定给用户 ${targetUser.email}`,
      data: {
        giftCard: result,
        assignedTo: targetUser
      }
    })

  } catch (error) {
    console.error('指定礼品卡兑换用户失败:', error)
    return NextResponse.json(
      { success: false, error: '指定操作失败' },
      { status: 500 }
    )
  }
}

// 取消指定兑换账户
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { id } = await params
    const url = new URL(request.url)
    const reason = url.searchParams.get('reason')

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '请提供取消指定的原因' },
        { status: 400 }
      )
    }

    // 查找礼品卡
    const giftCard = await (prisma as any).giftCard.findUnique({
      where: { id },
      include: {
        assignedTo: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    if (!giftCard) {
      return NextResponse.json(
        { success: false, error: '礼品卡不存在' },
        { status: 404 }
      )
    }

    if (!giftCard.assignedToId) {
      return NextResponse.json(
        { success: false, error: '礼品卡没有指定用户' },
        { status: 400 }
      )
    }

    if (giftCard.status === 'REDEEMED') {
      return NextResponse.json(
        { success: false, error: '已兑换的礼品卡不能取消指定' },
        { status: 400 }
      )
    }

    // 执行取消指定操作
    const result = await (prisma as any).$transaction(async (tx: any) => {
      // 1. 更新礼品卡，清除指定信息
      const updatedGiftCard = await tx.giftCard.update({
        where: { id },
        data: {
          assignedToId: null,
          assignedAt: null,
          assignedById: null,
          notes: giftCard.notes ? 
            `${giftCard.notes}\n[管理员取消指定] ${new Date().toLocaleString()}: ${reason}` :
            `[管理员取消指定] ${new Date().toLocaleString()}: ${reason}`
        }
      })

      // 2. 创建取消指定交易记录
      await tx.giftCardTransaction.create({
        data: {
          giftCardId: id,
          transactionType: 'UNASSIGN',
          amount: 0,
          userId: giftCard.assignedToId,
          description: `管理员取消指定兑换用户: ${reason}`,
          adminId: session.user.id,
          metadata: {
            previousAssignedToEmail: giftCard.assignedTo?.email,
            previousAssignedToName: giftCard.assignedTo?.name,
            reason
          }
        }
      })

      // 3. 记录操作日志
      await tx.securityLog.create({
        data: {
          userId: session.user.id,
          action: 'UNASSIGN_GIFT_CARD',
          description: `取消礼品卡指定用户: ${giftCard.cardCode}`,
          status: 'SUCCESS',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
          userAgent: request.headers.get('user-agent') || 'Admin Panel',
          metadata: {
            giftCardId: id,
            cardCode: giftCard.cardCode,
            previousAssignedToId: giftCard.assignedToId,
            previousAssignedToEmail: giftCard.assignedTo?.email,
            reason
          }
        }
      })

      return updatedGiftCard
    })

    return NextResponse.json({
      success: true,
      message: '已取消礼品卡指定用户',
      data: {
        giftCard: result,
        previousAssignedTo: giftCard.assignedTo
      }
    })

  } catch (error) {
    console.error('取消指定礼品卡兑换用户失败:', error)
    return NextResponse.json(
      { success: false, error: '取消指定操作失败' },
      { status: 500 }
    )
  }
}
