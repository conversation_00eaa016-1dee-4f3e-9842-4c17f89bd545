import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取兑换券列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    
    if (status) {
      where.status = status
    }
    
    if (search) {
      where.code = {
        contains: search,
        mode: 'insensitive'
      }
    }

    // 获取兑换券列表
    const [vouchers, total] = await Promise.all([
      prisma.withdrawalVoucher.findMany({
        where,
        include: {
          usedBy: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.withdrawalVoucher.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        vouchers,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取兑换券列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 创建兑换券
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      )
    }

    const { amount, quantity, validDays = 30, notes } = await request.json()

    // 验证参数
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: '金额必须大于0' },
        { status: 400 }
      )
    }

    if (!quantity || quantity <= 0 || quantity > 100) {
      return NextResponse.json(
        { success: false, error: '数量必须在1-100之间' },
        { status: 400 }
      )
    }

    const validUntil = new Date(Date.now() + validDays * 24 * 60 * 60 * 1000)

    // 批量生成兑换券
    const vouchers = []
    for (let i = 0; i < quantity; i++) {
      // 生成12位随机兑换码
      const code = Array.from({length: 12}, () =>
        '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 36)]
      ).join('')

      vouchers.push({
        code,
        amount,
        status: 'ACTIVE',
        validUntil,
        notes: notes || `批量生成 - ${new Date().toLocaleDateString()}`
      })
    }

    // 使用事务批量插入
    const result = await prisma.$transaction(async (tx) => {
      const createdVouchers = await tx.withdrawalVoucher.createMany({
        data: vouchers
      })

      return { createdVouchers }
    })

    return NextResponse.json({
      success: true,
      message: `成功生成 ${quantity} 张兑换券`,
      data: {
        quantity: result.createdVouchers.count,
        amount,
        validUntil
      }
    })

  } catch (error) {
    console.error('创建兑换券失败:', error)
    return NextResponse.json(
      { success: false, error: '创建失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 更新兑换券状态
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      )
    }

    const { id, status, notes } = await request.json()

    if (!id) {
      return NextResponse.json(
        { success: false, error: '兑换券ID不能为空' },
        { status: 400 }
      )
    }

    const validStatuses = ['ACTIVE', 'DISABLED', 'EXPIRED']
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: '无效的状态值' },
        { status: 400 }
      )
    }

    // 更新兑换券
    const updateData: any = {}
    if (status) updateData.status = status
    if (notes !== undefined) updateData.notes = notes

    const voucher = await prisma.withdrawalVoucher.update({
      where: { id },
      data: updateData,
      include: {
        usedBy: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '兑换券更新成功',
      data: voucher
    })

  } catch (error) {
    console.error('更新兑换券失败:', error)
    return NextResponse.json(
      { success: false, error: '更新失败，请稍后重试' },
      { status: 500 }
    )
  }
}
