import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 执行数据清理任务
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { taskType } = await request.json()

    let result: any = {}

    switch (taskType) {
      case 'giftcard_cleanup':
        result = await cleanupGiftCardRecords()
        break
      case 'redemption_cleanup':
        result = await cleanupRedemptionRecords()
        break
      case 'all_cleanup':
        const giftCardResult = await cleanupGiftCardRecords()
        const redemptionResult = await cleanupRedemptionRecords()
        result = {
          giftCard: giftCardResult,
          redemption: redemptionResult,
          total: {
            deletedRecords: giftCardResult.deletedRecords + redemptionResult.deletedRecords,
            deletedTransactions: giftCardResult.deletedTransactions + redemptionResult.deletedTransactions
          }
        }
        break
      default:
        return NextResponse.json(
          { success: false, error: '无效的清理任务类型' },
          { status: 400 }
        )
    }

    // 记录清理操作日志
    await prisma.securityLog.create({
      data: {
        userId: session.user.id,
        action: 'DATA_CLEANUP',
        description: `执行数据清理任务: ${taskType}`,
        status: 'SUCCESS',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
        userAgent: request.headers.get('user-agent') || 'Admin Panel',
        metadata: result
      }
    })

    return NextResponse.json({
      success: true,
      message: '数据清理完成',
      data: result
    })

  } catch (error) {
    console.error('数据清理失败:', error)
    return NextResponse.json(
      { success: false, error: '数据清理失败' },
      { status: 500 }
    )
  }
}

// 清理礼品卡记录
async function cleanupGiftCardRecords() {
  const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)

  // 查找需要清理的已兑换礼品卡
  const expiredGiftCards = await prisma.giftCard.findMany({
    where: {
      status: 'REDEEMED',
      redeemedAt: {
        lt: fifteenDaysAgo
      }
    },
    select: {
      id: true,
      cardCode: true,
      redeemedAt: true
    }
  })

  let deletedRecords = 0
  let deletedTransactions = 0

  if (expiredGiftCards.length > 0) {
    const giftCardIds = expiredGiftCards.map(card => card.id)

    // 删除相关的交易记录
    const deletedTransactionResult = await prisma.giftCardTransaction.deleteMany({
      where: {
        giftCardId: {
          in: giftCardIds
        }
      }
    })
    deletedTransactions = deletedTransactionResult.count

    // 删除礼品卡记录
    const deletedGiftCardResult = await prisma.giftCard.deleteMany({
      where: {
        id: {
          in: giftCardIds
        }
      }
    })
    deletedRecords = deletedGiftCardResult.count
  }

  return {
    type: 'giftcard',
    deletedRecords,
    deletedTransactions,
    expiredCards: expiredGiftCards.map(card => ({
      id: card.id,
      cardCode: card.cardCode,
      redeemedAt: card.redeemedAt
    }))
  }
}

// 清理兑换券记录
async function cleanupRedemptionRecords() {
  const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)

  // 查找需要清理的已使用完毕的兑换券
  const expiredRedemptionCodes = await prisma.redemptionCode.findMany({
    where: {
      status: 'USED',
      lastUsedAt: {
        lt: fifteenDaysAgo
      }
    },
    select: {
      id: true,
      codeValue: true,
      title: true,
      lastUsedAt: true
    }
  })

  let deletedRecords = 0
  let deletedTransactions = 0

  if (expiredRedemptionCodes.length > 0) {
    const redemptionCodeIds = expiredRedemptionCodes.map(code => code.id)

    // 删除相关的使用记录
    const deletedTransactionResult = await prisma.redemptionTransaction.deleteMany({
      where: {
        redemptionCodeId: {
          in: redemptionCodeIds
        }
      }
    })
    deletedTransactions = deletedTransactionResult.count

    // 删除兑换券记录
    const deletedRedemptionResult = await prisma.redemptionCode.deleteMany({
      where: {
        id: {
          in: redemptionCodeIds
        }
      }
    })
    deletedRecords = deletedRedemptionResult.count
  }

  return {
    type: 'redemption',
    deletedRecords,
    deletedTransactions,
    expiredCodes: expiredRedemptionCodes.map(code => ({
      id: code.id,
      codeValue: code.codeValue,
      title: code.title,
      lastUsedAt: code.lastUsedAt
    }))
  }
}

// 获取清理统计信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)

    // 统计可清理的礼品卡
    const expiredGiftCardsCount = await prisma.giftCard.count({
      where: {
        status: 'REDEEMED',
        redeemedAt: {
          lt: fifteenDaysAgo
        }
      }
    })

    // 统计可清理的兑换券
    const expiredRedemptionCodesCount = await prisma.redemptionCode.count({
      where: {
        status: 'USED',
        lastUsedAt: {
          lt: fifteenDaysAgo
        }
      }
    })

    // 获取最近的清理记录
    const recentCleanupLogs = await prisma.securityLog.findMany({
      where: {
        action: 'DATA_CLEANUP'
      },
      include: {
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    return NextResponse.json({
      success: true,
      data: {
        statistics: {
          expiredGiftCards: expiredGiftCardsCount,
          expiredRedemptionCodes: expiredRedemptionCodesCount,
          totalExpiredRecords: expiredGiftCardsCount + expiredRedemptionCodesCount
        },
        recentCleanups: recentCleanupLogs.map(log => ({
          id: log.id,
          adminName: log.user.name,
          description: log.description,
          details: log.metadata ? JSON.stringify(log.metadata) : null,
          createdAt: log.createdAt
        }))
      }
    })

  } catch (error) {
    console.error('获取清理统计失败:', error)
    return NextResponse.json(
      { success: false, error: '获取清理统计失败' },
      { status: 500 }
    )
  }
}
