import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取单个公告详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params
    const announcement = await prisma.announcement.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!announcement) {
      return NextResponse.json(
        { error: '公告不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(announcement)

  } catch (error) {
    console.error('Get announcement error:', error)
    return NextResponse.json(
      { error: '获取公告详情失败' },
      { status: 500 }
    )
  }
}

// 更新公告
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const admin = await requireAdmin()

    const { id } = await params
    const body = await request.json()
    const {
      title,
      content,
      summary,
      category,
      priority,
      targetUsers,
      status,
      isSticky,
      showOnHome,
      publishAt,
      expireAt,
      reviewNote
    } = body

    // 验证公告是否存在
    const existingAnnouncement = await prisma.announcement.findUnique({
      where: { id }
    })

    if (!existingAnnouncement) {
      return NextResponse.json(
        { error: '公告不存在' },
        { status: 404 }
      )
    }

    // 验证必填字段
    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容为必填项' },
        { status: 400 }
      )
    }

    // 构建更新数据
    const updateData: any = {
      title,
      content,
      summary,
      category,
      priority,
      targetUsers,
      status,
      isSticky,
      showOnHome,
      publishAt: publishAt ? new Date(publishAt) : null,
      expireAt: expireAt ? new Date(expireAt) : null
    }

    // 如果状态变为已发布，记录审核信息
    if (status === 'PUBLISHED' && existingAnnouncement.status !== 'PUBLISHED') {
      updateData.reviewerId = admin?.id
      updateData.reviewedAt = new Date()
      updateData.reviewNote = reviewNote
    }

    // 更新公告
    const announcement = await prisma.announcement.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json(announcement)

  } catch (error) {
    console.error('Update announcement error:', error)
    return NextResponse.json(
      { error: '更新公告失败' },
      { status: 500 }
    )
  }
}

// 删除公告
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params

    // 验证公告是否存在
    const existingAnnouncement = await prisma.announcement.findUnique({
      where: { id }
    })

    if (!existingAnnouncement) {
      return NextResponse.json(
        { error: '公告不存在' },
        { status: 404 }
      )
    }

    // 软删除：更新状态为DELETED
    await prisma.announcement.update({
      where: { id },
      data: {
        status: 'DELETED'
      }
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Delete announcement error:', error)
    return NextResponse.json(
      { error: '删除公告失败' },
      { status: 500 }
    )
  }
}
