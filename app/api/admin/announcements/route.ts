import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取公告列表
export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const category = searchParams.get('category')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    
    if (status && status !== 'all') {
      where.status = status
    }
    
    if (category && category !== 'all') {
      where.category = category
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { content: { contains: search } },
        { summary: { contains: search } }
      ]
    }

    // 获取公告列表
    const [announcements, total] = await Promise.all([
      prisma.announcement.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          reviewer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: [
          { isSticky: 'desc' },
          { publishAt: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.announcement.count({ where })
    ])

    return NextResponse.json({
      announcements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get announcements error:', error)
    return NextResponse.json(
      { error: '获取公告列表失败' },
      { status: 500 }
    )
  }
}

// 创建新公告
export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    const admin = await requireAdmin()

    const body = await request.json()
    const {
      title,
      content,
      summary,
      category,
      priority,
      targetUsers,
      status,
      isSticky,
      showOnHome,
      publishAt,
      expireAt
    } = body

    // 验证必填字段
    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容为必填项' },
        { status: 400 }
      )
    }

    // 验证分类
    const validCategories = ['GENERAL', 'URGENT', 'MAINTENANCE', 'FEATURE', 'SECURITY']
    if (category && !validCategories.includes(category)) {
      return NextResponse.json(
        { error: '无效的公告分类' },
        { status: 400 }
      )
    }

    // 验证优先级
    const validPriorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT']
    if (priority && !validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: '无效的优先级' },
        { status: 400 }
      )
    }

    // 验证状态
    const validStatuses = ['DRAFT', 'PUBLISHED', 'ARCHIVED']
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: '无效的状态' },
        { status: 400 }
      )
    }

    // 创建公告
    const announcement = await prisma.announcement.create({
      data: {
        title,
        content,
        summary,
        category: category || 'GENERAL',
        priority: priority || 'NORMAL',
        targetUsers: targetUsers || 'ALL',
        status: status || 'DRAFT',
        isSticky: isSticky || false,
        showOnHome: showOnHome || false,
        publishAt: publishAt ? new Date(publishAt) : null,
        expireAt: expireAt ? new Date(expireAt) : null,
        authorId: admin?.id || ''
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json(announcement, { status: 201 })

  } catch (error) {
    console.error('Create announcement error:', error)
    return NextResponse.json(
      { error: '创建公告失败' },
      { status: 500 }
    )
  }
}
