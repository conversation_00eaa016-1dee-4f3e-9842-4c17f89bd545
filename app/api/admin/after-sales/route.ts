import { NextRequest, NextResponse } from 'next/server'
import { checkAdminPermission } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取售后申请列表（管理员视角）
export async function GET(request: NextRequest) {
  try {
    const { isAdmin } = await checkAdminPermission()
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type') || ''
    const status = searchParams.get('status') || ''
    const priority = searchParams.get('priority') || ''
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // 构建查询条件
    let where: any = {}

    if (search) {
      where.OR = [
        { reason: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
        { buyer: { name: { contains: search, mode: 'insensitive' } } },
        { seller: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    if (startDate) {
      where.createdAt = {
        ...where.createdAt,
        gte: new Date(startDate)
      }
    }

    if (endDate) {
      where.createdAt = {
        ...where.createdAt,
        lte: new Date(endDate + 'T23:59:59.999Z')
      }
    }

    // 获取售后申请列表
    const [requests, total] = await Promise.all([
      prisma.afterSalesRequest.findMany({
        where,
        include: {
          order: {
            select: {
              orderNumber: true,
              totalAmount: true,
              status: true,
              product: {
                select: {
                  title: true,
                  images: true,
                  price: true
                }
              }
            }
          },
          buyer: {
            select: {
              id: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          messages: {
            select: {
              id: true,
              content: true,
              senderId: true,
              createdAt: true,
              isRead: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 1
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        },
        skip,
        take: limit
      }),
      prisma.afterSalesRequest.count({ where })
    ])

    // 计算分页信息
    const totalPages = Math.ceil(total / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1

    // 计算优先级（基于状态、金额、时间等因素）
    const requestsWithPriority = requests.map(request => {
      let priorityScore = 0
      
      // 状态优先级
      if (request.status === 'PENDING') priorityScore += 10
      else if (request.status === 'PROCESSING') priorityScore += 8
      else if (request.status === 'APPROVED') priorityScore += 6
      
      // 类型优先级
      if (request.type === 'REFUND') priorityScore += 5
      else if (request.type === 'EXCHANGE') priorityScore += 3
      
      // 金额优先级
      if (request.requestedAmount && request.requestedAmount > 1000) priorityScore += 5
      else if (request.requestedAmount && request.requestedAmount > 500) priorityScore += 3
      
      // 时间优先级（超过3天的申请）
      const daysSinceCreated = Math.floor((Date.now() - new Date(request.createdAt).getTime()) / (1000 * 60 * 60 * 24))
      if (daysSinceCreated > 3) priorityScore += 5
      else if (daysSinceCreated > 1) priorityScore += 2
      
      let priority = 'LOW'
      if (priorityScore >= 15) priority = 'HIGH'
      else if (priorityScore >= 10) priority = 'MEDIUM'
      
      return {
        ...request,
        priority,
        priorityScore,
        daysSinceCreated,
        lastMessage: request.messages[0] || null,
        unreadMessagesCount: request.messages.filter(msg => !msg.isRead).length
      }
    })

    return NextResponse.json({
      requests: requestsWithPriority,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    })

  } catch (error) {
    console.error('获取售后申请列表失败:', error)
    return NextResponse.json(
      { error: '获取售后申请列表失败' },
      { status: 500 }
    )
  }
}

// 管理员处理售后申请
export async function POST(request: NextRequest) {
  try {
    const { isAdmin } = await checkAdminPermission()
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { requestId, action, reason, amount, notes } = body

    if (!requestId || !action) {
      return NextResponse.json(
        { error: '请提供申请ID和操作类型' },
        { status: 400 }
      )
    }

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id: requestId },
      include: {
        order: {
          include: {
            product: true,
            buyer: true,
            seller: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    let updateData: any = {}
    let statusMessage = ''

    switch (action) {
      case 'approve':
        updateData.status = 'APPROVED'
        updateData.adminNotes = notes
        statusMessage = '管理员已同意此售后申请'
        break

      case 'reject':
        if (!reason) {
          return NextResponse.json(
            { error: '拒绝申请需要提供原因' },
            { status: 400 }
          )
        }
        updateData.status = 'REJECTED'
        updateData.adminNotes = notes
        updateData.rejectionReason = reason
        statusMessage = `管理员已拒绝此售后申请：${reason}`
        break

      case 'process_refund':
        if (afterSalesRequest.type !== 'REFUND') {
          return NextResponse.json(
            { error: '只能对退款申请执行退款操作' },
            { status: 400 }
          )
        }
        
        const refundAmount = amount || afterSalesRequest.requestedAmount
        
        // 执行退款逻辑
        await prisma.$transaction(async (tx) => {
          // 更新售后申请状态
          await tx.afterSalesRequest.update({
            where: { id: requestId },
            data: {
              status: 'COMPLETED',
              refundAmount: refundAmount,
              adminNotes: notes,
              completedAt: new Date()
            }
          })

          // 更新买家余额
          await tx.user.update({
            where: { id: afterSalesRequest.order.buyerId },
            data: {
              availableBalance: {
                increment: refundAmount
              }
            }
          })

          // 创建余额变动记录
          await tx.fundTransaction.create({
            data: {
              userId: afterSalesRequest.order.buyerId,
              type: 'REFUND',
              amount: refundAmount,
              description: `售后退款 - 订单 ${afterSalesRequest.order.orderNumber}`,
              relatedId: afterSalesRequest.orderId
            }
          })
        })

        statusMessage = `管理员已处理退款，退款金额：¥${refundAmount}`
        break

      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        )
    }

    // 更新售后申请（如果不是退款处理）
    if (action !== 'process_refund') {
      await prisma.afterSalesRequest.update({
        where: { id: requestId },
        data: updateData
      })
    }

    // 添加系统消息
    await prisma.afterSalesMessage.create({
      data: {
        afterSalesId: requestId,
        senderId: 'system', // 系统消息
        content: statusMessage,
        messageType: 'SYSTEM'
      }
    })

    return NextResponse.json({
      success: true,
      message: '操作成功',
      action,
      statusMessage
    })

  } catch (error) {
    console.error('处理售后申请失败:', error)
    return NextResponse.json(
      { error: '处理售后申请失败' },
      { status: 500 }
    )
  }
}
