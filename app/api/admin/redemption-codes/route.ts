import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 生成16位兑换码
function generateRedemptionCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 创建兑换券
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const {
      codeType, // DIRECT_CREDIT, REDEMPTION_CODE
      rewardType, // WITHDRAWAL_FEE_DISCOUNT, PLATFORM_POINTS, SHOPPING_VOUCHER, CASH_CREDIT
      rewardValue,
      rewardUnit = 'USDT',
      distributionType, // PUBLIC, TARGETED, BATCH
      targetUserIds = [],
      quantity = 1,
      validDays = 30,
      maxUses = 1,
      title,
      description,
      terms,
      batchName
    } = await request.json()

    // 验证必要参数
    if (!codeType || !rewardType || !rewardValue || !distributionType || !title) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      )
    }

    if (rewardValue <= 0) {
      return NextResponse.json(
        { success: false, error: '奖励价值必须大于0' },
        { status: 400 }
      )
    }

    if (quantity <= 0 || quantity > 10000) {
      return NextResponse.json(
        { success: false, error: '数量必须在1-10000之间' },
        { status: 400 }
      )
    }

    const validFrom = new Date()
    const validUntil = new Date(Date.now() + validDays * 24 * 60 * 60 * 1000)
    const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`

    // 使用事务创建兑换券
    const result = await prisma.$transaction(async (tx) => {
      const createdCodes: any[] = []

      if (codeType === 'DIRECT_CREDIT') {
        // 直购式：直接添加到用户账户
        if (distributionType === 'TARGETED' && targetUserIds.length > 0) {
          for (const userIdOrEmail of targetUserIds) {
            // 验证用户存在（支持ID或邮箱）
            const isEmail = userIdOrEmail.includes('@')
            const user = await (tx as any).user.findUnique({
              where: isEmail ? { email: userIdOrEmail } : { id: userIdOrEmail },
              select: { id: true, name: true, email: true }
            })

            if (!user) {
              throw new Error(`用户 ${userIdOrEmail} 不存在`)
            }

            // 创建兑换券记录
            const redemptionCode = await (tx as any).redemptionCode.create({
              data: {
                codeType,
                rewardType,
                rewardValue,
                rewardUnit,
                distributionType,
                targetUserId: user.id,
                batchId,
                maxUses,
                validFrom,
                validUntil,
                status: 'USED', // 直购式立即标记为已使用
                firstUsedAt: new Date(),
                lastUsedAt: new Date(),
                usedCount: 1,
                createdById: session.user.id,
                title,
                description,
                terms
              }
            })

            // 根据奖励类型处理
            await processReward(tx, user.id, rewardType, rewardValue, rewardUnit, redemptionCode.id)

            createdCodes.push(redemptionCode)
          }
        } else {
          throw new Error('直购式兑换券必须指定目标用户')
        }
      } else {
        // 兑换码式：生成兑换码
        const generatedCodes = new Set()

        // 如果是定向发放，查找目标用户
        let targetUserId = null
        if (distributionType === 'TARGETED' && targetUserIds.length > 0) {
          const userIdOrEmail = targetUserIds[0]
          const isEmail = userIdOrEmail.includes('@')
          const targetUser = await (tx as any).user.findUnique({
            where: isEmail ? { email: userIdOrEmail } : { id: userIdOrEmail },
            select: { id: true, name: true, email: true }
          })

          if (!targetUser) {
            throw new Error(`目标用户 ${userIdOrEmail} 不存在`)
          }

          targetUserId = targetUser.id
        }

        for (let i = 0; i < quantity; i++) {
          let codeValue
          do {
            codeValue = generateRedemptionCode()
          } while (generatedCodes.has(codeValue))
          
          generatedCodes.add(codeValue)

          const redemptionCode = await (tx as any).redemptionCode.create({
            data: {
              codeValue,
              codeType,
              rewardType,
              rewardValue,
              rewardUnit,
              distributionType,
              targetUserId,
              batchId,
              maxUses,
              validFrom,
              validUntil,
              status: 'ACTIVE',
              createdById: session.user.id,
              title,
              description,
              terms
            }
          })

          createdCodes.push(redemptionCode)
        }

        // 检查数据库中是否有重复的兑换码
        const existingCodes = await (tx as any).redemptionCode.findMany({
          where: {
            codeValue: {
              in: Array.from(generatedCodes)
            }
          },
          select: { codeValue: true }
        })

        if (existingCodes.length > 0) {
          throw new Error('生成的兑换码与现有兑换码冲突，请重试')
        }
      }

      // 记录操作日志
      await (tx as any).securityLog.create({
        data: {
          userId: session.user.id,
          action: 'CREATE_REDEMPTION_CODES',
          description: `创建${codeType === 'DIRECT_CREDIT' ? '直购式' : '兑换码式'}兑换券 ${createdCodes.length} 个`,
          status: 'SUCCESS',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
          userAgent: request.headers.get('user-agent') || 'Admin Panel',
          metadata: {
            batchId,
            codeType,
            rewardType,
            rewardValue,
            rewardUnit,
            distributionType,
            quantity: createdCodes.length,
            validDays,
            batchName
          }
        }
      })

      return createdCodes
    })

    return NextResponse.json({
      success: true,
      message: `成功创建 ${result.length} 个兑换券`,
      data: {
        batchId,
        quantity: result.length,
        codeType,
        rewardType,
        rewardValue,
        rewardUnit,
        validUntil,
        codes: codeType === 'REDEMPTION_CODE' ? result.map(code => code.codeValue) : undefined
      }
    })

  } catch (error) {
    console.error('创建兑换券失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '创建兑换券失败'
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    )
  }
}

// 处理奖励发放
async function processReward(
  tx: any,
  userId: string,
  rewardType: string,
  rewardValue: number,
  rewardUnit: string,
  redemptionCodeId: string
) {
  switch (rewardType) {
    case 'CASH_CREDIT':
      // 现金奖励：直接增加用户余额
      await (tx as any).user.update({
        where: { id: userId },
        data: {
          depositBalance: {
            increment: rewardValue
          }
        }
      })

      await (tx as any).fundTransaction.create({
        data: {
          userId,
          type: 'REDEMPTION_REWARD',
          amount: rewardValue,
          description: `兑换券奖励: ${rewardValue} ${rewardUnit}`,
          relatedId: redemptionCodeId,
          metadata: {
            relatedType: 'REDEMPTION_CODE',
            rewardUnit
          }
        }
      })
      break



    case 'WITHDRAWAL_FEE_DISCOUNT':
      // 提现手续费减免：创建优惠券记录
      await (tx as any).userCoupon.create({
        data: {
          userId,
          couponType: 'WITHDRAWAL_FEE_DISCOUNT',
          discountValue: rewardValue,
          discountUnit: rewardUnit,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天有效期
          status: 'ACTIVE',
          sourceId: redemptionCodeId,
          sourceType: 'REDEMPTION_CODE'
        }
      })
      break

    case 'SHOPPING_VOUCHER':
      // 购物券：创建购物券记录
      await (tx as any).shoppingVoucher.create({
        data: {
          userId,
          voucherValue: rewardValue,
          voucherUnit: rewardUnit,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          status: 'ACTIVE',
          sourceId: redemptionCodeId,
          sourceType: 'REDEMPTION_CODE'
        }
      })
      break

    default:
      throw new Error(`不支持的奖励类型: ${rewardType}`)
  }

  // 创建兑换券使用记录
  await (tx as any).redemptionTransaction.create({
    data: {
      redemptionCodeId,
      userId,
      transactionType: 'USE',
      rewardValue,
      rewardUnit,
      usageContext: 'DIRECT_CREDIT',
      description: `直购式兑换券自动发放: ${rewardType}`,
      metadata: {
        rewardType,
        processedAt: new Date().toISOString()
      }
    }
  })
}

// 获取兑换券列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const codeType = searchParams.get('codeType')
    const rewardType = searchParams.get('rewardType')
    const status = searchParams.get('status')
    const batchId = searchParams.get('batchId')
    const search = searchParams.get('search')

    // 构建查询条件
    const where: any = {}

    if (codeType) where.codeType = codeType
    if (rewardType) where.rewardType = rewardType
    if (status) where.status = status
    if (batchId) where.batchId = batchId

    if (search) {
      where.OR = [
        { codeValue: { contains: search, mode: 'insensitive' } },
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    const redemptionCodes = await (prisma as any).redemptionCode.findMany({
      where,
      include: {
        targetUser: {
          select: { id: true, name: true, email: true }
        },
        createdBy: {
          select: { id: true, name: true }
        },
        transactions: {
          select: {
            id: true,
            userId: true,
            transactionType: true,
            createdAt: true,
            user: {
              select: { name: true }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.redemptionCode.count({ where })

    // 获取统计信息
    const stats = await prisma.redemptionCode.groupBy({
      by: ['status', 'codeType', 'rewardType'],
      _count: {
        id: true
      },
      _sum: {
        rewardValue: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        redemptionCodes,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        stats
      }
    })

  } catch (error) {
    console.error('获取兑换券列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取兑换券列表失败' },
      { status: 500 }
    )
  }
}
