import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// 验证邮箱格式
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证密码强度
function isValidPassword(password: string): boolean {
  return password.length >= 6
}

// 生成管理员用户ID
function generateAdminUserId(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = 'admin-'
  for (let i = 0; i < 10; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // 检查是否已有管理员权限（只有管理员可以创建其他管理员）
    if (session?.user?.id) {
      const currentUser = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { role: true }
      })
      
      if (currentUser?.role !== 'ADMIN') {
        return NextResponse.json(
          { error: '只有管理员可以创建其他管理员账号' },
          { status: 403 }
        )
      }
    } else {
      // 如果没有登录，检查是否已有管理员存在
      const adminCount = await prisma.user.count({
        where: { role: 'ADMIN' }
      })
      
      if (adminCount > 0) {
        return NextResponse.json(
          { error: '系统已有管理员，请先登录' },
          { status: 403 }
        )
      }
    }

    const body = await request.json()
    const { email, password, name } = body

    // 验证输入
    if (!email || !password) {
      return NextResponse.json(
        { error: '邮箱和密码不能为空' },
        { status: 400 }
      )
    }

    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      )
    }

    if (!isValidPassword(password)) {
      return NextResponse.json(
        { error: '密码至少需要6位字符' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      // 如果用户已存在，询问是否更新为管理员
      if (existingUser.role === 'ADMIN') {
        return NextResponse.json(
          { error: '该邮箱已经是管理员账号' },
          { status: 400 }
        )
      }

      // 更新现有用户为管理员
      const hashedPassword = await bcrypt.hash(password, 12)
      
      const updatedUser = await prisma.user.update({
        where: { email },
        data: {
          role: 'ADMIN',
          name: name || existingUser.name || '系统管理员',
          password: hashedPassword,
          emailVerified: new Date(),
          creditScore: Math.max(existingUser.creditScore, 100) // 确保管理员有高信用分
        },
        select: {
          id: true,
          userId: true,
          email: true,
          name: true,
          role: true,
          creditScore: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true
        }
      })

      return NextResponse.json({
        success: true,
        message: '现有用户已更新为管理员',
        data: updatedUser
      })
    }

    // 生成唯一用户ID
    let userId = generateAdminUserId()
    let existingUserId = await prisma.user.findUnique({
      where: { userId }
    })
    
    while (existingUserId) {
      userId = generateAdminUserId()
      existingUserId = await prisma.user.findUnique({
        where: { userId }
      })
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 创建新管理员用户
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name: name || '系统管理员',
        userId,
        role: 'ADMIN',
        creditScore: 100, // 管理员默认高信用分
        emailVerified: new Date(), // 管理员账号默认已验证
        status: 'ACTIVE',
        depositBalance: 0
      },
      select: {
        id: true,
        userId: true,
        email: true,
        name: true,
        role: true,
        creditScore: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true
      }
    })

    // 验证创建结果
    const loginTest = await bcrypt.compare(password, hashedPassword)
    
    return NextResponse.json({
      success: true,
      message: '管理员账号创建成功',
      data: newUser,
      verification: {
        passwordTest: loginTest,
        roleVerified: newUser.role === 'ADMIN',
        emailVerified: !!newUser.emailVerified
      }
    })

  } catch (error) {
    console.error('创建管理员账号失败:', error)
    
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: '该邮箱或用户ID已被使用' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: '创建管理员账号失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取管理员创建状态
export async function GET() {
  try {
    const adminCount = await prisma.user.count({
      where: { role: 'ADMIN' }
    })
    
    const totalUsers = await prisma.user.count()
    
    return NextResponse.json({
      hasAdmin: adminCount > 0,
      adminCount,
      totalUsers,
      canCreateAdmin: adminCount === 0 // 只有在没有管理员时才允许直接创建
    })
    
  } catch (error) {
    console.error('获取管理员状态失败:', error)
    return NextResponse.json(
      { error: '获取管理员状态失败' },
      { status: 500 }
    )
  }
}
