import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || ''
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    
    if (status) {
      where.status = status
    }

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { txHash: { contains: search, mode: 'insensitive' } },
        { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
        { order: { buyer: { email: { contains: search, mode: 'insensitive' } } } },
        { order: { seller: { email: { contains: search, mode: 'insensitive' } } } }
      ]
    }

    // 获取托管支付记录
    const [escrowPayments, total] = await Promise.all([
      prisma.escrowPayment.findMany({
        where,
        include: {
          order: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  price: true
                }
              },
              buyer: {
                select: {
                  id: true,
                  userId: true,
                  name: true,
                  email: true,
                  creditScore: true
                }
              },
              seller: {
                select: {
                  id: true,
                  userId: true,
                  name: true,
                  email: true,
                  creditScore: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.escrowPayment.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      escrowPayments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Admin escrow API error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取托管记录失败' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const body = await request.json()
    const { escrowId, action, data } = body

    if (!escrowId || !action) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 查找托管记录
    const escrowPayment = await prisma.escrowPayment.findUnique({
      where: { id: escrowId },
      include: {
        order: {
          include: {
            product: true,
            buyer: true,
            seller: true
          }
        }
      }
    })

    if (!escrowPayment) {
      return NextResponse.json(
        { error: '托管记录不存在' },
        { status: 404 }
      )
    }

    let result

    switch (action) {
      case 'confirmFunding':
        // 确认资金到账
        result = await prisma.$transaction(async (tx) => {
          // 更新托管记录
          const updatedEscrow = await tx.escrowPayment.update({
            where: { id: escrowId },
            data: {
              status: 'FUNDED',
              fundedAt: new Date(),
              adminNotes: data.notes || '管理员确认资金到账'
            }
          })

          // 更新订单状态
          await tx.order.update({
            where: { id: escrowPayment.orderId },
            data: {
              escrowStatus: 'FUNDED',
              escrowFundedAt: new Date(),
              paymentConfirmed: true
            }
          })

          return updatedEscrow
        })
        break

      case 'releaseFunds':
        // 释放资金给卖家
        if (escrowPayment.status !== 'FUNDED') {
          return NextResponse.json(
            { error: '只能释放已确认到账的资金' },
            { status: 400 }
          )
        }

        result = await prisma.$transaction(async (tx) => {
          // 计算释放金额（扣除平台手续费）
          const releaseAmount = escrowPayment.amount - escrowPayment.platformFee

          // 更新托管记录
          const updatedEscrow = await tx.escrowPayment.update({
            where: { id: escrowId },
            data: {
              status: 'RELEASED',
              releasedAt: new Date(),
              adminNotes: data.notes || `管理员释放资金给卖家，金额: ${releaseAmount} USDT`
            }
          })

          // 更新订单状态
          await tx.order.update({
            where: { id: escrowPayment.orderId },
            data: {
              escrowStatus: 'RELEASED',
              escrowReleasedAt: new Date(),
              status: 'COMPLETED'
            }
          })

          return updatedEscrow
        })
        break

      case 'refundBuyer':
        // 退款给买家
        if (escrowPayment.status !== 'FUNDED') {
          return NextResponse.json(
            { error: '只能退款已确认到账的资金' },
            { status: 400 }
          )
        }

        const refundAmount = data.refundAmount || escrowPayment.amount

        result = await prisma.$transaction(async (tx) => {
          // 更新托管记录
          const updatedEscrow = await tx.escrowPayment.update({
            where: { id: escrowId },
            data: {
              status: 'REFUNDED',
              refundedAt: new Date(),
              adminNotes: data.notes || `管理员退款给买家，金额: ${refundAmount} USDT`
            }
          })

          // 更新订单状态
          await tx.order.update({
            where: { id: escrowPayment.orderId },
            data: {
              escrowStatus: 'REFUNDED',
              status: 'REFUND_APPROVED',
              refundAmount: refundAmount,
              refundReason: data.notes || '管理员处理退款'
            }
          })

          return updatedEscrow
        })
        break

      case 'addNotes':
        // 添加管理员备注
        result = await prisma.escrowPayment.update({
          where: { id: escrowId },
          data: {
            adminNotes: data.notes
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      escrowPayment: result
    })

  } catch (error) {
    console.error('Admin escrow update error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '更新托管记录失败' },
      { status: 500 }
    )
  }
}
