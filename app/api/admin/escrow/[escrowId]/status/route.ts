import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 更新托管订单状态（管理员）
export async function PATCH(
  request: NextRequest,
  { params }: { params: { escrowId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { escrowId } = params
    const body = await request.json()
    const { status, reason } = body

    // 验证状态值
    const validStatuses = ['PENDING', 'FUNDED', 'SHIPPED', 'DELIVERED', 'COMPLETED', 'DISPUTED', 'CANCELLED']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: '无效的状态值' },
        { status: 400 }
      )
    }

    // 获取当前托管订单
    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowId },
      include: {
        order: true,
        mediator: true,
        buyer: true,
        seller: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      status,
      updatedAt: new Date()
    }

    // 根据状态设置相应的时间戳
    switch (status) {
      case 'FUNDED':
        updateData.fundedAt = new Date()
        break
      case 'SHIPPED':
        updateData.shippedAt = new Date()
        break
      case 'DELIVERED':
        updateData.deliveredAt = new Date()
        break
      case 'COMPLETED':
        updateData.completedAt = new Date()
        break
      case 'DISPUTED':
        updateData.disputedAt = new Date()
        break
      case 'CANCELLED':
        updateData.cancelledAt = new Date()
        break
    }

    // 执行更新
    const updatedOrder = await prisma.$transaction(async (tx) => {
      // 更新托管订单状态
      const updated = await tx.escrowOrder.update({
        where: { id: escrowId },
        data: updateData,
        include: {
          order: true,
          mediator: true,
          buyer: true,
          seller: true
        }
      })

      // 创建系统消息到聊天室
      const chatRoom = await tx.escrowChatRoom.findUnique({
        where: { escrowOrderId: escrowId }
      })

      if (chatRoom) {
        await tx.escrowChatMessage.create({
          data: {
            content: `管理员更新订单状态为: ${getStatusLabel(status)}${reason ? ` (原因: ${reason})` : ''}`,
            messageType: 'SYSTEM',
            status: 'SENT',
            chatRoomId: chatRoom.id,
            senderId: session.user.id
          }
        })
      }

      // 记录操作日志
      await tx.securityLog.create({
        data: {
          userId: session.user.id,
          action: 'ADMIN_UPDATE_ESCROW_STATUS',
          details: {
            escrowOrderId: escrowId,
            oldStatus: escrowOrder.status,
            newStatus: status,
            reason: reason || 'Admin action',
            orderNumber: escrowOrder.orderId
          },
          ipAddress: request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })

      return updated
    })

    // 发送通知给相关用户
    const notificationPromises = []
    
    // 通知买家
    notificationPromises.push(
      prisma.notification.create({
        data: {
          userId: escrowOrder.buyerId,
          type: 'ESCROW_STATUS_UPDATE',
          title: '托管订单状态更新',
          message: `您的托管订单状态已更新为: ${getStatusLabel(status)}`,
          data: {
            escrowOrderId: escrowId,
            orderNumber: escrowOrder.orderId,
            newStatus: status,
            updatedBy: 'ADMIN'
          },
          priority: 'MEDIUM'
        }
      })
    )

    // 通知卖家
    notificationPromises.push(
      prisma.notification.create({
        data: {
          userId: escrowOrder.sellerId,
          type: 'ESCROW_STATUS_UPDATE',
          title: '托管订单状态更新',
          message: `托管订单状态已更新为: ${getStatusLabel(status)}`,
          data: {
            escrowOrderId: escrowId,
            orderNumber: escrowOrder.orderId,
            newStatus: status,
            updatedBy: 'ADMIN'
          },
          priority: 'MEDIUM'
        }
      })
    )

    // 通知中间人
    notificationPromises.push(
      prisma.notification.create({
        data: {
          userId: escrowOrder.mediatorId,
          type: 'ESCROW_STATUS_UPDATE',
          title: '托管订单状态更新',
          message: `您负责的托管订单状态已更新为: ${getStatusLabel(status)}`,
          data: {
            escrowOrderId: escrowId,
            orderNumber: escrowOrder.orderId,
            newStatus: status,
            updatedBy: 'ADMIN'
          },
          priority: 'MEDIUM'
        }
      })
    )

    // 并行发送通知
    await Promise.allSettled(notificationPromises)

    return NextResponse.json({
      success: true,
      message: '托管订单状态更新成功',
      data: {
        escrowOrder: updatedOrder,
        oldStatus: escrowOrder.status,
        newStatus: status,
        updatedAt: updateData.updatedAt
      }
    })

  } catch (error) {
    console.error('更新托管订单状态失败:', error)
    return NextResponse.json(
      { error: '更新状态失败' },
      { status: 500 }
    )
  }
}

// 辅助函数：获取状态标签
function getStatusLabel(status: string): string {
  const statusLabels: { [key: string]: string } = {
    'PENDING': '待资金',
    'FUNDED': '已托管',
    'SHIPPED': '已发货',
    'DELIVERED': '已送达',
    'COMPLETED': '已完成',
    'DISPUTED': '争议中',
    'CANCELLED': '已取消'
  }
  
  return statusLabels[status] || status
}
