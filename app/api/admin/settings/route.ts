import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 默认系统设置
const defaultSettings = {
  platform: {
    name: '比特市场',
    description: 'USDT-based decentralized C2C marketplace',
    contactEmail: '<EMAIL>',
    supportPhone: '+86-************'
  },
  fees: {
    platformFeeRate: 0.02, // 2%
    guarantorFeeRate: 0.01, // 1%
    withdrawalFeeRate: 0.005, // 0.5%
    minimumOrderAmount: 10, // 10 USDT
    maximumOrderAmount: 50000 // 50,000 USDT
  },
  security: {
    requireEmailVerification: true,
    requirePhoneVerification: false,
    enableTwoFactorAuth: false,
    sessionTimeoutMinutes: 60,
    maxLoginAttempts: 5
  },
  trading: {
    allowGuestBrowsing: true,
    requireKycForSelling: false,
    autoApproveProducts: false,
    disputeTimeoutDays: 7,
    reviewTimeoutDays: 7
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    adminAlerts: true
  }
}

/**
 * 获取系统设置
 * @param category 设置分类，如果不指定则返回所有设置
 * @returns 系统设置对象
 */
async function getSystemSettings(category?: string) {
  try {
    const whereClause = category ? { category } : {}

    const settings = await prisma.systemSetting.findMany({
      where: whereClause,
      orderBy: [
        { category: 'asc' },
        { key: 'asc' }
      ]
    })

    // 如果数据库中没有设置，使用默认设置并初始化数据库
    if (settings.length === 0) {
      await initializeDefaultSettings()
      return category ? defaultSettings[category as keyof typeof defaultSettings] : defaultSettings
    }

    // 将数据库设置转换为嵌套对象格式
    const result: any = {}

    for (const setting of settings) {
      if (!result[setting.category]) {
        result[setting.category] = {}
      }
      result[setting.category][setting.key] = setting.value
    }

    return category ? result[category] : result
  } catch (error) {
    console.error('获取系统设置失败:', error)
    // 如果数据库查询失败，返回默认设置
    return category ? defaultSettings[category as keyof typeof defaultSettings] : defaultSettings
  }
}

/**
 * 初始化默认设置到数据库
 */
async function initializeDefaultSettings() {
  try {
    const settingsToCreate: any[] = []

    for (const [category, categorySettings] of Object.entries(defaultSettings)) {
      for (const [key, value] of Object.entries(categorySettings)) {
        settingsToCreate.push({
          category,
          key,
          value: value as any,
          description: getSettingDescription(category, key),
          dataType: getDataType(value),
          isPublic: isPublicSetting(category, key),
          isEditable: true
        })
      }
    }

    await prisma.systemSetting.createMany({
      data: settingsToCreate,
      skipDuplicates: true
    })

    console.log('默认系统设置已初始化')
  } catch (error) {
    console.error('初始化默认设置失败:', error)
  }
}

/**
 * 获取设置描述
 */
function getSettingDescription(category: string, key: string): string {
  const descriptions: Record<string, Record<string, string>> = {
    platform: {
      name: '平台名称',
      description: '平台描述',
      contactEmail: '联系邮箱',
      supportPhone: '客服电话'
    },
    fees: {
      platformFeeRate: '平台手续费率',
      guarantorFeeRate: '担保人手续费率',
      withdrawalFeeRate: '提现手续费率',
      minimumOrderAmount: '最小订单金额',
      maximumOrderAmount: '最大订单金额'
    },
    security: {
      requireEmailVerification: '是否需要邮箱验证',
      requirePhoneVerification: '是否需要手机验证',
      enableTwoFactorAuth: '是否启用双因素认证',
      sessionTimeoutMinutes: '会话超时时间（分钟）',
      maxLoginAttempts: '最大登录尝试次数'
    },
    trading: {
      allowGuestBrowsing: '是否允许游客浏览',
      requireKycForSelling: '是否需要KYC才能销售',
      autoApproveProducts: '是否自动审核商品',
      disputeTimeoutDays: '争议超时天数',
      reviewTimeoutDays: '评价超时天数'
    },
    notifications: {
      emailNotifications: '是否启用邮件通知',
      smsNotifications: '是否启用短信通知',
      pushNotifications: '是否启用推送通知',
      adminAlerts: '是否启用管理员警报'
    }
  }

  return descriptions[category]?.[key] || `${category}.${key}`
}

/**
 * 获取数据类型
 */
function getDataType(value: any): string {
  if (typeof value === 'boolean') return 'boolean'
  if (typeof value === 'number') return 'number'
  if (typeof value === 'string') return 'string'
  if (Array.isArray(value)) return 'array'
  if (typeof value === 'object') return 'json'
  return 'string'
}

/**
 * 判断是否为公开设置
 */
function isPublicSetting(category: string, key: string): boolean {
  const publicSettings = [
    'platform.name',
    'platform.description',
    'platform.contactEmail',
    'trading.allowGuestBrowsing',
    'fees.minimumOrderAmount',
    'fees.maximumOrderAmount'
  ]

  return publicSettings.includes(`${category}.${key}`)
}

/**
 * 验证设置值
 */
function validateSettings(category: string, settings: any): { isValid: boolean; error?: string } {
  try {
    switch (category) {
      case 'fees':
        for (const [key, value] of Object.entries(settings)) {
          if (typeof value !== 'number' || value < 0) {
            return { isValid: false, error: `费用设置 ${key} 必须为非负数` }
          }
          if (key.includes('Rate') && value > 1) {
            return { isValid: false, error: `费率 ${key} 不能超过100%` }
          }
        }
        break

      case 'security':
        if ('sessionTimeoutMinutes' in settings && (settings.sessionTimeoutMinutes < 5 || settings.sessionTimeoutMinutes > 1440)) {
          return { isValid: false, error: '会话超时时间必须在5-1440分钟之间' }
        }
        if ('maxLoginAttempts' in settings && (settings.maxLoginAttempts < 1 || settings.maxLoginAttempts > 20)) {
          return { isValid: false, error: '最大登录尝试次数必须在1-20之间' }
        }
        break

      case 'trading':
        if ('disputeTimeoutDays' in settings && (settings.disputeTimeoutDays < 1 || settings.disputeTimeoutDays > 30)) {
          return { isValid: false, error: '争议超时天数必须在1-30天之间' }
        }
        if ('reviewTimeoutDays' in settings && (settings.reviewTimeoutDays < 1 || settings.reviewTimeoutDays > 30)) {
          return { isValid: false, error: '评价超时天数必须在1-30天之间' }
        }
        break

      case 'platform':
        if ('contactEmail' in settings && settings.contactEmail && !isValidEmail(settings.contactEmail)) {
          return { isValid: false, error: '联系邮箱格式不正确' }
        }
        break
    }

    return { isValid: true }
  } catch (error) {
    return { isValid: false, error: '设置验证失败' }
  }
}

/**
 * 验证邮箱格式
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const includeMetadata = searchParams.get('metadata') === 'true'

    const settings = await getSystemSettings(category || undefined)

    if (includeMetadata) {
      // 返回包含元数据的详细设置信息
      const whereClause = category ? { category } : {}
      const settingsWithMetadata = await prisma.systemSetting.findMany({
        where: whereClause,
        include: {
          creator: { select: { name: true, email: true } },
          updater: { select: { name: true, email: true } }
        },
        orderBy: [
          { category: 'asc' },
          { key: 'asc' }
        ]
      })

      return NextResponse.json({
        success: true,
        settings,
        metadata: settingsWithMetadata
      })
    }

    return NextResponse.json({
      success: true,
      settings
    })

  } catch (error) {
    console.error('获取系统设置失败:', error)

    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: '获取设置失败' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const session = await getServerSession(authOptions)
    const admin = await prisma.user.findUnique({
      where: { email: session?.user?.email || '' }
    })

    if (!admin) {
      return NextResponse.json(
        { error: '管理员用户不存在' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { category, settings } = body

    if (!category || !settings) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 验证设置类别
    const validCategories = ['platform', 'fees', 'security', 'trading', 'notifications']
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: '无效的设置类别' },
        { status: 400 }
      )
    }

    // 验证设置值
    const validationResult = validateSettings(category, settings)
    if (!validationResult.isValid) {
      return NextResponse.json(
        { error: validationResult.error },
        { status: 400 }
      )
    }

    // 更新数据库中的设置
    const updatePromises: any[] = []

    for (const [key, value] of Object.entries(settings)) {
      updatePromises.push(
        prisma.systemSetting.upsert({
          where: {
            category_key: {
              category,
              key
            }
          },
          update: {
            value: value as any,
            updatedBy: admin.id
          },
          create: {
            category,
            key,
            value: value as any,
            description: getSettingDescription(category, key),
            dataType: getDataType(value),
            isPublic: isPublicSetting(category, key),
            isEditable: true,
            createdBy: admin.id,
            updatedBy: admin.id
          }
        })
      )
    }

    await Promise.all(updatePromises)

    // 获取更新后的设置
    const updatedSettings = await getSystemSettings(category)

    console.log(`系统设置已更新 - ${category}:`, settings)

    return NextResponse.json({
      success: true,
      message: '设置已更新',
      settings: updatedSettings
    })

  } catch (error) {
    console.error('Admin settings update error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '更新系统设置失败' },
      { status: 500 }
    )
  }
}

// 全局系统设置变量
let systemSettings = {
  platform: {
    name: '比特市场',
    description: 'USDT-based decentralized C2C marketplace',
    contactEmail: '<EMAIL>',
    supportPhone: '+86-************'
  },
  fees: {
    platformFeeRate: 0.02,
    guarantorFeeRate: 0.01,
    withdrawalFeeRate: 0.005,
    minimumOrderAmount: 10,
    maximumOrderAmount: 50000
  },
  security: {
    requireEmailVerification: true,
    requirePhoneVerification: false,
    enableTwoFactorAuth: false,
    sessionTimeoutMinutes: 60,
    maxLoginAttempts: 5
  },
  trading: {
    allowGuestBrowsing: true,
    requireKycForSelling: false,
    autoApproveProducts: false,
    disputeTimeoutDays: 7,
    reviewTimeoutDays: 7
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    adminAlerts: true
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'resetToDefaults':
        // 重置为默认设置
        systemSettings = {
          platform: {
            name: '比特市场',
            description: 'USDT-based decentralized C2C marketplace',
            contactEmail: '<EMAIL>',
            supportPhone: '+86-************'
          },
          fees: {
            platformFeeRate: 0.02,
            guarantorFeeRate: 0.01,
            withdrawalFeeRate: 0.005,
            minimumOrderAmount: 10,
            maximumOrderAmount: 50000
          },
          security: {
            requireEmailVerification: true,
            requirePhoneVerification: false,
            enableTwoFactorAuth: false,
            sessionTimeoutMinutes: 60,
            maxLoginAttempts: 5
          },
          trading: {
            allowGuestBrowsing: true,
            requireKycForSelling: false,
            autoApproveProducts: false,
            disputeTimeoutDays: 7,
            reviewTimeoutDays: 7
          },
          notifications: {
            emailNotifications: true,
            smsNotifications: false,
            pushNotifications: true,
            adminAlerts: true
          }
        }
        break

      case 'exportSettings':
        // 导出设置
        return NextResponse.json({
          success: true,
          data: systemSettings,
          filename: `bitmarket-settings-${new Date().toISOString().split('T')[0]}.json`
        })

      case 'clearCache':
        // 清理缓存（模拟）
        console.log('系统缓存已清理')
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: '操作完成',
      settings: systemSettings
    })

  } catch (error) {
    console.error('Admin settings action error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '执行操作失败' },
      { status: 500 }
    )
  }
}
