import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // 默认30天
    const type = searchParams.get('type') || 'overview' // overview, revenue, users, products

    const days = parseInt(period)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    let reportData: any = {}

    switch (type) {
      case 'overview':
        reportData = await getOverviewReport(startDate)
        break
      case 'revenue':
        reportData = await getRevenueReport(startDate)
        break
      case 'users':
        reportData = await getUsersReport(startDate)
        break
      case 'products':
        reportData = await getProductsReport(startDate)
        break
      default:
        reportData = await getOverviewReport(startDate)
    }

    return NextResponse.json({
      success: true,
      period: days,
      type,
      data: reportData
    })

  } catch (error) {
    console.error('Admin reports API error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取报表数据失败' },
      { status: 500 }
    )
  }
}

// 概览报表
async function getOverviewReport(startDate: Date) {
  const [
    totalUsers,
    newUsers,
    totalProducts,
    newProducts,
    totalOrders,
    newOrders,
    totalRevenue,
    newRevenue
  ] = await Promise.all([
    // 总用户数
    prisma.user.count(),
    // 新用户数
    prisma.user.count({
      where: { createdAt: { gte: startDate } }
    }),
    // 总商品数
    prisma.product.count(),
    // 新商品数
    prisma.product.count({
      where: { createdAt: { gte: startDate } }
    }),
    // 总订单数
    prisma.order.count(),
    // 新订单数
    prisma.order.count({
      where: { createdAt: { gte: startDate } }
    }),
    // 总收入
    prisma.order.aggregate({
      where: { paymentConfirmed: true },
      _sum: { platformFee: true }
    }),
    // 新收入
    prisma.order.aggregate({
      where: { 
        paymentConfirmed: true,
        createdAt: { gte: startDate }
      },
      _sum: { platformFee: true }
    })
  ])

  return {
    users: {
      total: totalUsers,
      new: newUsers,
      growth: totalUsers > 0 ? ((newUsers / totalUsers) * 100).toFixed(1) : '0'
    },
    products: {
      total: totalProducts,
      new: newProducts,
      growth: totalProducts > 0 ? ((newProducts / totalProducts) * 100).toFixed(1) : '0'
    },
    orders: {
      total: totalOrders,
      new: newOrders,
      growth: totalOrders > 0 ? ((newOrders / totalOrders) * 100).toFixed(1) : '0'
    },
    revenue: {
      total: totalRevenue._sum.platformFee || 0,
      new: newRevenue._sum.platformFee || 0,
      growth: (totalRevenue._sum.platformFee || 0) > 0 ? 
        (((newRevenue._sum.platformFee || 0) / (totalRevenue._sum.platformFee || 1)) * 100).toFixed(1) : '0'
    }
  }
}

// 收入报表
async function getRevenueReport(startDate: Date) {
  // 按天统计收入
  const dailyRevenue = await prisma.$queryRaw`
    SELECT 
      DATE(createdAt) as date,
      SUM(platformFee) as revenue,
      COUNT(*) as orders
    FROM Order 
    WHERE paymentConfirmed = true 
      AND createdAt >= ${startDate}
    GROUP BY DATE(createdAt)
    ORDER BY date ASC
  `

  // 按支付状态统计
  const paymentStats = await prisma.order.groupBy({
    by: ['paymentConfirmed'],
    where: { createdAt: { gte: startDate } },
    _count: { id: true },
    _sum: { totalAmount: true, platformFee: true }
  })

  return {
    dailyRevenue,
    paymentStats: paymentStats.map(stat => ({
      status: stat.paymentConfirmed ? 'confirmed' : 'pending',
      count: stat._count.id,
      totalAmount: stat._sum.totalAmount || 0,
      platformFee: stat._sum.platformFee || 0
    }))
  }
}

// 用户报表
async function getUsersReport(startDate: Date) {
  // 按天统计新用户
  const dailyUsers = await prisma.$queryRaw`
    SELECT 
      DATE(createdAt) as date,
      COUNT(*) as newUsers
    FROM User 
    WHERE createdAt >= ${startDate}
    GROUP BY DATE(createdAt)
    ORDER BY date ASC
  `

  // 按角色统计
  const roleStats = await prisma.user.groupBy({
    by: ['role'],
    _count: { id: true }
  })

  // 活跃用户（有订单的用户）
  const activeUsers = await prisma.user.count({
    where: {
      OR: [
        { ordersAsBuyer: { some: {} } },
        { ordersAsSeller: { some: {} } }
      ]
    }
  })

  return {
    dailyUsers,
    roleStats: roleStats.map(stat => ({
      role: stat.role,
      count: stat._count.id
    })),
    activeUsers
  }
}

// 商品报表
async function getProductsReport(startDate: Date) {
  // 按天统计新商品
  const dailyProducts = await prisma.$queryRaw`
    SELECT 
      DATE(createdAt) as date,
      COUNT(*) as newProducts
    FROM Product 
    WHERE createdAt >= ${startDate}
    GROUP BY DATE(createdAt)
    ORDER BY date ASC
  `

  // 按状态统计
  const statusStats = await prisma.product.groupBy({
    by: ['status'],
    _count: { id: true }
  })

  // 热门商品（按订单数）
  const popularProducts = await prisma.product.findMany({
    include: {
      _count: { select: { orders: true } },
      seller: { select: { name: true, email: true } }
    },
    orderBy: { orders: { _count: 'desc' } },
    take: 10
  })

  return {
    dailyProducts,
    statusStats: statusStats.map(stat => ({
      status: stat.status,
      count: stat._count.id
    })),
    popularProducts: popularProducts.map(product => ({
      id: product.id,
      title: product.title,
      price: product.price,
      orderCount: product._count.orders,
      seller: product.seller
    }))
  }
}
