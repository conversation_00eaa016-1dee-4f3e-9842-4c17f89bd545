import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const category = searchParams.get('category') || ''

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { seller: { name: { contains: search, mode: 'insensitive' } } },
        { seller: { email: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (category) {
      where.category = category
    }

    // 获取商品列表
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          seller: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          _count: {
            select: {
              orders: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.product.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Admin products API error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取商品列表失败' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const body = await request.json()
    const { productId, action, data } = body

    if (!productId || !action) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    let result
    
    switch (action) {
      case 'updateStatus':
        if (!data.status || !['ACTIVE', 'INACTIVE', 'PENDING', 'REJECTED'].includes(data.status)) {
          return NextResponse.json(
            { error: '无效的状态' },
            { status: 400 }
          )
        }
        
        result = await prisma.product.update({
          where: { id: productId },
          data: { status: data.status },
          include: {
            seller: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })
        break

      case 'updatePrice':
        if (typeof data.priceUsdt !== 'number' || data.priceUsdt <= 0) {
          return NextResponse.json(
            { error: '价格必须大于0' },
            { status: 400 }
          )
        }
        
        result = await prisma.product.update({
          where: { id: productId },
          data: {
            price: data.priceUsdt || data.price
          },
          include: {
            seller: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })
        break

      case 'delete':
        // 检查是否有关联的订单
        const orderCount = await prisma.order.count({
          where: { productId }
        })

        if (orderCount > 0) {
          return NextResponse.json(
            { error: '该商品有关联订单，无法删除。请先将商品状态设为下架。' },
            { status: 400 }
          )
        }

        result = await prisma.product.delete({
          where: { id: productId }
        })
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      product: result
    })

  } catch (error) {
    console.error('Admin products update error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '更新商品失败' },
      { status: 500 }
    )
  }
}
