import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('🔍 检查管理员权限...')
    
    // 获取会话
    const session = await getServerSession(authOptions)
    console.log('📋 会话信息:', {
      hasSession: !!session,
      userId: session?.user?.id,
      userEmail: session?.user?.email
    })
    
    if (!session?.user?.id) {
      console.log('❌ 没有有效会话')
      return NextResponse.json(
        { 
          error: '未登录',
          isAdmin: false,
          debug: {
            hasSession: !!session,
            hasUserId: !!session?.user?.id
          }
        },
        { status: 401 }
      )
    }

    // 查询用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true
      }
    })

    console.log('👤 用户信息:', user)

    if (!user) {
      console.log('❌ 用户不存在')
      return NextResponse.json(
        { 
          error: '用户不存在',
          isAdmin: false,
          debug: {
            sessionUserId: session.user.id,
            userFound: false
          }
        },
        { status: 404 }
      )
    }

    if (user.role !== 'ADMIN') {
      console.log('❌ 用户不是管理员，当前角色:', user.role)
      return NextResponse.json(
        { 
          error: '需要管理员权限',
          isAdmin: false,
          debug: {
            userRole: user.role,
            requiredRole: 'ADMIN'
          }
        },
        { status: 403 }
      )
    }

    console.log('✅ 管理员权限验证通过')
    return NextResponse.json({
      isAdmin: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      debug: {
        sessionValid: true,
        userFound: true,
        roleValid: true
      }
    })

  } catch (error) {
    console.error('❌ 管理员权限检查失败:', error)
    return NextResponse.json(
      { 
        error: '服务器错误',
        isAdmin: false,
        debug: {
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      },
      { status: 500 }
    )
  }
}
