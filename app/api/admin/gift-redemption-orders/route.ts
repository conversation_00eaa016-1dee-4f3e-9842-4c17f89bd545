import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取礼品卡和兑换券相关的订单
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '无权限操作' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type') || 'all' // all, giftcard, redemption
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // 获取礼品卡销售订单
    let giftCardOrders: any[] = []
    if (type === 'all' || type === 'giftcard') {
      const giftCardWhere: any = {}
      
      if (status) {
        giftCardWhere.status = status
      }
      
      if (search) {
        giftCardWhere.OR = [
          { cardCode: { contains: search, mode: 'insensitive' } },
          { soldTo: { name: { contains: search, mode: 'insensitive' } } },
          { soldTo: { email: { contains: search, mode: 'insensitive' } } }
        ]
      }

      giftCardOrders = await prisma.giftCard.findMany({
        where: {
          ...giftCardWhere,
          status: { in: ['SOLD', 'REDEEMED'] }
        },
        include: {
          soldTo: {
            select: { id: true, name: true, email: true }
          },
          redeemedBy: {
            select: { id: true, name: true, email: true }
          },
          saleOrder: {
            select: { id: true, orderNumber: true, totalAmount: true, status: true }
          },
          transactions: {
            select: {
              id: true,
              transactionType: true,
              amount: true,
              createdAt: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: type === 'giftcard' ? skip : 0,
        take: type === 'giftcard' ? limit : undefined
      })
    }

    // 获取兑换券使用记录
    let redemptionOrders: any[] = []
    if (type === 'all' || type === 'redemption') {
      const redemptionWhere: any = {}
      
      if (status) {
        redemptionWhere.status = status
      }
      
      if (search) {
        redemptionWhere.OR = [
          { codeValue: { contains: search, mode: 'insensitive' } },
          { title: { contains: search, mode: 'insensitive' } },
          { targetUser: { name: { contains: search, mode: 'insensitive' } } },
          { targetUser: { email: { contains: search, mode: 'insensitive' } } }
        ]
      }

      redemptionOrders = await prisma.redemptionCode.findMany({
        where: {
          ...redemptionWhere,
          status: { in: ['USED', 'ACTIVE'] }
        },
        include: {
          targetUser: {
            select: { id: true, name: true, email: true }
          },
          createdBy: {
            select: { id: true, name: true }
          },
          transactions: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              },
              order: {
                select: { id: true, orderNumber: true, totalAmount: true }
              }
            },
            orderBy: { createdAt: 'desc' }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: type === 'redemption' ? skip : 0,
        take: type === 'redemption' ? limit : undefined
      })
    }

    // 合并和格式化数据
    const allOrders: any[] = []

    // 添加礼品卡订单
    giftCardOrders.forEach(card => {
      allOrders.push({
        id: card.id,
        type: 'giftcard',
        code: card.cardCode,
        title: `礼品卡 - ${card.faceValue} USDT`,
        amount: card.faceValue,
        status: card.status,
        customer: card.soldTo || card.redeemedBy,
        createdAt: card.soldAt || card.createdAt,
        redeemedAt: card.redeemedAt,
        saleOrder: card.saleOrder,
        transactions: card.transactions,
        details: {
          faceValue: card.faceValue,
          validUntil: card.validUntil,
          batchId: card.batchId,
          notes: card.notes
        }
      })
    })

    // 添加兑换券订单
    redemptionOrders.forEach(code => {
      code.transactions.forEach(transaction => {
        allOrders.push({
          id: `${code.id}-${transaction.id}`,
          type: 'redemption',
          code: code.codeValue || `直购-${code.id.slice(-8)}`,
          title: code.title,
          amount: transaction.rewardValue,
          status: code.status,
          customer: transaction.user,
          createdAt: transaction.createdAt,
          usedAt: transaction.createdAt,
          relatedOrder: transaction.order,
          details: {
            codeType: code.codeType,
            rewardType: code.rewardType,
            rewardValue: code.rewardValue,
            rewardUnit: code.rewardUnit,
            distributionType: code.distributionType,
            maxUses: code.maxUses,
            usedCount: code.usedCount,
            validFrom: code.validFrom,
            validUntil: code.validUntil,
            transactionType: transaction.transactionType,
            usageContext: transaction.usageContext
          }
        })
      })
    })

    // 排序
    allOrders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    // 分页处理（如果是查看全部）
    let paginatedOrders = allOrders
    let total = allOrders.length

    if (type === 'all') {
      paginatedOrders = allOrders.slice(skip, skip + limit)
    } else if (type === 'giftcard') {
      total = await prisma.giftCard.count({
        where: {
          status: { in: ['SOLD', 'REDEEMED'] }
        }
      })
    } else if (type === 'redemption') {
      const redemptionCount = await prisma.redemptionTransaction.count({
        where: {
          redemptionCode: {
            status: { in: ['USED', 'ACTIVE'] }
          }
        }
      })
      total = redemptionCount
    }

    return NextResponse.json({
      success: true,
      data: {
        orders: paginatedOrders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary: {
          giftCards: giftCardOrders.length,
          redemptions: redemptionOrders.reduce((sum, code) => sum + code.transactions.length, 0),
          totalValue: allOrders.reduce((sum, order) => sum + order.amount, 0)
        }
      }
    })

  } catch (error) {
    console.error('获取礼品卡和兑换券订单失败:', error)
    return NextResponse.json(
      { success: false, error: '获取订单列表失败' },
      { status: 500 }
    )
  }
}
