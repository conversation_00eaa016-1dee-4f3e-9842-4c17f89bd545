/**
 * 管理员提现审核API
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  reviewWithdrawal, 
  processWithdrawal 
} from '@/lib/adapters/withdrawal-adapter'
import { prisma } from '@/lib/prisma'

// 获取待审核的提现申请
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'PENDING'
    const limit = parseInt(searchParams.get('limit') || '50')

    const withdrawals = await prisma.withdrawal.findMany({
      where: {
        status
      },
      orderBy: {
        createdAt: 'asc' // 按创建时间升序，优先处理早期申请
      },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            creditScore: true,
            depositBalance: true
          }
        }
      }
    })

    // 获取相关的冻结记录
    const withdrawalIds = withdrawals.map(w => w.id)
    const freezeRecords = await prisma.fundFreeze.findMany({
      where: {
        relatedId: {
          in: withdrawalIds
        },
        purpose: 'WITHDRAWAL'
      }
    })

    // 将冻结记录关联到提现记录
    const withdrawalsWithFreeze = withdrawals.map(withdrawal => ({
      ...withdrawal,
      freezeRecord: freezeRecords.find(f => f.relatedId === withdrawal.id)
    }))

    return NextResponse.json({
      withdrawals: withdrawalsWithFreeze,
      total: withdrawalsWithFreeze.length
    })

  } catch (error) {
    console.error('获取提现申请失败:', error)
    return NextResponse.json(
      { error: '获取提现申请失败' },
      { status: 500 }
    )
  }
}

// 审核提现申请
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { withdrawalId, action, reviewNotes, txHash } = await request.json()

    if (!withdrawalId) {
      return NextResponse.json(
        { error: '提现ID不能为空' },
        { status: 400 }
      )
    }

    let result

    switch (action) {
      case 'approve':
        // 批准提现
        result = await reviewWithdrawal(
          withdrawalId,
          true,
          session.user.id,
          reviewNotes
        )
        break

      case 'reject':
        // 拒绝提现
        if (!reviewNotes) {
          return NextResponse.json(
            { error: '拒绝提现必须提供原因' },
            { status: 400 }
          )
        }
        result = await reviewWithdrawal(
          withdrawalId,
          false,
          session.user.id,
          reviewNotes
        )
        break

      case 'process':
        // 处理提现（实际转账）
        if (!txHash) {
          return NextResponse.json(
            { error: '处理提现必须提供交易哈希' },
            { status: 400 }
          )
        }
        result = await processWithdrawal(
          withdrawalId,
          txHash,
          session.user.id
        )
        break

      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        )
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      withdrawalId: result.withdrawalId
    })

  } catch (error) {
    console.error('审核提现申请失败:', error)
    return NextResponse.json(
      { error: '审核提现申请失败' },
      { status: 500 }
    )
  }
}
