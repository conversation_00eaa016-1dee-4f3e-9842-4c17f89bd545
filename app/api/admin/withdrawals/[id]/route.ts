import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个提现记录详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { id } = await params

    // 获取提现记录详情
    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            depositBalance: true,
            creditScore: true
          }
        }
      }
    })

    // 如果有审核人，单独获取审核人信息
    let reviewer = null
    if (withdrawal?.reviewedBy) {
      reviewer = await prisma.user.findUnique({
        where: { id: withdrawal.reviewedBy },
        select: {
          name: true,
          email: true
        }
      })
    }

    if (!withdrawal) {
      return NextResponse.json(
        { error: '提现记录不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否已被删除（如果有status字段的话）
    if (withdrawal.user.status && withdrawal.user.status === 'DELETED') {
      return NextResponse.json(
        { error: '该用户已被删除' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      ...withdrawal,
      reviewer
    })

  } catch (error) {
    console.error('获取提现记录详情失败:', error)
    return NextResponse.json(
      { error: '获取提现记录详情失败' },
      { status: 500 }
    )
  }
}
