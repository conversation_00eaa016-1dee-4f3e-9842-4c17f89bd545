import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 审核充值申请
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { id: recordId } = await params
    const { action, notes } = await request.json() // action: 'approve' | 'reject'

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: '无效的操作类型' },
        { status: 400 }
      )
    }

    // 获取充值记录
    const depositRecord = await prisma.depositRecord.findUnique({
      where: { id: recordId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            depositBalance: true
          }
        }
      }
    })

    if (!depositRecord) {
      return NextResponse.json(
        { error: '充值记录不存在' },
        { status: 404 }
      )
    }

    if (!['PENDING', 'PENDING_APPROVAL'].includes(depositRecord.status)) {
      return NextResponse.json(
        { error: '该充值申请已处理' },
        { status: 400 }
      )
    }

    if (action === 'approve') {
      // 批准充值
      const result = await prisma.$transaction(async (tx) => {
        // 更新充值记录状态
        const updatedRecord = await tx.depositRecord.update({
          where: { id: recordId },
          data: {
            status: 'COMPLETED',
            metadata: {
              ...(depositRecord.metadata && typeof depositRecord.metadata === 'object' ? depositRecord.metadata : {}),
              approvedBy: admin.id,
              approvedAt: new Date().toISOString(),
              approveNotes: notes
            }
          }
        })

        // 更新用户余额
        const updatedUser = await tx.user.update({
          where: { id: depositRecord.userId },
          data: {
            depositBalance: {
              increment: depositRecord.amount
            }
          }
        })

        // 记录操作历史
        const operation = await tx.depositOperation.create({
          data: {
            userId: depositRecord.userId,
            operationType: 'DEPOSIT_APPROVE',
            amount: depositRecord.amount,
            balanceBefore: depositRecord.user.depositBalance,
            balanceAfter: depositRecord.user.depositBalance + depositRecord.amount,
            reason: '管理员批准充值申请',
            notes: notes || `批准充值申请 ${depositRecord.method} ${depositRecord.amount} USDT`,
            operatorId: admin.id,
            relatedId: recordId,
            relatedType: 'DEPOSIT'
          }
        })

        // 记录资金交易
        await tx.fundTransaction.create({
          data: {
            userId: depositRecord.userId,
            type: 'DEPOSIT',
            amount: depositRecord.amount,
            description: `充值申请批准 - ${depositRecord.method}`,
            relatedId: recordId,
            metadata: {
              depositRecordId: recordId,
              method: depositRecord.method,
              approvedBy: admin.id,
              approvedAt: new Date().toISOString(),
              notes
            }
          }
        })

        return { updatedRecord, updatedUser, operation }
      })

      return NextResponse.json({
        success: true,
        message: '充值申请已批准',
        record: result.updatedRecord,
        user: {
          id: result.updatedUser.id,
          name: result.updatedUser.name,
          email: result.updatedUser.email,
          depositBalance: result.updatedUser.depositBalance
        }
      })

    } else {
      // 拒绝充值
      const updatedRecord = await prisma.depositRecord.update({
        where: { id: recordId },
        data: {
          status: 'FAILED',
          metadata: {
            ...(depositRecord.metadata && typeof depositRecord.metadata === 'object' ? depositRecord.metadata : {}),
            rejectedBy: admin.id,
            rejectedAt: new Date().toISOString(),
            rejectNotes: notes
          }
        }
      })

      // 记录操作历史
      await prisma.depositOperation.create({
        data: {
          userId: depositRecord.userId,
          operationType: 'DEPOSIT_REJECT',
          amount: 0,
          balanceBefore: depositRecord.user.depositBalance,
          balanceAfter: depositRecord.user.depositBalance,
          reason: '管理员拒绝充值申请',
          notes: notes || `拒绝充值申请 ${depositRecord.method} ${depositRecord.amount} USDT`,
          operatorId: admin.id,
          relatedId: recordId,
          relatedType: 'DEPOSIT'
        }
      })

      return NextResponse.json({
        success: true,
        message: '充值申请已拒绝',
        record: updatedRecord
      })
    }

  } catch (error) {
    console.error('审核充值申请失败:', error)
    return NextResponse.json(
      { error: '审核失败' },
      { status: 500 }
    )
  }
}
