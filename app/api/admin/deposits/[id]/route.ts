import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个充值记录详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { id } = await params

    // 获取充值记录详情
    const depositRecord = await prisma.depositRecord.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            depositBalance: true,
            creditScore: true
          }
        }
      }
    })

    if (!depositRecord) {
      return NextResponse.json(
        { error: '充值记录不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否已被删除
    if (depositRecord.user.status === 'DELETED') {
      return NextResponse.json(
        { error: '该用户已被删除' },
        { status: 404 }
      )
    }

    return NextResponse.json(depositRecord)

  } catch (error) {
    console.error('获取充值记录详情失败:', error)
    return NextResponse.json(
      { error: '获取充值记录详情失败' },
      { status: 500 }
    )
  }
}
