import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户保证金管理数据
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    console.log('🔍 [deposits API] 检查管理员权限...')
    console.log('📋 [deposits API] 会话邮箱:', session.user.email)

    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    console.log('👤 [deposits API] 查找到的用户:', admin ? {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role
    } : null)

    if (!admin || admin.role !== 'ADMIN') {
      console.log('❌ [deposits API] 权限验证失败')
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    console.log('✅ [deposits API] 管理员权限验证通过')

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const type = searchParams.get('type') || 'users' // users, deposits, withdrawals, operations

    const skip = (page - 1) * limit

    if (type === 'users') {
      // 获取用户保证金信息（排除已删除的用户）
      const whereClause: any = {
        status: { not: 'DELETED' } // 只显示未删除的用户
      }

      if (search) {
        whereClause.OR = [
          { name: { contains: search } },
          { email: { contains: search } },
          { userId: { contains: search } }
        ]
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where: whereClause,
          select: {
            id: true,
            userId: true,
            name: true,
            email: true,
            depositBalance: true,
            creditScore: true,
            status: true,
            isGuarantor: true,
            createdAt: true,
            _count: {
              select: {
                depositRecords: true,
                fundTransactions: true
              }
            }
          },
          orderBy: { depositBalance: 'desc' },
          skip,
          take: limit
        }),
        prisma.user.count({ where: whereClause })
      ])

      return NextResponse.json({
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })

    } else if (type === 'deposits') {
      // 获取充值申请（排除已删除用户的记录）
      const whereClause: any = {
        user: {
          status: { not: 'DELETED' } // 只显示未删除用户的充值记录
        }
      }

      if (status) {
        whereClause.status = status
      }

      if (search) {
        whereClause.user = {
          ...whereClause.user,
          OR: [
            { name: { contains: search } },
            { email: { contains: search } }
          ]
        }
      }

      const [deposits, total] = await Promise.all([
        prisma.depositRecord.findMany({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                depositBalance: true,
                creditScore: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.depositRecord.count({ where: whereClause })
      ])

      return NextResponse.json({
        deposits,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })

    } else if (type === 'withdrawals') {
      // 获取提现申请（排除已删除用户的记录）
      const whereClause: any = {
        user: {
          status: { not: 'DELETED' } // 只显示未删除用户的提现记录
        }
      }

      if (status) {
        whereClause.status = status
      }

      if (search) {
        whereClause.user = {
          ...whereClause.user,
          OR: [
            { name: { contains: search } },
            { email: { contains: search } }
          ]
        }
      }

      const [withdrawals, total] = await Promise.all([
        prisma.withdrawal.findMany({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                depositBalance: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.withdrawal.count({ where: whereClause })
      ])

      return NextResponse.json({
        withdrawals,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })

    } else if (type === 'operations') {
      // 获取保证金操作历史（排除已删除用户的记录）
      const whereClause: any = {
        user: {
          status: { not: 'DELETED' } // 只显示未删除用户的操作记录
        }
      }

      if (search) {
        whereClause.user = {
          ...whereClause.user,
          OR: [
            { name: { contains: search } },
            { email: { contains: search } }
          ]
        }
      }

      const [operations, total] = await Promise.all([
        prisma.depositOperation.findMany({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            operator: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.depositOperation.count({ where: whereClause })
      ])

      return NextResponse.json({
        operations,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })
    }

    return NextResponse.json({ error: '无效的查询类型' }, { status: 400 })

  } catch (error) {
    console.error('获取保证金管理数据失败:', error)
    return NextResponse.json(
      { error: '获取数据失败' },
      { status: 500 }
    )
  }
}

// 手动调整用户保证金
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { userId, operationType, amount, reason, notes } = await request.json()

    if (!userId || !operationType || !amount || !reason) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    if (!['MANUAL_ADD', 'MANUAL_SUBTRACT'].includes(operationType)) {
      return NextResponse.json(
        { error: '无效的操作类型' },
        { status: 400 }
      )
    }

    // 获取用户当前余额
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true, email: true, depositBalance: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const balanceBefore = user.depositBalance
    const operationAmount = operationType === 'MANUAL_ADD' ? amount : -amount
    const balanceAfter = balanceBefore + operationAmount

    if (balanceAfter < 0) {
      return NextResponse.json(
        { error: '操作后余额不能为负数' },
        { status: 400 }
      )
    }

    // 执行事务
    const result = await prisma.$transaction(async (tx) => {
      // 更新用户余额
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: { depositBalance: balanceAfter }
      })

      // 记录操作历史
      const operation = await tx.depositOperation.create({
        data: {
          userId,
          operationType,
          amount: Math.abs(amount),
          balanceBefore,
          balanceAfter,
          reason,
          notes,
          operatorId: admin.id
        }
      })

      // 记录资金交易
      await tx.fundTransaction.create({
        data: {
          userId,
          type: operationType === 'MANUAL_ADD' ? 'DEPOSIT' : 'WITHDRAWAL',
          amount: operationAmount,
          description: `管理员${operationType === 'MANUAL_ADD' ? '增加' : '减少'}保证金: ${reason}`,
          relatedId: operation.id,
          metadata: {
            operationType,
            operatorId: admin.id,
            operatorName: admin.name,
            reason,
            notes
          }
        }
      })

      return { updatedUser, operation }
    })

    return NextResponse.json({
      success: true,
      message: '保证金调整成功',
      user: {
        id: result.updatedUser.id,
        name: result.updatedUser.name,
        email: result.updatedUser.email,
        depositBalance: result.updatedUser.depositBalance
      },
      operation: result.operation
    })

  } catch (error) {
    console.error('保证金调整失败:', error)
    return NextResponse.json(
      { error: '保证金调整失败' },
      { status: 500 }
    )
  }
}
