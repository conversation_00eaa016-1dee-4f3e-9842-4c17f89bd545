import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { batchCheckProductStock, createStockNotification } from '@/lib/inventory'
import { checkAdminPermission } from '@/lib/admin'

// 批量检查商品库存状态
export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    const adminCheck = await checkAdminPermission()
    if (!adminCheck.isAdmin) {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }

    console.log('开始批量检查商品库存状态...')
    
    // 批量检查并下架库存不足的商品
    const deactivatedProducts = await batchCheckProductStock()
    
    // 为每个下架的商品发送通知
    const notifications = await Promise.allSettled(
      deactivatedProducts.map(product => 
        createStockNotification(
          product.id,
          product.sellerId,
          `您的商品"${product.title}"库存已售完，已自动下架。如需继续销售，请补充库存后重新上架。`
        )
      )
    )

    const successfulNotifications = notifications.filter(n => n.status === 'fulfilled').length
    const failedNotifications = notifications.filter(n => n.status === 'rejected').length

    console.log(`库存检查完成: 下架了 ${deactivatedProducts.length} 个商品，发送了 ${successfulNotifications} 个通知`)

    return NextResponse.json({
      success: true,
      message: '库存检查完成',
      deactivatedCount: deactivatedProducts.length,
      deactivatedProducts: deactivatedProducts.map(p => ({
        id: p.id,
        title: p.title,
        stock: p.stock,
        sellerId: p.sellerId,
        sellerName: p.seller.name
      })),
      notifications: {
        successful: successfulNotifications,
        failed: failedNotifications
      }
    })

  } catch (error) {
    console.error('批量检查库存失败:', error)
    return NextResponse.json(
      { 
        error: '批量检查库存失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

// 获取库存统计信息
export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    const adminCheck = await checkAdminPermission()
    if (!adminCheck.isAdmin) {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }

    const { prisma } = await import('@/lib/prisma')

    // 获取库存统计
    const stats = await prisma.product.groupBy({
      by: ['status'],
      _count: {
        id: true
      },
      where: {
        isDemandGenerated: false // 排除需求生成的商品
      }
    })

    // 获取低库存商品（库存 <= 5）
    const lowStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lte: 5,
          gt: 0
        },
        status: 'AVAILABLE',
        isDemandGenerated: false
      },
      select: {
        id: true,
        title: true,
        stock: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        stock: 'asc'
      },
      take: 20
    })

    // 获取零库存但仍在售的商品
    const zeroStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lte: 0
        },
        status: 'AVAILABLE',
        isDemandGenerated: false
      },
      select: {
        id: true,
        title: true,
        stock: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      take: 10
    })

    return NextResponse.json({
      stats: stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      lowStockProducts,
      zeroStockProducts,
      alerts: {
        lowStock: lowStockProducts.length,
        zeroStock: zeroStockProducts.length
      }
    })

  } catch (error) {
    console.error('获取库存统计失败:', error)
    return NextResponse.json(
      { 
        error: '获取库存统计失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
