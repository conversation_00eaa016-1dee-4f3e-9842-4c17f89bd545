import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const paymentMethod = searchParams.get('paymentMethod') || ''

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { txHash: { contains: search, mode: 'insensitive' } },
        { order: { id: { contains: search, mode: 'insensitive' } } },
        { order: { buyer: { name: { contains: search, mode: 'insensitive' } } } },
        { order: { buyer: { email: { contains: search, mode: 'insensitive' } } } },
        { order: { seller: { name: { contains: search, mode: 'insensitive' } } } },
        { order: { seller: { email: { contains: search, mode: 'insensitive' } } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod
    }

    // 构建订单查询条件（支付相关）
    const orderWhere: any = {}

    if (search) {
      orderWhere.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { paymentTxHash: { contains: search, mode: 'insensitive' } },
        { buyer: { name: { contains: search, mode: 'insensitive' } } },
        { buyer: { email: { contains: search, mode: 'insensitive' } } },
        { seller: { name: { contains: search, mode: 'insensitive' } } },
        { seller: { email: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // 根据支付状态筛选
    if (status) {
      if (status === 'PENDING') {
        orderWhere.paymentConfirmed = false
        orderWhere.status = { in: ['PENDING_PAYMENT', 'PAID'] }
      } else if (status === 'CONFIRMED') {
        orderWhere.paymentConfirmed = true
      } else if (status === 'REJECTED') {
        orderWhere.status = 'CANCELLED'
      } else if (status === 'REFUNDED') {
        orderWhere.status = 'REFUND_REQUESTED'
      }
    }

    // 获取订单列表（作为支付记录）
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where: orderWhere,
        include: {
          product: {
            select: {
              id: true,
              title: true,
              price: true
            }
          },
          buyer: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          seller: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.order.count({ where: orderWhere })
    ])

    // 转换为支付记录格式
    const payments = orders.map(order => ({
      id: order.id,
      amount: order.totalAmount,
      paymentMethod: 'USDT_TRC20', // 默认支付方式
      status: order.paymentConfirmed ? 'CONFIRMED' :
              order.status === 'CANCELLED' ? 'REJECTED' :
              order.status === 'REFUND_REQUESTED' ? 'REFUNDED' : 'PENDING',
      txHash: order.paymentTxHash,
      refundAmount: null,
      refundTxHash: null,
      adminNotes: null,
      createdAt: order.createdAt,
      confirmedAt: order.paymentConfirmed ? order.updatedAt : null,
      refundedAt: order.status === 'REFUND_REQUESTED' ? order.updatedAt : null,
      order: order
    }))

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Admin payments API error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取支付列表失败' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 检查管理员权限
    const admin = await requireAdmin()

    if (!admin) {
      return NextResponse.json(
        { error: '管理员信息获取失败' },
        { status: 500 }
      )
    }

    const body = await request.json()
    const { paymentId, action, data } = body

    if (!paymentId || !action) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    let result: any
    
    switch (action) {
      case 'confirmPayment':
        result = await prisma.order.update({
          where: { id: paymentId },
          data: {
            paymentConfirmed: true,
            status: 'PAID'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'rejectPayment':
        result = await prisma.order.update({
          where: { id: paymentId },
          data: {
            paymentConfirmed: false,
            status: 'CANCELLED'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'processRefund':
        if (!data.refundAmount || data.refundAmount <= 0) {
          return NextResponse.json(
            { error: '退款金额必须大于0' },
            { status: 400 }
          )
        }

        result = await prisma.order.update({
          where: { id: paymentId },
          data: {
            status: 'REFUND_REQUESTED'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'addNotes':
        // 添加管理员备注
        const { notes, isPrivate = true } = body

        if (!notes || typeof notes !== 'string' || notes.trim().length === 0) {
          return NextResponse.json(
            { error: '备注内容不能为空' },
            { status: 400 }
          )
        }

        // 验证订单存在
        const orderExists = await prisma.order.findUnique({
          where: { id: paymentId },
          select: { id: true }
        })

        if (!orderExists) {
          return NextResponse.json(
            { error: '订单不存在' },
            { status: 404 }
          )
        }

        // 创建管理员备注
        const adminNote = await prisma.adminNote.create({
          data: {
            orderId: paymentId,
            adminId: admin.id,
            content: notes.trim(),
            isPrivate: Boolean(isPrivate)
          }
        })

        // 获取更新后的订单信息
        result = await prisma.order.findUnique({
          where: { id: paymentId },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } },
            adminNotes: {
              include: {
                admin: { select: { name: true, email: true } }
              },
              orderBy: { createdAt: 'desc' },
              take: 5
            }
          }
        })

        // 添加新创建的备注信息到结果中
        result.newNote = {
          id: adminNote.id,
          content: adminNote.content,
          isPrivate: adminNote.isPrivate,
          createdAt: adminNote.createdAt,
          admin: {
            name: admin.name,
            email: admin.email
          }
        }
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    // 根据操作类型返回不同的成功消息
    let message = '操作成功'
    switch (action) {
      case 'confirmPayment':
        message = '支付确认成功'
        break
      case 'rejectPayment':
        message = '支付拒绝成功'
        break
      case 'processRefund':
        message = '退款处理成功'
        break
      case 'addNotes':
        message = '备注添加成功'
        break
    }

    return NextResponse.json({
      success: true,
      message,
      order: result
    })

  } catch (error) {
    console.error('Admin payments update error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '更新支付失败' },
      { status: 500 }
    )
  }
}
