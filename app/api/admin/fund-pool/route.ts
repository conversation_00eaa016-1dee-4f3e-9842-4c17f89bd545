/**
 * 管理员资金池统计API
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  getFundPoolOverview, 
  getFundPoolTrends,
  generateDailyStats,
  calculateInterestRevenue 
} from '@/lib/fund-pool-stats'
import { 
  getSettlementHistory,
  calculateCostSavings,
  runDailySettlement,
  runWeeklySettlement 
} from '@/lib/batch-settlement'
import { batchUpdateCreditLevels, getCreditLevelStats } from '@/lib/credit-level'
import { prisma } from '@/lib/prisma'

// 获取资金池概览
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const days = parseInt(searchParams.get('days') || '30')

    switch (action) {
      case 'overview':
        const overview = await getFundPoolOverview()
        return NextResponse.json(overview)

      case 'trends':
        const trends = await getFundPoolTrends(days)
        return NextResponse.json({ trends })

      case 'settlements':
        const settlements = await getSettlementHistory(20)
        return NextResponse.json({ settlements })

      case 'credit-stats':
        const creditStats = await getCreditLevelStats()
        return NextResponse.json({ creditStats })

      case 'revenue-projection':
        const overview2 = await getFundPoolOverview()
        const revenueProjection = await calculateInterestRevenue(
          overview2.totalAvailable,
          0.05, // 5% 年化收益率
          1
        )
        return NextResponse.json({ revenueProjection })

      default:
        // 默认返回完整概览
        const [
          poolOverview,
          recentTrends,
          recentSettlements,
          levelStats,
          revenue
        ] = await Promise.all([
          getFundPoolOverview(),
          getFundPoolTrends(7), // 最近7天
          getSettlementHistory(10),
          getCreditLevelStats(),
          getFundPoolOverview().then(overview => 
            calculateInterestRevenue(overview.totalAvailable, 0.05, 1)
          )
        ])

        return NextResponse.json({
          overview: poolOverview,
          trends: recentTrends,
          settlements: recentSettlements,
          creditStats: levelStats,
          revenueProjection: revenue
        })
    }

  } catch (error) {
    console.error('获取资金池统计失败:', error)
    return NextResponse.json(
      { error: '获取资金池统计失败' },
      { status: 500 }
    )
  }
}

// 执行管理操作
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { action, params } = await request.json()

    switch (action) {
      case 'generate-daily-stats':
        const date = params?.date ? new Date(params.date) : new Date()
        await generateDailyStats(date)
        return NextResponse.json({
          success: true,
          message: `生成 ${date.toISOString().split('T')[0]} 的统计数据成功`
        })

      case 'run-daily-settlement':
        await runDailySettlement()
        return NextResponse.json({
          success: true,
          message: '日结算执行完成'
        })

      case 'run-weekly-settlement':
        await runWeeklySettlement()
        return NextResponse.json({
          success: true,
          message: '周结算执行完成'
        })

      case 'update-credit-levels':
        const limit = params?.limit || 100
        const result = await batchUpdateCreditLevels(limit)
        return NextResponse.json({
          success: true,
          message: '批量更新信用等级完成',
          result
        })

      case 'calculate-settlement-savings':
        const batchId = params?.batchId
        if (!batchId) {
          return NextResponse.json(
            { error: '缺少批次ID' },
            { status: 400 }
          )
        }
        const savings = await calculateCostSavings(batchId)
        return NextResponse.json({ savings })

      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('执行管理操作失败:', error)
    return NextResponse.json(
      { error: '执行管理操作失败' },
      { status: 500 }
    )
  }
}
