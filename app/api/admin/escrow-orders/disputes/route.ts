import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取争议列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const search = searchParams.get('search')
    const skip = (page - 1) * limit

    const where: any = {}

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    if (search) {
      where.OR = [
        {
          escrowOrder: {
            order: {
              orderNumber: { contains: search }
            }
          }
        },
        {
          reporter: {
            name: { contains: search }
          }
        },
        {
          reported: {
            name: { contains: search }
          }
        },
        {
          description: { contains: search }
        }
      ]
    }

    const [disputes, total] = await Promise.all([
      prisma.escrowDispute.findMany({
        where,
        include: {
          escrowOrder: {
            include: {
              order: {
                select: {
                  orderNumber: true,
                  totalAmount: true,
                  product: {
                    select: { title: true, images: true }
                  }
                }
              },
              buyer: {
                select: { id: true, name: true, email: true }
              },
              seller: {
                select: { id: true, name: true, email: true }
              },
              mediator: {
                select: { id: true, name: true, email: true }
              }
            }
          },
          reporter: {
            select: { id: true, name: true, email: true }
          },
          reported: {
            select: { id: true, name: true, email: true }
          },
          adminUser: {
            select: { id: true, name: true, email: true }
          },
          votes: {
            include: {
              voter: {
                select: { name: true, email: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.escrowDispute.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        disputes,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取争议列表失败:', error)
    return NextResponse.json(
      { error: '获取争议列表失败' },
      { status: 500 }
    )
  }
}

// 处理争议
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { disputeIds, action, data: actionData } = body

    if (!disputeIds || !Array.isArray(disputeIds) || !action) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    let result

    switch (action) {
      case 'assign':
        // 分配给管理员处理
        result = await prisma.escrowDispute.updateMany({
          where: {
            id: { in: disputeIds },
            status: 'PENDING'
          },
          data: {
            status: 'UNDER_REVIEW',
            adminAssigned: session.user.id
          }
        })
        break

      case 'escalate_to_voting':
        // 升级到仲裁投票
        const disputesToEscalate = await prisma.escrowDispute.findMany({
          where: {
            id: { in: disputeIds },
            status: 'UNDER_REVIEW'
          },
          include: {
            escrowOrder: {
              select: {
                id: true,
                mediatorId: true
              }
            }
          }
        })

        if (disputesToEscalate.length > 0) {
          await prisma.$transaction(async (tx) => {
            for (const dispute of disputesToEscalate) {
              // 更新争议状态
              await tx.escrowDispute.update({
                where: { id: dispute.id },
                data: {
                  status: 'VOTING',
                  adminAssigned: session.user.id
                }
              })

              // 更新托管订单状态
              await tx.escrowOrder.update({
                where: { id: dispute.escrowOrderId },
                data: { status: 'DISPUTED' }
              })

              // 通知所有活跃中间人
              const activeMediators = await tx.user.findMany({
                where: {
                  isMediator: true,
                  mediatorStatus: 'ACTIVE',
                  id: { not: dispute.escrowOrder?.mediatorId } // 排除涉事中间人
                },
                select: { id: true }
              })

              const notifications = activeMediators.map(mediator => ({
                userId: mediator.id,
                type: 'ARBITRATION' as const,
                title: '新的仲裁投票',
                message: `有新的争议案例需要您参与仲裁投票`,
                data: {
                  disputeId: dispute.id,
                  escrowOrderId: dispute.escrowOrderId
                },
                priority: 'HIGH' as const
              }))

              await tx.notification.createMany({
                data: notifications
              })
            }
          })

          result = { count: disputesToEscalate.length }
        } else {
          result = { count: 0 }
        }
        break

      case 'resolve_directly':
        // 直接解决争议
        const { resolution, decision } = actionData || {}
        if (!resolution || !decision) {
          return NextResponse.json(
            { error: '请提供解决方案和决定' },
            { status: 400 }
          )
        }

        const disputesToResolve = await prisma.escrowDispute.findMany({
          where: {
            id: { in: disputeIds },
            status: { in: ['PENDING', 'UNDER_REVIEW'] }
          },
          include: {
            escrowOrder: true
          }
        })

        if (disputesToResolve.length > 0) {
          await prisma.$transaction(async (tx) => {
            for (const dispute of disputesToResolve) {
              // 更新争议状态
              await tx.escrowDispute.update({
                where: { id: dispute.id },
                data: {
                  status: 'RESOLVED',
                  resolution,
                  resolvedAt: new Date(),
                  adminAssigned: session.user.id
                }
              })

              // 执行决定
              await executeAdminDecision(tx, dispute.escrowOrder, decision)
            }
          })

          result = { count: disputesToResolve.length }
        } else {
          result = { count: 0 }
        }
        break

      case 'close':
        // 关闭争议
        const { closeReason } = actionData || {}
        result = await prisma.escrowDispute.updateMany({
          where: {
            id: { in: disputeIds },
            status: { in: ['PENDING', 'UNDER_REVIEW', 'RESOLVED'] }
          },
          data: {
            status: 'CLOSED',
            resolution: closeReason || '管理员关闭',
            resolvedAt: new Date(),
            adminAssigned: session.user.id
          }
        })
        break

      case 'update_priority':
        // 更新优先级
        const { priority } = actionData || {}
        if (!priority || !['LOW', 'MEDIUM', 'HIGH', 'URGENT'].includes(priority)) {
          return NextResponse.json(
            { error: '无效的优先级' },
            { status: 400 }
          )
        }

        result = await prisma.escrowDispute.updateMany({
          where: {
            id: { in: disputeIds }
          },
          data: {
            priority,
            adminAssigned: session.user.id
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `操作完成，影响 ${result.count || 0} 个争议`,
      data: result
    })

  } catch (error) {
    console.error('处理争议失败:', error)
    return NextResponse.json(
      { error: '处理争议失败' },
      { status: 500 }
    )
  }
}

// 执行管理员决定
async function executeAdminDecision(tx: any, escrowOrder: any, decision: string) {
  switch (decision) {
    case 'BUYER_FAVOR':
      // 退款给买家
      await tx.user.update({
        where: { id: escrowOrder.buyerId },
        data: { depositBalance: { increment: escrowOrder.amount + escrowOrder.mediatorFee } }
      })

      // 释放冻结资金
      await tx.fundFreeze.updateMany({
        where: {
          userId: escrowOrder.buyerId,
          relatedId: escrowOrder.id,
          relatedType: 'ESCROW_ORDER',
          status: 'FROZEN'
        },
        data: { status: 'RELEASED' }
      })

      // 更新托管订单状态
      await tx.escrowOrder.update({
        where: { id: escrowOrder.id },
        data: { 
          status: 'CANCELLED',
          cancelledAt: new Date()
        }
      })

      // 更新订单状态
      await tx.order.update({
        where: { id: escrowOrder.orderId },
        data: { status: 'CANCELLED' }
      })
      break

    case 'SELLER_FAVOR':
      // 打款给卖家
      await tx.user.update({
        where: { id: escrowOrder.sellerId },
        data: { depositBalance: { increment: escrowOrder.amount } }
      })

      // 给中间人支付费用
      const mediatorFee = escrowOrder.mediatorFee - escrowOrder.platformFee
      await tx.user.update({
        where: { id: escrowOrder.mediatorId },
        data: { depositBalance: { increment: mediatorFee } }
      })

      // 释放冻结资金
      await tx.fundFreeze.updateMany({
        where: {
          userId: escrowOrder.buyerId,
          relatedId: escrowOrder.id,
          relatedType: 'ESCROW_ORDER',
          status: 'FROZEN'
        },
        data: { status: 'RELEASED' }
      })

      // 更新托管订单状态
      await tx.escrowOrder.update({
        where: { id: escrowOrder.id },
        data: { 
          status: 'COMPLETED',
          completedAt: new Date()
        }
      })

      // 更新订单状态
      await tx.order.update({
        where: { id: escrowOrder.orderId },
        data: { status: 'COMPLETED' }
      })
      break

    case 'SPLIT_DECISION':
      // 平分款项
      const halfAmount = escrowOrder.amount / 2
      await tx.user.update({
        where: { id: escrowOrder.buyerId },
        data: { depositBalance: { increment: halfAmount + escrowOrder.mediatorFee } }
      })
      await tx.user.update({
        where: { id: escrowOrder.sellerId },
        data: { depositBalance: { increment: halfAmount } }
      })

      // 释放冻结资金
      await tx.fundFreeze.updateMany({
        where: {
          userId: escrowOrder.buyerId,
          relatedId: escrowOrder.id,
          relatedType: 'ESCROW_ORDER',
          status: 'FROZEN'
        },
        data: { status: 'RELEASED' }
      })

      // 更新托管订单状态
      await tx.escrowOrder.update({
        where: { id: escrowOrder.id },
        data: { 
          status: 'COMPLETED',
          completedAt: new Date()
        }
      })

      // 更新订单状态
      await tx.order.update({
        where: { id: escrowOrder.orderId },
        data: { status: 'COMPLETED' }
      })
      break
  }
}
