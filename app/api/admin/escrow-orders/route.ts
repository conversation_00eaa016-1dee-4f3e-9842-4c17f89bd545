import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const skip = (page - 1) * limit

    const where: any = {}

    if (status) {
      where.status = status
    }

    if (search) {
      where.OR = [
        {
          order: {
            orderNumber: { contains: search }
          }
        },
        {
          order: {
            product: {
              title: { contains: search }
            }
          }
        },
        {
          buyer: {
            name: { contains: search }
          }
        },
        {
          seller: {
            name: { contains: search }
          }
        },
        {
          mediator: {
            name: { contains: search }
          }
        }
      ]
    }

    const [orders, total] = await Promise.all([
      prisma.escrowOrder.findMany({
        where,
        include: {
          order: {
            include: {
              product: {
                select: {
                  title: true,
                  images: true,
                  price: true
                }
              }
            }
          },
          buyer: {
            select: { id: true, name: true, email: true }
          },
          seller: {
            select: { id: true, name: true, email: true }
          },
          mediator: {
            select: { id: true, name: true, email: true }
          },
          disputes: {
            select: {
              id: true,
              status: true,
              reason: true,
              priority: true,
              createdAt: true
            }
          },
          blockchainTransactions: {
            select: {
              id: true,
              txHash: true,
              status: true,
              amount: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.escrowOrder.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取托管订单列表失败:', error)
    return NextResponse.json(
      { error: '获取托管订单列表失败' },
      { status: 500 }
    )
  }
}

// 管理员操作托管订单
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { orderIds, action, data: actionData } = body

    if (!orderIds || !Array.isArray(orderIds) || !action) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    let result

    switch (action) {
      case 'force_complete':
        // 强制完成托管订单
        const completableOrders = await prisma.escrowOrder.findMany({
          where: {
            id: { in: orderIds },
            status: { in: ['DELIVERED', 'DISPUTED'] }
          }
        })

        if (completableOrders.length > 0) {
          await prisma.$transaction(async (tx) => {
            for (const escrowOrder of completableOrders) {
              // 更新托管状态
              await tx.escrowOrder.update({
                where: { id: escrowOrder.id },
                data: {
                  status: 'COMPLETED',
                  completedAt: new Date()
                }
              })

              // 释放买家冻结资金
              await tx.fundFreeze.updateMany({
                where: {
                  userId: escrowOrder.buyerId,
                  relatedId: escrowOrder.id,
                  relatedType: 'ESCROW_ORDER',
                  status: 'FROZEN'
                },
                data: {
                  status: 'RELEASED'
                }
              })

              // 给卖家打款
              await tx.user.update({
                where: { id: escrowOrder.sellerId },
                data: {
                  depositBalance: { increment: escrowOrder.amount }
                }
              })

              // 给中间人支付费用
              const mediatorFee = escrowOrder.mediatorFee - escrowOrder.platformFee
              await tx.user.update({
                where: { id: escrowOrder.mediatorId },
                data: {
                  depositBalance: { increment: mediatorFee }
                }
              })

              // 更新订单状态
              await tx.order.update({
                where: { id: escrowOrder.orderId },
                data: { status: 'COMPLETED' }
              })

              // 关闭相关争议
              await tx.escrowDispute.updateMany({
                where: {
                  escrowOrderId: escrowOrder.id,
                  status: { in: ['PENDING', 'UNDER_REVIEW', 'VOTING'] }
                },
                data: {
                  status: 'CLOSED',
                  resolution: '管理员强制完成托管订单',
                  resolvedAt: new Date()
                }
              })
            }
          })

          result = { count: completableOrders.length }
        } else {
          result = { count: 0 }
        }
        break

      case 'force_refund':
        // 强制退款
        const refundableOrders = await prisma.escrowOrder.findMany({
          where: {
            id: { in: orderIds },
            status: { in: ['PENDING', 'FUNDED', 'DISPUTED'] }
          }
        })

        if (refundableOrders.length > 0) {
          await prisma.$transaction(async (tx) => {
            for (const escrowOrder of refundableOrders) {
              // 更新托管状态
              await tx.escrowOrder.update({
                where: { id: escrowOrder.id },
                data: {
                  status: 'CANCELLED',
                  cancelledAt: new Date()
                }
              })

              // 释放买家冻结资金并退款
              await tx.fundFreeze.updateMany({
                where: {
                  userId: escrowOrder.buyerId,
                  relatedId: escrowOrder.id,
                  relatedType: 'ESCROW_ORDER',
                  status: 'FROZEN'
                },
                data: {
                  status: 'RELEASED'
                }
              })

              // 退款给买家
              const refundAmount = escrowOrder.amount + escrowOrder.mediatorFee
              await tx.user.update({
                where: { id: escrowOrder.buyerId },
                data: {
                  depositBalance: { increment: refundAmount }
                }
              })

              // 更新订单状态
              await tx.order.update({
                where: { id: escrowOrder.orderId },
                data: { status: 'CANCELLED' }
              })

              // 关闭相关争议
              await tx.escrowDispute.updateMany({
                where: {
                  escrowOrderId: escrowOrder.id,
                  status: { in: ['PENDING', 'UNDER_REVIEW', 'VOTING'] }
                },
                data: {
                  status: 'CLOSED',
                  resolution: '管理员强制退款',
                  resolvedAt: new Date()
                }
              })
            }
          })

          result = { count: refundableOrders.length }
        } else {
          result = { count: 0 }
        }
        break

      case 'assign_mediator':
        // 重新分配中间人
        const { newMediatorId } = actionData || {}
        if (!newMediatorId) {
          return NextResponse.json(
            { error: '请指定新的中间人' },
            { status: 400 }
          )
        }

        // 验证新中间人
        const newMediator = await prisma.user.findUnique({
          where: { id: newMediatorId },
          select: { isMediator: true, mediatorStatus: true }
        })

        if (!newMediator || !newMediator.isMediator || newMediator.mediatorStatus !== 'ACTIVE') {
          return NextResponse.json(
            { error: '指定的用户不是活跃中间人' },
            { status: 400 }
          )
        }

        result = await prisma.escrowOrder.updateMany({
          where: {
            id: { in: orderIds },
            status: { in: ['PENDING', 'FUNDED'] }
          },
          data: {
            mediatorId: newMediatorId
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `操作完成，影响 ${result.count || 0} 个订单`,
      data: result
    })

  } catch (error) {
    console.error('管理员操作失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}
