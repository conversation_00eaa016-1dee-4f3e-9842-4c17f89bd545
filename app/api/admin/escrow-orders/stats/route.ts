import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管系统统计数据
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    // 基础统计
    const [
      totalOrders,
      totalAmount,
      totalFees,
      activeOrders,
      completedOrders,
      disputedOrders,
      activeMediators
    ] = await Promise.all([
      // 总托管订单数
      prisma.escrowOrder.count(),
      
      // 总托管金额
      prisma.escrowOrder.aggregate({
        _sum: { amount: true }
      }),
      
      // 总手续费
      prisma.escrowOrder.aggregate({
        _sum: { mediatorFee: true }
      }),
      
      // 活跃订单数
      prisma.escrowOrder.count({
        where: {
          status: { in: ['PENDING', 'FUNDED', 'SHIPPED', 'DELIVERED'] }
        }
      }),
      
      // 已完成订单数
      prisma.escrowOrder.count({
        where: { status: 'COMPLETED' }
      }),
      
      // 争议订单数
      prisma.escrowOrder.count({
        where: { status: 'DISPUTED' }
      }),
      
      // 活跃中间人数
      prisma.user.count({
        where: {
          isMediator: true,
          mediatorStatus: 'ACTIVE'
        }
      })
    ])

    // 状态分布统计
    const statusStats = await prisma.escrowOrder.groupBy({
      by: ['status'],
      _count: {
        status: true
      },
      _sum: {
        amount: true
      }
    })

    // 月度趋势统计（最近12个月）
    const monthlyStats = await prisma.$queryRaw`
      SELECT 
        DATE_FORMAT(createdAt, '%Y-%m') as month,
        COUNT(*) as orderCount,
        SUM(amount) as totalAmount,
        SUM(mediatorFee) as totalFees
      FROM EscrowOrder 
      WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
      ORDER BY month ASC
    `

    // 中间人表现统计
    const mediatorStats = await prisma.escrowOrder.groupBy({
      by: ['mediatorId'],
      _count: {
        mediatorId: true
      },
      _sum: {
        amount: true,
        mediatorFee: true
      },
      where: {
        status: 'COMPLETED'
      },
      orderBy: {
        _count: {
          mediatorId: 'desc'
        }
      },
      take: 10
    })

    // 获取中间人详细信息
    const topMediators = await Promise.all(
      mediatorStats.map(async (stat) => {
        const mediator = await prisma.user.findUnique({
          where: { id: stat.mediatorId },
          select: {
            id: true,
            name: true,
            email: true,
            mediatorReputation: true,
            mediatorSuccessRate: true
          }
        })
        
        return {
          ...mediator,
          orderCount: stat._count.mediatorId,
          totalAmount: stat._sum.amount || 0,
          totalFees: stat._sum.mediatorFee || 0
        }
      })
    )

    // 争议统计
    const disputeStats = await prisma.escrowDispute.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    const disputePriorityStats = await prisma.escrowDispute.groupBy({
      by: ['priority'],
      _count: {
        priority: true
      }
    })

    // 平台收入统计
    const platformRevenue = (totalFees._sum.mediatorFee || 0) * 0.3

    // 今日统计
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todayStats = await Promise.all([
      prisma.escrowOrder.count({
        where: {
          createdAt: { gte: today }
        }
      }),
      prisma.escrowOrder.aggregate({
        where: {
          createdAt: { gte: today }
        },
        _sum: { amount: true }
      }),
      prisma.escrowOrder.count({
        where: {
          completedAt: { gte: today }
        }
      })
    ])

    return NextResponse.json({
      success: true,
      data: {
        // 基础统计
        totalOrders,
        totalAmount: totalAmount._sum.amount || 0,
        totalFees: totalFees._sum.mediatorFee || 0,
        activeOrders,
        completedOrders,
        disputedOrders,
        activeMediators,
        platformRevenue,
        
        // 今日统计
        todayStats: {
          newOrders: todayStats[0],
          newAmount: todayStats[1]._sum.amount || 0,
          completedOrders: todayStats[2]
        },
        
        // 状态分布
        statusDistribution: statusStats.reduce((acc, item) => {
          acc[item.status] = {
            count: item._count.status,
            amount: item._sum.amount || 0
          }
          return acc
        }, {} as Record<string, { count: number; amount: number }>),
        
        // 月度趋势
        monthlyTrend: monthlyStats,
        
        // 顶级中间人
        topMediators,
        
        // 争议统计
        disputeStats: {
          byStatus: disputeStats.reduce((acc, item) => {
            acc[item.status] = item._count.status
            return acc
          }, {} as Record<string, number>),
          byPriority: disputePriorityStats.reduce((acc, item) => {
            acc[item.priority] = item._count.priority
            return acc
          }, {} as Record<string, number>)
        }
      }
    })

  } catch (error) {
    console.error('获取托管统计数据失败:', error)
    return NextResponse.json(
      { error: '获取统计数据失败' },
      { status: 500 }
    )
  }
}
