import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/admin'

// 获取所有用户反馈列表（管理员）
export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}

    if (category) {
      where.category = category
    }

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } },
        { user: { name: { contains: search } } },
        { user: { email: { contains: search } } }
      ]
    }

    // 获取反馈列表
    const [feedbacks, total] = await Promise.all([
      prisma.userFeedback.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              userId: true
            }
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.userFeedback.count({ where })
    ])

    return NextResponse.json({
      success: true,
      feedbacks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('获取反馈列表失败:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取反馈列表失败' },
      { status: 500 }
    )
  }
}

// 批量操作反馈
export async function PATCH(request: NextRequest) {
  try {
    const adminUser = await requireAdmin()
    const body = await request.json()
    const { feedbackIds, action, data } = body

    if (!feedbackIds || !Array.isArray(feedbackIds) || feedbackIds.length === 0) {
      return NextResponse.json(
        { error: '请选择要操作的反馈' },
        { status: 400 }
      )
    }

    if (!action) {
      return NextResponse.json(
        { error: '请指定操作类型' },
        { status: 400 }
      )
    }

    let updateData: any = {}

    switch (action) {
      case 'updateStatus':
        if (!data.status) {
          return NextResponse.json(
            { error: '请指定状态' },
            { status: 400 }
          )
        }
        updateData.status = data.status
        if (data.status === 'RESOLVED') {
          updateData.resolvedAt = new Date()
        }
        break

      case 'updatePriority':
        if (!data.priority) {
          return NextResponse.json(
            { error: '请指定优先级' },
            { status: 400 }
          )
        }
        updateData.priority = data.priority
        break

      case 'assign':
        updateData.assignedToId = data.assignedToId || adminUser?.id
        break

      case 'unassign':
        updateData.assignedToId = null
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作类型' },
          { status: 400 }
        )
    }

    // 批量更新
    const result = await prisma.userFeedback.updateMany({
      where: {
        id: {
          in: feedbackIds
        }
      },
      data: updateData
    })

    return NextResponse.json({
      success: true,
      message: `成功更新 ${result.count} 条反馈`,
      updatedCount: result.count
    })

  } catch (error) {
    console.error('批量操作反馈失败:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '批量操作失败' },
      { status: 500 }
    )
  }
}
