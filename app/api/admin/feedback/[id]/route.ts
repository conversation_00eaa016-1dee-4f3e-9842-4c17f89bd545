import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/admin'

// 获取单个反馈详情（管理员）
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = params

    const feedback = await prisma.userFeedback.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            userId: true,
            creditScore: true,
            status: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!feedback) {
      return NextResponse.json(
        { error: '反馈不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      feedback
    })

  } catch (error) {
    console.error('获取反馈详情失败:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取反馈详情失败' },
      { status: 500 }
    )
  }
}

// 更新反馈（管理员）
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await requireAdmin()
    const { id } = params
    const body = await request.json()
    const { status, priority, adminResponse, assignedToId } = body

    // 检查反馈是否存在
    const existingFeedback = await prisma.userFeedback.findUnique({
      where: { id }
    })

    if (!existingFeedback) {
      return NextResponse.json(
        { error: '反馈不存在' },
        { status: 404 }
      )
    }

    // 构建更新数据
    const updateData: any = {}

    if (status) {
      updateData.status = status
      if (status === 'RESOLVED' && existingFeedback.status !== 'RESOLVED') {
        updateData.resolvedAt = new Date()
      }
    }

    if (priority) {
      updateData.priority = priority
    }

    if (adminResponse !== undefined) {
      updateData.adminResponse = adminResponse
    }

    if (assignedToId !== undefined) {
      updateData.assignedToId = assignedToId
    }

    // 更新反馈
    const updatedFeedback = await prisma.userFeedback.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            userId: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '反馈更新成功',
      feedback: updatedFeedback
    })

  } catch (error) {
    console.error('更新反馈失败:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '更新反馈失败' },
      { status: 500 }
    )
  }
}

// 删除反馈（管理员）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = params

    // 检查反馈是否存在
    const existingFeedback = await prisma.userFeedback.findUnique({
      where: { id }
    })

    if (!existingFeedback) {
      return NextResponse.json(
        { error: '反馈不存在' },
        { status: 404 }
      )
    }

    // 删除反馈
    await prisma.userFeedback.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      message: '反馈删除成功'
    })

  } catch (error) {
    console.error('删除反馈失败:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '删除反馈失败' },
      { status: 500 }
    )
  }
}
