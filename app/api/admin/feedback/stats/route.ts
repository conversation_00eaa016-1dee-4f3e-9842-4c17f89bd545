import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/admin'

// 获取反馈统计数据
export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    // 获取基础统计
    const [
      totalFeedbacks,
      pendingFeedbacks,
      inProgressFeedbacks,
      resolvedFeedbacks,
      categoryStats,
      priorityStats,
      recentFeedbacks,
      monthlyStats
    ] = await Promise.all([
      // 总反馈数
      prisma.userFeedback.count(),
      
      // 待处理反馈数
      prisma.userFeedback.count({
        where: { status: 'PENDING' }
      }),
      
      // 处理中反馈数
      prisma.userFeedback.count({
        where: { status: 'IN_PROGRESS' }
      }),
      
      // 已解决反馈数
      prisma.userFeedback.count({
        where: { status: 'RESOLVED' }
      }),
      
      // 分类统计
      prisma.userFeedback.groupBy({
        by: ['category'],
        _count: {
          category: true
        }
      }),
      
      // 优先级统计
      prisma.userFeedback.groupBy({
        by: ['priority'],
        _count: {
          priority: true
        }
      }),
      
      // 最近7天的反馈
      prisma.userFeedback.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // 月度统计（最近12个月）
      prisma.$queryRaw`
        SELECT 
          DATE_FORMAT(createdAt, '%Y-%m') as month,
          COUNT(*) as count,
          SUM(CASE WHEN status = 'RESOLVED' THEN 1 ELSE 0 END) as resolved
        FROM UserFeedback 
        WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        ORDER BY month ASC
      `
    ])

    // 获取平均响应时间
    const avgResponseTime = await prisma.$queryRaw`
      SELECT 
        AVG(TIMESTAMPDIFF(HOUR, createdAt, updatedAt)) as avgHours
      FROM UserFeedback 
      WHERE status = 'RESOLVED' 
        AND updatedAt > createdAt
        AND createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ` as any[]

    // 获取管理员处理统计
    const adminStats = await prisma.userFeedback.groupBy({
      by: ['assignedToId'],
      _count: {
        assignedToId: true
      },
      where: {
        assignedToId: {
          not: null
        }
      }
    })

    // 获取管理员信息
    const adminIds = adminStats.map(stat => stat.assignedToId).filter(Boolean)
    const admins = await prisma.user.findMany({
      where: {
        id: {
          in: adminIds as string[]
        }
      },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    // 组合管理员统计数据
    const adminStatsWithInfo = adminStats.map(stat => {
      const admin = admins.find(a => a.id === stat.assignedToId)
      return {
        adminId: stat.assignedToId,
        adminName: admin?.name || '未知管理员',
        adminEmail: admin?.email || '',
        assignedCount: stat._count.assignedToId
      }
    })

    // 计算分类分布
    const categoryDistribution = categoryStats.reduce((acc, item) => {
      const categoryNames: Record<string, string> = {
        'BUG_REPORT': '错误报告',
        'IMPROVEMENT': '改进建议',
        'APPEAL': '申诉'
      }
      acc[categoryNames[item.category] || item.category] = item._count.category
      return acc
    }, {} as Record<string, number>)

    // 计算优先级分布
    const priorityDistribution = priorityStats.reduce((acc, item) => {
      const priorityNames: Record<string, string> = {
        'LOW': '低',
        'MEDIUM': '中',
        'HIGH': '高',
        'URGENT': '紧急'
      }
      acc[priorityNames[item.priority] || item.priority] = item._count.priority
      return acc
    }, {} as Record<string, number>)

    // 计算解决率
    const resolutionRate = totalFeedbacks > 0 ? (resolvedFeedbacks / totalFeedbacks) * 100 : 0

    const stats = {
      // 基础统计
      total: totalFeedbacks,
      pending: pendingFeedbacks,
      inProgress: inProgressFeedbacks,
      resolved: resolvedFeedbacks,
      recent: recentFeedbacks,
      
      // 计算指标
      resolutionRate: Math.round(resolutionRate * 100) / 100,
      avgResponseTime: avgResponseTime[0]?.avgHours || 0,
      
      // 分布统计
      categoryDistribution,
      priorityDistribution,
      
      // 状态分布
      statusDistribution: {
        '待处理': pendingFeedbacks,
        '处理中': inProgressFeedbacks,
        '已解决': resolvedFeedbacks
      },
      
      // 管理员统计
      adminStats: adminStatsWithInfo,
      
      // 月度趋势
      monthlyTrend: monthlyStats,
      
      // 今日统计
      today: {
        new: await prisma.userFeedback.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        resolved: await prisma.userFeedback.count({
          where: {
            status: 'RESOLVED',
            resolvedAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        })
      }
    }

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('获取反馈统计失败:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取统计数据失败' },
      { status: 500 }
    )
  }
}
