import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'
import { UserStatus } from '@/lib/user-status'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''
    const status = searchParams.get('status') || ''
    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { userId: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (role) {
      where.role = role
    }

    // 获取用户列表
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          userId: true,
          name: true,
          email: true,
          role: true,
          status: true,
          creditScore: true,
          creditHistory: true,
          // 中间人相关字段
          isMediator: true,
          mediatorStatus: true,
          emailVerified: true,
          bannedAt: true,
          bannedUntil: true,
          banReason: true,
          bannedBy: true,
          riskFlags: true,
          riskLevel: true,
          flaggedAt: true,
          flaggedBy: true,
          flagNotes: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              products: true,
              ordersAsBuyer: true,
              ordersAsSeller: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.user.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Admin users API error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取用户列表失败' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    // 获取当前session用于记录操作者
    const session = await getServerSession(authOptions)
    const adminId = session?.user?.id

    const body = await request.json()
    const { userId, action, data } = body

    if (!userId || !action) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    let result
    
    switch (action) {
      case 'updateRole':
        if (!data.role || !['USER', 'ADMIN'].includes(data.role)) {
          return NextResponse.json(
            { error: '无效的角色' },
            { status: 400 }
          )
        }
        
        result = await prisma.user.update({
          where: { id: userId },
          data: { role: data.role },
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        })
        break

      case 'updateMediator':
        result = await prisma.user.update({
          where: { id: userId },
          data: {
            isMediator: data.isMediator,
            mediatorStatus: data.mediatorStatus,
            // 如果设为中间人，设置一些默认值
            ...(data.isMediator && {
              mediatorFeeRate: 0.025, // 2.5%
              mediatorReputation: 0,
              mediatorSuccessRate: 0,
              mediatorTotalOrders: 0,
              mediatorVerifiedAt: new Date()
            })
          },
          select: {
            id: true,
            name: true,
            email: true,
            isMediator: true,
            mediatorStatus: true
          }
        })
        break

      case 'updateCreditScore':
        if (typeof data.creditScore !== 'number' || data.creditScore < 0 || data.creditScore > 1000) {
          return NextResponse.json(
            { error: '信用分数必须在0-1000之间' },
            { status: 400 }
          )
        }
        
        result = await prisma.user.update({
          where: { id: userId },
          data: { creditScore: data.creditScore },
          select: {
            id: true,
            name: true,
            email: true,
            creditScore: true
          }
        })
        break

      case 'banUser':
        // 封禁用户
        const { banReason, banDuration } = data
        if (!banReason) {
          return NextResponse.json(
            { error: '封禁原因不能为空' },
            { status: 400 }
          )
        }

        let bannedUntil: Date | null = null
        if (banDuration && banDuration > 0) {
          bannedUntil = new Date(Date.now() + banDuration * 24 * 60 * 60 * 1000) // 转换为毫秒
        }

        result = await prisma.user.update({
          where: { id: userId },
          data: {
            status: UserStatus.BANNED,
            bannedAt: new Date(),
            bannedUntil,
            banReason,
            bannedBy: adminId
          },
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            bannedAt: true,
            bannedUntil: true,
            banReason: true
          }
        })
        break

      case 'unbanUser':
        // 解封用户
        result = await prisma.user.update({
          where: { id: userId },
          data: {
            status: UserStatus.ACTIVE,
            bannedAt: null,
            bannedUntil: null,
            banReason: null,
            bannedBy: null
          },
          select: {
            id: true,
            name: true,
            email: true,
            status: true
          }
        })
        break

      case 'deleteUser':
        // 永久删除用户 - 真正从数据库中移除用户记录
        // 1. 首先检查用户是否有未完成的订单
        const activeOrders = await prisma.order.count({
          where: {
            OR: [
              { buyerId: userId },
              { sellerId: userId }
            ],
            status: {
              in: ['PENDING_PAYMENT', 'PAID', 'SHIPPED']
            }
          }
        })

        if (activeOrders > 0) {
          return NextResponse.json(
            { error: '用户有未完成的订单，无法删除。请先处理完所有订单。' },
            { status: 400 }
          )
        }

        // 2. 获取用户信息
        const userToDelete = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            name: true,
            email: true,
            userId: true
          }
        })

        if (!userToDelete) {
          return NextResponse.json(
            { error: '用户不存在' },
            { status: 404 }
          )
        }

        // 3. 使用事务进行永久删除操作
        result = await prisma.$transaction(async (tx) => {
          // 3.1 首先处理所有外键约束，删除或更新引用此用户的记录

          // 处理没有级联删除的外键约束 - 将引用设为null或删除记录

          // 删除用户发布的产品（sellerId是必需字段，不能设为null）
          await tx.product.deleteMany({
            where: { sellerId: userId }
          })

          // 删除用户的订单（buyerId和sellerId是必需字段）
          await tx.order.deleteMany({
            where: {
              OR: [
                { buyerId: userId },
                { sellerId: userId }
              ]
            }
          })

          // 删除管理员笔记（adminId是必需字段，不能设为null）
          await tx.adminNote.deleteMany({
            where: { adminId: userId }
          })

          // 更新订单日志的操作员引用
          await tx.orderLog.updateMany({
            where: { operatorId: userId },
            data: { operatorId: null }
          })

          // 删除用户的评价记录
          await tx.review.deleteMany({
            where: {
              OR: [
                { reviewerId: userId },
                { revieweeId: userId }
              ]
            }
          })

          // 删除用户的消息记录
          await tx.message.deleteMany({
            where: {
              OR: [
                { senderId: userId },
                { receiverId: userId }
              ]
            }
          })

          // 删除用户的需求和需求响应
          await tx.demand.deleteMany({
            where: { userId: userId }
          })

          await tx.demandOffer.deleteMany({
            where: { sellerId: userId }
          })

          // 删除资金冻结记录和结算项目
          await tx.fundFreeze.deleteMany({
            where: { userId: userId }
          })

          await tx.settlementItem.deleteMany({
            where: { userId: userId }
          })

          // 更新用户反馈的分配引用
          await tx.userFeedback.updateMany({
            where: { assignedToId: userId },
            data: { assignedToId: null }
          })

          // 删除用户作为作者的公告（authorId是必需字段）
          await tx.announcement.deleteMany({
            where: { authorId: userId }
          })

          // 更新公告的审核者引用（reviewerId是可选字段）
          await tx.announcement.updateMany({
            where: { reviewerId: userId },
            data: { reviewerId: null }
          })

          // 删除用户作为作者的帮助文章（authorId是必需字段）
          await tx.helpArticle.deleteMany({
            where: { authorId: userId }
          })

          // 更新帮助文章的审核者引用（reviewerId是可选字段）
          await tx.helpArticle.updateMany({
            where: { reviewerId: userId },
            data: { reviewerId: null }
          })

          // 更新用户上传的帮助媒体文件的上传者引用（使用原始SQL，因为updateMany不支持外键更新）
          await tx.$executeRaw`UPDATE HelpMediaFile SET uploaderId = NULL WHERE uploaderId = ${userId}`

          // 更新担保人申请的审核者引用
          await tx.guarantorApplication.updateMany({
            where: { reviewedBy: userId },
            data: { reviewedBy: null }
          })

          // 删除该用户作为操作员的存款操作记录
          await tx.depositOperation.deleteMany({
            where: { operatorId: userId }
          })

          // 删除用户作为举报者的仲裁案例（reporterId是必需字段）
          await tx.arbitrationCase.deleteMany({
            where: { reporterId: userId }
          })

          // 删除用户的仲裁投票（voterId是必需字段）
          await tx.arbitrationVote.deleteMany({
            where: { voterId: userId }
          })

          // 更新奖励券的创建者和用户引用
          await tx.rewardCoupon.updateMany({
            where: { createdById: userId },
            data: { createdById: null }
          })

          await tx.rewardCoupon.updateMany({
            where: { userId: userId },
            data: { userId: null }
          })

          // 更新身份验证的验证者引用
          await tx.mediatorVerification.updateMany({
            where: { verifiedBy: userId },
            data: { verifiedBy: null }
          })

          // 更新争议报告的分配引用
          await tx.disputeReport.updateMany({
            where: { assignedToId: userId },
            data: { assignedToId: null }
          })

          // 删除该用户创建的礼品卡产品（createdById是必需字段）
          await tx.giftCardProduct.deleteMany({
            where: { createdById: userId }
          })

          await tx.giftCardProduct.updateMany({
            where: { updatedById: userId },
            data: { updatedById: null }
          })

          // 删除用户的礼品卡订单（userId是必需字段）
          await tx.giftCardOrder.deleteMany({
            where: { userId: userId }
          })

          // 更新礼品卡的各种用户引用
          await tx.giftCard.updateMany({
            where: { assignedById: userId },
            data: { assignedById: null }
          })

          await tx.giftCard.updateMany({
            where: { assignedToId: userId },
            data: { assignedToId: null }
          })

          await tx.giftCard.updateMany({
            where: { redeemedById: userId },
            data: { redeemedById: null }
          })

          await tx.giftCard.updateMany({
            where: { soldToId: userId },
            data: { soldToId: null }
          })

          // 更新礼品卡交易的用户引用
          await tx.giftCardTransaction.updateMany({
            where: { userId: userId },
            data: { userId: null }
          })

          // 更新兑换码的目标用户引用
          await tx.redemptionCode.updateMany({
            where: { targetUserId: userId },
            data: { targetUserId: null }
          })

          // 更新系统设置的创建者和更新者引用
          await tx.systemSetting.updateMany({
            where: { createdBy: userId },
            data: { createdBy: null }
          })

          await tx.systemSetting.updateMany({
            where: { updatedBy: userId },
            data: { updatedBy: null }
          })

          // 删除该用户任命的中间人委员会成员（appointedBy是必需字段）
          await tx.mediatorCommittee.deleteMany({
            where: { appointedBy: userId }
          })

          // 更新系统配置的更新者引用
          await tx.systemConfig.updateMany({
            where: { updatedBy: userId },
            data: { updatedBy: null }
          })

          // 更新托管争议的管理员分配引用
          await tx.escrowDispute.updateMany({
            where: { adminAssigned: userId },
            data: { adminAssigned: null }
          })

          // 更新中间人申请的审核者引用
          await tx.mediatorApplication.updateMany({
            where: { reviewedBy: userId },
            data: { reviewedBy: null }
          })

          // 更新提现券的使用者和发行者引用
          await tx.withdrawalVoucher.updateMany({
            where: { usedBy: userId },
            data: { usedBy: null }
          })

          // 删除该用户发行的提现凭证（issuedBy是必需字段）
          await tx.withdrawalVoucher.deleteMany({
            where: { issuedBy: userId }
          })

          // 现在删除有级联删除的记录

          // 删除中间人委员会记录（删除作为中间人的记录，已处理appointedBy外键）
          await tx.mediatorCommittee.deleteMany({
            where: { mediatorId: userId }
          })

          // 删除中间人奖励记录
          await tx.mediatorReward.deleteMany({
            where: { mediatorId: userId }
          })

          // 删除托管订单（作为中间人、买家、卖家的记录）
          await tx.escrowOrder.deleteMany({
            where: {
              OR: [
                { mediatorId: userId },
                { buyerId: userId },
                { sellerId: userId }
              ]
            }
          })



          // 删除仲裁投票
          await tx.arbitrationVote.deleteMany({
            where: { voterId: userId }
          })

          // 删除所有可能引用此用户的记录（确保没有遗漏）
          // 删除用户地址
          await tx.address.deleteMany({
            where: { userId: userId }
          })

          // 删除用户收藏
          await tx.favorite.deleteMany({
            where: { userId: userId }
          })

          // 删除用户会话
          await tx.userSession.deleteMany({
            where: { userId: userId }
          })

          // 删除用户反馈
          await tx.userFeedback.deleteMany({
            where: { userId: userId }
          })

          // 删除安全日志
          await tx.securityLog.deleteMany({
            where: { userId: userId }
          })

          // 删除信用历史记录
          await tx.creditHistory.deleteMany({
            where: { userId: userId }
          })

          // 删除通知
          await tx.notification.deleteMany({
            where: { userId: userId }
          })

          // 删除资金冻结记录
          await tx.fundFreeze.deleteMany({
            where: { userId: userId }
          })

          // 删除资金交易记录
          await tx.fundTransaction.deleteMany({
            where: { userId: userId }
          })

          // 删除提现券（通过usedBy字段）
          await tx.withdrawalVoucher.deleteMany({
            where: { usedBy: userId }
          })

          // 删除中间人申请
          await tx.mediatorApplication.deleteMany({
            where: { userId: userId }
          })

          // 删除托管争议（作为举报人或被举报人）
          await tx.escrowDispute.deleteMany({
            where: {
              OR: [
                { reporterId: userId },
                { reportedId: userId }
              ]
            }
          })

          // 删除争议报告
          await tx.disputeReport.deleteMany({
            where: { reporterId: userId }
          })

          // 删除存款记录
          await tx.depositRecord.deleteMany({
            where: { userId: userId }
          })

          // 删除存款操作记录
          await tx.depositOperation.deleteMany({
            where: { userId: userId }
          })

          // 删除担保人申请
          await tx.guarantorApplication.deleteMany({
            where: { userId: userId }
          })

          // 删除身份验证记录
          await tx.mediatorVerification.deleteMany({
            where: { userId: userId }
          })

          // 删除兑换交易记录
          await tx.redemptionTransaction.deleteMany({
            where: { userId: userId }
          })

          // 删除兑换码记录
          await tx.redemptionCode.deleteMany({
            where: { createdById: userId }
          })

          // 删除中间人验证记录
          await tx.mediatorVerification.deleteMany({
            where: { userId: userId }
          })

          // 删除提现记录
          await tx.withdrawal.deleteMany({
            where: { userId: userId }
          })

          // 现在删除有级联删除的记录（这些表有 onDelete: Cascade）
          // 这些记录会自动级联删除，但为了确保完整性，我们显式删除

          // 这些记录会因为外键约束自动级联删除，但我们显式删除以确保完整性
          // 注意：由于我们已经将外键设为null，现在可以安全删除用户

          // 3.2 最后删除用户记录
          const deletedUser = await tx.user.delete({
            where: { id: userId },
            select: {
              id: true,
              name: true,
              email: true
            }
          })

          return {
            id: deletedUser.id,
            name: deletedUser.name,
            email: deletedUser.email,
            message: '用户已永久删除，邮箱和用户名现在可以重新注册使用'
          }
        })
        break

      case 'flagUser':
        // 标记用户为风险用户
        const { flagType, reason, riskLevel } = data
        if (!flagType || !reason || !riskLevel) {
          return NextResponse.json(
            { error: '标记类型、原因和风险等级不能为空' },
            { status: 400 }
          )
        }

        // 获取现有的风险标记
        const existingUser = await prisma.user.findUnique({
          where: { id: userId },
          select: { riskFlags: true }
        })

        const existingFlags = existingUser?.riskFlags as any[] || []
        const newFlag = {
          type: flagType,
          reason: reason,
          flaggedAt: new Date().toISOString(),
          flaggedBy: adminId
        }

        result = await prisma.user.update({
          where: { id: userId },
          data: {
            riskFlags: [...existingFlags, newFlag],
            riskLevel: riskLevel,
            flaggedAt: existingFlags.length === 0 ? new Date() : undefined, // 只在首次标记时设置
            flaggedBy: adminId,
            flagNotes: reason
          },
          select: {
            id: true,
            name: true,
            email: true,
            riskFlags: true,
            riskLevel: true,
            flaggedAt: true,
            flaggedBy: true,
            flagNotes: true
          }
        })
        break

      case 'removeFlag':
        // 移除用户的风险标记
        result = await prisma.user.update({
          where: { id: userId },
          data: {
            riskFlags: undefined,
            riskLevel: 'NORMAL',
            flaggedAt: null,
            flaggedBy: null,
            flagNotes: null
          },
          select: {
            id: true,
            name: true,
            email: true,
            riskFlags: true,
            riskLevel: true,
            flaggedAt: true,
            flaggedBy: true,
            flagNotes: true
          }
        })
        break

      case 'updateCreditScore':
        // 修改用户信用分数
        const { creditScore, reason: creditReason, adjustmentType, originalScore } = data
        if (typeof creditScore !== 'number' || !creditReason || !adjustmentType) {
          return NextResponse.json(
            { error: '信用分数、原因和调整类型不能为空' },
            { status: 400 }
          )
        }

        // 验证信用分数范围
        if (creditScore < 0 || creditScore > 1000) {
          return NextResponse.json(
            { error: '信用分数必须在0-1000之间' },
            { status: 400 }
          )
        }

        // 创建信用历史记录
        const creditHistory = {
          adjustmentType,
          originalScore,
          newScore: creditScore,
          reason: creditReason,
          adjustedBy: adminId,
          adjustedAt: new Date().toISOString()
        }

        // 获取现有的信用历史
        const existingUserForCredit = await prisma.user.findUnique({
          where: { id: userId },
          select: { creditHistory: true }
        })

        const existingCreditHistory = existingUserForCredit?.creditHistory as any[] || []

        result = await prisma.user.update({
          where: { id: userId },
          data: {
            creditScore: creditScore,
            creditHistory: [...existingCreditHistory, creditHistory]
          },
          select: {
            id: true,
            name: true,
            email: true,
            creditScore: true,
            creditHistory: true
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      user: result
    })

  } catch (error) {
    console.error('Admin users update error:', error)

    // 确保总是返回有效的JSON响应
    try {
      if (error instanceof Error && error.message === '需要管理员权限') {
        return NextResponse.json(
          { error: '需要管理员权限' },
          { status: 403 }
        )
      }

      return NextResponse.json(
        {
          error: '更新用户失败',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    } catch (jsonError) {
      console.error('JSON response error:', jsonError)
      return new NextResponse('Internal Server Error', { status: 500 })
    }
  }
}
