import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''

    const skip = (page - 1) * limit

    // 构建查询条件 - 查找有纠纷的订单
    const where: any = {
      status: 'DISPUTED'
    }
    
    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { product: { title: { contains: search, mode: 'insensitive' } } },
        { buyer: { name: { contains: search, mode: 'insensitive' } } },
        { buyer: { email: { contains: search, mode: 'insensitive' } } },
        { seller: { name: { contains: search, mode: 'insensitive' } } },
        { seller: { email: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // 获取纠纷订单列表
    const [disputes, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              title: true,
              price: true,
              images: true
            }
          },
          buyer: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          },
          seller: {
            select: {
              id: true,
              userId: true,
              name: true,
              email: true,
              creditScore: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.order.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      disputes,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Admin disputes API error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取纠纷列表失败' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 检查管理员权限并获取session
    const session = await requireAdmin()

    const body = await request.json()
    const { disputeId, action, data } = body

    if (!disputeId || !action) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    let result
    
    switch (action) {
      case 'resolveForBuyer':
        // 支持买家，退款给买家
        result = await prisma.order.update({
          where: { id: disputeId },
          data: { 
            status: 'REFUND_APPROVED',
            adminNotes: data.notes || '管理员支持买家，同意退款'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'resolveForSeller':
        // 支持卖家，维持交易
        result = await prisma.order.update({
          where: { id: disputeId },
          data: { 
            status: 'COMPLETED',
            adminNotes: data.notes || '管理员支持卖家，维持交易'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'requestMoreInfo':
        // 要求提供更多信息
        result = await prisma.order.update({
          where: { id: disputeId },
          data: { 
            adminNotes: data.notes || '管理员要求提供更多信息'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      case 'partialRefund':
        // 部分退款解决方案
        if (!data.refundAmount || data.refundAmount <= 0) {
          return NextResponse.json(
            { error: '退款金额必须大于0' },
            { status: 400 }
          )
        }

        result = await prisma.order.update({
          where: { id: disputeId },
          data: {
            status: 'PARTIAL_REFUND'
          },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })

        // 创建管理员备注
        if (session) {
          await prisma.adminNote.create({
            data: {
              orderId: disputeId,
              adminId: session.id,
              content: `管理员裁决部分退款: ${data.refundAmount} USDT. ${data.notes || ''}`,
              isPrivate: false
            }
          })
        }
        break

      case 'addNotes':
        // 添加管理员备注
        result = await prisma.order.update({
          where: { id: disputeId },
          data: { adminNotes: data.notes },
          include: {
            product: { select: { title: true } },
            buyer: { select: { name: true, email: true } },
            seller: { select: { name: true, email: true } }
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      dispute: result
    })

  } catch (error) {
    console.error('Admin disputes update error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '处理纠纷失败' },
      { status: 500 }
    )
  }
}
