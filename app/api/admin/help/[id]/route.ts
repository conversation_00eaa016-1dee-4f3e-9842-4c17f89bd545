import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取单个帮助文章详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params
    const article = await prisma.helpArticle.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        mediaFiles: {
          where: { status: 'ACTIVE' },
          orderBy: { createdAt: 'asc' }
        },
        versions: {
          select: {
            id: true,
            version: true,
            title: true,
            createdAt: true,
            author: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            version: 'desc'
          }
        }
      }
    })

    if (!article) {
      return NextResponse.json(
        { error: '帮助文章不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(article)

  } catch (error) {
    console.error('Get help article error:', error)
    return NextResponse.json(
      { error: '获取帮助文章详情失败' },
      { status: 500 }
    )
  }
}

// 更新帮助文章
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    const admin = await requireAdmin()

    const { id } = await params
    const body = await request.json()
    const {
      title,
      content,
      summary,
      category,
      subcategory,
      tags,
      keywords,
      articleType,
      status,
      isFeatured,
      sortOrder,
      difficulty,
      reviewNote,
      createVersion
    } = body

    // 验证文章是否存在
    const existingArticle = await prisma.helpArticle.findUnique({
      where: { id }
    })

    if (!existingArticle) {
      return NextResponse.json(
        { error: '帮助文章不存在' },
        { status: 404 }
      )
    }

    // 验证必填字段
    if (!title || !content || !category) {
      return NextResponse.json(
        { error: '标题、内容和分类为必填项' },
        { status: 400 }
      )
    }

    // 如果需要创建新版本
    if (createVersion && (
      title !== existingArticle.title || 
      content !== existingArticle.content
    )) {
      // 创建新版本
      const newVersion = await prisma.helpArticle.create({
        data: {
          title,
          content,
          summary,
          category,
          subcategory,
          tags: tags || '',
          keywords: keywords || '',
          articleType: articleType || 'GUIDE',
          status: status || 'DRAFT',
          isFeatured: isFeatured || false,
          sortOrder: sortOrder || 0,
          difficulty: difficulty || 'BEGINNER',
          version: existingArticle.version + 1,
          parentId: id,
          authorId: admin?.id || ''
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          mediaFiles: {
            where: { status: 'ACTIVE' },
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      return NextResponse.json(newVersion)
    }

    // 构建更新数据
    const updateData: any = {
      title,
      content,
      summary,
      category,
      subcategory,
      tags: tags || '',
      keywords: keywords || '',
      articleType,
      status,
      isFeatured,
      sortOrder,
      difficulty
    }

    // 如果状态变为已发布，记录审核信息
    if (status === 'PUBLISHED' && existingArticle.status !== 'PUBLISHED') {
      updateData.reviewerId = admin?.id
      updateData.reviewedAt = new Date()
      updateData.reviewNote = reviewNote
    }

    // 更新文章
    const article = await prisma.helpArticle.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        mediaFiles: {
          where: { status: 'ACTIVE' },
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    return NextResponse.json(article)

  } catch (error) {
    console.error('Update help article error:', error)
    return NextResponse.json(
      { error: '更新帮助文章失败' },
      { status: 500 }
    )
  }
}

// 删除帮助文章
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params

    // 验证文章是否存在
    const existingArticle = await prisma.helpArticle.findUnique({
      where: { id }
    })

    if (!existingArticle) {
      return NextResponse.json(
        { error: '帮助文章不存在' },
        { status: 404 }
      )
    }

    // 软删除：更新状态为DELETED
    await prisma.helpArticle.update({
      where: { id },
      data: {
        status: 'DELETED'
      }
    })

    // 将关联的媒体文件标记为孤立状态
    await prisma.helpMediaFile.updateMany({
      where: { articleId: id },
      data: {
        status: 'ORPHANED',
        articleId: null
      }
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Delete help article error:', error)
    return NextResponse.json(
      { error: '删除帮助文章失败' },
      { status: 500 }
    )
  }
}
