import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

// 获取帮助文章列表
export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const category = searchParams.get('category')
    const articleType = searchParams.get('articleType')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    
    if (status && status !== 'all') {
      where.status = status
    }
    
    if (category && category !== 'all') {
      where.category = category
    }
    
    if (articleType && articleType !== 'all') {
      where.articleType = articleType
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { content: { contains: search } },
        { summary: { contains: search } },
        { keywords: { contains: search } }
      ]
    }

    // 获取文章列表
    const [articles, total] = await Promise.all([
      prisma.helpArticle.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          reviewer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          mediaFiles: {
            where: { status: 'ACTIVE' },
            orderBy: { createdAt: 'asc' }
          }
        },
        orderBy: [
          { isFeatured: 'desc' },
          { sortOrder: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.helpArticle.count({ where })
    ])

    return NextResponse.json({
      articles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get help articles error:', error)
    return NextResponse.json(
      { error: '获取帮助文章列表失败' },
      { status: 500 }
    )
  }
}

// 创建新帮助文章
export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    const admin = await requireAdmin()

    const body = await request.json()
    const {
      title,
      content,
      summary,
      category,
      subcategory,
      tags,
      keywords,
      articleType,
      status,
      isFeatured,
      sortOrder,
      difficulty
    } = body

    // 验证必填字段
    if (!title || !content || !category) {
      return NextResponse.json(
        { error: '标题、内容和分类为必填项' },
        { status: 400 }
      )
    }

    // 验证分类
    const validCategories = ['payment', 'trading', 'security', 'account', 'general']
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: '无效的文章分类' },
        { status: 400 }
      )
    }

    // 验证文章类型
    const validTypes = ['GUIDE', 'FAQ', 'TUTORIAL', 'TIPS', 'TROUBLESHOOTING']
    if (articleType && !validTypes.includes(articleType)) {
      return NextResponse.json(
        { error: '无效的文章类型' },
        { status: 400 }
      )
    }

    // 验证状态
    const validStatuses = ['DRAFT', 'PUBLISHED', 'ARCHIVED']
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: '无效的状态' },
        { status: 400 }
      )
    }

    // 验证难度级别
    const validDifficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED']
    if (difficulty && !validDifficulties.includes(difficulty)) {
      return NextResponse.json(
        { error: '无效的难度级别' },
        { status: 400 }
      )
    }

    // 创建帮助文章
    const article = await prisma.helpArticle.create({
      data: {
        title,
        content,
        summary,
        category,
        subcategory,
        tags: tags || '',
        keywords: keywords || '',
        articleType: articleType || 'GUIDE',
        status: status || 'DRAFT',
        isFeatured: isFeatured || false,
        sortOrder: sortOrder || 0,
        difficulty: difficulty || 'BEGINNER',
        authorId: admin?.id || ''
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        mediaFiles: true
      }
    })

    return NextResponse.json(article, { status: 201 })

  } catch (error) {
    console.error('Create help article error:', error)
    return NextResponse.json(
      { error: '创建帮助文章失败' },
      { status: 500 }
    )
  }
}
