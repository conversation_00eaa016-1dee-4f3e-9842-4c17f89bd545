import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import sharp from 'sharp'
import { prisma } from '@/lib/prisma'

// 支持的文件类型
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/mov']
const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES]

// 文件大小限制
const MAX_IMAGE_SIZE = 5 * 1024 * 1024 // 5MB
const MAX_VIDEO_SIZE = 50 * 1024 * 1024 // 50MB

// 生成唯一文件名
function generateFileName(originalName: string, type: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()?.toLowerCase() || ''
  return `${type}_${timestamp}_${random}.${extension}`
}

// 获取文件元数据
async function getFileMetadata(buffer: Buffer, mimeType: string): Promise<any> {
  const metadata: any = {}
  
  if (ALLOWED_IMAGE_TYPES.includes(mimeType)) {
    try {
      const imageInfo = await sharp(buffer).metadata()
      metadata.width = imageInfo.width
      metadata.height = imageInfo.height
      metadata.format = imageInfo.format
      metadata.hasAlpha = imageInfo.hasAlpha
    } catch (error) {
      console.error('Error getting image metadata:', error)
    }
  } else if (ALLOWED_VIDEO_TYPES.includes(mimeType)) {
    // 对于视频文件，我们暂时只存储基本信息
    // 在实际应用中，可以使用 ffprobe 或类似工具获取视频元数据
    metadata.type = 'video'
    metadata.mimeType = mimeType
  }
  
  return metadata
}

// 上传帮助内容媒体文件
export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    const admin = await requireAdmin()

    const formData = await request.formData()
    const file = formData.get('file') as File
    const articleId = formData.get('articleId') as string | null

    if (!file) {
      return NextResponse.json(
        { error: '未选择文件' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: '不支持的文件类型。支持的格式：JPG、PNG、GIF、WebP、MP4、WebM、MOV' },
        { status: 400 }
      )
    }

    // 验证文件大小
    const isImage = ALLOWED_IMAGE_TYPES.includes(file.type)
    const isVideo = ALLOWED_VIDEO_TYPES.includes(file.type)
    const maxSize = isImage ? MAX_IMAGE_SIZE : MAX_VIDEO_SIZE
    const mediaType = isImage ? 'image' : 'video'

    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `文件大小不能超过 ${maxSize / (1024 * 1024)}MB` },
        { status: 400 }
      )
    }

    // 如果指定了文章ID，验证文章是否存在
    if (articleId) {
      const article = await prisma.helpArticle.findUnique({
        where: { id: articleId }
      })

      if (!article) {
        return NextResponse.json(
          { error: '指定的帮助文章不存在' },
          { status: 404 }
        )
      }
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 获取文件元数据
    const metadata = await getFileMetadata(buffer, file.type)

    // 处理图片（压缩和优化）
    let processedBuffer: Buffer = buffer
    if (isImage) {
      try {
        processedBuffer = await sharp(Buffer.from(buffer))
          .resize(1920, 1080, { 
            fit: 'inside', 
            withoutEnlargement: true 
          })
          .jpeg({ 
            quality: 85, 
            progressive: true 
          })
          .toBuffer()
        
        // 更新元数据
        const processedInfo = await sharp(processedBuffer).metadata()
        metadata.processedWidth = processedInfo.width
        metadata.processedHeight = processedInfo.height
        metadata.originalSize = buffer.length
        metadata.processedSize = processedBuffer.length
      } catch (error) {
        console.error('Image processing error:', error)
        // 如果处理失败，使用原始文件
        processedBuffer = buffer
      }
    }

    // 生成文件名和路径
    const fileName = generateFileName(file.name, mediaType)
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'help')
    
    // 确保上传目录存在
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    // 保存文件
    const filePath = join(uploadDir, fileName)
    await writeFile(filePath, processedBuffer)

    // 保存文件信息到数据库
    const fileUrl = `/uploads/help/${fileName}`
    const helpMediaFile = await prisma.helpMediaFile.create({
      data: {
        filename: file.name,
        storedName: fileName,
        filePath: `uploads/help/${fileName}`,
        fileUrl,
        fileSize: processedBuffer.length,
        mimeType: file.type,
        mediaType,
        metadata,
        articleId: articleId || null,
        uploaderId: admin?.id || '',
        status: 'ACTIVE',
        isUsed: false
      }
    })

    return NextResponse.json({
      success: true,
      file: {
        id: helpMediaFile.id,
        url: fileUrl,
        filename: file.name,
        size: processedBuffer.length,
        type: mediaType,
        mimeType: file.type,
        metadata
      }
    })

  } catch (error) {
    console.error('Help media upload error:', error)
    return NextResponse.json(
      { error: '文件上传失败' },
      { status: 500 }
    )
  }
}

// 获取帮助内容媒体文件列表
export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const articleId = searchParams.get('articleId')
    const mediaType = searchParams.get('type') // 'image' | 'video'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {
      status: 'ACTIVE'
    }

    if (articleId) {
      where.articleId = articleId
    }

    if (mediaType) {
      where.mediaType = mediaType
    }

    // 获取文件列表
    const [files, total] = await Promise.all([
      prisma.helpMediaFile.findMany({
        where,
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          article: {
            select: {
              id: true,
              title: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.helpMediaFile.count({ where })
    ])

    return NextResponse.json({
      files,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get help media files error:', error)
    return NextResponse.json(
      { error: '获取媒体文件列表失败' },
      { status: 500 }
    )
  }
}
