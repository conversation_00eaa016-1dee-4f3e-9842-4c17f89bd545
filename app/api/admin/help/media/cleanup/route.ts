import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { unlink, readdir } from 'fs/promises'
import { join } from 'path'
import { prisma } from '@/lib/prisma'

// 清理孤立的媒体文件
export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const body = await request.json()
    const { dryRun = true } = body // 默认为试运行模式

    const results = {
      orphanedFiles: [] as string[],
      deletedFiles: [] as string[],
      errors: [] as string[],
      summary: {
        totalOrphaned: 0,
        totalDeleted: 0,
        totalErrors: 0
      }
    }

    // 1. 查找数据库中标记为孤立或未使用的文件
    const orphanedDbFiles = await prisma.helpMediaFile.findMany({
      where: {
        OR: [
          { status: 'ORPHANED' },
          { 
            AND: [
              { isUsed: false },
              { articleId: null },
              { 
                createdAt: {
                  lt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 超过24小时未使用
                }
              }
            ]
          }
        ]
      }
    })

    // 2. 查找文件系统中存在但数据库中不存在的文件
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'help')
    let physicalFiles: string[] = []
    
    try {
      physicalFiles = await readdir(uploadDir)
    } catch (error) {
      console.error('Error reading upload directory:', error)
      results.errors.push('无法读取上传目录')
    }

    // 获取数据库中所有活跃文件的存储名称
    const activeDbFiles = await prisma.helpMediaFile.findMany({
      where: { status: 'ACTIVE' },
      select: { storedName: true }
    })
    
    const activeFileNames = new Set(activeDbFiles.map(f => f.storedName))

    // 找出物理文件中不在数据库中的文件
    const orphanedPhysicalFiles = physicalFiles.filter(fileName => 
      !activeFileNames.has(fileName) && 
      (fileName.startsWith('image_') || fileName.startsWith('video_'))
    )

    // 3. 处理数据库中的孤立文件
    for (const dbFile of orphanedDbFiles) {
      results.orphanedFiles.push(`DB: ${dbFile.filename} (${dbFile.storedName})`)
      
      if (!dryRun) {
        try {
          // 删除物理文件
          const filePath = join(uploadDir, dbFile.storedName)
          try {
            await unlink(filePath)
          } catch (error) {
            console.error(`Error deleting file ${filePath}:`, error)
          }

          // 删除数据库记录
          await prisma.helpMediaFile.delete({
            where: { id: dbFile.id }
          })

          results.deletedFiles.push(`DB: ${dbFile.filename}`)
        } catch (error) {
          console.error(`Error deleting DB file ${dbFile.id}:`, error)
          results.errors.push(`删除数据库文件失败: ${dbFile.filename}`)
        }
      }
    }

    // 4. 处理物理文件中的孤立文件
    for (const fileName of orphanedPhysicalFiles) {
      results.orphanedFiles.push(`Physical: ${fileName}`)
      
      if (!dryRun) {
        try {
          const filePath = join(uploadDir, fileName)
          await unlink(filePath)
          results.deletedFiles.push(`Physical: ${fileName}`)
        } catch (error) {
          console.error(`Error deleting physical file ${fileName}:`, error)
          results.errors.push(`删除物理文件失败: ${fileName}`)
        }
      }
    }

    // 5. 更新统计信息
    results.summary.totalOrphaned = results.orphanedFiles.length
    results.summary.totalDeleted = results.deletedFiles.length
    results.summary.totalErrors = results.errors.length

    return NextResponse.json({
      success: true,
      dryRun,
      results
    })

  } catch (error) {
    console.error('Cleanup help media files error:', error)
    return NextResponse.json(
      { error: '清理媒体文件失败' },
      { status: 500 }
    )
  }
}

// 获取孤立文件统计信息
export async function GET() {
  try {
    // 检查管理员权限
    await requireAdmin()

    // 统计孤立的数据库文件
    const orphanedDbCount = await prisma.helpMediaFile.count({
      where: {
        OR: [
          { status: 'ORPHANED' },
          { 
            AND: [
              { isUsed: false },
              { articleId: null },
              { 
                createdAt: {
                  lt: new Date(Date.now() - 24 * 60 * 60 * 1000)
                }
              }
            ]
          }
        ]
      }
    })

    // 统计总文件数
    const totalFiles = await prisma.helpMediaFile.count({
      where: { status: 'ACTIVE' }
    })

    // 统计使用中的文件数
    const usedFiles = await prisma.helpMediaFile.count({
      where: { 
        status: 'ACTIVE',
        isUsed: true 
      }
    })

    // 统计各类型文件数
    const imageCount = await prisma.helpMediaFile.count({
      where: { 
        status: 'ACTIVE',
        mediaType: 'image' 
      }
    })

    const videoCount = await prisma.helpMediaFile.count({
      where: { 
        status: 'ACTIVE',
        mediaType: 'video' 
      }
    })

    return NextResponse.json({
      statistics: {
        totalFiles,
        usedFiles,
        unusedFiles: totalFiles - usedFiles,
        orphanedFiles: orphanedDbCount,
        imageFiles: imageCount,
        videoFiles: videoCount
      }
    })

  } catch (error) {
    console.error('Get help media statistics error:', error)
    return NextResponse.json(
      { error: '获取媒体文件统计失败' },
      { status: 500 }
    )
  }
}
