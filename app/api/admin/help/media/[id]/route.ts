import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { unlink } from 'fs/promises'
import { join } from 'path'
import { prisma } from '@/lib/prisma'

// 获取单个媒体文件信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params

    const mediaFile = await prisma.helpMediaFile.findUnique({
      where: { id },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        article: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    if (!mediaFile) {
      return NextResponse.json(
        { error: '媒体文件不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(mediaFile)

  } catch (error) {
    console.error('Get help media file error:', error)
    return NextResponse.json(
      { error: '获取媒体文件信息失败' },
      { status: 500 }
    )
  }
}

// 更新媒体文件信息
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params
    const body = await request.json()
    const { articleId, isUsed, status } = body

    // 验证媒体文件是否存在
    const existingFile = await prisma.helpMediaFile.findUnique({
      where: { id }
    })

    if (!existingFile) {
      return NextResponse.json(
        { error: '媒体文件不存在' },
        { status: 404 }
      )
    }

    // 如果指定了文章ID，验证文章是否存在
    if (articleId) {
      const article = await prisma.helpArticle.findUnique({
        where: { id: articleId }
      })

      if (!article) {
        return NextResponse.json(
          { error: '指定的帮助文章不存在' },
          { status: 404 }
        )
      }
    }

    // 更新媒体文件信息
    const updatedFile = await prisma.helpMediaFile.update({
      where: { id },
      data: {
        ...(articleId !== undefined && { articleId }),
        ...(isUsed !== undefined && { isUsed }),
        ...(status !== undefined && { status })
      },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        article: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    return NextResponse.json(updatedFile)

  } catch (error) {
    console.error('Update help media file error:', error)
    return NextResponse.json(
      { error: '更新媒体文件失败' },
      { status: 500 }
    )
  }
}

// 删除媒体文件
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查管理员权限
    await requireAdmin()

    const { id } = await params

    // 获取媒体文件信息
    const mediaFile = await prisma.helpMediaFile.findUnique({
      where: { id }
    })

    if (!mediaFile) {
      return NextResponse.json(
        { error: '媒体文件不存在' },
        { status: 404 }
      )
    }

    // 检查文件是否正在被使用
    if (mediaFile.isUsed) {
      return NextResponse.json(
        { error: '文件正在被使用，无法删除' },
        { status: 400 }
      )
    }

    // 删除物理文件
    try {
      const filePath = join(process.cwd(), 'public', mediaFile.filePath)
      await unlink(filePath)
    } catch (error) {
      console.error('Error deleting physical file:', error)
      // 即使物理文件删除失败，也继续删除数据库记录
    }

    // 删除数据库记录
    await prisma.helpMediaFile.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      message: '媒体文件删除成功'
    })

  } catch (error) {
    console.error('Delete help media file error:', error)
    return NextResponse.json(
      { error: '删除媒体文件失败' },
      { status: 500 }
    )
  }
}
