import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取提现券列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const skip = (page - 1) * limit

    const where: any = {}

    if (status === 'used') {
      where.isUsed = true
    } else if (status === 'unused') {
      where.isUsed = false
      where.validUntil = { gt: new Date() }
    } else if (status === 'expired') {
      where.isUsed = false
      where.validUntil = { lte: new Date() }
    }

    if (search) {
      where.OR = [
        { code: { contains: search } },
        { description: { contains: search } },
        { user: { name: { contains: search } } },
        { user: { email: { contains: search } } }
      ]
    }

    const [vouchers, total] = await Promise.all([
      prisma.withdrawalVoucher.findMany({
        where,
        include: {
          user: {
            select: { name: true, email: true }
          },
          issuer: {
            select: { name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.withdrawalVoucher.count({ where })
    ])

    // 统计信息
    const stats = await prisma.withdrawalVoucher.aggregate({
      _sum: { amount: true },
      _count: {
        _all: true,
        isUsed: true
      }
    })

    const expiredCount = await prisma.withdrawalVoucher.count({
      where: {
        isUsed: false,
        validUntil: { lte: new Date() }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        vouchers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        statistics: {
          totalVouchers: stats._count._all,
          usedVouchers: stats._count.isUsed,
          expiredVouchers: expiredCount,
          activeVouchers: stats._count._all - stats._count.isUsed - expiredCount,
          totalValue: stats._sum.amount || 0
        }
      }
    })

  } catch (error) {
    console.error('获取提现券列表失败:', error)
    return NextResponse.json(
      { error: '获取提现券列表失败' },
      { status: 500 }
    )
  }
}

// 创建提现券
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { 
      amount, 
      description, 
      validDays, 
      quantity = 1, 
      targetUserId 
    } = body

    if (!amount || !description || !validDays) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    if (amount <= 0 || validDays <= 0 || quantity <= 0) {
      return NextResponse.json(
        { error: '参数值必须大于0' },
        { status: 400 }
      )
    }

    if (quantity > 100) {
      return NextResponse.json(
        { error: '单次最多创建100张提现券' },
        { status: 400 }
      )
    }

    // 如果指定了目标用户，验证用户存在
    if (targetUserId) {
      const targetUser = await prisma.user.findUnique({
        where: { id: targetUserId },
        select: { id: true, name: true, email: true }
      })

      if (!targetUser) {
        return NextResponse.json(
          { error: '目标用户不存在' },
          { status: 404 }
        )
      }
    }

    const validUntil = new Date()
    validUntil.setDate(validUntil.getDate() + validDays)

    // 批量创建提现券
    const vouchers: any[] = []
    for (let i = 0; i < quantity; i++) {
      const code = generateVoucherCode()
      
      const voucher = await prisma.withdrawalVoucher.create({
        data: {
          code,
          amount,
          description,
          validUntil,
          issuedBy: session.user.id,
          ...(targetUserId && { usedBy: targetUserId })
        }
      })

      vouchers.push(voucher)

      // 如果指定了目标用户，发送通知
      if (targetUserId) {
        await prisma.notification.create({
          data: {
            userId: targetUserId,
            type: 'REWARD',
            title: '您收到一张提现券',
            message: `管理员为您发放了一张 ${amount} USDT 免手续费提现券`,
            data: {
              voucherId: voucher.id,
              voucherCode: code,
              amount,
              validUntil
            },
            priority: 'NORMAL'
          }
        }).catch(() => {})
      }
    }

    return NextResponse.json({
      success: true,
      message: `成功创建 ${quantity} 张提现券`,
      data: {
        vouchers: vouchers.map(v => ({
          id: v.id,
          code: v.code,
          amount: v.amount,
          validUntil: v.validUntil
        })),
        totalValue: amount * quantity
      }
    })

  } catch (error) {
    console.error('创建提现券失败:', error)
    return NextResponse.json(
      { error: '创建提现券失败' },
      { status: 500 }
    )
  }
}

// 停用提现券
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { voucherIds, action } = body

    if (!voucherIds || !Array.isArray(voucherIds) || !action) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    if (!['disable', 'enable'].includes(action)) {
      return NextResponse.json(
        { error: '无效的操作' },
        { status: 400 }
      )
    }

    let updateData: any = {}

    if (action === 'disable') {
      // 停用：设置为已过期
      updateData.validUntil = new Date('2000-01-01')
    } else {
      // 启用：延长有效期7天
      const newValidUntil = new Date()
      newValidUntil.setDate(newValidUntil.getDate() + 7)
      updateData.validUntil = newValidUntil
    }

    const result = await prisma.withdrawalVoucher.updateMany({
      where: {
        id: { in: voucherIds },
        isUsed: false // 只能操作未使用的券
      },
      data: updateData
    })

    return NextResponse.json({
      success: true,
      message: `成功${action === 'disable' ? '停用' : '启用'} ${result.count} 张提现券`,
      data: { affectedCount: result.count }
    })

  } catch (error) {
    console.error('操作提现券失败:', error)
    return NextResponse.json(
      { error: '操作提现券失败' },
      { status: 500 }
    )
  }
}

// 删除提现券
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const voucherIds = searchParams.get('ids')?.split(',') || []

    if (voucherIds.length === 0) {
      return NextResponse.json(
        { error: '请选择要删除的提现券' },
        { status: 400 }
      )
    }

    // 只能删除未使用的券
    const result = await prisma.withdrawalVoucher.deleteMany({
      where: {
        id: { in: voucherIds },
        isUsed: false
      }
    })

    return NextResponse.json({
      success: true,
      message: `成功删除 ${result.count} 张提现券`,
      data: { deletedCount: result.count }
    })

  } catch (error) {
    console.error('删除提现券失败:', error)
    return NextResponse.json(
      { error: '删除提现券失败' },
      { status: 500 }
    )
  }
}

// 生成提现券代码
function generateVoucherCode(): string {
  const prefix = 'WV'
  const timestamp = Date.now().toString(36).toUpperCase()
  const random = Math.random().toString(36).substr(2, 6).toUpperCase()
  return `${prefix}${timestamp}${random}`
}
