import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个提现券详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = params

    const voucher = await prisma.withdrawalVoucher.findUnique({
      where: { id },
      include: {
        issuer: {
          select: {
            name: true,
            email: true
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    if (!voucher) {
      return NextResponse.json(
        { error: '提现券不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: voucher
    })

  } catch (error) {
    console.error('获取提现券详情失败:', error)
    return NextResponse.json(
      { error: '获取提现券详情失败' },
      { status: 500 }
    )
  }
}

// 更新提现券状态
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { action } = body

    if (!action || !['cancel', 'activate'].includes(action)) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    const voucher = await prisma.withdrawalVoucher.findUnique({
      where: { id }
    })

    if (!voucher) {
      return NextResponse.json(
        { error: '提现券不存在' },
        { status: 404 }
      )
    }

    let updateData: any = {}

    if (action === 'cancel') {
      // 检查提现券是否可以取消（未使用且未过期）
      if (voucher.isUsed) {
        return NextResponse.json(
          { error: '已使用的提现券无法取消' },
          { status: 400 }
        )
      }

      if (new Date() > voucher.validUntil) {
        return NextResponse.json(
          { error: '已过期的提现券无法取消' },
          { status: 400 }
        )
      }

      // 将过期时间设置为当前时间，实现"取消"效果
      updateData = {
        validUntil: new Date()
      }
    } else if (action === 'activate') {
      // 检查提现券是否可以激活（未使用但已过期）
      if (voucher.isUsed) {
        return NextResponse.json(
          { error: '已使用的提现券无法激活' },
          { status: 400 }
        )
      }

      if (new Date() <= voucher.validUntil) {
        return NextResponse.json(
          { error: '有效的提现券无需激活' },
          { status: 400 }
        )
      }

      // 延长有效期30天
      const newValidUntil = new Date()
      newValidUntil.setDate(newValidUntil.getDate() + 30)

      updateData = {
        validUntil: newValidUntil
      }
    }

    const updatedVoucher = await prisma.withdrawalVoucher.update({
      where: { id },
      data: updateData,
      include: {
        issuer: {
          select: {
            name: true,
            email: true
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: action === 'cancel' ? '提现券已取消' : '提现券已重新激活',
      data: updatedVoucher
    })

  } catch (error) {
    console.error('更新提现券状态失败:', error)
    return NextResponse.json(
      { error: '更新提现券状态失败' },
      { status: 500 }
    )
  }
}

// 删除提现券
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = params

    const voucher = await prisma.withdrawalVoucher.findUnique({
      where: { id }
    })

    if (!voucher) {
      return NextResponse.json(
        { error: '提现券不存在' },
        { status: 404 }
      )
    }

    if (voucher.isUsed) {
      return NextResponse.json(
        { error: '已使用的提现券不能删除' },
        { status: 400 }
      )
    }

    await prisma.withdrawalVoucher.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      message: '提现券已删除'
    })

  } catch (error) {
    console.error('删除提现券失败:', error)
    return NextResponse.json(
      { error: '删除提现券失败' },
      { status: 500 }
    )
  }
}
