import { NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/admin'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // 检查管理员权限
    await requireAdmin()

    // 获取统计数据
    const [
      totalUsers,
      totalProducts,
      totalOrders,
      pendingOrders,
      pendingPayments,
      completedOrders,
      giftCardStats,
      redemptionCodeStats,
      feedbackCount
    ] = await Promise.all([
      // 总用户数（排除已删除的匿名用户）
      prisma.user.count({
        where: {
          NOT: {
            name: {
              contains: '已删除用户#'
            }
          }
        }
      }),
      
      // 总商品数（排除已删除用户的商品）
      prisma.product.count({
        where: {
          seller: {
            NOT: {
              name: {
                contains: '已删除用户#'
              }
            }
          }
        }
      }),
      
      // 总订单数（包含已删除用户的历史订单，用于业务统计）
      prisma.order.count(),
      
      // 待处理订单数（待付款、已付款、已发货）
      prisma.order.count({
        where: {
          status: {
            in: ['PENDING_PAYMENT', 'PAID', 'SHIPPED']
          }
        }
      }),
      
      // 待确认付款数
      prisma.order.count({
        where: {
          status: 'PAID',
          paymentConfirmed: false
        }
      }),
      
      // 已完成订单（用于计算平台收入）
      prisma.order.findMany({
        where: {
          status: 'COMPLETED'
        },
        select: {
          platformFee: true
        }
      }),

      // 礼品卡统计
      prisma.giftCard.groupBy({
        by: ['status'],
        _count: {
          id: true
        },
        _sum: {
          faceValue: true
        }
      }),

      // 兑换券统计
      prisma.redemptionCode.groupBy({
        by: ['status'],
        _count: {
          id: true
        }
      }),

      // 反馈数量
      prisma.userFeedback.count()
    ])

    // 计算平台总收入
    const totalRevenue = completedOrders.reduce((sum: number, order: any) => sum + (order.platformFee || 0), 0)

    // 处理礼品卡统计
    const giftCardSummary = giftCardStats.reduce((acc: any, stat: any) => {
      acc[stat.status] = {
        count: stat._count.id,
        totalValue: stat._sum.faceValue || 0
      }
      return acc
    }, {})

    // 处理兑换券统计
    const redemptionCodeSummary = redemptionCodeStats.reduce((acc: any, stat: any) => {
      acc[stat.status] = stat._count.id
      return acc
    }, {})

    const stats = {
      totalUsers,
      totalProducts,
      totalOrders,
      pendingOrders,
      pendingPayments,
      totalRevenue,
      feedbacks: feedbackCount,
      giftCards: {
        total: giftCardStats.reduce((sum: number, stat: any) => sum + stat._count.id, 0),
        generated: giftCardSummary.GENERATED?.count || 0,
        sold: giftCardSummary.SOLD?.count || 0,
        redeemed: giftCardSummary.REDEEMED?.count || 0,
        expired: giftCardSummary.EXPIRED?.count || 0,
        totalValue: giftCardStats.reduce((sum: number, stat: any) => sum + (stat._sum.faceValue || 0), 0)
      },
      redemptionCodes: {
        total: redemptionCodeStats.reduce((sum: number, stat: any) => sum + stat._count.id, 0),
        active: redemptionCodeSummary.ACTIVE || 0,
        used: redemptionCodeSummary.USED || 0,
        expired: redemptionCodeSummary.EXPIRED || 0,
        revoked: redemptionCodeSummary.REVOKED || 0
      }
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Admin dashboard error:', error)
    
    if (error instanceof Error && error.message === '需要管理员权限') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
    
    return NextResponse.json(
      { error: '获取统计数据失败' },
      { status: 500 }
    )
  }
}
