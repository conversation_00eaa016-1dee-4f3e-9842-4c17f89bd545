import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const files: File[] = []
    
    // 收集所有上传的文件
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('image_') && value instanceof File) {
        files.push(value)
      }
    }

    if (files.length === 0) {
      return NextResponse.json(
        { error: '没有找到要上传的图片' },
        { status: 400 }
      )
    }

    if (files.length > 6) {
      return NextResponse.json(
        { error: '最多只能上传6张图片' },
        { status: 400 }
      )
    }

    const uploadedUrls: string[] = []
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'after-sales')

    // 确保上传目录存在
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    for (const file of files) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        return NextResponse.json(
          { error: '只能上传图片文件' },
          { status: 400 }
        )
      }

      // 验证文件大小（5MB限制）
      if (file.size > 5 * 1024 * 1024) {
        return NextResponse.json(
          { error: '图片文件不能超过5MB' },
          { status: 400 }
        )
      }

      // 生成唯一文件名
      const timestamp = Date.now()
      const randomString = Math.random().toString(36).substring(2, 15)
      const extension = file.name.split('.').pop() || 'jpg'
      const fileName = `${session.user.id}_${timestamp}_${randomString}.${extension}`
      const filePath = join(uploadDir, fileName)

      // 保存文件
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(filePath, buffer)

      // 添加到URL列表
      uploadedUrls.push(`/uploads/after-sales/${fileName}`)
    }

    return NextResponse.json({
      success: true,
      urls: uploadedUrls,
      message: `成功上传${files.length}张图片`
    })

  } catch (error) {
    console.error('上传售后图片失败:', error)
    return NextResponse.json(
      { error: '上传图片失败' },
      { status: 500 }
    )
  }
}
