import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { isValidEmail, isValidBinanceUID, isValidBNBAddress } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      email, 
      password, 
      name,
      binanceUid,
      bnbWalletAddress,
      city,
      district 
    } = body

    // 验证必填字段
    if (!email || !password) {
      return NextResponse.json(
        { error: '邮箱和密码为必填项' },
        { status: 400 }
      )
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      )
    }

    // 验证币安UID格式（如果提供）
    if (binanceUid && !isValidBinanceUID(binanceUid)) {
      return NextResponse.json(
        { error: '币安UID格式不正确，应为8位以上纯数字' },
        { status: 400 }
      )
    }

    // 验证BNB钱包地址格式（如果提供）
    if (bnbWalletAddress && !isValidBNBAddress(bnbWalletAddress)) {
      return NextResponse.json(
        { error: 'BNB钱包地址格式不正确' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 400 }
      )
    }

    // 检查币安UID是否已存在（如果提供）
    if (binanceUid) {
      const existingBinanceUser = await prisma.user.findUnique({
        where: { binanceUid }
      })

      if (existingBinanceUser) {
        return NextResponse.json(
          { error: '该币安UID已被绑定' },
          { status: 400 }
        )
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 生成简单的用户ID
    const randomString = Math.random().toString(36).substring(2, 14)
    const userId = `user-${randomString}`

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        userId,
        name: name || email.split('@')[0], // 如果没有提供名称，使用邮箱前缀
        binanceUid,
        bnbWalletAddress,
        city,
        district,
      }
    })

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user
    
    return NextResponse.json({
      message: '注册成功',
      user: userWithoutPassword
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    )
  }
}
