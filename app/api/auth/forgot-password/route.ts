import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: '邮箱地址为必填项' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      // 为了安全，即使用户不存在也返回成功消息
      return NextResponse.json({
        message: '如果该邮箱地址存在，重置密码链接已发送'
      })
    }

    // 生成重置令牌
    const resetToken = crypto.randomBytes(32).toString('hex')
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1小时后过期

    // 保存重置令牌到数据库
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry
      }
    })

    // 在实际应用中，这里应该发送邮件
    // 现在我们只是返回成功消息和令牌（仅用于测试）
    console.log(`密码重置令牌: ${resetToken}`)
    console.log(`重置链接: http://localhost:3000/auth/reset-password?token=${resetToken}`)

    return NextResponse.json({
      message: '重置密码链接已发送到您的邮箱',
      // 仅在开发环境中返回令牌用于测试
      ...(process.env.NODE_ENV === 'development' && {
        resetToken,
        resetLink: `http://localhost:3000/auth/reset-password?token=${resetToken}`
      })
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: '服务器错误，请稍后重试' },
      { status: 500 }
    )
  }
}
