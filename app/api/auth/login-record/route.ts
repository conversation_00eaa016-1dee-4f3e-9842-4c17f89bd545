import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordSecurityLog, getClientIP } from '@/lib/security'

// 记录登录活动
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { success, email, deviceInfo } = body

    // 获取请求信息
    const ipAddress = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || ''

    if (success) {
      // 登录成功 - 创建会话记录
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`
      
      // 解析设备信息
      const deviceData = parseDeviceInfo(userAgent, deviceInfo)

      // 创建会话记录
      const userSession = await prisma.userSession.create({
        data: {
          userId: session.user.id,
          sessionId,
          deviceName: deviceData.deviceName,
          deviceType: deviceData.deviceType,
          browser: deviceData.browser,
          os: deviceData.os,
          ipAddress,
          location: deviceData.location,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
        }
      })

      // 记录成功登录日志
      await recordSecurityLog(
        session.user.id,
        'LOGIN',
        `用户登录成功，设备：${deviceData.deviceName}`,
        'SUCCESS',
        request
      )

      // 检查异常登录
      await checkSuspiciousLogin(session.user.id, ipAddress, deviceData)

      return NextResponse.json({
        message: '登录记录创建成功',
        session: userSession
      })
    } else {
      // 登录失败 - 只记录日志
      // 尝试根据邮箱找到用户ID
      let userId: string | null = null
      if (email) {
        const user = await prisma.user.findUnique({
          where: { email },
          select: { id: true }
        })
        userId = user?.id || null
      }

      if (userId) {
        await recordSecurityLog(
          userId,
          'LOGIN',
          '用户登录失败：密码错误',
          'FAILED',
          request
        )
      }

      return NextResponse.json({
        message: '登录失败记录已保存'
      })
    }

  } catch (error) {
    console.error('Login record error:', error)
    return NextResponse.json(
      { error: '记录登录活动失败' },
      { status: 500 }
    )
  }
}

// 解析设备信息
function parseDeviceInfo(userAgent: string, deviceInfo?: any) {
  const ua = userAgent.toLowerCase()
  
  // 解析浏览器
  let browser = 'Unknown'
  if (ua.includes('chrome') && !ua.includes('edg')) browser = 'Chrome'
  else if (ua.includes('firefox')) browser = 'Firefox'
  else if (ua.includes('safari') && !ua.includes('chrome')) browser = 'Safari'
  else if (ua.includes('edg')) browser = 'Edge'
  else if (ua.includes('opera')) browser = 'Opera'
  
  // 解析操作系统
  let os = 'Unknown'
  if (ua.includes('windows nt 10')) os = 'Windows 10'
  else if (ua.includes('windows nt 6.3')) os = 'Windows 8.1'
  else if (ua.includes('windows nt 6.1')) os = 'Windows 7'
  else if (ua.includes('windows')) os = 'Windows'
  else if (ua.includes('mac os x')) {
    const macMatch = ua.match(/mac os x (\d+[._]\d+[._]?\d*)/)
    os = macMatch ? `macOS ${macMatch[1].replace(/_/g, '.')}` : 'macOS'
  }
  else if (ua.includes('linux')) os = 'Linux'
  else if (ua.includes('android')) {
    const androidMatch = ua.match(/android (\d+\.?\d*\.?\d*)/)
    os = androidMatch ? `Android ${androidMatch[1]}` : 'Android'
  }
  else if (ua.includes('iphone') || ua.includes('ipad')) {
    const iosMatch = ua.match(/os (\d+_\d+_?\d*)/)
    os = iosMatch ? `iOS ${iosMatch[1].replace(/_/g, '.')}` : 'iOS'
  }
  
  // 解析设备类型
  let deviceType = 'desktop'
  if (ua.includes('mobile') && !ua.includes('tablet')) deviceType = 'mobile'
  else if (ua.includes('tablet') || ua.includes('ipad')) deviceType = 'tablet'
  
  return {
    deviceName: deviceInfo?.deviceName || `${browser} on ${os}`,
    deviceType,
    browser,
    os,
    location: deviceInfo?.location || null
  }
}

// 检查可疑登录
async function checkSuspiciousLogin(userId: string, ipAddress: string, deviceData: any) {
  try {
    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    // 检查是否是新设备
    const existingSession = await prisma.userSession.findFirst({
      where: {
        userId,
        deviceType: deviceData.deviceType,
        browser: deviceData.browser,
        os: deviceData.os
      }
    })

    if (!existingSession) {
      await recordSecurityLog(
        userId,
        'SUSPICIOUS_ACTIVITY',
        `检测到新设备登录：${deviceData.deviceName}`,
        'SUCCESS'
      )
    }

    // 检查是否是新IP
    const recentIPs = await prisma.securityLog.findMany({
      where: {
        userId,
        action: 'LOGIN',
        status: 'SUCCESS',
        ipAddress: { not: null },
        createdAt: { gte: oneDayAgo }
      },
      select: { ipAddress: true },
      distinct: ['ipAddress']
    })

    const isNewIP = !recentIPs.some(log => log.ipAddress === ipAddress)
    if (isNewIP && recentIPs.length > 0) {
      await recordSecurityLog(
        userId,
        'SUSPICIOUS_ACTIVITY',
        `检测到新IP地址登录：${ipAddress}`,
        'SUCCESS'
      )
    }

    // 检查短时间内多次登录
    const recentLogins = await prisma.securityLog.count({
      where: {
        userId,
        action: 'LOGIN',
        status: 'SUCCESS',
        createdAt: { gte: new Date(now.getTime() - 60 * 60 * 1000) } // 1小时内
      }
    })

    if (recentLogins >= 5) {
      await recordSecurityLog(
        userId,
        'SUSPICIOUS_ACTIVITY',
        `检测到频繁登录：1小时内登录${recentLogins}次`,
        'SUCCESS'
      )
    }

  } catch (error) {
    console.error('Check suspicious login error:', error)
  }
}
