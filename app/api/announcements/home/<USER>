import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const now = new Date()

    // 获取首页显示的公告
    const announcements = await prisma.announcement.findMany({
      where: {
        status: 'PUBLISHED',
        showOnHome: true,
        AND: [
          // 确保公告在有效期内
          {
            OR: [
              { expireAt: null }, // 没有过期时间
              { expireAt: { gt: now } } // 或者还没过期
            ]
          },
          // 确保已经到了发布时间
          {
            OR: [
              { publishAt: null }, // 没有定时发布
              { publishAt: { lte: now } } // 或者已经到了发布时间
            ]
          }
        ]
      },
      select: {
        id: true,
        title: true,
        content: true,
        summary: true,
        category: true,
        priority: true,
        isSticky: true,
        publishAt: true,
        createdAt: true,
        viewCount: true
      },
      orderBy: [
        { isSticky: 'desc' }, // 置顶的优先
        { priority: 'desc' }, // 按优先级排序 (URGENT > HIGH > NORMAL > LOW)
        { publishAt: 'desc' }, // 按发布时间排序
        { createdAt: 'desc' } // 最后按创建时间排序
      ],
      take: 10 // 最多取10条公告
    })

    // 转换数据格式以适配轮播组件
    const carouselItems = announcements.map((announcement) => ({
      id: announcement.id,
      title: announcement.title,
      description: announcement.summary || announcement.content.substring(0, 100) + '...',
      image: '', // 公告暂时不支持图片，使用默认背景
      link: `/announcements/${announcement.id}`, // 点击跳转到公告详情页
      category: announcement.category,
      priority: announcement.priority,
      isSticky: announcement.isSticky,
      viewCount: announcement.viewCount,
      publishAt: announcement.publishAt,
      createdAt: announcement.createdAt
    }))

    // 增加查看次数（异步执行，不影响响应速度）
    if (announcements.length > 0) {
      // 批量更新查看次数
      const announcementIds = announcements.map(a => a.id)
      prisma.announcement.updateMany({
        where: {
          id: { in: announcementIds }
        },
        data: {
          viewCount: { increment: 1 }
        }
      }).catch(error => {
        console.error('更新公告查看次数失败:', error)
      })
    }

    return NextResponse.json({
      success: true,
      announcements: carouselItems,
      total: announcements.length
    })

  } catch (error) {
    console.error('获取首页公告失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: '获取公告失败',
        announcements: [],
        total: 0
      },
      { status: 500 }
    )
  }
}

// 获取公告统计信息
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { announcementId } = body

    if (!announcementId) {
      return NextResponse.json(
        { error: '公告ID不能为空' },
        { status: 400 }
      )
    }

    // 增加特定公告的查看次数
    await prisma.announcement.update({
      where: { id: announcementId },
      data: {
        viewCount: { increment: 1 }
      }
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('更新公告查看次数失败:', error)
    return NextResponse.json(
      { error: '更新失败' },
      { status: 500 }
    )
  }
}
