import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取订单聊天消息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('orderId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')

    if (!orderId) {
      return NextResponse.json(
        { error: '订单ID为必填项' },
        { status: 400 }
      )
    }

    // 验证用户是否有权限查看此订单的消息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        id: true,
        buyerId: true,
        sellerId: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单的消息' },
        { status: 403 }
      )
    }

    const skip = (page - 1) * limit

    // 获取消息列表
    const messages = await prisma.message.findMany({
      where: {
        orderId
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        receiver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc' // 按时间正序排列
      },
      skip,
      take: limit
    })

    // 标记消息为已读（当前用户接收的消息）
    await prisma.message.updateMany({
      where: {
        orderId,
        receiverId: session.user.id,
        status: {
          in: ['SENT', 'DELIVERED']
        }
      },
      data: {
        status: 'READ'
      }
    })

    // 获取总消息数
    const total = await prisma.message.count({
      where: { orderId }
    })

    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get messages error:', error)
    return NextResponse.json(
      { error: '获取消息失败' },
      { status: 500 }
    )
  }
}

// 发送消息
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      orderId,
      content,
      messageType = 'TEXT',
      fileUrl,
      fileName,
      fileSize,
      fileMimeType,
      fileMetadata
    } = body

    // 验证必填字段
    if (!orderId) {
      return NextResponse.json(
        { error: '订单ID为必填项' },
        { status: 400 }
      )
    }

    // 对于文本消息，内容是必填的
    if (messageType === 'TEXT' && !content) {
      return NextResponse.json(
        { error: '消息内容为必填项' },
        { status: 400 }
      )
    }

    // 对于文件消息，文件URL是必填的
    if (['IMAGE', 'VIDEO', 'FILE'].includes(messageType) && !fileUrl) {
      return NextResponse.json(
        { error: '文件URL为必填项' },
        { status: 400 }
      )
    }

    // 验证消息内容长度（如果有内容的话）
    if (content && content.length > 1000) {
      return NextResponse.json(
        { error: '消息内容不能超过1000个字符' },
        { status: 400 }
      )
    }

    // 验证订单存在且用户有权限
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        id: true,
        buyerId: true,
        sellerId: true,
        status: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限在此订单中发送消息' },
        { status: 403 }
      )
    }

    // 确定接收者
    const receiverId = order.buyerId === session.user.id ? order.sellerId : order.buyerId

    // 创建消息
    const message = await prisma.message.create({
      data: {
        orderId,
        content: content ? content.trim() : '',
        messageType,
        senderId: session.user.id,
        receiverId,
        status: 'SENT',
        fileUrl,
        fileName,
        fileSize,
        fileMimeType,
        fileMetadata
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        receiver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json(message, { status: 201 })

  } catch (error) {
    console.error('Send message error:', error)
    return NextResponse.json(
      { error: '发送消息失败' },
      { status: 500 }
    )
  }
}
