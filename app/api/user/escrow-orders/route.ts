import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的托管订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const role = searchParams.get('role') // buyer, seller, mediator
    const skip = (page - 1) * limit

    let where: any = {}

    // 根据角色筛选
    if (role === 'buyer') {
      where.buyerId = session.user.id
    } else if (role === 'seller') {
      where.sellerId = session.user.id
    } else if (role === 'mediator') {
      where.mediatorId = session.user.id
    } else {
      // 默认显示用户参与的所有托管订单
      where.OR = [
        { buyerId: session.user.id },
        { sellerId: session.user.id },
        { mediatorId: session.user.id }
      ]
    }

    if (status) {
      where.status = status
    }

    const [orders, total] = await Promise.all([
      prisma.escrowOrder.findMany({
        where,
        include: {
          order: {
            include: {
              product: {
                select: {
                  title: true,
                  images: true,
                  price: true
                }
              }
            }
          },
          buyer: {
            select: { id: true, name: true, email: true }
          },
          seller: {
            select: { id: true, name: true, email: true }
          },
          mediator: {
            select: { id: true, name: true, email: true, mediatorReputation: true }
          },
          disputes: {
            select: {
              id: true,
              status: true,
              reason: true,
              createdAt: true
            }
          },
          chatRoom: {
            select: {
              id: true,
              roomCode: true,
              isActive: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.escrowOrder.count({ where })
    ])

    // 为每个订单添加用户角色信息
    const ordersWithRole = orders.map(order => {
      let userRole = 'observer'
      if (order.buyerId === session.user.id) userRole = 'buyer'
      else if (order.sellerId === session.user.id) userRole = 'seller'
      else if (order.mediatorId === session.user.id) userRole = 'mediator'

      return {
        ...order,
        userRole
      }
    })

    // 统计信息
    const stats = await prisma.escrowOrder.aggregate({
      where: {
        OR: [
          { buyerId: session.user.id },
          { sellerId: session.user.id },
          { mediatorId: session.user.id }
        ]
      },
      _count: {
        _all: true
      },
      _sum: {
        amount: true
      }
    })

    const statusStats = await prisma.escrowOrder.groupBy({
      by: ['status'],
      where: {
        OR: [
          { buyerId: session.user.id },
          { sellerId: session.user.id },
          { mediatorId: session.user.id }
        ]
      },
      _count: {
        status: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        orders: ordersWithRole,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        statistics: {
          totalOrders: stats._count._all,
          totalAmount: stats._sum.amount || 0,
          statusBreakdown: statusStats.reduce((acc, item) => {
            acc[item.status] = item._count.status
            return acc
          }, {} as Record<string, number>)
        }
      }
    })

  } catch (error) {
    console.error('获取用户托管订单失败:', error)
    return NextResponse.json(
      { error: '获取托管订单失败' },
      { status: 500 }
    )
  }
}
