import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 从收藏夹移除商品
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { productId } = await params

    // 查找收藏记录
    const favorite = await prisma.favorite.findUnique({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId: productId
        }
      }
    })

    if (!favorite) {
      return NextResponse.json(
        { error: '收藏记录不存在' },
        { status: 404 }
      )
    }

    // 删除收藏记录
    await prisma.favorite.delete({
      where: {
        id: favorite.id
      }
    })

    return NextResponse.json({
      message: '取消收藏成功'
    })

  } catch (error) {
    console.error('Remove favorite error:', error)
    return NextResponse.json(
      { error: '取消收藏失败' },
      { status: 500 }
    )
  }
}
