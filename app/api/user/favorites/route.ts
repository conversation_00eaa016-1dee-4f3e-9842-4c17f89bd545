import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户收藏列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // 获取收藏列表
    const favorites = await prisma.favorite.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        product: {
          include: {
            seller: {
              select: {
                id: true,
                name: true,
                userId: true,
                creditScore: true,
                city: true,
                district: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    })

    // 获取总数
    const total = await prisma.favorite.count({
      where: {
        userId: session.user.id
      }
    })

    return NextResponse.json({
      favorites,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get favorites error:', error)
    return NextResponse.json(
      { error: '获取收藏列表失败' },
      { status: 500 }
    )
  }
}

// 添加商品到收藏夹
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { productId } = body

    if (!productId) {
      return NextResponse.json(
        { error: '商品ID不能为空' },
        { status: 400 }
      )
    }

    // 检查商品是否存在
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    // 检查是否已经收藏
    const existingFavorite = await prisma.favorite.findUnique({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId: productId
        }
      }
    })

    if (existingFavorite) {
      return NextResponse.json(
        { error: '商品已在收藏夹中' },
        { status: 400 }
      )
    }

    // 不能收藏自己的商品
    if (product.sellerId === session.user.id) {
      return NextResponse.json(
        { error: '不能收藏自己的商品' },
        { status: 400 }
      )
    }

    // 创建收藏记录
    const favorite = await prisma.favorite.create({
      data: {
        userId: session.user.id,
        productId: productId
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true
          }
        }
      }
    })

    return NextResponse.json({
      message: '收藏成功',
      favorite
    })

  } catch (error) {
    console.error('Add favorite error:', error)
    return NextResponse.json(
      { error: '收藏失败' },
      { status: 500 }
    )
  }
}
