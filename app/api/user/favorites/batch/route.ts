import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 批量删除收藏
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { productIds, deleteAll } = body

    if (deleteAll) {
      // 清空收藏夹
      const result = await prisma.favorite.deleteMany({
        where: {
          userId: session.user.id
        }
      })

      return NextResponse.json({
        message: '收藏夹已清空',
        deletedCount: result.count
      })
    } else if (productIds && Array.isArray(productIds)) {
      // 批量删除指定商品
      const result = await prisma.favorite.deleteMany({
        where: {
          userId: session.user.id,
          productId: {
            in: productIds
          }
        }
      })

      return NextResponse.json({
        message: '批量删除成功',
        deletedCount: result.count
      })
    } else {
      return NextResponse.json(
        { error: '请提供要删除的商品ID或设置deleteAll为true' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Batch delete favorites error:', error)
    return NextResponse.json(
      { error: '批量删除失败' },
      { status: 500 }
    )
  }
}

// 检查商品是否已收藏
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { productIds } = body

    if (!productIds || !Array.isArray(productIds)) {
      return NextResponse.json(
        { error: '请提供商品ID数组' },
        { status: 400 }
      )
    }

    // 查询哪些商品已被收藏
    const favorites = await prisma.favorite.findMany({
      where: {
        userId: session.user.id,
        productId: {
          in: productIds
        }
      },
      select: {
        productId: true
      }
    })

    const favoriteProductIds = favorites.map(f => f.productId)

    return NextResponse.json({
      favoriteProductIds
    })

  } catch (error) {
    console.error('Check favorites error:', error)
    return NextResponse.json(
      { error: '检查收藏状态失败' },
      { status: 500 }
    )
  }
}
