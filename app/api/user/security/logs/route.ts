import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户安全日志
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const action = searchParams.get('action') // 过滤操作类型
    const status = searchParams.get('status') // 过滤状态
    const skip = (page - 1) * limit

    // 构建查询条件
    const whereCondition: any = {
      userId: session.user.id
    }

    if (action) {
      whereCondition.action = action
    }

    if (status) {
      whereCondition.status = status
    }

    // 获取安全日志
    const securityLogs = await prisma.securityLog.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    })

    // 获取总数
    const total = await prisma.securityLog.count({
      where: whereCondition
    })

    // 获取统计信息
    const stats = await getSecurityStats(session.user.id)

    return NextResponse.json({
      logs: securityLogs,
      stats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get security logs error:', error)
    return NextResponse.json(
      { error: '获取安全日志失败' },
      { status: 500 }
    )
  }
}

// 获取安全统计信息
async function getSecurityStats(userId: string) {
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

  const [
    totalLogs,
    recentLogs,
    loginAttempts,
    failedLogins,
    passwordChanges,
    emailChanges
  ] = await Promise.all([
    // 总日志数
    prisma.securityLog.count({
      where: { userId }
    }),
    
    // 最近30天日志数
    prisma.securityLog.count({
      where: {
        userId,
        createdAt: { gte: thirtyDaysAgo }
      }
    }),
    
    // 登录尝试次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'LOGIN',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),
    
    // 失败登录次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'LOGIN',
        status: 'FAILED',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),
    
    // 密码修改次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'PASSWORD_CHANGE',
        status: 'SUCCESS',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),
    
    // 邮箱修改次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'EMAIL_CHANGE',
        status: 'SUCCESS',
        createdAt: { gte: thirtyDaysAgo }
      }
    })
  ])

  // 获取最近的登录IP地址
  const recentIPs = await prisma.securityLog.findMany({
    where: {
      userId,
      action: 'LOGIN',
      status: 'SUCCESS',
      ipAddress: { not: null }
    },
    select: {
      ipAddress: true,
      createdAt: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 5
  })

  // 去重IP地址
  const uniqueIPs = Array.from(
    new Set(recentIPs.map(log => log.ipAddress))
  ).slice(0, 5)

  return {
    totalLogs,
    recentLogs,
    loginAttempts,
    failedLogins,
    passwordChanges,
    emailChanges,
    recentIPs: uniqueIPs,
    loginSuccessRate: loginAttempts > 0 ? 
      Math.round(((loginAttempts - failedLogins) / loginAttempts) * 100) : 100
  }
}
