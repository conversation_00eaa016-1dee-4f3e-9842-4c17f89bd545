import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { recordSecurityLog } from '@/lib/security'

// 修改密码
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { currentPassword, newPassword } = body

    // 验证输入
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: '当前密码和新密码不能为空' },
        { status: 400 }
      )
    }

    // 验证新密码强度
    if (!isPasswordStrong(newPassword)) {
      return NextResponse.json(
        { error: '新密码必须至少8位，包含大小写字母、数字' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, password: true, email: true }
    })

    if (!user || !user.password) {
      return NextResponse.json(
        { error: '用户不存在或密码未设置' },
        { status: 404 }
      )
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
    if (!isCurrentPasswordValid) {
      // 记录失败的密码修改尝试
      await recordSecurityLog(
        session.user.id,
        'PASSWORD_CHANGE',
        '密码修改失败：当前密码错误',
        'FAILED',
        request
      )

      return NextResponse.json(
        { error: '当前密码错误' },
        { status: 400 }
      )
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await bcrypt.compare(newPassword, user.password)
    if (isSamePassword) {
      return NextResponse.json(
        { error: '新密码不能与当前密码相同' },
        { status: 400 }
      )
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 12)

    // 更新密码
    await prisma.user.update({
      where: { id: session.user.id },
      data: { password: hashedNewPassword }
    })

    // 记录成功的密码修改
    await recordSecurityLog(
      session.user.id,
      'PASSWORD_CHANGE',
      '密码修改成功',
      'SUCCESS',
      request
    )

    return NextResponse.json({
      message: '密码修改成功'
    })

  } catch (error) {
    console.error('Change password error:', error)
    return NextResponse.json(
      { error: '密码修改失败' },
      { status: 500 }
    )
  }
}

// 密码强度验证函数
function isPasswordStrong(password: string): boolean {
  // 至少8位，包含大小写字母、数字
  const minLength = password.length >= 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  
  return minLength && hasUpperCase && hasLowerCase && hasNumbers
}
