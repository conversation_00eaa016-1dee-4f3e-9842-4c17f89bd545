import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { recordSecurityLog } from '@/lib/security'

// 更换邮箱
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { newEmail, password } = body

    // 验证输入
    if (!newEmail || !password) {
      return NextResponse.json(
        { error: '新邮箱和密码不能为空' },
        { status: 400 }
      )
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(newEmail)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, email: true, password: true }
    })

    if (!user || !user.password) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      // 记录失败的邮箱修改尝试
      await recordSecurityLog(
        session.user.id,
        'EMAIL_CHANGE',
        `邮箱修改失败：密码错误，尝试修改为 ${newEmail}`,
        'FAILED',
        request
      )

      return NextResponse.json(
        { error: '密码错误' },
        { status: 400 }
      )
    }

    // 检查新邮箱是否与当前邮箱相同
    if (newEmail === user.email) {
      return NextResponse.json(
        { error: '新邮箱不能与当前邮箱相同' },
        { status: 400 }
      )
    }

    // 检查新邮箱是否已被其他用户使用
    const existingUser = await prisma.user.findUnique({
      where: { email: newEmail }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被其他用户使用' },
        { status: 400 }
      )
    }

    // 更新邮箱
    await prisma.user.update({
      where: { id: session.user.id },
      data: { 
        email: newEmail,
        emailVerified: null // 重置邮箱验证状态
      }
    })

    // 记录成功的邮箱修改
    await recordSecurityLog(
      session.user.id,
      'EMAIL_CHANGE',
      `邮箱修改成功：从 ${user.email} 修改为 ${newEmail}`,
      'SUCCESS',
      request
    )

    return NextResponse.json({
      message: '邮箱修改成功',
      newEmail
    })

  } catch (error) {
    console.error('Change email error:', error)
    return NextResponse.json(
      { error: '邮箱修改失败' },
      { status: 500 }
    )
  }
}
