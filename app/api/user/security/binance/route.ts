import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { recordSecurityLog } from '@/lib/security'

// 管理币安UID绑定
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { binanceUid, password, action } = body

    // 验证输入
    if (!password || !action) {
      return NextResponse.json(
        { error: '密码和操作类型不能为空' },
        { status: 400 }
      )
    }

    if (!['bind', 'unbind', 'update'].includes(action)) {
      return NextResponse.json(
        { error: '无效的操作类型' },
        { status: 400 }
      )
    }

    if ((action === 'bind' || action === 'update') && !binanceUid) {
      return NextResponse.json(
        { error: '币安UID不能为空' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, binanceUid: true, password: true }
    })

    if (!user || !user.password) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      // 记录失败的币安UID操作尝试
      await recordSecurityLog(
        session.user.id,
        'BINANCE_BIND',
        `币安UID${action}失败：密码错误`,
        'FAILED',
        request
      )

      return NextResponse.json(
        { error: '密码错误' },
        { status: 400 }
      )
    }

    let updateData: { binanceUid: string | null } = { binanceUid: null }
    let logMessage = ''

    switch (action) {
      case 'bind':
        // 检查币安UID是否已被其他用户使用
        if (binanceUid) {
          const existingUser = await prisma.user.findFirst({
            where: {
              binanceUid,
              id: { not: session.user.id }
            }
          })

          if (existingUser) {
            return NextResponse.json(
              { error: '该币安UID已被其他用户绑定' },
              { status: 400 }
            )
          }

          if (user.binanceUid === binanceUid) {
            return NextResponse.json(
              { error: '该币安UID已经绑定到您的账户' },
              { status: 400 }
            )
          }
        }

        updateData = { binanceUid }
        logMessage = `币安UID绑定成功：${binanceUid}`
        break

      case 'unbind':
        if (!user.binanceUid) {
          return NextResponse.json(
            { error: '您还没有绑定币安UID' },
            { status: 400 }
          )
        }

        updateData = { binanceUid: null }
        logMessage = `币安UID解绑成功：${user.binanceUid}`
        break

      case 'update':
        if (!user.binanceUid) {
          return NextResponse.json(
            { error: '您还没有绑定币安UID，请先绑定' },
            { status: 400 }
          )
        }

        // 检查新的币安UID是否已被其他用户使用
        if (binanceUid) {
          const existingUser = await prisma.user.findFirst({
            where: {
              binanceUid,
              id: { not: session.user.id }
            }
          })

          if (existingUser) {
            return NextResponse.json(
              { error: '该币安UID已被其他用户绑定' },
              { status: 400 }
            )
          }

          if (user.binanceUid === binanceUid) {
            return NextResponse.json(
              { error: '新的币安UID与当前绑定的相同' },
              { status: 400 }
            )
          }
        }

        updateData = { binanceUid }
        logMessage = `币安UID更新成功：从 ${user.binanceUid} 更新为 ${binanceUid}`
        break
    }

    // 更新币安UID
    await prisma.user.update({
      where: { id: session.user.id },
      data: updateData
    })

    // 记录成功的币安UID操作
    await recordSecurityLog(
      session.user.id,
      'BINANCE_BIND',
      logMessage,
      'SUCCESS',
      request
    )

    return NextResponse.json({
      message: `币安UID${action === 'bind' ? '绑定' : action === 'unbind' ? '解绑' : '更新'}成功`,
      binanceUid: updateData.binanceUid
    })

  } catch (error) {
    console.error('Manage Binance UID error:', error)
    return NextResponse.json(
      { error: '币安UID操作失败' },
      { status: 500 }
    )
  }
}
