import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { calculateSecurityLevel, generateSecurityRecommendations } from '@/lib/security'
import { cache } from '@/lib/cache-fallback'

// 获取用户安全概览
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 检查缓存
    const cacheKey = `security_overview:${session.user.id}`
    const cachedData = await cache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json(cachedData)
    }

    // 获取用户基本信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        binanceUid: true,
        emailVerified: true,
        password: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取安全统计数据
    const stats = await getSecurityStats(session.user.id)

    // 计算安全等级
    const securityLevel = calculateSecurityLevel(user, stats)

    // 生成安全建议
    const recommendations = generateSecurityRecommendations(user, stats)

    // 返回数据（不包含敏感信息）
    const responseData = {
      user: {
        id: user.id,
        email: user.email,
        binanceUid: user.binanceUid,
        emailVerified: !!user.emailVerified
      },
      securityLevel,
      stats,
      recommendations
    }

    // 缓存响应数据（60秒）
    await cache.set(cacheKey, responseData, 60)

    return NextResponse.json(responseData)

  } catch (error) {
    console.error('Get security overview error:', error)
    return NextResponse.json(
      { error: '获取安全概览失败' },
      { status: 500 }
    )
  }
}

// 获取安全统计信息
async function getSecurityStats(userId: string) {
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

  const [
    totalLogs,
    recentLogs,
    loginAttempts,
    successfulLogins,
    failedLogins,
    passwordChanges,
    emailChanges
  ] = await Promise.all([
    // 总日志数
    prisma.securityLog.count({
      where: { userId }
    }),

    // 最近30天日志数
    prisma.securityLog.count({
      where: {
        userId,
        createdAt: { gte: thirtyDaysAgo }
      }
    }),

    // 登录尝试次数（包括成功和失败）
    prisma.securityLog.count({
      where: {
        userId,
        action: 'LOGIN',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),

    // 成功登录次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'LOGIN',
        status: 'SUCCESS',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),

    // 失败登录次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'LOGIN',
        status: 'FAILED',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),

    // 密码修改次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'PASSWORD_CHANGE',
        status: 'SUCCESS',
        createdAt: { gte: thirtyDaysAgo }
      }
    }),

    // 邮箱修改次数
    prisma.securityLog.count({
      where: {
        userId,
        action: 'EMAIL_CHANGE',
        status: 'SUCCESS',
        createdAt: { gte: thirtyDaysAgo }
      }
    })
  ])

  // 获取最近的登录IP地址
  const recentIPs = await prisma.securityLog.findMany({
    where: {
      userId,
      action: 'LOGIN',
      status: 'SUCCESS',
      ipAddress: { not: null },
      createdAt: { gte: thirtyDaysAgo }
    },
    select: {
      ipAddress: true,
      createdAt: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 10
  })

  // 去重IP地址
  const uniqueIPs = Array.from(
    new Set(recentIPs.map(log => log.ipAddress))
  ).slice(0, 5)

  return {
    totalLogs,
    recentLogs,
    loginAttempts,
    successfulLogins,
    failedLogins,
    passwordChanges,
    emailChanges,
    recentIPs: uniqueIPs,
    loginSuccessRate: loginAttempts > 0 ?
      Math.round((successfulLogins / loginAttempts) * 100) : 100
  }
}
