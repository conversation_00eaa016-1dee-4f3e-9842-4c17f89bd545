import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordSecurityLog } from '@/lib/security'

// 删除信任设备
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { ipAddress, deviceInfo } = body

    if (!ipAddress) {
      return NextResponse.json(
        { error: '缺少IP地址参数' },
        { status: 400 }
      )
    }

    // 查找并删除该IP地址相关的所有活跃会话
    const sessionsToDelete = await prisma.userSession.findMany({
      where: {
        userId: session.user.id,
        ipAddress: ipAddress,
        isActive: true
      },
      select: {
        id: true,
        deviceName: true,
        ipAddress: true,
        createdAt: true
      }
    })

    if (sessionsToDelete.length === 0) {
      return NextResponse.json(
        { error: '未找到该设备的活跃会话' },
        { status: 404 }
      )
    }

    // 批量删除会话（设置为非活跃状态）
    const result = await prisma.userSession.updateMany({
      where: {
        userId: session.user.id,
        ipAddress: ipAddress,
        isActive: true
      },
      data: {
        isActive: false
      }
    })

    // 记录删除信任设备的安全日志
    await recordSecurityLog(
      session.user.id,
      'DEVICE_REMOVAL',
      `删除信任设备：${deviceInfo || `IP ${ipAddress}`}，终止了 ${result.count} 个会话`,
      'SUCCESS',
      request
    )

    // 为每个被删除的会话记录单独的日志
    for (const sessionData of sessionsToDelete) {
      await recordSecurityLog(
        session.user.id,
        'LOGOUT',
        `会话被强制终止：${sessionData.deviceName} (${sessionData.ipAddress})`,
        'SUCCESS',
        request
      )
    }

    return NextResponse.json({
      message: '信任设备删除成功',
      deletedSessions: result.count,
      deletedSessionDetails: sessionsToDelete
    })

  } catch (error) {
    console.error('Delete trusted device error:', error)
    return NextResponse.json(
      { error: '删除信任设备失败' },
      { status: 500 }
    )
  }
}

// 获取信任设备列表
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 获取最近30天的成功登录记录，按IP地址分组
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const trustedDevices = await prisma.securityLog.findMany({
      where: {
        userId: session.user.id,
        action: 'LOGIN',
        status: 'SUCCESS',
        ipAddress: { not: null },
        createdAt: { gte: thirtyDaysAgo }
      },
      select: {
        id: true,
        ipAddress: true,
        description: true,
        createdAt: true,
        userAgent: true,
        location: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 按IP地址分组，获取每个IP的最新登录信息
    const deviceMap = new Map()
    
    trustedDevices.forEach(log => {
      if (!deviceMap.has(log.ipAddress)) {
        // 提取设备信息
        let deviceName = '未知设备'
        if (log.description && log.description.includes('设备：')) {
          deviceName = log.description.split('设备：')[1]
        }

        deviceMap.set(log.ipAddress, {
          ipAddress: log.ipAddress,
          deviceName,
          lastLogin: log.createdAt,
          location: log.location,
          userAgent: log.userAgent,
          loginCount: 1,
          logId: log.id
        })
      } else {
        // 增加登录次数
        const device = deviceMap.get(log.ipAddress)
        device.loginCount += 1
      }
    })

    // 检查每个设备是否有活跃会话
    const devicesWithSessions: any[] = []
    for (const [ipAddress, device] of deviceMap) {
      const activeSession = await prisma.userSession.findFirst({
        where: {
          userId: session.user.id,
          ipAddress: ipAddress,
          isActive: true
        },
        select: {
          id: true,
          lastActivity: true
        }
      })

      devicesWithSessions.push({
        ...device,
        hasActiveSession: !!activeSession,
        lastActivity: activeSession?.lastActivity || device.lastLogin
      })
    }

    return NextResponse.json({
      trustedDevices: devicesWithSessions,
      total: devicesWithSessions.length
    })

  } catch (error) {
    console.error('Get trusted devices error:', error)
    return NextResponse.json(
      { error: '获取信任设备列表失败' },
      { status: 500 }
    )
  }
}

// 批量删除信任设备
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, ipAddresses, excludeCurrentIP } = body

    if (action === 'deleteAll') {
      const whereCondition: any = {
        userId: session.user.id,
        isActive: true
      }

      // 如果需要排除当前IP
      if (excludeCurrentIP) {
        const currentIP = request.headers.get('x-forwarded-for') || 
                         request.headers.get('x-real-ip') || 
                         '127.0.0.1'
        
        whereCondition.ipAddress = {
          not: currentIP
        }
      }

      // 获取要删除的会话信息
      const sessionsToDelete = await prisma.userSession.findMany({
        where: whereCondition,
        select: {
          id: true,
          deviceName: true,
          ipAddress: true
        }
      })

      // 批量删除会话
      const result = await prisma.userSession.updateMany({
        where: whereCondition,
        data: {
          isActive: false
        }
      })

      // 记录批量删除日志
      await recordSecurityLog(
        session.user.id,
        'DEVICE_REMOVAL',
        `批量删除所有信任设备，终止了 ${result.count} 个会话`,
        'SUCCESS',
        request
      )

      return NextResponse.json({
        message: '批量删除信任设备成功',
        deletedSessions: result.count,
        deletedSessionDetails: sessionsToDelete
      })
    }

    if (action === 'deleteSelected' && ipAddresses && Array.isArray(ipAddresses)) {
      // 删除指定IP地址的设备
      const result = await prisma.userSession.updateMany({
        where: {
          userId: session.user.id,
          ipAddress: { in: ipAddresses },
          isActive: true
        },
        data: {
          isActive: false
        }
      })

      // 记录删除日志
      await recordSecurityLog(
        session.user.id,
        'DEVICE_REMOVAL',
        `批量删除选定的信任设备 (${ipAddresses.join(', ')})，终止了 ${result.count} 个会话`,
        'SUCCESS',
        request
      )

      return NextResponse.json({
        message: '批量删除选定设备成功',
        deletedSessions: result.count
      })
    }

    return NextResponse.json(
      { error: '无效的操作类型' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Batch delete trusted devices error:', error)
    return NextResponse.json(
      { error: '批量删除信任设备失败' },
      { status: 500 }
    )
  }
}
