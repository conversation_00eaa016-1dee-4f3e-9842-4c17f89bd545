import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordSecurityLog } from '@/lib/security'

// 批量会话操作
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, excludeCurrentSession } = body

    if (action === 'deleteAll') {
      // 获取当前会话ID（如果需要排除）
      const whereCondition: any = {
        userId: session.user.id,
        isActive: true
      }

      if (excludeCurrentSession) {
        // 这里我们假设当前会话是最新的活跃会话
        // 在实际应用中，你可能需要传递当前会话ID
        const currentSession = await prisma.userSession.findFirst({
          where: {
            userId: session.user.id,
            isActive: true
          },
          orderBy: {
            lastActivity: 'desc'
          }
        })

        if (currentSession) {
          whereCondition.id = {
            not: currentSession.id
          }
        }
      }

      // 获取要删除的会话信息（用于日志记录）
      const sessionsToDelete = await prisma.userSession.findMany({
        where: whereCondition,
        select: {
          id: true,
          deviceName: true,
          ipAddress: true
        }
      })

      // 批量删除会话
      const result = await prisma.userSession.updateMany({
        where: whereCondition,
        data: {
          isActive: false
        }
      })

      // 记录批量删除日志
      await recordSecurityLog(
        session.user.id,
        'LOGOUT',
        `批量删除 ${result.count} 个会话`,
        'SUCCESS',
        request
      )

      return NextResponse.json({
        message: '批量删除会话成功',
        deletedCount: result.count,
        deletedSessions: sessionsToDelete
      })
    }

    if (action === 'deleteExpired') {
      // 删除过期会话
      const now = new Date()
      
      const result = await prisma.userSession.updateMany({
        where: {
          userId: session.user.id,
          OR: [
            { expiresAt: { lt: now } },
            { isActive: false }
          ]
        },
        data: {
          isActive: false
        }
      })

      // 记录清理日志
      await recordSecurityLog(
        session.user.id,
        'LOGOUT',
        `清理 ${result.count} 个过期会话`,
        'SUCCESS',
        request
      )

      return NextResponse.json({
        message: '清理过期会话成功',
        deletedCount: result.count
      })
    }

    if (action === 'refreshCurrent') {
      // 刷新当前会话的活动时间
      const currentSession = await prisma.userSession.findFirst({
        where: {
          userId: session.user.id,
          isActive: true
        },
        orderBy: {
          lastActivity: 'desc'
        }
      })

      if (currentSession) {
        await prisma.userSession.update({
          where: { id: currentSession.id },
          data: {
            lastActivity: new Date()
          }
        })

        return NextResponse.json({
          message: '会话刷新成功',
          session: currentSession
        })
      }
    }

    return NextResponse.json(
      { error: '无效的操作类型' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Batch session operation error:', error)
    return NextResponse.json(
      { error: '批量操作失败' },
      { status: 500 }
    )
  }
}

// 获取会话统计信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const [
      totalSessions,
      activeSessions,
      expiredSessions,
      recentSessions,
      weeklyLogins
    ] = await Promise.all([
      // 总会话数
      prisma.userSession.count({
        where: { userId: session.user.id }
      }),
      
      // 活跃会话数
      prisma.userSession.count({
        where: {
          userId: session.user.id,
          isActive: true,
          expiresAt: { gt: now }
        }
      }),
      
      // 过期会话数
      prisma.userSession.count({
        where: {
          userId: session.user.id,
          OR: [
            { expiresAt: { lt: now } },
            { isActive: false }
          ]
        }
      }),
      
      // 最近24小时会话数
      prisma.userSession.count({
        where: {
          userId: session.user.id,
          createdAt: { gte: oneDayAgo }
        }
      }),
      
      // 最近一周登录次数
      prisma.securityLog.count({
        where: {
          userId: session.user.id,
          action: 'LOGIN',
          status: 'SUCCESS',
          createdAt: { gte: oneWeekAgo }
        }
      })
    ])

    // 获取设备类型统计
    const deviceStats = await prisma.userSession.groupBy({
      by: ['deviceType'],
      where: {
        userId: session.user.id,
        isActive: true,
        expiresAt: { gt: now }
      },
      _count: {
        deviceType: true
      }
    })

    // 获取最近登录的IP地址
    const recentIPs = await prisma.userSession.findMany({
      where: {
        userId: session.user.id,
        isActive: true
      },
      select: {
        ipAddress: true,
        location: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    const uniqueIPs = Array.from(
      new Map(recentIPs.map(session => [session.ipAddress, session])).values()
    ).slice(0, 5)

    return NextResponse.json({
      statistics: {
        totalSessions,
        activeSessions,
        expiredSessions,
        recentSessions,
        weeklyLogins
      },
      deviceStats,
      recentIPs: uniqueIPs
    })

  } catch (error) {
    console.error('Get session statistics error:', error)
    return NextResponse.json(
      { error: '获取会话统计失败' },
      { status: 500 }
    )
  }
}
