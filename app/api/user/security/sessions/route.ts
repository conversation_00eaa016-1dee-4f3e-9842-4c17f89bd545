import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordSecurityLog } from '@/lib/security'

// 获取用户活跃会话
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // 获取活跃会话
    const sessions = await prisma.userSession.findMany({
      where: {
        userId: session.user.id,
        isActive: true,
        expiresAt: {
          gt: new Date() // 未过期
        }
      },
      orderBy: {
        lastActivity: 'desc'
      },
      skip,
      take: limit
    })

    // 获取总数
    const total = await prisma.userSession.count({
      where: {
        userId: session.user.id,
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    return NextResponse.json({
      sessions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get sessions error:', error)
    return NextResponse.json(
      { error: '获取会话列表失败' },
      { status: 500 }
    )
  }
}

// 创建新会话（登录时调用）
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { sessionId, deviceInfo } = body

    if (!sessionId) {
      return NextResponse.json(
        { error: '会话ID不能为空' },
        { status: 400 }
      )
    }

    // 获取请求信息
    const ipAddress = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || ''

    // 解析设备信息
    const deviceData = parseDeviceInfo(userAgent, deviceInfo)

    // 创建会话记录
    const userSession = await prisma.userSession.create({
      data: {
        userId: session.user.id,
        sessionId,
        deviceName: deviceData.deviceName,
        deviceType: deviceData.deviceType,
        browser: deviceData.browser,
        os: deviceData.os,
        ipAddress,
        location: deviceData.location,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
      }
    })

    // 记录登录日志
    await recordSecurityLog(
      session.user.id,
      'LOGIN',
      `用户登录成功，设备：${deviceData.deviceName}`,
      'SUCCESS',
      request
    )

    return NextResponse.json({
      message: '会话创建成功',
      session: userSession
    })

  } catch (error) {
    console.error('Create session error:', error)
    return NextResponse.json(
      { error: '创建会话失败' },
      { status: 500 }
    )
  }
}

// 获取客户端IP地址
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

// 解析设备信息
function parseDeviceInfo(userAgent: string, deviceInfo?: any) {
  const ua = userAgent.toLowerCase()
  
  // 解析浏览器
  let browser = 'Unknown'
  if (ua.includes('chrome')) browser = 'Chrome'
  else if (ua.includes('firefox')) browser = 'Firefox'
  else if (ua.includes('safari')) browser = 'Safari'
  else if (ua.includes('edge')) browser = 'Edge'
  
  // 解析操作系统
  let os = 'Unknown'
  if (ua.includes('windows')) os = 'Windows'
  else if (ua.includes('mac')) os = 'macOS'
  else if (ua.includes('linux')) os = 'Linux'
  else if (ua.includes('android')) os = 'Android'
  else if (ua.includes('ios')) os = 'iOS'
  
  // 解析设备类型
  let deviceType = 'desktop'
  if (ua.includes('mobile')) deviceType = 'mobile'
  else if (ua.includes('tablet')) deviceType = 'tablet'
  
  return {
    deviceName: deviceInfo?.deviceName || `${browser} on ${os}`,
    deviceType,
    browser,
    os,
    location: deviceInfo?.location || null
  }
}
