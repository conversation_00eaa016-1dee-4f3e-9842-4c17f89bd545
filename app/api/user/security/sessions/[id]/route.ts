import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordSecurityLog } from '@/lib/security'

// 删除指定会话
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 查找会话
    const userSession = await prisma.userSession.findUnique({
      where: { id }
    })

    if (!userSession) {
      return NextResponse.json(
        { error: '会话不存在' },
        { status: 404 }
      )
    }

    // 检查会话是否属于当前用户
    if (userSession.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限删除此会话' },
        { status: 403 }
      )
    }

    // 删除会话
    await prisma.userSession.update({
      where: { id },
      data: { isActive: false }
    })

    // 记录会话删除日志
    await recordSecurityLog(
      session.user.id,
      'LOGOUT',
      `手动删除会话：${userSession.deviceName} (${userSession.ipAddress})`,
      'SUCCESS',
      request
    )

    return NextResponse.json({
      message: '会话删除成功'
    })

  } catch (error) {
    console.error('Delete session error:', error)
    return NextResponse.json(
      { error: '删除会话失败' },
      { status: 500 }
    )
  }
}

// 批量删除会话
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    
    if (id === 'batch') {
      const body = await request.json()
      const { action, excludeCurrentSession } = body

      if (action === 'deleteAll') {
        // 获取当前会话ID（如果需要排除）
        const whereCondition: any = {
          userId: session.user.id,
          isActive: true
        }

        if (excludeCurrentSession) {
          // 这里需要从session中获取当前会话ID
          // 实际实现中可能需要在JWT中存储sessionId
          // 暂时跳过当前会话的排除逻辑
        }

        // 批量删除所有其他会话
        const result = await prisma.userSession.updateMany({
          where: whereCondition,
          data: { isActive: false }
        })

        // 记录批量删除日志
        await recordSecurityLog(
          session.user.id,
          'LOGOUT',
          `批量删除所有会话，共删除 ${result.count} 个会话`,
          'SUCCESS',
          request
        )

        return NextResponse.json({
          message: '批量删除会话成功',
          deletedCount: result.count
        })
      }
    }

    return NextResponse.json(
      { error: '无效的操作' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Batch delete sessions error:', error)
    return NextResponse.json(
      { error: '批量删除会话失败' },
      { status: 500 }
    )
  }
}
