import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { checkUserStatus } from '@/lib/user-status'

// 获取用户状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const status = await checkUserStatus(session.user.id)

    return NextResponse.json({
      success: true,
      status
    })

  } catch (error) {
    console.error('获取用户状态失败:', error)
    return NextResponse.json(
      { error: '获取用户状态失败' },
      { status: 500 }
    )
  }
}
