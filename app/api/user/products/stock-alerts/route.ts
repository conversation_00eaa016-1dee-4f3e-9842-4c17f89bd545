import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户商品的库存提醒
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取用户的低库存商品（库存 <= 5 且 > 0）
    const lowStockProducts = await prisma.product.findMany({
      where: {
        sellerId: user.id,
        stock: {
          lte: 5,
          gt: 0
        },
        status: 'AVAILABLE',
        isDemandGenerated: false
      },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        price: true,
        createdAt: true
      },
      orderBy: {
        stock: 'asc'
      }
    })

    // 获取用户的零库存商品
    const outOfStockProducts = await prisma.product.findMany({
      where: {
        sellerId: user.id,
        stock: {
          lte: 0
        },
        status: {
          in: ['AVAILABLE', 'SOLD_OUT']
        },
        isDemandGenerated: false
      },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        price: true,
        createdAt: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    // 获取库存统计
    const stockStats = await prisma.product.groupBy({
      by: ['status'],
      _count: {
        id: true
      },
      where: {
        sellerId: user.id,
        isDemandGenerated: false
      }
    })

    const stats = stockStats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.id
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      lowStock: lowStockProducts,
      outOfStock: outOfStockProducts,
      stats,
      alerts: {
        lowStockCount: lowStockProducts.length,
        outOfStockCount: outOfStockProducts.length,
        totalAlerts: lowStockProducts.length + outOfStockProducts.length
      }
    })

  } catch (error) {
    console.error('获取库存提醒失败:', error)
    return NextResponse.json(
      { 
        error: '获取库存提醒失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

// 批量更新用户商品库存
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { updates } = body // [{ productId: string, stock: number }]

    if (!Array.isArray(updates) || updates.length === 0) {
      return NextResponse.json(
        { error: '无效的更新数据' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const results: any[] = []

    // 逐个更新商品库存
    for (const update of updates) {
      try {
        const { productId, stock } = update

        if (typeof stock !== 'number' || stock < 0) {
          results.push({
            productId,
            success: false,
            error: '库存数量必须为非负数'
          })
          continue
        }

        // 验证商品所有权
        const product = await prisma.product.findFirst({
          where: {
            id: productId,
            sellerId: user.id
          },
          select: {
            id: true,
            title: true,
            stock: true,
            status: true
          }
        })

        if (!product) {
          results.push({
            productId,
            success: false,
            error: '商品不存在或无权限'
          })
          continue
        }

        // 更新库存并检查状态
        const newStatus = stock > 0 && product.status === 'SOLD_OUT' ? 'AVAILABLE' : 
                         stock <= 0 && product.status === 'AVAILABLE' ? 'SOLD_OUT' : 
                         product.status

        const updatedProduct = await prisma.product.update({
          where: { id: productId },
          data: {
            stock,
            status: newStatus
          },
          select: {
            id: true,
            title: true,
            stock: true,
            status: true
          }
        })

        results.push({
          productId,
          success: true,
          product: updatedProduct,
          statusChanged: newStatus !== product.status,
          message: newStatus !== product.status ? 
            (newStatus === 'AVAILABLE' ? '商品已重新上架' : '商品已自动下架') : 
            '库存已更新'
        })

      } catch (error) {
        results.push({
          productId: update.productId,
          success: false,
          error: error instanceof Error ? error.message : '更新失败'
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      message: `批量更新完成: ${successCount} 成功, ${failureCount} 失败`,
      results,
      summary: {
        total: updates.length,
        successful: successCount,
        failed: failureCount
      }
    })

  } catch (error) {
    console.error('批量更新库存失败:', error)
    return NextResponse.json(
      { 
        error: '批量更新库存失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
