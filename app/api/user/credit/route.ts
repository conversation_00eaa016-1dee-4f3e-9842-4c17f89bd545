import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户信用信息和历史记录
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // 获取用户基本信用信息
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id
      },
      select: {
        id: true,
        name: true,
        creditScore: true,
        createdAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取信用变动历史
    const creditHistories = await prisma.creditHistory.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    })

    // 获取历史记录总数
    const totalHistories = await prisma.creditHistory.count({
      where: {
        userId: session.user.id
      }
    })

    // 获取交易统计
    const [
      totalOrders,
      completedOrders,
      receivedReviews,
      averageRating
    ] = await Promise.all([
      // 总订单数（买家+卖家）
      prisma.order.count({
        where: {
          OR: [
            { buyerId: session.user.id },
            { sellerId: session.user.id }
          ]
        }
      }),
      
      // 已完成订单数
      prisma.order.count({
        where: {
          OR: [
            { buyerId: session.user.id },
            { sellerId: session.user.id }
          ],
          status: 'COMPLETED'
        }
      }),
      
      // 收到的评价
      prisma.review.findMany({
        where: {
          revieweeId: session.user.id
        },
        select: {
          rating: true
        }
      }),
      
      // 平均评分
      prisma.review.aggregate({
        where: {
          revieweeId: session.user.id
        },
        _avg: {
          rating: true
        }
      })
    ])

    // 计算好评率（4-5星为好评）
    const goodReviews = receivedReviews.filter(review => review.rating >= 4).length
    const goodReviewRate = receivedReviews.length > 0 ? (goodReviews / receivedReviews.length) * 100 : 0

    // 计算信用等级
    const creditLevel = getCreditLevel(user.creditScore)

    // 获取最近30天的信用变动趋势
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const recentChanges = await prisma.creditHistory.findMany({
      where: {
        userId: session.user.id,
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      orderBy: {
        createdAt: 'asc'
      },
      select: {
        changeScore: true,
        afterScore: true,
        createdAt: true,
        changeType: true
      }
    })

    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        creditScore: user.creditScore,
        creditLevel,
        memberSince: user.createdAt
      },
      statistics: {
        totalOrders,
        completedOrders,
        totalReviews: receivedReviews.length,
        averageRating: averageRating._avg.rating || 0,
        goodReviewRate: Math.round(goodReviewRate),
        completionRate: totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0
      },
      creditHistories,
      recentChanges,
      pagination: {
        page,
        limit,
        total: totalHistories,
        pages: Math.ceil(totalHistories / limit)
      }
    })

  } catch (error) {
    console.error('Get credit info error:', error)
    return NextResponse.json(
      { error: '获取信用信息失败' },
      { status: 500 }
    )
  }
}

// 信用等级计算函数
function getCreditLevel(score: number) {
  if (score >= 90) {
    return {
      level: 'EXCELLENT',
      name: '优秀',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      icon: '⭐',
      description: '信用优秀，值得信赖'
    }
  } else if (score >= 70) {
    return {
      level: 'GOOD',
      name: '良好',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      icon: '👍',
      description: '信用良好，交易可靠'
    }
  } else if (score >= 50) {
    return {
      level: 'FAIR',
      name: '一般',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      icon: '⚡',
      description: '信用一般，需要提升'
    }
  } else if (score >= 30) {
    return {
      level: 'POOR',
      name: '较差',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      icon: '⚠️',
      description: '信用较差，请注意改善'
    }
  } else {
    return {
      level: 'BAD',
      name: '很差',
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      icon: '❌',
      description: '信用很差，需要重建信任'
    }
  }
}
