import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户余额信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeHistory = searchParams.get('history') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // 获取用户基本信息和余额
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true,
        creditPoints: true,
        createdAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 计算冻结余额（从FundFreeze表）
    const frozenAmount = await prisma.fundFreeze.aggregate({
      where: {
        userId: session.user.id,
        status: 'ACTIVE'
      },
      _sum: { amount: true }
    })

    // 计算总收入和支出（从FundTransaction表）
    const [earnings, expenses] = await Promise.all([
      prisma.fundTransaction.aggregate({
        where: {
          userId: session.user.id,
          amount: { gt: 0 }
        },
        _sum: { amount: true }
      }),
      prisma.fundTransaction.aggregate({
        where: {
          userId: session.user.id,
          amount: { lt: 0 }
        },
        _sum: { amount: true }
      })
    ])

    const frozenBalance = frozenAmount._sum.amount || 0
    const totalEarnings = earnings._sum.amount || 0
    const totalSpent = Math.abs(expenses._sum.amount || 0)

    const result: any = {
      balance: {
        available: user.depositBalance,
        frozen: frozenBalance,
        total: user.depositBalance + frozenBalance,
        creditPoints: user.creditPoints || 0
      },
      statistics: {
        totalEarnings,
        totalSpent,
        netBalance: totalEarnings - totalSpent
      },
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        memberSince: user.createdAt
      }
    }

    // 如果需要包含交易历史
    if (includeHistory) {
      const skip = (page - 1) * limit

      // 获取资金交易记录
      const [transactions, totalTransactions] = await Promise.all([
        prisma.fundTransaction.findMany({
          where: { userId: session.user.id },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          select: {
            id: true,
            type: true,
            amount: true,
            description: true,
            relatedId: true,
            metadata: true,
            createdAt: true
          }
        }),
        prisma.fundTransaction.count({
          where: { userId: session.user.id }
        })
      ])

      // 获取冻结记录
      const freezeRecords = await prisma.fundFreeze.findMany({
        where: { 
          userId: session.user.id,
          status: 'ACTIVE'
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          amount: true,
          purpose: true,
          relatedId: true,
          relatedType: true,
          createdAt: true,
          status: true
        }
      })

      result.history = {
        transactions,
        freezeRecords,
        pagination: {
          page,
          limit,
          total: totalTransactions,
          pages: Math.ceil(totalTransactions / limit)
        }
      }
    }

    // 获取最近的余额变动统计
    const recentStats = await getRecentBalanceStats(session.user.id)
    result.recentActivity = recentStats

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('获取用户余额失败:', error)
    return NextResponse.json(
      { error: '获取余额信息失败' },
      { status: 500 }
    )
  }
}

/**
 * 获取最近的余额变动统计
 */
async function getRecentBalanceStats(userId: string) {
  try {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    // 获取最近30天和7天的统计
    const [
      monthlyIncome,
      monthlyExpense,
      weeklyIncome,
      weeklyExpense,
      recentTransactions
    ] = await Promise.all([
      // 30天收入
      prisma.fundTransaction.aggregate({
        where: {
          userId,
          amount: { gt: 0 },
          createdAt: { gte: thirtyDaysAgo }
        },
        _sum: { amount: true },
        _count: true
      }),
      // 30天支出
      prisma.fundTransaction.aggregate({
        where: {
          userId,
          amount: { lt: 0 },
          createdAt: { gte: thirtyDaysAgo }
        },
        _sum: { amount: true },
        _count: true
      }),
      // 7天收入
      prisma.fundTransaction.aggregate({
        where: {
          userId,
          amount: { gt: 0 },
          createdAt: { gte: sevenDaysAgo }
        },
        _sum: { amount: true },
        _count: true
      }),
      // 7天支出
      prisma.fundTransaction.aggregate({
        where: {
          userId,
          amount: { lt: 0 },
          createdAt: { gte: sevenDaysAgo }
        },
        _sum: { amount: true },
        _count: true
      }),
      // 最近5笔交易
      prisma.fundTransaction.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          type: true,
          amount: true,
          description: true,
          createdAt: true
        }
      })
    ])

    return {
      monthly: {
        income: monthlyIncome._sum.amount || 0,
        expense: Math.abs(monthlyExpense._sum.amount || 0),
        net: (monthlyIncome._sum.amount || 0) + (monthlyExpense._sum.amount || 0),
        transactionCount: monthlyIncome._count + monthlyExpense._count
      },
      weekly: {
        income: weeklyIncome._sum.amount || 0,
        expense: Math.abs(weeklyExpense._sum.amount || 0),
        net: (weeklyIncome._sum.amount || 0) + (weeklyExpense._sum.amount || 0),
        transactionCount: weeklyIncome._count + weeklyExpense._count
      },
      recentTransactions
    }
  } catch (error) {
    console.error('获取余额统计失败:', error)
    return {
      monthly: { income: 0, expense: 0, net: 0, transactionCount: 0 },
      weekly: { income: 0, expense: 0, net: 0, transactionCount: 0 },
      recentTransactions: []
    }
  }
}
