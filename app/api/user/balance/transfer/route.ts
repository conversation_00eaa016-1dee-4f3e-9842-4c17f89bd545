import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 用户间转账
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      recipientEmail, 
      recipientId, 
      amount, 
      description, 
      password 
    } = body

    // 验证必要字段
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '转账金额必须大于0' },
        { status: 400 }
      )
    }

    if (!recipientEmail && !recipientId) {
      return NextResponse.json(
        { error: '请提供收款人邮箱或ID' },
        { status: 400 }
      )
    }

    // 获取发送方用户信息
    const sender = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true,
        password: true
      }
    })

    if (!sender) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 验证余额
    if (sender.depositBalance < amount) {
      return NextResponse.json(
        { error: '余额不足' },
        { status: 400 }
      )
    }

    // 验证密码（简化版，实际应该使用bcrypt）
    if (password && sender.password !== password) {
      return NextResponse.json(
        { error: '密码错误' },
        { status: 400 }
      )
    }

    // 查找收款人
    const whereClause = recipientId 
      ? { id: recipientId }
      : { email: recipientEmail }

    const recipient = await prisma.user.findUnique({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true
      }
    })

    if (!recipient) {
      return NextResponse.json(
        { error: '收款人不存在' },
        { status: 404 }
      )
    }

    if (recipient.id === sender.id) {
      return NextResponse.json(
        { error: '不能向自己转账' },
        { status: 400 }
      )
    }

    // 执行转账事务
    const result = await prisma.$transaction(async (tx) => {
      // 扣除发送方余额
      const updatedSender = await tx.user.update({
        where: { id: sender.id },
        data: {
          depositBalance: {
            decrement: amount
          }
        }
      })

      // 增加接收方余额
      const updatedRecipient = await tx.user.update({
        where: { id: recipient.id },
        data: {
          depositBalance: {
            increment: amount
          }
        }
      })

      // 创建发送方交易记录
      const senderTransaction = await tx.fundTransaction.create({
        data: {
          userId: sender.id,
          type: 'TRANSFER_OUT',
          amount: -amount,
          description: description || `转账给 ${recipient.name || recipient.email}`,
          relatedId: recipient.id,
          metadata: {
            relatedType: 'USER_TRANSFER',
            recipientId: recipient.id,
            recipientName: recipient.name,
            recipientEmail: recipient.email
          }
        }
      })

      // 创建接收方交易记录
      const recipientTransaction = await tx.fundTransaction.create({
        data: {
          userId: recipient.id,
          type: 'TRANSFER_IN',
          amount: amount,
          description: description || `收到来自 ${sender.name || sender.email} 的转账`,
          relatedId: sender.id,
          metadata: {
            relatedType: 'USER_TRANSFER',
            senderId: sender.id,
            senderName: sender.name,
            senderEmail: sender.email
          }
        }
      })

      return {
        senderBalance: updatedSender.depositBalance,
        recipientBalance: updatedRecipient.depositBalance,
        senderTransaction,
        recipientTransaction
      }
    })

    // 发送通知（如果通知系统可用）
    try {
      await Promise.all([
        // 通知发送方
        prisma.notification.create({
          data: {
            userId: sender.id,
            type: 'PAYMENT',
            title: '转账成功',
            message: `您已成功向 ${recipient.name || recipient.email} 转账 ${amount} USDT`,
            data: {
              amount,
              recipientName: recipient.name,
              recipientEmail: recipient.email,
              transactionId: result.senderTransaction.id
            },
            priority: 'NORMAL'
          }
        }),
        // 通知接收方
        prisma.notification.create({
          data: {
            userId: recipient.id,
            type: 'PAYMENT',
            title: '收到转账',
            message: `您收到来自 ${sender.name || sender.email} 的转账 ${amount} USDT`,
            data: {
              amount,
              senderName: sender.name,
              senderEmail: sender.email,
              transactionId: result.recipientTransaction.id
            },
            priority: 'HIGH'
          }
        })
      ])
    } catch (notificationError) {
      console.log('发送转账通知失败:', notificationError)
    }

    return NextResponse.json({
      success: true,
      message: '转账成功',
      data: {
        amount,
        sender: {
          id: sender.id,
          name: sender.name,
          email: sender.email,
          newBalance: result.senderBalance
        },
        recipient: {
          id: recipient.id,
          name: recipient.name,
          email: recipient.email,
          newBalance: result.recipientBalance
        },
        transactionId: result.senderTransaction.id,
        timestamp: new Date()
      }
    })

  } catch (error) {
    console.error('转账失败:', error)
    return NextResponse.json(
      { error: '转账失败，请稍后重试' },
      { status: 500 }
    )
  }
}
