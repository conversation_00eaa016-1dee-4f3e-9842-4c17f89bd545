/**
 * 用户信用等级管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  updateUserCreditLevel, 
  getUserBenefits, 
  LEVEL_CONFIG,
  CreditLevel 
} from '@/lib/credit-level'
import { getWithdrawalFeeEstimate, getOptimalWithdrawalTime } from '@/lib/adapters/withdrawal-adapter'
import { getUserPendingSettlement } from '@/lib/batch-settlement'
import { prisma } from '@/lib/prisma'

// 获取用户信用等级信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 更新用户信用等级
    const levelUpdate = await updateUserCreditLevel(userId)

    // 获取用户详细信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true,
        creditLevel: true,
        creditPoints: true,
        lastCreditUpdate: true,
        minWithdrawalAmount: true,
        autoWithdrawal: true,
        withdrawalThreshold: true,
        createdAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取等级配置和权益
    const levelConfig = LEVEL_CONFIG[user.creditLevel as CreditLevel]
    const benefits = getUserBenefits(user.creditLevel as CreditLevel)

    // 计算下一等级信息
    const nextLevel = getNextLevel(user.creditLevel as CreditLevel)
    let nextLevelInfo: {
      level: CreditLevel;
      config: typeof LEVEL_CONFIG[CreditLevel];
      progress: {
        deposit: number;
        points: number;
        overall: number;
      };
      requirements: {
        depositNeeded: number;
        pointsNeeded: number;
      };
    } | null = null
    if (nextLevel) {
      const nextLevelConfig = LEVEL_CONFIG[nextLevel]
      const depositProgress = user.depositBalance / nextLevelConfig.minDeposit
      const pointsProgress = user.creditPoints / nextLevelConfig.minCreditPoints
      
      nextLevelInfo = {
        level: nextLevel,
        config: nextLevelConfig,
        progress: {
          deposit: Math.min(depositProgress, 1),
          points: Math.min(pointsProgress, 1),
          overall: Math.min((depositProgress + pointsProgress) / 2, 1)
        },
        requirements: {
          depositNeeded: Math.max(0, nextLevelConfig.minDeposit - user.depositBalance),
          pointsNeeded: Math.max(0, nextLevelConfig.minCreditPoints - user.creditPoints)
        }
      }
    }

    // 获取待结算信息
    const pendingSettlement = await getUserPendingSettlement(userId)

    // 获取提现费率预估（假设提现100 USDT）
    const withdrawalEstimate = await getWithdrawalFeeEstimate(userId, 100)

    // 获取最佳提现时机
    const optimalWithdrawal = await getOptimalWithdrawalTime(userId, 100)

    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        depositBalance: user.depositBalance,
        creditPoints: user.creditPoints,
        lastCreditUpdate: user.lastCreditUpdate,
        accountAge: Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24))
      },
      currentLevel: {
        level: user.creditLevel,
        config: levelConfig,
        benefits,
        upgraded: levelUpdate.upgraded
      },
      nextLevel: nextLevelInfo,
      pendingSettlement,
      withdrawalOptimization: {
        estimate: withdrawalEstimate,
        optimal: optimalWithdrawal
      },
      settings: {
        minWithdrawalAmount: user.minWithdrawalAmount,
        autoWithdrawal: user.autoWithdrawal,
        withdrawalThreshold: user.withdrawalThreshold
      }
    })

  } catch (error) {
    console.error('获取信用等级信息失败:', error)
    return NextResponse.json(
      { error: '获取信用等级信息失败' },
      { status: 500 }
    )
  }
}

// 更新用户提现设置
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { minWithdrawalAmount, autoWithdrawal, withdrawalThreshold } = await request.json()

    // 验证参数
    if (minWithdrawalAmount !== undefined && (minWithdrawalAmount < 1 || minWithdrawalAmount > 1000)) {
      return NextResponse.json(
        { error: '最小提现金额必须在1-1000 USDT之间' },
        { status: 400 }
      )
    }

    if (withdrawalThreshold !== undefined && (withdrawalThreshold < 100 || withdrawalThreshold > 10000)) {
      return NextResponse.json(
        { error: '自动提现阈值必须在100-10000 USDT之间' },
        { status: 400 }
      )
    }

    // 更新用户设置
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        ...(minWithdrawalAmount !== undefined && { minWithdrawalAmount }),
        ...(autoWithdrawal !== undefined && { autoWithdrawal }),
        ...(withdrawalThreshold !== undefined && { withdrawalThreshold })
      },
      select: {
        minWithdrawalAmount: true,
        autoWithdrawal: true,
        withdrawalThreshold: true
      }
    })

    return NextResponse.json({
      success: true,
      message: '设置更新成功',
      settings: updatedUser
    })

  } catch (error) {
    console.error('更新用户设置失败:', error)
    return NextResponse.json(
      { error: '更新用户设置失败' },
      { status: 500 }
    )
  }
}

/**
 * 获取下一个等级
 */
function getNextLevel(currentLevel: CreditLevel): CreditLevel | null {
  const levels = [
    CreditLevel.BRONZE,
    CreditLevel.SILVER,
    CreditLevel.GOLD,
    CreditLevel.PLATINUM,
    CreditLevel.DIAMOND
  ]
  
  const currentIndex = levels.indexOf(currentLevel)
  if (currentIndex >= 0 && currentIndex < levels.length - 1) {
    return levels[currentIndex + 1]
  }
  
  return null // 已经是最高等级
}
