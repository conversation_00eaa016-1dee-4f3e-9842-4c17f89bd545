import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { cache } from '@/lib/cache-fallback'
import { isValidBinanceUID, isValidBNBAddress } from '@/lib/utils'

// 获取用户个人信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 检查缓存
    const cacheKey = `user_profile:${session.user.id}`
    const cachedProfile = await cache.get(cacheKey)
    if (cachedProfile) {
      return NextResponse.json(cachedProfile)
    }

    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        binanceUid: true,
        bnbWalletAddress: true,
        city: true,
        district: true,
        creditScore: true,
        depositBalance: true,
        status: true,
        isGuarantor: true,
        // 中间人相关字段
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorDeposit: true,
        mediatorReputation: true,
        mediatorVerifiedAt: true,
        bnbWalletVerified: true,
        mediatorExperience: true,
        mediatorIntroduction: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取用户的评价统计
    const reviewStats = await prisma.review.aggregate({
      where: {
        revieweeId: session.user.id
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    })

    const userWithStats = {
      ...user,
      averageRating: reviewStats._avg.rating || 0,
      totalReviews: reviewStats._count.rating || 0
    }

    // 缓存用户资料（120秒）
    await cache.set(cacheKey, userWithStats, 120)

    return NextResponse.json(userWithStats)

  } catch (error) {
    console.error('Get profile error:', error)
    return NextResponse.json(
      { error: '获取用户信息失败' },
      { status: 500 }
    )
  }
}

// 更新用户个人信息
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      avatar,
      binanceUid,
      bnbWalletAddress,
      city,
      district
    } = body

    // 验证币安UID格式（如果提供）
    if (binanceUid && !isValidBinanceUID(binanceUid)) {
      return NextResponse.json(
        { error: '币安UID格式不正确，应为8位以上纯数字' },
        { status: 400 }
      )
    }

    // 验证BNB钱包地址格式（如果提供）
    if (bnbWalletAddress && !isValidBNBAddress(bnbWalletAddress)) {
      return NextResponse.json(
        { error: 'BNB钱包地址格式不正确' },
        { status: 400 }
      )
    }

    // 检查币安UID是否已被其他用户使用
    if (binanceUid) {
      const existingUser = await prisma.user.findFirst({
        where: {
          binanceUid,
          id: {
            not: session.user.id
          }
        }
      })

      if (existingUser) {
        return NextResponse.json(
          { error: '该币安UID已被其他用户绑定' },
          { status: 400 }
        )
      }
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: {
        id: session.user.id
      },
      data: {
        name: name || null,
        avatar: avatar || null,
        binanceUid: binanceUid || null,
        bnbWalletAddress: bnbWalletAddress || null,
        city: city || null,
        district: district || null,
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        binanceUid: true,
        bnbWalletAddress: true,
        city: true,
        district: true,
        creditScore: true,
        depositBalance: true,
        status: true,
        isGuarantor: true,
        // 中间人相关字段
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorDeposit: true,
        mediatorReputation: true,
        mediatorVerifiedAt: true,
        bnbWalletVerified: true,
        mediatorExperience: true,
        mediatorIntroduction: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return NextResponse.json(updatedUser)

  } catch (error) {
    console.error('Update profile error:', error)
    return NextResponse.json(
      { error: '更新用户信息失败' },
      { status: 500 }
    )
  }
}
