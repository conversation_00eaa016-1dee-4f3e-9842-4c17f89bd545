import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface RedeemHistory {
  id: string
  type: 'GIFT_CARD' | 'REDEMPTION_CODE'
  code: string
  title: string
  value: number
  unit: string
  redeemedAt: Date
  description: string | null
}

// 获取用户兑换历史
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const type = searchParams.get('type') // 'GIFT_CARD' | 'REDEMPTION_CODE'

    // 获取礼品卡兑换记录
    const giftCardTransactions = await prisma.giftCardTransaction.findMany({
      where: {
        userId: session.user.id,
        transactionType: 'REDEMPTION'
      },
      include: {
        giftCard: {
          select: {
            cardCode: true,
            faceValue: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 获取兑换券使用记录
    const redemptionTransactions = await prisma.redemptionTransaction.findMany({
      where: {
        userId: session.user.id,
        transactionType: 'USE'
      },
      include: {
        redemptionCode: {
          select: {
            codeValue: true,
            title: true,
            description: true,
            rewardType: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 合并和格式化历史记录
    const history: RedeemHistory[] = []

    // 添加礼品卡兑换记录
    giftCardTransactions.forEach(transaction => {
      history.push({
        id: transaction.id,
        type: 'GIFT_CARD',
        code: transaction.giftCard.cardCode,
        title: '礼品卡',
        value: transaction.amount,
        unit: 'USDT',
        redeemedAt: transaction.createdAt,
        description: transaction.description
      })
    })

    // 添加兑换券使用记录
    redemptionTransactions.forEach(transaction => {
      const rewardTypeMap: Record<string, string> = {
        'CASH_CREDIT': '现金奖励',
        'PLATFORM_POINTS': '平台积分',
        'WITHDRAWAL_FEE_DISCOUNT': '提现手续费减免',
        'SHOPPING_VOUCHER': '购物券'
      }

      history.push({
        id: transaction.id,
        type: 'REDEMPTION_CODE',
        code: transaction.redemptionCode.codeValue || '',
        title: transaction.redemptionCode.title,
        value: transaction.rewardValue,
        unit: transaction.rewardUnit,
        redeemedAt: transaction.createdAt,
        description: `${rewardTypeMap[transaction.redemptionCode.rewardType] || transaction.redemptionCode.rewardType} - ${transaction.description}`
      })
    })

    // 按时间排序
    history.sort((a, b) => new Date(b.redeemedAt).getTime() - new Date(a.redeemedAt).getTime())

    // 应用筛选
    let filteredHistory = history
    if (type) {
      filteredHistory = history.filter(item => item.type === type)
    }

    // 分页
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedHistory = filteredHistory.slice(startIndex, endIndex)

    // 获取统计信息
    const stats = {
      total: history.length,
      giftCardCount: history.filter(item => item.type === 'GIFT_CARD').length,
      redemptionCodeCount: history.filter(item => item.type === 'REDEMPTION_CODE').length,
      totalValue: {
        USDT: history
          .filter(item => item.unit === 'USDT')
          .reduce((sum, item) => sum + item.value, 0),
        POINTS: history
          .filter(item => item.unit === 'POINTS')
          .reduce((sum, item) => sum + item.value, 0)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        history: paginatedHistory,
        pagination: {
          page,
          limit,
          total: filteredHistory.length,
          pages: Math.ceil(filteredHistory.length / limit)
        },
        stats
      }
    })

  } catch (error) {
    console.error('获取兑换历史失败:', error)
    return NextResponse.json(
      { success: false, error: '获取兑换历史失败' },
      { status: 500 }
    )
  }
}
