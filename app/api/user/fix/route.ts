/**
 * 用户修复API
 * 当用户在会话中存在但数据库中不存在时，自动创建用户记录
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (existingUser) {
      return NextResponse.json({
        message: '用户已存在',
        user: existingUser
      })
    }

    // 创建新用户记录
    const newUser = await prisma.user.create({
      data: {
        id: session.user.id,
        email: session.user.email || '',
        name: session.user.name || '新用户',
        depositBalance: 0,
        creditScore: 30,
        status: 'ACTIVE'
      }
    })

    return NextResponse.json({
      message: '用户记录已创建',
      user: newUser
    })

  } catch (error) {
    console.error('User fix error:', error)
    return NextResponse.json(
      { error: '修复用户记录失败' },
      { status: 500 }
    )
  }
}
