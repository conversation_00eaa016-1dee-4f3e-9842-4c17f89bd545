import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 默认公开设置
const defaultPublicSettings = {
  platform: {
    name: '比特市场',
    description: 'USDT-based decentralized C2C marketplace',
    contactEmail: '<EMAIL>'
  },
  trading: {
    allowGuestBrowsing: true,
    minimumOrderAmount: 10,
    maximumOrderAmount: 50000
  },
  fees: {
    minimumOrderAmount: 10,
    maximumOrderAmount: 50000
  }
}

/**
 * 获取公开系统设置
 * 这些设置可以被前端直接访问，无需认证
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')

    // 尝试从数据库获取公开设置
    try {
      const whereClause: any = { isPublic: true }
      if (category) {
        whereClause.category = category
      }

      const settings = await prisma.systemSetting.findMany({
        where: whereClause,
        select: {
          category: true,
          key: true,
          value: true,
          description: true,
          dataType: true
        },
        orderBy: [
          { category: 'asc' },
          { key: 'asc' }
        ]
      })

      if (settings.length > 0) {
        // 将数据库设置转换为嵌套对象格式
        const result: any = {}
        
        for (const setting of settings) {
          if (!result[setting.category]) {
            result[setting.category] = {}
          }
          result[setting.category][setting.key] = setting.value
        }

        return NextResponse.json({
          success: true,
          settings: category ? result[category] : result,
          source: 'database'
        })
      }
    } catch (error) {
      console.log('数据库查询失败，使用默认设置:', error instanceof Error ? error.message : String(error))
    }

    // 如果数据库查询失败或没有数据，返回默认设置
    const result = category 
      ? defaultPublicSettings[category as keyof typeof defaultPublicSettings] 
      : defaultPublicSettings

    return NextResponse.json({
      success: true,
      settings: result,
      source: 'default'
    })

  } catch (error) {
    console.error('获取公开设置失败:', error)
    
    // 即使出错也返回默认设置，确保前端能正常工作
    const result = defaultPublicSettings
    
    return NextResponse.json({
      success: true,
      settings: result,
      source: 'fallback',
      error: '获取设置时出现错误，返回默认设置'
    })
  }
}

/**
 * 获取平台基本信息
 * 专门用于首页和公开页面显示
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type = 'basic' } = body

    let result: any = {}

    switch (type) {
      case 'basic':
        // 基本平台信息
        result = {
          name: defaultPublicSettings.platform.name,
          description: defaultPublicSettings.platform.description,
          contactEmail: defaultPublicSettings.platform.contactEmail,
          features: [
            'USDT交易',
            '安全托管',
            '实时聊天',
            '信用评级',
            '争议处理'
          ],
          stats: await getPlatformStats()
        }
        break

      case 'trading':
        // 交易相关设置
        result = {
          allowGuestBrowsing: defaultPublicSettings.trading.allowGuestBrowsing,
          minimumOrderAmount: defaultPublicSettings.trading.minimumOrderAmount,
          maximumOrderAmount: defaultPublicSettings.trading.maximumOrderAmount,
          supportedCurrencies: ['USDT'],
          paymentMethods: ['银行转账', '支付宝', '微信支付', 'USDT转账']
        }
        break

      case 'fees':
        // 费用信息
        result = {
          minimumOrderAmount: defaultPublicSettings.fees.minimumOrderAmount,
          maximumOrderAmount: defaultPublicSettings.fees.maximumOrderAmount,
          feeStructure: {
            platform: '2%',
            guarantor: '1%',
            withdrawal: '0.5%'
          }
        }
        break

      default:
        return NextResponse.json(
          { error: '无效的信息类型' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      data: result,
      type
    })

  } catch (error) {
    console.error('获取平台信息失败:', error)
    return NextResponse.json(
      { error: '获取平台信息失败' },
      { status: 500 }
    )
  }
}

/**
 * 获取平台统计信息
 */
async function getPlatformStats() {
  try {
    const [
      totalUsers,
      totalProducts,
      totalOrders,
      activeUsers
    ] = await Promise.all([
      prisma.user.count(),
      prisma.product.count({ where: { status: 'AVAILABLE' } }),
      prisma.order.count(),
      prisma.user.count({
        where: {
          OR: [
            { ordersAsBuyer: { some: {} } },
            { ordersAsSeller: { some: {} } }
          ]
        }
      })
    ])

    return {
      totalUsers,
      totalProducts,
      totalOrders,
      activeUsers,
      lastUpdated: new Date().toISOString()
    }
  } catch (error) {
    console.error('获取平台统计失败:', error)
    return {
      totalUsers: 0,
      totalProducts: 0,
      totalOrders: 0,
      activeUsers: 0,
      lastUpdated: new Date().toISOString()
    }
  }
}
