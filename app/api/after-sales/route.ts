import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取售后申请列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type') // REFUND, EXCHANGE, REPAIR
    const status = searchParams.get('status') // PENDING, APPROVED, etc.
    const role = searchParams.get('role') || 'buyer' // buyer, seller
    const search = searchParams.get('search') // 搜索关键词
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const skip = (page - 1) * limit

    // 构建查询条件
    let where: any = {}

    if (role === 'buyer') {
      where.buyerId = session.user.id
    } else if (role === 'seller') {
      where.sellerId = session.user.id
    } else {
      // 如果角色不明确，查询用户相关的所有售后申请
      where.OR = [
        { buyerId: session.user.id },
        { sellerId: session.user.id }
      ]
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    if (search) {
      where.OR = [
        ...(where.OR || []),
        {
          reason: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          order: {
            orderNumber: {
              contains: search,
              mode: 'insensitive'
            }
          }
        }
      ]
    }

    if (startDate) {
      where.createdAt = {
        ...where.createdAt,
        gte: new Date(startDate)
      }
    }

    if (endDate) {
      where.createdAt = {
        ...where.createdAt,
        lte: new Date(endDate + 'T23:59:59.999Z')
      }
    }

    // 获取售后申请列表
    const [requests, total] = await Promise.all([
      prisma.afterSalesRequest.findMany({
        where,
        include: {
          order: {
            select: {
              orderNumber: true,
              totalAmount: true,
              product: {
                select: {
                  title: true,
                  images: true
                }
              }
            }
          },
          buyer: {
            select: {
              name: true
            }
          },
          seller: {
            select: {
              name: true
            }
          },
          messages: {
            where: {
              senderId: { not: session.user.id },
              isRead: false
            },
            select: {
              id: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.afterSalesRequest.count({ where })
    ])

    // 计算分页信息
    const totalPages = Math.ceil(total / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1

    return NextResponse.json({
      requests: requests.map(request => ({
        ...request,
        unreadCount: request.messages.length
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    })

  } catch (error) {
    console.error('获取售后申请列表失败:', error)
    return NextResponse.json(
      { error: '获取售后申请列表失败' },
      { status: 500 }
    )
  }
}

// 获取售后统计信息
export async function HEAD(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse(null, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const role = searchParams.get('role') || 'buyer'

    // 构建基础查询条件
    let baseWhere: any = {}
    if (role === 'buyer') {
      baseWhere.buyerId = session.user.id
    } else if (role === 'seller') {
      baseWhere.sellerId = session.user.id
    }

    // 获取各种统计数据
    const [
      totalCount,
      pendingCount,
      processingCount,
      completedCount,
      refundCount,
      exchangeCount,
      repairCount,
      unreadMessagesCount
    ] = await Promise.all([
      // 总数
      prisma.afterSalesRequest.count({ where: baseWhere }),
      
      // 待处理数量
      prisma.afterSalesRequest.count({
        where: { ...baseWhere, status: 'PENDING' }
      }),
      
      // 处理中数量
      prisma.afterSalesRequest.count({
        where: { ...baseWhere, status: { in: ['APPROVED', 'PROCESSING'] } }
      }),
      
      // 已完成数量
      prisma.afterSalesRequest.count({
        where: { ...baseWhere, status: 'COMPLETED' }
      }),
      
      // 退款申请数量
      prisma.afterSalesRequest.count({
        where: { ...baseWhere, type: 'REFUND' }
      }),
      
      // 换货申请数量
      prisma.afterSalesRequest.count({
        where: { ...baseWhere, type: 'EXCHANGE' }
      }),
      
      // 维修申请数量
      prisma.afterSalesRequest.count({
        where: { ...baseWhere, type: 'REPAIR' }
      }),
      
      // 未读消息数量
      prisma.afterSalesMessage.count({
        where: {
          afterSales: baseWhere,
          senderId: { not: session.user.id },
          isRead: false
        }
      })
    ])

    return new NextResponse(null, {
      status: 200,
      headers: {
        'X-Total-Count': totalCount.toString(),
        'X-Pending-Count': pendingCount.toString(),
        'X-Processing-Count': processingCount.toString(),
        'X-Completed-Count': completedCount.toString(),
        'X-Refund-Count': refundCount.toString(),
        'X-Exchange-Count': exchangeCount.toString(),
        'X-Repair-Count': repairCount.toString(),
        'X-Unread-Messages': unreadMessagesCount.toString()
      }
    })

  } catch (error) {
    console.error('获取售后统计信息失败:', error)
    return new NextResponse(null, { status: 500 })
  }
}
