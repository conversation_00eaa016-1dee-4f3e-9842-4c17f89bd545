import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 更新售后申请状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { 
      status, 
      refundTxHash, 
      exchangeTrackingNumber, 
      repairInstructions,
      adminNotes 
    } = body

    // 验证状态
    const validStatuses = ['PENDING', 'APPROVED', 'REJECTED', 'PROCESSING', 'COMPLETED', 'CANCELLED']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: '无效的状态' },
        { status: 400 }
      )
    }

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true,
            orderNumber: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限操作此售后申请' },
        { status: 403 }
      )
    }

    // 验证状态流转规则
    const currentStatus = afterSalesRequest.status
    const isValidTransition = validateStatusTransition(currentStatus, status, isBuyer, isSeller)
    
    if (!isValidTransition) {
      return NextResponse.json(
        { error: '无效的状态流转' },
        { status: 400 }
      )
    }

    // 准备更新数据
    let updateData: any = {
      status,
      updatedAt: new Date()
    }

    // 根据状态设置相应字段
    if (status === 'PROCESSING') {
      updateData.processedAt = new Date()
    } else if (status === 'COMPLETED') {
      updateData.completedAt = new Date()
      
      // 根据类型设置完成相关字段
      if (afterSalesRequest.type === 'REFUND' && refundTxHash) {
        updateData.refundTxHash = refundTxHash
        updateData.refundedAt = new Date()
      } else if (afterSalesRequest.type === 'EXCHANGE' && exchangeTrackingNumber) {
        updateData.exchangeTrackingNumber = exchangeTrackingNumber
      } else if (afterSalesRequest.type === 'REPAIR' && repairInstructions) {
        updateData.repairInstructions = repairInstructions
      }
    }

    if (adminNotes) {
      updateData.adminNotes = adminNotes
    }

    // 更新售后申请
    const updatedRequest = await prisma.afterSalesRequest.update({
      where: { id },
      data: updateData
    })

    // 创建状态变更日志
    await prisma.orderLog.create({
      data: {
        orderId: afterSalesRequest.orderId,
        operatorId: session.user.id,
        action: 'UPDATE_AFTER_SALES_STATUS',
        description: `售后状态变更：${getStatusText(currentStatus)} → ${getStatusText(status)}`
      }
    })

    // TODO: 发送状态变更通知
    // await sendStatusChangeNotification(afterSalesRequest, status)

    return NextResponse.json({
      success: true,
      message: '状态更新成功',
      request: updatedRequest
    })

  } catch (error) {
    console.error('更新售后状态失败:', error)
    return NextResponse.json(
      { error: '更新售后状态失败' },
      { status: 500 }
    )
  }
}

// 获取售后申请状态历史
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true,
            orderNumber: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限查看此售后申请' },
        { status: 403 }
      )
    }

    // 获取状态变更历史
    const statusHistory = await prisma.orderLog.findMany({
      where: {
        orderId: afterSalesRequest.orderId,
        action: 'UPDATE_AFTER_SALES_STATUS'
      },
      include: {
        operator: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // 构建状态时间线
    const timeline = [
      {
        status: 'PENDING',
        timestamp: afterSalesRequest.createdAt,
        operator: '买家',
        description: '提交售后申请'
      }
    ]

    if (afterSalesRequest.sellerResponseAt) {
      timeline.push({
        status: afterSalesRequest.status === 'APPROVED' ? 'APPROVED' : 'REJECTED',
        timestamp: afterSalesRequest.sellerResponseAt,
        operator: '卖家',
        description: afterSalesRequest.status === 'APPROVED' ? '同意售后申请' : '拒绝售后申请'
      })
    }

    if (afterSalesRequest.processedAt && afterSalesRequest.status === 'PROCESSING') {
      timeline.push({
        status: 'PROCESSING',
        timestamp: afterSalesRequest.processedAt,
        operator: '卖家',
        description: '开始处理售后'
      })
    }

    if (afterSalesRequest.completedAt && afterSalesRequest.status === 'COMPLETED') {
      timeline.push({
        status: 'COMPLETED',
        timestamp: afterSalesRequest.completedAt,
        operator: '系统',
        description: '售后处理完成'
      })
    }

    return NextResponse.json({
      request: afterSalesRequest,
      timeline,
      statusHistory
    })

  } catch (error) {
    console.error('获取售后状态历史失败:', error)
    return NextResponse.json(
      { error: '获取售后状态历史失败' },
      { status: 500 }
    )
  }
}

// 验证状态流转规则
function validateStatusTransition(
  currentStatus: string, 
  newStatus: string, 
  isBuyer: boolean, 
  isSeller: boolean
): boolean {
  // 状态流转规则
  const transitions: Record<string, { allowed: string[], roles: string[] }> = {
    'PENDING': {
      allowed: ['APPROVED', 'REJECTED', 'CANCELLED'],
      roles: ['seller', 'buyer'] // 卖家可以同意/拒绝，买家可以取消
    },
    'APPROVED': {
      allowed: ['PROCESSING', 'CANCELLED'],
      roles: ['seller', 'buyer']
    },
    'REJECTED': {
      allowed: ['CANCELLED'],
      roles: ['buyer']
    },
    'PROCESSING': {
      allowed: ['COMPLETED', 'CANCELLED'],
      roles: ['seller', 'buyer']
    },
    'COMPLETED': {
      allowed: [],
      roles: []
    },
    'CANCELLED': {
      allowed: [],
      roles: []
    }
  }

  const transition = transitions[currentStatus]
  if (!transition || !transition.allowed.includes(newStatus)) {
    return false
  }

  // 检查角色权限
  if (newStatus === 'APPROVED' || newStatus === 'REJECTED' || newStatus === 'PROCESSING' || newStatus === 'COMPLETED') {
    return isSeller
  }
  
  if (newStatus === 'CANCELLED') {
    return isBuyer || isSeller
  }

  return false
}

// 获取状态文本
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'PENDING': '待处理',
    'APPROVED': '已同意',
    'REJECTED': '已拒绝',
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}
