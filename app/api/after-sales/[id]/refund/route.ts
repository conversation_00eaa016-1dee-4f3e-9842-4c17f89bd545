import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 处理退款申请
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { refundMethod, refundAmount } = body

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            id: true,
            sellerId: true,
            buyerId: true,
            totalAmount: true,
            orderNumber: true,
            paymentMethod: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有卖家可以处理退款
    if (afterSalesRequest.order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限处理此退款申请' },
        { status: 403 }
      )
    }

    // 检查申请类型和状态
    if (afterSalesRequest.type !== 'REFUND') {
      return NextResponse.json(
        { error: '该申请不是退款申请' },
        { status: 400 }
      )
    }

    if (afterSalesRequest.status !== 'APPROVED') {
      return NextResponse.json(
        { error: '该退款申请尚未同意' },
        { status: 400 }
      )
    }

    // 验证退款金额
    if (!refundAmount || refundAmount <= 0 || refundAmount > afterSalesRequest.order.totalAmount) {
      return NextResponse.json(
        { error: '退款金额不正确' },
        { status: 400 }
      )
    }

    // 验证退款方式
    const validRefundMethods = ['ORIGINAL_PAYMENT', 'BALANCE', 'BANK_TRANSFER']
    if (!validRefundMethods.includes(refundMethod)) {
      return NextResponse.json(
        { error: '无效的退款方式' },
        { status: 400 }
      )
    }

    // 开始退款处理
    let refundTxHash: string | null = null
    let refundSuccess = false

    try {
      // 根据退款方式处理退款
      switch (refundMethod) {
        case 'ORIGINAL_PAYMENT':
          // 确保 paymentMethod 不为 null
          if (afterSalesRequest.order.paymentMethod) {
            refundTxHash = await processOriginalPaymentRefund(afterSalesRequest.order, refundAmount)
            refundSuccess = true
          } else {
            throw new Error('支付方式不能为空')
          }
          break
          
        case 'BALANCE':
          refundTxHash = await processBalanceRefund(afterSalesRequest.order.buyerId as string, refundAmount)
          refundSuccess = true
          break
          
        case 'BANK_TRANSFER':
          // 银行转账需要手动处理，这里只记录
          refundTxHash = `BANK_TRANSFER_${Date.now()}`
          refundSuccess = true
          break
          
        default:
          throw new Error('不支持的退款方式')
      }

      if (refundSuccess) {
        // 更新售后申请状态
        await prisma.afterSalesRequest.update({
          where: { id },
          data: {
            status: 'COMPLETED',
            refundAmount,
            refundMethod,
            refundTxHash,
            refundedAt: new Date(),
            completedAt: new Date()
          }
        })

        // 更新订单状态
        await prisma.order.update({
          where: { id: afterSalesRequest.orderId },
          data: {
            status: 'REFUNDED',
            refundAmount
          }
        })

        // 创建操作日志
        await prisma.orderLog.create({
          data: {
            orderId: afterSalesRequest.orderId,
            operatorId: session.user.id,
            action: 'PROCESS_REFUND',
            description: `处理退款：¥${refundAmount.toFixed(2)}，方式：${getRefundMethodText(refundMethod)}`
          }
        })

        // TODO: 发送退款完成通知
        // await sendRefundCompletedNotification(afterSalesRequest.order.buyerId, refundAmount)

        return NextResponse.json({
          success: true,
          message: '退款处理成功',
          refundTxHash,
          refundAmount
        })
      }

    } catch (error) {
      console.error('退款处理失败:', error)
      
      // 记录退款失败日志
      await prisma.orderLog.create({
        data: {
          orderId: afterSalesRequest.orderId,
          operatorId: session.user.id,
          action: 'REFUND_FAILED',
          description: `退款失败：${error instanceof Error ? error.message : '未知错误'}`
        }
      })

      return NextResponse.json(
        { error: '退款处理失败，请稍后重试' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('处理退款申请失败:', error)
    return NextResponse.json(
      { error: '处理退款申请失败' },
      { status: 500 }
    )
  }
}

// 获取退款信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true,
            totalAmount: true,
            paymentMethod: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限查看此退款信息' },
        { status: 403 }
      )
    }

    // 计算可退款金额
    const maxRefundAmount = afterSalesRequest.order.totalAmount
    const suggestedRefundAmount = afterSalesRequest.requestedAmount || maxRefundAmount

    // 获取可用的退款方式
    const availableRefundMethods = getAvailableRefundMethods(afterSalesRequest.order.paymentMethod)

    return NextResponse.json({
      maxRefundAmount,
      suggestedRefundAmount,
      availableRefundMethods,
      currentStatus: afterSalesRequest.status,
      refundAmount: afterSalesRequest.refundAmount,
      refundMethod: afterSalesRequest.refundMethod,
      refundTxHash: afterSalesRequest.refundTxHash,
      refundedAt: afterSalesRequest.refundedAt
    })

  } catch (error) {
    console.error('获取退款信息失败:', error)
    return NextResponse.json(
      { error: '获取退款信息失败' },
      { status: 500 }
    )
  }
}

// 处理原支付方式退款
async function processOriginalPaymentRefund(order: { paymentMethod: string | null; buyerId: string }, refundAmount: number): Promise<string> {
  // 这里应该调用相应的支付接口进行退款
  // 目前返回模拟的交易哈希
  
  if (order.paymentMethod === 'BINANCE_PAY') {
    // 调用币安支付退款API
    return `BINANCE_REFUND_${Date.now()}`
  } else if (order.paymentMethod === 'BSC_PAY') {
    // 调用BSC链上退款
    return `BSC_REFUND_${Date.now()}`
  } else if (order.paymentMethod === 'BALANCE') {
    // 退回到用户余额
    await processBalanceRefund(order.buyerId as string, refundAmount);
    return `BALANCE_REFUND_${Date.now()}`;
  }
  
  throw new Error('不支持的原支付方式退款')
}

// 处理余额退款
async function processBalanceRefund(buyerId: string, refundAmount: number): Promise<string> {
  // 增加用户余额
  await prisma.user.update({
    where: { id: buyerId },
    data: {
      depositBalance: {
        increment: refundAmount
      }
    }
  })

  // 创建余额变动记录
  await prisma.fundTransaction.create({
    data: {
      userId: buyerId,
      type: 'REFUND',
      amount: refundAmount,
      description: '售后退款',
      createdAt: new Date()
    }
  })

  return `BALANCE_REFUND_${Date.now()}`
}

// 获取可用的退款方式
function getAvailableRefundMethods(paymentMethod: string | null): Array<{code: string, name: string, description: string}> {
  const methods = [
    {
      code: 'ORIGINAL_PAYMENT',
      name: '原路退回',
      description: '退款到原支付方式'
    },
    {
      code: 'BALANCE',
      name: '退到余额',
      description: '退款到账户余额'
    }
  ]

  // 根据原支付方式添加特定选项
  if (paymentMethod === 'BINANCE_PAY' || paymentMethod === 'BSC_PAY') {
    methods.push({
      code: 'BANK_TRANSFER',
      name: '银行转账',
      description: '通过银行转账退款'
    })
  }

  return methods
}

// 获取退款方式文本
function getRefundMethodText(method: string): string {
  const methodMap: Record<string, string> = {
    'ORIGINAL_PAYMENT': '原路退回',
    'BALANCE': '退到余额',
    'BANK_TRANSFER': '银行转账'
  }
  return methodMap[method] || method
}
