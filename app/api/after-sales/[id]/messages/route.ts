import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取售后消息列表
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限查看此售后申请的消息' },
        { status: 403 }
      )
    }

    // 获取消息列表
    const messages = await prisma.afterSalesMessage.findMany({
      where: { afterSalesId: id },
      include: {
        sender: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // 标记消息为已读（除了自己发送的消息）
    await prisma.afterSalesMessage.updateMany({
      where: {
        afterSalesId: id,
        senderId: { not: session.user.id },
        isRead: false
      },
      data: {
        isRead: true
      }
    })

    return NextResponse.json({ messages })

  } catch (error) {
    console.error('获取售后消息失败:', error)
    return NextResponse.json(
      { error: '获取售后消息失败' },
      { status: 500 }
    )
  }
}

// 发送售后消息
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { content, images, messageType = 'TEXT' } = body

    // 验证消息内容
    if (!content?.trim() && (!images || images.length === 0)) {
      return NextResponse.json(
        { error: '消息内容不能为空' },
        { status: 400 }
      )
    }

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true,
            orderNumber: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限发送消息' },
        { status: 403 }
      )
    }

    // 检查售后申请状态
    if (['COMPLETED', 'CANCELLED'].includes(afterSalesRequest.status)) {
      return NextResponse.json(
        { error: '该售后申请已结束，无法发送消息' },
        { status: 400 }
      )
    }

    // 创建消息
    const message = await prisma.afterSalesMessage.create({
      data: {
        afterSalesId: id,
        senderId: session.user.id,
        content: content?.trim() || '',
        images: images && images.length > 0 ? images : null,
        messageType,
        isRead: false
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // TODO: 发送消息通知给对方
    // const recipientId = isBuyer ? afterSalesRequest.order.sellerId : afterSalesRequest.order.buyerId
    // await sendMessageNotification(recipientId, message)

    return NextResponse.json({
      success: true,
      message: message
    })

  } catch (error) {
    console.error('发送售后消息失败:', error)
    return NextResponse.json(
      { error: '发送售后消息失败' },
      { status: 500 }
    )
  }
}

// 获取未读消息数量
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse(null, { status: 401 })
    }

    const { id } = await params

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return new NextResponse(null, { status: 404 })
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return new NextResponse(null, { status: 403 })
    }

    // 获取未读消息数量
    const unreadCount = await prisma.afterSalesMessage.count({
      where: {
        afterSalesId: id,
        senderId: { not: session.user.id },
        isRead: false
      }
    })

    return new NextResponse(null, {
      status: 200,
      headers: {
        'X-Unread-Count': unreadCount.toString()
      }
    })

  } catch (error) {
    console.error('获取未读消息数量失败:', error)
    return new NextResponse(null, { status: 500 })
  }
}
