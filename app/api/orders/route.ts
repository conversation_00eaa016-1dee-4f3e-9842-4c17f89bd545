import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { canUserCreateOrder } from '@/lib/user-status'
import { createStockNotification } from '@/lib/inventory'
import { generatePinForOrder } from '@/lib/payment-pin'
import { generateOrderNumber } from '@/lib/utils'

// 获取订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'buyer' | 'seller'
    const status = searchParams.get('status')
    const search = searchParams.get('search') // 搜索关键词
    const dateFrom = searchParams.get('dateFrom') // 开始日期
    const dateTo = searchParams.get('dateTo') // 结束日期
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}

    if (type === 'buyer') {
      where.buyerId = session.user.id
    } else if (type === 'seller') {
      where.sellerId = session.user.id
    } else {
      // 默认返回用户相关的所有订单
      where.OR = [
        { buyerId: session.user.id },
        { sellerId: session.user.id }
      ]
    }

    if (status) {
      where.status = status
    }

    // 添加搜索功能
    if (search) {
      where.AND = [
        ...(where.AND || []),
        {
          OR: [
            { orderNumber: { contains: search, mode: 'insensitive' } },
            { product: { title: { contains: search, mode: 'insensitive' } } },
            ...(type === 'seller' ? [
              { buyer: { name: { contains: search, mode: 'insensitive' } } }
            ] : [])
          ]
        }
      ]
    }

    // 添加日期筛选
    if (dateFrom || dateTo) {
      where.AND = [
        ...(where.AND || []),
        {
          createdAt: {
            ...(dateFrom && { gte: new Date(dateFrom) }),
            ...(dateTo && { lte: new Date(dateTo + 'T23:59:59.999Z') })
          }
        }
      ]
    }

    // 获取订单列表
    const orders = await prisma.order.findMany({
      where,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true,
            images: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    })

    // 获取总数
    const total = await prisma.order.count({ where })

    // 如果是卖家查询，计算统计信息
    let statistics: any = null
    if (type === 'seller') {
      const statsWhere = {
        sellerId: session.user.id
      }

      const [
        totalOrders,
        pendingPayment,
        paid,
        shipped,
        completed,
        cancelled,
        refundRequested
      ] = await Promise.all([
        prisma.order.count({ where: statsWhere }),
        prisma.order.count({ where: { ...statsWhere, status: 'PENDING_PAYMENT' } }),
        prisma.order.count({ where: { ...statsWhere, status: 'PAID' } }),
        prisma.order.count({ where: { ...statsWhere, status: 'SHIPPED' } }),
        prisma.order.count({ where: { ...statsWhere, status: 'COMPLETED' } }),
        prisma.order.count({ where: { ...statsWhere, status: 'CANCELLED' } }),
        prisma.order.count({ where: { ...statsWhere, status: 'REFUND_REQUESTED' } })
      ])

      statistics = {
        totalOrders,
        pendingPayment,
        paid,
        shipped,
        completed,
        cancelled,
        refundRequested
      }
    }

    return NextResponse.json({
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      ...(statistics && { statistics })
    })

  } catch (error) {
    console.error('Get orders error:', error)
    return NextResponse.json(
      { error: '获取订单列表失败' },
      { status: 500 }
    )
  }
}

// 创建订单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // 严格的身份验证
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录，请先登录' },
        { status: 401 }
      )
    }

    // 验证用户存在性和状态
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        status: true,
        bannedAt: true,
        bannedUntil: true,
        email: true,
        name: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 401 }
      )
    }

    // 检查用户是否可以创建订单
    const canCreate = await canUserCreateOrder(session.user.id)
    if (!canCreate) {
      return NextResponse.json(
        { error: '您的账户状态不允许创建订单，请联系客服' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      productId,
      variantId,
      quantity = 1,
      shippingAddress
    } = body

    // 验证必填字段
    if (!productId) {
      return NextResponse.json(
        { error: '商品ID为必填项' },
        { status: 400 }
      )
    }

    if (!shippingAddress || !shippingAddress.name || !shippingAddress.phone || !shippingAddress.detail) {
      return NextResponse.json(
        { error: '收货地址信息不完整' },
        { status: 400 }
      )
    }

    // 获取商品信息
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true
          }
        },
        variants: {
          include: {
            attributes: true
          }
        }
      }
    })

    // 如果指定了变体，获取变体信息
    let selectedVariant: any = null
    if (variantId) {
      selectedVariant = await prisma.productVariant.findUnique({
        where: { id: variantId },
        include: {
          attributes: true
        }
      })

      if (!selectedVariant || selectedVariant.productId !== productId) {
        return NextResponse.json(
          { error: '变体不存在或不属于该商品' },
          { status: 400 }
        )
      }
    }

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    if (product.sellerId === session.user.id) {
      return NextResponse.json(
        { error: '不能购买自己的商品' },
        { status: 400 }
      )
    }

    // 检查商品或变体的状态和库存
    const itemToCheck = selectedVariant || product
    const itemPrice = selectedVariant ? selectedVariant.price : product.price

    if (itemToCheck.status !== 'AVAILABLE') {
      return NextResponse.json(
        { error: selectedVariant ? '该规格不可购买' : '商品不可购买' },
        { status: 400 }
      )
    }

    if (itemToCheck.stock < quantity) {
      return NextResponse.json(
        { error: '库存不足' },
        { status: 400 }
      )
    }

    // 如果商品有变体但没有选择变体，返回错误
    if (product.hasVariants && !selectedVariant) {
      return NextResponse.json(
        { error: '请选择商品规格' },
        { status: 400 }
      )
    }

    // 计算费用
    const productPrice = itemPrice * quantity
    const shippingFee = 0 // 暂时设为0，后续可以根据地址计算
    const platformFee = productPrice <= 50 ? 0.5 : productPrice <= 100 ? 1 : productPrice * 0.015
    const totalAmount = productPrice + shippingFee + platformFee

    // 生成订单号
    const orderNumber = generateOrderNumber()

    // 使用事务创建订单并处理库存
    const result = await prisma.$transaction(async (tx) => {
      // 创建订单
      const order = await tx.order.create({
        data: {
          orderNumber,
          productId,
          buyerId: session.user.id,
          sellerId: product.sellerId,
          status: 'PENDING_PAYMENT',
          totalAmount,
          productPrice,
          shippingFee,
          platformFee,
          shippingAddress,
          autoConfirmAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后自动确认
        }
      })

      // 创建订单项目
      await tx.orderItem.create({
        data: {
          orderId: order.id,
          productId,
          variantId: selectedVariant?.id,
          quantity,
          unitPrice: itemPrice,
          totalPrice: productPrice
        }
      })

      // 扣减库存
      if (selectedVariant) {
        // 扣减变体库存
        const currentVariant = await tx.productVariant.findUnique({
          where: { id: selectedVariant.id }
        })

        if (!currentVariant || currentVariant.stock < quantity) {
          throw new Error('变体库存不足')
        }

        await tx.productVariant.update({
          where: { id: selectedVariant.id },
          data: {
            stock: {
              decrement: quantity
            }
          }
        })

        // 检查是否需要自动下架变体
        if (currentVariant.stock - quantity <= 0) {
          await tx.productVariant.update({
            where: { id: selectedVariant.id },
            data: { status: 'SOLD_OUT' }
          })
        }
      } else {
        // 扣减商品库存
        const currentProduct = await tx.product.findUnique({
          where: { id: productId }
        })

        if (!currentProduct || currentProduct.stock < quantity) {
          throw new Error('商品库存不足')
        }

        await tx.product.update({
          where: { id: productId },
          data: {
            stock: {
              decrement: quantity
            }
          }
        })

        // 检查是否需要自动下架商品
        if (currentProduct.stock - quantity <= 0) {
          await tx.product.update({
            where: { id: productId },
            data: { status: 'SOLD_OUT' }
          })
        }
      }

      // 获取完整的订单信息
      const fullOrder = await tx.order.findUnique({
        where: { id: order.id },
        include: {
          product: {
            select: {
              id: true,
              title: true,
              price: true,
              images: true
            }
          },
          buyer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          seller: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          orderItems: {
            include: {
              variant: {
                include: {
                  attributes: true
                }
              }
            }
          }
        }
      })

      // 使用JSON.parse(JSON.stringify())来深度清理数据
      const cleanOrder = JSON.parse(JSON.stringify(fullOrder, (_key, value) => {
        // 转换Date对象为ISO字符串
        if (value instanceof Date) {
          return value.toISOString()
        }
        // 移除函数和undefined值
        if (typeof value === 'function' || value === undefined) {
          return undefined
        }
        return value
      }))

      return cleanOrder
    })

    // 为订单生成支付PIN码
    try {
      if (result?.id) {
        await generatePinForOrder(result.id)
      }
    } catch (pinError) {
      console.error('Failed to generate PIN for order:', pinError)
      // PIN生成失败不影响订单创建，但需要记录日志
    }

    // 检查是否需要发送库存不足通知
    const finalStock = selectedVariant
      ? (await prisma.productVariant.findUnique({ where: { id: selectedVariant.id } }))?.stock || 0
      : (await prisma.product.findUnique({ where: { id: productId } }))?.stock || 0

    if (finalStock <= 0) {
      const itemName = selectedVariant
        ? `${product.title} (${selectedVariant.attributes?.map((a: any) => `${a.name}: ${a.value}`).join(', ') || ''})`
        : product.title

      await createStockNotification(
        productId,
        product.sellerId,
        `您的${selectedVariant ? '商品规格' : '商品'}"${itemName}"库存已售完，已自动下架。如需继续销售，请补充库存后重新上架。`
      )
    }

    return NextResponse.json(result, { status: 201 })

  } catch (error) {
    console.error('Create order error:', error)
    return NextResponse.json(
      { error: '创建订单失败' },
      { status: 500 }
    )
  }
}
