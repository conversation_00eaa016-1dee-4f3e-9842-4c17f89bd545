import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 批量发货
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { orderIds } = body

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { error: '请选择要发货的订单' },
        { status: 400 }
      )
    }

    // 获取订单信息并验证权限
    const orders = await prisma.order.findMany({
      where: {
        id: { in: orderIds },
        sellerId: session.user.id
      },
      select: {
        id: true,
        status: true,
        orderNumber: true
      }
    })

    if (orders.length !== orderIds.length) {
      return NextResponse.json(
        { error: '部分订单不存在或无权限操作' },
        { status: 403 }
      )
    }

    // 检查订单状态
    const invalidOrders = orders.filter(order => order.status !== 'PAID')
    if (invalidOrders.length > 0) {
      return NextResponse.json(
        { error: `订单 ${invalidOrders.map(o => o.orderNumber).join(', ')} 状态不允许发货` },
        { status: 400 }
      )
    }

    const shippedAt = new Date()

    // 批量更新订单状态
    await prisma.order.updateMany({
      where: {
        id: { in: orderIds }
      },
      data: {
        status: 'SHIPPED',
        shippedAt
      }
    })

    // 批量创建操作日志
    const logData = orderIds.map(orderId => ({
      orderId,
      operatorId: session.user.id,
      action: 'SHIP_ORDER',
      description: '卖家批量发货'
    }))

    await prisma.orderLog.createMany({
      data: logData
    })

    // TODO: 发送发货通知给买家
    // for (const orderId of orderIds) {
    //   await sendShippingNotification(orderId)
    // }

    return NextResponse.json({
      success: true,
      message: `成功发货 ${orderIds.length} 个订单`,
      shippedCount: orderIds.length
    })

  } catch (error) {
    console.error('批量发货失败:', error)
    return NextResponse.json(
      { error: '批量发货失败' },
      { status: 500 }
    )
  }
}
