import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generatePinForOrder } from '@/lib/payment-pin'

// 生成支付PIN码
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params

    // 验证订单
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user || user.id !== order.buyerId) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 验证订单状态
    if (order.status !== 'PENDING_PAYMENT') {
      return NextResponse.json(
        { error: '订单状态不允许生成PIN码' },
        { status: 400 }
      )
    }

    // 检查是否已有未使用的PIN码
    if (order.paymentPin && !order.paymentPinUsed && order.paymentPinExpiry && new Date() < order.paymentPinExpiry) {
      return NextResponse.json({
        success: true,
        pinCode: order.paymentPin,
        expiryTime: order.paymentPinExpiry,
        message: '使用现有的PIN码'
      })
    }

    // 生成新的PIN码
    const pinCode = await generatePinForOrder(orderId)

    // 获取更新后的订单信息
    const updatedOrder = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        paymentPin: true,
        paymentPinExpiry: true
      }
    })

    return NextResponse.json({
      success: true,
      pinCode: pinCode,
      expiryTime: updatedOrder?.paymentPinExpiry,
      message: 'PIN码生成成功'
    })

  } catch (error) {
    console.error('Generate PIN error:', error)
    return NextResponse.json(
      { error: '生成PIN码失败' },
      { status: 500 }
    )
  }
}
