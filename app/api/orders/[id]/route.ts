import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { checkAndUpdateProductStock } from '@/lib/inventory'
import { handleOrderStatusChange, handlePaymentConfirmed } from '@/lib/systemMessage'
import { sendOrderStatusNotification, sendPaymentConfirmedNotification } from '@/lib/notifications'

// 辅助函数：生成操作日志描述
function getLogDescription(action: string, updateData: any, oldStatus: string, newStatus: string): string {
  switch (action) {
    case 'upload_payment':
      return '买家上传支付凭证'
    case 'confirm_payment':
      return '卖家确认支付'
    case 'ship':
      return `卖家发货${updateData.trackingNumber ? `，运单号：${updateData.trackingNumber}` : ''}`
    case 'confirm_received':
      return '买家确认收货'
    case 'request_refund':
      return `买家申请退款${updateData.refundReason ? `，原因：${updateData.refundReason}` : ''}`
    case 'cancel':
      return '订单被取消'
    case 'update_status':
      return `订单状态从 ${oldStatus} 更新为 ${newStatus}`
    case 'update_info':
      return '更新订单信息'
    default:
      return `订单操作：${action}`
  }
}

// 获取单个订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const order = await prisma.order.findUnique({
      where: {
        id: id
      },
      include: {
        product: {
          include: {
            seller: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有买家或卖家可以查看订单
    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单' },
        { status: 403 }
      )
    }

    return NextResponse.json(order)

  } catch (error) {
    console.error('Get order error:', error)
    return NextResponse.json(
      { error: '获取订单详情失败' },
      { status: 500 }
    )
  }
}

// 更新订单状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const {
      action,
      status,
      paymentScreenshot,
      paymentTxHash,
      trackingNumber,
      shippingCompany,
      refundReason,
      receivedAt,
      shippingAddress,
      paymentMethod
    } = body

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        product: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    let updateData: any = {}

    switch (action) {
      case 'upload_payment':
        // 买家上传支付凭证
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PENDING_PAYMENT') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'PAID',
          paymentScreenshot,
          paymentTxHash,
          paymentConfirmed: false
        }
        break

      case 'confirm_payment':
        // 管理员确认支付（这里简化为卖家确认）
        if (order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PAID') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          paymentConfirmed: true
        }
        // 发送支付确认系统消息
        await handlePaymentConfirmed(order.id, order.buyerId)
        break

      case 'ship':
        // 卖家发货
        if (order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PAID' || !order.paymentConfirmed) {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        if (!trackingNumber) {
          return NextResponse.json(
            { error: '请提供快递单号' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'SHIPPED',
          trackingNumber,
          shippingCompany: shippingCompany || '未指定'
        }
        break

      case 'confirm_received':
        // 买家确认收货
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'SHIPPED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'COMPLETED',
          receivedAt: new Date()
        }
        break

      case 'request_refund':
        // 买家申请退款
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (!['PAID', 'SHIPPED'].includes(order.status)) {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'REFUND_REQUESTED',
          refundReason: refundReason || '买家申请退款'
        }
        break

      case 'cancel':
        // 取消订单
        if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PENDING_PAYMENT') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'CANCELLED'
        }

        // 恢复商品库存并检查是否需要重新上架
        await prisma.$transaction(async (tx) => {
          // 恢复库存
          await tx.product.update({
            where: { id: order.productId },
            data: {
              stock: {
                increment: 1 // 假设每个订单数量为1
              }
            }
          })

          // 检查商品是否从SOLD_OUT状态恢复为AVAILABLE
          const product = await tx.product.findUnique({
            where: { id: order.productId },
            select: {
              id: true,
              title: true,
              stock: true,
              status: true
            }
          })

          // 如果商品之前是SOLD_OUT状态且现在有库存，自动重新上架
          if (product && product.status === 'SOLD_OUT' && product.stock > 0) {
            await tx.product.update({
              where: { id: order.productId },
              data: {
                status: 'AVAILABLE'
              }
            })
            console.log(`商品 ${product.title} (ID: ${order.productId}) 库存恢复，已自动重新上架`)
          }
        })
        break

      case 'update_status':
        // 直接更新订单状态（用于简单的状态更新）
        if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }

        if (!status) {
          return NextResponse.json(
            { error: '请提供要更新的状态' },
            { status: 400 }
          )
        }

        // 根据状态和用户角色进行权限检查
        if (status === 'CANCELLED' && order.buyerId === session.user.id && order.status === 'PENDING_PAYMENT') {
          updateData = { status: 'CANCELLED' }
        } else if (status === 'COMPLETED' && order.buyerId === session.user.id && ['SHIPPED', 'DELIVERED'].includes(order.status)) {
          updateData = {
            status: 'COMPLETED',
            receivedAt: receivedAt ? new Date(receivedAt) : new Date()
          }
        } else if (status === 'SHIPPED' && order.sellerId === session.user.id && order.status === 'PAID') {
          updateData = {
            status: 'SHIPPED',
            shippedAt: new Date(),
            ...(trackingNumber && { trackingNumber }),
            ...(shippingCompany && { shippingCompany })
          }
        } else {
          return NextResponse.json(
            { error: '无权限执行此状态更新或状态转换不合法' },
            { status: 403 }
          )
        }
        break

      case 'update_info':
        // 更新订单信息（用于信息收集页面）
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }

        if (!['DRAFT', 'PENDING_PAYMENT'].includes(order.status)) {
          return NextResponse.json(
            { error: '订单状态不允许修改信息' },
            { status: 400 }
          )
        }

        updateData = {
          ...(shippingAddress && { shippingAddress }),
          ...(paymentMethod && { paymentMethod }),
          ...(status && { status })
        }
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            description: true,
            price: true,
            images: true,
            category: true,
            condition: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // 记录操作日志
    const actionMap: Record<string, string> = {
      'upload_payment': 'UPLOAD_PAYMENT',
      'confirm_payment': 'CONFIRM_PAYMENT',
      'ship': 'SHIP_ORDER',
      'confirm_received': 'CONFIRM_RECEIPT',
      'request_refund': 'REQUEST_REFUND',
      'cancel': 'CANCEL_ORDER',
      'update_status': 'UPDATE_STATUS',
      'update_info': 'UPDATE_INFO'
    }

    const logAction = actionMap[action] || 'UPDATE_ORDER'
    const logDescription = getLogDescription(action, updateData, order.status, updatedOrder.status)

    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: logAction,
        description: logDescription
      }
    })

    // 发送订单状态变化的系统消息（除了支付确认，因为已经单独处理）
    if (action !== 'confirm_payment') {
      await handleOrderStatusChange(
        updatedOrder.id,
        updatedOrder.status,
        updatedOrder.buyerId,
        updatedOrder.sellerId
      )
    }

    // 发送实时通知
    if (action === 'confirm_payment') {
      await sendPaymentConfirmedNotification(updatedOrder.id, updatedOrder.buyerId)
    } else {
      await sendOrderStatusNotification(
        updatedOrder.id,
        updatedOrder.status,
        updatedOrder.buyerId,
        updatedOrder.sellerId
      )
    }

    return NextResponse.json(updatedOrder)

  } catch (error) {
    console.error('Update order error:', error)
    return NextResponse.json(
      { error: '更新订单失败' },
      { status: 500 }
    )
  }
}
