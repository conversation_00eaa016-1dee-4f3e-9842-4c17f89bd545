import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取订单操作日志
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    
    // 首先检查用户是否有权限查看此订单
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        buyerId: true,
        sellerId: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有买家或卖家可以查看订单日志
    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单日志' },
        { status: 403 }
      )
    }

    // 获取订单日志
    const logs = await prisma.orderLog.findMany({
      where: { orderId: id },
      include: {
        operator: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({ logs })

  } catch (error) {
    console.error('获取订单日志失败:', error)
    return NextResponse.json(
      { error: '获取订单日志失败' },
      { status: 500 }
    )
  }
}
