import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 延长收货时间
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    
    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        id: true,
        buyerId: true,
        status: true,
        autoConfirmAt: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有买家可以延长收货时间
    if (order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 检查订单状态
    if (!['SHIPPED', 'DELIVERED'].includes(order.status)) {
      return NextResponse.json(
        { error: '当前订单状态不允许延长收货时间' },
        { status: 400 }
      )
    }

    // 延长7天
    const newAutoConfirmAt = new Date(order.autoConfirmAt || new Date())
    newAutoConfirmAt.setDate(newAutoConfirmAt.getDate() + 7)

    // 更新订单
    await prisma.order.update({
      where: { id },
      data: {
        autoConfirmAt: newAutoConfirmAt
      }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: 'EXTEND_RECEIPT_TIME',
        description: '买家延长收货时间7天'
      }
    })

    return NextResponse.json({
      success: true,
      message: '收货时间已延长7天',
      newAutoConfirmAt
    })

  } catch (error) {
    console.error('延长收货时间失败:', error)
    return NextResponse.json(
      { error: '延长收货时间失败' },
      { status: 500 }
    )
  }
}
