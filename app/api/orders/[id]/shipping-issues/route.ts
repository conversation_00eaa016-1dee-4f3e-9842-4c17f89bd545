import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 处理发货异常
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { 
      issueType, 
      reason, 
      estimatedResolutionDate, 
      alternativeSolution,
      notifyBuyer = true 
    } = body

    // 验证必填字段
    if (!issueType || !reason) {
      return NextResponse.json(
        { error: '请提供异常类型和原因' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        id: true,
        sellerId: true,
        buyerId: true,
        status: true,
        orderNumber: true,
        product: {
          select: {
            title: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有卖家可以报告发货异常
    if (order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 检查订单状态
    if (!['PAID', 'SHIPPED'].includes(order.status)) {
      return NextResponse.json(
        { error: '当前订单状态不允许报告发货异常' },
        { status: 400 }
      )
    }

    // 创建发货异常记录
    const shippingIssue = await prisma.shippingIssue.create({
      data: {
        orderId: id,
        sellerId: session.user.id,
        issueType,
        reason: reason.trim(),
        estimatedResolutionDate: estimatedResolutionDate ? new Date(estimatedResolutionDate) : null,
        alternativeSolution: alternativeSolution?.trim() || null,
        status: 'REPORTED',
        reportedAt: new Date()
      }
    })

    // 更新订单状态（如果需要）
    let orderUpdateData: any = {}
    
    if (issueType === 'OUT_OF_STOCK') {
      // 缺货情况，可能需要取消订单或等待补货
      orderUpdateData.status = 'PENDING_STOCK'
    } else if (issueType === 'SHIPPING_DELAY') {
      // 发货延迟，更新预计发货时间
      if (estimatedResolutionDate) {
        orderUpdateData.estimatedShippingDate = new Date(estimatedResolutionDate)
      }
    }

    if (Object.keys(orderUpdateData).length > 0) {
      await prisma.order.update({
        where: { id },
        data: orderUpdateData
      })
    }

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: 'REPORT_SHIPPING_ISSUE',
        description: `报告发货异常：${getIssueTypeText(issueType)} - ${reason.trim()}`
      }
    })

    // 发送通知给买家（如果需要）
    if (notifyBuyer) {
      // TODO: 实现买家通知功能
      // await sendShippingIssueNotification(order.buyerId, shippingIssue)
    }

    return NextResponse.json({
      success: true,
      message: '发货异常已记录',
      issueId: shippingIssue.id
    })

  } catch (error) {
    console.error('处理发货异常失败:', error)
    return NextResponse.json(
      { error: '处理发货异常失败' },
      { status: 500 }
    )
  }
}

// 获取发货异常记录
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取订单信息以验证权限
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        sellerId: true,
        buyerId: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：买家和卖家都可以查看
    if (order.sellerId !== session.user.id && order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单的异常记录' },
        { status: 403 }
      )
    }

    // 获取发货异常记录
    const issues = await prisma.shippingIssue.findMany({
      where: { orderId: id },
      include: {
        seller: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        reportedAt: 'desc'
      }
    })

    return NextResponse.json({ issues })

  } catch (error) {
    console.error('获取发货异常记录失败:', error)
    return NextResponse.json(
      { error: '获取发货异常记录失败' },
      { status: 500 }
    )
  }
}

// 更新发货异常状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { issueId, status, resolution, actualResolutionDate } = body

    if (!issueId || !status) {
      return NextResponse.json(
        { error: '请提供异常ID和状态' },
        { status: 400 }
      )
    }

    // 获取异常记录
    const issue = await prisma.shippingIssue.findUnique({
      where: { id: issueId },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true
          }
        }
      }
    })

    if (!issue) {
      return NextResponse.json(
        { error: '异常记录不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有卖家可以更新异常状态
    if (issue.order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限更新此异常记录' },
        { status: 403 }
      )
    }

    // 更新异常记录
    const updatedIssue = await prisma.shippingIssue.update({
      where: { id: issueId },
      data: {
        status,
        resolution: resolution?.trim() || null,
        actualResolutionDate: actualResolutionDate ? new Date(actualResolutionDate) : null,
        resolvedAt: status === 'RESOLVED' ? new Date() : null
      }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: 'UPDATE_SHIPPING_ISSUE',
        description: `更新发货异常状态：${status}${resolution ? ` - ${resolution}` : ''}`
      }
    })

    return NextResponse.json({
      success: true,
      message: '异常状态已更新',
      issue: updatedIssue
    })

  } catch (error) {
    console.error('更新发货异常状态失败:', error)
    return NextResponse.json(
      { error: '更新发货异常状态失败' },
      { status: 500 }
    )
  }
}

// 辅助函数：获取异常类型文本
function getIssueTypeText(issueType: string): string {
  const typeMap: Record<string, string> = {
    'OUT_OF_STOCK': '缺货',
    'SHIPPING_DELAY': '发货延迟',
    'DAMAGED_GOODS': '商品损坏',
    'LOGISTICS_ISSUE': '物流问题',
    'OTHER': '其他异常'
  }
  return typeMap[issueType] || issueType
}
