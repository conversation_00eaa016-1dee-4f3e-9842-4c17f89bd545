import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取订单发货信息
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const order = await prisma.order.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        trackingNumber: true,
        shippingCompany: true,
        shippingProofText: true,
        shippingProofImages: true,
        shippedAt: true,
        sellerId: true,
        buyerId: true,
        seller: {
          select: {
            name: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证权限：只有买家和卖家可以查看发货信息
    if (order.sellerId !== session.user.id && order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        orderId: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        trackingNumber: order.trackingNumber,
        shippingCompany: order.shippingCompany,
        shippingProofText: order.shippingProofText,
        shippingProofImages: order.shippingProofImages,
        shippedAt: order.shippedAt,
        sellerName: order.seller.name
      }
    })

  } catch (error) {
    console.error('获取发货信息失败:', error)
    return NextResponse.json(
      { error: '获取发货信息失败' },
      { status: 500 }
    )
  }
}

// 更新发货信息
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      type, // 'text' | 'images' | 'direct'
      trackingNumber,
      shippingCompany,
      shippingProofText,
      shippingProofImages
    } = body

    // 验证必填字段
    if (!type || !['text', 'images', 'direct'].includes(type)) {
      return NextResponse.json(
        { error: '发货类型无效' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        status: true,
        sellerId: true,
        orderNumber: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证权限：只有卖家可以发货
    if (order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '只有卖家可以发货' },
        { status: 403 }
      )
    }

    // 验证订单状态
    const validStatuses = ['PAID', 'PENDING_SHIPMENT', 'PROCESSING']
    if (!validStatuses.includes(order.status)) {
      return NextResponse.json(
        { error: '订单状态不允许发货操作' },
        { status: 400 }
      )
    }

    // 根据发货类型验证参数
    if (type === 'text' && !shippingProofText) {
      return NextResponse.json(
        { error: '请输入发货凭证文字' },
        { status: 400 }
      )
    }

    if (type === 'images' && (!shippingProofImages || !Array.isArray(shippingProofImages) || shippingProofImages.length === 0)) {
      return NextResponse.json(
        { error: '请上传发货凭证图片' },
        { status: 400 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      status: 'SHIPPED',
      shippedAt: new Date()
    }

    if (trackingNumber) {
      updateData.trackingNumber = trackingNumber
    }

    if (shippingCompany) {
      updateData.shippingCompany = shippingCompany
    }

    if (type === 'text' && shippingProofText) {
      updateData.shippingProofText = shippingProofText
    }

    if (type === 'images' && shippingProofImages) {
      updateData.shippingProofImages = shippingProofImages
    }

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        orderNumber: true,
        status: true,
        shippedAt: true
      }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: params.id,
        operatorId: session.user.id,
        action: 'SHIP_ORDER',
        description: `卖家发货 - 类型: ${type === 'text' ? '文字凭证' : type === 'images' ? '图片凭证' : '直接确认'}`
      }
    })

    return NextResponse.json({
      success: true,
      message: '发货成功',
      data: {
        orderId: updatedOrder.id,
        orderNumber: updatedOrder.orderNumber,
        status: updatedOrder.status,
        shippedAt: updatedOrder.shippedAt
      }
    })

  } catch (error) {
    console.error('发货失败:', error)
    return NextResponse.json(
      { error: '发货失败' },
      { status: 500 }
    )
  }
}
