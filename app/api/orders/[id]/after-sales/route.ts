import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建售后申请
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { 
      type, 
      reason, 
      description, 
      requestedAmount, 
      images 
    } = body

    // 验证必填字段
    if (!type || !reason || !description?.trim()) {
      return NextResponse.json(
        { error: '请填写完整的申请信息' },
        { status: 400 }
      )
    }

    // 验证售后类型
    if (!['REFUND', 'EXCHANGE', 'REPAIR'].includes(type)) {
      return NextResponse.json(
        { error: '无效的售后类型' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        id: true,
        buyerId: true,
        sellerId: true,
        status: true,
        totalAmount: true,
        productPrice: true,
        orderNumber: true,
        product: {
          select: {
            title: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有买家可以申请售后
    if (order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限申请此订单的售后' },
        { status: 403 }
      )
    }

    // 检查订单状态
    if (!['SHIPPED', 'DELIVERED', 'COMPLETED'].includes(order.status)) {
      return NextResponse.json(
        { error: '当前订单状态不支持申请售后' },
        { status: 400 }
      )
    }

    // 检查是否已有未完成的售后申请
    const existingRequest = await prisma.afterSalesRequest.findFirst({
      where: {
        orderId: id,
        status: { in: ['PENDING', 'APPROVED', 'PROCESSING'] }
      }
    })

    if (existingRequest) {
      return NextResponse.json(
        { error: '该订单已有进行中的售后申请' },
        { status: 400 }
      )
    }

    // 验证退款金额
    if (type === 'REFUND') {
      if (!requestedAmount || requestedAmount <= 0 || requestedAmount > order.totalAmount) {
        return NextResponse.json(
          { error: '退款金额不正确' },
          { status: 400 }
        )
      }
    }

    // 创建售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.create({
      data: {
        orderId: id,
        buyerId: order.buyerId,
        sellerId: order.sellerId,
        type,
        reason,
        description: description.trim(),
        requestedAmount: type === 'REFUND' ? requestedAmount : null,
        images: images && images.length > 0 ? images : null,
        status: 'PENDING'
      }
    })

    // 创建操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: 'CREATE_AFTER_SALES',
        description: `申请售后：${getTypeText(type)} - ${reason}`
      }
    })

    // TODO: 发送通知给卖家
    // await sendAfterSalesNotification(order.sellerId, afterSalesRequest)

    return NextResponse.json({
      success: true,
      message: '售后申请提交成功',
      requestId: afterSalesRequest.id
    })

  } catch (error) {
    console.error('创建售后申请失败:', error)
    return NextResponse.json(
      { error: '创建售后申请失败' },
      { status: 500 }
    )
  }
}

// 获取订单的售后申请列表
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取订单信息以验证权限
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        buyerId: true,
        sellerId: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：买家和卖家都可以查看
    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单的售后申请' },
        { status: 403 }
      )
    }

    // 获取售后申请列表
    const afterSalesRequests = await prisma.afterSalesRequest.findMany({
      where: { orderId: id },
      include: {
        buyer: {
          select: {
            name: true
          }
        },
        seller: {
          select: {
            name: true
          }
        },
        messages: {
          include: {
            sender: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({ requests: afterSalesRequests })

  } catch (error) {
    console.error('获取售后申请列表失败:', error)
    return NextResponse.json(
      { error: '获取售后申请列表失败' },
      { status: 500 }
    )
  }
}

// 更新售后申请状态（卖家操作）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { 
      requestId, 
      action, 
      response, 
      refundAmount, 
      exchangeProductId 
    } = body

    if (!requestId || !action) {
      return NextResponse.json(
        { error: '请提供申请ID和操作类型' },
        { status: 400 }
      )
    }

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id: requestId },
      include: {
        order: {
          select: {
            sellerId: true,
            totalAmount: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有卖家可以处理
    if (afterSalesRequest.order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限处理此售后申请' },
        { status: 403 }
      )
    }

    // 检查状态
    if (afterSalesRequest.status !== 'PENDING') {
      return NextResponse.json(
        { error: '该售后申请已被处理' },
        { status: 400 }
      )
    }

    let updateData: any = {
      sellerResponse: response?.trim() || null,
      sellerResponseAt: new Date()
    }

    if (action === 'APPROVE') {
      updateData.status = 'APPROVED'
      updateData.processedAt = new Date()
      
      if (afterSalesRequest.type === 'REFUND' && refundAmount) {
        updateData.refundAmount = refundAmount
      }
      
      if (afterSalesRequest.type === 'EXCHANGE' && exchangeProductId) {
        updateData.exchangeProductId = exchangeProductId
      }
    } else if (action === 'REJECT') {
      updateData.status = 'REJECTED'
      updateData.processedAt = new Date()
    }

    // 更新售后申请
    const updatedRequest = await prisma.afterSalesRequest.update({
      where: { id: requestId },
      data: updateData
    })

    // 创建操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: `${action}_AFTER_SALES`,
        description: `${action === 'APPROVE' ? '同意' : '拒绝'}售后申请：${getTypeText(afterSalesRequest.type)}`
      }
    })

    // TODO: 发送通知给买家
    // await sendAfterSalesUpdateNotification(afterSalesRequest.buyerId, updatedRequest)

    return NextResponse.json({
      success: true,
      message: `售后申请已${action === 'APPROVE' ? '同意' : '拒绝'}`,
      request: updatedRequest
    })

  } catch (error) {
    console.error('更新售后申请失败:', error)
    return NextResponse.json(
      { error: '更新售后申请失败' },
      { status: 500 }
    )
  }
}

// 辅助函数：获取售后类型文本
function getTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'REFUND': '退款',
    'EXCHANGE': '换货',
    'REPAIR': '维修'
  }
  return typeMap[type] || type
}
