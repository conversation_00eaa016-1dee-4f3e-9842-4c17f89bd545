import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 申请退款
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { reason } = body

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json(
        { error: '请输入退款原因' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        id: true,
        buyerId: true,
        sellerId: true,
        status: true,
        totalAmount: true,
        refundReason: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有买家可以申请退款
    if (order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 检查订单状态
    if (!['PAID', 'SHIPPED', 'DELIVERED'].includes(order.status)) {
      return NextResponse.json(
        { error: '当前订单状态不允许申请退款' },
        { status: 400 }
      )
    }

    // 检查是否已经申请过退款
    if (order.refundReason) {
      return NextResponse.json(
        { error: '已经申请过退款，请勿重复申请' },
        { status: 400 }
      )
    }

    // 更新订单状态
    await prisma.order.update({
      where: { id },
      data: {
        status: 'REFUND_REQUESTED',
        refundReason: reason.trim(),
        refundAmount: order.totalAmount
      }
    })

    // 记录操作日志
    await prisma.orderLog.create({
      data: {
        orderId: id,
        operatorId: session.user.id,
        action: 'REQUEST_REFUND',
        description: `买家申请退款，原因：${reason.trim()}`
      }
    })

    // TODO: 发送通知给卖家
    // await sendRefundRequestNotification(order.sellerId, id)

    return NextResponse.json({
      success: true,
      message: '退款申请已提交，请等待卖家处理'
    })

  } catch (error) {
    console.error('申请退款失败:', error)
    return NextResponse.json(
      { error: '申请退款失败' },
      { status: 500 }
    )
  }
}
