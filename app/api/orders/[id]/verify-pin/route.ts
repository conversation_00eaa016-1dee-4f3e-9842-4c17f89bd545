import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { verifyPaymentPin, regeneratePinForOrder } from '@/lib/payment-pin'
import { sendOrderStatusNotification } from '@/lib/notifications'

// 验证支付PIN码
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params
    const body = await request.json()
    const { pin, orderNumber } = body

    // 验证输入
    if (!pin || !orderNumber) {
      return NextResponse.json(
        { error: 'PIN码和币安订单号不能为空' },
        { status: 400 }
      )
    }

    // 获取用户IP和User-Agent
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // 执行PIN验证
    const verificationResult = await verifyPaymentPin(
      orderId,
      pin,
      orderNumber,
      ipAddress,
      userAgent
    )

    if (verificationResult.success) {
      // 验证成功，发送通知
      try {
        const order = await prisma.order.findUnique({
          where: { id: orderId },
          include: {
            buyer: true,
            seller: true
          }
        })

        if (order) {
          await sendOrderStatusNotification(
            orderId,
            'PAID',
            order.buyerId,
            order.sellerId
          )
        }
      } catch (notificationError) {
        console.error('Failed to send notification:', notificationError)
        // 通知失败不影响验证结果
      }

      return NextResponse.json({
        success: true,
        message: verificationResult.message,
        status: verificationResult.status
      })
    } else {
      return NextResponse.json({
        success: false,
        message: verificationResult.message,
        status: verificationResult.status
      }, { 
        status: verificationResult.status === 'TOO_MANY_ATTEMPTS' ? 429 : 400 
      })
    }

  } catch (error) {
    console.error('PIN verification error:', error)
    return NextResponse.json(
      { error: '验证失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 重新生成PIN码
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params

    // 验证用户权限
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user || user.id !== order.buyerId) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 重新生成PIN码
    const newPin = await regeneratePinForOrder(orderId)

    return NextResponse.json({
      success: true,
      message: 'PIN码已重新生成',
      pin: newPin
    })

  } catch (error) {
    console.error('PIN regeneration error:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: '重新生成PIN码失败' },
      { status: 500 }
    )
  }
}

// 获取PIN码信息（不返回实际PIN码，只返回状态）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params

    // 验证用户权限
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user || user.id !== order.buyerId) {
      return NextResponse.json(
        { error: '无权限查看此订单信息' },
        { status: 403 }
      )
    }

    // 返回PIN状态信息（包含实际PIN码用于显示）
    return NextResponse.json({
      hasPinCode: !!order.paymentPin,
      pinCode: order.paymentPin, // 返回实际PIN码用于显示
      pinExpiry: order.paymentPinExpiry,
      pinUsed: order.paymentPinUsed,
      verificationAttempts: order.verificationAttempts,
      maxAttempts: 5,
      canRegenerate: order.status === 'PENDING_PAYMENT' && !order.paymentPinUsed,
      recentVerifications: [] // 暂时返回空数组，避免数据库关系问题
    })

  } catch (error) {
    console.error('Get PIN info error:', error)
    return NextResponse.json(
      { error: '获取PIN信息失败' },
      { status: 500 }
    )
  }
}
