import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params
    const body = await request.json()
    const { paymentMethod } = body

    if (!paymentMethod) {
      return NextResponse.json(
        { error: '缺少支付方式' },
        { status: 400 }
      )
    }

    // 验证支付方式
    const validMethods = ['BINANCE_PAY', 'BNB_CHAIN']
    if (!validMethods.includes(paymentMethod)) {
      return NextResponse.json(
        { error: '无效的支付方式' },
        { status: 400 }
      )
    }

    // 查找订单
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user || user.id !== order.buyerId) {
      return NextResponse.json(
        { error: '无权限修改此订单' },
        { status: 403 }
      )
    }

    // 验证订单状态
    if (order.status !== 'PENDING_PAYMENT') {
      return NextResponse.json(
        { error: '订单状态不允许修改支付方式' },
        { status: 400 }
      )
    }

    // 更新支付方式
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        paymentMethod
      },
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    return NextResponse.json({
      success: true,
      order: updatedOrder,
      message: '支付方式更新成功'
    })

  } catch (error) {
    console.error('Update payment method error:', error)
    return NextResponse.json(
      { error: '更新支付方式失败' },
      { status: 500 }
    )
  }
}
