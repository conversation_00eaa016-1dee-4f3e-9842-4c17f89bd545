import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { type, amount } = await request.json()

    if (!type || !amount || amount <= 0) {
      return NextResponse.json({ error: '参数无效' }, { status: 400 })
    }

    if (!['deposit', 'withdraw'].includes(type)) {
      return NextResponse.json({ error: '操作类型无效' }, { status: 400 })
    }

    const userId = session.user.id

    // 使用事务确保操作的原子性
    const result = await prisma.$transaction(async (tx) => {
      // 获取用户当前余额
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          depositBalance: true
        }
      })

      if (!user) {
        throw new Error('用户不存在')
      }

      // 计算当前冻结资金状态
      const guaranteeDeposits = await tx.fundTransaction.aggregate({
        where: {
          userId: userId,
          type: 'GUARANTEE_DEPOSIT'
        },
        _sum: { amount: true }
      })

      const guaranteeWithdrawals = await tx.fundTransaction.aggregate({
        where: {
          userId: userId,
          type: 'GUARANTEE_WITHDRAWAL'
        },
        _sum: { amount: true }
      })

      const currentFrozenFunds = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)

      // 计算活跃担保订单的锁定金额
      const activeGuarantees = await tx.order.aggregate({
        where: {
          mediatorId: userId,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS']
          }
        },
        _sum: { escrowAmount: true }
      })

      const lockedAmount = Math.abs(activeGuarantees._sum.escrowAmount || 0)
      const availableAmount = Math.max(0, currentFrozenFunds - lockedAmount)

      if (type === 'deposit') {
        // 充值到冻结资金
        if (user.depositBalance < amount) {
          throw new Error('可用保证金余额不足')
        }

        // 更新用户余额
        await tx.user.update({
          where: { id: userId },
          data: {
            depositBalance: user.depositBalance - amount
          }
        })

        // 创建担保充值交易记录
        await tx.fundTransaction.create({
          data: {
            userId: userId,
            type: 'GUARANTEE_DEPOSIT',
            amount: -amount, // 负数表示从保证金转出
            description: '充值到冻结资金'
          }
        })

        return {
          success: true,
          message: '充值成功',
          newBalance: user.depositBalance - amount,
          newFrozenFunds: currentFrozenFunds + amount
        }

      } else if (type === 'withdraw') {
        // 从冻结资金提现
        if (amount > availableAmount) {
          throw new Error(`可提现金额不足，最多可提现 ${availableAmount.toFixed(2)} USDT`)
        }

        // 更新用户余额
        await tx.user.update({
          where: { id: userId },
          data: {
            depositBalance: user.depositBalance + amount
          }
        })

        // 创建担保提现交易记录
        await tx.fundTransaction.create({
          data: {
            userId: userId,
            type: 'GUARANTEE_WITHDRAWAL',
            amount: amount, // 正数表示转入保证金
            description: '从冻结资金提现'
          }
        })

        return {
          success: true,
          message: '提现成功',
          newBalance: user.depositBalance + amount,
          newFrozenFunds: currentFrozenFunds - amount
        }
      }
    })

    return NextResponse.json(result)

  } catch (error) {
    console.error('冻结资金操作失败:', error)

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}
