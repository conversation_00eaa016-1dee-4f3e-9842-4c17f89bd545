import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'



export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const userId = session.user.id

    // 获取用户基本信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        depositBalance: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 })
    }

    // 计算冻结资金总额（担保池金额）
    const guaranteeTransactions = await prisma.fundTransaction.aggregate({
      where: {
        userId: userId,
        type: 'GUARANTEE_DEPOSIT'
      },
      _sum: {
        amount: true
      }
    })

    const guaranteeWithdrawals = await prisma.fundTransaction.aggregate({
      where: {
        userId: userId,
        type: 'GUARANTEE_WITHDRAWAL'
      },
      _sum: {
        amount: true
      }
    })

    const totalFrozenFunds = Math.abs(guaranteeTransactions._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)

    // 获取活跃担保订单（锁定的资金）
    const activeGuarantees = await prisma.order.findMany({
      where: {
        mediatorId: userId,
        status: {
          in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS']
        }
      },
      select: {
        id: true,
        orderNumber: true,
        escrowAmount: true,
        status: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 计算锁定金额
    const lockedAmount = activeGuarantees.reduce((sum, order) => sum + (order.escrowAmount || 0), 0)

    // 计算可用金额
    const availableAmount = Math.max(0, totalFrozenFunds - lockedAmount)

    // 获取最近的冻结资金交易记录
    const recentTransactions = await prisma.fundTransaction.findMany({
      where: {
        userId: userId,
        type: {
          in: ['GUARANTEE_DEPOSIT', 'GUARANTEE_WITHDRAWAL']
        }
      },
      select: {
        id: true,
        type: true,
        amount: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    // 格式化交易记录
    const formattedTransactions = recentTransactions.map(transaction => ({
      id: transaction.id,
      type: transaction.type === 'GUARANTEE_DEPOSIT' ? 'DEPOSIT' : 'WITHDRAW',
      amount: Math.abs(transaction.amount),
      status: 'COMPLETED', // 默认状态
      createdAt: transaction.createdAt
    }))

    // 格式化活跃担保订单
    const formattedGuarantees = activeGuarantees.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      amount: order.escrowAmount || 0,
      status: order.status,
      createdAt: order.createdAt
    }))

    return NextResponse.json({
      balance: {
        total: totalFrozenFunds,
        available: availableAmount,
        locked: lockedAmount
      },
      activeGuarantees: formattedGuarantees,
      transactions: formattedTransactions
    })

  } catch (error) {
    console.error('获取冻结资金数据失败:', error)
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}
