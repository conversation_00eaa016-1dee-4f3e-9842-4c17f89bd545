/**
 * 提现管理API
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  createWithdrawal, 
  getUserWithdrawals,
  cancelWithdrawal 
} from '@/lib/adapters/withdrawal-adapter'

// 获取用户提现记录
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')

    const withdrawals = await getUserWithdrawals(session.user.id, limit)

    return NextResponse.json({
      withdrawals,
      total: withdrawals.length
    })

  } catch (error) {
    console.error('获取提现记录失败:', error)
    return NextResponse.json(
      { error: '获取提现记录失败' },
      { status: 500 }
    )
  }
}

// 创建提现申请
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { amount, walletAddress, notes, couponId, useCoupon } = await request.json()

    // 验证参数
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '提现金额必须大于0' },
        { status: 400 }
      )
    }

    if (!walletAddress) {
      return NextResponse.json(
        { error: '钱包地址不能为空' },
        { status: 400 }
      )
    }

    // 设置最小提现金额
    const minWithdrawalAmount = 10 // 最小提现10 USDT
    if (amount < minWithdrawalAmount) {
      return NextResponse.json(
        { error: `最小提现金额为 ${minWithdrawalAmount} USDT` },
        { status: 400 }
      )
    }

    // 计算提现手续费率（考虑减免券）
    let withdrawalFeeRate = 0.01 // 默认1%手续费

    // 如果使用了减免券，设置为0手续费
    if (useCoupon && couponId) {
      withdrawalFeeRate = 0
    } else {
      // 阶梯手续费计算
      if (amount <= 100) {
        withdrawalFeeRate = 0.5 / amount // 固定0.5 USDT转换为比例
      } else if (amount <= 500) {
        withdrawalFeeRate = 0.005 // 0.5%
      } else if (amount <= 2000) {
        withdrawalFeeRate = 0.003 // 0.3%
      } else {
        withdrawalFeeRate = 0 // 大额免手续费
      }
    }

    const result = await createWithdrawal({
      userId: session.user.id,
      amount,
      walletAddress,
      withdrawalFeeRate,
      notes,
      metadata: {
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        couponId: couponId || null,
        usedCoupon: useCoupon || false
      }
    })

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      withdrawalId: result.withdrawalId,
      freezeId: result.freezeId,
      availableBalance: result.availableBalance
    })

  } catch (error) {
    console.error('创建提现申请失败:', error)
    return NextResponse.json(
      { error: '创建提现申请失败' },
      { status: 500 }
    )
  }
}

// 取消提现申请
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const withdrawalId = searchParams.get('id')
    const reason = searchParams.get('reason') || '用户主动取消'

    if (!withdrawalId) {
      return NextResponse.json(
        { error: '提现ID不能为空' },
        { status: 400 }
      )
    }

    const result = await cancelWithdrawal(
      withdrawalId,
      reason,
      session.user.id
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      withdrawalId: result.withdrawalId
    })

  } catch (error) {
    console.error('取消提现申请失败:', error)
    return NextResponse.json(
      { error: '取消提现申请失败' },
      { status: 500 }
    )
  }
}
