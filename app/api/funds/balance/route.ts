/**
 * 资金余额API - 支持新的改革方案
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { getAvailableBalance } from '@/lib/fund-flow'

// 获取用户资金余额信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取用户基本信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true,
        creditScore: true,
        isGuarantor: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 计算冻结资金总额（担保池金额）- 使用真实交易记录
    const guaranteeDeposits = await prisma.fundTransaction.aggregate({
      where: {
        userId: userId,
        type: 'GUARANTEE_DEPOSIT'
      },
      _sum: {
        amount: true
      }
    })

    const guaranteeWithdrawals = await prisma.fundTransaction.aggregate({
      where: {
        userId: userId,
        type: 'GUARANTEE_WITHDRAWAL'
      },
      _sum: {
        amount: true
      }
    })

    const guaranteePoolAmount = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)

    // 计算待提现金额 (从提现申请中获取)
    const pendingWithdrawals = await prisma.withdrawal.aggregate({
      where: {
        userId,
        status: {
          in: ['PENDING', 'APPROVED', 'PROCESSING']
        }
      },
      _sum: {
        amount: true
      }
    })

    const pendingWithdrawalAmount = pendingWithdrawals._sum.amount || 0

    // 计算可用余额 (用户保证金余额减去待提现金额)
    const availableBalance = Math.max(0, user.depositBalance - pendingWithdrawalAmount)

    // 计算总余额 = 可用保证金余额 + 冻结资金余额
    const totalBalance = availableBalance + guaranteePoolAmount

    // 获取最近的交易记录 (真实数据)
    const recentTransactions = await prisma.fundTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        type: true,
        amount: true,
        createdAt: true,
        description: true
      }
    })

    // 计算真实的信誉相关数据
    const currentDate = new Date()
    const oneMonthAgo = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, currentDate.getDate())
    const threeMonthsAgo = new Date(currentDate.getFullYear(), currentDate.getMonth() - 3, currentDate.getDate())

    // 计算担保次数 (作为买家或卖家的订单数)
    const guaranteeCount = await prisma.order.count({
      where: {
        OR: [
          { buyerId: userId },
          { sellerId: userId }
        ],
        status: {
          in: ['COMPLETED', 'DELIVERED', 'CONFIRMED']
        }
      }
    })

    // 计算履约率 (已完成订单 / 总订单)
    const totalOrders = await prisma.order.count({
      where: {
        OR: [
          { buyerId: userId },
          { sellerId: userId }
        ]
      }
    })

    const completedOrders = await prisma.order.count({
      where: {
        OR: [
          { buyerId: userId },
          { sellerId: userId }
        ],
        status: {
          in: ['COMPLETED', 'DELIVERED', 'CONFIRMED']
        }
      }
    })

    const fulfillmentRate = totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 100

    // 计算信誉等级 (基于信用分数和履约率)
    const trustLevel = Math.min(5, Math.max(1, Math.floor((user.creditScore + fulfillmentRate) / 40)))

    // 计算月交易量 (最近一个月的交易总额)
    const monthlyOrdersAsBuyer = await prisma.order.aggregate({
      where: {
        buyerId: userId,
        createdAt: { gte: oneMonthAgo },
        status: {
          in: ['COMPLETED', 'DELIVERED', 'CONFIRMED']
        }
      },
      _sum: {
        totalAmount: true
      }
    })

    const monthlyOrdersAsSeller = await prisma.order.aggregate({
      where: {
        sellerId: userId,
        createdAt: { gte: oneMonthAgo },
        status: {
          in: ['COMPLETED', 'DELIVERED', 'CONFIRMED']
        }
      },
      _sum: {
        totalAmount: true
      }
    })

    const monthlyVolume = (monthlyOrdersAsBuyer._sum.totalAmount || 0) + (monthlyOrdersAsSeller._sum.totalAmount || 0)

    // 计算总交易次数
    const totalTransactions = await prisma.order.count({
      where: {
        OR: [
          { buyerId: userId },
          { sellerId: userId }
        ],
        status: {
          in: ['COMPLETED', 'DELIVERED', 'CONFIRMED']
        }
      }
    })

    // 计算综合评分 (信用分数 + 履约率加权)
    const trustScore = Math.round((user.creditScore * 0.7) + (fulfillmentRate * 0.3))

    // 计算总担保金额 (用户参与的所有担保)
    const totalGuaranteeAmount = await prisma.fundTransaction.aggregate({
      where: {
        userId,
        type: 'GUARANTEE',
        createdAt: { gte: threeMonthsAgo }
      },
      _sum: {
        amount: true
      }
    })

    const totalGuaranteeSum = Math.abs(totalGuaranteeAmount._sum.amount || 0)

    // 计算好评数 (模拟数据，实际应从评价系统获取)
    const positiveReviews = Math.floor(completedOrders * 0.85) // 假设85%的订单获得好评

    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        creditScore: user.creditScore,
        isGuarantor: user.isGuarantor,
        trustLevel,
        guaranteeCount,
        fulfillmentRate,
        // 新增字段用于前端计算
        totalOrders,
        successfulOrders: completedOrders,
        positiveReviews
      },
      balance: {
        total: totalBalance,
        available: availableBalance,
        guaranteePool: guaranteePoolAmount,
        pendingWithdrawal: pendingWithdrawalAmount
      },
      statistics: {
        totalGuaranteeAmount: totalGuaranteeSum,
        totalTransactions,
        monthlyVolume,
        trustScore
      },
      recentTransactions
    })

  } catch (error) {
    console.error('获取资金余额失败:', error)
    return NextResponse.json(
      { error: '获取资金余额失败' },
      { status: 500 }
    )
  }
}

// 充值担保金（模拟接口，实际应该通过支付系统）
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { amount, txHash, notes } = await request.json()

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '充值金额必须大于0' },
        { status: 400 }
      )
    }

    const userId = session.user.id

    // 在实际应用中，这里应该验证交易哈希的有效性
    // 这里只是模拟充值过程
    const result = await prisma.$transaction(async (tx) => {
      // 更新用户担保金余额
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          depositBalance: {
            increment: amount
          }
        }
      })

      // 创建充值记录（如果有相关表的话）
      // 这里可以记录充值历史

      return updatedUser
    })

    // 获取更新后的可用余额
    const availableBalance = await getAvailableBalance(userId)

    return NextResponse.json({
      success: true,
      message: '充值成功',
      balance: {
        total: result.depositBalance,
        available: availableBalance
      }
    })

  } catch (error) {
    console.error('充值失败:', error)
    return NextResponse.json(
      { error: '充值失败' },
      { status: 500 }
    )
  }
}
