/**
 * 充值PIN码验证API
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 验证充值PIN码
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: depositId } = await params
    const { pin, txHash, orderNumber } = await request.json()

    // 验证输入
    if (!pin || !pin.trim()) {
      return NextResponse.json(
        { error: '请输入PIN码' },
        { status: 400 }
      )
    }

    // 获取充值记录
    const depositRecord = await prisma.depositRecord.findUnique({
      where: { id: depositId },
      include: {
        user: true
      }
    })

    if (!depositRecord) {
      return NextResponse.json(
        { error: '充值记录不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    if (depositRecord.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此充值记录' },
        { status: 403 }
      )
    }

    // 检查充值状态
    if (depositRecord.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'PIN码已使用，充值已完成' },
        { status: 400 }
      )
    }

    if (depositRecord.status === 'CANCELLED') {
      return NextResponse.json(
        { error: '充值已取消' },
        { status: 400 }
      )
    }

    // 获取PIN码信息
    const metadata = depositRecord.metadata as any
    const storedPin = metadata?.paymentPin
    const pinExpiry = metadata?.paymentPinExpiry

    if (!storedPin || !pinExpiry) {
      return NextResponse.json(
        { error: 'PIN码信息不存在' },
        { status: 400 }
      )
    }

    // 检查PIN码是否过期
    const expiryDate = new Date(pinExpiry)
    if (new Date() > expiryDate) {
      return NextResponse.json(
        { error: 'PIN码已过期，请重新发起充值' },
        { status: 400 }
      )
    }

    // 验证PIN码
    if (pin.trim().toUpperCase() !== storedPin) {
      return NextResponse.json(
        { error: 'PIN码不正确' },
        { status: 400 }
      )
    }

    // 验证交易信息
    let finalTxHash = txHash
    if (depositRecord.method === 'binance_qr' && orderNumber) {
      finalTxHash = orderNumber
    }

    if (!finalTxHash || !finalTxHash.trim()) {
      return NextResponse.json(
        { error: '请提供交易哈希或订单号' },
        { status: 400 }
      )
    }

    // 更新充值记录状态为等待管理员审核
    const updatedRecord = await prisma.depositRecord.update({
      where: { id: depositId },
      data: {
        status: 'PENDING_APPROVAL', // 等待管理员确认
        txHash: finalTxHash.trim(),
        metadata: {
          ...metadata,
          pinUsed: true,
          pinUsedAt: new Date().toISOString(),
          verifiedTxHash: finalTxHash.trim(),
          submittedForApproval: true,
          submittedAt: new Date().toISOString()
        }
      }
    })

    // 不立即更新用户余额，等待管理员确认
    // 不立即记录资金变动，等待管理员确认

    return NextResponse.json({
      success: true,
      message: '充值申请已提交，请等待管理员确认到账。确认后保证金将自动添加到您的账户。',
      depositRecord: {
        id: updatedRecord.id,
        amount: updatedRecord.amount,
        status: updatedRecord.status,
        method: updatedRecord.method,
        txHash: updatedRecord.txHash
      }
    })

  } catch (error) {
    console.error('PIN码验证失败:', error)
    return NextResponse.json(
      { error: 'PIN码验证失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取充值记录和PIN码状态
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: depositId } = await params

    // 获取充值记录
    const depositRecord = await prisma.depositRecord.findUnique({
      where: { id: depositId }
    })

    if (!depositRecord) {
      return NextResponse.json(
        { error: '充值记录不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    if (depositRecord.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限访问此充值记录' },
        { status: 403 }
      )
    }

    const metadata = depositRecord.metadata as any
    const pinExpiry = metadata?.paymentPinExpiry
    const pinUsed = metadata?.pinUsed || false

    return NextResponse.json({
      depositId: depositRecord.id,
      amount: depositRecord.amount,
      method: depositRecord.method,
      status: depositRecord.status,
      hasPinCode: !!metadata?.paymentPin,
      pinExpiry,
      pinUsed,
      walletAddress: metadata?.walletAddress,
      paymentUrl: metadata?.paymentUrl,
      canVerify: depositRecord.status === 'PENDING' && !pinUsed && 
                 pinExpiry && new Date() <= new Date(pinExpiry)
    })

  } catch (error) {
    console.error('获取PIN码状态失败:', error)
    return NextResponse.json(
      { error: '获取PIN码状态失败' },
      { status: 500 }
    )
  }
}

// 重新生成PIN码
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: depositId } = await params

    // 获取充值记录
    const depositRecord = await prisma.depositRecord.findUnique({
      where: { id: depositId }
    })

    if (!depositRecord) {
      return NextResponse.json(
        { error: '充值记录不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    if (depositRecord.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此充值记录' },
        { status: 403 }
      )
    }

    // 检查是否可以重新生成
    if (depositRecord.status !== 'PENDING') {
      return NextResponse.json(
        { error: '当前状态不允许重新生成PIN码' },
        { status: 400 }
      )
    }

    // 生成新的PIN码
    const generatePin = () => {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let pin = ''
      for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        pin += characters[randomIndex]
      }
      return pin
    }

    const newPin = generatePin()
    const newExpiry = new Date()
    newExpiry.setMinutes(newExpiry.getMinutes() + 30)

    // 更新PIN码
    const metadata = depositRecord.metadata as any
    await prisma.depositRecord.update({
      where: { id: depositId },
      data: {
        metadata: {
          ...metadata,
          paymentPin: newPin,
          paymentPinExpiry: newExpiry.toISOString(),
          pinUsed: false,
          regeneratedAt: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'PIN码已重新生成',
      pin: newPin,
      pinExpiry: newExpiry.toISOString()
    })

  } catch (error) {
    console.error('重新生成PIN码失败:', error)
    return NextResponse.json(
      { error: '重新生成PIN码失败' },
      { status: 500 }
    )
  }
}

function getMethodName(method: string): string {
  switch (method) {
    case 'chain':
      return '链上'
    case 'binance_qr':
      return '币安扫码'
    default:
      return '未知'
  }
}
