import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 中间人自动分配API
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { orderAmount, orderId } = await request.json()

    if (!orderAmount || orderAmount < 100) {
      return NextResponse.json(
        { error: '订单金额必须超过100 USDT才能使用托管服务' },
        { status: 400 }
      )
    }

    // 获取所有活跃的中间人
    const activeMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        mediatorDeposit: true
      }
    })

    if (activeMediators.length === 0) {
      return NextResponse.json(
        { error: '暂无可用的中间人，请稍后再试' },
        { status: 404 }
      )
    }

    // 为每个中间人计算可用保证金和活跃订单数
    const mediatorCandidates = []

    for (const mediator of activeMediators) {
      // 获取中间人的完整余额信息
      const mediatorBalance = await prisma.user.findUnique({
        where: { id: mediator.id },
        select: {
          depositBalance: true,
          frozenBalance: true,
          availableBalance: true
        }
      })

      if (!mediatorBalance) {
        continue
      }

      // 计算当前锁定金额（活跃担保订单）
      const activeOrders = await prisma.order.findMany({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        },
        select: {
          escrowAmount: true
        }
      })

      const lockedAmount = activeOrders.reduce((sum, order) => sum + (order.escrowAmount || 0), 0)

      // 使用正确的可用余额计算
      // 可用金额 = 可用余额 - 当前锁定金额
      const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount)

      // 只有可用金额足够的中间人才能参与分配
      if (availableAmount >= orderAmount) {
        mediatorCandidates.push({
          ...mediator,
          totalDeposit: mediatorBalance.depositBalance,
          frozenBalance: mediatorBalance.frozenBalance,
          lockedAmount,
          availableAmount,
          activeOrderCount: activeOrders.length,
          // 计算综合评分用于排序
          score: calculateMediatorScore(mediator, activeOrders.length, availableAmount, orderAmount)
        })
      }
    }

    if (mediatorCandidates.length === 0) {
      return NextResponse.json(
        { 
          error: '暂无保证金充足的中间人可用',
          suggestion: '请稍后再试或联系客服'
        },
        { status: 404 }
      )
    }

    // 按综合评分排序，优先选择评分最高的中间人
    mediatorCandidates.sort((a, b) => b.score - a.score)

    // 选择最优中间人
    const selectedMediator = mediatorCandidates[0]

    // 计算托管费用
    const escrowFee = orderAmount * (selectedMediator.mediatorFeeRate || 0.02)

    return NextResponse.json({
      success: true,
      data: {
        mediator: {
          id: selectedMediator.id,
          name: selectedMediator.name,
          feeRate: selectedMediator.mediatorFeeRate,
          reputation: selectedMediator.mediatorReputation,
          successRate: selectedMediator.mediatorSuccessRate,
          totalOrders: selectedMediator.mediatorTotalOrders,
          availableAmount: selectedMediator.availableAmount,
          activeOrderCount: selectedMediator.activeOrderCount
        },
        escrowFee,
        totalCost: orderAmount + escrowFee,
        assignmentReason: generateAssignmentReason(selectedMediator, mediatorCandidates.length)
      }
    })

  } catch (error) {
    console.error('自动分配中间人失败:', error)
    return NextResponse.json(
      { error: '自动分配中间人失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 计算中间人综合评分
function calculateMediatorScore(
  mediator: any, 
  activeOrderCount: number, 
  availableAmount: number, 
  orderAmount: number
): number {
  let score = 0

  // 信誉度权重 (40%)
  const reputationScore = (mediator.mediatorReputation || 0) / 100 * 40

  // 成功率权重 (30%)
  const successRateScore = (mediator.mediatorSuccessRate || 0) / 100 * 30

  // 负载均衡权重 (20%) - 活跃订单越少分数越高
  const maxOrders = 50 // 假设最大活跃订单数为50
  const loadBalanceScore = Math.max(0, (maxOrders - activeOrderCount) / maxOrders) * 20

  // 资金充足度权重 (10%) - 可用资金越多分数越高
  const fundsSufficiencyScore = Math.min(availableAmount / (orderAmount * 5), 1) * 10

  score = reputationScore + successRateScore + loadBalanceScore + fundsSufficiencyScore

  return score
}

// 生成分配原因说明
function generateAssignmentReason(selectedMediator: any, totalCandidates: number): string {
  const reasons = []

  if (selectedMediator.mediatorReputation >= 90) {
    reasons.push('高信誉度')
  }

  if (selectedMediator.mediatorSuccessRate >= 95) {
    reasons.push('高成功率')
  }

  if (selectedMediator.activeOrderCount <= 5) {
    reasons.push('负载较低')
  }

  if (selectedMediator.availableAmount >= selectedMediator.lockedAmount * 2) {
    reasons.push('资金充足')
  }

  const reasonText = reasons.length > 0 ? reasons.join('、') : '综合评估最优'
  
  return `系统从${totalCandidates}个可用中间人中，基于${reasonText}为您智能分配`
}
