import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 申请成为中间人
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      walletAddress, 
      feeRate, 
      depositAmount, 
      introduction,
      experience 
    } = body

    // 验证输入
    if (!walletAddress || !feeRate || !depositAmount) {
      return NextResponse.json(
        { success: false, error: '请填写完整信息' },
        { status: 400 }
      )
    }

    // 验证费率范围
    if (feeRate < 0.01 || feeRate > 0.30) {
      return NextResponse.json(
        { success: false, error: '服务费率必须在1%-30%之间' },
        { status: 400 }
      )
    }

    // 验证保证金最低要求
    if (depositAmount < 1000) {
      return NextResponse.json(
        { success: false, error: '保证金最低要求为1000 USDT' },
        { status: 400 }
      )
    }

    // 检查用户是否已经是中间人
    const existingUser = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (existingUser?.isMediator) {
      return NextResponse.json(
        { success: false, error: '您已经是中间人' },
        { status: 400 }
      )
    }

    // 检查钱包地址是否已被使用
    const existingWallet = await prisma.user.findFirst({
      where: {
        bnbWalletAddress: walletAddress,
        bnbWalletVerified: true
      }
    })

    if (existingWallet && existingWallet.id !== session.user.id) {
      return NextResponse.json(
        { success: false, error: '该钱包地址已被其他用户使用' },
        { status: 400 }
      )
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        isMediator: true,
        mediatorStatus: 'PENDING',
        mediatorFeeRate: feeRate,
        mediatorDeposit: depositAmount,
        bnbWalletAddress: walletAddress,
        bnbWalletVerified: false, // 需要管理员验证
        mediatorIntroduction: introduction,
        mediatorExperience: experience
      }
    })

    // 创建中间人申请记录
    await prisma.mediatorApplication.create({
      data: {
        userId: session.user.id,
        bnbWalletAddress: walletAddress,
        depositAmount: depositAmount,
        feeRate: feeRate,
        experience: experience || '',
        introduction: introduction || '',
        status: 'PENDING'
      }
    })

    return NextResponse.json({
      success: true,
      message: '中间人申请已提交，等待管理员审核',
      data: {
        id: updatedUser.id,
        mediatorStatus: updatedUser.mediatorStatus,
        mediatorFeeRate: updatedUser.mediatorFeeRate,
        mediatorDeposit: updatedUser.mediatorDeposit
      }
    })
  } catch (error) {
    console.error('申请中间人失败:', error)
    return NextResponse.json(
      { success: false, error: '申请中间人失败' },
      { status: 500 }
    )
  }
}

// 获取中间人申请状态
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorDeposit: true,
        mediatorReputation: true,
        bnbWalletAddress: true,
        bnbWalletVerified: true,
        mediatorVerifiedAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 如果是中间人，获取统计信息
    let stats: {
      totalOrders: number
      totalVolume: number
      completedOrders: number
      activeOrders: number
      successRate: number
    } | null = null
    if (user.isMediator) {
      const orderStats = await prisma.order.aggregate({
        where: {
          mediatorId: session.user.id,
          useEscrow: true
        },
        _count: {
          id: true
        },
        _sum: {
          totalAmount: true
        }
      })

      const completedOrders = await prisma.order.count({
        where: {
          mediatorId: session.user.id,
          useEscrow: true,
          status: 'COMPLETED'
        }
      })

      const activeOrders = await prisma.order.count({
        where: {
          mediatorId: session.user.id,
          useEscrow: true,
          status: {
            in: ['PENDING', 'PAID', 'SHIPPED']
          }
        }
      })

      stats = {
        totalOrders: orderStats._count.id || 0,
        totalVolume: orderStats._sum.totalAmount || 0,
        completedOrders,
        activeOrders,
        successRate: orderStats._count.id > 0 ? (completedOrders / orderStats._count.id) * 100 : 0
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        ...user,
        stats
      }
    })
  } catch (error) {
    console.error('获取中间人状态失败:', error)
    return NextResponse.json(
      { success: false, error: '获取中间人状态失败' },
      { status: 500 }
    )
  }
}
