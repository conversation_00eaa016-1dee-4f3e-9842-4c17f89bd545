import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取中间人的托管订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证用户是否为中间人
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        isMediator: true,
        mediatorStatus: true
      }
    })

    if (!user || !user.isMediator) {
      return NextResponse.json(
        { error: '您不是认证中间人' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const skip = (page - 1) * limit

    const where: any = {
      mediatorId: session.user.id
    }

    if (status) {
      where.status = status
    }

    const [orders, total] = await Promise.all([
      prisma.escrowOrder.findMany({
        where,
        include: {
          order: {
            include: {
              product: {
                select: {
                  title: true,
                  images: true,
                  price: true
                }
              }
            }
          },
          buyer: {
            select: { id: true, name: true, email: true }
          },
          seller: {
            select: { id: true, name: true, email: true }
          },
          disputes: {
            select: {
              id: true,
              status: true,
              reason: true,
              createdAt: true
            }
          },
          chatRoom: {
            select: {
              id: true,
              roomCode: true,
              isActive: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.escrowOrder.count({ where })
    ])

    // 统计信息
    const stats = await prisma.escrowOrder.aggregate({
      where: { mediatorId: session.user.id },
      _count: {
        _all: true
      },
      _sum: {
        amount: true,
        mediatorFee: true
      }
    })

    const statusStats = await prisma.escrowOrder.groupBy({
      by: ['status'],
      where: { mediatorId: session.user.id },
      _count: {
        status: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        statistics: {
          totalOrders: stats._count._all,
          totalAmount: stats._sum.amount || 0,
          totalFees: stats._sum.mediatorFee || 0,
          statusBreakdown: statusStats.reduce((acc, item) => {
            acc[item.status] = item._count.status
            return acc
          }, {} as Record<string, number>)
        }
      }
    })

  } catch (error) {
    console.error('获取托管订单列表失败:', error)
    return NextResponse.json(
      { error: '获取托管订单列表失败' },
      { status: 500 }
    )
  }
}

// 批量操作托管订单
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证用户是否为中间人
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        isMediator: true,
        mediatorStatus: true
      }
    })

    if (!user || !user.isMediator) {
      return NextResponse.json(
        { error: '您不是认证中间人' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { orderIds, action } = body

    if (!orderIds || !Array.isArray(orderIds) || !action) {
      return NextResponse.json(
        { error: '参数错误' },
        { status: 400 }
      )
    }

    let result
    
    switch (action) {
      case 'mark_funded':
        // 批量标记为已到账
        result = await prisma.escrowOrder.updateMany({
          where: {
            id: { in: orderIds },
            mediatorId: session.user.id,
            status: 'PENDING'
          },
          data: {
            status: 'FUNDED',
            fundedAt: new Date()
          }
        })
        break

      case 'mark_completed':
        // 批量完成托管
        const completableOrders = await prisma.escrowOrder.findMany({
          where: {
            id: { in: orderIds },
            mediatorId: session.user.id,
            status: 'DELIVERED'
          }
        })

        if (completableOrders.length > 0) {
          await prisma.$transaction(async (tx) => {
            for (const escrowOrder of completableOrders) {
              // 更新托管状态
              await tx.escrowOrder.update({
                where: { id: escrowOrder.id },
                data: {
                  status: 'COMPLETED',
                  completedAt: new Date()
                }
              })

              // 释放买家冻结资金
              await tx.fundFreeze.updateMany({
                where: {
                  userId: escrowOrder.buyerId,
                  relatedId: escrowOrder.id,
                  relatedType: 'ESCROW_ORDER',
                  status: 'FROZEN'
                },
                data: {
                  status: 'RELEASED'
                }
              })

              // 给卖家打款
              await tx.user.update({
                where: { id: escrowOrder.sellerId },
                data: {
                  depositBalance: { increment: escrowOrder.amount }
                }
              })

              // 给中间人支付费用
              const mediatorFee = escrowOrder.mediatorFee - escrowOrder.platformFee
              await tx.user.update({
                where: { id: escrowOrder.mediatorId },
                data: {
                  depositBalance: { increment: mediatorFee }
                }
              })

              // 创建交易记录
              await tx.fundTransaction.createMany({
                data: [
                  {
                    userId: escrowOrder.sellerId,
                    type: 'ESCROW_RELEASE',
                    amount: escrowOrder.amount,
                    description: '托管订单收款',
                    relatedId: escrowOrder.id,
                    metadata: { relatedType: 'ESCROW_ORDER' }
                  },
                  {
                    userId: escrowOrder.mediatorId,
                    type: 'MEDIATOR_FEE',
                    amount: mediatorFee,
                    description: '中间人服务费',
                    relatedId: escrowOrder.id,
                    metadata: { relatedType: 'ESCROW_ORDER' }
                  }
                ]
              })

              // 更新订单状态
              await tx.order.update({
                where: { id: escrowOrder.orderId },
                data: { status: 'COMPLETED' }
              })
            }
          })

          result = { count: completableOrders.length }
        } else {
          result = { count: 0 }
        }
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `批量操作完成，影响 ${result.count || 0} 个订单`,
      data: result
    })

  } catch (error) {
    console.error('批量操作失败:', error)
    return NextResponse.json(
      { error: '批量操作失败' },
      { status: 500 }
    )
  }
}
