import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { getMediatorRewardStats } from '@/lib/mediator-rewards'

// 获取中间人奖励信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证用户是否为中间人
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        isMediator: true,
        mediatorStatus: true
      }
    })

    if (!user || !user.isMediator) {
      return NextResponse.json(
        { error: '您不是中间人' },
        { status: 403 }
      )
    }

    // 获取奖励统计
    const stats = await getMediatorRewardStats(session.user.id)

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('获取奖励信息失败:', error)
    return NextResponse.json(
      { error: '获取奖励信息失败' },
      { status: 500 }
    )
  }
}

// 手动触发奖励发放（仅限管理员）
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, mediatorId, rewardType, amount, description } = body

    switch (action) {
      case 'issue_manual_reward':
        // 手动发放奖励
        if (!mediatorId || !amount || !description) {
          return NextResponse.json(
            { error: '缺少必要参数' },
            { status: 400 }
          )
        }

        const validUntil = new Date()
        validUntil.setDate(validUntil.getDate() + 30)

        const result = await prisma.$transaction(async (tx) => {
          // 创建提现券
          const voucher = await tx.withdrawalVoucher.create({
            data: {
              code: `MR${Date.now().toString(36).toUpperCase()}`,
              amount: parseFloat(amount),
              description: '管理员手动发放',
              validUntil,
              isUsed: false,
              usedBy: mediatorId,
              issuedBy: session.user.id
            }
          })

          // 创建奖励记录
          const reward = await tx.mediatorReward.create({
            data: {
              mediatorId,
              rewardType: rewardType || 'MANUAL',
              amount: parseFloat(amount),
              voucherId: voucher.id,
              description,
              earnedAt: new Date()
            }
          })

          return { voucher, reward }
        })

        // 发送通知
        await prisma.notification.create({
          data: {
            userId: mediatorId,
            type: 'REWARD',
            title: '获得管理员奖励',
            message: `管理员为您发放了 ${amount} USDT 奖励：${description}`,
            data: {
              rewardType: 'MANUAL',
              amount: parseFloat(amount),
              voucherCode: result.voucher.code,
              validUntil: validUntil.toISOString()
            },
            priority: 'HIGH'
          }
        }).catch(() => {})

        return NextResponse.json({
          success: true,
          message: '奖励发放成功',
          data: result.reward
        })

      case 'issue_monthly_bonuses':
        // 批量发放月度奖励
        const { issueMonthlyBonuses } = await import('@/lib/mediator-rewards')
        const bonusResult = await issueMonthlyBonuses()

        return NextResponse.json({
          success: bonusResult.success,
          message: `处理完成，成功发放 ${bonusResult.processed} 个奖励`,
          data: {
            processed: bonusResult.processed,
            errors: bonusResult.errors
          }
        })

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('奖励操作失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}
