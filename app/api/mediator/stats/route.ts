import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取中间人统计数据
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 验证用户是否为中间人
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        isMediator: true,
        mediatorStatus: true,
        depositBalance: true // 使用统一的保证金字段
      }
    })

    if (!user?.isMediator) {
      return NextResponse.json(
        { success: false, error: '您不是中间人' },
        { status: 403 }
      )
    }

    // 获取托管订单统计
    const [
      totalOrders,
      activeOrders,
      completedOrders,
      totalVolume,
      totalEarnings,
      pendingDisputes,
      recentOrders
    ] = await Promise.all([
      // 总订单数
      prisma.escrowOrder.count({
        where: { mediatorId: session.user.id }
      }),
      
      // 活跃订单数
      prisma.escrowOrder.count({
        where: {
          mediatorId: session.user.id,
          status: {
            in: ['PENDING', 'FUNDED', 'SHIPPED']
          }
        }
      }),
      
      // 已完成订单数
      prisma.escrowOrder.count({
        where: {
          mediatorId: session.user.id,
          status: 'COMPLETED'
        }
      }),
      
      // 总交易量
      prisma.escrowOrder.aggregate({
        where: { mediatorId: session.user.id },
        _sum: { amount: true }
      }),
      
      // 总收益
      prisma.escrowOrder.aggregate({
        where: { 
          mediatorId: session.user.id,
          status: 'COMPLETED'
        },
        _sum: { mediatorFee: true }
      }),
      
      // 待处理争议数
      prisma.escrowDispute.count({
        where: {
          escrowOrder: {
            mediatorId: session.user.id
          },
          status: {
            in: ['PENDING', 'INVESTIGATING', 'VOTING']
          }
        }
      }),
      
      // 最近订单
      prisma.escrowOrder.findMany({
        where: { mediatorId: session.user.id },
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          order: {
            select: {
              orderNumber: true,
              buyer: { select: { name: true, email: true } },
              seller: { select: { name: true, email: true } }
            }
          }
        }
      })
    ])

    // 计算保证金使用情况
    // 中间人保证金的使用应该基于担保规则，而不是订单金额
    // 这里我们使用一个简化的计算：每个活跃订单占用固定比例的保证金
    const activeOrdersCount = await prisma.escrowOrder.count({
      where: {
        mediatorId: session.user.id,
        status: {
          in: ['PENDING', 'FUNDED', 'SHIPPED']
        }
      }
    })

    // 假设每个活跃订单占用 1000 USDT 的保证金（可以根据实际业务规则调整）
    const depositPerOrder = 1000
    const usedDeposit = activeOrdersCount * depositPerOrder
    const availableDeposit = Math.max(0, user.depositBalance - usedDeposit)

    // 获取本月统计
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)

    const monthlyStats = await Promise.all([
      // 本月订单数
      prisma.escrowOrder.count({
        where: {
          mediatorId: session.user.id,
          createdAt: { gte: currentMonth }
        }
      }),
      
      // 本月收益
      prisma.escrowOrder.aggregate({
        where: {
          mediatorId: session.user.id,
          status: 'COMPLETED',
          completedAt: { gte: currentMonth }
        },
        _sum: { mediatorFee: true }
      }),
      
      // 本月争议数
      prisma.escrowDispute.count({
        where: {
          escrowOrder: {
            mediatorId: session.user.id
          },
          createdAt: { gte: currentMonth }
        }
      })
    ])

    // 获取仲裁投票统计
    const arbitrationStats = await Promise.all([
      // 参与的投票数
      prisma.arbitrationVote.count({
        where: { voterId: session.user.id }
      }),
      
      // 本月参与的投票数
      prisma.arbitrationVote.count({
        where: {
          voterId: session.user.id,
          createdAt: { gte: currentMonth }
        }
      }),
      
      // 获得的奖励券数
      prisma.withdrawalVoucher.count({
        where: {
          usedBy: session.user.id
        }
      })
    ])

    const stats = {
      // 基础统计
      activeOrders,
      completedOrders,
      totalOrders,
      totalEarnings: totalEarnings._sum.mediatorFee || 0,
      pendingDisputes,
      
      // 保证金信息
      totalDeposit: user.depositBalance,
      usedDeposit,
      availableDeposit,
      
      // 交易统计
      totalVolume: totalVolume._sum.amount || 0,
      successRate: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
      
      // 本月统计
      monthly: {
        orders: monthlyStats[0],
        earnings: monthlyStats[1]._sum.mediatorFee || 0,
        disputes: monthlyStats[2]
      },
      
      // 仲裁统计
      arbitration: {
        totalVotes: arbitrationStats[0],
        monthlyVotes: arbitrationStats[1],
        rewardVouchers: arbitrationStats[2]
      },
      
      // 最近订单
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        orderNumber: order.order.orderNumber,
        amount: order.amount,
        status: order.status,
        buyer: order.order.buyer.name || order.order.buyer.email,
        seller: order.order.seller.name || order.order.seller.email,
        createdAt: order.createdAt
      }))
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('获取中间人统计失败:', error)
    return NextResponse.json(
      { success: false, error: '获取统计数据失败' },
      { status: 500 }
    )
  }
}
