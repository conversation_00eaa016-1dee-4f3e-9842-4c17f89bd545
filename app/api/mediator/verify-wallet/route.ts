import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ethers } from 'ethers'

// 验证BNB钱包签名
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { address, message, signature } = await request.json()

    if (!address || !message || !signature) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 验证钱包地址格式
    if (!ethers.isAddress(address)) {
      return NextResponse.json(
        { success: false, error: '无效的钱包地址' },
        { status: 400 }
      )
    }

    try {
      // 验证签名
      const recoveredAddress = ethers.verifyMessage(message, signature)
      
      if (recoveredAddress.toLowerCase() !== address.toLowerCase()) {
        return NextResponse.json(
          { success: false, error: '签名验证失败' },
          { status: 400 }
        )
      }

      // 检查钱包地址是否已被其他用户使用
      const existingUser = await prisma.user.findFirst({
        where: {
          bnbWalletAddress: address,
          id: {
            not: session.user.id
          }
        }
      })

      if (existingUser) {
        return NextResponse.json(
          { success: false, error: '该钱包地址已被其他用户使用' },
          { status: 400 }
        )
      }

      // 更新用户的钱包信息
      await prisma.user.update({
        where: { id: session.user.id },
        data: {
          bnbWalletAddress: address,
          bnbWalletVerified: true,
          bnbWalletVerifiedAt: new Date()
        }
      })

      // 创建验证记录
      await prisma.mediatorVerification.create({
        data: {
          userId: session.user.id,
          walletAddress: address,
          verificationMethod: 'SIGNATURE',
          verificationData: {
            message,
            signature,
            recoveredAddress,
            timestamp: new Date().toISOString()
          },
          status: 'VERIFIED',
          verifiedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        message: '钱包验证成功',
        data: {
          address,
          verified: true,
          verifiedAt: new Date().toISOString()
        }
      })

    } catch (error) {
      console.error('签名验证错误:', error)
      return NextResponse.json(
        { success: false, error: '签名验证失败' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('钱包验证失败:', error)
    return NextResponse.json(
      { success: false, error: '钱包验证失败' },
      { status: 500 }
    )
  }
}

// 获取钱包验证状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        bnbWalletAddress: true,
        bnbWalletVerified: true,
        bnbWalletVerifiedAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取最新的验证记录
    const latestVerification = await prisma.mediatorVerification.findFirst({
      where: {
        userId: session.user.id,
        status: 'VERIFIED'
      },
      orderBy: {
        verifiedAt: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        walletAddress: user.bnbWalletAddress,
        isVerified: user.bnbWalletVerified,
        verifiedAt: user.bnbWalletVerifiedAt,
        verificationRecord: latestVerification
      }
    })

  } catch (error) {
    console.error('获取钱包验证状态失败:', error)
    return NextResponse.json(
      { success: false, error: '获取钱包验证状态失败' },
      { status: 500 }
    )
  }
}

// 重置钱包验证（用于更换钱包）
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    // 检查用户是否有活跃的中间人订单
    const activeOrders = await prisma.order.count({
      where: {
        mediatorId: session.user.id,
        status: {
          in: ['PENDING', 'PAID', 'SHIPPED']
        },
        useEscrow: true
      }
    })

    if (activeOrders > 0) {
      return NextResponse.json(
        { success: false, error: '您有活跃的托管订单，无法重置钱包验证' },
        { status: 400 }
      )
    }

    // 重置钱包验证状态
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        bnbWalletAddress: null,
        bnbWalletVerified: false,
        bnbWalletVerifiedAt: null
      }
    })

    // 将之前的验证记录标记为已撤销
    await prisma.mediatorVerification.updateMany({
      where: {
        userId: session.user.id,
        status: 'VERIFIED'
      },
      data: {
        status: 'REVOKED'
      }
    })

    return NextResponse.json({
      success: true,
      message: '钱包验证已重置'
    })

  } catch (error) {
    console.error('重置钱包验证失败:', error)
    return NextResponse.json(
      { success: false, error: '重置钱包验证失败' },
      { status: 500 }
    )
  }
}
