import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 提交中间人申请
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      bnbWalletAddress, 
      depositAmount, 
      feeRate, 
      experience, 
      introduction 
    } = body

    // 验证必要字段
    if (!bnbWalletAddress || !depositAmount || !feeRate || !experience || !introduction) {
      return NextResponse.json(
        { error: '请填写所有必要信息' },
        { status: 400 }
      )
    }

    // 验证费率范围
    if (feeRate < 0.01 || feeRate > 0.30) {
      return NextResponse.json(
        { error: '手续费率必须在1%-30%之间' },
        { status: 400 }
      )
    }

    // 验证保证金最低金额
    if (depositAmount < 1000) {
      return NextResponse.json(
        { error: '保证金最低1000 USDT' },
        { status: 400 }
      )
    }

    // 验证BNB钱包地址格式
    if (!bnbWalletAddress.startsWith('0x') || bnbWalletAddress.length !== 42) {
      return NextResponse.json(
        { error: 'BNB钱包地址格式不正确' },
        { status: 400 }
      )
    }

    // 检查是否已有申请
    const existingApplication = await prisma.mediatorApplication.findUnique({
      where: { userId: session.user.id }
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: '您已提交过中间人申请' },
        { status: 400 }
      )
    }

    // 检查用户余额是否足够
    const user = await prisma.user.findUnique({
      where: { id: session.user.id! },
      select: { depositBalance: true }
    })

    if (!user || user.depositBalance < depositAmount) {
      return NextResponse.json(
        { error: '账户余额不足以支付保证金' },
        { status: 400 }
      )
    }

    // 创建申请记录
    const application = await prisma.$transaction(async (tx) => {
      // 创建申请
      const app = await tx.mediatorApplication.create({
        data: {
          userId: session.user.id,
          bnbWalletAddress,
          depositAmount,
          feeRate,
          experience,
          introduction,
          status: 'PENDING'
        }
      })

      // 冻结保证金
      await tx.fundFreeze.create({
        data: {
          userId: session.user.id,
          amount: depositAmount,
          purpose: '中间人保证金',
          status: 'ACTIVE',
          relatedId: app.id,
          relatedType: 'MEDIATOR_APPLICATION'
        }
      })

      // 扣除用户余额
      await tx.user.update({
        where: { id: session.user.id },
        data: {
          depositBalance: { decrement: depositAmount }
        }
      })

      // 创建交易记录
      await tx.fundTransaction.create({
        data: {
          userId: session.user.id,
          type: 'MEDIATOR_DEPOSIT',
          amount: -depositAmount,
          description: '中间人保证金冻结',
          relatedId: app.id,
          metadata: {
            relatedType: 'MEDIATOR_APPLICATION',
            walletAddress: bnbWalletAddress
          }
        }
      })

      return app
    })

    // 发送通知给管理员
    await prisma.notification.create({
      data: {
        userId: 'admin', // 系统管理员
        type: 'ADMIN',
        title: '新的中间人申请',
        message: `用户 ${session.user.name || session.user.email} 提交了中间人申请`,
        data: {
          applicationId: application.id,
          userId: session.user.id,
          depositAmount,
          feeRate: feeRate * 100
        },
        priority: 'HIGH'
      }
    }).catch(() => {
      // 忽略通知发送失败
    })

    return NextResponse.json({
      success: true,
      message: '中间人申请已提交，等待审核',
      data: {
        applicationId: application.id,
        status: application.status,
        depositAmount,
        feeRate
      }
    })

  } catch (error) {
    console.error('提交中间人申请失败:', error)
    return NextResponse.json(
      { error: '提交申请失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取申请状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const application = await prisma.mediatorApplication.findUnique({
      where: { userId: session.user.id },
      include: {
        reviewer: {
          select: { name: true, email: true }
        }
      }
    })

    if (!application) {
      return NextResponse.json({
        success: true,
        data: null
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: application.id,
        status: application.status,
        bnbWalletAddress: application.bnbWalletAddress,
        depositAmount: application.depositAmount,
        feeRate: application.feeRate,
        experience: application.experience,
        introduction: application.introduction,
        reviewNotes: application.reviewNotes,
        reviewer: application.reviewer,
        createdAt: application.createdAt,
        approvedAt: application.approvedAt,
        rejectedAt: application.rejectedAt
      }
    })

  } catch (error) {
    console.error('获取申请状态失败:', error)
    return NextResponse.json(
      { error: '获取申请状态失败' },
      { status: 500 }
    )
  }
}

// 更新申请信息
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { feeRate, experience, introduction } = body

    // 查找申请
    const application = await prisma.mediatorApplication.findUnique({
      where: { userId: session.user.id }
    })

    if (!application) {
      return NextResponse.json(
        { error: '未找到申请记录' },
        { status: 404 }
      )
    }

    if (application.status !== 'PENDING') {
      return NextResponse.json(
        { error: '只能修改待审核的申请' },
        { status: 400 }
      )
    }

    // 验证费率
    if (feeRate && (feeRate < 0.01 || feeRate > 0.30)) {
      return NextResponse.json(
        { error: '手续费率必须在1%-30%之间' },
        { status: 400 }
      )
    }

    // 更新申请
    const updatedApplication = await prisma.mediatorApplication.update({
      where: { id: application.id },
      data: {
        ...(feeRate && { feeRate }),
        ...(experience && { experience }),
        ...(introduction && { introduction })
      }
    })

    return NextResponse.json({
      success: true,
      message: '申请信息已更新',
      data: updatedApplication
    })

  } catch (error) {
    console.error('更新申请失败:', error)
    return NextResponse.json(
      { error: '更新申请失败' },
      { status: 500 }
    )
  }
}
