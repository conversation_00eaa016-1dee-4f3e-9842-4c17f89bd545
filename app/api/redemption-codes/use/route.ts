import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 使用兑换码
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { codeValue } = await request.json()

    if (!codeValue || typeof codeValue !== 'string') {
      return NextResponse.json(
        { success: false, error: '请提供有效的兑换码' },
        { status: 400 }
      )
    }

    // 使用事务处理兑换
    const result = await (prisma as any).$transaction(async (tx: any) => {
      // 查找兑换码
      const redemptionCode = await tx.redemptionCode.findUnique({
        where: { codeValue: codeValue.toUpperCase() },
        include: {
          targetUser: {
            select: { id: true, name: true, email: true }
          }
        }
      })

      if (!redemptionCode) {
        throw new Error('兑换码不存在')
      }

      // 检查状态
      if (redemptionCode.status === 'USED') {
        throw new Error('兑换码已使用')
      }

      if (redemptionCode.status === 'EXPIRED') {
        throw new Error('兑换码已过期')
      }

      if (redemptionCode.status !== 'ACTIVE') {
        throw new Error('兑换码状态异常，无法使用')
      }

      // 检查有效期
      if (new Date() > redemptionCode.validUntil) {
        // 自动过期
        await tx.redemptionCode.update({
          where: { id: redemptionCode.id },
          data: { status: 'EXPIRED' }
        })
        throw new Error('兑换码已过期')
      }

      // 检查是否有使用权限
      if (redemptionCode.distributionType === 'TARGETED' && redemptionCode.targetUserId !== session.user.id) {
        throw new Error('该兑换码已指定给其他用户，您无权使用')
      }

      // 处理奖励
      await processReward(tx, session.user.id, redemptionCode.rewardType, redemptionCode.rewardValue, redemptionCode.rewardUnit, redemptionCode.id)

      // 更新兑换码状态
      const updatedCode = await tx.redemptionCode.update({
        where: { id: redemptionCode.id },
        data: {
          status: 'USED',
          firstUsedAt: redemptionCode.firstUsedAt || new Date(),
          lastUsedAt: new Date(),
          usedCount: {
            increment: 1
          }
        }
      })

      return {
        redemptionCode: updatedCode,
        rewardType: redemptionCode.rewardType,
        rewardValue: redemptionCode.rewardValue,
        rewardUnit: redemptionCode.rewardUnit
      }
    })

    return NextResponse.json({
      success: true,
      message: `成功使用兑换码，获得 ${result.rewardValue} ${result.rewardUnit} ${getRewardTypeText(result.rewardType)}`,
      data: {
        codeValue: result.redemptionCode.codeValue,
        rewardType: result.rewardType,
        rewardValue: result.rewardValue,
        rewardUnit: result.rewardUnit,
        usedAt: result.redemptionCode.lastUsedAt
      }
    })

  } catch (error) {
    console.error('使用兑换码失败:', error)
    
    // 根据错误类型返回不同的错误信息
    const errorMessage = error instanceof Error ? error.message : '使用兑换码失败'
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 400 }
    )
  }
}

// 处理奖励
async function processReward(
  tx: any,
  userId: string,
  rewardType: string,
  rewardValue: number,
  rewardUnit: string,
  redemptionCodeId: string
) {
  switch (rewardType) {
    case 'CASH_CREDIT':
      // 现金奖励：直接增加用户余额
      await tx.user.update({
        where: { id: userId },
        data: {
          depositBalance: {
            increment: rewardValue
          }
        }
      })

      await tx.fundTransaction.create({
        data: {
          userId,
          type: 'REDEMPTION_REWARD',
          amount: rewardValue,
          description: `兑换码奖励: ${rewardValue} ${rewardUnit}`,
          relatedId: redemptionCodeId
        }
      })
      break

    case 'WITHDRAWAL_FEE_DISCOUNT':
      // 提现手续费减免：创建优惠券记录
      await tx.userCoupon.create({
        data: {
          userId,
          couponType: 'WITHDRAWAL_FEE_DISCOUNT',
          discountValue: rewardValue,
          discountUnit: rewardUnit,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天有效期
          status: 'ACTIVE',
          sourceType: 'REDEMPTION_CODE',
          sourceId: redemptionCodeId
        }
      })
      break

    case 'SHOPPING_VOUCHER':
      // 购物券：创建购物券记录
      await tx.shoppingVoucher.create({
        data: {
          userId,
          voucherValue: rewardValue,
          voucherUnit: rewardUnit,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天有效期
          status: 'ACTIVE',
          sourceType: 'REDEMPTION_CODE',
          sourceId: redemptionCodeId
        }
      })
      break

    case 'BALANCE':
      // 余额奖励：直接增加用户余额（与CASH_CREDIT相同）
      await tx.user.update({
        where: { id: userId },
        data: {
          depositBalance: {
            increment: rewardValue
          }
        }
      })

      await tx.fundTransaction.create({
        data: {
          userId,
          type: 'REDEMPTION_REWARD',
          amount: rewardValue,
          description: `兑换码奖励: ${rewardValue} ${rewardUnit}`,
          relatedId: redemptionCodeId
        }
      })
      break

    default:
      throw new Error(`不支持的奖励类型: ${rewardType}`)
  }

  // 创建兑换券使用记录
  await tx.redemptionTransaction.create({
    data: {
      redemptionCodeId,
      userId,
      transactionType: 'USE',
      rewardValue,
      rewardUnit,
      description: `用户使用兑换码获得 ${rewardValue} ${rewardUnit} ${getRewardTypeText(rewardType)}`
    }
  })
}

function getRewardTypeText(rewardType: string): string {
  switch (rewardType) {
    case 'CASH_CREDIT': return '现金奖励'
    case 'WITHDRAWAL_FEE_DISCOUNT': return '提现手续费减免'
    case 'SHOPPING_VOUCHER': return '购物券'
    case 'BALANCE': return '余额奖励'
    default: return rewardType
  }
}
