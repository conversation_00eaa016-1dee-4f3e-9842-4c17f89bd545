import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 验证兑换码
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')

    if (!code) {
      return NextResponse.json(
        { success: false, error: '请提供兑换码' },
        { status: 400 }
      )
    }

    // 查找兑换码
    const redemptionCode = await (prisma as any).redemptionCode.findUnique({
      where: { codeValue: code.toUpperCase() },
      include: {
        targetUser: {
          select: { id: true, name: true, email: true }
        },
        createdBy: {
          select: { id: true, name: true }
        }
      }
    })

    if (!redemptionCode) {
      return NextResponse.json(
        { success: false, error: '兑换码不存在' },
        { status: 404 }
      )
    }

    // 检查状态和权限
    let canUse = true
    let statusMessage = ''

    if (redemptionCode.status === 'USED') {
      canUse = false
      statusMessage = '已使用'
    } else if (redemptionCode.status === 'EXPIRED') {
      canUse = false
      statusMessage = '已过期'
    } else if (new Date() > redemptionCode.validUntil) {
      canUse = false
      statusMessage = '已过期'
    } else if (redemptionCode.distributionType === 'TARGETED' && redemptionCode.targetUserId !== session.user.id) {
      canUse = false
      statusMessage = `专属兑换码 (${redemptionCode.targetUser?.email || '指定用户'})`
    } else if (redemptionCode.status === 'ACTIVE') {
      statusMessage = '可使用'
    } else {
      canUse = false
      statusMessage = '状态异常'
    }

    return NextResponse.json({
      success: true,
      data: {
        id: redemptionCode.id,
        codeValue: redemptionCode.codeValue,
        title: redemptionCode.title,
        description: redemptionCode.description,
        rewardType: redemptionCode.rewardType,
        rewardValue: redemptionCode.rewardValue,
        rewardUnit: redemptionCode.rewardUnit,
        status: redemptionCode.status,
        distributionType: redemptionCode.distributionType,
        validUntil: redemptionCode.validUntil,
        canUse,
        statusMessage,
        targetUser: redemptionCode.targetUser,
        isTargetedToMe: redemptionCode.targetUserId === session.user.id
      }
    })

  } catch (error) {
    console.error('验证兑换码失败:', error)
    return NextResponse.json(
      { success: false, error: '验证兑换码失败' },
      { status: 500 }
    )
  }
}
