import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 兑换兑换券
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { codeValue } = await request.json()

    if (!codeValue || typeof codeValue !== 'string') {
      return NextResponse.json(
        { success: false, error: '请输入有效的兑换码' },
        { status: 400 }
      )
    }

    // 验证兑换码格式（16位字母数字组合）
    const normalizedCodeValue = codeValue.toUpperCase()
    if (!/^[A-Z0-9]{16}$/.test(normalizedCodeValue)) {
      return NextResponse.json(
        { success: false, error: '兑换码格式不正确，应为16位字母数字组合' },
        { status: 400 }
      )
    }

    // 使用事务处理兑换
    const result = await prisma.$transaction(async (tx) => {
      // 查找兑换券
      const redemptionCode = await tx.redemptionCode.findUnique({
        where: { codeValue: normalizedCodeValue },
        include: {
          targetUser: {
            select: { id: true, name: true }
          },
          transactions: {
            select: {
              userId: true,
              transactionType: true,
              createdAt: true
            }
          }
        }
      })

      if (!redemptionCode) {
        throw new Error('兑换码不存在')
      }

      // 检查兑换券状态
      if (redemptionCode.status === 'USED' && redemptionCode.usedCount >= redemptionCode.maxUses) {
        throw new Error('兑换码已被使用完毕')
      }

      if (redemptionCode.status === 'EXPIRED') {
        throw new Error('兑换码已过期')
      }

      if (redemptionCode.status === 'REVOKED') {
        throw new Error('兑换码已被撤销')
      }

      if (redemptionCode.status !== 'ACTIVE') {
        throw new Error('兑换码状态异常，无法使用')
      }

      // 检查有效期
      const now = new Date()
      if (now < redemptionCode.validFrom || now > redemptionCode.validUntil) {
        // 自动过期
        await tx.redemptionCode.update({
          where: { id: redemptionCode.id },
          data: { status: 'EXPIRED' }
        })
        throw new Error('兑换码已过期')
      }

      // 检查是否为定向发放且用户匹配
      if (redemptionCode.distributionType === 'TARGETED' && 
          redemptionCode.targetUserId && 
          redemptionCode.targetUserId !== session.user.id) {
        throw new Error('此兑换码不是为您发放的')
      }

      // 检查用户是否已经使用过此兑换码
      const userUsage = redemptionCode.transactions.filter(
        t => t.userId === session.user.id && t.transactionType === 'USE'
      )

      if (userUsage.length > 0 && redemptionCode.maxUses === 1) {
        throw new Error('您已经使用过此兑换码')
      }

      // 更新兑换券使用状态
      const newUsedCount = redemptionCode.usedCount + 1
      const isFullyUsed = newUsedCount >= redemptionCode.maxUses

      await tx.redemptionCode.update({
        where: { id: redemptionCode.id },
        data: {
          usedCount: newUsedCount,
          status: isFullyUsed ? 'USED' : 'ACTIVE',
          firstUsedAt: redemptionCode.firstUsedAt || now,
          lastUsedAt: now
        }
      })

      // 处理奖励发放
      await processRedemptionReward(
        tx, 
        session.user.id, 
        redemptionCode.rewardType, 
        redemptionCode.rewardValue, 
        redemptionCode.rewardUnit, 
        redemptionCode.id
      )

      // 创建兑换券使用记录
      await tx.redemptionTransaction.create({
        data: {
          redemptionCodeId: redemptionCode.id,
          userId: session.user.id,
          transactionType: 'USE',
          rewardValue: redemptionCode.rewardValue,
          rewardUnit: redemptionCode.rewardUnit,
          usageContext: 'USER_REDEMPTION',
          description: `用户兑换: ${redemptionCode.title}`,
          metadata: {
            codeValue: redemptionCode.codeValue,
            rewardType: redemptionCode.rewardType,
            redeemedAt: now.toISOString()
          },
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
          userAgent: request.headers.get('user-agent')
        }
      })

      return {
        redemptionCode,
        rewardValue: redemptionCode.rewardValue,
        rewardUnit: redemptionCode.rewardUnit,
        rewardType: redemptionCode.rewardType
      }
    })

    // 根据奖励类型返回不同的成功消息
    let successMessage = ''
    switch (result.rewardType) {
      case 'CASH_CREDIT':
        successMessage = `成功兑换，获得 ${result.rewardValue} ${result.rewardUnit} 余额`
        break
      case 'PLATFORM_POINTS':
        successMessage = `成功兑换，获得 ${result.rewardValue} 平台积分`
        break
      case 'WITHDRAWAL_FEE_DISCOUNT':
        successMessage = `成功兑换，获得提现手续费减免券`
        break
      case 'SHOPPING_VOUCHER':
        successMessage = `成功兑换，获得 ${result.rewardValue} ${result.rewardUnit} 购物券`
        break
      case 'BALANCE':
        successMessage = `成功兑换，获得 ${result.rewardValue} ${result.rewardUnit} 余额`
        break
      default:
        successMessage = '兑换成功'
    }

    return NextResponse.json({
      success: true,
      message: successMessage,
      data: {
        codeValue: result.redemptionCode.codeValue,
        title: result.redemptionCode.title,
        rewardType: result.rewardType,
        rewardValue: result.rewardValue,
        rewardUnit: result.rewardUnit
      }
    })

  } catch (error) {
    console.error('兑换兑换券失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '兑换兑换券失败'
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 400 }
    )
  }
}

// 处理兑换奖励发放
async function processRedemptionReward(
  tx: any,
  userId: string,
  rewardType: string,
  rewardValue: number,
  rewardUnit: string,
  redemptionCodeId: string
) {
  switch (rewardType) {
    case 'CASH_CREDIT':
      // 现金奖励：直接增加用户余额
      await tx.user.update({
        where: { id: userId },
        data: {
          depositBalance: {
            increment: rewardValue
          }
        }
      })

      await tx.fundTransaction.create({
        data: {
          userId,
          type: 'REDEMPTION_REWARD',
          amount: rewardValue,
          description: `兑换券奖励: ${rewardValue} ${rewardUnit}`,
          relatedId: redemptionCodeId
        }
      })
      break

    case 'PLATFORM_POINTS':
      // 平台积分：增加用户信用积分
      await tx.user.update({
        where: { id: userId },
        data: {
          creditPoints: {
            increment: rewardValue
          }
        }
      })

      // 创建资金交易记录（用于记录积分变动）
      await tx.fundTransaction.create({
        data: {
          userId,
          type: 'REDEMPTION_REWARD',
          amount: rewardValue,
          description: `兑换券奖励: ${rewardValue} 积分`,
          relatedId: redemptionCodeId,
          metadata: {
            relatedType: 'REDEMPTION_CODE',
            rewardType: 'PLATFORM_POINTS'
          }
        }
      })
      break

    case 'WITHDRAWAL_FEE_DISCOUNT':
      // 提现手续费减免：创建优惠券记录
      await tx.userCoupon.create({
        data: {
          userId,
          couponType: 'WITHDRAWAL_FEE_DISCOUNT',
          discountValue: rewardValue,
          discountUnit: rewardUnit,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天有效期
          status: 'ACTIVE',
          sourceId: redemptionCodeId,
          sourceType: 'REDEMPTION_CODE',
          title: '提现手续费减免券',
          description: `减免提现手续费 ${rewardValue} ${rewardUnit}`
        }
      })
      break

    case 'SHOPPING_VOUCHER':
      // 购物券：创建购物券记录
      await tx.shoppingVoucher.create({
        data: {
          userId,
          voucherValue: rewardValue,
          voucherUnit: rewardUnit,
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          status: 'ACTIVE',
          sourceId: redemptionCodeId,
          sourceType: 'REDEMPTION_CODE',
          title: '购物券',
          description: `购物抵扣券 ${rewardValue} ${rewardUnit}`
        }
      })
      break

    case 'BALANCE':
      // 余额奖励：直接增加用户余额（与CASH_CREDIT相同）
      await tx.user.update({
        where: { id: userId },
        data: {
          depositBalance: {
            increment: rewardValue
          }
        }
      })

      await tx.fundTransaction.create({
        data: {
          userId,
          type: 'REDEMPTION_REWARD',
          amount: rewardValue,
          description: `兑换券奖励: ${rewardValue} ${rewardUnit}`,
          relatedId: redemptionCodeId
        }
      })
      break

    default:
      throw new Error(`不支持的奖励类型: ${rewardType}`)
  }
}

// 验证兑换券信息（不执行兑换）
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const codeValue = searchParams.get('codeValue')

    if (!codeValue) {
      return NextResponse.json(
        { success: false, error: '请提供兑换码' },
        { status: 400 }
      )
    }

    // 验证兑换码格式（16位字母数字组合）
    const normalizedCode = codeValue.toUpperCase()
    if (!/^[A-Z0-9]{16}$/.test(normalizedCode)) {
      return NextResponse.json(
        { success: false, error: '兑换码格式不正确，应为16位字母数字组合' },
        { status: 400 }
      )
    }

    // 查找兑换券
    const redemptionCode = await prisma.redemptionCode.findUnique({
      where: { codeValue: normalizedCode },
      select: {
        id: true,
        codeValue: true,
        title: true,
        description: true,
        rewardType: true,
        rewardValue: true,
        rewardUnit: true,
        status: true,
        validFrom: true,
        validUntil: true,
        maxUses: true,
        usedCount: true,
        distributionType: true,
        targetUserId: true,
        transactions: {
          where: {
            userId: session.user.id,
            transactionType: 'USE'
          },
          select: {
            createdAt: true
          }
        }
      }
    })

    if (!redemptionCode) {
      return NextResponse.json(
        { success: false, error: '兑换码不存在' },
        { status: 404 }
      )
    }

    // 检查状态和有效期
    let canRedeem = true
    let statusMessage = ''

    const now = new Date()

    if (redemptionCode.status === 'USED' && redemptionCode.usedCount >= redemptionCode.maxUses) {
      canRedeem = false
      statusMessage = '已被使用完毕'
    } else if (redemptionCode.status === 'EXPIRED') {
      canRedeem = false
      statusMessage = '已过期'
    } else if (redemptionCode.status === 'REVOKED') {
      canRedeem = false
      statusMessage = '已被撤销'
    } else if (now < redemptionCode.validFrom) {
      canRedeem = false
      statusMessage = '尚未生效'
    } else if (now > redemptionCode.validUntil) {
      canRedeem = false
      statusMessage = '已过期'
    } else if (redemptionCode.distributionType === 'TARGETED' && 
               redemptionCode.targetUserId && 
               redemptionCode.targetUserId !== session.user.id) {
      canRedeem = false
      statusMessage = '此兑换码不是为您发放的'
    } else if (redemptionCode.transactions.length > 0 && redemptionCode.maxUses === 1) {
      canRedeem = false
      statusMessage = '您已经使用过此兑换码'
    } else {
      statusMessage = '可以兑换'
    }

    // 获取奖励类型描述
    const rewardTypeMap: Record<string, string> = {
      'CASH_CREDIT': '现金奖励',
      'PLATFORM_POINTS': '平台积分',
      'WITHDRAWAL_FEE_DISCOUNT': '提现手续费减免',
      'SHOPPING_VOUCHER': '购物券'
    }

    return NextResponse.json({
      success: true,
      data: {
        codeValue: redemptionCode.codeValue,
        title: redemptionCode.title,
        description: redemptionCode.description,
        rewardType: redemptionCode.rewardType,
        rewardTypeText: rewardTypeMap[redemptionCode.rewardType] || redemptionCode.rewardType,
        rewardValue: redemptionCode.rewardValue,
        rewardUnit: redemptionCode.rewardUnit,
        validFrom: redemptionCode.validFrom,
        validUntil: redemptionCode.validUntil,
        maxUses: redemptionCode.maxUses,
        usedCount: redemptionCode.usedCount,
        canRedeem,
        statusMessage
      }
    })

  } catch (error) {
    console.error('验证兑换券失败:', error)
    return NextResponse.json(
      { success: false, error: '验证兑换券失败' },
      { status: 500 }
    )
  }
}
