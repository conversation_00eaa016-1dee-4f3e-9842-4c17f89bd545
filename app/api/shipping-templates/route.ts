import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取发货模板列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const templates = await prisma.shippingTemplate.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: {
        isDefault: 'desc',
        updatedAt: 'desc'
      }
    })

    return NextResponse.json({ templates })

  } catch (error) {
    console.error('获取发货模板失败:', error)
    return NextResponse.json(
      { error: '获取发货模板失败' },
      { status: 500 }
    )
  }
}

// 创建发货模板
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      name, 
      shippingCompany, 
      estimatedDeliveryDays, 
      shippingNotes, 
      isDefault = false 
    } = body

    if (!name || !shippingCompany) {
      return NextResponse.json(
        { error: '模板名称和快递公司为必填项' },
        { status: 400 }
      )
    }

    // 如果设置为默认模板，先取消其他默认模板
    if (isDefault) {
      await prisma.shippingTemplate.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      })
    }

    const template = await prisma.shippingTemplate.create({
      data: {
        name: name.trim(),
        shippingCompany: shippingCompany.trim(),
        estimatedDeliveryDays: estimatedDeliveryDays || null,
        shippingNotes: shippingNotes?.trim() || null,
        isDefault,
        userId: session.user.id
      }
    })

    return NextResponse.json({
      success: true,
      template
    })

  } catch (error) {
    console.error('创建发货模板失败:', error)
    return NextResponse.json(
      { error: '创建发货模板失败' },
      { status: 500 }
    )
  }
}

// 更新发货模板
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      id,
      name, 
      shippingCompany, 
      estimatedDeliveryDays, 
      shippingNotes, 
      isDefault = false 
    } = body

    if (!id) {
      return NextResponse.json(
        { error: '请提供模板ID' },
        { status: 400 }
      )
    }

    // 检查模板是否存在且属于当前用户
    const existingTemplate = await prisma.shippingTemplate.findFirst({
      where: {
        id,
        userId: session.user.id
      }
    })

    if (!existingTemplate) {
      return NextResponse.json(
        { error: '模板不存在或无权限修改' },
        { status: 404 }
      )
    }

    // 如果设置为默认模板，先取消其他默认模板
    if (isDefault && !existingTemplate.isDefault) {
      await prisma.shippingTemplate.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      })
    }

    const template = await prisma.shippingTemplate.update({
      where: { id },
      data: {
        name: name?.trim() || existingTemplate.name,
        shippingCompany: shippingCompany?.trim() || existingTemplate.shippingCompany,
        estimatedDeliveryDays: estimatedDeliveryDays !== undefined ? estimatedDeliveryDays : existingTemplate.estimatedDeliveryDays,
        shippingNotes: shippingNotes !== undefined ? (shippingNotes?.trim() || null) : existingTemplate.shippingNotes,
        isDefault
      }
    })

    return NextResponse.json({
      success: true,
      template
    })

  } catch (error) {
    console.error('更新发货模板失败:', error)
    return NextResponse.json(
      { error: '更新发货模板失败' },
      { status: 500 }
    )
  }
}

// 删除发货模板
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const templateId = searchParams.get('id')

    if (!templateId) {
      return NextResponse.json(
        { error: '请提供模板ID' },
        { status: 400 }
      )
    }

    // 检查模板是否存在且属于当前用户
    const template = await prisma.shippingTemplate.findFirst({
      where: {
        id: templateId,
        userId: session.user.id
      }
    })

    if (!template) {
      return NextResponse.json(
        { error: '模板不存在或无权限删除' },
        { status: 404 }
      )
    }

    await prisma.shippingTemplate.delete({
      where: { id: templateId }
    })

    return NextResponse.json({
      success: true,
      message: '模板删除成功'
    })

  } catch (error) {
    console.error('删除发货模板失败:', error)
    return NextResponse.json(
      { error: '删除发货模板失败' },
      { status: 500 }
    )
  }
}
