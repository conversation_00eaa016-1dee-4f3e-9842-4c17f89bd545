import { NextRequest, NextResponse } from 'next/server'
import { prismaConnection } from '@/lib/prisma-optimized'
import { cache } from '@/lib/cache-fallback'
import { performanceMonitor, getMemoryUsage, getSystemStats } from '@/lib/performance-monitor'

// 健康检查端点
export async function GET(request: NextRequest) {
  const startTime = performance.now()
  
  try {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {} as any,
      performance: {} as any,
      system: {} as any,
    }

    // 检查数据库连接
    try {
      const dbStart = performance.now()
      const dbHealthy = await prismaConnection.healthCheck()
      const dbTime = performance.now() - dbStart
      
      healthStatus.checks.database = {
        status: dbHealthy ? 'healthy' : 'unhealthy',
        responseTime: Math.round(dbTime),
        message: dbHealthy ? 'Database connection OK' : 'Database connection failed'
      }
    } catch (error) {
      healthStatus.checks.database = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Database check failed'
      }
      healthStatus.status = 'degraded'
    }

    // 检查Redis缓存
    try {
      const cacheStart = performance.now()
      const cacheHealthy = await cache.healthCheck()
      const cacheTime = performance.now() - cacheStart
      
      healthStatus.checks.cache = {
        status: cacheHealthy ? 'healthy' : 'unhealthy',
        responseTime: Math.round(cacheTime),
        message: cacheHealthy ? 'Cache connection OK' : 'Cache connection failed'
      }
    } catch (error) {
      healthStatus.checks.cache = {
        status: 'not_configured',
        message: 'Cache not configured or connection failed'
      }
    }

    // 获取性能统计
    try {
      const stats = performanceMonitor.getStats()
      healthStatus.performance = {
        api: {
          averageResponseTime: Math.round(stats.api.avg || 0),
          p95ResponseTime: Math.round(stats.api.p95 || 0),
          totalRequests: stats.counts.totalRequests || 0,
          slowRequests: stats.slowApis || 0,
        },
        database: {
          averageQueryTime: Math.round(stats.database.avg || 0),
          p95QueryTime: Math.round(stats.database.p95 || 0),
          totalQueries: stats.counts.dbQueries || 0,
          slowQueries: stats.slowQueries || 0,
        }
      }
    } catch (error) {
      healthStatus.performance = {
        message: 'Performance metrics not available'
      }
    }

    // 获取系统信息
    try {
      const systemStats = getSystemStats()
      const memoryUsage = getMemoryUsage()
      
      healthStatus.system = {
        uptime: Math.round(systemStats.uptime),
        memory: {
          used: memoryUsage.heapUsed,
          total: memoryUsage.heapTotal,
          usage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
        },
        cpu: {
          user: Math.round(systemStats.cpu.user),
          system: Math.round(systemStats.cpu.system)
        },
        pid: systemStats.pid,
        nodeVersion: systemStats.version
      }

      // 检查系统资源警告
      if (healthStatus.system.memory.usage > 80) {
        healthStatus.status = 'degraded'
        healthStatus.checks.memory = {
          status: 'warning',
          message: 'High memory usage detected'
        }
      }
    } catch (error) {
      healthStatus.system = {
        message: 'System stats not available'
      }
    }

    // 计算总响应时间
    const totalTime = performance.now() - startTime
    healthStatus.performance.healthCheckTime = Math.round(totalTime)

    // 确定整体健康状态
    const checks = Object.values(healthStatus.checks)
    const unhealthyChecks = checks.filter((check: any) => check.status === 'unhealthy' || check.status === 'error')
    const warningChecks = checks.filter((check: any) => check.status === 'warning')

    if (unhealthyChecks.length > 0) {
      healthStatus.status = 'unhealthy'
    } else if (warningChecks.length > 0 || healthStatus.status === 'degraded') {
      healthStatus.status = 'degraded'
    }

    // 根据健康状态设置HTTP状态码
    let httpStatus = 200
    if (healthStatus.status === 'degraded') {
      httpStatus = 200 // 仍然返回200，但状态为degraded
    } else if (healthStatus.status === 'unhealthy') {
      httpStatus = 503 // Service Unavailable
    }

    return NextResponse.json(healthStatus, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error) {
    console.error('Health check error:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error instanceof Error ? error.message : 'Health check failed',
      checks: {
        system: {
          status: 'error',
          message: 'Health check system error'
        }
      }
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}

// 简化的健康检查端点（用于负载均衡器）
export async function HEAD(request: NextRequest) {
  try {
    // 快速检查数据库连接
    const dbHealthy = await prismaConnection.healthCheck()
    
    if (dbHealthy) {
      return new NextResponse(null, { status: 200 })
    } else {
      return new NextResponse(null, { status: 503 })
    }
  } catch (error) {
    return new NextResponse(null, { status: 503 })
  }
}
