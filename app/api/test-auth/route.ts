import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    console.log('=== 测试认证API ===')
    
    // 获取session
    const session = await getServerSession(authOptions)
    console.log('Session:', session)
    
    // 检查请求头
    const headers = Object.fromEntries(request.headers.entries())
    console.log('Request headers:', headers)
    
    // 检查cookies
    const cookies = request.cookies.getAll()
    console.log('Cookies:', cookies)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: '未登录',
        debug: {
          hasSession: !!session,
          sessionUser: session?.user,
          cookies: cookies.map(c => ({ name: c.name, value: c.value.substring(0, 20) + '...' })),
          headers: {
            cookie: headers.cookie?.substring(0, 100) + '...',
            authorization: headers.authorization,
            'user-agent': headers['user-agent']
          }
        }
      }, { status: 401 })
    }
    
    return NextResponse.json({
      success: true,
      message: '认证成功',
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
        isMediator: session.user.isMediator
      },
      debug: {
        sessionExists: true,
        cookieCount: cookies.length,
        hasAuthCookie: cookies.some(c => c.name.includes('next-auth'))
      }
    })
    
  } catch (error) {
    console.error('测试认证API失败:', error)
    return NextResponse.json({
      success: false,
      error: '服务器错误',
      debug: {
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      }
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const body = await request.json()
    
    console.log('POST 测试认证API')
    console.log('Session:', session)
    console.log('Body:', body)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: '未登录',
        receivedData: body
      }, { status: 401 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'POST 认证成功',
      user: session.user,
      receivedData: body
    })
    
  } catch (error) {
    console.error('POST 测试认证API失败:', error)
    return NextResponse.json({
      success: false,
      error: '服务器错误'
    }, { status: 500 })
  }
}
