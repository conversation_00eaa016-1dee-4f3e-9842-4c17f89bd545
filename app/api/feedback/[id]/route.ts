import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个反馈详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 })
    }

    const { id } = params

    const feedback = await prisma.userFeedback.findFirst({
      where: {
        id,
        userId: session.user.id // 只能查看自己的反馈
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!feedback) {
      return NextResponse.json({ error: '反馈不存在' }, { status: 404 })
    }

    return NextResponse.json(feedback)

  } catch (error) {
    console.error('获取反馈详情失败:', error)
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}

// 更新反馈状态（用户可以关闭自己的反馈）
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { status } = body

    // 验证状态值
    if (!['CLOSED'].includes(status)) {
      return NextResponse.json({ error: '无效的状态值' }, { status: 400 })
    }

    // 检查反馈是否存在且属于当前用户
    const existingFeedback = await prisma.userFeedback.findFirst({
      where: {
        id,
        userId: session.user.id
      }
    })

    if (!existingFeedback) {
      return NextResponse.json({ error: '反馈不存在' }, { status: 404 })
    }

    // 更新反馈状态
    const updatedFeedback = await prisma.userFeedback.update({
      where: { id },
      data: { 
        status,
        updatedAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({
      message: '反馈状态更新成功',
      feedback: updatedFeedback
    })

  } catch (error) {
    console.error('更新反馈状态失败:', error)
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}
