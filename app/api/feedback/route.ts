import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// 反馈提交验证模式
const feedbackSchema = z.object({
  category: z.enum(['BUG_REPORT', 'IMPROVEMENT', 'APPEAL']),
  title: z.string().min(1, '标题不能为空').max(200, '标题不能超过200字符'),
  description: z.string().min(10, '描述至少需要10个字符').max(2000, '描述不能超过2000字符'),
  contactEmail: z.string().optional().refine(
    (email) => !email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
    { message: '邮箱格式不正确' }
  ),
  contactPhone: z.string().optional(),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string(),
    size: z.number(),
    type: z.string()
  })).optional()
})

// 获取用户反馈列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const status = searchParams.get('status')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {
      userId: session.user.id
    }

    if (category) {
      where.category = category
    }

    if (status) {
      where.status = status
    }

    // 获取反馈列表
    const [feedbacks, total] = await Promise.all([
      prisma.userFeedback.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.userFeedback.count({ where })
    ])

    return NextResponse.json({
      feedbacks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('获取反馈列表失败:', error)
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}

// 提交新反馈
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 })
    }

    const body = await request.json()
    
    // 验证输入数据
    const validatedData = feedbackSchema.parse(body)

    // 获取请求信息
    const userAgent = request.headers.get('user-agent') || undefined
    const referer = request.headers.get('referer') || undefined

    // 创建反馈记录
    const feedback = await prisma.userFeedback.create({
      data: {
        userId: session.user.id,
        category: validatedData.category,
        title: validatedData.title.trim(),
        description: validatedData.description.trim(),
        contactEmail: validatedData.contactEmail?.trim() || null,
        contactPhone: validatedData.contactPhone?.trim() || null,
        attachments: validatedData.attachments || undefined,
        userAgent,
        pageUrl: referer,
        browserInfo: {
          userAgent,
          timestamp: new Date().toISOString()
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({
      message: '反馈提交成功',
      feedback
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: '输入数据验证失败',
        details: error.issues
      }, { status: 400 })
    }

    console.error('提交反馈失败:', error)
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}
