import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 记录发货安全操作日志
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      orderId, 
      action, 
      details, 
      ipAddress, 
      userAgent,
      riskLevel = 'LOW' 
    } = body

    if (!orderId || !action) {
      return NextResponse.json(
        { error: '请提供订单ID和操作类型' },
        { status: 400 }
      )
    }

    // 验证订单权限
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        sellerId: true,
        buyerId: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    if (order.sellerId !== session.user.id && order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 记录安全日志
    const securityLog = await prisma.shippingSecurityLog.create({
      data: {
        orderId,
        userId: session.user.id,
        action,
        details: details || null,
        ipAddress: ipAddress || null,
        userAgent: userAgent || null,
        riskLevel,
        timestamp: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      logId: securityLog.id
    })

  } catch (error) {
    console.error('记录发货安全日志失败:', error)
    return NextResponse.json(
      { error: '记录安全日志失败' },
      { status: 500 }
    )
  }
}

// 获取发货安全日志
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('orderId')
    const limit = parseInt(searchParams.get('limit') || '50')
    const riskLevel = searchParams.get('riskLevel')

    let where: any = {
      userId: session.user.id
    }

    if (orderId) {
      // 验证订单权限
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: {
          sellerId: true,
          buyerId: true
        }
      })

      if (!order || (order.sellerId !== session.user.id && order.buyerId !== session.user.id)) {
        return NextResponse.json(
          { error: '无权限查看此订单日志' },
          { status: 403 }
        )
      }

      where.orderId = orderId
    }

    if (riskLevel) {
      where.riskLevel = riskLevel
    }

    const logs = await prisma.shippingSecurityLog.findMany({
      where,
      include: {
        order: {
          select: {
            orderNumber: true
          }
        },
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: limit
    })

    return NextResponse.json({ logs })

  } catch (error) {
    console.error('获取发货安全日志失败:', error)
    return NextResponse.json(
      { error: '获取安全日志失败' },
      { status: 500 }
    )
  }
}

// 分析发货操作风险
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { orderId, trackingNumber } = body

    if (!orderId) {
      return NextResponse.json(
        { error: '请提供订单ID' },
        { status: 400 }
      )
    }

    // 风险分析
    const riskFactors: string[] = []
    let riskLevel = 'LOW'

    // 检查运单号是否重复使用
    if (trackingNumber) {
      const duplicateOrders = await prisma.order.count({
        where: {
          trackingNumber: trackingNumber.trim(),
          status: { in: ['SHIPPED', 'DELIVERED', 'COMPLETED'] },
          id: { not: orderId }
        }
      })

      if (duplicateOrders > 0) {
        riskFactors.push('运单号重复使用')
        riskLevel = 'HIGH'
      }
    }

    // 检查用户最近发货频率
    const recentShippings = await prisma.order.count({
      where: {
        sellerId: session.user.id,
        status: 'SHIPPED',
        shippedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24小时内
        }
      }
    })

    if (recentShippings > 50) {
      riskFactors.push('24小时内发货频率过高')
      riskLevel = riskLevel === 'HIGH' ? 'HIGH' : 'MEDIUM'
    }

    // 检查订单创建到发货的时间间隔
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        createdAt: true,
        sellerId: true
      }
    })

    if (order && order.sellerId === session.user.id) {
      const timeDiff = Date.now() - order.createdAt.getTime()
      const hoursDiff = timeDiff / (1000 * 60 * 60)

      if (hoursDiff < 1) {
        riskFactors.push('订单创建后立即发货')
        riskLevel = riskLevel === 'HIGH' ? 'HIGH' : 'MEDIUM'
      }
    }

    return NextResponse.json({
      riskLevel,
      riskFactors,
      recommendations: getRiskRecommendations(riskLevel, riskFactors)
    })

  } catch (error) {
    console.error('分析发货风险失败:', error)
    return NextResponse.json(
      { error: '分析发货风险失败' },
      { status: 500 }
    )
  }
}

// 获取风险建议
function getRiskRecommendations(riskLevel: string, riskFactors: string[]): string[] {
  const recommendations: string[] = []

  if (riskLevel === 'HIGH') {
    recommendations.push('建议仔细核对发货信息')
    recommendations.push('建议联系买家确认收货地址')
  }

  if (riskFactors.includes('运单号重复使用')) {
    recommendations.push('请检查运单号是否正确')
    recommendations.push('重复运单号可能导致物流跟踪混乱')
  }

  if (riskFactors.includes('24小时内发货频率过高')) {
    recommendations.push('请确认所有发货信息准确无误')
    recommendations.push('建议适当降低发货频率')
  }

  if (riskFactors.includes('订单创建后立即发货')) {
    recommendations.push('建议确认商品库存充足')
    recommendations.push('建议核对买家收货信息')
  }

  if (recommendations.length === 0) {
    recommendations.push('发货信息正常，可以安全发货')
  }

  return recommendations
}
