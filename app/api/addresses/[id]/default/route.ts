import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 设置默认地址
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证地址是否存在且属于当前用户
    const address = await prisma.address.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!address) {
      return NextResponse.json(
        { error: '地址不存在' },
        { status: 404 }
      )
    }

    // 使用事务设置默认地址
    await prisma.$transaction(async (tx) => {
      // 先取消所有默认地址
      await tx.address.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      })

      // 设置新的默认地址
      await tx.address.update({
        where: {
          id: params.id
        },
        data: {
          isDefault: true
        }
      })
    })

    return NextResponse.json({
      success: true,
      message: '默认地址设置成功'
    })

  } catch (error) {
    console.error('设置默认地址失败:', error)
    return NextResponse.json(
      { error: '设置默认地址失败' },
      { status: 500 }
    )
  }
}
