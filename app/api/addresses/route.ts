import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的收货地址列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const addresses = await prisma.address.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: [
        { isDefault: 'desc' },
        { updatedAt: 'desc' }
      ]
    })

    return NextResponse.json(addresses)

  } catch (error) {
    console.error('获取地址列表失败:', error)
    return NextResponse.json(
      { error: '获取地址列表失败' },
      { status: 500 }
    )
  }
}

// 创建新的收货地址
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      phone,
      province,
      city,
      district,
      detail,
      remark,
      isDefault = false
    } = body

    // 验证必填字段
    if (!name || !phone || !province || !city || !district || !detail) {
      return NextResponse.json(
        { error: '地址信息不完整，请填写所有必填字段' },
        { status: 400 }
      )
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return NextResponse.json(
        { error: '手机号格式不正确' },
        { status: 400 }
      )
    }

    // 验证姓名长度
    if (name.length < 2 || name.length > 20) {
      return NextResponse.json(
        { error: '收件人姓名长度应在2-20个字符之间' },
        { status: 400 }
      )
    }

    // 验证详细地址长度
    if (detail.length < 5 || detail.length > 200) {
      return NextResponse.json(
        { error: '详细地址长度应在5-200个字符之间' },
        { status: 400 }
      )
    }

    // 使用事务处理默认地址设置
    const result = await prisma.$transaction(async (tx) => {
      // 如果设置为默认地址，先取消其他默认地址
      if (isDefault) {
        await tx.address.updateMany({
          where: {
            userId: session.user.id,
            isDefault: true
          },
          data: {
            isDefault: false
          }
        })
      }

      // 创建新地址
      const newAddress = await tx.address.create({
        data: {
          name,
          phone,
          province,
          city,
          district,
          detail,
          remark,
          isDefault,
          userId: session.user.id
        }
      })

      return newAddress
    })

    return NextResponse.json(result, { status: 201 })

  } catch (error) {
    console.error('创建地址失败:', error)
    return NextResponse.json(
      { error: '创建地址失败' },
      { status: 500 }
    )
  }
}

// 批量删除地址
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const ids = searchParams.get('ids')?.split(',') || []

    if (ids.length === 0) {
      return NextResponse.json(
        { error: '请选择要删除的地址' },
        { status: 400 }
      )
    }

    // 验证地址所有权
    const addresses = await prisma.address.findMany({
      where: {
        id: { in: ids },
        userId: session.user.id
      }
    })

    if (addresses.length !== ids.length) {
      return NextResponse.json(
        { error: '部分地址不存在或无权限删除' },
        { status: 403 }
      )
    }

    // 删除地址
    await prisma.address.deleteMany({
      where: {
        id: { in: ids },
        userId: session.user.id
      }
    })

    return NextResponse.json({
      success: true,
      message: `成功删除 ${addresses.length} 个地址`
    })

  } catch (error) {
    console.error('删除地址失败:', error)
    return NextResponse.json(
      { error: '删除地址失败' },
      { status: 500 }
    )
  }
}
