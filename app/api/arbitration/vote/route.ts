import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 中间人投票
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { caseId, vote, reason } = body

    // 验证中间人权限
    const mediator = await prisma.user.findUnique({
      where: { 
        id: session.user.id,
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      }
    })

    if (!mediator) {
      return NextResponse.json(
        { success: false, error: '需要活跃中间人权限' },
        { status: 403 }
      )
    }

    // 验证仲裁案例
    const arbitrationCase = await prisma.arbitrationCase.findUnique({
      where: { id: caseId },
      include: {
        order: {
          include: {
            buyer: true,
            seller: true,
            mediator: true
          }
        },
        votes: true
      }
    })

    if (!arbitrationCase) {
      return NextResponse.json(
        { success: false, error: '仲裁案例不存在' },
        { status: 404 }
      )
    }

    // 检查案例状态
    if (arbitrationCase.status !== 'VOTING') {
      return NextResponse.json(
        { success: false, error: '该案例不在投票阶段' },
        { status: 400 }
      )
    }

    // 检查投票截止时间
    if (arbitrationCase.votingDeadline && new Date() > arbitrationCase.votingDeadline) {
      return NextResponse.json(
        { success: false, error: '投票已截止' },
        { status: 400 }
      )
    }

    // 检查是否已投票
    const existingVote = await prisma.arbitrationVote.findUnique({
      where: {
        caseId_voterId: {
          caseId,
          voterId: session.user.id
        }
      }
    })

    if (existingVote) {
      return NextResponse.json(
        { success: false, error: '您已经投过票了' },
        { status: 400 }
      )
    }

    // 验证投票选项
    if (!['BUYER_FAVOR', 'SELLER_FAVOR', 'NEUTRAL'].includes(vote)) {
      return NextResponse.json(
        { success: false, error: '无效的投票选项' },
        { status: 400 }
      )
    }

    // 创建投票记录
    const voteRecord = await prisma.arbitrationVote.create({
      data: {
        caseId,
        voterId: session.user.id,
        decision: vote,
        reasoning: reason
      },
      include: {
        voter: { select: { id: true, name: true } }
      }
    })

    // 检查是否达到投票要求，自动结算
    await checkAndResolveCase(caseId)

    return NextResponse.json({
      success: true,
      data: voteRecord
    })
  } catch (error) {
    console.error('投票失败:', error)
    return NextResponse.json(
      { success: false, error: '投票失败' },
      { status: 500 }
    )
  }
}

// 获取投票详情
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('caseId')

    if (!caseId) {
      return NextResponse.json(
        { success: false, error: '缺少案例ID' },
        { status: 400 }
      )
    }

    const votes = await prisma.arbitrationVote.findMany({
      where: { caseId },
      include: {
        voter: {
          select: {
            id: true,
            name: true,
            avatar: true,
            mediatorReputation: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 统计投票结果
    const voteStats = {
      BUYER_FAVOR: 0,
      SELLER_FAVOR: 0,
      NEUTRAL: 0,
      total: votes.length
    }

    votes.forEach(vote => {
      voteStats[vote.decision as keyof typeof voteStats]++
    })

    return NextResponse.json({
      success: true,
      data: {
        votes,
        stats: voteStats
      }
    })
  } catch (error) {
    console.error('获取投票详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取投票详情失败' },
      { status: 500 }
    )
  }
}

// 检查并解决仲裁案例
async function checkAndResolveCase(caseId: string) {
  try {
    const arbitrationCase = await prisma.arbitrationCase.findUnique({
      where: { id: caseId },
      include: {
        order: true,
        votes: true
      }
    })

    if (!arbitrationCase || arbitrationCase.status !== 'VOTING') {
      return
    }

    const votes = arbitrationCase.votes
    const minVotes = 3 // 最少需要3票
    const isExpired = arbitrationCase.votingDeadline && new Date() > arbitrationCase.votingDeadline

    // 如果投票数量足够或者已过期，进行结算
    if (votes.length >= minVotes || isExpired) {
      // 统计投票结果
      const voteStats = {
        BUYER_FAVOR: 0,
        SELLER_FAVOR: 0,
        NEUTRAL: 0
      }

      votes.forEach(vote => {
        voteStats[vote.decision as keyof typeof voteStats]++
      })

      // 确定结果（多数决）
      let resolution = 'NEUTRAL'
      let maxVotes = voteStats.NEUTRAL

      if (voteStats.BUYER_FAVOR > maxVotes) {
        resolution = 'BUYER_FAVOR'
        maxVotes = voteStats.BUYER_FAVOR
      }

      if (voteStats.SELLER_FAVOR > maxVotes) {
        resolution = 'SELLER_FAVOR'
        maxVotes = voteStats.SELLER_FAVOR
      }

      // 更新案例状态
      await prisma.arbitrationCase.update({
        where: { id: caseId },
        data: {
          status: 'RESOLVED',
          resolution,
          resolvedAt: new Date()
        }
      })

      // 根据仲裁结果处理订单
      await executeArbitrationResult(arbitrationCase.orderId, resolution)

      // 给参与投票的中间人发放奖励
      await rewardVotingMediators(votes)
    }
  } catch (error) {
    console.error('解决仲裁案例失败:', error)
  }
}

// 执行仲裁结果
async function executeArbitrationResult(orderId: string, resolution: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId }
    })

    if (!order) return

    let updateData: any = {
      disputeResolvedAt: new Date()
    }

    if (resolution === 'BUYER_FAVOR') {
      // 支持买家，退款
      updateData.escrowStatus = 'REFUNDED'
      updateData.status = 'REFUNDED'
      updateData.refundReason = '仲裁结果：支持买家'
      updateData.refundAmount = order.totalAmount
    } else if (resolution === 'SELLER_FAVOR') {
      // 支持卖家，释放资金
      updateData.escrowStatus = 'RELEASED'
      updateData.escrowReleasedAt = new Date()
      updateData.status = 'COMPLETED'
    } else {
      // 中性结果，可能需要进一步处理
      updateData.escrowStatus = 'DISPUTED'
    }

    await prisma.order.update({
      where: { id: orderId },
      data: updateData
    })

    // 创建交易记录
    await prisma.escrowTransaction.create({
      data: {
        orderId,
        type: resolution === 'BUYER_FAVOR' ? 'REFUND' : 'RELEASE',
        amount: order.totalAmount,
        description: `仲裁结果执行: ${resolution}`
      }
    })
  } catch (error) {
    console.error('执行仲裁结果失败:', error)
  }
}

// 奖励投票中间人
async function rewardVotingMediators(votes: any[]) {
  try {
    const now = new Date()
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) // 7天后过期

    for (const vote of votes) {
      // 检查本月是否已获得奖励
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const existingReward = await prisma.rewardCoupon.findFirst({
        where: {
          userId: vote.voterId,
          type: 'WITHDRAWAL_FEE_WAIVER',
          createdAt: { gte: thisMonth }
        }
      })

      if (!existingReward) {
        await prisma.rewardCoupon.create({
          data: {
            userId: vote.voterId,
            type: 'WITHDRAWAL_FEE_WAIVER',
            value: 10,
            description: '参与仲裁投票奖励 - 10 USDT 免手续费提现券',
            expiresAt
          }
        })
      }
    }
  } catch (error) {
    console.error('奖励投票中间人失败:', error)
  }
}
