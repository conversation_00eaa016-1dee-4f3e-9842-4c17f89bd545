import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建仲裁案例
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { orderId, reason, description, evidence } = body

    // 验证订单
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true,
        seller: true,
        mediator: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { success: false, error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查用户权限（只有买家或卖家可以发起仲裁）
    if (![order.buyerId, order.sellerId].includes(session.user.id)) {
      return NextResponse.json(
        { success: false, error: '只有交易双方可以发起仲裁' },
        { status: 403 }
      )
    }

    // 检查订单是否使用托管服务
    if (!order.useEscrow) {
      return NextResponse.json(
        { success: false, error: '只有托管订单可以申请仲裁' },
        { status: 400 }
      )
    }

    // 检查是否已有未解决的仲裁案例
    const existingCase = await prisma.arbitrationCase.findFirst({
      where: {
        orderId,
        status: {
          in: ['PENDING', 'VOTING']
        }
      }
    })

    if (existingCase) {
      return NextResponse.json(
        { success: false, error: '该订单已有进行中的仲裁案例' },
        { status: 400 }
      )
    }

    // 确定举报人类型
    const reporterType = order.buyerId === session.user.id ? 'BUYER' : 'SELLER'

    // 创建仲裁案例
    const arbitrationCase = await prisma.arbitrationCase.create({
      data: {
        orderId,
        reporterId: session.user.id,
        reporterType,
        reason,
        description,
        evidence,
        status: 'PENDING',
        votingDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后
      },
      include: {
        order: {
          include: {
            product: true,
            buyer: { select: { id: true, name: true } },
            seller: { select: { id: true, name: true } },
            mediator: { select: { id: true, name: true } }
          }
        },
        reporter: { select: { id: true, name: true } }
      }
    })

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        escrowStatus: 'DISPUTED',
        disputeReason: reason,
        disputeReportedAt: new Date()
      }
    })

    // 通知相关方
    await notifyArbitrationParties(arbitrationCase)

    return NextResponse.json({
      success: true,
      data: arbitrationCase
    })
  } catch (error) {
    console.error('创建仲裁案例失败:', error)
    return NextResponse.json(
      { success: false, error: '创建仲裁案例失败' },
      { status: 500 }
    )
  }
}

// 获取仲裁案例列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const role = searchParams.get('role') // reporter, mediator, admin
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    const where: any = {}

    // 根据角色筛选
    if (role === 'reporter') {
      where.reporterId = session.user.id
    } else if (role === 'mediator') {
      // 只有中间人可以查看待投票的案例
      if (!user?.isMediator || user.mediatorStatus !== 'ACTIVE') {
        return NextResponse.json(
          { success: false, error: '需要中间人权限' },
          { status: 403 }
        )
      }
      where.status = 'VOTING'
    } else if (role === 'admin') {
      // 管理员可以查看所有案例
      if (user?.role !== 'ADMIN') {
        return NextResponse.json(
          { success: false, error: '需要管理员权限' },
          { status: 403 }
        )
      }
    } else {
      // 默认只显示用户相关的案例
      where.OR = [
        { reporterId: session.user.id },
        { 
          order: {
            OR: [
              { buyerId: session.user.id },
              { sellerId: session.user.id },
              { mediatorId: session.user.id }
            ]
          }
        }
      ]
    }

    if (status) {
      where.status = status
    }

    const cases = await prisma.arbitrationCase.findMany({
      where,
      include: {
        order: {
          include: {
            product: { select: { id: true, title: true, images: true } },
            buyer: { select: { id: true, name: true, avatar: true } },
            seller: { select: { id: true, name: true, avatar: true } },
            mediator: { select: { id: true, name: true, avatar: true } }
          }
        },
        reporter: { select: { id: true, name: true, avatar: true } },
        votes: {
          include: {
            voter: { select: { id: true, name: true } }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.arbitrationCase.count({ where })

    return NextResponse.json({
      success: true,
      data: {
        cases,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取仲裁案例失败:', error)
    return NextResponse.json(
      { success: false, error: '获取仲裁案例失败' },
      { status: 500 }
    )
  }
}

// 通知仲裁相关方的辅助函数
async function notifyArbitrationParties(arbitrationCase: any) {
  try {
    console.log(`仲裁案例创建: ${arbitrationCase.id}, 订单: ${arbitrationCase.orderId}`)

    // 获取订单信息以确定通知对象
    const order = await prisma.order.findUnique({
      where: { id: arbitrationCase.orderId },
      include: {
        buyer: { select: { id: true, name: true, email: true } },
        seller: { select: { id: true, name: true, email: true } },
        product: { select: { title: true } }
      }
    })

    if (!order) {
      console.error('订单不存在，无法发送仲裁通知')
      return false
    }

    const notifications: Array<{
      userId: string;
      type: string;
      title: string;
      message: string;
      data: any;
      relatedId: string;
      relatedType: string;
      priority: string;
    }> = []

    // 通知买家
    notifications.push({
      userId: order.buyer.id,
      type: 'DISPUTE',
      title: '仲裁案例已创建',
      message: `您的订单 ${order.orderNumber} 已进入仲裁程序，仲裁员将协助解决争议`,
      data: {
        arbitrationCaseId: arbitrationCase.id,
        orderId: arbitrationCase.orderId,
        orderNumber: order.orderNumber,
        productTitle: order.product.title
      },
      relatedId: arbitrationCase.orderId,
      relatedType: 'ORDER',
      priority: 'HIGH'
    })

    // 通知卖家
    notifications.push({
      userId: order.seller.id,
      type: 'DISPUTE',
      title: '仲裁案例已创建',
      message: `您的订单 ${order.orderNumber} 已进入仲裁程序，仲裁员将协助解决争议`,
      data: {
        arbitrationCaseId: arbitrationCase.id,
        orderId: arbitrationCase.orderId,
        orderNumber: order.orderNumber,
        productTitle: order.product.title
      },
      relatedId: arbitrationCase.orderId,
      relatedType: 'ORDER',
      priority: 'HIGH'
    })

    // 通知仲裁员
    if (arbitrationCase.arbitratorId) {
      notifications.push({
        userId: arbitrationCase.arbitratorId,
        type: 'SYSTEM',
        title: '新的仲裁案例',
        message: `您被分配了一个新的仲裁案例，订单号: ${order.orderNumber}`,
        data: {
          arbitrationCaseId: arbitrationCase.id,
          orderId: arbitrationCase.orderId,
          orderNumber: order.orderNumber,
          productTitle: order.product.title,
          buyerName: order.buyer.name,
          sellerName: order.seller.name
        },
        relatedId: arbitrationCase.id,
        relatedType: 'ARBITRATION',
        priority: 'URGENT'
      })
    }

    // 批量创建通知
    if (notifications.length > 0) {
      await prisma.notification.createMany({
        data: notifications
      })
      console.log(`仲裁通知已发送给 ${notifications.length} 个用户`)
    }

    return true
  } catch (error) {
    console.error('发送仲裁通知失败:', error)
    return false
  }
}
