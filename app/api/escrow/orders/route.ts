import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建托管订单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      productId, 
      variantId, 
      quantity, 
      mediatorId, 
      shippingAddress,
      notes 
    } = body

    // 验证商品和中间人
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: { seller: true }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: '商品不存在' },
        { status: 404 }
      )
    }

    if (product.sellerId === session.user.id) {
      return NextResponse.json(
        { success: false, error: '不能购买自己的商品' },
        { status: 400 }
      )
    }

    // 验证中间人
    const mediator = await prisma.user.findUnique({
      where: { 
        id: mediatorId,
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      }
    })

    if (!mediator) {
      return NextResponse.json(
        { success: false, error: '中间人不存在或未激活' },
        { status: 400 }
      )
    }

    // 计算订单金额
    let unitPrice = product.price
    if (variantId) {
      const variant = await prisma.productVariant.findUnique({
        where: { id: variantId }
      })
      if (variant) {
        unitPrice = variant.price
      }
    }

    const totalAmount = unitPrice * quantity
    
    // 检查是否达到托管服务最低金额
    if (totalAmount < 100) {
      return NextResponse.json(
        { success: false, error: '订单金额未达到托管服务最低要求 (100 USDT)' },
        { status: 400 }
      )
    }

    // 计算托管费用
    const escrowFeeRate = mediator.mediatorFeeRate || 0.05 // 默认5%
    const escrowFee = totalAmount * escrowFeeRate

    // 检查中间人保证金是否足够
    const mediatorActiveOrders = await prisma.order.aggregate({
      where: {
        mediatorId: mediatorId,
        status: {
          in: ['PENDING', 'PAID', 'SHIPPED']
        },
        useEscrow: true
      },
      _sum: {
        totalAmount: true
      }
    })

    const activeOrdersAmount = mediatorActiveOrders._sum.totalAmount || 0
    const requiredDeposit = activeOrdersAmount + totalAmount

    if (requiredDeposit > mediator.mediatorDeposit) {
      return NextResponse.json(
        { success: false, error: '中间人保证金不足，无法承接此订单' },
        { status: 400 }
      )
    }

    // 创建托管订单
    const order = await prisma.order.create({
      data: {
        orderNumber: `ESC${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
        productId,
        variantId,
        buyerId: session.user.id,
        sellerId: product.sellerId,
        mediatorId,
        quantity,
        unitPrice,
        totalAmount,
        shippingAddress,
        notes,
        status: 'PENDING',
        useEscrow: true,
        escrowStatus: 'PENDING',
        escrowFee,
        escrowFeeRate,
        paymentMethod: 'USDT'
      },
      include: {
        product: true,
        buyer: true,
        seller: true,
        mediator: true
      }
    })

    // 创建多方聊天室
    await createEscrowChatRoom(order.id, [
      session.user.id, // 买家
      product.sellerId, // 卖家
      mediatorId, // 中间人
      // 管理员会在需要时加入
    ])

    return NextResponse.json({
      success: true,
      data: order
    })
  } catch (error) {
    console.error('创建托管订单失败:', error)
    return NextResponse.json(
      { success: false, error: '创建托管订单失败' },
      { status: 500 }
    )
  }
}

// 获取托管订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const role = searchParams.get('role') // buyer, seller, mediator
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const where: any = {
      useEscrow: true
    }

    // 根据角色筛选
    if (role === 'buyer') {
      where.buyerId = session.user.id
    } else if (role === 'seller') {
      where.sellerId = session.user.id
    } else if (role === 'mediator') {
      where.mediatorId = session.user.id
    } else {
      // 默认显示用户相关的所有托管订单
      where.OR = [
        { buyerId: session.user.id },
        { sellerId: session.user.id },
        { mediatorId: session.user.id }
      ]
    }

    if (status) {
      where.escrowStatus = status
    }

    const orders = await prisma.order.findMany({
      where,
      include: {
        product: true,
        buyer: { select: { id: true, name: true, avatar: true } },
        seller: { select: { id: true, name: true, avatar: true } },
        mediator: { select: { id: true, name: true, avatar: true, mediatorReputation: true } }
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.order.count({ where })

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取托管订单失败:', error)
    return NextResponse.json(
      { success: false, error: '获取托管订单失败' },
      { status: 500 }
    )
  }
}

// 创建多方聊天室的辅助函数
async function createEscrowChatRoom(orderId: string, participantIds: string[]) {
  // 这里可以创建一个特殊的聊天室或者在现有消息系统中标记为托管订单相关
  // 暂时使用现有的订单消息系统
  return true
}
