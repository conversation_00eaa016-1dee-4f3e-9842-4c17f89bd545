import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建托管订单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const {
      productId,
      variantId,
      quantity = 1,
      mediatorId,
      shippingAddress,
      notes
    } = await request.json()

    // 验证必要参数
    if (!productId || !mediatorId) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 获取商品信息
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        variants: true
      }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: '商品不存在' },
        { status: 404 }
      )
    }

    // 检查库存
    if (product.stock < quantity) {
      return NextResponse.json(
        { success: false, error: '库存不足' },
        { status: 400 }
      )
    }

    // 获取中间人信息
    const mediator = await prisma.user.findUnique({
      where: {
        id: mediatorId,
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        email: true,
        mediatorFeeRate: true,
        mediatorDeposit: true,
        bnbWalletAddress: true,
        bnbWalletVerified: true
      } as any
    })

    if (!mediator) {
      return NextResponse.json(
        { success: false, error: '中间人不存在或未激活' },
        { status: 404 }
      )
    }

    if (!(mediator as any).bnbWalletVerified) {
      return NextResponse.json(
        { success: false, error: '中间人钱包未验证' },
        { status: 400 }
      )
    }

    // 计算价格
    let unitPrice = product.price
    if (variantId) {
      const variant = product.variants.find(v => v.id === variantId)
      if (variant) {
        unitPrice = variant.price
      }
    }

    const totalAmount = unitPrice * quantity
    
    // 检查是否符合托管条件
    if (totalAmount < 100) {
      return NextResponse.json(
        { success: false, error: '托管服务仅适用于100 USDT以上的订单' },
        { status: 400 }
      )
    }

    // 计算托管费用
    const escrowFee = totalAmount * ((mediator as any).mediatorFeeRate || 0.05)
    const escrowFeeRate = (mediator as any).mediatorFeeRate || 0.05

    // 检查中间人保证金是否足够
    const activeOrdersSum = await prisma.order.aggregate({
      where: {
        mediatorId: mediator.id,
        status: {
          in: ['PENDING', 'PAID', 'SHIPPED']
        },
        useEscrow: true
      } as any,
      _sum: {
        totalAmount: true
      }
    })

    const usedDeposit = activeOrdersSum._sum?.totalAmount || 0
    const availableDeposit = ((mediator as any).mediatorDeposit || 0) - usedDeposit

    if (availableDeposit < totalAmount) {
      return NextResponse.json(
        { success: false, error: '中间人保证金不足，无法处理此订单' },
        { status: 400 }
      )
    }

    // 防止买家购买自己的商品
    if (product.sellerId === session.user.id) {
      return NextResponse.json(
        { success: false, error: '不能购买自己的商品' },
        { status: 400 }
      )
    }

    // 使用事务创建托管订单
    const result = await prisma.$transaction(async (tx) => {
      // 生成订单号
      const orderNumber = `ESC${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`

      // 创建订单
      const order = await tx.order.create({
        data: {
          orderNumber,
          productId,
          buyerId: session.user.id,
          sellerId: product.sellerId,
          mediatorId: (mediator as any).id,
          status: 'PENDING_PAYMENT',
          totalAmount,
          productPrice: totalAmount,
          shippingFee: 0,
          platformFee: 0,
          useEscrow: true,
          escrowStatus: 'PENDING',
          escrowFee,
          escrowFeeRate,
          escrowNotes: notes,
          shippingAddress,
          paymentMethod: 'USDT',
          autoConfirmAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后自动确认
        } as any
      })

      // 创建订单项目
      await tx.orderItem.create({
        data: {
          orderId: order.id,
          productId,
          variantId,
          quantity,
          unitPrice,
          totalPrice: totalAmount
        }
      })

      // 减少商品库存
      await tx.product.update({
        where: { id: productId },
        data: {
          stock: {
            decrement: quantity
          }
        }
      })

      // 如果有变体，也要减少变体库存
      if (variantId) {
        await tx.productVariant.update({
          where: { id: variantId },
          data: {
            stock: {
              decrement: quantity
            }
          }
        })
      }

      // 创建托管聊天室
      await (tx as any).escrowChatRoom.create({
        data: {
          orderId: order.id,
          participants: [
            session.user.id, // 买家
            product.sellerId, // 卖家
            mediator.id, // 中间人
            // 管理员会在需要时加入
          ],
          roomType: 'ESCROW',
          status: 'ACTIVE'
        }
      })

      // 创建订单日志
      await tx.orderLog.create({
        data: {
          orderId: order.id,
          operatorId: session.user.id,
          action: 'ESCROW_ORDER_CREATED',
          description: `创建托管订单，中间人: ${mediator.name}`,
          newValue: JSON.stringify({
            mediatorId: mediator.id,
            mediatorName: mediator.name,
            escrowFee,
            escrowFeeRate,
            totalAmount
          }),
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
          userAgent: request.headers.get('user-agent')
        }
      })

      return order
    })

    // 获取完整的订单信息
    const fullOrder = await prisma.order.findUnique({
      where: { id: result.id },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            description: true,
            price: true,
            images: true,
            category: true,
            condition: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        mediator: {
          select: {
            id: true,
            name: true,
            email: true,
            mediatorFeeRate: true,
            bnbWalletAddress: true
          }
        },
        orderItems: true
      }
    })

    return NextResponse.json({
      success: true,
      message: '托管订单创建成功',
      data: {
        order: fullOrder,
        paymentInfo: {
          totalAmount: totalAmount + escrowFee,
          productAmount: totalAmount,
          escrowFee,
          escrowFeeRate,
          mediatorWallet: mediator.bnbWalletAddress,
          paymentDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时支付期限
        }
      }
    })

  } catch (error) {
    console.error('创建托管订单失败:', error)
    return NextResponse.json(
      { success: false, error: '创建托管订单失败' },
      { status: 500 }
    )
  }
}

// 获取托管订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const role = searchParams.get('role') // 'buyer', 'seller', 'mediator'
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // 构建查询条件
    const where: any = {
      useEscrow: true
    }

    if (role === 'buyer') {
      where.buyerId = session.user.id
    } else if (role === 'seller') {
      where.sellerId = session.user.id
    } else if (role === 'mediator') {
      where.mediatorId = session.user.id
    } else {
      // 默认返回用户相关的所有托管订单
      where.OR = [
        { buyerId: session.user.id },
        { sellerId: session.user.id },
        { mediatorId: session.user.id }
      ]
    }

    if (status) {
      where.status = status
    }

    const orders = await prisma.order.findMany({
      where,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true,
            images: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        mediator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        orderItems: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.order.count({ where })

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取托管订单列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取托管订单列表失败' },
      { status: 500 }
    )
  }
}
