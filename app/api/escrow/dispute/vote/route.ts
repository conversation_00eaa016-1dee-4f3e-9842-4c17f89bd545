import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 提交托管争议投票
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { disputeId, decision, reasoning } = body

    if (!disputeId || !decision || !reasoning) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 验证决定类型
    const validDecisions = ['BUYER_FAVOR', 'SELLER_FAVOR', 'MEDIATOR_FAVOR', 'SPLIT_DECISION']
    if (!validDecisions.includes(decision)) {
      return NextResponse.json(
        { error: '无效的仲裁决定' },
        { status: 400 }
      )
    }

    // 验证用户是否为活跃中间人
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        isMediator: true,
        mediatorStatus: true,
        mediatorReputation: true
      }
    })

    if (!user || !user.isMediator || user.mediatorStatus !== 'ACTIVE') {
      return NextResponse.json(
        { error: '只有活跃的中间人可以参与仲裁投票' },
        { status: 403 }
      )
    }

    // 获取争议信息
    const dispute = await prisma.escrowDispute.findUnique({
      where: { id: disputeId },
      include: {
        escrowOrder: {
          include: {
            order: true,
            mediator: true
          }
        }
      }
    })

    if (!dispute) {
      return NextResponse.json(
        { error: '争议不存在' },
        { status: 404 }
      )
    }

    // 验证争议状态
    if (dispute.status !== 'VOTING') {
      return NextResponse.json(
        { error: '争议当前不在投票阶段' },
        { status: 400 }
      )
    }

    // 验证中间人不能为自己涉及的争议投票
    if (dispute.escrowOrder?.mediatorId === session.user.id) {
      return NextResponse.json(
        { error: '不能为自己涉及的争议投票' },
        { status: 403 }
      )
    }

    // 检查是否已经投票
    const existingVote = await prisma.arbitrationVote.findFirst({
      where: {
        disputeId,
        voterId: session.user.id
      }
    })

    if (existingVote) {
      return NextResponse.json(
        { error: '您已经为此争议投票' },
        { status: 400 }
      )
    }

    // 创建投票记录
    const vote = await prisma.arbitrationVote.create({
      data: {
        disputeId,
        voterId: session.user.id,
        decision,
        reasoning,
        voteWeight: calculateVoteWeight(user.mediatorReputation || 0),
        votedAt: new Date()
      }
    })

    // 检查是否达到投票阈值
    const voteStats = await getVoteStatistics(disputeId)
    const shouldFinalize = shouldFinalizeVoting(voteStats)

    if (shouldFinalize) {
      await finalizeArbitration(disputeId, voteStats)
    }

    return NextResponse.json({
      success: true,
      message: '投票已提交',
      data: {
        voteId: vote.id,
        decision,
        voteWeight: vote.voteWeight,
        shouldFinalize
      }
    })

  } catch (error) {
    console.error('提交仲裁投票失败:', error)
    return NextResponse.json(
      { error: '提交投票失败' },
      { status: 500 }
    )
  }
}

// 获取待投票的争议列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证用户是否为活跃中间人
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        isMediator: true,
        mediatorStatus: true
      }
    })

    if (!user || !user.isMediator || user.mediatorStatus !== 'ACTIVE') {
      return NextResponse.json(
        { error: '只有活跃的中间人可以查看仲裁案例' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // 获取待投票的争议（排除自己涉及的）
    const [disputes, total] = await Promise.all([
      prisma.escrowDispute.findMany({
        where: {
          status: 'VOTING',
          escrowOrder: {
            mediatorId: { not: session.user.id }
          },
          votes: {
            none: {
              voterId: session.user.id
            }
          }
        },
        include: {
          escrowOrder: {
            include: {
              order: {
                select: {
                  orderNumber: true,
                  totalAmount: true,
                  product: {
                    select: { title: true, images: true }
                  }
                }
              },
              buyer: {
                select: { name: true, email: true }
              },
              seller: {
                select: { name: true, email: true }
              },
              mediator: {
                select: { name: true, email: true }
              }
            }
          },
          reporter: {
            select: { name: true, email: true }
          },
          reported: {
            select: { name: true, email: true }
          },
          votes: {
            select: {
              decision: true,
              voteWeight: true,
              voter: {
                select: { name: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'asc' },
        skip,
        take: limit
      }),
      prisma.escrowDispute.count({
        where: {
          status: 'VOTING',
          escrowOrder: {
            mediatorId: { not: session.user.id }
          },
          votes: {
            none: {
              voterId: session.user.id
            }
          }
        }
      })
    ])

    // 为每个争议添加投票统计
    const disputesWithStats = await Promise.all(
      disputes.map(async (dispute) => {
        const voteStats = await getVoteStatistics(dispute.id)
        return {
          ...dispute,
          voteStatistics: voteStats
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        disputes: disputesWithStats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取仲裁案例失败:', error)
    return NextResponse.json(
      { error: '获取仲裁案例失败' },
      { status: 500 }
    )
  }
}

// 计算投票权重（基于中间人信誉度）
function calculateVoteWeight(reputation: number): number {
  if (reputation >= 90) return 3
  if (reputation >= 70) return 2
  return 1
}

// 获取投票统计
async function getVoteStatistics(disputeId: string) {
  const votes = await prisma.arbitrationVote.findMany({
    where: { disputeId },
    select: {
      decision: true,
      voteWeight: true
    }
  })

  const stats = {
    totalVotes: votes.length,
    totalWeight: votes.reduce((sum, vote) => sum + vote.voteWeight, 0),
    decisions: {} as Record<string, { count: number; weight: number }>
  }

  votes.forEach(vote => {
    if (!stats.decisions[vote.decision]) {
      stats.decisions[vote.decision] = { count: 0, weight: 0 }
    }
    stats.decisions[vote.decision].count++
    stats.decisions[vote.decision].weight += vote.voteWeight
  })

  return stats
}

// 判断是否应该结束投票
function shouldFinalizeVoting(voteStats: any): boolean {
  // 至少需要3票
  if (voteStats.totalVotes < 3) return false

  // 如果有超过50%的权重支持某个决定，则结束投票
  for (const decision in voteStats.decisions) {
    const weightPercentage = voteStats.decisions[decision].weight / voteStats.totalWeight
    if (weightPercentage > 0.5) return true
  }

  // 如果投票数达到7票，强制结束
  return voteStats.totalVotes >= 7
}

// 完成仲裁
async function finalizeArbitration(disputeId: string, voteStats: any) {
  try {
    // 找出获胜的决定
    let winningDecision = ''
    let maxWeight = 0

    for (const decision in voteStats.decisions) {
      if (voteStats.decisions[decision].weight > maxWeight) {
        maxWeight = voteStats.decisions[decision].weight
        winningDecision = decision
      }
    }

    await prisma.$transaction(async (tx) => {
      // 更新争议状态
      await tx.escrowDispute.update({
        where: { id: disputeId },
        data: {
          status: 'RESOLVED',
          resolution: `仲裁结果：${winningDecision}。基于${voteStats.totalVotes}票投票，总权重${voteStats.totalWeight}。`,
          resolvedAt: new Date()
        }
      })

      // 执行仲裁结果
      await executeArbitrationDecision(tx, disputeId, winningDecision)

      // 给参与投票的中间人发放奖励
      await rewardVotingMediators(tx, disputeId)
    })

  } catch (error) {
    console.error('完成仲裁失败:', error)
  }
}

// 执行仲裁决定
async function executeArbitrationDecision(tx: any, disputeId: string, decision: string) {
  const dispute = await tx.escrowDispute.findUnique({
    where: { id: disputeId },
    include: {
      escrowOrder: {
        include: {
          order: true
        }
      }
    }
  })

  if (!dispute?.escrowOrder) return

  const escrowOrder = dispute.escrowOrder

  switch (decision) {
    case 'BUYER_FAVOR':
      // 退款给买家
      await tx.user.update({
        where: { id: escrowOrder.buyerId },
        data: { depositBalance: { increment: escrowOrder.amount } }
      })
      break

    case 'SELLER_FAVOR':
      // 打款给卖家
      await tx.user.update({
        where: { id: escrowOrder.sellerId },
        data: { depositBalance: { increment: escrowOrder.amount } }
      })
      break

    case 'SPLIT_DECISION':
      // 平分款项
      const halfAmount = escrowOrder.amount / 2
      await tx.user.update({
        where: { id: escrowOrder.buyerId },
        data: { depositBalance: { increment: halfAmount } }
      })
      await tx.user.update({
        where: { id: escrowOrder.sellerId },
        data: { depositBalance: { increment: halfAmount } }
      })
      break
  }

  // 更新托管订单状态
  await tx.escrowOrder.update({
    where: { id: escrowOrder.id },
    data: { status: 'COMPLETED' }
  })

  // 更新订单状态
  await tx.order.update({
    where: { id: escrowOrder.orderId },
    data: {
      status: 'COMPLETED',
      disputeStatus: 'RESOLVED',
      disputeResolvedAt: new Date()
    }
  })
}

// 奖励参与投票的中间人
async function rewardVotingMediators(tx: any, disputeId: string) {
  const votes = await tx.arbitrationVote.findMany({
    where: { disputeId },
    select: { voterId: true }
  })

  for (const vote of votes) {
    // 检查本月是否已获得奖励
    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)

    const existingReward = await tx.mediatorReward.findFirst({
      where: {
        mediatorId: vote.voterId,
        rewardType: 'VOTING_PARTICIPATION',
        earnedAt: { gte: thisMonth }
      }
    })

    if (existingReward) continue // 本月已获得奖励

    // 创建提现券奖励
    const voucher = await tx.withdrawalVoucher.create({
      data: {
        code: `VOTE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount: 10,
        description: '仲裁投票参与奖励',
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天有效期
        issuedBy: 'system'
      }
    })

    // 创建奖励记录
    await tx.mediatorReward.create({
      data: {
        mediatorId: vote.voterId,
        rewardType: 'VOTING_PARTICIPATION',
        voucherId: voucher.id,
        description: '参与仲裁投票奖励',
        earnedAt: new Date()
      }
    })

    // 发送通知
    await tx.notification.create({
      data: {
        userId: vote.voterId,
        type: 'REWARD',
        title: '仲裁投票奖励',
        message: '感谢您参与仲裁投票，已获得10 USDT免手续费提现券',
        data: {
          voucherId: voucher.id,
          amount: 10,
          validUntil: voucher.validUntil
        },
        priority: 'NORMAL'
      }
    })
  }
}
