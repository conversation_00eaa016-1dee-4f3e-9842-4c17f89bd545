import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: { escrowId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { escrowId } = params

    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowId },
      include: {
        order: {
          include: {
            product: true
          }
        },
        buyer: {
          select: { id: true, name: true, email: true }
        },
        seller: {
          select: { id: true, name: true, email: true }
        },
        mediator: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            mediatorReputation: true,
            mediatorSuccessRate: true 
          }
        },
        chatRoom: true,
        disputes: {
          include: {
            reporter: { select: { name: true } },
            reported: { select: { name: true } }
          }
        },
        blockchainTransactions: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    // 验证权限
    const allowedUsers = [
      escrowOrder.buyerId,
      escrowOrder.sellerId,
      escrowOrder.mediatorId
    ]

    if (!allowedUsers.includes(session.user.id) && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: escrowOrder
    })

  } catch (error) {
    console.error('获取托管订单详情失败:', error)
    return NextResponse.json(
      { error: '获取托管订单详情失败' },
      { status: 500 }
    )
  }
}

// 更新托管订单状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { escrowId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { escrowId } = params
    const body = await request.json()
    const { action, data: actionData } = body

    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowId },
      include: {
        order: true,
        buyer: true,
        seller: true,
        mediator: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    let result

    switch (action) {
      case 'fund':
        // 中间人确认资金到账
        if (session.user.id !== escrowOrder.mediatorId) {
          return NextResponse.json(
            { error: '只有中间人可以确认资金到账' },
            { status: 403 }
          )
        }

        if (escrowOrder.status !== 'PENDING') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }

        result = await prisma.$transaction(async (tx) => {
          const updated = await tx.escrowOrder.update({
            where: { id: escrowId },
            data: {
              status: 'FUNDED',
              fundedAt: new Date(),
              bnbTransactionHash: actionData?.txHash
            }
          })

          await tx.order.update({
            where: { id: escrowOrder.orderId },
            data: { status: 'CONFIRMED' }
          })

          return updated
        })

        // 通知卖家发货
        await prisma.notification.create({
          data: {
            userId: escrowOrder.sellerId,
            type: 'ORDER',
            title: '托管资金已到账，请发货',
            message: '中间人已确认收到托管资金，请尽快发货',
            data: { escrowId, orderId: escrowOrder.orderId },
            priority: 'HIGH'
          }
        })
        break

      case 'ship':
        // 卖家确认发货
        if (session.user.id !== escrowOrder.sellerId) {
          return NextResponse.json(
            { error: '只有卖家可以确认发货' },
            { status: 403 }
          )
        }

        if (escrowOrder.status !== 'FUNDED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }

        result = await prisma.$transaction(async (tx) => {
          const updated = await tx.escrowOrder.update({
            where: { id: escrowId },
            data: {
              status: 'SHIPPED',
              shippedAt: new Date()
            }
          })

          await tx.order.update({
            where: { id: escrowOrder.orderId },
            data: {
              status: 'SHIPPED',
              trackingNumber: actionData?.trackingNumber,
              shippingCompany: actionData?.shippingCompany
            }
          })

          return updated
        })

        // 通知买家和中间人
        const shipNotifications = [
          {
            userId: escrowOrder.buyerId,
            type: 'ORDER',
            title: '商品已发货',
            message: `卖家已发货，快递单号：${actionData?.trackingNumber || '待更新'}`,
            data: { escrowId, orderId: escrowOrder.orderId, trackingNumber: actionData?.trackingNumber },
            priority: 'NORMAL'
          },
          {
            userId: escrowOrder.mediatorId,
            type: 'ESCROW',
            title: '托管订单已发货',
            message: '卖家已确认发货，请跟踪物流状态',
            data: { escrowId, orderId: escrowOrder.orderId },
            priority: 'NORMAL'
          }
        ]

        await Promise.all(
          shipNotifications.map(notification =>
            prisma.notification.create({ data: notification }).catch(() => {})
          )
        )
        break

      case 'deliver':
        // 买家确认收货
        if (session.user.id !== escrowOrder.buyerId) {
          return NextResponse.json(
            { error: '只有买家可以确认收货' },
            { status: 403 }
          )
        }

        if (escrowOrder.status !== 'SHIPPED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }

        result = await prisma.$transaction(async (tx) => {
          const updated = await tx.escrowOrder.update({
            where: { id: escrowId },
            data: {
              status: 'DELIVERED',
              deliveredAt: new Date()
            }
          })

          await tx.order.update({
            where: { id: escrowOrder.orderId },
            data: { status: 'DELIVERED' }
          })

          return updated
        })

        // 通知中间人可以打款
        await prisma.notification.create({
          data: {
            userId: escrowOrder.mediatorId,
            type: 'ESCROW',
            title: '买家已确认收货',
            message: '买家已确认收货，可以向卖家打款了',
            data: { escrowId, orderId: escrowOrder.orderId },
            priority: 'HIGH'
          }
        })
        break

      case 'complete':
        // 中间人完成托管（打款给卖家）
        if (session.user.id !== escrowOrder.mediatorId) {
          return NextResponse.json(
            { error: '只有中间人可以完成托管' },
            { status: 403 }
          )
        }

        if (escrowOrder.status !== 'DELIVERED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }

        result = await prisma.$transaction(async (tx) => {
          const updated = await tx.escrowOrder.update({
            where: { id: escrowId },
            data: {
              status: 'COMPLETED',
              completedAt: new Date()
            }
          })

          // 释放买家冻结资金
          await tx.fundFreeze.updateMany({
            where: {
              userId: escrowOrder.buyerId,
              relatedId: escrowId,
              relatedType: 'ESCROW_ORDER',
              status: 'ACTIVE'
            },
            data: {
              status: 'RELEASED',
              settledAt: new Date()
            }
          })

          // 给卖家打款
          const sellerAmount = escrowOrder.amount
          await tx.user.update({
            where: { id: escrowOrder.sellerId },
            data: {
              depositBalance: { increment: sellerAmount }
            }
          })

          // 给中间人支付费用
          const mediatorFee = escrowOrder.mediatorFee - escrowOrder.platformFee
          await tx.user.update({
            where: { id: escrowOrder.mediatorId },
            data: {
              depositBalance: { increment: mediatorFee }
            }
          })

          // 创建交易记录
          await tx.fundTransaction.createMany({
            data: [
              {
                userId: escrowOrder.sellerId,
                type: 'ESCROW_RELEASE',
                amount: sellerAmount,
                description: '托管订单收款',
                relatedId: escrowId,
                metadata: { relatedType: 'ESCROW_ORDER' }
              },
              {
                userId: escrowOrder.mediatorId,
                type: 'MEDIATOR_FEE',
                amount: mediatorFee,
                description: '中间人服务费',
                relatedId: escrowId,
                metadata: { relatedType: 'ESCROW_ORDER' }
              }
            ]
          })

          await tx.order.update({
            where: { id: escrowOrder.orderId },
            data: { status: 'COMPLETED' }
          })

          return updated
        })

        // 通知各方
        const completeNotifications = [
          {
            userId: escrowOrder.sellerId,
            type: 'PAYMENT',
            title: '托管款项已到账',
            message: `您已收到托管订单款项 ${escrowOrder.amount} USDT`,
            data: { escrowId, amount: escrowOrder.amount },
            priority: 'HIGH'
          },
          {
            userId: escrowOrder.buyerId,
            type: 'ORDER',
            title: '托管订单已完成',
            message: '托管订单已成功完成，感谢您的使用',
            data: { escrowId, orderId: escrowOrder.orderId },
            priority: 'NORMAL'
          }
        ]

        await Promise.all(
          completeNotifications.map(notification =>
            prisma.notification.create({ data: notification }).catch(() => {})
          )
        )
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: '操作成功',
      data: result
    })

  } catch (error) {
    console.error('更新托管订单状态失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}
