import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建托管订单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { orderId, mediatorId } = body

    if (!orderId || !mediatorId) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        product: true,
        buyer: true,
        seller: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证订单状态
    if (order.status !== 'PENDING_PAYMENT') {
      return NextResponse.json(
        { error: '订单状态不允许创建托管' },
        { status: 400 }
      )
    }

    // 验证订单金额（必须超过100 USDT）
    if (order.totalAmount < 100) {
      return NextResponse.json(
        { error: '订单金额必须超过100 USDT才能使用托管服务' },
        { status: 400 }
      )
    }

    // 验证买家权限
    if (order.buyerId !== session.user.id) {
      return NextResponse.json(
        { error: '只有买家可以创建托管订单' },
        { status: 403 }
      )
    }

    // 获取中间人信息
    const mediator = await prisma.user.findUnique({
      where: { id: mediatorId },
      select: {
        id: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorDeposit: true,
        bnbWalletAddress: true
      }
    })

    if (!mediator || !mediator.isMediator || mediator.mediatorStatus !== 'ACTIVE') {
      return NextResponse.json(
        { error: '中间人不可用' },
        { status: 400 }
      )
    }

    // 验证中间人保证金是否足够
    const mediatorFrozenAmount = await prisma.fundFreeze.aggregate({
      where: {
        userId: mediatorId,
        status: 'ACTIVE',
        relatedType: 'MEDIATOR_DEPOSIT'
      },
      _sum: { amount: true }
    })

    const availableDeposit = mediatorFrozenAmount._sum.amount || 0
    if (availableDeposit < order.totalAmount) {
      return NextResponse.json(
        { error: '中间人保证金不足以担保此订单' },
        { status: 400 }
      )
    }

    // 计算费用
    const mediatorFee = order.totalAmount * (mediator.mediatorFeeRate || 0.02)
    const platformFee = mediatorFee * 0.3 // 平台抽取30%
    const totalAmount = order.totalAmount + mediatorFee

    // 验证买家余额
    const buyer = await prisma.user.findUnique({
      where: { id: order.buyerId },
      select: { depositBalance: true }
    })

    if (!buyer || buyer.depositBalance < totalAmount) {
      return NextResponse.json(
        { error: '余额不足以支付订单金额和中间人费用' },
        { status: 400 }
      )
    }

    // 创建托管订单
    const escrowOrder = await prisma.$transaction(async (tx) => {
      // 创建托管记录
      const escrow = await tx.escrowOrder.create({
        data: {
          orderId,
          mediatorId,
          buyerId: order.buyerId,
          sellerId: order.sellerId,
          amount: order.totalAmount,
          mediatorFee,
          platformFee,
          status: 'PENDING',
          mediatorWalletAddress: mediator.bnbWalletAddress || ''
        }
      })

      // 扣除买家余额
      await tx.user.update({
        where: { id: order.buyerId },
        data: {
          depositBalance: { decrement: totalAmount }
        }
      })

      // 创建资金冻结记录
      await tx.fundFreeze.create({
        data: {
          userId: order.buyerId,
          amount: totalAmount,
          reason: '托管订单资金',
          status: 'ACTIVE',
          relatedId: escrow.id,
          relatedType: 'ESCROW_ORDER',
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
        }
      })

      // 创建交易记录
      await tx.fundTransaction.create({
        data: {
          userId: order.buyerId,
          type: 'ESCROW_PAYMENT',
          amount: -totalAmount,
          description: `托管订单支付: ${order.product.title}`,
          relatedId: escrow.id,
          metadata: {
            relatedType: 'ESCROW_ORDER',
            orderId,
            mediatorId,
            mediatorFee,
            platformFee
          }
        }
      })

      // 更新订单状态
      await tx.order.update({
        where: { id: orderId },
        data: {
          status: 'PAID',
          paymentConfirmed: true,
          useEscrow: true,
          mediatorId,
          escrowFee: mediatorFee,
          escrowFeeRate: mediator.mediatorFeeRate
        }
      })

      // 创建多方聊天室
      const chatRoom = await tx.escrowChatRoom.create({
        data: {
          escrowOrderId: escrow.id,
          roomCode: `ESCROW_${escrow.id}_${Date.now()}`,
          isActive: true
        }
      })

      return { escrow, chatRoom }
    })

    // 发送通知给各方
    const notifications = [
      // 通知卖家
      {
        userId: order.sellerId,
        type: 'ORDER',
        title: '订单已使用托管服务',
        message: `买家已为订单 ${order.orderNumber} 选择了托管服务，请准备发货`,
        data: {
          orderId,
          escrowId: escrowOrder.escrow.id,
          mediatorId,
          amount: order.totalAmount
        },
        priority: 'HIGH'
      },
      // 通知中间人
      {
        userId: mediatorId,
        type: 'ESCROW',
        title: '新的托管订单',
        message: `您有一个新的托管订单需要处理，订单金额 ${order.totalAmount} USDT`,
        data: {
          orderId,
          escrowId: escrowOrder.escrow.id,
          amount: order.totalAmount,
          fee: mediatorFee
        },
        priority: 'HIGH'
      }
    ]

    await Promise.all(
      notifications.map(notification =>
        prisma.notification.create({ data: notification }).catch(() => {})
      )
    )

    return NextResponse.json({
      success: true,
      message: '托管订单创建成功',
      data: {
        escrowId: escrowOrder.escrow.id,
        orderId,
        amount: order.totalAmount,
        mediatorFee,
        platformFee,
        totalAmount,
        chatRoomCode: escrowOrder.chatRoom.roomCode,
        status: 'PENDING'
      }
    })

  } catch (error) {
    console.error('创建托管订单失败:', error)
    return NextResponse.json(
      { error: '创建托管订单失败' },
      { status: 500 }
    )
  }
}

// 获取可用中间人列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const orderAmount = parseFloat(searchParams.get('amount') || '0')

    if (orderAmount < 100) {
      return NextResponse.json({
        success: true,
        data: [],
        message: '订单金额必须超过100 USDT才能使用托管服务'
      })
    }

    // 获取可用中间人
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        mediatorDeposit: true,
        mediatorExperience: true,
        mediatorIntroduction: true,
        mediatorLastActiveAt: true
      }
    })

    // 过滤保证金足够的中间人
    const availableMediators = []

    for (const mediator of mediators) {
      // 获取中间人的余额信息
      const mediatorBalance = await prisma.user.findUnique({
        where: { id: mediator.id },
        select: {
          availableBalance: true
        }
      })

      if (!mediatorBalance) {
        continue
      }

      // 计算当前锁定金额（活跃担保订单）
      const activeOrdersSum = await prisma.order.aggregate({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        },
        _sum: {
          escrowAmount: true
        }
      })

      const lockedAmount = activeOrdersSum._sum.escrowAmount || 0
      const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount)

      if (availableAmount >= orderAmount) {
        availableMediators.push({
          ...mediator,
          availableDeposit: availableAmount,
          estimatedFee: orderAmount * (mediator.mediatorFeeRate || 0.02)
        })
      }
    }

    // 按信誉度排序
    availableMediators.sort((a, b) => {
      return (b.mediatorReputation || 0) - (a.mediatorReputation || 0)
    })

    return NextResponse.json({
      success: true,
      data: availableMediators
    })

  } catch (error) {
    console.error('获取中间人列表失败:', error)
    return NextResponse.json(
      { error: '获取中间人列表失败' },
      { status: 500 }
    )
  }
}
