import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import PDFDocument from 'pdfkit'

// 导出托管聊天记录
export async function POST(
  request: NextRequest,
  { params }: { params: { chatRoomId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    // 获取聊天室信息
    const chatRoom = await (prisma as any).escrowChatRoom.findUnique({
      where: { id: params.chatRoomId },
      include: {
        order: {
          include: {
            product: {
              select: { title: true, price: true }
            },
            buyer: {
              select: { id: true, name: true, email: true }
            },
            seller: {
              select: { id: true, name: true, email: true }
            },
            mediator: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        messages: {
          include: {
            sender: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    })

    if (!chatRoom) {
      return NextResponse.json(
        { success: false, error: '聊天室不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否有权限导出聊天记录
    const hasAccess = chatRoom.participants.includes(session.user.id) ||
                     session.user.role === 'ADMIN'

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: '无权限导出此聊天记录' },
        { status: 403 }
      )
    }

    // 确定用户角色
    const getUserRole = (userId: string) => {
      if (userId === chatRoom.order.buyerId) return '买家'
      if (userId === chatRoom.order.sellerId) return '卖家'
      if (userId === chatRoom.order.mediatorId) return '中间人'
      return '管理员'
    }

    // 创建PDF文档
    const doc = new PDFDocument({ margin: 50 })
    const chunks: Buffer[] = []

    doc.on('data', (chunk) => chunks.push(chunk))

    // 添加中文字体支持（如果有的话）
    try {
      // 这里可以添加中文字体，如果没有则使用默认字体
      // doc.font('path/to/chinese-font.ttf')
    } catch (error) {
      // 使用默认字体
    }

    // 文档标题
    doc.fontSize(20).text('托管聊天记录导出', { align: 'center' })
    doc.moveDown()

    // 订单信息
    doc.fontSize(14).text('订单信息', { underline: true })
    doc.fontSize(12)
    doc.text(`订单号: ${chatRoom.order.orderNumber}`)
    doc.text(`商品: ${chatRoom.order.product.title}`)
    doc.text(`金额: ${chatRoom.order.totalAmount} USDT`)
    doc.text(`订单状态: ${chatRoom.order.status}`)
    doc.text(`创建时间: ${new Date(chatRoom.order.createdAt).toLocaleString('zh-CN')}`)
    doc.moveDown()

    // 参与者信息
    doc.fontSize(14).text('参与者信息', { underline: true })
    doc.fontSize(12)
    if (chatRoom.order.buyer) {
      doc.text(`买家: ${chatRoom.order.buyer.name} (${chatRoom.order.buyer.email})`)
    }
    if (chatRoom.order.seller) {
      doc.text(`卖家: ${chatRoom.order.seller.name} (${chatRoom.order.seller.email})`)
    }
    if (chatRoom.order.mediator) {
      doc.text(`中间人: ${chatRoom.order.mediator.name} (${chatRoom.order.mediator.email})`)
    }
    doc.moveDown()

    // 聊天记录
    doc.fontSize(14).text('聊天记录', { underline: true })
    doc.moveDown()

    if (chatRoom.messages.length === 0) {
      doc.fontSize(12).text('暂无聊天记录')
    } else {
      chatRoom.messages.forEach((message: any) => {
        const senderRole = getUserRole(message.senderId)
        const timestamp = new Date(message.createdAt).toLocaleString('zh-CN')
        
        // 检查是否需要新页面
        if (doc.y > 700) {
          doc.addPage()
        }

        // 消息头部
        doc.fontSize(10)
          .fillColor('gray')
          .text(`[${timestamp}] ${message.sender.name} (${senderRole})`, { continued: false })

        // 消息内容
        doc.fontSize(11)
          .fillColor('black')
          .text(message.content || '[文件消息]', { indent: 20 })

        // 如果是文件消息，添加文件信息
        if (message.fileUrl && message.fileName) {
          doc.fontSize(9)
            .fillColor('blue')
            .text(`文件: ${message.fileName}`, { indent: 20 })
          
          if (message.fileSize) {
            const fileSizeKB = (message.fileSize / 1024).toFixed(2)
            doc.text(`大小: ${fileSizeKB} KB`, { indent: 20 })
          }
        }

        doc.moveDown(0.5)
      })
    }

    // 导出信息
    doc.moveDown()
    doc.fontSize(10)
      .fillColor('gray')
      .text(`导出时间: ${new Date().toLocaleString('zh-CN')}`)
    doc.text(`导出人: ${session.user.name} (${session.user.email})`)
    doc.text(`聊天室ID: ${chatRoom.id}`)

    // 结束PDF文档
    doc.end()

    // 等待PDF生成完成
    const pdfBuffer = await new Promise<Buffer>((resolve) => {
      doc.on('end', () => {
        resolve(Buffer.concat(chunks))
      })
    })

    // 记录导出操作
    await prisma.orderLog.create({
      data: {
        orderId: chatRoom.orderId,
        operatorId: session.user.id,
        action: 'EXPORT_CHAT_HISTORY',
        description: '导出托管聊天记录',
        newValue: JSON.stringify({
          chatRoomId: chatRoom.id,
          messageCount: chatRoom.messages.length,
          exportedAt: new Date().toISOString()
        }),
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        userAgent: request.headers.get('user-agent')
      }
    })

    // 返回PDF文件
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="escrow-chat-${chatRoom.order.orderNumber}-${new Date().toISOString().split('T')[0]}.pdf"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    })

  } catch (error) {
    console.error('导出聊天记录失败:', error)
    return NextResponse.json(
      { success: false, error: '导出聊天记录失败' },
      { status: 500 }
    )
  }
}

// 获取聊天记录导出历史
export async function GET(
  _request: NextRequest,
  { params }: { params: { chatRoomId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    // 获取聊天室信息
    const chatRoom = await (prisma as any).escrowChatRoom.findUnique({
      where: { id: params.chatRoomId }
    })

    if (!chatRoom) {
      return NextResponse.json(
        { success: false, error: '聊天室不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const hasAccess = chatRoom.participants.includes(session.user.id) ||
                     session.user.role === 'ADMIN'

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: '无权限查看导出历史' },
        { status: 403 }
      )
    }

    // 获取导出历史
    const exportHistory = await prisma.orderLog.findMany({
      where: {
        orderId: chatRoom.orderId,
        action: 'EXPORT_CHAT_HISTORY'
      },
      include: {
        operator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        exports: exportHistory.map(log => ({
          id: log.id,
          exportedAt: log.createdAt,
          operator: log.operator,
          details: log.newValue ? JSON.parse(log.newValue) : null
        }))
      }
    })

  } catch (error) {
    console.error('获取导出历史失败:', error)
    return NextResponse.json(
      { success: false, error: '获取导出历史失败' },
      { status: 500 }
    )
  }
}
