import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管聊天室参与者信息
export async function GET(
  request: NextRequest,
  { params }: { params: { chatRoomId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { chatRoomId } = params

    // 获取聊天室信息
    const chatRoom = await prisma.escrowChatRoom.findUnique({
      where: { id: chatRoomId },
      include: {
        escrowOrder: {
          include: {
            mediator: {
              select: {
                id: true,
                name: true,
                avatar: true,
                mediatorReputation: true,
                isMediator: true,
                mediatorStatus: true
              }
            },
            buyer: {
              select: {
                id: true,
                name: true,
                avatar: true,
                creditScore: true,
                creditLevel: true
              }
            },
            seller: {
              select: {
                id: true,
                name: true,
                avatar: true,
                creditScore: true,
                creditLevel: true
              }
            }
          }
        }
      }
    })

    if (!chatRoom) {
      return NextResponse.json(
        { error: '聊天室不存在' },
        { status: 404 }
      )
    }

    const escrowOrder = chatRoom.escrowOrder

    // 验证用户权限
    const hasPermission = [
      escrowOrder.mediatorId,
      escrowOrder.buyerId,
      escrowOrder.sellerId
    ].includes(session.user.id)

    // 检查是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    const isAdmin = user?.role === 'ADMIN'

    if (!hasPermission && !isAdmin) {
      return NextResponse.json(
        { error: '无权限访问此聊天室' },
        { status: 403 }
      )
    }

    // 构建参与者列表
    const participants = []

    // 添加管理员（如果当前用户是管理员或相关方）
    if (isAdmin || hasPermission) {
      participants.push({
        id: 'admin',
        name: '平台管理员',
        role: 'ADMIN',
        avatar: null,
        isOnline: true, // 管理员始终在线
        lastSeen: new Date().toISOString()
      })
    }

    // 添加中间人
    participants.push({
      id: escrowOrder.mediator.id,
      name: escrowOrder.mediator.name,
      role: 'MEDIATOR',
      avatar: escrowOrder.mediator.avatar,
      reputation: escrowOrder.mediator.mediatorReputation,
      status: escrowOrder.mediator.mediatorStatus,
      isOnline: await checkUserOnlineStatus(escrowOrder.mediator.id),
      lastSeen: await getLastSeenTime(escrowOrder.mediator.id)
    })

    // 添加买家
    participants.push({
      id: escrowOrder.buyer.id,
      name: escrowOrder.buyer.name,
      role: 'BUYER',
      avatar: escrowOrder.buyer.avatar,
      creditScore: escrowOrder.buyer.creditScore,
      creditLevel: escrowOrder.buyer.creditLevel,
      isOnline: await checkUserOnlineStatus(escrowOrder.buyer.id),
      lastSeen: await getLastSeenTime(escrowOrder.buyer.id)
    })

    // 添加卖家
    participants.push({
      id: escrowOrder.seller.id,
      name: escrowOrder.seller.name,
      role: 'SELLER',
      avatar: escrowOrder.seller.avatar,
      creditScore: escrowOrder.seller.creditScore,
      creditLevel: escrowOrder.seller.creditLevel,
      isOnline: await checkUserOnlineStatus(escrowOrder.seller.id),
      lastSeen: await getLastSeenTime(escrowOrder.seller.id)
    })

    return NextResponse.json({
      success: true,
      data: {
        participants,
        chatRoom: {
          id: chatRoom.id,
          roomCode: chatRoom.roomCode,
          isActive: chatRoom.isActive,
          createdAt: chatRoom.createdAt
        },
        escrowOrder: {
          id: escrowOrder.id,
          orderId: escrowOrder.orderId,
          status: escrowOrder.status,
          amount: escrowOrder.amount
        },
        currentUser: {
          id: session.user.id,
          role: getUserRole(session.user.id, escrowOrder, isAdmin)
        }
      }
    })

  } catch (error) {
    console.error('获取聊天室参与者失败:', error)
    return NextResponse.json(
      { error: '获取参与者信息失败' },
      { status: 500 }
    )
  }
}

// 辅助函数：检查用户在线状态
async function checkUserOnlineStatus(userId: string): Promise<boolean> {
  try {
    // 检查用户最近的会话记录
    const recentSession = await prisma.userSession.findFirst({
      where: {
        userId,
        lastActiveAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000) // 5分钟内活跃
        }
      },
      orderBy: { lastActiveAt: 'desc' }
    })

    return !!recentSession
  } catch {
    return false
  }
}

// 辅助函数：获取最后在线时间
async function getLastSeenTime(userId: string): Promise<string | null> {
  try {
    const lastSession = await prisma.userSession.findFirst({
      where: { userId },
      orderBy: { lastActiveAt: 'desc' },
      select: { lastActiveAt: true }
    })

    return lastSession?.lastActiveAt?.toISOString() || null
  } catch {
    return null
  }
}

// 辅助函数：获取用户在当前托管订单中的角色
function getUserRole(
  userId: string, 
  escrowOrder: any, 
  isAdmin: boolean
): string {
  if (isAdmin) return 'ADMIN'
  if (userId === escrowOrder.mediatorId) return 'MEDIATOR'
  if (userId === escrowOrder.buyerId) return 'BUYER'
  if (userId === escrowOrder.sellerId) return 'SELLER'
  return 'UNKNOWN'
}
