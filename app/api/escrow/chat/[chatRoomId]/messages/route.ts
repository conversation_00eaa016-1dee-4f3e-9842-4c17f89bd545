import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管聊天室消息
export async function GET(
  request: NextRequest,
  { params }: { params: { chatRoomId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')

    // 获取聊天室信息
    const chatRoom = await (prisma as any).escrowChatRoom.findUnique({
      where: { id: params.chatRoomId },
      include: {
        order: {
          include: {
            buyer: {
              select: { id: true, name: true, email: true }
            },
            seller: {
              select: { id: true, name: true, email: true }
            },
            mediator: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    })

    if (!chatRoom) {
      return NextResponse.json(
        { success: false, error: '聊天室不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否有权限访问此聊天室
    const chatParticipants = Array.isArray(chatRoom.participants) ? chatRoom.participants : []
    const hasAccess = chatParticipants.includes(session.user.id) ||
                     session.user.role === 'ADMIN'

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: '无权限访问此聊天室' },
        { status: 403 }
      )
    }

    // 获取聊天消息（使用扩展的消息表）
    const messages = await (prisma as any).escrowChatMessage.findMany({
      where: {
        chatRoomId: params.chatRoomId
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    // 构建参与者信息
    const participants: Array<{
      id: string
      name: string | null
      email: string | null
      role: string
      isOnline: boolean
    }> = []
    
    // 买家
    if (chatRoom.order.buyer) {
      participants.push({
        id: chatRoom.order.buyer.id,
        name: chatRoom.order.buyer.name,
        email: chatRoom.order.buyer.email,
        role: 'buyer',
        isOnline: false // 这里可以集成在线状态检测
      })
    }

    // 卖家
    if (chatRoom.order.seller) {
      participants.push({
        id: chatRoom.order.seller.id,
        name: chatRoom.order.seller.name,
        email: chatRoom.order.seller.email,
        role: 'seller',
        isOnline: false
      })
    }

    // 中间人
    if (chatRoom.order.mediator) {
      participants.push({
        id: chatRoom.order.mediator.id,
        name: chatRoom.order.mediator.name,
        email: chatRoom.order.mediator.email,
        role: 'mediator',
        isOnline: false
      })
    }

    // 管理员（如果已加入）
    const adminParticipants = await prisma.user.findMany({
      where: {
        id: {
          in: chatParticipants.filter((id: string) =>
            ![chatRoom.order.buyerId, chatRoom.order.sellerId, chatRoom.order.mediatorId].includes(id)
          )
        },
        role: 'ADMIN'
      },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    adminParticipants.forEach(admin => {
      participants.push({
        id: admin.id,
        name: admin.name,
        email: admin.email,
        role: 'admin',
        isOnline: false
      })
    })

    // 确定用户角色
    const getUserRole = (userId: string) => {
      if (userId === chatRoom.order.buyerId) return 'buyer'
      if (userId === chatRoom.order.sellerId) return 'seller'
      if (userId === chatRoom.order.mediatorId) return 'mediator'
      return 'admin'
    }

    // 格式化消息
    const formattedMessages = messages.map((message: any) => ({
      id: message.id,
      content: message.content,
      messageType: message.messageType,
      status: message.status,
      createdAt: message.createdAt,
      fileUrl: message.fileUrl,
      fileName: message.fileName,
      fileSize: message.fileSize,
      fileMimeType: message.fileMimeType,
      fileMetadata: message.fileMetadata,
      sender: message.sender,
      senderRole: getUserRole(message.senderId),
      isSystemMessage: message.messageType === 'SYSTEM'
    }))

    // 检查是否可以导出聊天记录
    const canExport = [chatRoom.order.buyerId, chatRoom.order.sellerId, chatRoom.order.mediatorId].includes(session.user.id) ||
                     session.user.role === 'ADMIN'

    return NextResponse.json({
      success: true,
      data: {
        messages: formattedMessages,
        participants,
        chatRoom: {
          id: chatRoom.id,
          orderId: chatRoom.orderId,
          roomType: chatRoom.roomType,
          status: chatRoom.status
        },
        canExport
      }
    })

  } catch (error) {
    console.error('获取托管聊天消息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取聊天消息失败' },
      { status: 500 }
    )
  }
}

// 发送托管聊天消息
export async function POST(
  request: NextRequest,
  { params }: { params: { chatRoomId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { content, messageType = 'TEXT', fileUrl, fileName, fileSize, fileMimeType, fileMetadata } = await request.json()

    if (!content && !fileUrl) {
      return NextResponse.json(
        { success: false, error: '消息内容不能为空' },
        { status: 400 }
      )
    }

    // 获取聊天室信息
    const chatRoom = await (prisma as any).escrowChatRoom.findUnique({
      where: { id: params.chatRoomId },
      include: {
        order: true
      }
    })

    if (!chatRoom) {
      return NextResponse.json(
        { success: false, error: '聊天室不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否有权限在此聊天室发送消息
    const postChatParticipants = Array.isArray(chatRoom.participants) ? chatRoom.participants : []
    const hasAccess = postChatParticipants.includes(session.user.id) ||
                     session.user.role === 'ADMIN'

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: '无权限在此聊天室发送消息' },
        { status: 403 }
      )
    }

    // 检查聊天室状态
    if (chatRoom.status === 'CLOSED') {
      return NextResponse.json(
        { success: false, error: '聊天室已关闭' },
        { status: 400 }
      )
    }

    // 创建消息
    const message = await (prisma as any).escrowChatMessage.create({
      data: {
        chatRoomId: params.chatRoomId,
        senderId: session.user.id,
        content: content || '',
        messageType,
        fileUrl,
        fileName,
        fileSize,
        fileMimeType,
        fileMetadata,
        status: 'SENT'
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // 更新聊天室最后活动时间
    await (prisma as any).escrowChatRoom.update({
      where: { id: params.chatRoomId },
      data: { updatedAt: new Date() }
    })

    // 创建系统通知（如果是重要消息）
    if (messageType === 'SYSTEM' || content.includes('争议') || content.includes('仲裁')) {
      // 通知所有参与者
      const notifications = postChatParticipants
        .filter((participantId: string) => participantId !== session.user.id)
        .map((participantId: string) => ({
          userId: participantId,
          type: 'ESCROW_MESSAGE',
          title: '托管聊天室新消息',
          message: `订单 ${chatRoom.order.orderNumber} 的托管聊天室有新消息`,
          relatedId: chatRoom.orderId,
          relatedType: 'ORDER'
        }))

      if (notifications.length > 0) {
        // TODO: 实现通知系统
        console.log('需要发送通知:', notifications)
      }
    }

    return NextResponse.json({
      success: true,
      data: message
    })

  } catch (error) {
    console.error('发送托管聊天消息失败:', error)
    return NextResponse.json(
      { success: false, error: '发送消息失败' },
      { status: 500 }
    )
  }
}
