import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { v4 as uuidv4 } from 'uuid'

// 文件上传配置
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  uploadDir: process.env.UPLOAD_DIR || './public/uploads'
}

// 托管聊天文件上传
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const escrowOrderId = formData.get('escrowOrderId') as string

    if (!file) {
      return NextResponse.json(
        { error: '未选择文件' },
        { status: 400 }
      )
    }

    if (!escrowOrderId) {
      return NextResponse.json(
        { error: '缺少托管订单ID' },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > UPLOAD_CONFIG.maxFileSize) {
      return NextResponse.json(
        { error: `文件大小不能超过 ${UPLOAD_CONFIG.maxFileSize / 1024 / 1024}MB` },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!UPLOAD_CONFIG.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: '不支持的文件类型' },
        { status: 400 }
      )
    }

    // 验证托管订单权限
    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowOrderId },
      select: {
        id: true,
        mediatorId: true,
        buyerId: true,
        sellerId: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    // 检查用户权限
    const hasPermission = [
      escrowOrder.mediatorId,
      escrowOrder.buyerId,
      escrowOrder.sellerId
    ].includes(session.user.id)

    // 检查是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    const isAdmin = user?.role === 'ADMIN'

    if (!hasPermission && !isAdmin) {
      return NextResponse.json(
        { error: '无权限上传文件到此聊天室' },
        { status: 403 }
      )
    }

    // 生成文件名和路径
    const fileExtension = file.name.split('.').pop()
    const fileName = `${uuidv4()}.${fileExtension}`
    const uploadPath = join(UPLOAD_CONFIG.uploadDir, 'escrow-chat', escrowOrderId)
    const filePath = join(uploadPath, fileName)
    const publicUrl = `/uploads/escrow-chat/${escrowOrderId}/${fileName}`

    try {
      // 确保上传目录存在
      await mkdir(uploadPath, { recursive: true })

      // 保存文件
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(filePath, buffer)

      // 记录文件信息到数据库
      const fileRecord = await prisma.$transaction(async (tx) => {
        // 创建文件记录
        const record = await tx.uploadedFile.create({
          data: {
            originalName: file.name,
            fileName,
            filePath: publicUrl,
            fileSize: file.size,
            mimeType: file.type,
            uploadedBy: session.user.id,
            relatedType: 'ESCROW_CHAT',
            relatedId: escrowOrderId,
            metadata: {
              escrowOrderId,
              uploadedAt: new Date().toISOString(),
              userAgent: request.headers.get('user-agent')
            }
          }
        })

        // 记录安全日志
        await tx.securityLog.create({
          data: {
            userId: session.user.id,
            action: 'ESCROW_CHAT_FILE_UPLOAD',
            details: {
              fileName: file.name,
              fileSize: file.size,
              mimeType: file.type,
              escrowOrderId,
              fileId: record.id
            },
            ipAddress: request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown'
          }
        })

        return record
      })

      return NextResponse.json({
        success: true,
        message: '文件上传成功',
        data: {
          fileId: fileRecord.id,
          fileName: file.name,
          fileUrl: publicUrl,
          fileSize: file.size,
          mimeType: file.type,
          uploadedAt: fileRecord.createdAt
        }
      })

    } catch (uploadError) {
      console.error('文件保存失败:', uploadError)
      return NextResponse.json(
        { error: '文件保存失败' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { error: '文件上传失败' },
      { status: 500 }
    )
  }
}

// 获取托管聊天文件列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const escrowOrderId = searchParams.get('escrowOrderId')

    if (!escrowOrderId) {
      return NextResponse.json(
        { error: '缺少托管订单ID' },
        { status: 400 }
      )
    }

    // 验证权限
    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowOrderId },
      select: {
        mediatorId: true,
        buyerId: true,
        sellerId: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    const hasPermission = [
      escrowOrder.mediatorId,
      escrowOrder.buyerId,
      escrowOrder.sellerId
    ].includes(session.user.id)

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (!hasPermission && user?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无权限查看文件' },
        { status: 403 }
      )
    }

    // 获取文件列表
    const files = await prisma.uploadedFile.findMany({
      where: {
        relatedType: 'ESCROW_CHAT',
        relatedId: escrowOrderId
      },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: {
        files: files.map(file => ({
          id: file.id,
          originalName: file.originalName,
          fileName: file.fileName,
          fileUrl: file.filePath,
          fileSize: file.fileSize,
          mimeType: file.mimeType,
          uploadedAt: file.createdAt,
          uploader: file.uploader
        }))
      }
    })

  } catch (error) {
    console.error('获取文件列表失败:', error)
    return NextResponse.json(
      { error: '获取文件列表失败' },
      { status: 500 }
    )
  }
}
