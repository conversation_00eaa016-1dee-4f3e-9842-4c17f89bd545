import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管服务配置
export async function GET() {
  try {
    // 返回托管服务的基本配置
    const config = {
      minAmount: 100, // 最低托管金额 (USDT)
      platformFeeRate: 0.3, // 平台费率 30%
      minMediatorFeeRate: 0.01, // 最低中间人费率 1%
      maxMediatorFeeRate: 0.30, // 最高中间人费率 30%
      arbitrationVotingPeriod: 7 * 24 * 60 * 60 * 1000, // 仲裁投票期限 7天
      rewardCouponValue: 10, // 奖励券面值 10 USDT
      rewardCouponValidDays: 7, // 奖励券有效期 7天
      maxRewardCouponsPerMonth: 1 // 每月最多获得奖励券数量
    }

    return NextResponse.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('获取托管配置失败:', error)
    return NextResponse.json(
      { success: false, error: '获取托管配置失败' },
      { status: 500 }
    )
  }
}

// 更新托管服务配置 (管理员专用)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '需要管理员权限' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { 
      minAmount, 
      platformFeeRate, 
      minMediatorFeeRate, 
      maxMediatorFeeRate,
      arbitrationVotingPeriod,
      rewardCouponValue,
      rewardCouponValidDays,
      maxRewardCouponsPerMonth
    } = body

    // 验证配置参数
    if (minAmount < 0 || platformFeeRate < 0 || platformFeeRate > 1) {
      return NextResponse.json(
        { success: false, error: '配置参数无效' },
        { status: 400 }
      )
    }

    if (minMediatorFeeRate < 0 || maxMediatorFeeRate > 1 || minMediatorFeeRate > maxMediatorFeeRate) {
      return NextResponse.json(
        { success: false, error: '中间人费率配置无效' },
        { status: 400 }
      )
    }

    // 这里可以将配置保存到数据库或配置文件
    // 目前返回成功响应
    return NextResponse.json({
      success: true,
      message: '托管配置更新成功'
    })
  } catch (error) {
    console.error('更新托管配置失败:', error)
    return NextResponse.json(
      { success: false, error: '更新托管配置失败' },
      { status: 500 }
    )
  }
}
