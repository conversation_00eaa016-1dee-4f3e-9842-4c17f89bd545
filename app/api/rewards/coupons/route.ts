import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的奖励券列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // ACTIVE, USED, EXPIRED, REVOKED
    const type = searchParams.get('type') // WITHDRAWAL_FEE_WAIVER, PLATFORM_CREDIT
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const where: any = {
      userId: session.user.id
    }

    if (status) {
      where.status = status
    }

    if (type) {
      where.type = type
    }

    const coupons = await prisma.rewardCoupon.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.rewardCoupon.count({ where })

    // 统计各状态的券数量
    const stats = await prisma.rewardCoupon.groupBy({
      by: ['status'],
      where: { userId: session.user.id },
      _count: {
        status: true
      }
    })

    const statusStats = stats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      success: true,
      data: {
        coupons,
        stats: statusStats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取奖励券失败:', error)
    return NextResponse.json(
      { success: false, error: '获取奖励券失败' },
      { status: 500 }
    )
  }
}

// 管理员发放奖励券
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: session.user.id, role: 'ADMIN' }
    })

    if (!admin) {
      return NextResponse.json(
        { success: false, error: '需要管理员权限' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { 
      userIds, 
      type, 
      value, 
      description, 
      validDays = 7 
    } = body

    // 验证输入
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { success: false, error: '请选择用户' },
        { status: 400 }
      )
    }

    if (!type || !value || value <= 0) {
      return NextResponse.json(
        { success: false, error: '请填写完整的券信息' },
        { status: 400 }
      )
    }

    const expiresAt = new Date(Date.now() + validDays * 24 * 60 * 60 * 1000)

    // 批量创建奖励券
    const coupons = await Promise.all(
      userIds.map(userId => 
        prisma.rewardCoupon.create({
          data: {
            userId,
            type,
            value,
            description,
            expiresAt
          },
          include: {
            user: { select: { id: true, name: true, email: true } }
          }
        })
      )
    )

    return NextResponse.json({
      success: true,
      message: `成功发放 ${coupons.length} 张奖励券`,
      data: coupons
    })
  } catch (error) {
    console.error('发放奖励券失败:', error)
    return NextResponse.json(
      { success: false, error: '发放奖励券失败' },
      { status: 500 }
    )
  }
}
