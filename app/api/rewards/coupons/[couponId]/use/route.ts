import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 使用奖励券
export async function POST(
  request: NextRequest,
  { params }: { params: { couponId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { context } = body // 使用场景，如 'withdrawal', 'platform_fee' 等

    // 获取奖励券
    const coupon = await prisma.rewardCoupon.findUnique({
      where: { id: params.couponId },
      include: {
        user: true
      }
    })

    if (!coupon) {
      return NextResponse.json(
        { success: false, error: '奖励券不存在' },
        { status: 404 }
      )
    }

    // 检查所有权
    if (coupon.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: '无权使用此奖励券' },
        { status: 403 }
      )
    }

    // 检查券状态
    if (coupon.status !== 'ACTIVE') {
      return NextResponse.json(
        { success: false, error: '奖励券不可用' },
        { status: 400 }
      )
    }

    // 检查是否过期
    if (new Date() > coupon.expiresAt) {
      // 自动标记为过期
      await prisma.rewardCoupon.update({
        where: { id: params.couponId },
        data: { status: 'EXPIRED' }
      })

      return NextResponse.json(
        { success: false, error: '奖励券已过期' },
        { status: 400 }
      )
    }

    // 验证使用场景
    if (coupon.type === 'WITHDRAWAL_FEE_WAIVER' && context !== 'withdrawal') {
      return NextResponse.json(
        { success: false, error: '此券只能用于提现免手续费' },
        { status: 400 }
      )
    }

    // 标记为已使用
    const usedCoupon = await prisma.rewardCoupon.update({
      where: { id: params.couponId },
      data: {
        status: 'USED',
        usedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      message: '奖励券使用成功',
      data: {
        coupon: usedCoupon,
        discount: coupon.value
      }
    })
  } catch (error) {
    console.error('使用奖励券失败:', error)
    return NextResponse.json(
      { success: false, error: '使用奖励券失败' },
      { status: 500 }
    )
  }
}

// 检查奖励券可用性
export async function GET(
  request: NextRequest,
  { params }: { params: { couponId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const context = searchParams.get('context')

    const coupon = await prisma.rewardCoupon.findUnique({
      where: { id: params.couponId }
    })

    if (!coupon) {
      return NextResponse.json(
        { success: false, error: '奖励券不存在' },
        { status: 404 }
      )
    }

    if (coupon.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: '无权访问此奖励券' },
        { status: 403 }
      )
    }

    // 检查可用性
    const isExpired = new Date() > coupon.expiresAt
    const isUsable = coupon.status === 'ACTIVE' && !isExpired

    // 检查使用场景匹配
    let contextMatch = true
    if (context && coupon.type === 'WITHDRAWAL_FEE_WAIVER') {
      contextMatch = context === 'withdrawal'
    }

    return NextResponse.json({
      success: true,
      data: {
        coupon,
        isUsable: isUsable && contextMatch,
        reason: !isUsable 
          ? (isExpired ? '已过期' : '不可用') 
          : (!contextMatch ? '使用场景不匹配' : null)
      }
    })
  } catch (error) {
    console.error('检查奖励券失败:', error)
    return NextResponse.json(
      { success: false, error: '检查奖励券失败' },
      { status: 500 }
    )
  }
}
