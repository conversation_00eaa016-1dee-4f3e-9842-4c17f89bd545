import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { GuaranteeSystem } from '@/lib/guarantee-system'
import { prisma } from '@/lib/prisma'

// 申请提现担保金
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, description, walletAddress } = body

    // 验证参数
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '提现金额必须大于0' },
        { status: 400 }
      )
    }

    if (!description) {
      return NextResponse.json(
        { error: '请提供提现说明' },
        { status: 400 }
      )
    }

    if (!walletAddress) {
      return NextResponse.json(
        { error: '请提供提现钱包地址' },
        { status: 400 }
      )
    }

    // 检查可提现金额
    const maxWithdrawable = await GuaranteeSystem.getMaxWithdrawableAmount(session.user.id)
    
    if (amount > maxWithdrawable) {
      return NextResponse.json(
        { 
          error: `提现金额超过限制，当前可提现金额: ${maxWithdrawable} USDT`,
          maxWithdrawable 
        },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        guaranteeLevel: true,
        bnbWalletAddress: true,
        bnbWalletVerified: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 验证钱包地址
    if (!user.bnbWalletVerified) {
      return NextResponse.json(
        { error: '请先验证您的钱包地址' },
        { status: 400 }
      )
    }

    if (user.bnbWalletAddress !== walletAddress) {
      return NextResponse.json(
        { error: '提现地址必须与已验证的钱包地址一致' },
        { status: 400 }
      )
    }

    // 获取担保金等级配置
    const levelConfig = await prisma.guaranteeLevel.findUnique({
      where: { level: user.guaranteeLevel }
    })

    // 计算提现手续费
    const feeRate = levelConfig?.withdrawalFeeRate || 0.01 // 默认1%
    const fee = amount * feeRate
    const actualAmount = amount - fee

    // 创建提现申请记录
    const withdrawalRequest = await prisma.withdrawal.create({
      data: {
        userId: session.user.id,
        amount,
        fee,
        actualAmount,
        method: 'GUARANTEE_BALANCE',
        walletAddress,
        description,
        status: 'PENDING',
        metadata: {
          guaranteeLevel: user.guaranteeLevel,
          feeRate,
          requestedAt: new Date().toISOString()
        }
      }
    })

    // 冻结相应的担保金
    const freezeResult = await GuaranteeSystem.freeze(
      session.user.id,
      amount,
      `提现申请冻结 - 申请ID: ${withdrawalRequest.id}`,
      'WITHDRAWAL',
      withdrawalRequest.id
    )

    if (!freezeResult.success) {
      // 如果冻结失败，删除提现申请
      await prisma.withdrawal.delete({
        where: { id: withdrawalRequest.id }
      })

      return NextResponse.json(
        { error: freezeResult.error || '冻结资金失败' },
        { status: 400 }
      )
    }

    // 更新提现申请状态
    await prisma.withdrawal.update({
      where: { id: withdrawalRequest.id },
      data: {
        status: 'FROZEN',
        metadata: {
          ...withdrawalRequest.metadata,
          freezeTransactionId: freezeResult.transactionId,
          frozenAt: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '提现申请已提交，等待处理',
      data: {
        withdrawalId: withdrawalRequest.id,
        amount,
        fee,
        actualAmount,
        status: 'FROZEN',
        estimatedProcessTime: '1-24小时'
      }
    })

  } catch (error) {
    console.error('提现申请失败:', error)
    return NextResponse.json(
      { error: '提现申请失败' },
      { status: 500 }
    )
  }
}

// 获取提现记录
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')

    const where: any = { userId: session.user.id }
    if (status) {
      where.status = status.toUpperCase()
    }

    const skip = (page - 1) * limit

    const [withdrawals, total] = await Promise.all([
      prisma.withdrawal.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip,
        select: {
          id: true,
          amount: true,
          fee: true,
          actualAmount: true,
          method: true,
          walletAddress: true,
          description: true,
          status: true,
          txHash: true,
          processedAt: true,
          createdAt: true,
          metadata: true
        }
      }),
      prisma.withdrawal.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        withdrawals,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasMore: skip + limit < total
        }
      }
    })

  } catch (error) {
    console.error('获取提现记录失败:', error)
    return NextResponse.json(
      { error: '获取提现记录失败' },
      { status: 500 }
    )
  }
}
