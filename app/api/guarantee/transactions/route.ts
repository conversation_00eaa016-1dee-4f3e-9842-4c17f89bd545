import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { GuaranteeSystem, GuaranteeTransactionType } from '@/lib/guarantee-system'

// 获取用户担保金交易历史
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type') as GuaranteeTransactionType
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const options: any = {
      limit,
      offset: (page - 1) * limit
    }

    if (type && Object.values(GuaranteeTransactionType).includes(type)) {
      options.type = type
    }

    if (startDate) {
      options.startDate = new Date(startDate)
    }

    if (endDate) {
      options.endDate = new Date(endDate)
    }

    // 获取交易历史
    const { transactions, total } = await GuaranteeSystem.getUserTransactionHistory(
      session.user.id,
      options
    )

    // 格式化交易数据
    const formattedTransactions = transactions.map(tx => ({
      id: tx.id,
      type: tx.type,
      amount: tx.amount,
      balanceBefore: tx.balanceBefore,
      balanceAfter: tx.balanceAfter,
      frozenBefore: tx.frozenBefore,
      frozenAfter: tx.frozenAfter,
      description: tx.description,
      relatedType: tx.relatedType,
      relatedId: tx.relatedId,
      txHash: tx.txHash,
      status: tx.status,
      operator: tx.operator ? {
        id: tx.operator.id,
        name: tx.operator.name
      } : null,
      createdAt: tx.createdAt,
      // 添加友好的显示信息
      displayInfo: getTransactionDisplayInfo(tx)
    }))

    // 计算统计信息
    const stats = {
      totalTransactions: total,
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalEarnings: 0,
      totalSpending: 0
    }

    transactions.forEach(tx => {
      switch (tx.type) {
        case GuaranteeTransactionType.DEPOSIT:
          stats.totalDeposits += tx.amount
          break
        case GuaranteeTransactionType.WITHDRAW:
          stats.totalWithdrawals += Math.abs(tx.amount)
          break
        case GuaranteeTransactionType.EARN:
          stats.totalEarnings += tx.amount
          break
        case GuaranteeTransactionType.SPEND:
          stats.totalSpending += Math.abs(tx.amount)
          break
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        stats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasMore: (page - 1) * limit + limit < total
        }
      }
    })

  } catch (error) {
    console.error('获取交易历史失败:', error)
    return NextResponse.json(
      { error: '获取交易历史失败' },
      { status: 500 }
    )
  }
}

// 辅助函数：获取交易显示信息
function getTransactionDisplayInfo(transaction: any) {
  const typeLabels: { [key: string]: string } = {
    'DEPOSIT': '充值',
    'WITHDRAW': '提现',
    'FREEZE': '冻结',
    'UNFREEZE': '解冻',
    'EARN': '收入',
    'SPEND': '支出',
    'TRANSFER': '转账',
    'FEE': '手续费',
    'REFUND': '退款',
    'REWARD': '奖励'
  }

  const relatedTypeLabels: { [key: string]: string } = {
    'ORDER': '订单',
    'ESCROW': '托管',
    'WITHDRAWAL': '提现',
    'DEPOSIT': '充值',
    'REWARD': '奖励',
    'MEDIATION': '中间人服务'
  }

  let icon = '💰'
  let color = 'blue'
  let amountPrefix = ''

  switch (transaction.type) {
    case GuaranteeTransactionType.DEPOSIT:
      icon = '⬆️'
      color = 'green'
      amountPrefix = '+'
      break
    case GuaranteeTransactionType.WITHDRAW:
      icon = '⬇️'
      color = 'red'
      amountPrefix = '-'
      break
    case GuaranteeTransactionType.FREEZE:
      icon = '🔒'
      color = 'orange'
      break
    case GuaranteeTransactionType.UNFREEZE:
      icon = '🔓'
      color = 'blue'
      break
    case GuaranteeTransactionType.EARN:
      icon = '💵'
      color = 'green'
      amountPrefix = '+'
      break
    case GuaranteeTransactionType.SPEND:
      icon = '💸'
      color = 'red'
      amountPrefix = '-'
      break
    case GuaranteeTransactionType.REWARD:
      icon = '🎁'
      color = 'purple'
      amountPrefix = '+'
      break
  }

  return {
    typeLabel: typeLabels[transaction.type] || transaction.type,
    relatedTypeLabel: transaction.relatedType 
      ? relatedTypeLabels[transaction.relatedType] || transaction.relatedType
      : null,
    icon,
    color,
    amountPrefix,
    formattedAmount: `${amountPrefix}${Math.abs(transaction.amount).toFixed(2)} USDT`,
    isPositive: transaction.amount > 0 || transaction.type === GuaranteeTransactionType.UNFREEZE,
    balanceChange: transaction.balanceAfter - transaction.balanceBefore,
    frozenChange: transaction.frozenAfter - transaction.frozenBefore
  }
}
