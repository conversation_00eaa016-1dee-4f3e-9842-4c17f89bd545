import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { GuaranteeSystem } from '@/lib/guarantee-system'

// 获取用户担保金余额信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 获取余额信息
    const balance = await GuaranteeSystem.getUserBalance(session.user.id)
    
    if (!balance) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取可提现金额
    const maxWithdrawable = await GuaranteeSystem.getMaxWithdrawableAmount(session.user.id)

    // 获取担保金等级信息
    const currentLevel = await GuaranteeSystem.getUserGuaranteeLevel(session.user.id)

    return NextResponse.json({
      success: true,
      data: {
        ...balance,
        maxWithdrawable,
        currentLevel,
        // 计算一些有用的衍生数据
        totalBalance: balance.depositBalance,
        utilizationRate: balance.depositBalance > 0 
          ? (balance.frozenBalance / balance.depositBalance * 100).toFixed(2)
          : '0.00',
        netEarnings: balance.totalEarnings - balance.totalWithdrawals
      }
    })

  } catch (error) {
    console.error('获取担保金余额失败:', error)
    return NextResponse.json(
      { error: '获取余额信息失败' },
      { status: 500 }
    )
  }
}

// 充值担保金
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, description, txHash } = body

    // 验证参数
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '充值金额必须大于0' },
        { status: 400 }
      )
    }

    if (!description) {
      return NextResponse.json(
        { error: '请提供充值说明' },
        { status: 400 }
      )
    }

    // 执行充值操作
    const result = await GuaranteeSystem.deposit(
      session.user.id,
      amount,
      description,
      txHash
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || '充值失败' },
        { status: 400 }
      )
    }

    // 更新用户担保金等级
    await GuaranteeSystem.updateUserGuaranteeLevel(session.user.id)

    return NextResponse.json({
      success: true,
      message: '充值成功',
      data: {
        transactionId: result.transactionId,
        balanceBefore: result.balanceBefore,
        balanceAfter: result.balanceAfter,
        amount
      }
    })

  } catch (error) {
    console.error('担保金充值失败:', error)
    return NextResponse.json(
      { error: '充值操作失败' },
      { status: 500 }
    )
  }
}
