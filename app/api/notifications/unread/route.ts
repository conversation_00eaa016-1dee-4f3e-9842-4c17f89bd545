import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取未读通知数量
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const unreadCount = await prisma.notification.count({
      where: {
        userId: session.user.id,
        isRead: false,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      }
    })

    // 获取最新的几条未读通知
    const recentNotifications = await prisma.notification.findMany({
      where: {
        userId: session.user.id,
        isRead: false,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ],
      take: 5,
      select: {
        id: true,
        type: true,
        title: true,
        message: true,
        priority: true,
        createdAt: true,
        relatedId: true,
        relatedType: true
      }
    })

    return NextResponse.json({
      success: true,
      unreadCount,
      recentNotifications
    })

  } catch (error) {
    console.error('获取未读通知失败:', error)
    return NextResponse.json(
      { error: '获取未读通知失败' },
      { status: 500 }
    )
  }
}
