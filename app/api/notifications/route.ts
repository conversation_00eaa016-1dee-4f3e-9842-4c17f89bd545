import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户通知列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const isRead = searchParams.get('read')
    const priority = searchParams.get('priority')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {
      userId: session.user.id,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    }

    if (type) {
      where.type = type
    }

    if (isRead !== null && isRead !== undefined) {
      where.isRead = isRead === 'true'
    }

    if (priority) {
      where.priority = priority
    }

    // 获取通知列表
    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.notification.count({ where })
    ])

    // 获取未读通知数量
    const unreadCount = await prisma.notification.count({
      where: {
        userId: session.user.id,
        isRead: false,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      }
    })

    return NextResponse.json({
      success: true,
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    })

  } catch (error) {
    console.error('获取通知列表失败:', error)
    return NextResponse.json(
      { error: '获取通知失败' },
      { status: 500 }
    )
  }
}

// 创建通知
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      userId,
      type,
      title,
      message,
      data,
      relatedId,
      relatedType,
      priority = 'NORMAL',
      expiresAt
    } = body

    // 验证必要字段
    if (!userId || !type || !title || !message) {
      return NextResponse.json(
        { error: '缺少必要字段' },
        { status: 400 }
      )
    }

    // 验证通知类型
    const validTypes = ['SYSTEM', 'ORDER', 'PAYMENT', 'DISPUTE', 'ANNOUNCEMENT']
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: '无效的通知类型' },
        { status: 400 }
      )
    }

    // 验证优先级
    const validPriorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT']
    if (!validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: '无效的优先级' },
        { status: 400 }
      )
    }

    // 检查目标用户是否存在
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!targetUser) {
      return NextResponse.json(
        { error: '目标用户不存在' },
        { status: 404 }
      )
    }

    // 创建通知
    const notification = await prisma.notification.create({
      data: {
        userId,
        type,
        title,
        message,
        data,
        relatedId,
        relatedType,
        priority,
        expiresAt: expiresAt ? new Date(expiresAt) : null
      }
    })

    return NextResponse.json({
      success: true,
      message: '通知已创建',
      notification
    })

  } catch (error) {
    console.error('创建通知失败:', error)
    return NextResponse.json(
      { error: '创建通知失败' },
      { status: 500 }
    )
  }
}

// 批量标记通知为已读
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { notificationIds, markAll = false } = body

    if (markAll) {
      // 标记所有未读通知为已读
      const result = await prisma.notification.updateMany({
        where: {
          userId: session.user.id,
          isRead: false
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        message: `已标记 ${result.count} 条通知为已读`
      })
    } else if (notificationIds && Array.isArray(notificationIds)) {
      // 标记指定通知为已读
      const result = await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: session.user.id
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        message: `已标记 ${result.count} 条通知为已读`
      })
    } else {
      return NextResponse.json(
        { error: '缺少通知ID或标记全部参数' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('标记通知已读失败:', error)
    return NextResponse.json(
      { error: '标记通知已读失败' },
      { status: 500 }
    )
  }
}

// 删除通知
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const notificationId = searchParams.get('id')

    if (!notificationId) {
      return NextResponse.json(
        { error: '缺少通知ID' },
        { status: 400 }
      )
    }

    // 删除通知（只能删除自己的通知）
    const result = await prisma.notification.deleteMany({
      where: {
        id: notificationId,
        userId: session.user.id
      }
    })

    if (result.count === 0) {
      return NextResponse.json(
        { error: '通知不存在或无权限删除' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '通知已删除'
    })

  } catch (error) {
    console.error('删除通知失败:', error)
    return NextResponse.json(
      { error: '删除通知失败' },
      { status: 500 }
    )
  }
}
