import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { assessTransactionRisk, logRiskAssessment } from '@/lib/security-controls'

// 执行风险评估
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      amount, 
      walletAddress, 
      mediatorId, 
      transactionType = 'ESCROW_TRANSACTION' 
    } = body

    // 验证必要参数
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '无效的交易金额' },
        { status: 400 }
      )
    }

    // 执行风险评估
    const assessment = await assessTransactionRisk(
      session.user.id,
      parseFloat(amount),
      walletAddress,
      mediatorId
    )

    // 记录风险评估日志
    await logRiskAssessment(
      session.user.id,
      assessment,
      transactionType,
      parseFloat(amount),
      {
        walletAddress,
        mediatorId,
        userAgent: request.headers.get('user-agent'),
        ipAddress: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown'
      }
    )

    // 根据风险等级决定响应
    const response = {
      success: true,
      data: {
        riskLevel: assessment.riskLevel,
        riskScore: assessment.riskScore,
        requiresApproval: assessment.requiresApproval,
        requiresKYC: assessment.requiresKYC,
        maxAllowedAmount: assessment.maxAllowedAmount,
        recommendations: assessment.recommendations,
        canProceed: assessment.riskLevel !== 'CRITICAL' || assessment.requiresApproval === false
      }
    }

    // 如果风险等级为 CRITICAL，返回特殊状态码
    if (assessment.riskLevel === 'CRITICAL' && assessment.requiresApproval) {
      return NextResponse.json(
        {
          ...response,
          message: '交易风险较高，需要人工审核'
        },
        { status: 202 } // Accepted but requires approval
      )
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('风险评估失败:', error)
    return NextResponse.json(
      { error: '风险评估失败' },
      { status: 500 }
    )
  }
}

// 获取用户风险历史
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // 获取用户风险评估历史
    const { prisma } = await import('@/lib/prisma')
    
    const [assessments, total] = await Promise.all([
      prisma.riskAssessmentLog.findMany({
        where: { userId: session.user.id },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        select: {
          id: true,
          riskLevel: true,
          riskScore: true,
          requiresApproval: true,
          requiresKYC: true,
          transactionType: true,
          amount: true,
          createdAt: true,
          riskFactors: true,
          recommendations: true
        }
      }),
      prisma.riskAssessmentLog.count({
        where: { userId: session.user.id }
      })
    ])

    // 计算风险统计
    const riskStats = await prisma.riskAssessmentLog.groupBy({
      by: ['riskLevel'],
      where: { userId: session.user.id },
      _count: { _all: true }
    })

    const stats = {
      total,
      riskDistribution: riskStats.reduce((acc, stat) => {
        acc[stat.riskLevel] = stat._count._all
        return acc
      }, {} as Record<string, number>)
    }

    return NextResponse.json({
      success: true,
      data: {
        assessments,
        stats,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    })

  } catch (error) {
    console.error('获取风险历史失败:', error)
    return NextResponse.json(
      { error: '获取风险历史失败' },
      { status: 500 }
    )
  }
}
