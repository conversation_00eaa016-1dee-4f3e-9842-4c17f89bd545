import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { updateProductVariant, deleteProductVariant, getVariantById } from '@/lib/variants'

// 获取单个变体详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: variantId } = await params

    const result = await getVariantById(variantId)
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 404 }
      )
    }

    return NextResponse.json(result.variant)
  } catch (error) {
    console.error('获取变体详情失败:', error)
    return NextResponse.json(
      { error: '获取变体详情失败' },
      { status: 500 }
    )
  }
}

// 更新变体
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: variantId } = await params
    const body = await request.json()

    // 验证变体所有权
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const variant = await prisma.productVariant.findUnique({
      where: { id: variantId },
      include: {
        product: {
          select: {
            sellerId: true
          }
        }
      }
    })

    if (!variant || variant.product.sellerId !== user.id) {
      return NextResponse.json(
        { error: '变体不存在或无权限' },
        { status: 404 }
      )
    }

    // 验证请求数据
    const { sku, price, stock, status, isDefault, attributes } = body

    if (price !== undefined && (typeof price !== 'number' || price <= 0)) {
      return NextResponse.json(
        { error: '价格必须为正数' },
        { status: 400 }
      )
    }

    if (stock !== undefined && (typeof stock !== 'number' || stock < 0)) {
      return NextResponse.json(
        { error: '库存必须为非负数' },
        { status: 400 }
      )
    }

    if (attributes !== undefined) {
      if (!Array.isArray(attributes) || attributes.length === 0) {
        return NextResponse.json(
          { error: '变体属性不能为空' },
          { status: 400 }
        )
      }

      // 验证属性格式
      for (const attr of attributes) {
        if (!attr.name || !attr.value) {
          return NextResponse.json(
            { error: '属性名称和值不能为空' },
            { status: 400 }
          )
        }
      }
    }

    // 检查SKU是否重复
    if (sku && sku !== variant.sku) {
      const existingVariant = await prisma.productVariant.findFirst({
        where: {
          productId: variant.productId,
          sku,
          id: { not: variantId }
        }
      })

      if (existingVariant) {
        return NextResponse.json(
          { error: 'SKU已存在' },
          { status: 400 }
        )
      }
    }

    // 更新变体
    const result = await updateProductVariant(variantId, {
      sku,
      price,
      stock,
      status,
      isDefault,
      attributes
    })

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    // 获取更新后的变体信息
    const updatedVariant = await prisma.productVariant.findUnique({
      where: { id: variantId },
      include: {
        attributes: true
      }
    })

    return NextResponse.json(updatedVariant)
  } catch (error) {
    console.error('更新变体失败:', error)
    return NextResponse.json(
      { error: '更新变体失败' },
      { status: 500 }
    )
  }
}

// 删除变体
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: variantId } = await params

    // 验证变体所有权
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const variant = await prisma.productVariant.findUnique({
      where: { id: variantId },
      include: {
        product: {
          select: {
            sellerId: true
          }
        }
      }
    })

    if (!variant || variant.product.sellerId !== user.id) {
      return NextResponse.json(
        { error: '变体不存在或无权限' },
        { status: 404 }
      )
    }

    // 删除变体
    const result = await deleteProductVariant(variantId)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({ message: '变体删除成功' })
  } catch (error) {
    console.error('删除变体失败:', error)
    return NextResponse.json(
      { error: '删除变体失败' },
      { status: 500 }
    )
  }
}
