/**
 * 提现费用计算器API
 * 支持阶梯式费率计算和优化建议
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  getWithdrawalFeeCalculator,
  calculateComprehensiveWithdrawalFee,
  getOptimalWithdrawalSuggestion,
  WITHDRAWAL_FEE_TIERS 
} from '@/lib/withdrawal-fee-reform'

// 获取提现费用计算器
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const amount = parseFloat(searchParams.get('amount') || '0')

    switch (action) {
      case 'calculator':
        const calculator = await getWithdrawalFeeCalculator(session.user.id)
        return NextResponse.json(calculator)

      case 'estimate':
        if (!amount || amount <= 0) {
          return NextResponse.json(
            { error: '请提供有效的提现金额' },
            { status: 400 }
          )
        }
        
        const [normalFee, batchFee] = await Promise.all([
          calculateComprehensiveWithdrawalFee(session.user.id, amount, false),
          calculateComprehensiveWithdrawalFee(session.user.id, amount, true)
        ])

        return NextResponse.json({
          amount,
          normalWithdrawal: normalFee,
          batchWithdrawal: batchFee,
          recommendation: batchFee.finalFee < normalFee.finalFee 
            ? '建议选择批量提现时间以节省手续费'
            : '当前费率已经很优惠了'
        })

      case 'suggestion':
        if (!amount || amount <= 0) {
          return NextResponse.json(
            { error: '请提供有效的余额金额' },
            { status: 400 }
          )
        }

        const suggestion = await getOptimalWithdrawalSuggestion(session.user.id, amount)
        return NextResponse.json(suggestion)

      case 'tiers':
        return NextResponse.json({
          tiers: WITHDRAWAL_FEE_TIERS,
          description: '阶梯式提现手续费结构'
        })

      default:
        // 默认返回完整计算器信息
        const [calculatorData, tiersData] = await Promise.all([
          getWithdrawalFeeCalculator(session.user.id),
          Promise.resolve({ tiers: WITHDRAWAL_FEE_TIERS })
        ])

        return NextResponse.json({
          calculator: calculatorData,
          tiers: tiersData.tiers,
          tips: [
            '💡 提现金额越大，费率越优惠',
            '⏰ 选择批量提现时间可享受额外折扣',
            '🏆 提升信用等级可获得更多手续费折扣',
            '🎯 大额提现（2000+ USDT）完全免手续费'
          ]
        })
    }

  } catch (error) {
    console.error('获取提现计算器数据失败:', error)
    return NextResponse.json(
      { error: '获取提现计算器数据失败' },
      { status: 500 }
    )
  }
}

// 模拟提现费用计算
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { amounts, isBatch = false } = await request.json()

    if (!Array.isArray(amounts) || amounts.length === 0) {
      return NextResponse.json(
        { error: '请提供有效的金额数组' },
        { status: 400 }
      )
    }

    // 批量计算多个金额的手续费
    const calculations = await Promise.all(
      amounts.map(async (amount: number) => {
        if (amount <= 0) {
          return {
            amount,
            error: '金额必须大于0'
          }
        }

        try {
          const feeCalc = await calculateComprehensiveWithdrawalFee(
            session.user.id, 
            amount, 
            isBatch
          )

          return {
            amount,
            ...feeCalc,
            actualReceived: amount - feeCalc.finalFee,
            effectiveRate: amount > 0 ? (feeCalc.finalFee / amount) * 100 : 0
          }
        } catch (error) {
          return {
            amount,
            error: error instanceof Error ? error.message : '计算失败'
          }
        }
      })
    )

    // 计算总体统计
    const validCalculations = calculations.filter(c => !('error' in c)) as Array<{
      amount: number;
      actualReceived: number;
      effectiveRate: number;
      originalFee: number;
      creditDiscount: number;
      batchDiscount: number;
      finalFee: number;
      savings: number;
      savingsRate: number;
      tier: string;
      breakdown: {
        baseFee: number;
        creditDiscountAmount: number;
        batchDiscountAmount: number;
      };
    }>
    const totalAmount = validCalculations.reduce((sum, c) => sum + c.amount, 0)
    const totalFee = validCalculations.reduce((sum, c) => sum + (c.finalFee || 0), 0)
    const totalSavings = validCalculations.reduce((sum, c) => sum + (c.savings || 0), 0)

    return NextResponse.json({
      calculations,
      summary: {
        totalAmount,
        totalFee,
        totalSavings,
        avgSavingsRate: validCalculations.length > 0 
          ? validCalculations.reduce((sum, c) => sum + (c.savingsRate || 0), 0) / validCalculations.length 
          : 0,
        recommendation: totalSavings > 0 
          ? `通过智能费率优化，总共可节省 ${totalSavings.toFixed(4)} USDT`
          : '当前费率已经很优惠了'
      }
    })

  } catch (error) {
    console.error('批量计算提现费用失败:', error)
    return NextResponse.json(
      { error: '批量计算提现费用失败' },
      { status: 500 }
    )
  }
}
