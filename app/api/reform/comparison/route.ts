/**
 * 改革前后对比API
 * 展示手续费改革的效果和优势
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getReformComparison, getReformEffectStatistics } from '@/lib/adapters/reformed-purchase-adapter'
import { getReformEffectStats } from '@/lib/withdrawal-fee-reform'
import { prisma } from '@/lib/prisma'

// 获取改革对比数据
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const amount = parseFloat(searchParams.get('amount') || '100')
    const days = parseInt(searchParams.get('days') || '30')

    switch (action) {
      case 'fee-comparison':
        if (amount <= 0) {
          return NextResponse.json(
            { error: '请提供有效的金额' },
            { status: 400 }
          )
        }

        const comparison = await getReformComparison(session.user.id, amount)
        return NextResponse.json(comparison)

      case 'user-stats':
        const userStats = await getUserReformStats(session.user.id, days)
        return NextResponse.json(userStats)

      case 'platform-stats':
        // 检查管理员权限
        const user = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: { role: true }
        })

        if (user?.role !== 'ADMIN') {
          return NextResponse.json(
            { error: '权限不足' },
            { status: 403 }
          )
        }

        const [purchaseStats, withdrawalStats] = await Promise.all([
          getReformEffectStatistics(days),
          getReformEffectStats(days)
        ])

        return NextResponse.json({
          purchase: purchaseStats,
          withdrawal: withdrawalStats,
          summary: {
            totalFeesSaved: purchaseStats.totalFeesSaved + withdrawalStats.totalFeesSaved,
            userSatisfactionImprovement: purchaseStats.userSatisfactionImprovement,
            operationalEfficiency: calculateOperationalEfficiency(purchaseStats, withdrawalStats)
          }
        })

      default:
        // 默认返回完整对比数据
        const [feeComparison, userReformStats] = await Promise.all([
          getReformComparison(session.user.id, amount),
          getUserReformStats(session.user.id, days)
        ])

        return NextResponse.json({
          feeComparison,
          userStats: userReformStats,
          benefits: [
            '🎉 交易零手续费，提升交易体验',
            '💰 阶梯式提现费率，大额免费',
            '⭐ 信誉可视化，增强信任度',
            '🚀 批量提现优化，降低成本',
            '🏆 等级权益系统，激励长期使用'
          ]
        })
    }

  } catch (error) {
    console.error('获取改革对比数据失败:', error)
    return NextResponse.json(
      { error: '获取改革对比数据失败' },
      { status: 500 }
    )
  }
}

/**
 * 获取用户改革统计
 */
async function getUserReformStats(userId: string, days: number) {
  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    // 获取用户在改革期间的订单
    const [allOrders, reformedOrders, withdrawals] = await Promise.all([
      prisma.order.findMany({
        where: {
          OR: [
            { buyerId: userId },
            { sellerId: userId }
          ],
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      }),
      prisma.order.findMany({
        where: {
          OR: [
            { buyerId: userId },
            { sellerId: userId }
          ],
          createdAt: { gte: startDate },
          status: 'COMPLETED',
          platformFee: 0 // 改革后的订单没有平台手续费
        }
      }),
      prisma.withdrawal.findMany({
        where: {
          userId,
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      })
    ])

    // 计算节省的交易手续费
    const transactionFeesSaved = reformedOrders.reduce((sum, order) => {
      const originalFee = order.totalAmount * 0.05 // 假设原来5%手续费
      return sum + originalFee
    }, 0)

    // 计算提现费用优化
    const withdrawalFeesSaved = withdrawals.reduce((sum, withdrawal) => {
      const originalFee = withdrawal.amount * 0.01 // 假设原来1%提现费
      const actualFee = withdrawal.withdrawalFee
      return sum + (originalFee - actualFee)
    }, 0)

    const totalFeesSaved = transactionFeesSaved + withdrawalFeesSaved
    const totalTransactionVolume = allOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    const reformedOrdersCount = reformedOrders.length
    const reformAdoptionRate = allOrders.length > 0 ? (reformedOrdersCount / allOrders.length) * 100 : 0

    return {
      period: {
        days,
        startDate
      },
      transactions: {
        total: allOrders.length,
        reformed: reformedOrdersCount,
        adoptionRate: reformAdoptionRate,
        totalVolume: totalTransactionVolume
      },
      savings: {
        transactionFees: transactionFeesSaved,
        withdrawalFees: withdrawalFeesSaved,
        total: totalFeesSaved,
        savingsRate: totalTransactionVolume > 0 ? (totalFeesSaved / totalTransactionVolume) * 100 : 0
      },
      withdrawals: {
        count: withdrawals.length,
        totalAmount: withdrawals.reduce((sum, w) => sum + w.amount, 0),
        avgFeeRate: withdrawals.length > 0 
          ? withdrawals.reduce((sum, w) => sum + (w.withdrawalFee / w.amount), 0) / withdrawals.length * 100
          : 0
      }
    }

  } catch (error) {
    console.error('获取用户改革统计失败:', error)
    throw error
  }
}

/**
 * 计算运营效率提升
 */
function calculateOperationalEfficiency(purchaseStats: any, withdrawalStats: any) {
  // 基于交易无手续费和批量提现的效率提升
  const transactionEfficiency = purchaseStats.reformedOrders / Math.max(purchaseStats.totalOrders, 1) * 100
  const withdrawalEfficiency = withdrawalStats.userSatisfaction.freeWithdrawals / Math.max(withdrawalStats.totalWithdrawals, 1) * 100
  
  return {
    transactionProcessing: transactionEfficiency,
    withdrawalProcessing: withdrawalEfficiency,
    overall: (transactionEfficiency + withdrawalEfficiency) / 2,
    description: '基于无手续费交易和优化提现的运营效率提升'
  }
}

// 提交改革反馈
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { feedback, rating, suggestions } = await request.json()

    if (!feedback || !rating) {
      return NextResponse.json(
        { error: '请提供反馈内容和评分' },
        { status: 400 }
      )
    }

    // 保存用户反馈（可以创建一个专门的反馈表）
    const feedbackRecord = await prisma.helpRequest.create({
      data: {
        userId: session.user.id,
        subject: '手续费改革反馈',
        description: feedback,
        status: 'OPEN',
        metadata: {
          type: 'REFORM_FEEDBACK',
          rating,
          suggestions,
          submittedAt: new Date()
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '感谢您的反馈！我们会认真考虑您的建议',
      feedbackId: feedbackRecord.id
    })

  } catch (error) {
    console.error('提交改革反馈失败:', error)
    return NextResponse.json(
      { error: '提交改革反馈失败' },
      { status: 500 }
    )
  }
}
