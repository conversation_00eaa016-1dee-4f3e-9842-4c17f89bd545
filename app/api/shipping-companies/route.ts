import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 默认快递公司列表
const DEFAULT_SHIPPING_COMPANIES = [
  { code: 'SF', name: '顺丰速运', website: 'https://www.sf-express.com', trackingUrl: 'https://www.sf-express.com/chn/sc/dynamic_function/waybill/#search/bill-number/{trackingNumber}' },
  { code: 'YTO', name: '圆通速递', website: 'https://www.yto.net.cn', trackingUrl: 'https://www.yto.net.cn/index/query/gotoQueryIndex.html?billcode={trackingNumber}' },
  { code: 'ZTO', name: '中通快递', website: 'https://www.zto.com', trackingUrl: 'https://www.zto.com/index.php?m=Index&a=query&nu={trackingNumber}' },
  { code: 'STO', name: '申通快递', website: 'https://www.sto.cn', trackingUrl: 'https://www.sto.cn/query?billcode={trackingNumber}' },
  { code: 'YD', name: '韵达速递', website: 'https://www.yunda56.com', trackingUrl: 'https://www.yunda56.com/query/{trackingNumber}' },
  { code: 'HTKY', name: '百世汇通', website: 'https://www.800best.com', trackingUrl: 'https://www.800best.com/query/{trackingNumber}' },
  { code: 'JD', name: '京东物流', website: 'https://www.jdl.cn', trackingUrl: 'https://www.jdl.cn/express/query?waybillCode={trackingNumber}' },
  { code: 'EMS', name: '中国邮政EMS', website: 'https://www.ems.com.cn', trackingUrl: 'https://www.ems.com.cn/queryList?mailNum={trackingNumber}' },
  { code: 'DBKD', name: '德邦快递', website: 'https://www.deppon.com', trackingUrl: 'https://www.deppon.com/gw/dop/newOrderTracking.action?mailNo={trackingNumber}' },
  { code: 'OTHER', name: '其他快递公司', website: '', trackingUrl: '' }
]

// 获取快递公司列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 获取用户自定义的快递公司
    const customCompanies = await prisma.shippingCompany.findMany({
      where: {
        OR: [
          { userId: session.user.id },
          { isGlobal: true }
        ]
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 合并默认快递公司和自定义快递公司
    const allCompanies = [
      ...DEFAULT_SHIPPING_COMPANIES,
      ...customCompanies.map(company => ({
        code: company.code,
        name: company.name,
        website: company.website || '',
        trackingUrl: company.trackingUrl || '',
        isCustom: true,
        id: company.id
      }))
    ]

    return NextResponse.json({ companies: allCompanies })

  } catch (error) {
    console.error('获取快递公司列表失败:', error)
    return NextResponse.json(
      { error: '获取快递公司列表失败' },
      { status: 500 }
    )
  }
}

// 添加自定义快递公司
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { code, name, website, trackingUrl } = body

    if (!code || !name) {
      return NextResponse.json(
        { error: '快递公司代码和名称为必填项' },
        { status: 400 }
      )
    }

    // 检查代码是否已存在
    const existingCompany = await prisma.shippingCompany.findFirst({
      where: {
        code: code.toUpperCase(),
        OR: [
          { userId: session.user.id },
          { isGlobal: true }
        ]
      }
    })

    if (existingCompany) {
      return NextResponse.json(
        { error: '快递公司代码已存在' },
        { status: 400 }
      )
    }

    // 检查默认快递公司中是否已存在
    const defaultExists = DEFAULT_SHIPPING_COMPANIES.some(
      company => company.code === code.toUpperCase()
    )

    if (defaultExists) {
      return NextResponse.json(
        { error: '该快递公司已在默认列表中' },
        { status: 400 }
      )
    }

    // 创建自定义快递公司
    const newCompany = await prisma.shippingCompany.create({
      data: {
        code: code.toUpperCase(),
        name: name.trim(),
        website: website?.trim() || null,
        trackingUrl: trackingUrl?.trim() || null,
        userId: session.user.id,
        isGlobal: false
      }
    })

    return NextResponse.json({
      success: true,
      company: {
        code: newCompany.code,
        name: newCompany.name,
        website: newCompany.website || '',
        trackingUrl: newCompany.trackingUrl || '',
        isCustom: true,
        id: newCompany.id
      }
    })

  } catch (error) {
    console.error('添加快递公司失败:', error)
    return NextResponse.json(
      { error: '添加快递公司失败' },
      { status: 500 }
    )
  }
}

// 删除自定义快递公司
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('id')

    if (!companyId) {
      return NextResponse.json(
        { error: '请提供快递公司ID' },
        { status: 400 }
      )
    }

    // 检查快递公司是否存在且属于当前用户
    const company = await prisma.shippingCompany.findFirst({
      where: {
        id: companyId,
        userId: session.user.id
      }
    })

    if (!company) {
      return NextResponse.json(
        { error: '快递公司不存在或无权限删除' },
        { status: 404 }
      )
    }

    // 检查是否有订单正在使用此快递公司
    const ordersUsingCompany = await prisma.order.count({
      where: {
        shippingCompany: company.name,
        status: {
          in: ['SHIPPED', 'DELIVERED']
        }
      }
    })

    if (ordersUsingCompany > 0) {
      return NextResponse.json(
        { error: '该快递公司正在被订单使用，无法删除' },
        { status: 400 }
      )
    }

    // 删除快递公司
    await prisma.shippingCompany.delete({
      where: { id: companyId }
    })

    return NextResponse.json({
      success: true,
      message: '快递公司删除成功'
    })

  } catch (error) {
    console.error('删除快递公司失败:', error)
    return NextResponse.json(
      { error: '删除快递公司失败' },
      { status: 500 }
    )
  }
}
