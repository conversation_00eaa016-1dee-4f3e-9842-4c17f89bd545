import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { v4 as uuidv4 } from 'uuid'

// 定义上传文件的类型
interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadedAt: string
}

// 上传争议证据文件
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: '没有选择文件' },
        { status: 400 }
      )
    }

    // 检查文件数量限制
    if (files.length > 10) {
      return NextResponse.json(
        { success: false, error: '最多只能上传10个文件' },
        { status: 400 }
      )
    }

    const uploadedFiles: UploadedFile[] = []
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'disputes')

    // 确保上传目录存在
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // 目录已存在，忽略错误
    }

    for (const file of files) {
      // 检查文件大小（10MB限制）
      if (file.size > 10 * 1024 * 1024) {
        return NextResponse.json(
          { success: false, error: `文件 ${file.name} 超过10MB限制` },
          { status: 400 }
        )
      }

      // 检查文件类型
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ]

      if (!allowedTypes.includes(file.type)) {
        return NextResponse.json(
          { success: false, error: `不支持的文件类型: ${file.type}` },
          { status: 400 }
        )
      }

      // 生成唯一文件名
      const fileId = uuidv4()
      const fileExtension = file.name.split('.').pop() || ''
      const fileName = `${fileId}.${fileExtension}`
      const filePath = join(uploadDir, fileName)

      // 保存文件
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(filePath, buffer)

      // 构建文件URL
      const fileUrl = `/uploads/disputes/${fileName}`

      uploadedFiles.push({
        id: fileId,
        name: file.name,
        size: file.size,
        type: file.type,
        url: fileUrl,
        uploadedAt: new Date().toISOString()
      })
    }

    return NextResponse.json({
      success: true,
      message: `成功上传 ${uploadedFiles.length} 个文件`,
      data: {
        files: uploadedFiles
      }
    })

  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { success: false, error: '文件上传失败' },
      { status: 500 }
    )
  }
}

// 删除上传的文件
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { fileId } = await request.json()

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: '缺少文件ID' },
        { status: 400 }
      )
    }

    // 这里可以添加文件删除逻辑
    // 由于我们使用的是简单的文件系统存储，可以根据需要实现文件删除
    // 在生产环境中，建议使用云存储服务

    return NextResponse.json({
      success: true,
      message: '文件删除成功'
    })

  } catch (error) {
    console.error('文件删除失败:', error)
    return NextResponse.json(
      { success: false, error: '文件删除失败' },
      { status: 500 }
    )
  }
}
