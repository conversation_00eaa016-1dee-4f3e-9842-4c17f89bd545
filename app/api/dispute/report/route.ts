import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 提交争议举报
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { orderId, disputeType, description, evidence } = await request.json()

    if (!orderId || !disputeType || !description) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      )
    }

    if (description.length < 50) {
      return NextResponse.json(
        { success: false, error: '问题描述至少需要50个字符' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: {
          select: { id: true, name: true, email: true }
        },
        seller: {
          select: { id: true, name: true, email: true }
        },
        mediator: {
          select: { id: true, name: true, email: true }
        },
        product: {
          select: { title: true }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { success: false, error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否有权限举报此订单
    const hasAccess = [order.buyerId, order.sellerId].includes(session.user.id)

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: '无权限举报此订单' },
        { status: 403 }
      )
    }

    // 检查是否已有未处理的争议
    const existingDispute = await prisma.disputeReport.findFirst({
      where: {
        orderId,
        status: {
          in: ['PENDING', 'INVESTIGATING']
        }
      }
    })

    if (existingDispute) {
      return NextResponse.json(
        { success: false, error: '该订单已有未处理的争议举报' },
        { status: 400 }
      )
    }

    // 使用事务创建争议举报
    const result = await prisma.$transaction(async (tx) => {
      // 创建争议举报
      const dispute = await tx.disputeReport.create({
        data: {
          orderId,
          reporterId: session.user.id,
          disputeType,
          description,
          evidence: evidence || [],
          status: 'PENDING',
          priority: 'MEDIUM',
          reportedAt: new Date()
        }
      })

      // 更新订单争议状态
      await tx.order.update({
        where: { id: orderId },
        data: {
          disputeStatus: 'REPORTED',
          disputeReason: disputeType,
          disputeReportedAt: new Date()
        }
      })

      // 创建订单日志
      await tx.orderLog.create({
        data: {
          orderId,
          operatorId: session.user.id,
          action: 'DISPUTE_REPORTED',
          description: `用户举报争议: ${disputeType}`,
          newValue: JSON.stringify({
            disputeId: dispute.id,
            disputeType,
            description: description.substring(0, 100) + '...'
          }),
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
          userAgent: request.headers.get('user-agent')
        }
      })

      // TODO: 实现通知功能
      // 当前系统暂未实现通知模型，可以在后续版本中添加
      console.log(`争议举报已创建: 订单 ${order.orderNumber}, 举报人: ${session.user.id}`)

      return dispute
    })

    return NextResponse.json({
      success: true,
      message: '争议举报已提交，我们会在24小时内处理',
      data: {
        disputeId: result.id,
        status: result.status
      }
    })

  } catch (error) {
    console.error('提交争议举报失败:', error)
    return NextResponse.json(
      { success: false, error: '提交争议举报失败' },
      { status: 500 }
    )
  }
}

// 获取用户的争议举报历史
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')

    // 构建查询条件
    const where: any = {
      reporterId: session.user.id
    }

    if (status) {
      where.status = status
    }

    const disputes = await prisma.disputeReport.findMany({
      where,
      include: {
        order: {
          include: {
            product: {
              select: {
                title: true,
                images: true
              }
            },
            buyer: {
              select: {
                id: true,
                name: true
              }
            },
            seller: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        reporter: {
          select: {
            id: true,
            name: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.disputeReport.count({ where })

    return NextResponse.json({
      success: true,
      data: {
        disputes,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取争议历史失败:', error)
    return NextResponse.json(
      { success: false, error: '获取争议历史失败' },
      { status: 500 }
    )
  }
}
