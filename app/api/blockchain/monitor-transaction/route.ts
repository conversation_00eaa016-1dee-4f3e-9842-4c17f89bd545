import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { 
  getTransactionDetails, 
  verifyUSDTTransfer, 
  waitForTransaction,
  isValidAddress 
} from '@/lib/blockchain'

// 提交交易哈希进行监控
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      txHash, 
      escrowOrderId, 
      expectedFrom, 
      expectedTo, 
      expectedAmount,
      transactionType = 'ESCROW_FUNDING'
    } = body

    // 验证必要参数
    if (!txHash || !escrowOrderId || !expectedFrom || !expectedTo || !expectedAmount) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 验证交易哈希格式
    if (!txHash.startsWith('0x') || txHash.length !== 66) {
      return NextResponse.json(
        { error: '交易哈希格式不正确' },
        { status: 400 }
      )
    }

    // 验证钱包地址格式
    if (!isValidAddress(expectedFrom) || !isValidAddress(expectedTo)) {
      return NextResponse.json(
        { error: '钱包地址格式不正确' },
        { status: 400 }
      )
    }

    // 验证托管订单存在且用户有权限
    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowOrderId },
      include: {
        order: true,
        mediator: true,
        buyer: true,
        seller: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    const hasPermission = [
      escrowOrder.mediatorId,
      escrowOrder.buyerId,
      escrowOrder.sellerId
    ].includes(session.user.id)

    if (!hasPermission) {
      return NextResponse.json(
        { error: '无权限访问此托管订单' },
        { status: 403 }
      )
    }

    // 检查交易是否已存在
    const existingTx = await prisma.blockchainTransaction.findFirst({
      where: {
        txHash: txHash.toLowerCase(),
        relatedEscrowId: escrowOrderId
      }
    })

    if (existingTx) {
      return NextResponse.json(
        { error: '交易已存在监控列表中' },
        { status: 400 }
      )
    }

    // 获取交易详情
    const { transaction, receipt } = await getTransactionDetails(txHash)

    if (!transaction) {
      return NextResponse.json(
        { error: '交易不存在或尚未上链' },
        { status: 400 }
      )
    }

    // 创建区块链交易记录
    const blockchainTx = await prisma.blockchainTransaction.create({
      data: {
        txHash: txHash.toLowerCase(),
        network: 'BNB_CHAIN',
        type: transactionType,
        status: receipt ? (receipt.status === 1 ? 'CONFIRMED' : 'FAILED') : 'PENDING',
        fromAddress: transaction.from?.toLowerCase() || '',
        toAddress: transaction.to?.toLowerCase() || '',
        amount: parseFloat(expectedAmount),
        gasUsed: receipt ? parseFloat(receipt.gasUsed.toString()) : null,
        gasFee: receipt && transaction.gasPrice ? 
          parseFloat((receipt.gasUsed * transaction.gasPrice).toString()) / 1e18 : null,
        blockNumber: receipt ? receipt.blockNumber : null,
        confirmations: receipt ? await getConfirmationCount(receipt.blockNumber) : 0,
        relatedEscrowId: escrowOrderId,
        metadata: {
          expectedFrom,
          expectedTo,
          expectedAmount,
          submittedBy: session.user.id,
          submittedAt: new Date().toISOString()
        }
      }
    })

    // 如果交易已确认，验证转账
    if (receipt && receipt.status === 1) {
      const isValidTransfer = await verifyUSDTTransfer(
        txHash,
        expectedFrom,
        expectedTo,
        expectedAmount
      )

      if (isValidTransfer) {
        // 更新托管订单状态
        await updateEscrowOrderStatus(escrowOrderId, transactionType, txHash)
        
        // 更新交易验证状态
        await prisma.blockchainTransaction.update({
          where: { id: blockchainTx.id },
          data: {
            status: 'VERIFIED',
            metadata: {
              ...blockchainTx.metadata,
              verified: true,
              verifiedAt: new Date().toISOString()
            }
          }
        })
      } else {
        await prisma.blockchainTransaction.update({
          where: { id: blockchainTx.id },
          data: {
            status: 'INVALID',
            metadata: {
              ...blockchainTx.metadata,
              verified: false,
              verificationError: 'Transfer verification failed'
            }
          }
        })
      }
    }

    // 启动后台监控任务
    if (!receipt) {
      // 这里可以添加后台任务来持续监控交易状态
      monitorTransactionInBackground(txHash, blockchainTx.id)
    }

    return NextResponse.json({
      success: true,
      message: '交易已提交监控',
      data: {
        transactionId: blockchainTx.id,
        txHash,
        status: blockchainTx.status,
        confirmations: blockchainTx.confirmations,
        verified: receipt ? await verifyUSDTTransfer(txHash, expectedFrom, expectedTo, expectedAmount) : false
      }
    })

  } catch (error) {
    console.error('提交交易监控失败:', error)
    return NextResponse.json(
      { error: '提交交易监控失败' },
      { status: 500 }
    )
  }
}

// 获取交易监控状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const escrowOrderId = searchParams.get('escrowOrderId')
    const txHash = searchParams.get('txHash')

    if (!escrowOrderId && !txHash) {
      return NextResponse.json(
        { error: '需要提供托管订单ID或交易哈希' },
        { status: 400 }
      )
    }

    const where: any = {}
    if (escrowOrderId) where.relatedEscrowId = escrowOrderId
    if (txHash) where.txHash = txHash.toLowerCase()

    const transactions = await prisma.blockchainTransaction.findMany({
      where,
      include: {
        escrow: {
          select: {
            id: true,
            status: true,
            amount: true,
            mediatorId: true,
            buyerId: true,
            sellerId: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 验证用户权限
    const authorizedTransactions = transactions.filter(tx => {
      if (!tx.escrow) return false
      return [
        tx.escrow.mediatorId,
        tx.escrow.buyerId,
        tx.escrow.sellerId
      ].includes(session.user.id)
    })

    return NextResponse.json({
      success: true,
      data: authorizedTransactions
    })

  } catch (error) {
    console.error('获取交易监控状态失败:', error)
    return NextResponse.json(
      { error: '获取交易监控状态失败' },
      { status: 500 }
    )
  }
}

// 辅助函数：获取确认数
async function getConfirmationCount(blockNumber: number): Promise<number> {
  try {
    const { createProvider } = await import('@/lib/blockchain')
    const provider = createProvider()
    const currentBlock = await provider.getBlockNumber()
    return Math.max(0, currentBlock - blockNumber)
  } catch {
    return 0
  }
}

// 辅助函数：更新托管订单状态
async function updateEscrowOrderStatus(
  escrowOrderId: string, 
  transactionType: string, 
  txHash: string
) {
  try {
    const updateData: any = {}
    
    switch (transactionType) {
      case 'ESCROW_FUNDING':
        updateData.status = 'FUNDED'
        updateData.fundedAt = new Date()
        updateData.bnbTransactionHash = txHash
        break
      case 'ESCROW_RELEASE':
        updateData.status = 'COMPLETED'
        updateData.completedAt = new Date()
        break
      case 'ESCROW_REFUND':
        updateData.status = 'CANCELLED'
        updateData.cancelledAt = new Date()
        break
    }

    if (Object.keys(updateData).length > 0) {
      await prisma.escrowOrder.update({
        where: { id: escrowOrderId },
        data: updateData
      })
    }
  } catch (error) {
    console.error('更新托管订单状态失败:', error)
  }
}

// 辅助函数：后台监控交易
async function monitorTransactionInBackground(txHash: string, transactionId: string) {
  // 这里可以实现后台任务逻辑
  // 例如使用队列系统或定时任务来持续监控交易状态
  console.log(`开始监控交易: ${txHash}, ID: ${transactionId}`)
}
