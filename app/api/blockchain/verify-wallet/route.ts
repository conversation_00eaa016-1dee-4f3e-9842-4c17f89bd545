import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { verifyWalletSignature, generateVerificationMessage, isValidAddress } from '@/lib/blockchain'

// 验证钱包签名
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { address, signature, timestamp } = body

    // 验证必要参数
    if (!address || !signature || !timestamp) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 验证地址格式
    if (!isValidAddress(address)) {
      return NextResponse.json(
        { error: '钱包地址格式不正确' },
        { status: 400 }
      )
    }

    // 验证时间戳（5分钟内有效）
    const now = Date.now()
    const timestampNum = parseInt(timestamp)
    if (now - timestampNum > 5 * 60 * 1000) {
      return NextResponse.json(
        { error: '签名已过期，请重新验证' },
        { status: 400 }
      )
    }

    // 生成验证消息
    const message = generateVerificationMessage(address, timestampNum)

    // 验证签名
    const isValidSignature = await verifyWalletSignature(address, message, signature)
    
    if (!isValidSignature) {
      return NextResponse.json(
        { error: '钱包签名验证失败' },
        { status: 400 }
      )
    }

    // 检查地址是否已被其他用户使用
    const existingUser = await prisma.user.findFirst({
      where: {
        bnbWalletAddress: address.toLowerCase(),
        id: { not: session.user.id }
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '该钱包地址已被其他用户绑定' },
        { status: 400 }
      )
    }

    // 更新用户钱包信息
    const updatedUser = await prisma.$transaction(async (tx) => {
      // 更新用户钱包地址和验证状态
      const user = await tx.user.update({
        where: { id: session.user.id },
        data: {
          bnbWalletAddress: address.toLowerCase(),
          bnbWalletVerified: true,
          bnbWalletVerifiedAt: new Date()
        }
      })

      // 创建钱包验证记录
      await tx.mediatorVerification.create({
        data: {
          userId: session.user.id,
          walletAddress: address.toLowerCase(),
          verificationMethod: 'SIGNATURE',
          verificationData: {
            signature,
            message,
            timestamp: timestampNum,
            verifiedAt: new Date().toISOString()
          },
          status: 'VERIFIED',
          verifiedAt: new Date()
        }
      })

      return user
    })

    // 记录安全日志
    await prisma.securityLog.create({
      data: {
        userId: session.user.id,
        action: 'WALLET_VERIFIED',
        details: {
          walletAddress: address.toLowerCase(),
          verificationMethod: 'SIGNATURE',
          timestamp: timestampNum
        },
        ipAddress: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    }).catch(() => {
      // 忽略日志记录失败
    })

    return NextResponse.json({
      success: true,
      message: '钱包验证成功',
      data: {
        address: address.toLowerCase(),
        verified: true,
        verifiedAt: updatedUser.bnbWalletVerifiedAt
      }
    })

  } catch (error) {
    console.error('钱包验证失败:', error)
    return NextResponse.json(
      { error: '钱包验证失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取钱包验证状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        bnbWalletAddress: true,
        bnbWalletVerified: true,
        bnbWalletVerifiedAt: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取最近的验证记录
    const latestVerification = await prisma.mediatorVerification.findFirst({
      where: {
        userId: session.user.id,
        status: 'VERIFIED'
      },
      orderBy: { verifiedAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: {
        address: user.bnbWalletAddress,
        verified: user.bnbWalletVerified,
        verifiedAt: user.bnbWalletVerifiedAt,
        verificationMethod: latestVerification?.verificationMethod || null
      }
    })

  } catch (error) {
    console.error('获取钱包验证状态失败:', error)
    return NextResponse.json(
      { error: '获取验证状态失败' },
      { status: 500 }
    )
  }
}

// 重置钱包验证
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 检查用户是否有进行中的托管订单
    const activeEscrows = await prisma.escrowOrder.count({
      where: {
        OR: [
          { mediatorId: session.user.id },
          { buyerId: session.user.id },
          { sellerId: session.user.id }
        ],
        status: {
          in: ['PENDING', 'FUNDED', 'SHIPPED']
        }
      }
    })

    if (activeEscrows > 0) {
      return NextResponse.json(
        { error: '存在进行中的托管订单，无法重置钱包验证' },
        { status: 400 }
      )
    }

    // 重置钱包验证状态
    await prisma.$transaction(async (tx) => {
      await tx.user.update({
        where: { id: session.user.id },
        data: {
          bnbWalletAddress: null,
          bnbWalletVerified: false,
          bnbWalletVerifiedAt: null
        }
      })

      // 标记所有验证记录为已撤销
      await tx.mediatorVerification.updateMany({
        where: {
          userId: session.user.id,
          status: 'VERIFIED'
        },
        data: {
          status: 'REVOKED'
        }
      })
    })

    // 记录安全日志
    await prisma.securityLog.create({
      data: {
        userId: session.user.id,
        action: 'WALLET_VERIFICATION_RESET',
        details: {
          reason: 'USER_REQUEST',
          timestamp: Date.now()
        },
        ipAddress: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    }).catch(() => {
      // 忽略日志记录失败
    })

    return NextResponse.json({
      success: true,
      message: '钱包验证已重置'
    })

  } catch (error) {
    console.error('重置钱包验证失败:', error)
    return NextResponse.json(
      { error: '重置钱包验证失败' },
      { status: 500 }
    )
  }
}
