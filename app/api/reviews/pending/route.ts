import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取待评价的订单
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 获取用户已完成但未评价的订单
    const completedOrders = await prisma.order.findMany({
      where: {
        status: 'COMPLETED',
        OR: [
          { buyerId: session.user.id },
          { sellerId: session.user.id }
        ]
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        receivedAt: 'desc'
      }
    })

    // 获取用户已经提交的评价
    const existingReviews = await prisma.review.findMany({
      where: {
        reviewerId: session.user.id,
        orderId: {
          in: completedOrders.map(order => order.id)
        }
      },
      select: {
        orderId: true
      }
    })

    const reviewedOrderIds = new Set(existingReviews.map(review => review.orderId))

    // 筛选出待评价的订单
    const pendingReviews = completedOrders
      .filter(order => !reviewedOrderIds.has(order.id))
      .map(order => {
        const isBuyer = order.buyerId === session.user.id
        const completedAt = order.receivedAt || order.updatedAt
        const reviewDeadline = new Date(completedAt.getTime() + 7 * 24 * 60 * 60 * 1000)
        const isExpired = new Date() > reviewDeadline
        
        return {
          orderId: order.id,
          orderNumber: order.orderNumber,
          product: order.product,
          otherParty: isBuyer ? order.seller : order.buyer,
          userRole: isBuyer ? 'buyer' : 'seller',
          reviewType: isBuyer ? 'BUYER_TO_SELLER' : 'SELLER_TO_BUYER',
          completedAt,
          reviewDeadline,
          isExpired,
          canReview: !isExpired
        }
      })

    return NextResponse.json({
      pendingReviews: pendingReviews.filter(review => review.canReview),
      expiredReviews: pendingReviews.filter(review => review.isExpired)
    })

  } catch (error) {
    console.error('Get pending reviews error:', error)
    return NextResponse.json(
      { error: '获取待评价订单失败' },
      { status: 500 }
    )
  }
}
