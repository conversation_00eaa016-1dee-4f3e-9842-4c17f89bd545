import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取评价列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('orderId')
    const userId = searchParams.get('userId') // 查看某个用户的评价
    const type = searchParams.get('type') // 'given' | 'received'

    const where: any = {}

    if (orderId) {
      // 获取特定订单的评价
      where.orderId = orderId
    } else if (userId) {
      // 获取特定用户的评价
      if (type === 'received') {
        where.revieweeId = userId
      } else {
        where.reviewerId = userId
      }
    } else {
      // 获取当前用户相关的评价
      if (type === 'received') {
        where.revieweeId = session.user.id
      } else {
        where.reviewerId = session.user.id
      }
    }

    const reviews = await prisma.review.findMany({
      where,
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        product: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({ reviews })

  } catch (error) {
    console.error('Get reviews error:', error)
    return NextResponse.json(
      { error: '获取评价失败' },
      { status: 500 }
    )
  }
}

// 创建评价
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { orderId, rating, type } = body

    // 验证必填字段
    if (!orderId || !rating || !type) {
      return NextResponse.json(
        { error: '订单ID、评分和评价类型为必填项' },
        { status: 400 }
      )
    }

    // 验证评分范围
    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: '评分必须在1-5之间' },
        { status: 400 }
      )
    }

    // 验证评价类型
    if (!['BUYER_TO_SELLER', 'SELLER_TO_BUYER'].includes(type)) {
      return NextResponse.json(
        { error: '无效的评价类型' },
        { status: 400 }
      )
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        product: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查订单状态
    if (order.status !== 'COMPLETED') {
      return NextResponse.json(
        { error: '只能对已完成的订单进行评价' },
        { status: 400 }
      )
    }

    // 检查评价权限和确定被评价人
    let revieweeId: string
    if (type === 'BUYER_TO_SELLER') {
      if (order.buyerId !== session.user.id) {
        return NextResponse.json(
          { error: '只有买家可以评价卖家' },
          { status: 403 }
        )
      }
      revieweeId = order.sellerId
    } else {
      if (order.sellerId !== session.user.id) {
        return NextResponse.json(
          { error: '只有卖家可以评价买家' },
          { status: 403 }
        )
      }
      revieweeId = order.buyerId
    }

    // 检查是否已经评价过
    const existingReview = await prisma.review.findUnique({
      where: {
        orderId_reviewerId: {
          orderId,
          reviewerId: session.user.id
        }
      }
    })

    if (existingReview) {
      return NextResponse.json(
        { error: '您已经对此订单进行过评价' },
        { status: 400 }
      )
    }

    // 检查评价时间窗口（订单完成后7天内）
    const completedAt = order.receivedAt || order.updatedAt
    const reviewDeadline = new Date(completedAt.getTime() + 7 * 24 * 60 * 60 * 1000)
    
    if (new Date() > reviewDeadline) {
      return NextResponse.json(
        { error: '评价时间已过期（订单完成后7天内有效）' },
        { status: 400 }
      )
    }

    // 创建评价
    const review = await prisma.review.create({
      data: {
        orderId,
        productId: order.productId,
        reviewerId: session.user.id,
        revieweeId,
        rating,
        type,
        canReviewUntil: reviewDeadline
      },
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        product: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    // 更新被评价用户的信誉积分
    await updateUserCreditScore(revieweeId)

    return NextResponse.json(review, { status: 201 })

  } catch (error) {
    console.error('Create review error:', error)
    return NextResponse.json(
      { error: '创建评价失败' },
      { status: 500 }
    )
  }
}

// 更新用户信誉积分的辅助函数
async function updateUserCreditScore(userId: string) {
  try {
    // 获取用户收到的所有评价
    const reviews = await prisma.review.findMany({
      where: {
        revieweeId: userId
      }
    })

    if (reviews.length === 0) {
      return // 没有评价，保持默认积分
    }

    // 计算平均评分
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
    const averageRating = totalRating / reviews.length

    // 根据平均评分和评价数量计算信誉积分
    // 基础分30分，平均评分每1分对应20分，评价数量加成
    const baseScore = 30
    const ratingScore = (averageRating - 1) * 20 // 1星=0分，5星=80分
    const reviewCountBonus = Math.min(reviews.length * 2, 20) // 每个评价+2分，最多+20分
    
    const newCreditScore = Math.round(baseScore + ratingScore + reviewCountBonus)
    const finalScore = Math.max(0, Math.min(100, newCreditScore)) // 限制在0-100之间

    // 更新用户信誉积分
    await prisma.user.update({
      where: { id: userId },
      data: { creditScore: finalScore }
    })

  } catch (error) {
    console.error('Update credit score error:', error)
  }
}
