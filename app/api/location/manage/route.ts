/**
 * 地理位置管理API
 * 用于设置和管理用户、商品、需求单的地理位置信息
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的位置设置
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // user | product | demand
    const itemId = searchParams.get('itemId')

    if (type === 'user') {
      // 获取用户默认位置设置
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: {
          id: true,
          name: true,
          // 如果用户表有位置字段的话
          // defaultLatitude: true,
          // defaultLongitude: true,
          // defaultAddress: true,
          // defaultCity: true,
          // defaultDistrict: true
        }
      })

      return NextResponse.json({
        user,
        hasLocationPermission: true, // 前端会检查实际权限
        locationSettings: {
          autoDetect: true,
          shareLocation: false,
          defaultRadius: 5
        }
      })

    } else if (type === 'product' && itemId) {
      // 获取商品位置信息
      const product = await prisma.product.findUnique({
        where: { 
          id: itemId,
          sellerId: session.user.id // 只能查看自己的商品
        },
        select: {
          id: true,
          title: true,
          latitude: true,
          longitude: true,
          address: true,
          city: true,
          district: true,
          locationRadius: true,
          isLocationPublic: true,
          preferLocalTrade: true
        }
      })

      if (!product) {
        return NextResponse.json(
          { error: '商品不存在或无权限访问' },
          { status: 404 }
        )
      }

      return NextResponse.json({ product })

    } else if (type === 'demand' && itemId) {
      // 获取需求单位置信息
      const demand = await prisma.demand.findUnique({
        where: { 
          id: itemId,
          userId: session.user.id // 只能查看自己的需求单
        },
        select: {
          id: true,
          title: true,
          latitude: true,
          longitude: true,
          address: true,
          city: true,
          district: true,
          locationRadius: true,
          isLocationPublic: true
        }
      })

      if (!demand) {
        return NextResponse.json(
          { error: '需求单不存在或无权限访问' },
          { status: 404 }
        )
      }

      return NextResponse.json({ demand })

    } else {
      return NextResponse.json(
        { error: '请指定有效的查询类型和ID' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('获取位置信息失败:', error)
    return NextResponse.json(
      { error: '获取位置信息失败' },
      { status: 500 }
    )
  }
}

// 更新位置信息
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { 
      type, 
      itemId, 
      latitude, 
      longitude, 
      address, 
      city, 
      district,
      locationRadius,
      isLocationPublic,
      preferLocalTrade
    } = await request.json()

    // 验证坐标
    if (latitude !== undefined && (latitude < -90 || latitude > 90)) {
      return NextResponse.json(
        { error: '纬度必须在-90到90之间' },
        { status: 400 }
      )
    }

    if (longitude !== undefined && (longitude < -180 || longitude > 180)) {
      return NextResponse.json(
        { error: '经度必须在-180到180之间' },
        { status: 400 }
      )
    }

    if (locationRadius !== undefined && (locationRadius < 0 || locationRadius > 100)) {
      return NextResponse.json(
        { error: '位置半径必须在0-100km之间' },
        { status: 400 }
      )
    }

    if (type === 'product' && itemId) {
      // 更新商品位置
      const product = await prisma.product.findUnique({
        where: { 
          id: itemId,
          sellerId: session.user.id
        }
      })

      if (!product) {
        return NextResponse.json(
          { error: '商品不存在或无权限修改' },
          { status: 404 }
        )
      }

      const updatedProduct = await prisma.product.update({
        where: { id: itemId },
        data: {
          ...(latitude !== undefined && { latitude }),
          ...(longitude !== undefined && { longitude }),
          ...(address !== undefined && { address }),
          ...(city !== undefined && { city }),
          ...(district !== undefined && { district }),
          ...(locationRadius !== undefined && { locationRadius }),
          ...(isLocationPublic !== undefined && { isLocationPublic }),
          ...(preferLocalTrade !== undefined && { preferLocalTrade })
        }
      })

      return NextResponse.json({
        success: true,
        message: '商品位置信息已更新',
        product: updatedProduct
      })

    } else if (type === 'demand' && itemId) {
      // 更新需求单位置
      const demand = await prisma.demand.findUnique({
        where: { 
          id: itemId,
          userId: session.user.id
        }
      })

      if (!demand) {
        return NextResponse.json(
          { error: '需求单不存在或无权限修改' },
          { status: 404 }
        )
      }

      const updatedDemand = await prisma.demand.update({
        where: { id: itemId },
        data: {
          ...(latitude !== undefined && { latitude }),
          ...(longitude !== undefined && { longitude }),
          ...(address !== undefined && { address }),
          ...(city !== undefined && { city }),
          ...(district !== undefined && { district }),
          ...(locationRadius !== undefined && { locationRadius }),
          ...(isLocationPublic !== undefined && { isLocationPublic })
        }
      })

      return NextResponse.json({
        success: true,
        message: '需求单位置信息已更新',
        demand: updatedDemand
      })

    } else {
      return NextResponse.json(
        { error: '请指定有效的更新类型和ID' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('更新位置信息失败:', error)
    return NextResponse.json(
      { error: '更新位置信息失败' },
      { status: 500 }
    )
  }
}

// 批量设置位置信息
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { 
      action,
      items, // [{ type, id, location }]
      defaultLocation 
    } = await request.json()

    if (action === 'batch-update' && items && Array.isArray(items)) {
      const results: { type: string; id: string; success: boolean; error?: string }[] = []

      for (const item of items) {
        try {
          const { type, id, location } = item

          if (type === 'product') {
            const product = await prisma.product.findUnique({
              where: { 
                id,
                sellerId: session.user.id
              }
            })

            if (product) {
              await prisma.product.update({
                where: { id },
                data: {
                  latitude: location.latitude,
                  longitude: location.longitude,
                  address: location.address,
                  city: location.city,
                  district: location.district
                }
              })
              results.push({ type, id, success: true })
            } else {
              results.push({ type, id, success: false, error: '商品不存在或无权限' })
            }

          } else if (type === 'demand') {
            const demand = await prisma.demand.findUnique({
              where: { 
                id,
                userId: session.user.id
              }
            })

            if (demand) {
              await prisma.demand.update({
                where: { id },
                data: {
                  latitude: location.latitude,
                  longitude: location.longitude,
                  address: location.address,
                  city: location.city,
                  district: location.district
                }
              })
              results.push({ type, id, success: true })
            } else {
              results.push({ type, id, success: false, error: '需求单不存在或无权限' })
            }
          }

        } catch (error) {
          results.push({ 
            type: item.type, 
            id: item.id, 
            success: false, 
            error: error instanceof Error ? error.message : '更新失败' 
          })
        }
      }

      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      return NextResponse.json({
        success: true,
        message: `批量更新完成：成功 ${successCount} 个，失败 ${failCount} 个`,
        results
      })

    } else if (action === 'set-default' && defaultLocation) {
      // 设置用户默认位置（如果用户表支持的话）
      // 这里可以保存到用户偏好设置中
      
      return NextResponse.json({
        success: true,
        message: '默认位置已设置',
        defaultLocation
      })

    } else {
      return NextResponse.json(
        { error: '无效的操作类型' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('批量位置操作失败:', error)
    return NextResponse.json(
      { error: '批量位置操作失败' },
      { status: 500 }
    )
  }
}
