/**
 * 附近搜索API
 * 支持商品和需求单的地理位置筛选
 */

import { NextRequest, NextResponse } from 'next/server'
import {
  searchNearbyProducts,
  searchNearbyDemands,
  getPopularAreas,
  calculateDistance,
  formatDistance
} from '@/lib/location-service'

// 获取附近的商品或需求单
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 解析参数
    const type = searchParams.get('type') || 'product' // product | demand
    const latitude = parseFloat(searchParams.get('lat') || '0')
    const longitude = parseFloat(searchParams.get('lng') || '0')
    const radius = parseFloat(searchParams.get('radius') || '5') // 默认5km
    const category = searchParams.get('category') || undefined
    const demandType = searchParams.get('demandType') || undefined
    const status = searchParams.get('status') || undefined
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sortBy') || 'distance'
    const sortOrder = searchParams.get('sortOrder') || 'asc'
    const action = searchParams.get('action')

    // 验证必需参数
    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: '请提供有效的地理位置坐标' },
        { status: 400 }
      )
    }

    if (radius <= 0 || radius > 100) {
      return NextResponse.json(
        { error: '搜索半径必须在0-100km之间' },
        { status: 400 }
      )
    }

    // 特殊操作处理
    if (action === 'popular-areas') {
      const areas = await getPopularAreas(type as 'product' | 'demand')
      return NextResponse.json({ areas })
    }

    // 构建筛选条件
    const filter = { latitude, longitude, radius }
    const options = {
      limit,
      offset,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any
    }

    let result: any

    if (type === 'product') {
      result = await searchNearbyProducts(filter, {
        ...options,
        category,
        status
      })
    } else if (type === 'demand') {
      result = await searchNearbyDemands(filter, {
        ...options,
        demandType,
        status
      })
    } else {
      return NextResponse.json(
        { error: '无效的搜索类型' },
        { status: 400 }
      )
    }

    // 添加搜索元数据
    const metadata = {
      searchCenter: {
        latitude,
        longitude
      },
      searchRadius: radius,
      searchRadiusText: formatDistance(radius),
      totalResults: result.total,
      hasMore: result.hasMore,
      currentPage: Math.floor(offset / limit) + 1,
      totalPages: Math.ceil(result.total / limit)
    }

    return NextResponse.json({
      ...result,
      metadata
    })

  } catch (error) {
    console.error('附近搜索失败:', error)
    return NextResponse.json(
      { error: '附近搜索失败' },
      { status: 500 }
    )
  }
}

// 计算两点间距离
export async function POST(request: NextRequest) {
  try {
    const { from, to, locations } = await request.json()

    if (locations && Array.isArray(locations)) {
      // 批量计算距离
      if (!from || !from.latitude || !from.longitude) {
        return NextResponse.json(
          { error: '请提供起始位置坐标' },
          { status: 400 }
        )
      }

      const results = locations.map((location: any) => {
        if (!location.latitude || !location.longitude) {
          return {
            ...location,
            distance: null,
            distanceText: '位置信息不完整'
          }
        }

        const distance = calculateDistance(
          from.latitude,
          from.longitude,
          location.latitude,
          location.longitude
        )

        return {
          ...location,
          distance,
          distanceText: formatDistance(distance)
        }
      })

      return NextResponse.json({ results })

    } else if (from && to) {
      // 计算两点间距离
      if (!from.latitude || !from.longitude || !to.latitude || !to.longitude) {
        return NextResponse.json(
          { error: '请提供完整的坐标信息' },
          { status: 400 }
        )
      }

      const distance = calculateDistance(
        from.latitude,
        from.longitude,
        to.latitude,
        to.longitude
      )

      return NextResponse.json({
        distance,
        distanceText: formatDistance(distance),
        from,
        to
      })

    } else {
      return NextResponse.json(
        { error: '请提供有效的位置参数' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('距离计算失败:', error)
    return NextResponse.json(
      { error: '距离计算失败' },
      { status: 500 }
    )
  }
}
