/**
 * 用户信誉档案API
 * 展示担保金转化的信誉指标
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  getUserReputationProfile,
  calculateReputationScore,
  getUserPerformanceStats,
  generateReputationBadge,
  REPUTATION_LEVELS 
} from '@/lib/reputation-system'
import { prisma } from '@/lib/prisma'

// 获取用户信誉档案
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const targetUserId = searchParams.get('userId')
    const action = searchParams.get('action')

    // 如果指定了用户ID，获取该用户的公开信誉信息
    if (targetUserId) {
      const publicProfile = await getPublicReputationProfile(targetUserId)
      return NextResponse.json(publicProfile)
    }

    // 否则获取当前登录用户的完整信誉档案
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    switch (action) {
      case 'score':
        const score = await calculateReputationScore(session.user.id)
        return NextResponse.json(score)

      case 'performance':
        const performance = await getUserPerformanceStats(session.user.id)
        return NextResponse.json(performance)

      case 'badge':
        const [scoreData, performanceData] = await Promise.all([
          calculateReputationScore(session.user.id),
          getUserPerformanceStats(session.user.id)
        ])
        const badge = generateReputationBadge(
          scoreData.level, 
          scoreData.totalScore, 
          performanceData.fulfillmentRate
        )
        return NextResponse.json(badge)

      default:
        const fullProfile = await getUserReputationProfile(session.user.id)
        return NextResponse.json(fullProfile)
    }

  } catch (error) {
    console.error('获取信誉档案失败:', error)
    return NextResponse.json(
      { error: '获取信誉档案失败' },
      { status: 500 }
    )
  }
}

/**
 * 获取用户公开信誉信息（用于商品页面展示）
 */
async function getPublicReputationProfile(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        createdAt: true,
        depositBalance: true // 担保金作为信誉指标可以公开
      }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const [reputation, performance] = await Promise.all([
      calculateReputationScore(userId),
      getUserPerformanceStats(userId)
    ])

    const badge = generateReputationBadge(
      reputation.level, 
      reputation.totalScore, 
      performance.fulfillmentRate
    )

    const levelConfig = REPUTATION_LEVELS[reputation.level]

    return {
      user: {
        id: user.id,
        name: user.name,
        memberSince: user.createdAt,
        depositBalance: user.depositBalance
      },
      reputation: {
        level: reputation.level,
        levelConfig: {
          title: levelConfig.title,
          icon: levelConfig.icon,
          color: levelConfig.color,
          stars: levelConfig.stars
        },
        score: reputation.totalScore,
        badge: badge.badge,
        trustLevel: badge.trustLevel
      },
      performance: {
        totalOrders: performance.totalOrders,
        fulfillmentRate: performance.fulfillmentRate,
        avgRating: performance.avgRating,
        totalReviews: performance.totalReviews,
        responseTime: performance.responseTime
      },
      highlights: generateUserHighlights(reputation, performance, user.depositBalance)
    }

  } catch (error) {
    console.error('获取公开信誉档案失败:', error)
    throw error
  }
}

/**
 * 生成用户亮点信息
 */
function generateUserHighlights(
  reputation: any, 
  performance: any, 
  depositBalance: number
): string[] {
  const highlights: string[] = []

  // 担保金亮点
  if (depositBalance >= 5000) {
    highlights.push('💎 超高担保金用户')
  } else if (depositBalance >= 2000) {
    highlights.push('🏆 高担保金用户')
  } else if (depositBalance >= 500) {
    highlights.push('⭐ 优质担保金用户')
  }

  // 履约率亮点
  if (performance.fulfillmentRate >= 98) {
    highlights.push('🎯 极高履约率')
  } else if (performance.fulfillmentRate >= 95) {
    highlights.push('✅ 高履约率')
  }

  // 评价亮点
  if (performance.avgRating >= 4.8 && performance.totalReviews >= 10) {
    highlights.push('🌟 五星好评用户')
  } else if (performance.avgRating >= 4.5 && performance.totalReviews >= 5) {
    highlights.push('👍 好评用户')
  }

  // 交易量亮点
  if (performance.totalOrders >= 100) {
    highlights.push('🔥 资深交易用户')
  } else if (performance.totalOrders >= 50) {
    highlights.push('📈 活跃交易用户')
  } else if (performance.totalOrders >= 10) {
    highlights.push('🚀 经验用户')
  }

  // 响应速度亮点
  if (performance.responseTime <= 2 && performance.totalOrders >= 5) {
    highlights.push('⚡ 快速响应')
  }

  // 信誉等级亮点
  if (reputation.level === 'MASTER') {
    highlights.push('👑 大师级用户')
  } else if (reputation.level === 'EXPERT') {
    highlights.push('🏅 专家用户')
  }

  return highlights.slice(0, 4) // 最多显示4个亮点
}

// 更新用户信誉档案
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { action } = await request.json()

    switch (action) {
      case 'refresh':
        // 刷新信誉分数
        const updatedScore = await calculateReputationScore(session.user.id)
        
        // 更新用户的信誉等级（如果有变化）
        await prisma.user.update({
          where: { id: session.user.id },
          data: {
            creditLevel: updatedScore.level.toUpperCase(),
            creditPoints: updatedScore.totalScore,
            lastCreditUpdate: new Date()
          }
        })

        return NextResponse.json({
          success: true,
          message: '信誉档案已刷新',
          score: updatedScore
        })

      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('更新信誉档案失败:', error)
    return NextResponse.json(
      { error: '更新信誉档案失败' },
      { status: 500 }
    )
  }
}
