import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取托管聊天数据
export async function GET(
  _request: NextRequest,
  { params }: { params: { escrowId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { escrowId } = params

    // 获取托管订单信息
    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowId },
      include: {
        order: {
          include: {
            product: {
              select: {
                title: true,
                images: true
              }
            }
          }
        },
        buyer: {
          select: { id: true, name: true, email: true }
        },
        seller: {
          select: { id: true, name: true, email: true }
        },
        mediator: {
          select: { id: true, name: true, email: true }
        },
        chatRoom: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    // 验证权限
    const allowedUsers = [
      escrowOrder.buyerId,
      escrowOrder.sellerId,
      escrowOrder.mediatorId
    ]

    const isAdmin = session.user.role === 'ADMIN'
    
    if (!allowedUsers.includes(session.user.id) && !isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    // 如果没有聊天室，创建一个
    let chatRoom = escrowOrder.chatRoom
    if (!chatRoom) {
      chatRoom = await prisma.escrowChatRoom.create({
        data: {
          escrowOrderId: escrowId,
          roomCode: `ESCROW_${escrowId}_${Date.now()}`,
          isActive: true
        }
      })
    }

    // 获取聊天消息
    const messages = await (prisma as any).escrowChatMessage.findMany({
      where: {
        chatRoomId: chatRoom.id
      },
      include: {
        sender: {
          select: { id: true, name: true, role: true }
        }
      },
      orderBy: { createdAt: 'asc' },
      take: 100 // 限制消息数量
    })

    // 构建参与者列表
    const participants: Array<{
      id: string
      name: string
      role: 'buyer' | 'seller' | 'mediator' | 'admin'
      isOnline: boolean
    }> = [
      {
        id: escrowOrder.buyer.id,
        name: escrowOrder.buyer.name || '买家',
        role: 'buyer' as const,
        isOnline: true // 简化处理，实际需要实时状态
      },
      {
        id: escrowOrder.seller.id,
        name: escrowOrder.seller.name || '卖家',
        role: 'seller' as const,
        isOnline: true
      },
      {
        id: escrowOrder.mediator.id,
        name: escrowOrder.mediator.name || '中间人',
        role: 'mediator' as const,
        isOnline: true
      }
    ]

    // 如果有管理员参与，添加到列表
    if (isAdmin) {
      const adminExists = participants.some(p => p.id === session.user.id)
      if (!adminExists) {
        participants.push({
          id: session.user.id,
          name: session.user.name || '管理员',
          role: 'admin' as const,
          isOnline: true
        })
      }
    }

    // 转换消息格式
    const formattedMessages = messages.map((msg: any) => {
      let senderRole = 'buyer'
      if (msg.senderId === escrowOrder.sellerId) senderRole = 'seller'
      else if (msg.senderId === escrowOrder.mediatorId) senderRole = 'mediator'
      else if (msg.sender.role === 'ADMIN') senderRole = 'admin'

      return {
        id: msg.id,
        content: msg.content,
        senderId: msg.senderId,
        senderName: msg.sender.name || '用户',
        senderRole,
        timestamp: msg.createdAt.toISOString(),
        messageType: msg.messageType || 'text'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        escrowOrder: {
          id: escrowOrder.id,
          status: escrowOrder.status,
          amount: escrowOrder.amount,
          order: {
            orderNumber: escrowOrder.order.orderNumber,
            product: escrowOrder.order.product
          },
          buyer: escrowOrder.buyer,
          seller: escrowOrder.seller,
          mediator: escrowOrder.mediator
        },
        chatRoom: {
          id: chatRoom.id,
          roomCode: chatRoom.roomCode,
          isActive: chatRoom.isActive
        },
        messages: formattedMessages,
        participants
      }
    })

  } catch (error) {
    console.error('获取托管聊天数据失败:', error)
    return NextResponse.json(
      { error: '获取聊天数据失败' },
      { status: 500 }
    )
  }
}

// 发送消息
export async function POST(
  request: NextRequest,
  { params }: { params: { escrowId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { escrowId } = params
    const body = await request.json()
    const { content, messageType = 'text' } = body

    if (!content || !content.trim()) {
      return NextResponse.json(
        { error: '消息内容不能为空' },
        { status: 400 }
      )
    }

    // 获取托管订单信息
    const escrowOrder = await prisma.escrowOrder.findUnique({
      where: { id: escrowId },
      include: {
        chatRoom: true
      }
    })

    if (!escrowOrder) {
      return NextResponse.json(
        { error: '托管订单不存在' },
        { status: 404 }
      )
    }

    // 验证权限
    const allowedUsers = [
      escrowOrder.buyerId,
      escrowOrder.sellerId,
      escrowOrder.mediatorId
    ]

    const isAdmin = session.user.role === 'ADMIN'
    
    if (!allowedUsers.includes(session.user.id) && !isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    // 检查聊天室是否活跃
    if (!escrowOrder.chatRoom?.isActive) {
      return NextResponse.json(
        { error: '聊天室已关闭' },
        { status: 400 }
      )
    }

    // 创建消息
    const message = await (prisma as any).escrowChatMessage.create({
      data: {
        chatRoomId: escrowOrder.chatRoom.id,
        senderId: session.user.id,
        content: content.trim(),
        messageType
      },
      include: {
        sender: {
          select: { id: true, name: true, role: true }
        }
      }
    })

    // 发送通知给其他参与者
    const notificationPromises: Promise<any>[] = []
    
    // 通知买家
    if (escrowOrder.buyerId !== session.user.id) {
      notificationPromises.push(
        prisma.notification.create({
          data: {
            userId: escrowOrder.buyerId,
            type: 'CHAT',
            title: '托管订单新消息',
            message: `${session.user.name || '用户'}: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
            data: {
              escrowId,
              chatRoomId: escrowOrder.chatRoom.id,
              messageId: message.id
            },
            priority: 'NORMAL'
          }
        }).catch(() => {})
      )
    }

    // 通知卖家
    if (escrowOrder.sellerId !== session.user.id) {
      notificationPromises.push(
        prisma.notification.create({
          data: {
            userId: escrowOrder.sellerId,
            type: 'CHAT',
            title: '托管订单新消息',
            message: `${session.user.name || '用户'}: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
            data: {
              escrowId,
              chatRoomId: escrowOrder.chatRoom.id,
              messageId: message.id
            },
            priority: 'NORMAL'
          }
        }).catch(() => {})
      )
    }

    // 通知中间人
    if (escrowOrder.mediatorId !== session.user.id) {
      notificationPromises.push(
        prisma.notification.create({
          data: {
            userId: escrowOrder.mediatorId,
            type: 'CHAT',
            title: '托管订单新消息',
            message: `${session.user.name || '用户'}: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
            data: {
              escrowId,
              chatRoomId: escrowOrder.chatRoom.id,
              messageId: message.id
            },
            priority: 'NORMAL'
          }
        }).catch(() => {})
      )
    }

    // 通知管理员（如果有管理员在线且不是发送者）
    if (!isAdmin) {
      // 这里可以添加通知在线管理员的逻辑
    }

    await Promise.all(notificationPromises)

    return NextResponse.json({
      success: true,
      message: '消息发送成功',
      data: {
        id: message.id,
        content: message.content,
        senderId: message.senderId,
        senderName: message.sender.name || '用户',
        timestamp: message.createdAt.toISOString(),
        messageType: message.messageType
      }
    })

  } catch (error) {
    console.error('发送消息失败:', error)
    return NextResponse.json(
      { error: '发送消息失败' },
      { status: 500 }
    )
  }
}
