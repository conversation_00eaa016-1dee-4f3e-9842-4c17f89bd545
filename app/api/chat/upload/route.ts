import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import sharp from 'sharp'
import { prisma } from '@/lib/prisma'

// 支持的文件类型
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/mov', 'video/avi', 'video/webm', 'video/quicktime']
const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES]

// 文件大小限制
const MAX_IMAGE_SIZE = 50 * 1024 * 1024 // 50MB
const MAX_VIDEO_SIZE = 50 * 1024 * 1024 // 50MB
const MAX_VIDEO_DURATION = 5 * 60 // 5分钟（秒）

// 获取视频元数据的函数（简化版，实际项目中可能需要使用ffprobe）
async function getVideoMetadata(buffer: Buffer): Promise<{ duration?: number, width?: number, height?: number }> {
  // 这里是一个简化的实现，实际项目中建议使用ffprobe或类似工具
  // 目前返回空对象，后续可以集成更完整的视频处理库
  return {}
}

// 获取图片元数据
async function getImageMetadata(buffer: Buffer): Promise<{ width: number, height: number }> {
  try {
    const metadata = await sharp(buffer).metadata()
    return {
      width: metadata.width || 0,
      height: metadata.height || 0
    }
  } catch (error) {
    return { width: 0, height: 0 }
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const orderId = formData.get('orderId') as string

    if (!file) {
      return NextResponse.json(
        { error: '未选择文件' },
        { status: 400 }
      )
    }

    if (!orderId) {
      return NextResponse.json(
        { error: '订单ID为必填项' },
        { status: 400 }
      )
    }

    // 验证用户是否有权限在此订单中上传文件
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        id: true,
        buyerId: true,
        sellerId: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限在此订单中上传文件' },
        { status: 403 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: '不支持的文件类型。支持的格式：JPG、PNG、GIF、WebP、MP4、MOV、AVI、WebM' },
        { status: 400 }
      )
    }

    // 验证文件大小
    const isImage = ALLOWED_IMAGE_TYPES.includes(file.type)
    const isVideo = ALLOWED_VIDEO_TYPES.includes(file.type)
    const maxSize = isImage ? MAX_IMAGE_SIZE : MAX_VIDEO_SIZE

    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `文件大小不能超过 ${maxSize / (1024 * 1024)}MB` },
        { status: 400 }
      )
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 获取文件元数据
    let metadata: any = {}
    let messageType = 'FILE'

    if (isImage) {
      messageType = 'IMAGE'
      metadata = await getImageMetadata(buffer)
    } else if (isVideo) {
      messageType = 'VIDEO'
      metadata = await getVideoMetadata(buffer)
      
      // 检查视频时长（如果能获取到的话）
      if (metadata.duration && metadata.duration > MAX_VIDEO_DURATION) {
        return NextResponse.json(
          { error: '视频时长不能超过5分钟' },
          { status: 400 }
        )
      }
    }

    // 创建文件名
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 8)
    const extension = file.name.split('.').pop()
    const fileName = `chat_${orderId}_${timestamp}_${randomString}.${extension}`

    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'chat')
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    let processedBuffer: Buffer = buffer

    // 如果是图片，进行压缩处理
    if (isImage && file.type !== 'image/gif') {
      try {
        processedBuffer = await sharp(Buffer.from(buffer))
          .resize(1920, 1080, { 
            fit: 'inside',
            withoutEnlargement: true 
          })
          .jpeg({ quality: 85 })
          .toBuffer()
      } catch (error) {
        // 如果处理失败，使用原始文件
        console.warn('Image processing failed, using original file:', error)
        processedBuffer = buffer
      }
    }

    // 保存文件
    const filePath = join(uploadDir, fileName)
    await writeFile(filePath, processedBuffer)

    // 返回文件信息
    const fileUrl = `/uploads/chat/${fileName}`

    return NextResponse.json({
      success: true,
      fileUrl,
      fileName: file.name,
      fileSize: processedBuffer.length,
      fileMimeType: file.type,
      messageType,
      metadata,
      originalSize: file.size
    })

  } catch (error) {
    console.error('Chat file upload error:', error)
    return NextResponse.json(
      { error: '文件上传失败' },
      { status: 500 }
    )
  }
}
