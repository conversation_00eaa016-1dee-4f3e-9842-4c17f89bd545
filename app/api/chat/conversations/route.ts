import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户的聊天对话列表
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 获取用户参与的所有订单
    const orders = await prisma.order.findMany({
      where: {
        OR: [
          { buyerId: session.user.id },
          { sellerId: session.user.id }
        ]
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        messages: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1 // 只取最新的一条消息
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    // 构建对话列表
    const conversations = await Promise.all(
      orders.map(async (order) => {
        // 确定对话的另一方
        const otherParty = order.buyerId === session.user.id ? order.seller : order.buyer

        // 获取未读消息数量
        const unreadCount = await prisma.message.count({
          where: {
            orderId: order.id,
            receiverId: session.user.id,
            status: {
              in: ['SENT', 'DELIVERED']
            }
          }
        })

        return {
          orderId: order.id,
          orderNumber: order.orderNumber,
          product: order.product,
          otherParty,
          lastMessage: order.messages[0] || null,
          unreadCount,
          orderStatus: order.status
        }
      })
    )

    // 按最后消息时间排序，没有消息的按订单更新时间排序
    conversations.sort((a, b) => {
      const aTime = a.lastMessage ? new Date(a.lastMessage.createdAt).getTime() : 0
      const bTime = b.lastMessage ? new Date(b.lastMessage.createdAt).getTime() : 0
      return bTime - aTime
    })

    return NextResponse.json({
      conversations
    })

  } catch (error) {
    console.error('Get conversations error:', error)
    return NextResponse.json(
      { error: '获取对话列表失败' },
      { status: 500 }
    )
  }
}
