/**
 * 中间人申请API
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 申请成为中间人
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        creditScore: true,
        depositBalance: true,
        isGuarantor: true,
        status: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 检查是否已经是中间人
    if (user.isGuarantor) {
      return NextResponse.json(
        { error: '您已经是认证中间人' },
        { status: 400 }
      )
    }

    // 检查账户状态
    if (user.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: '账户状态异常，无法申请' },
        { status: 400 }
      )
    }

    // 检查信用分数
    if (user.creditScore < 80) {
      return NextResponse.json(
        { error: `信用分数不足，需要80分以上，当前${user.creditScore}分` },
        { status: 400 }
      )
    }

    // 检查保证金余额
    if (user.depositBalance < 1000) {
      return NextResponse.json(
        { error: `保证金不足，需要1000 USDT以上，当前${user.depositBalance.toFixed(2)} USDT` },
        { status: 400 }
      )
    }

    // 检查是否有待处理的申请
    const existingApplication = await prisma.guarantorApplication.findFirst({
      where: {
        userId,
        status: 'PENDING'
      }
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: '您已有待处理的申请，请等待审核结果' },
        { status: 400 }
      )
    }

    const { reason, experience } = await request.json()

    // 创建申请记录
    const application = await prisma.guarantorApplication.create({
      data: {
        userId,
        reason: reason || '申请成为中间人，为平台交易提供担保服务',
        experience: experience || '',
        creditScoreAtApply: user.creditScore,
        depositBalanceAtApply: user.depositBalance,
        status: 'PENDING',
        metadata: {
          userAgent: request.headers.get('user-agent'),
          ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
          appliedAt: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '申请已提交，请等待管理员审核',
      applicationId: application.id,
      estimatedReviewTime: '1-3个工作日'
    })

  } catch (error) {
    console.error('中间人申请失败:', error)
    return NextResponse.json(
      { error: '申请失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取申请状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取最新的申请记录
    const application = await prisma.guarantorApplication.findFirst({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        status: true,
        reason: true,
        experience: true,
        reviewNotes: true,
        reviewedAt: true,
        createdAt: true,
        creditScoreAtApply: true,
        depositBalanceAtApply: true
      }
    })

    // 获取用户当前状态
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        isGuarantor: true,
        creditScore: true,
        depositBalance: true
      }
    })

    return NextResponse.json({
      user: {
        isGuarantor: user?.isGuarantor || false,
        creditScore: user?.creditScore || 0,
        depositBalance: user?.depositBalance || 0
      },
      application: application || null,
      canApply: !user?.isGuarantor && 
                (user?.creditScore || 0) >= 80 && 
                (user?.depositBalance || 0) >= 1000 &&
                (!application || application.status !== 'PENDING')
    })

  } catch (error) {
    console.error('获取申请状态失败:', error)
    return NextResponse.json(
      { error: '获取申请状态失败' },
      { status: 500 }
    )
  }
}
