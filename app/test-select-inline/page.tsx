'use client'

import { useState, type CSSProperties } from 'react'

export default function TestSelectInlinePage() {
  const [selectedValue, setSelectedValue] = useState('')

  // 内联样式，强制覆盖任何可能的CSS冲突
  const selectStyle: CSSProperties = {
    color: '#1f2937',
    backgroundColor: '#ffffff',
    border: '1px solid #d1d5db',
    borderRadius: '0.375rem',
    padding: '0.5rem 0.75rem',
    fontSize: '14px',
    lineHeight: '1.5',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    width: '100%',
    minHeight: '2.5rem',
    cursor: 'pointer',
    WebkitAppearance: 'menulist',
    MozAppearance: 'menulist',
    appearance: 'menulist',
    opacity: 1,
    visibility: 'visible',
    display: 'block'
  }

  const optionStyle = {
    color: '#1f2937',
    backgroundColor: '#ffffff',
    fontSize: '14px',
    padding: '0.5rem'
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">内联样式选择框测试</h1>
          
          <div className="space-y-6">
            {/* 使用内联样式的选择框 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                内联样式选择框
              </label>
              <select
                value={selectedValue}
                onChange={(e) => setSelectedValue(e.target.value)}
                style={selectStyle}
              >
                <option value="" style={optionStyle}>请选择一个选项</option>
                <option value="option1" style={optionStyle}>选项 1 - 这是第一个选项</option>
                <option value="option2" style={optionStyle}>选项 2 - 这是第二个选项</option>
                <option value="option3" style={optionStyle}>选项 3 - 这是第三个选项</option>
                <option value="option4" style={optionStyle}>选项 4 - 这是第四个选项</option>
                <option value="option5" style={optionStyle}>选项 5 - 这是第五个选项</option>
              </select>
              <p className="mt-2 text-sm text-gray-600">
                当前选择: <strong>{selectedValue || '未选择'}</strong>
              </p>
            </div>

            {/* 对比：使用CSS类的选择框 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CSS类样式选择框
              </label>
              <select
                value={selectedValue}
                onChange={(e) => setSelectedValue(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择一个选项</option>
                <option value="option1">选项 1 - 这是第一个选项</option>
                <option value="option2">选项 2 - 这是第二个选项</option>
                <option value="option3">选项 3 - 这是第三个选项</option>
                <option value="option4">选项 4 - 这是第四个选项</option>
                <option value="option5">选项 5 - 这是第五个选项</option>
              </select>
              <p className="mt-2 text-sm text-gray-600">
                当前选择: <strong>{selectedValue || '未选择'}</strong>
              </p>
            </div>

            {/* 最简单的选择框 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最简单的选择框（无样式）
              </label>
              <select
                value={selectedValue}
                onChange={(e) => setSelectedValue(e.target.value)}
              >
                <option value="">请选择一个选项</option>
                <option value="option1">选项 1</option>
                <option value="option2">选项 2</option>
                <option value="option3">选项 3</option>
              </select>
              <p className="mt-2 text-sm text-gray-600">
                当前选择: <strong>{selectedValue || '未选择'}</strong>
              </p>
            </div>

            {/* 测试说明 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">测试目的</h3>
              <ul className="text-yellow-700 space-y-1 text-sm">
                <li>• <strong>内联样式选择框</strong>：使用强制内联样式，应该能正确显示文本</li>
                <li>• <strong>CSS类样式选择框</strong>：使用Tailwind CSS类，可能受到样式冲突影响</li>
                <li>• <strong>无样式选择框</strong>：使用浏览器默认样式，应该能正常工作</li>
              </ul>
            </div>

            {/* 调试信息 */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">调试信息</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>浏览器:</strong> {typeof window !== 'undefined' ? window.navigator.userAgent : '服务端渲染'}</p>
                <p><strong>当前选择值:</strong> {selectedValue || '未选择'}</p>
                <p><strong>页面加载时间:</strong> {new Date().toLocaleString()}</p>
              </div>
            </div>

            {/* 操作说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">操作说明</h3>
              <ol className="text-blue-700 space-y-1 text-sm list-decimal list-inside">
                <li>点击每个选择框，查看是否能看到选项文本</li>
                <li>选择不同的选项，查看是否能正确显示选中的值</li>
                <li>如果某个选择框无法显示文本，请在浏览器开发者工具中检查其样式</li>
                <li>比较三个选择框的显示效果，找出问题所在</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
