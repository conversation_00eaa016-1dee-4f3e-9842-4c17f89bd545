'use client'

import { useState } from 'react'

export default function TestSelectPage() {
  const [selectedCity, setSelectedCity] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedCondition, setSelectedCondition] = useState('')

  const cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市']
  const categories = ['电子产品', '服装配饰', '家居用品', '图书音像', '运动户外', '美妆护肤']
  const conditions = ['全新', '几乎全新', '轻微使用痕迹', '明显使用痕迹', '功能正常']

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">选择框测试页面</h1>
          
          <div className="space-y-6">
            {/* 基础选择框测试 */}
            <div>
              <h2 className="text-lg font-semibold mb-4">基础HTML选择框</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    城市选择
                  </label>
                  <select
                    value={selectedCity}
                    onChange={(e) => setSelectedCity(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">请选择城市</option>
                    {cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    当前选择: {selectedCity || '未选择'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    商品分类
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">请选择分类</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    当前选择: {selectedCategory || '未选择'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    商品成色
                  </label>
                  <select
                    value={selectedCondition}
                    onChange={(e) => setSelectedCondition(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">请选择成色</option>
                    {conditions.map(condition => (
                      <option key={condition} value={condition}>{condition}</option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    当前选择: {selectedCondition || '未选择'}
                  </p>
                </div>
              </div>
            </div>

            {/* 样式测试 */}
            <div>
              <h2 className="text-lg font-semibold mb-4">不同样式的选择框</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    小尺寸选择框
                  </label>
                  <select className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                    <option value="">小尺寸选择框</option>
                    <option value="option1">选项 1</option>
                    <option value="option2">选项 2</option>
                    <option value="option3">选项 3</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    大尺寸选择框
                  </label>
                  <select className="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    <option value="">大尺寸选择框</option>
                    <option value="option1">选项 1</option>
                    <option value="option2">选项 2</option>
                    <option value="option3">选项 3</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 禁用状态测试 */}
            <div>
              <h2 className="text-lg font-semibold mb-4">禁用状态测试</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    正常选择框
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">正常状态</option>
                    <option value="option1">可以选择</option>
                    <option value="option2">可以选择</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    禁用选择框
                  </label>
                  <select 
                    disabled 
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">禁用状态</option>
                    <option value="option1">无法选择</option>
                    <option value="option2">无法选择</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 多选测试 */}
            <div>
              <h2 className="text-lg font-semibold mb-4">多选选择框测试</h2>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  多选城市 (按住Ctrl/Cmd可多选)
                </label>
                <select 
                  multiple 
                  size={5}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  {cities.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* 测试说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">测试说明</h3>
              <ul className="text-blue-700 space-y-1">
                <li>• 检查选择框是否能正确显示选项文本</li>
                <li>• 检查点击选择框是否能正常展开选项列表</li>
                <li>• 检查选择选项后是否能正确显示选中的值</li>
                <li>• 检查不同尺寸的选择框是否都能正常工作</li>
                <li>• 检查禁用状态是否正确显示</li>
                <li>• 检查多选选择框是否能正常工作</li>
              </ul>
            </div>

            {/* 调试信息 */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">调试信息</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>用户代理: {typeof window !== 'undefined' ? window.navigator.userAgent : '服务端渲染'}</p>
                <p>当前选择状态:</p>
                <ul className="ml-4 space-y-1">
                  <li>城市: {selectedCity || '未选择'}</li>
                  <li>分类: {selectedCategory || '未选择'}</li>
                  <li>成色: {selectedCondition || '未选择'}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
