'use client'

import { useState } from 'react'
import PaymentMethodSelector, { type PaymentMethod } from '@/components/payment/PaymentMethodSelector'
import BinancePayment from '@/components/payment/BinancePayment'
import BNBChainPayment from '@/components/payment/BNBChainPayment'

export default function TestPaymentPage() {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [currentStep, setCurrentStep] = useState<'select' | 'payment'>('select')

  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method)
  }

  const handleProceedToPayment = () => {
    setCurrentStep('payment')
  }

  const handlePaymentComplete = (txHash: string) => {
    alert(`支付完成！交易哈希: ${txHash}`)
    setCurrentStep('select')
    setSelectedMethod(null)
  }

  const handlePaymentCancel = () => {
    setCurrentStep('select')
    setSelectedMethod(null)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">支付系统测试</h1>
        
        {currentStep === 'select' && (
          <div className="bg-white rounded-lg shadow p-6">
            <PaymentMethodSelector
              selectedMethod={selectedMethod}
              onMethodSelect={handleMethodSelect}
              amount={100}
              currency="USDT"
            />
            
            <div className="mt-8 flex justify-end">
              <button
                onClick={handleProceedToPayment}
                disabled={!selectedMethod}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-6 py-3 rounded-md font-medium"
              >
                确认支付方式
              </button>
            </div>
          </div>
        )}

        {currentStep === 'payment' && selectedMethod && (
          <div className="bg-white rounded-lg shadow p-6">
            {selectedMethod.id === 'binance_pay' && (
              <BinancePayment
                amount={100}
                currency="USDT"
                orderId="test-order-123"
                onPaymentComplete={handlePaymentComplete}
                onCancel={handlePaymentCancel}
              />
            )}

            {selectedMethod.id === 'bnb_chain' && (
              <BNBChainPayment
                amount={100}
                currency="USDT"
                orderId="test-order-123"
                onPaymentComplete={handlePaymentComplete}
                onCancel={handlePaymentCancel}
              />
            )}
          </div>
        )}
      </div>
    </div>
  )
}
