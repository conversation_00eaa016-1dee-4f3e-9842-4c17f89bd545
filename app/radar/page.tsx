'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Navbar from '@/components/Navbar'

interface Feedback {
  id: string
  category: string
  title: string
  description: string
  status: string
  priority: string
  adminResponse?: string
  createdAt: string
  updatedAt: string
  resolvedAt?: string
}

interface FeedbackFormData {
  category: 'BUG_REPORT' | 'IMPROVEMENT' | 'APPEAL' | 'ESCROW_DISPUTE'
  title: string
  description: string
  contactEmail: string
  contactPhone: string
  orderId?: string // 托管争议需要的订单ID
  disputeReason?: string // 争议原因
}

const FEEDBACK_CATEGORIES = {
  BUG_REPORT: {
    label: '平台bug反馈',
    description: '报告技术问题、错误或故障',
    icon: '🐛',
    color: 'bg-red-50 border-red-200 text-red-800'
  },
  IMPROVEMENT: {
    label: '平台改进建议',
    description: '建议新功能、UI/UX改进或平台增强',
    icon: '💡',
    color: 'bg-blue-50 border-blue-200 text-blue-800'
  },
  APPEAL: {
    label: '平台申诉',
    description: '提交正式投诉、争议解决或申诉',
    icon: '⚖️',
    color: 'bg-yellow-50 border-yellow-200 text-yellow-800'
  },
  ESCROW_DISPUTE: {
    label: '托管争议举报',
    description: '针对托管订单的交易争议申请仲裁',
    icon: '🛡️',
    color: 'bg-purple-50 border-purple-200 text-purple-800'
  }
}

const DISPUTE_REASONS = [
  { value: 'PRODUCT_MISMATCH', label: '商品与描述不符' },
  { value: 'QUALITY_ISSUE', label: '商品质量问题' },
  { value: 'NO_DELIVERY', label: '未收到商品' },
  { value: 'FAKE_PRODUCT', label: '假冒商品' },
  { value: 'PAYMENT_ISSUE', label: '支付问题' },
  { value: 'COMMUNICATION_ISSUE', label: '沟通问题' },
  { value: 'FRAUD', label: '欺诈行为' },
  { value: 'SCAM', label: '诈骗' },
  { value: 'SAFETY_CONCERN', label: '安全问题' },
  { value: 'ILLEGAL_ACTIVITY', label: '违法活动' },
  { value: 'OTHER', label: '其他' }
]

const STATUS_CONFIG = {
  PENDING: { label: '待处理', color: 'bg-gray-100 text-gray-800' },
  IN_PROGRESS: { label: '处理中', color: 'bg-blue-100 text-blue-800' },
  RESOLVED: { label: '已解决', color: 'bg-green-100 text-green-800' },
  CLOSED: { label: '已关闭', color: 'bg-gray-100 text-gray-600' },
  VOTING: { label: '投票中', color: 'bg-purple-100 text-purple-800' },
  UNDER_REVIEW: { label: '审核中', color: 'bg-yellow-100 text-yellow-800' }
}

export default function RadarPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'submit' | 'history' | 'dispute'>('submit')

  // 托管争议相关状态
  const [disputeForm, setDisputeForm] = useState({
    escrowOrderId: '',
    orderNumber: '',
    reason: '',
    description: '',
    reportedUserId: ''
  })
  const [escrowOrders, setEscrowOrders] = useState([])
  const [loadingOrders, setLoadingOrders] = useState(false)
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null)
  const [successMessage, setSuccessMessage] = useState('')
  
  // 表单状态
  const [step, setStep] = useState<'category' | 'form'>('category')
  const [formData, setFormData] = useState<FeedbackFormData>({
    category: 'BUG_REPORT',
    title: '',
    description: '',
    contactEmail: '',
    contactPhone: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 检查登录状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  // 获取反馈历史
  const fetchFeedbacks = async () => {
    if (activeTab !== 'history') return
    
    try {
      setLoading(true)
      const response = await fetch('/api/feedback')
      if (response.ok) {
        const data = await response.json()
        setFeedbacks(data.feedbacks)
      }
    } catch (error) {
      console.error('获取反馈历史失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取用户的托管订单
  const fetchEscrowOrders = async () => {
    setLoadingOrders(true)
    try {
      const response = await fetch('/api/user/escrow-orders')
      const data = await response.json()

      if (data.success) {
        setEscrowOrders(data.data.orders || [])
      }
    } catch (error) {
      console.error('获取托管订单失败:', error)
    } finally {
      setLoadingOrders(false)
    }
  }

  useEffect(() => {
    if (activeTab === 'history') {
      fetchFeedbacks()
    } else if (activeTab === 'dispute') {
      fetchEscrowOrders()
    }
  }, [activeTab])

  // 表单处理
  const handleCategorySelect = (category: FeedbackFormData['category']) => {
    setFormData(prev => ({ ...prev, category }))
    setStep('form')
  }

  const handleInputChange = (field: keyof FeedbackFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = '请输入反馈标题'
    } else if (formData.title.length > 200) {
      newErrors.title = '标题不能超过200个字符'
    }

    if (!formData.description.trim()) {
      newErrors.description = '请输入详细描述'
    } else if (formData.description.length < 10) {
      newErrors.description = '描述至少需要10个字符'
    } else if (formData.description.length > 2000) {
      newErrors.description = '描述不能超过2000个字符'
    }

    if (formData.contactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = '请输入有效的邮箱地址'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // 准备提交数据，处理空字符串
      const submitData = {
        category: formData.category,
        title: formData.title.trim(),
        description: formData.description.trim(),
        contactEmail: formData.contactEmail.trim() || undefined,
        contactPhone: formData.contactPhone.trim() || undefined
      }

      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        setSuccessMessage('反馈提交成功！我们会尽快处理您的反馈。')
        setFormData({
          category: 'BUG_REPORT',
          title: '',
          description: '',
          contactEmail: '',
          contactPhone: ''
        })
        setStep('category')
        setTimeout(() => setSuccessMessage(''), 5000)
      } else {
        const data = await response.json()

        // 处理验证错误
        if (data.details && Array.isArray(data.details)) {
          const validationErrors: Record<string, string> = {}
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              const field = error.path[0]
              validationErrors[field] = error.message
            }
          })
          setErrors(validationErrors)
        } else {
          setErrors({ submit: data.error || '提交失败，请稍后重试' })
        }
      }
    } catch (error) {
      setErrors({ submit: '网络错误，请稍后重试' })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 提交争议举报
  const handleDisputeSubmit = async () => {
    if (!disputeForm.escrowOrderId || !disputeForm.reason || !disputeForm.description.trim()) {
      setErrors({ submit: '请填写完整的争议信息' })
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      const response = await fetch('/api/radar/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          escrowOrderId: disputeForm.escrowOrderId,
          reason: disputeForm.reason,
          description: disputeForm.description.trim(),
          reportedUserId: disputeForm.reportedUserId
        }),
      })

      if (response.ok) {
        setSuccessMessage('争议举报提交成功！管理员将尽快处理。')
        setDisputeForm({
          escrowOrderId: '',
          orderNumber: '',
          reason: '',
          description: '',
          reportedUserId: ''
        })
        setTimeout(() => setSuccessMessage(''), 5000)
      } else {
        const data = await response.json()
        setErrors({ submit: data.error || '提交失败，请稍后重试' })
      }
    } catch (error) {
      setErrors({ submit: '网络错误，请稍后重试' })
    } finally {
      setIsSubmitting(false)
    }
  }



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">加载中...</div>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const selectedCategory = FEEDBACK_CATEGORIES[formData.category]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">反馈助手</h1>
          <p className="mt-2 text-gray-600">
            我们重视您的每一个反馈，帮助我们不断改进BitMarket平台
          </p>
        </div>

        {/* 成功消息 */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{successMessage}</p>
          </div>
        )}

        {/* 标签页导航 */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('submit')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'submit'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              提交反馈
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              反馈历史
            </button>
            <button
              onClick={() => setActiveTab('dispute')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'dispute'
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              争议举报
            </button>
          </nav>
        </div>

        {/* 内容区域 */}
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'submit' ? (
            /* 提交反馈标签页 */
            <div className="p-6">
              {step === 'category' ? (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">选择反馈类型</h2>
                  <p className="text-gray-600">
                    请选择您要反馈的类型，我们会根据不同类型为您提供相应的处理流程：
                  </p>
                  
                  <div className="space-y-4">
                    {Object.entries(FEEDBACK_CATEGORIES).map(([key, category]) => (
                      <button
                        key={key}
                        onClick={() => handleCategorySelect(key as FeedbackFormData['category'])}
                        className={`w-full p-4 rounded-lg border-2 text-left transition-all hover:shadow-md ${category.color}`}
                      >
                        <div className="flex items-start space-x-3">
                          <span className="text-2xl">{category.icon}</span>
                          <div>
                            <h3 className="font-medium text-lg">{category.label}</h3>
                            <p className="text-sm opacity-80 mt-1">{category.description}</p>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* 返回按钮 */}
                  <button
                    onClick={() => setStep('category')}
                    className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    返回选择类型
                  </button>

                  {/* 选中的类型显示 */}
                  <div className={`p-4 rounded-lg border ${selectedCategory.color}`}>
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">{selectedCategory.icon}</span>
                      <div>
                        <h3 className="font-medium">{selectedCategory.label}</h3>
                        <p className="text-sm opacity-80">{selectedCategory.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* 表单 */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        反馈标题 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请简要描述您的问题或建议"
                        maxLength={200}
                      />
                      {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
                      <p className="mt-1 text-xs text-gray-500">{formData.title.length}/200</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        详细描述 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请详细描述您遇到的问题、建议的改进或申诉的具体情况..."
                        maxLength={2000}
                      />
                      {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                      <p className="mt-1 text-xs text-gray-500">{formData.description.length}/2000</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          联系邮箱
                        </label>
                        <input
                          type="email"
                          value={formData.contactEmail}
                          onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          placeholder="可选，用于接收处理结果"
                        />
                        {errors.contactEmail && <p className="mt-1 text-sm text-red-600">{errors.contactEmail}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          联系电话
                        </label>
                        <input
                          type="tel"
                          value={formData.contactPhone}
                          onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          placeholder="可选，紧急情况联系"
                        />
                      </div>
                    </div>

                    {errors.submit && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-600">{errors.submit}</p>
                      </div>
                    )}

                    {/* 提交按钮 */}
                    <div className="flex justify-end space-x-3 pt-4 border-t">
                      <button
                        onClick={() => setStep('category')}
                        className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                      >
                        取消
                      </button>
                      <button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? '提交中...' : '提交反馈'}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : activeTab === 'history' ? (
            /* 反馈历史标签页 */
            <div className="p-6">
              {selectedFeedback ? (
                /* 详情视图 */
                <div className="space-y-6">
                  <button
                    onClick={() => setSelectedFeedback(null)}
                    className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    返回列表
                  </button>

                  {/* 反馈基本信息 */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${FEEDBACK_CATEGORIES[selectedFeedback.category as keyof typeof FEEDBACK_CATEGORIES]?.color}`}>
                          {FEEDBACK_CATEGORIES[selectedFeedback.category as keyof typeof FEEDBACK_CATEGORIES]?.icon} {FEEDBACK_CATEGORIES[selectedFeedback.category as keyof typeof FEEDBACK_CATEGORIES]?.label}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${STATUS_CONFIG[selectedFeedback.status as keyof typeof STATUS_CONFIG]?.color}`}>
                          {STATUS_CONFIG[selectedFeedback.status as keyof typeof STATUS_CONFIG]?.label}
                        </span>
                      </div>
                    </div>

                    <h3 className="text-lg font-medium text-gray-900 mb-2">{selectedFeedback.title}</h3>
                    <p className="text-sm text-gray-600">提交时间: {formatDate(selectedFeedback.createdAt)}</p>
                    {selectedFeedback.resolvedAt && (
                      <p className="text-sm text-gray-600">解决时间: {formatDate(selectedFeedback.resolvedAt)}</p>
                    )}
                  </div>

                  {/* 详细描述 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">详细描述</h4>
                    <div className="bg-white border rounded-lg p-4">
                      <p className="text-gray-700 whitespace-pre-wrap">{selectedFeedback.description}</p>
                    </div>
                  </div>

                  {/* 管理员回复 */}
                  {selectedFeedback.adminResponse && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">管理员回复</h4>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p className="text-gray-700 whitespace-pre-wrap">{selectedFeedback.adminResponse}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* 列表视图 */
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">我的反馈历史</h2>

                  {loading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-gray-500">加载中...</div>
                    </div>
                  ) : feedbacks.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 text-6xl mb-4">📝</div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无反馈记录</h3>
                      <p className="text-gray-600 mb-4">您还没有提交过任何反馈</p>
                      <button
                        onClick={() => setActiveTab('submit')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        提交第一个反馈
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {feedbacks.map((feedback) => (
                        <div
                          key={feedback.id}
                          onClick={() => setSelectedFeedback(feedback)}
                          className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <span className={`px-2 py-1 rounded text-xs font-medium ${FEEDBACK_CATEGORIES[feedback.category as keyof typeof FEEDBACK_CATEGORIES]?.color}`}>
                                  {FEEDBACK_CATEGORIES[feedback.category as keyof typeof FEEDBACK_CATEGORIES]?.icon} {FEEDBACK_CATEGORIES[feedback.category as keyof typeof FEEDBACK_CATEGORIES]?.label}
                                </span>
                                <span className={`px-2 py-1 rounded text-xs font-medium ${STATUS_CONFIG[feedback.status as keyof typeof STATUS_CONFIG]?.color}`}>
                                  {STATUS_CONFIG[feedback.status as keyof typeof STATUS_CONFIG]?.label}
                                </span>
                              </div>
                              <h3 className="font-medium text-gray-900 mb-1">{feedback.title}</h3>
                              <p className="text-sm text-gray-600 line-clamp-2">{feedback.description}</p>
                              <p className="text-xs text-gray-500 mt-2">{formatDate(feedback.createdAt)}</p>
                            </div>
                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : activeTab === 'dispute' ? (
            /* 争议举报标签页 */
            <div className="p-6">
              <div className="space-y-6">
                <div className="border-l-4 border-red-500 bg-red-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        托管争议举报
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>如果您在托管交易中遇到问题，可以通过此功能向管理员举报。请提供详细的问题描述和相关证据。</p>
                      </div>
                    </div>
                  </div>
                </div>

                {loadingOrders ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">加载托管订单...</p>
                  </div>
                ) : escrowOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无托管订单</h3>
                    <p className="text-gray-500">您还没有参与任何托管交易</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">选择要举报的托管订单</h3>
                    {escrowOrders.map((order: any) => (
                      <div key={order.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-medium text-gray-900">{order.order.orderNumber}</h4>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                order.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                order.status === 'DISPUTED' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {order.status}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                order.userRole === 'buyer' ? 'bg-blue-100 text-blue-800' :
                                order.userRole === 'seller' ? 'bg-green-100 text-green-800' :
                                'bg-purple-100 text-purple-800'
                              }`}>
                                {order.userRole === 'buyer' ? '买家' : order.userRole === 'seller' ? '卖家' : '中间人'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{order.order.product.title}</p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>金额: {order.amount} USDT</span>
                              <span>创建: {new Date(order.createdAt).toLocaleDateString()}</span>
                            </div>
                          </div>
                          <button
                            onClick={() => {
                              setDisputeForm({
                                ...disputeForm,
                                escrowOrderId: order.id,
                                orderNumber: order.order.orderNumber
                              })
                              // 滚动到表单区域
                              document.getElementById('dispute-form')?.scrollIntoView({ behavior: 'smooth' })
                            }}
                            className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                          >
                            举报此订单
                          </button>
                        </div>
                      </div>
                    ))}

                    {/* 争议举报表单 */}
                    {disputeForm.escrowOrderId && (
                      <div id="dispute-form" className="mt-8 border-t pt-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">提交争议举报</h3>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              选中的订单
                            </label>
                            <div className="p-3 bg-gray-50 rounded border">
                              {disputeForm.orderNumber}
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              举报原因 *
                            </label>
                            <select
                              value={disputeForm.reason}
                              onChange={(e) => setDisputeForm({ ...disputeForm, reason: e.target.value })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                              required
                            >
                              <option value="">请选择举报原因</option>
                              {DISPUTE_REASONS.map((reason) => (
                                <option key={reason.value} value={reason.value}>
                                  {reason.label}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              详细描述 *
                            </label>
                            <textarea
                              value={disputeForm.description}
                              onChange={(e) => setDisputeForm({ ...disputeForm, description: e.target.value })}
                              placeholder="请详细描述遇到的问题，包括时间、经过、损失等..."
                              rows={6}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                              required
                            />
                          </div>

                          {errors.submit && (
                            <div className="text-red-600 text-sm">{errors.submit}</div>
                          )}

                          {successMessage && (
                            <div className="text-green-600 text-sm">{successMessage}</div>
                          )}

                          <div className="flex space-x-4">
                            <button
                              onClick={() => setDisputeForm({
                                escrowOrderId: '',
                                orderNumber: '',
                                reason: '',
                                description: '',
                                reportedUserId: ''
                              })}
                              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                            >
                              取消
                            </button>
                            <button
                              onClick={handleDisputeSubmit}
                              disabled={isSubmitting || !disputeForm.reason || !disputeForm.description.trim()}
                              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isSubmitting ? '提交中...' : '提交举报'}
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )
}
