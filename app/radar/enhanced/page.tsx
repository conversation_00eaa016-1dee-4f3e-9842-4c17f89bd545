"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  ExclamationTriangleIcon,
  DocumentArrowUpIcon,
  PhotoIcon,
  ChatBubbleLeftRightIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline'
import Navbar from '@/components/Navbar'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  useEscrow: boolean
  product: {
    title: string
    images: string[]
  }
  seller: {
    name: string
  }
  buyer: {
    name: string
  }
  mediator?: {
    name: string
  }
}

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  url: string
}

interface DisputeHistory {
  id: string
  disputeType: string
  description: string
  status: string
  createdAt: string
  order: {
    orderNumber: string
    product: {
      title: string
    }
  }
}

export default function EnhancedRadarPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [activeTab, setActiveTab] = useState<'report' | 'history'>('report')
  const [orders, setOrders] = useState<Order[]>([])
  const [disputeHistory, setDisputeHistory] = useState<DisputeHistory[]>([])
  const [selectedOrder, setSelectedOrder] = useState<string>('')
  const [disputeType, setDisputeType] = useState('')
  const [description, setDescription] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const disputeTypes = [
    { value: 'PRODUCT_QUALITY', label: '商品质量问题', description: '收到的商品与描述不符、有质量问题等' },
    { value: 'DELIVERY_ISSUE', label: '物流配送问题', description: '未收到商品、配送延迟、包装损坏等' },
    { value: 'SELLER_FRAUD', label: '卖家欺诈', description: '卖家虚假宣传、不发货、发错货等' },
    { value: 'BUYER_FRAUD', label: '买家恶意', description: '买家恶意退款、虚假投诉等' },
    { value: 'PAYMENT_ISSUE', label: '支付问题', description: '支付异常、资金未到账等' },
    { value: 'COMMUNICATION_ISSUE', label: '沟通问题', description: '对方不回复消息、态度恶劣等' },
    { value: 'MEDIATOR_ISSUE', label: '中间人问题', description: '中间人处理不当、偏袒一方等' },
    { value: 'PLATFORM_ISSUE', label: '平台问题', description: '平台功能异常、规则不合理等' },
    { value: 'OTHER', label: '其他问题', description: '其他未分类的争议问题' }
  ]

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    fetchUserOrders()
    fetchDisputeHistory()
  }, [session, status])

  const fetchUserOrders = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/orders?includeEscrow=true&includeActive=true')
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setOrders(result.data.orders)
        } else {
          setError(result.error || '获取订单列表失败')
        }
      } else {
        setError('获取订单列表失败')
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const fetchDisputeHistory = async () => {
    try {
      const response = await fetch('/api/dispute/history')
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setDisputeHistory(result.data.disputes)
        }
      }
    } catch (error) {
      console.error('获取争议历史失败:', error)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const formData = new FormData()
    Array.from(files).forEach(file => {
      formData.append('files', file)
    })

    try {
      const response = await fetch('/api/dispute/upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setUploadedFiles(prev => [...prev, ...result.data.files])
      } else {
        setError(result.error || '文件上传失败')
      }
    } catch (error) {
      console.error('文件上传失败:', error)
      setError('文件上传失败，请稍后重试')
    }
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId))
  }

  const submitDispute = async () => {
    if (!selectedOrder || !disputeType || !description.trim()) {
      setError('请填写所有必填字段')
      return
    }

    if (description.trim().length < 50) {
      setError('问题描述至少需要50个字符')
      return
    }

    try {
      setSubmitting(true)
      setError('')

      const response = await fetch('/api/dispute/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          orderId: selectedOrder,
          disputeType,
          description: description.trim(),
          evidence: uploadedFiles.map(file => ({
            id: file.id,
            name: file.name,
            url: file.url,
            type: file.type,
            size: file.size
          }))
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess('争议举报已提交，我们会在24小时内处理')
        // 重置表单
        setSelectedOrder('')
        setDisputeType('')
        setDescription('')
        setUploadedFiles([])
        // 刷新争议历史
        fetchDisputeHistory()
      } else {
        setError(result.error || '提交争议举报失败')
      }
    } catch (error) {
      console.error('提交争议举报失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  const exportChatHistory = async (orderId: string) => {
    try {
      // 首先获取聊天室ID
      const chatRoomResponse = await fetch(`/api/orders/${orderId}/chatroom`)
      
      if (chatRoomResponse.ok) {
        const chatRoomResult = await chatRoomResponse.json()
        if (chatRoomResult.success && chatRoomResult.data.chatRoomId) {
          // 导出聊天记录
          const exportResponse = await fetch(`/api/escrow/chat/${chatRoomResult.data.chatRoomId}/export`, {
            method: 'POST'
          })

          if (exportResponse.ok) {
            const blob = await exportResponse.blob()
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.style.display = 'none'
            a.href = url
            a.download = `chat-history-${orderId}-${new Date().toISOString().split('T')[0]}.pdf`
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
          } else {
            const result = await exportResponse.json()
            alert(result.error || '导出聊天记录失败')
          }
        } else {
          alert('该订单没有聊天记录')
        }
      } else {
        alert('获取聊天室信息失败')
      }
    } catch (error) {
      console.error('导出聊天记录失败:', error)
      alert('导出聊天记录失败，请稍后重试')
    }
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <PhotoIcon className="h-5 w-5 text-blue-500" />
    }
    return <DocumentArrowUpIcon className="h-5 w-5 text-gray-500" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'INVESTIGATING':
        return 'bg-blue-100 text-blue-800'
      case 'RESOLVED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'INVESTIGATING': '调查中',
      'RESOLVED': '已解决',
      'REJECTED': '已拒绝'
    }
    return statusMap[status] || status
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">争议举报中心</h1>
              <p className="text-gray-600">提交交易争议、查看处理进度、导出聊天记录</p>
            </div>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'report', name: '提交举报', icon: ExclamationTriangleIcon },
              { id: 'history', name: '举报历史', icon: ClockIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as 'report' | 'history')}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* 提交举报标签页 */}
        {activeTab === 'report' && (
          <div className="space-y-6">
            {/* 说明信息 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex">
                <InformationCircleIcon className="h-6 w-6 text-blue-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">举报须知</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>请详细描述遇到的问题，并提供相关证据</li>
                      <li>支持上传图片、文档等证据文件，单个文件最大10MB</li>
                      <li>可以导出聊天记录作为证据</li>
                      <li>我们会在24小时内处理您的举报</li>
                      <li>恶意举报将被记录并可能影响您的信誉</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 成功提示 */}
            {success && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <CheckCircleIcon className="h-5 w-5 text-green-400" />
                  <div className="ml-3">
                    <p className="text-sm text-green-700">{success}</p>
                    <button
                      onClick={() => setSuccess('')}
                      className="mt-2 text-sm text-green-600 hover:text-green-800 underline"
                    >
                      关闭
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                    <button
                      onClick={() => setError('')}
                      className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                    >
                      关闭
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 举报表单 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">提交争议举报</h2>
              
              <div className="space-y-6">
                {/* 选择订单 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择相关订单 *
                  </label>
                  {loading ? (
                    <div className="animate-pulse h-10 bg-gray-200 rounded"></div>
                  ) : (
                    <select
                      value={selectedOrder}
                      onChange={(e) => setSelectedOrder(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    >
                      <option value="">请选择订单</option>
                      {orders.map((order) => (
                        <option key={order.id} value={order.id}>
                          {order.orderNumber} - {order.product.title} ({order.totalAmount} USDT)
                          {order.useEscrow && ' [托管订单]'}
                        </option>
                      ))}
                    </select>
                  )}
                  
                  {/* 订单详情和聊天记录导出 */}
                  {selectedOrder && (
                    <div className="mt-3 p-4 bg-gray-50 rounded-md">
                      {(() => {
                        const order = orders.find(o => o.id === selectedOrder)
                        if (!order) return null
                        
                        return (
                          <div>
                            <div className="flex items-center justify-between mb-3">
                              <div className="text-sm text-gray-600">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <p className="flex items-center">
                                      <UserIcon className="h-4 w-4 mr-1" />
                                      买家: {order.buyer.name}
                                    </p>
                                    <p className="flex items-center">
                                      <UserIcon className="h-4 w-4 mr-1" />
                                      卖家: {order.seller.name}
                                    </p>
                                  </div>
                                  <div>
                                    {order.mediator && (
                                      <p className="flex items-center">
                                        <ShieldCheckIcon className="h-4 w-4 mr-1" />
                                        中间人: {order.mediator.name}
                                      </p>
                                    )}
                                    <p className="text-xs text-gray-500">
                                      订单状态: {order.status}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => exportChatHistory(order.id)}
                              className="w-full flex items-center justify-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50"
                            >
                              <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                              导出聊天记录作为证据
                            </button>
                          </div>
                        )
                      })()}
                    </div>
                  )}
                </div>

                {/* 争议类型 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    争议类型 *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {disputeTypes.map((type) => (
                      <label
                        key={type.value}
                        className={`flex items-start p-3 border rounded-lg cursor-pointer transition-colors ${
                          disputeType === type.value
                            ? 'border-red-500 bg-red-50'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <input
                          type="radio"
                          name="disputeType"
                          value={type.value}
                          checked={disputeType === type.value}
                          onChange={(e) => setDisputeType(e.target.value)}
                          className="mt-1 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                        />
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{type.label}</div>
                          <div className="text-xs text-gray-500">{type.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* 问题描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    问题描述 * (至少50个字符)
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="请详细描述您遇到的问题，包括：&#10;1. 具体发生了什么问题&#10;2. 问题发生的时间&#10;3. 涉及的金额或商品&#10;4. 您希望如何解决&#10;5. 其他相关信息"
                  />
                  <div className="mt-1 flex justify-between text-sm text-gray-500">
                    <span>详细的描述有助于我们更好地处理您的问题</span>
                    <span className={description.length < 50 ? 'text-red-500' : 'text-green-500'}>
                      {description.length}/50
                    </span>
                  </div>
                </div>

                {/* 证据上传 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    上传证据文件 (可选)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <div className="text-center">
                      <DocumentArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-4">
                        <label htmlFor="file-upload" className="cursor-pointer">
                          <span className="mt-2 block text-sm font-medium text-gray-900">
                            点击上传文件
                          </span>
                          <span className="mt-1 block text-sm text-gray-500">
                            支持图片、PDF、Word文档等，单个文件最大10MB
                          </span>
                        </label>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          multiple
                          accept="image/*,.pdf,.doc,.docx,.txt"
                          className="sr-only"
                          onChange={handleFileUpload}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 已上传文件列表 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <h4 className="text-sm font-medium text-gray-700">已上传文件:</h4>
                      {uploadedFiles.map((file) => (
                        <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                          <div className="flex items-center">
                            {getFileIcon(file.type)}
                            <div className="ml-3">
                              <p className="text-sm font-medium text-gray-900">{file.name}</p>
                              <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                            </div>
                          </div>
                          <button
                            onClick={() => removeFile(file.id)}
                            className="text-red-400 hover:text-red-600"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 提交按钮 */}
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => router.back()}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    onClick={submitDispute}
                    disabled={submitting || !selectedOrder || !disputeType || !description.trim() || description.length < 50}
                    className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {submitting ? '提交中...' : '提交举报'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 举报历史标签页 */}
        {activeTab === 'history' && (
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">我的举报历史</h2>
            </div>
            
            <div className="divide-y divide-gray-200">
              {disputeHistory.length > 0 ? (
                disputeHistory.map((dispute) => (
                  <div key={dispute.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h3 className="text-sm font-medium text-gray-900">
                            {disputeTypes.find(t => t.value === dispute.disputeType)?.label || dispute.disputeType}
                          </h3>
                          <span className={`ml-3 px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(dispute.status)}`}>
                            {getStatusText(dispute.status)}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-600">
                          订单: {dispute.order.orderNumber} - {dispute.order.product.title}
                        </p>
                        <p className="mt-2 text-sm text-gray-700 line-clamp-2">
                          {dispute.description}
                        </p>
                      </div>
                      <div className="ml-4 text-right">
                        <p className="text-sm text-gray-500">
                          {new Date(dispute.createdAt).toLocaleDateString('zh-CN')}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-12 text-center">
                  <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="mt-2 text-sm text-gray-500">暂无举报记录</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
