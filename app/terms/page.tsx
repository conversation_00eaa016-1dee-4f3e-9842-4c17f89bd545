// 静态生成配置 - 构建时预编译
export const dynamic = 'force-static'
export const revalidate = false

import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'BitMarket - 服务条款',
  description: 'BitMarket服务条款，了解使用我们平台的规则和条件',
}

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          {/* 返回按钮 */}
          <div className="mb-8">
            <a
              href="/about"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              返回关于
            </a>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-6">服务条款</h1>
          <p className="text-gray-600 mb-8">
            最后更新时间：2025年7月19日
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. 接受条款</h2>
              <p className="text-gray-600 leading-relaxed">
                欢迎使用BitMarket平台。通过访问或使用我们的服务，您同意受本服务条款的约束。
                如果您不同意这些条款，将无法使用我们的服务。
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. 服务描述</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                BitMarket是一个C2C交易平台，提供以下服务：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>买卖交易</li>
                <li>钱包服务</li>
                <li>中间人担保服务</li>
                <li>用户账户管理</li>
                <li>客户支持服务</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. 用户资格</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                使用我们的服务，您必须：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>年满18周岁</li>
                <li>具有完全民事行为能力</li>
                <li>遵守所在司法管辖区的法律法规</li>
                <li>提供真实、准确的身份信息</li>
                <li>完成必要的身份验证程序</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. 账户安全</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                您有责任：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>保护您的账户密码和私钥</li>
                <li>启用双重身份验证</li>
                <li>及时报告任何可疑活动</li>
                <li>定期更新安全设置</li>
                <li>不与他人共享账户信息</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. 风险披露</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                交易存在以下风险：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>价格波动风险</li>
                <li>技术风险</li>
                <li>监管风险</li>
                <li>流动性风险</li>
                <li>网络安全风险</li>
              </ul>
              <p className="text-gray-600 leading-relaxed mt-4">
                请在充分了解风险的基础上进行交易，禁止进行灰色产业交易。
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. 联系我们</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                如果您对这些服务条款有任何疑问，请联系我们：
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-600">
                  <strong>邮箱：</strong> <EMAIL><br />
                  <strong>地址：</strong> 北京市朝阳区<br />
                  <strong>电话：</strong> 暂无
                </p>
              </div>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              本服务条款受中华人民共和国法律管辖
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
