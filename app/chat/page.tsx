'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'

interface ChatConversation {
  orderId: string
  orderNumber: string
  product: {
    id: string
    title: string
    price: number
  }
  otherParty: {
    id: string
    name: string | null
    email: string | null
  }
  lastMessage: {
    id: string
    content: string
    createdAt: string
    senderId: string
  } | null
  unreadCount: number
  orderStatus: string
}

export default function ChatListPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [conversations, setConversations] = useState<ChatConversation[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    fetchConversations()
  }, [session])

  const fetchConversations = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/chat/conversations')
      if (response.ok) {
        const data = await response.json()
        setConversations(data.conversations)
      }
    } catch (error) {
      console.error('Failed to fetch conversations:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return '刚刚'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}天前`
    }
  }

  const getOrderStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING_PAYMENT': '待付款',
      'PAID': '已付款',
      'SHIPPED': '已发货',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消',
      'REFUND_REQUESTED': '申请退款中'
    }
    return statusMap[status] || status
  }

  const getOrderStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING_PAYMENT': 'text-yellow-600',
      'PAID': 'text-blue-600',
      'SHIPPED': 'text-purple-600',
      'COMPLETED': 'text-green-600',
      'CANCELLED': 'text-red-600',
      'REFUND_REQUESTED': 'text-orange-600'
    }
    return colorMap[status] || 'text-gray-600'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/orders" className="text-gray-700 hover:text-gray-900">
                我的订单
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">我的对话</h1>
            <p className="mt-2 text-gray-600">与买家和卖家的聊天记录</p>
          </div>

          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : conversations.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">暂无对话记录</div>
              <Link
                href="/orders"
                className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
              >
                查看我的订单
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {conversations.map((conversation) => (
                <Link
                  key={conversation.orderId}
                  href={`/chat/${conversation.orderId}`}
                  className="block bg-white rounded-lg shadow hover:shadow-md transition-shadow"
                >
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-gray-500 text-lg">👤</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            {conversation.otherParty.name || '匿名用户'}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {conversation.product.title}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${getOrderStatusColor(conversation.orderStatus)}`}>
                          {getOrderStatusText(conversation.orderStatus)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatUSDT(conversation.product.price)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        {conversation.lastMessage ? (
                          <div className="flex items-center space-x-2">
                            <p className="text-sm text-gray-600 truncate">
                              {conversation.lastMessage.senderId === session?.user?.id ? '我: ' : ''}
                              {conversation.lastMessage.content}
                            </p>
                            <span className="text-xs text-gray-400 whitespace-nowrap">
                              {formatTime(conversation.lastMessage.createdAt)}
                            </span>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">还没有消息</p>
                        )}
                      </div>
                      {conversation.unreadCount > 0 && (
                        <div className="ml-3">
                          <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-medium text-white bg-red-500 rounded-full">
                            {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="mt-3 text-xs text-gray-500">
                      订单号: {conversation.orderNumber}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
