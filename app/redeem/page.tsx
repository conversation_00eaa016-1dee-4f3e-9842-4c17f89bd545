"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  GiftIcon,
  TicketIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  ClockIcon,
  CurrencyDollarIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import Navbar from '@/components/Navbar'

interface RedeemHistory {
  id: string
  type: 'GIFT_CARD' | 'REDEMPTION_CODE'
  code: string
  title?: string
  value: number
  unit: string
  redeemedAt: string
  description?: string
}

export default function RedeemPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [activeTab, setActiveTab] = useState<'giftcard' | 'redemption'>('giftcard')
  const [code, setCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [verifying, setVerifying] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [codeInfo, setCodeInfo] = useState<any>(null)
  const [redeemHistory, setRedeemHistory] = useState<RedeemHistory[]>([])

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    fetchRedeemHistory()
  }, [session, status])

  const fetchRedeemHistory = async () => {
    try {
      const response = await fetch('/api/user/redeem-history')
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setRedeemHistory(result.data.history)
        }
      }
    } catch (error) {
      console.error('获取兑换历史失败:', error)
    }
  }

  const verifyCode = async () => {
    if (!code.trim()) {
      setError('请输入兑换码')
      return
    }

    // 验证格式（16位字母数字组合）
    const trimmedCode = code.trim()
    if (!/^[A-Z0-9]{16}$/i.test(trimmedCode)) {
      setError('兑换码格式不正确，应为16位字母数字组合')
      return
    }

    try {
      setVerifying(true)
      setError('')
      setCodeInfo(null)

      const endpoint = activeTab === 'giftcard' 
        ? `/api/giftcards/redeem?cardCode=${code.trim().toUpperCase()}`
        : `/api/redemption-codes/redeem?codeValue=${code.trim().toUpperCase()}`

      const response = await fetch(endpoint)
      const result = await response.json()

      if (response.ok && result.success) {
        setCodeInfo(result.data)
      } else {
        setError(result.error || '验证失败')
      }
    } catch (error) {
      console.error('验证兑换码失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setVerifying(false)
    }
  }

  const executeRedeem = async () => {
    if (!codeInfo) return

    try {
      setLoading(true)
      setError('')

      const endpoint = activeTab === 'giftcard' 
        ? '/api/giftcards/redeem'
        : '/api/redemption-codes/redeem'

      const body = activeTab === 'giftcard'
        ? { cardCode: code.trim().toUpperCase() }
        : { codeValue: code.trim().toUpperCase() }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess(result.message)
        setCode('')
        setCodeInfo(null)
        fetchRedeemHistory() // 刷新兑换历史
      } else {
        setError(result.error || '兑换失败')
      }
    } catch (error) {
      console.error('兑换失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const formatCode = (input: string) => {
    // 自动转换为大写并限制16位
    const formatted = input.toUpperCase().replace(/[^A-Z0-9]/g, '').slice(0, 16)
    setCode(formatted)

    // 清除之前的验证结果
    if (codeInfo) {
      setCodeInfo(null)
    }
    if (error) {
      setError('')
    }
  }

  const getRewardTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'CASH_CREDIT': '现金奖励',
      'PLATFORM_POINTS': '平台积分',
      'WITHDRAWAL_FEE_DISCOUNT': '提现手续费减免',
      'SHOPPING_VOUCHER': '购物券'
    }
    return typeMap[type] || type
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">兑换中心</h1>
          <p className="text-gray-600">兑换礼品卡和兑换券，获得丰厚奖励</p>
        </div>

        {/* 标签页导航 */}
        <div className="mb-8">
          <nav className="flex space-x-8 justify-center">
            {[
              { id: 'giftcard', name: '礼品卡兑换', icon: GiftIcon, description: '兑换礼品卡获得余额' },
              { id: 'redemption', name: '兑换券兑换', icon: TicketIcon, description: '兑换券获得各种奖励' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id as 'giftcard' | 'redemption')
                  setCode('')
                  setCodeInfo(null)
                  setError('')
                  setSuccess('')
                }}
                className={`flex flex-col items-center py-4 px-6 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-8 w-8 mb-2" />
                <span className="font-semibold">{tab.name}</span>
                <span className="text-xs text-gray-500 mt-1">{tab.description}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：兑换表单 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">
                {activeTab === 'giftcard' ? '礼品卡兑换' : '兑换券兑换'}
              </h2>

              {/* 成功提示 */}
              {success && (
                <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex">
                    <CheckCircleIcon className="h-5 w-5 text-green-400" />
                    <div className="ml-3">
                      <p className="text-sm text-green-700">{success}</p>
                      <button
                        onClick={() => setSuccess('')}
                        className="mt-2 text-sm text-green-600 hover:text-green-800 underline"
                      >
                        关闭
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* 错误提示 */}
              {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                    <div className="ml-3">
                      <p className="text-sm text-red-700">{error}</p>
                      <button
                        onClick={() => setError('')}
                        className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                      >
                        关闭
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* 兑换码输入 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {activeTab === 'giftcard' ? '礼品卡码' : '兑换券码'}
                  </label>
                  <div className="flex space-x-3">
                    <input
                      type="text"
                      value={code}
                      onChange={(e) => formatCode(e.target.value)}
                      placeholder={`请输入16位${activeTab === 'giftcard' ? '礼品卡码' : '兑换券码'}`}
                      className="flex-1 px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-lg tracking-wider"
                      maxLength={16}
                    />
                    <button
                      onClick={verifyCode}
                      disabled={verifying || !code.trim() || code.length !== 16}
                      className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {verifying ? '验证中...' : '验证'}
                    </button>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    {activeTab === 'giftcard'
                      ? '输入16位礼品卡码，例如：A1B2C3D4E5F6G7H8'
                      : '输入16位兑换券码，例如：X1Y2Z3A4B5C6D7E8'
                    }
                  </p>
                  <div className="text-sm text-gray-500 mt-1">
                    已输入: {code.length}/16
                  </div>
                </div>

                {/* 验证结果 */}
                {codeInfo && (
                  <div className="border border-green-200 bg-green-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-green-800 mb-3">验证成功</h3>
                    
                    {activeTab === 'giftcard' ? (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">礼品卡码:</span>
                          <span className="font-mono font-medium">{codeInfo.cardCode}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">面值:</span>
                          <span className="font-medium text-green-600">{codeInfo.faceValue} USDT</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">有效期:</span>
                          <span>{new Date(codeInfo.validUntil).toLocaleDateString('zh-CN')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">状态:</span>
                          <span className={codeInfo.canRedeem ? 'text-green-600' : 'text-red-600'}>
                            {codeInfo.statusMessage}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">兑换券:</span>
                          <span className="font-medium">{codeInfo.title}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">奖励类型:</span>
                          <span className="font-medium">{codeInfo.rewardTypeText}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">奖励价值:</span>
                          <span className="font-medium text-green-600">
                            {codeInfo.rewardValue} {codeInfo.rewardUnit}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">使用次数:</span>
                          <span>{codeInfo.usedCount}/{codeInfo.maxUses}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">有效期:</span>
                          <span>
                            {new Date(codeInfo.validFrom).toLocaleDateString('zh-CN')} - {new Date(codeInfo.validUntil).toLocaleDateString('zh-CN')}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">状态:</span>
                          <span className={codeInfo.canRedeem ? 'text-green-600' : 'text-red-600'}>
                            {codeInfo.statusMessage}
                          </span>
                        </div>
                        {codeInfo.description && (
                          <div className="mt-3 p-2 bg-white rounded border">
                            <p className="text-xs text-gray-600">{codeInfo.description}</p>
                          </div>
                        )}
                      </div>
                    )}

                    {codeInfo.canRedeem && (
                      <button
                        onClick={executeRedeem}
                        disabled={loading}
                        className="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                      >
                        {loading ? '兑换中...' : `确认兑换`}
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 使用说明 */}
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-blue-800">
                    {activeTab === 'giftcard' ? '礼品卡使用说明' : '兑换券使用说明'}
                  </h4>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      {activeTab === 'giftcard' ? (
                        <>
                          <li>输入16位礼品卡码进行兑换</li>
                          <li>兑换成功后余额将直接添加到您的账户</li>
                          <li>请确保在有效期内使用</li>
                          <li>每张礼品卡只能兑换一次</li>
                        </>
                      ) : (
                        <>
                          <li>输入16位兑换券码获得各种奖励</li>
                          <li>奖励类型包括现金、积分、优惠券等</li>
                          <li>部分兑换券可多次使用</li>
                          <li>请在有效期内使用兑换券</li>
                        </>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧：兑换历史 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">兑换历史</h3>
                <button
                  onClick={fetchRedeemHistory}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <ArrowPathIcon className="h-5 w-5" />
                </button>
              </div>

              {redeemHistory.length > 0 ? (
                <div className="space-y-3">
                  {redeemHistory.slice(0, 10).map((item) => (
                    <div key={item.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          {item.type === 'GIFT_CARD' ? (
                            <GiftIcon className="h-5 w-5 text-blue-500 mr-2" />
                          ) : (
                            <TicketIcon className="h-5 w-5 text-purple-500 mr-2" />
                          )}
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {item.title || (item.type === 'GIFT_CARD' ? '礼品卡' : '兑换券')}
                            </p>
                            <p className="text-xs text-gray-500 font-mono">{item.code}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-green-600">
                            +{item.value} {item.unit}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(item.redeemedAt).toLocaleDateString('zh-CN')}
                          </p>
                        </div>
                      </div>
                      {item.description && (
                        <p className="text-xs text-gray-600 mt-2">{item.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ClockIcon className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="mt-2 text-sm text-gray-500">暂无兑换记录</p>
                </div>
              )}
            </div>

            {/* 快速购买 */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center mb-3">
                <SparklesIcon className="h-6 w-6 mr-2" />
                <h3 className="text-lg font-semibold">购买礼品卡</h3>
              </div>
              <p className="text-sm text-blue-100 mb-4">
                使用余额购买礼品卡，送给朋友或自己使用
              </p>
              <button
                onClick={() => router.push('/giftcards/purchase')}
                className="w-full px-4 py-2 bg-white text-blue-600 rounded-md hover:bg-gray-100 font-medium"
              >
                立即购买
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
