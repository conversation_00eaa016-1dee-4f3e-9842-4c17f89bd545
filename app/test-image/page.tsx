'use client'

import { useState, useEffect } from 'react'

export default function TestImagePage() {
  const [imageStatus, setImageStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [imageInfo, setImageInfo] = useState<any>(null)

  const imagePath = '/uploads/product/product_cmd8d4wu70000v9rg9lf2wmwq_1752815939191_cxt0j0.png'

  useEffect(() => {
    // 测试图片是否可以加载
    const img = new Image()
    img.onload = () => {
      setImageStatus('success')
      setImageInfo({
        width: img.naturalWidth,
        height: img.naturalHeight,
        src: img.src
      })
    }
    img.onerror = () => {
      setImageStatus('error')
    }
    img.src = imagePath
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">图片显示测试</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <h2 className="text-xl font-semibold mb-4">图片路径测试</h2>
          <div className="space-y-4">
            <div><strong>图片路径:</strong> {imagePath}</div>
            <div><strong>完整URL:</strong> {`http://localhost:3000${imagePath}`}</div>
            <div><strong>加载状态:</strong> 
              <span className={`ml-2 px-2 py-1 rounded text-sm ${
                imageStatus === 'loading' ? 'bg-yellow-100 text-yellow-800' :
                imageStatus === 'success' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              }`}>
                {imageStatus === 'loading' ? '加载中...' :
                 imageStatus === 'success' ? '加载成功' : '加载失败'}
              </span>
            </div>
            {imageInfo && (
              <div><strong>图片信息:</strong> {imageInfo.width} x {imageInfo.height}px</div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <h2 className="text-xl font-semibold mb-4">图片显示测试</h2>
          
          {/* 方法1: 直接使用img标签 */}
          <div className="mb-6">
            <h3 className="font-medium mb-2">方法1: 直接img标签</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <img
                src={imagePath}
                alt="测试图片"
                className="max-w-full h-auto rounded"
                onLoad={() => console.log('图片加载成功')}
                onError={(e) => {
                  console.error('图片加载失败:', e)
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  const parent = target.parentElement
                  if (parent) {
                    const errorDiv = document.createElement('div')
                    errorDiv.className = 'text-red-600 text-center py-8'
                    errorDiv.innerHTML = '❌ 图片加载失败'
                    parent.appendChild(errorDiv)
                  }
                }}
              />
            </div>
          </div>

          {/* 方法2: 使用背景图片 */}
          <div className="mb-6">
            <h3 className="font-medium mb-2">方法2: 背景图片</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div
                className="w-full h-64 bg-cover bg-center bg-no-repeat rounded"
                style={{ backgroundImage: `url(${imagePath})` }}
              />
            </div>
          </div>

          {/* 方法3: 使用Next.js Image组件的替代方案 */}
          <div className="mb-6">
            <h3 className="font-medium mb-2">方法3: 条件渲染</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              {imageStatus === 'loading' && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">加载中...</p>
                </div>
              )}
              {imageStatus === 'success' && (
                <img
                  src={imagePath}
                  alt="测试图片"
                  className="max-w-full h-auto rounded"
                />
              )}
              {imageStatus === 'error' && (
                <div className="text-center py-8 text-red-600">
                  <div className="text-4xl mb-2">❌</div>
                  <div>图片加载失败</div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">调试信息</h2>
          <div className="space-y-2 text-sm">
            <div><strong>当前域名:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</div>
            <div><strong>图片完整URL:</strong> {typeof window !== 'undefined' ? `${window.location.origin}${imagePath}` : 'N/A'}</div>
            <div><strong>用户代理:</strong> {typeof window !== 'undefined' ? navigator.userAgent : 'N/A'}</div>
          </div>
        </div>
      </div>
    </div>
  )
}
