'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ShieldCheckIcon, 
  CurrencyDollarIcon, 
  UserGroupIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import Navbar from '@/components/Navbar'
import MediatorRegistration from '@/components/mediator/MediatorRegistration'

interface MediatorStats {
  totalOrders: number
  totalVolume: number
  completedOrders: number
  activeOrders: number
  successRate: number
  availableDeposit: number
}

interface MediatorInfo {
  id: string
  isMediator: boolean
  mediatorStatus: string
  mediatorFeeRate?: number
  mediatorDeposit: number
  mediatorReputation: number
  walletAddress?: string
  walletVerified: boolean
  mediatorVerifiedAt?: string
  stats?: MediatorStats
}

export default function MediatorDashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [mediatorInfo, setMediatorInfo] = useState<MediatorInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'arbitration' | 'escrow'>('overview')
  const [escrowOrders, setEscrowOrders] = useState([])
  const [loadingEscrow, setLoadingEscrow] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    fetchMediatorInfo()
  }, [session, status])

  const fetchMediatorInfo = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/mediator/register')
      const data = await response.json()
      
      if (data.success) {
        setMediatorInfo(data.data)
      }
    } catch (error) {
      console.error('获取中间人信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchEscrowOrders = async () => {
    setLoadingEscrow(true)
    try {
      const response = await fetch('/api/mediator/escrow-orders')
      const data = await response.json()

      if (data.success) {
        setEscrowOrders(data.data.orders || [])
      }
    } catch (error) {
      console.error('获取托管订单失败:', error)
    } finally {
      setLoadingEscrow(false)
    }
  }

  // 当切换到托管订单标签时获取数据
  useEffect(() => {
    if (activeTab === 'escrow' && mediatorInfo?.isMediator) {
      fetchEscrowOrders()
    }
  }, [activeTab, mediatorInfo])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      INACTIVE: { label: '未激活', color: 'bg-gray-100 text-gray-800' },
      PENDING: { label: '审核中', color: 'bg-yellow-100 text-yellow-800' },
      ACTIVE: { label: '已激活', color: 'bg-green-100 text-green-800' },
      SUSPENDED: { label: '已暂停', color: 'bg-red-100 text-red-800' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.INACTIVE
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  // 如果用户不是中间人，显示注册表单
  if (!mediatorInfo?.isMediator) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <MediatorRegistration onSuccess={fetchMediatorInfo} />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">中间人控制台</h1>
              <p className="text-gray-600">管理您的托管服务和仲裁工作</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {getStatusBadge(mediatorInfo.mediatorStatus)}
            <div className="text-right">
              <p className="text-sm text-gray-500">信誉评分</p>
              <p className="text-lg font-semibold text-gray-900">
                {(mediatorInfo.mediatorReputation || 0).toFixed(1)}
              </p>
            </div>
          </div>
        </div>

        {/* 状态提示 */}
        {mediatorInfo.mediatorStatus === 'PENDING' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
            <div className="flex">
              <ClockIcon className="h-5 w-5 text-yellow-400 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  审核中
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  您的中间人申请正在审核中，我们将在1-3个工作日内完成审核。
                </p>
              </div>
            </div>
          </div>
        )}

        {mediatorInfo.mediatorStatus === 'SUSPENDED' && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-red-800">
                  账户已暂停
                </h3>
                <p className="text-sm text-red-700 mt-1">
                  您的中间人账户已被暂停，请联系管理员了解详情。
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 统计卡片 */}
        {mediatorInfo.stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UserGroupIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总订单数</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {mediatorInfo.stats.totalOrders || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总交易额</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {(mediatorInfo.stats.totalVolume || 0).toFixed(0)} USDT
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">成功率</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {(mediatorInfo.stats.successRate || 0).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">可用保证金</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {(mediatorInfo.stats.availableDeposit || 0).toFixed(0)} USDT
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 标签页 */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                概览
              </button>
              <button
                onClick={() => setActiveTab('escrow')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'escrow'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                托管订单
              </button>
              <button
                onClick={() => setActiveTab('arbitration')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'arbitration'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                仲裁投票
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* 基本信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                  <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">服务费率</dt>
                      <dd className="text-sm text-gray-900">
                        {((mediatorInfo.mediatorFeeRate || 0) * 100).toFixed(1)}%
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">保证金</dt>
                      <dd className="text-sm text-gray-900">
                        {(mediatorInfo.mediatorDeposit || 0).toFixed(2)} USDT
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">钱包地址</dt>
                      <dd className="text-sm text-gray-900 font-mono">
                        {mediatorInfo.walletAddress || '未设置'}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">钱包验证</dt>
                      <dd className="text-sm text-gray-900">
                        {mediatorInfo.walletVerified ? '已验证' : '未验证'}
                      </dd>
                    </div>
                  </dl>
                </div>

                {/* 快捷操作 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">快捷操作</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Link
                      href="/escrow/orders?role=mediator"
                      className="bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors text-center"
                    >
                      <UserGroupIcon className="h-6 w-6 mx-auto mb-2" />
                      <p className="font-medium">管理托管订单</p>
                    </Link>
                    
                    <Link
                      href="/arbitration/cases?role=mediator"
                      className="bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors text-center"
                    >
                      <ChartBarIcon className="h-6 w-6 mx-auto mb-2" />
                      <p className="font-medium">参与仲裁投票</p>
                    </Link>
                    
                    <Link
                      href="/rewards/coupons"
                      className="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors text-center"
                    >
                      <CurrencyDollarIcon className="h-6 w-6 mx-auto mb-2" />
                      <p className="font-medium">查看奖励券</p>
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'escrow' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">托管订单管理</h3>
                  <Link
                    href="/mediator/arbitration"
                    className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors text-sm"
                  >
                    参与仲裁投票
                  </Link>
                </div>

                {loadingEscrow ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">加载中...</p>
                  </div>
                ) : escrowOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无托管订单</h3>
                    <p className="text-gray-500">当前没有需要您处理的托管订单</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {escrowOrders.map((order: any) => (
                      <div key={order.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-medium text-gray-900">{order.order.orderNumber}</h4>
                              {getStatusBadge(order.status)}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{order.order.product.title}</p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>金额: {(order.amount || 0).toFixed(2)} USDT</span>
                              <span>费用: {(order.mediatorFee || 0).toFixed(2)} USDT</span>
                              <span>创建: {new Date(order.createdAt).toLocaleDateString()}</span>
                            </div>
                          </div>
                          <Link
                            href={`/escrow/orders/${order.id}`}
                            className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                          >
                            查看详情
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'arbitration' && (
              <div className="text-center py-8">
                <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">仲裁投票</h3>
                <p className="text-gray-500 mb-4">参与争议案例的仲裁投票，获得奖励券</p>
                <Link
                  href="/mediator/arbitration"
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                >
                  查看待投票案例
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
