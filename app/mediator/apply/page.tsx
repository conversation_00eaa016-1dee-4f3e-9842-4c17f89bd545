'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { CheckCircle, AlertCircle, Clock, XCircle } from 'lucide-react'

interface MediatorApplication {
  id: string
  status: string
  bnbWalletAddress: string
  depositAmount: number
  feeRate: number
  experience: string
  introduction: string
  reviewNotes?: string
  reviewer?: { name: string; email: string }
  createdAt: string
  approvedAt?: string
  rejectedAt?: string
}

export default function MediatorApplyPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [application, setApplication] = useState<MediatorApplication | null>(null)
  const [formData, setFormData] = useState({
    bnbWalletAddress: '',
    depositAmount: 1000,
    feeRate: 2,
    experience: '',
    introduction: ''
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user) {
      fetchApplication()
    }
  }, [session, status])

  const fetchApplication = async () => {
    try {
      const response = await fetch('/api/mediator/application')
      const data = await response.json()

      if (data.success && data.data) {
        setApplication(data.data)
        setFormData({
          bnbWalletAddress: data.data.bnbWalletAddress,
          depositAmount: data.data.depositAmount,
          feeRate: data.data.feeRate * 100, // 转换为百分比
          experience: data.data.experience,
          introduction: data.data.introduction
        })
      }
    } catch (error) {
      console.error('获取申请状态失败:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/mediator/application', {
        method: application ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          feeRate: formData.feeRate / 100 // 转换为小数
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess(data.message)
        await fetchApplication()
      } else {
        setError(data.error)
      }
    } catch (error) {
      setError('提交失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="w-3 h-3 mr-1" />待审核</Badge>
      case 'APPROVED':
        return <Badge variant="outline" className="text-green-600"><CheckCircle className="w-3 h-3 mr-1" />已通过</Badge>
      case 'REJECTED':
        return <Badge variant="outline" className="text-red-600"><XCircle className="w-3 h-3 mr-1" />已拒绝</Badge>
      case 'SUSPENDED':
        return <Badge variant="outline" className="text-orange-600"><AlertCircle className="w-3 h-3 mr-1" />已暂停</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (status === 'loading') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">中间人申请</h1>
        <p className="text-gray-600">
          成为平台认证中间人，为用户提供安全的托管服务并获得收益
        </p>
      </div>

      {/* 申请状态卡片 */}
      {application && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              申请状态
              {getStatusBadge(application.status)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm text-gray-500">申请时间</Label>
                <p>{new Date(application.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <Label className="text-sm text-gray-500">保证金金额</Label>
                <p>{application.depositAmount} USDT</p>
              </div>
              <div>
                <Label className="text-sm text-gray-500">手续费率</Label>
                <p>{(application.feeRate * 100).toFixed(1)}%</p>
              </div>
              {application.reviewer && (
                <div>
                  <Label className="text-sm text-gray-500">审核人</Label>
                  <p>{application.reviewer.name}</p>
                </div>
              )}
            </div>
            
            {application.reviewNotes && (
              <div className="mt-4">
                <Label className="text-sm text-gray-500">审核备注</Label>
                <p className="mt-1 p-3 bg-gray-50 rounded-md">{application.reviewNotes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 中间人要求说明 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>中间人要求</CardTitle>
          <CardDescription>
            请仔细阅读以下要求，确保您符合条件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">保证金要求</h4>
                <p className="text-sm text-gray-600">最低保证金 1000 USDT，用于担保托管订单</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">手续费设置</h4>
                <p className="text-sm text-gray-600">手续费率范围 1%-30%，平台抽取30%</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">钱包要求</h4>
                <p className="text-sm text-gray-600">需要绑定并验证 BNB Chain 钱包地址</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">服务承诺</h4>
                <p className="text-sm text-gray-600">承诺及时响应托管服务，参与争议仲裁</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 申请表单 */}
      {(!application || application.status === 'PENDING') && (
        <Card>
          <CardHeader>
            <CardTitle>
              {application ? '修改申请信息' : '提交中间人申请'}
            </CardTitle>
            <CardDescription>
              请填写完整的申请信息，我们将在1-3个工作日内完成审核
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                <div>
                  <Label htmlFor="bnbWalletAddress">BNB Chain 钱包地址 *</Label>
                  <Input
                    id="bnbWalletAddress"
                    type="text"
                    placeholder="0x..."
                    value={formData.bnbWalletAddress}
                    onChange={(e) => setFormData({ ...formData, bnbWalletAddress: e.target.value })}
                    required
                    disabled={application?.status === 'APPROVED'}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    用于接收托管资金的BNB链钱包地址
                  </p>
                </div>

                <div>
                  <Label htmlFor="depositAmount">保证金金额 (USDT) *</Label>
                  <Input
                    id="depositAmount"
                    type="number"
                    min="1000"
                    step="100"
                    value={formData.depositAmount}
                    onChange={(e) => setFormData({ ...formData, depositAmount: Number(e.target.value) })}
                    required
                    disabled={application?.status === 'APPROVED'}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    最低1000 USDT，保证金越高可担保的订单金额越大
                  </p>
                </div>

                <div>
                  <Label htmlFor="feeRate">手续费率 (%) *</Label>
                  <Input
                    id="feeRate"
                    type="number"
                    min="1"
                    max="30"
                    step="0.1"
                    value={formData.feeRate}
                    onChange={(e) => setFormData({ ...formData, feeRate: Number(e.target.value) })}
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    您的服务费率，范围1%-30%，平台将抽取30%
                  </p>
                </div>

                <div>
                  <Label htmlFor="experience">相关经验 *</Label>
                  <Textarea
                    id="experience"
                    placeholder="请描述您在数字货币交易、托管服务等方面的经验..."
                    value={formData.experience}
                    onChange={(e) => setFormData({ ...formData, experience: e.target.value })}
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="introduction">个人介绍 *</Label>
                  <Textarea
                    id="introduction"
                    placeholder="请介绍您的背景、专业技能、服务理念等..."
                    value={formData.introduction}
                    onChange={(e) => setFormData({ ...formData, introduction: e.target.value })}
                    rows={4}
                    required
                  />
                </div>
              </div>

              <Separator />

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                >
                  返回
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading ? '提交中...' : (application ? '更新申请' : '提交申请')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* 已通过的申请显示 */}
      {application?.status === 'APPROVED' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">🎉 恭喜！您已成为认证中间人</CardTitle>
            <CardDescription>
              您现在可以为用户提供托管服务并获得收益
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-800">服务费率</h4>
                  <p className="text-2xl font-bold text-green-600">{(application.feeRate * 100).toFixed(1)}%</p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-800">保证金</h4>
                  <p className="text-2xl font-bold text-blue-600">{application.depositAmount} USDT</p>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <Button onClick={() => router.push('/mediator/dashboard')}>
                  进入中间人控制台
                </Button>
                <Button variant="outline" onClick={() => router.push('/mediator/orders')}>
                  查看托管订单
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
