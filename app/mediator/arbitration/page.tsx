'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { formatUSDT } from '@/lib/utils'
import { 
  Scale, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Users, 
  FileText,
  Award,
  Shield
} from 'lucide-react'

interface DisputeCase {
  id: string
  reason: string
  description: string
  evidence: any
  status: string
  priority: string
  createdAt: string
  escrowOrder: {
    id: string
    amount: number
    order: {
      orderNumber: string
      product: {
        title: string
        images: string[]
      }
    }
    buyer: {
      name: string
      email: string
    }
    seller: {
      name: string
      email: string
    }
    mediator: {
      name: string
      email: string
    }
  }
  reporter: {
    name: string
    email: string
  }
  reported: {
    name: string
    email: string
  }
  votes: {
    decision: string
    voteWeight: number
    voter: {
      name: string
    }
  }[]
  voteStatistics: {
    totalVotes: number
    totalWeight: number
    decisions: Record<string, { count: number; weight: number }>
  }
}

export default function ArbitrationPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [disputes, setDisputes] = useState<DisputeCase[]>([])
  const [loading, setLoading] = useState(true)
  const [voting, setVoting] = useState<string | null>(null)
  const [selectedDecision, setSelectedDecision] = useState('')
  const [reasoning, setReasoning] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user) {
      fetchDisputes()
    }
  }, [session, status])

  const fetchDisputes = async () => {
    try {
      const response = await fetch('/api/escrow/dispute/vote')
      const data = await response.json()

      if (data.success) {
        setDisputes(data.data.disputes || [])
      } else {
        setError(data.error)
      }
    } catch (error) {
      console.error('获取仲裁案例失败:', error)
      setError('获取仲裁案例失败')
    } finally {
      setLoading(false)
    }
  }

  const submitVote = async (disputeId: string) => {
    if (!selectedDecision || !reasoning.trim()) {
      setError('请选择仲裁决定并填写理由')
      return
    }

    setVoting(disputeId)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/escrow/dispute/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          disputeId,
          decision: selectedDecision,
          reasoning: reasoning.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess('投票提交成功！')
        setSelectedDecision('')
        setReasoning('')
        await fetchDisputes()
      } else {
        setError(data.error)
      }
    } catch (error) {
      console.error('提交投票失败:', error)
      setError('提交投票失败')
    } finally {
      setVoting(null)
    }
  }

  const getDecisionLabel = (decision: string) => {
    switch (decision) {
      case 'BUYER_FAVOR':
        return '支持买家'
      case 'SELLER_FAVOR':
        return '支持卖家'
      case 'MEDIATOR_FAVOR':
        return '支持中间人'
      case 'SPLIT_DECISION':
        return '平分款项'
      default:
        return decision
    }
  }

  const getDecisionColor = (decision: string) => {
    switch (decision) {
      case 'BUYER_FAVOR':
        return 'bg-blue-100 text-blue-800'
      case 'SELLER_FAVOR':
        return 'bg-green-100 text-green-800'
      case 'MEDIATOR_FAVOR':
        return 'bg-purple-100 text-purple-800'
      case 'SPLIT_DECISION':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <Badge variant="destructive">紧急</Badge>
      case 'HIGH':
        return <Badge variant="destructive" className="bg-orange-500">高</Badge>
      case 'MEDIUM':
        return <Badge variant="secondary">中</Badge>
      case 'LOW':
        return <Badge variant="outline">低</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const getReasonLabel = (reason: string) => {
    const reasonMap: Record<string, string> = {
      'PRODUCT_MISMATCH': '商品与描述不符',
      'QUALITY_ISSUE': '商品质量问题',
      'NO_DELIVERY': '未收到商品',
      'FAKE_PRODUCT': '假冒商品',
      'PAYMENT_ISSUE': '支付问题',
      'COMMUNICATION_ISSUE': '沟通问题',
      'FRAUD': '欺诈行为',
      'SCAM': '诈骗',
      'SAFETY_CONCERN': '安全问题',
      'ILLEGAL_ACTIVITY': '违法活动',
      'OTHER': '其他'
    }
    return reasonMap[reason] || reason
  }

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center">
          <Scale className="w-8 h-8 mr-3 text-purple-500" />
          仲裁投票
        </h1>
        <p className="text-gray-600">
          参与争议仲裁，维护平台交易秩序，获得奖励
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-6">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* 奖励说明 */}
      <Card className="mb-6 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center text-purple-700">
            <Award className="w-5 h-5 mr-2" />
            参与奖励
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-white rounded-lg">
              <div className="text-2xl font-bold text-purple-600">10 USDT</div>
              <div className="text-sm text-gray-600">免手续费提现券</div>
            </div>
            <div className="text-center p-4 bg-white rounded-lg">
              <div className="text-2xl font-bold text-blue-600">7天</div>
              <div className="text-sm text-gray-600">有效期</div>
            </div>
            <div className="text-center p-4 bg-white rounded-lg">
              <div className="text-2xl font-bold text-green-600">1张/月</div>
              <div className="text-sm text-gray-600">限制数量</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {disputes.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Scale className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无待投票的争议</h3>
            <p className="text-gray-500">当前没有需要您参与仲裁的争议案例</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {disputes.map((dispute) => (
            <Card key={dispute.id} className="border-l-4 border-l-purple-500">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <span>争议案例</span>
                      {getPriorityBadge(dispute.priority)}
                    </CardTitle>
                    <CardDescription>
                      订单: {dispute.escrowOrder.order.orderNumber} | 
                      金额: {formatUSDT(dispute.escrowOrder.amount)} | 
                      {new Date(dispute.createdAt).toLocaleString()}
                    </CardDescription>
                  </div>
                  <Badge variant="outline" className="text-yellow-600">
                    <Clock className="w-3 h-3 mr-1" />
                    投票中
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* 商品信息 */}
                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  {dispute.escrowOrder.order.product.images?.[0] && (
                    <img
                      src={dispute.escrowOrder.order.product.images[0]}
                      alt={dispute.escrowOrder.order.product.title}
                      className="w-16 h-16 object-cover rounded"
                    />
                  )}
                  <div>
                    <h4 className="font-medium">{dispute.escrowOrder.order.product.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      买家: {dispute.escrowOrder.buyer.name} | 
                      卖家: {dispute.escrowOrder.seller.name} | 
                      中间人: {dispute.escrowOrder.mediator.name}
                    </p>
                  </div>
                </div>

                {/* 争议信息 */}
                <div>
                  <h4 className="font-medium mb-2">争议详情</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">举报原因:</span>
                      <Badge variant="outline">{getReasonLabel(dispute.reason)}</Badge>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-sm text-gray-600">举报人:</span>
                      <span className="text-sm">{dispute.reporter.name}</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-sm text-gray-600">被举报人:</span>
                      <span className="text-sm">{dispute.reported.name}</span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">详细描述:</span>
                      <p className="text-sm mt-1 p-3 bg-white rounded border">
                        {dispute.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 当前投票统计 */}
                {dispute.voteStatistics.totalVotes > 0 && (
                  <div>
                    <h4 className="font-medium mb-2 flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      当前投票统计 ({dispute.voteStatistics.totalVotes}票)
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {Object.entries(dispute.voteStatistics.decisions).map(([decision, stats]) => (
                        <div key={decision} className={`p-2 rounded text-center ${getDecisionColor(decision)}`}>
                          <div className="text-sm font-medium">{getDecisionLabel(decision)}</div>
                          <div className="text-xs">{stats.count}票 (权重: {stats.weight})</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* 投票区域 */}
                <div>
                  <h4 className="font-medium mb-3 flex items-center">
                    <Scale className="w-4 h-4 mr-1" />
                    您的仲裁决定
                  </h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        仲裁决定 *
                      </label>
                      <Select value={selectedDecision} onValueChange={setSelectedDecision}>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择您的仲裁决定" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BUYER_FAVOR">支持买家 - 退款给买家</SelectItem>
                          <SelectItem value="SELLER_FAVOR">支持卖家 - 打款给卖家</SelectItem>
                          <SelectItem value="MEDIATOR_FAVOR">支持中间人 - 中间人处理得当</SelectItem>
                          <SelectItem value="SPLIT_DECISION">平分决定 - 买卖双方各承担50%</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        仲裁理由 *
                      </label>
                      <Textarea
                        value={reasoning}
                        onChange={(e) => setReasoning(e.target.value)}
                        placeholder="请详细说明您的仲裁理由和依据..."
                        rows={4}
                      />
                    </div>

                    <Button
                      onClick={() => submitVote(dispute.id)}
                      disabled={!selectedDecision || !reasoning.trim() || voting === dispute.id}
                      className="w-full"
                    >
                      {voting === dispute.id ? '提交中...' : '提交仲裁投票'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
