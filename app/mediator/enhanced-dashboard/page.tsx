"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ClockIcon,
  TrophyIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  UserGroupIcon,
  WalletIcon,
  GiftIcon,
  BellIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline'
import Navbar from '@/components/Navbar'

interface MediatorStats {
  totalOrders: number
  activeOrders: number
  completedOrders: number
  totalVolume: number
  totalEarnings: number
  successRate: number
  reputation: number
  availableDeposit: number
  usedDeposit: number
  monthlyRewards: number
  pendingDisputes: number
  recentRewards: any[]
}

interface Order {
  id: string
  orderNumber: string
  status: string
  escrowStatus: string
  totalAmount: number
  escrowFee: number
  createdAt: string
  disputeStatus?: string
  product: {
    title: string
    images: string[]
  }
  buyer: {
    name: string
  }
  seller: {
    name: string
  }
}

interface Notification {
  id: string
  type: string
  title: string
  message: string
  createdAt: string
  read: boolean
}

export default function EnhancedMediatorDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<MediatorStats | null>(null)
  const [orders, setOrders] = useState<Order[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    fetchDashboardData()
  }, [session, status])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError('')

      // 获取中间人统计数据
      const statsResponse = await fetch('/api/mediator/stats')
      if (statsResponse.ok) {
        const statsResult = await statsResponse.json()
        if (statsResult.success) {
          setStats(statsResult.data)
        }
      }

      // 获取托管订单列表
      const ordersResponse = await fetch('/api/escrow/orders/enhanced?role=mediator&limit=10')
      if (ordersResponse.ok) {
        const ordersResult = await ordersResponse.json()
        if (ordersResult.success) {
          setOrders(ordersResult.data.orders)
        }
      }

      // 获取通知
      const notificationsResponse = await fetch('/api/mediator/notifications?limit=5')
      if (notificationsResponse.ok) {
        const notificationsResult = await notificationsResponse.json()
        if (notificationsResult.success) {
          setNotifications(notificationsResult.data.notifications)
        }
      }

    } catch (error) {
      console.error('获取面板数据失败:', error)
      setError('获取数据失败，请刷新重试')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING_PAYMENT':
        return 'bg-yellow-100 text-yellow-800'
      case 'PAID':
        return 'bg-blue-100 text-blue-800'
      case 'SHIPPED':
        return 'bg-purple-100 text-purple-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'REFUNDED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING_PAYMENT': '待支付',
      'PAID': '已支付',
      'SHIPPED': '已发货',
      'COMPLETED': '已完成',
      'REFUNDED': '已退款'
    }
    return statusMap[status] || status
  }

  const getUrgentOrders = () => {
    return orders.filter(order => 
      order.disputeStatus === 'REPORTED' || 
      (order.status === 'SHIPPED' && new Date(order.createdAt) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和快速操作 */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">中间人控制台</h1>
              <p className="text-gray-600">管理您的托管服务和收益</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => router.push('/mediator/orders')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              管理订单
            </button>
            <button
              onClick={() => router.push('/mediator/arbitration')}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              仲裁中心
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
                <button
                  onClick={fetchDashboardData}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  重新加载
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 紧急通知 */}
        {stats && (stats.pendingDisputes > 0 || getUrgentOrders().length > 0) && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-yellow-800">需要您的关注</h4>
                <div className="mt-2 text-sm text-yellow-700">
                  {stats.pendingDisputes > 0 && (
                    <p>• 有 {stats.pendingDisputes} 个争议案例等待处理</p>
                  )}
                  {getUrgentOrders().length > 0 && (
                    <p>• 有 {getUrgentOrders().length} 个订单需要及时处理</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总收益</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats.totalEarnings.toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">USDT</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">活跃订单</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.activeOrders}</p>
                  <p className="text-xs text-gray-500">进行中</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrophyIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">成功率</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats.successRate.toFixed(1)}%
                  </p>
                  <p className="text-xs text-gray-500">历史记录</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <WalletIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">可用保证金</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats.availableDeposit.toFixed(0)}
                  </p>
                  <p className="text-xs text-gray-500">USDT</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <GiftIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">本月奖励</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats.monthlyRewards}
                  </p>
                  <p className="text-xs text-gray-500">奖励券</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：订单和业务数据 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 业务概览 */}
            {stats && (
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <ChartBarIcon className="h-5 w-5 mr-2" />
                    业务概览
                  </h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <div className="text-sm text-gray-500">总订单数</div>
                        <div className="text-2xl font-semibold text-gray-900">{stats.totalOrders}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-500">交易总额</div>
                        <div className="text-2xl font-semibold text-gray-900">
                          {(stats.totalVolume / 1000).toFixed(1)}K USDT
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <div className="text-sm text-gray-500">完成订单</div>
                        <div className="text-2xl font-semibold text-green-600">{stats.completedOrders}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-500">信誉评分</div>
                        <div className="text-2xl font-semibold text-yellow-600">
                          {stats.reputation.toFixed(1)}/5.0
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 保证金使用情况 */}
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">保证金使用情况</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">已使用</span>
                        <span className="font-medium">{stats.usedDeposit.toFixed(0)} USDT</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${(stats.usedDeposit / (stats.usedDeposit + stats.availableDeposit)) * 100}%`
                          }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">可用: {stats.availableDeposit.toFixed(0)} USDT</span>
                        <span className="text-gray-500">总计: {(stats.usedDeposit + stats.availableDeposit).toFixed(0)} USDT</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 最近订单 */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">最近托管订单</h3>
                  <button
                    onClick={() => router.push('/mediator/orders')}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    查看全部
                  </button>
                </div>
              </div>
              <div className="overflow-hidden">
                {orders.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {orders.slice(0, 5).map((order) => (
                      <div key={order.id} className="p-6 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              {order.product.images?.[0] ? (
                                <img
                                  className="h-10 w-10 rounded-lg object-cover"
                                  src={order.product.images[0]}
                                  alt=""
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                                  <ShieldCheckIcon className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {order.orderNumber}
                              </div>
                              <div className="text-sm text-gray-500">
                                {order.buyer.name} ↔ {order.seller.name}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">
                              {order.totalAmount.toFixed(2)} USDT
                            </div>
                            <div className="text-sm text-green-600">
                              +{order.escrowFee.toFixed(2)} USDT
                            </div>
                          </div>
                          <div>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                              {getStatusText(order.status)}
                            </span>
                            {order.disputeStatus === 'REPORTED' && (
                              <div className="mt-1">
                                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                  争议中
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-300" />
                    <p className="mt-2 text-sm text-gray-500">暂无托管订单</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧：通知和快速操作 */}
          <div className="space-y-6">
            {/* 通知中心 */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <BellIcon className="h-5 w-5 mr-2" />
                  通知中心
                </h3>
              </div>
              <div className="divide-y divide-gray-200">
                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <div key={notification.id} className={`p-4 ${!notification.read ? 'bg-blue-50' : ''}`}>
                      <div className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {notification.message}
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        {new Date(notification.createdAt).toLocaleString('zh-CN')}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-6 text-center text-gray-500">
                    <BellIcon className="mx-auto h-8 w-8 text-gray-300" />
                    <p className="mt-2 text-sm">暂无新通知</p>
                  </div>
                )}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">快速操作</h3>
              </div>
              <div className="p-6 space-y-3">
                <button
                  onClick={() => router.push('/mediator/arbitration')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <UserGroupIcon className="h-4 w-4 mr-2" />
                  仲裁投票
                </button>
                <button
                  onClick={() => router.push('/mediator/rewards')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <GiftIcon className="h-4 w-4 mr-2" />
                  我的奖励
                </button>
                <button
                  onClick={() => router.push('/mediator/settings')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <WalletIcon className="h-4 w-4 mr-2" />
                  保证金管理
                </button>
              </div>
            </div>

            {/* 最近奖励 */}
            {stats && stats.recentRewards && stats.recentRewards.length > 0 && (
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <ArrowTrendingUpIcon className="h-5 w-5 mr-2" />
                    最近奖励
                  </h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {stats.recentRewards.slice(0, 3).map((reward: any) => (
                    <div key={reward.id} className="p-4">
                      <div className="text-sm font-medium text-gray-900">
                        {reward.description}
                      </div>
                      <div className="text-sm text-green-600 mt-1">
                        +{reward.rewardAmount} USDT
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(reward.createdAt).toLocaleDateString('zh-CN')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
