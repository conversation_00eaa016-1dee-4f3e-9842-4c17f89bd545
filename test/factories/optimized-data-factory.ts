import { faker } from '@faker-js/faker'

// 预生成的常用数据池，避免重复计算
class DataPool {
  private static instance: DataPool
  private emailPool: string[] = []
  private namePool: string[] = []
  private addressPool: any[] = []
  private productTitlePool: string[] = []
  private descriptionPool: string[] = []
  private imageUrlPool: string[] = []

  private constructor() {
    this.initializePools()
  }

  static getInstance(): DataPool {
    if (!DataPool.instance) {
      DataPool.instance = new DataPool()
    }
    return DataPool.instance
  }

  private initializePools() {
    // 预生成1000个邮箱
    for (let i = 0; i < 1000; i++) {
      this.emailPool.push(faker.internet.email())
    }

    // 预生成500个姓名
    for (let i = 0; i < 500; i++) {
      this.namePool.push(faker.person.fullName())
    }

    // 预生成200个地址
    for (let i = 0; i < 200; i++) {
      this.addressPool.push({
        city: faker.location.city(),
        district: faker.location.county(),
        province: faker.location.state(),
        detail: faker.location.streetAddress()
      })
    }

    // 预生成商品标题
    const categories = ['iPhone', 'MacBook', 'iPad', 'AirPods', '小米', '华为', '三星', 'Dell', 'HP', 'Nike']
    const adjectives = ['全新', '二手', '95新', '99新', '未拆封', '原装', '正品', '限量版']
    
    for (const category of categories) {
      for (const adj of adjectives) {
        this.productTitlePool.push(`${adj} ${category} ${faker.commerce.productName()}`)
      }
    }

    // 预生成描述
    const descriptions = [
      '商品状况良好，功能正常，外观无明显瑕疵',
      '全新未拆封，原厂包装，支持官方保修',
      '个人闲置，9成新，配件齐全，价格实惠',
      '正品保证，支持验货，不满意可退换',
      '限时特价，数量有限，先到先得'
    ]
    this.descriptionPool = descriptions

    // 预生成图片URL
    for (let i = 0; i < 100; i++) {
      this.imageUrlPool.push(`/uploads/products/${faker.string.uuid()}.jpg`)
    }
  }

  getRandomEmail(): string {
    return this.emailPool[Math.floor(Math.random() * this.emailPool.length)]
  }

  getRandomName(): string {
    return this.namePool[Math.floor(Math.random() * this.namePool.length)]
  }

  getRandomAddress(): any {
    return this.addressPool[Math.floor(Math.random() * this.addressPool.length)]
  }

  getRandomProductTitle(): string {
    return this.productTitlePool[Math.floor(Math.random() * this.productTitlePool.length)]
  }

  getRandomDescription(): string {
    return this.descriptionPool[Math.floor(Math.random() * this.descriptionPool.length)]
  }

  getRandomImageUrl(): string {
    return this.imageUrlPool[Math.floor(Math.random() * this.imageUrlPool.length)]
  }
}

// 优化的用户工厂
export class OptimizedUserFactory {
  private static dataPool = DataPool.getInstance()
  private static userIdCounter = 1
  private static idCounter = 1

  static create(overrides: Partial<any> = {}) {
    const userId = `user-${String(this.userIdCounter++).padStart(8, '0')}`
    const id = `id-${String(this.idCounter++).padStart(8, '0')}`
    const address = this.dataPool.getRandomAddress()

    return {
      id,
      userId,
      name: this.dataPool.getRandomName(),
      email: this.dataPool.getRandomEmail(),
      password: 'hashed-password-123', // 固定值避免重复计算
      avatar: `/avatars/default-${(this.idCounter % 10) + 1}.jpg`,
      binanceUid: String(Math.floor(Math.random() * *********0)),
      bnbWalletAddress: `0x${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}${'0'.repeat(34)}`,
      city: address.city,
      district: address.district,
      creditScore: Math.floor(Math.random() * 101),
      depositBalance: Math.floor(Math.random() * 10000 * 100) / 100,
      status: ['ACTIVE', 'SUSPENDED', 'BANNED'][Math.floor(Math.random() * 3)],
      role: Math.random() > 0.9 ? 'ADMIN' : 'USER',
      riskLevel: ['NORMAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'][Math.floor(Math.random() * 5)],
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
      ...overrides
    }
  }

  static createBatch(count: number, overrides: Partial<any> = {}): any[] {
    const users = new Array(count)
    for (let i = 0; i < count; i++) {
      users[i] = this.create(overrides)
    }
    return users
  }

  static createBuyer(overrides: Partial<any> = {}) {
    return this.create({
      role: 'USER',
      creditScore: Math.floor(Math.random() * 31) + 70, // 70-100
      ...overrides
    })
  }

  static createSeller(overrides: Partial<any> = {}) {
    return this.create({
      role: 'USER',
      creditScore: Math.floor(Math.random() * 21) + 80, // 80-100
      depositBalance: Math.floor(Math.random() * 4900 + 100), // 100-5000
      ...overrides
    })
  }
}

// 优化的商品工厂
export class OptimizedProductFactory {
  private static dataPool = DataPool.getInstance()
  private static idCounter = 1

  static create(overrides: Partial<any> = {}) {
    const id = `product-${String(this.idCounter++).padStart(8, '0')}`
    const address = this.dataPool.getRandomAddress()
    const imageCount = Math.floor(Math.random() * 4) + 1
    const images = Array.from({ length: imageCount }, () => this.dataPool.getRandomImageUrl()).join(',')

    return {
      id,
      title: this.dataPool.getRandomProductTitle(),
      description: this.dataPool.getRandomDescription(),
      images,
      price: Math.floor(Math.random() * 9990 + 10), // 10-10000
      city: address.city,
      district: address.district,
      stock: Math.floor(Math.random() * 100) + 1,
      status: ['AVAILABLE', 'SOLD_OUT', 'DRAFT'][Math.floor(Math.random() * 3)],
      category: ['ELECTRONICS', 'CLOTHING', 'BOOKS', 'HOME', 'SPORTS', 'BEAUTY'][Math.floor(Math.random() * 6)],
      condition: ['NEW', 'USED_LIKE_NEW', 'USED_GOOD', 'USED_FAIR'][Math.floor(Math.random() * 4)],
      shippingFrom: address.city,
      reviewStatus: ['PENDING', 'APPROVED', 'REJECTED'][Math.floor(Math.random() * 3)],
      isDemandGenerated: Math.random() > 0.7,
      hasVariants: Math.random() > 0.8,
      sellerId: `seller-${Math.floor(Math.random() * 1000)}`,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
      ...overrides
    }
  }

  static createBatch(count: number, overrides: Partial<any> = {}): any[] {
    const products = new Array(count)
    for (let i = 0; i < count; i++) {
      products[i] = this.create(overrides)
    }
    return products
  }

  static createAvailable(overrides: Partial<any> = {}) {
    return this.create({
      status: 'AVAILABLE',
      reviewStatus: 'APPROVED',
      stock: Math.floor(Math.random() * 50) + 1,
      ...overrides
    })
  }
}

// 优化的订单工厂
export class OptimizedOrderFactory {
  private static idCounter = 1
  private static orderNumberCounter = 100000

  static create(overrides: Partial<any> = {}) {
    const id = `order-${String(this.idCounter++).padStart(8, '0')}`
    const orderNumber = `ORD-${String(this.orderNumberCounter++).toUpperCase()}`
    const address = DataPool.getInstance().getRandomAddress()
    const productPrice = Math.floor(Math.random() * 4990 + 10)
    const shippingFee = Math.floor(Math.random() * 50)
    const platformFee = Math.floor(productPrice * 0.03)

    return {
      id,
      orderNumber,
      status: ['PENDING_PAYMENT', 'PAID', 'SHIPPED', 'COMPLETED', 'CANCELLED', 'REFUNDED'][Math.floor(Math.random() * 6)],
      totalAmount: productPrice + shippingFee + platformFee,
      productPrice,
      shippingFee,
      platformFee,
      paymentMethod: Math.random() > 0.5 ? ['BINANCE_PAY', 'BNB_CHAIN'][Math.floor(Math.random() * 2)] : null,
      paymentScreenshot: Math.random() > 0.7 ? `/uploads/payment/${faker.string.uuid()}.jpg` : null,
      paymentTxHash: Math.random() > 0.6 ? `0x${Math.floor(Math.random() * 16777215).toString(16).padStart(64, '0')}` : null,
      paymentConfirmed: Math.random() > 0.5,
      escrowStatus: ['PENDING', 'FUNDED', 'RELEASED', 'REFUNDED'][Math.floor(Math.random() * 4)],
      escrowAmount: productPrice,
      shippingAddress: {
        name: DataPool.getInstance().getRandomName(),
        phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
        province: address.province,
        city: address.city,
        district: address.district,
        detail: address.detail
      },
      trackingNumber: Math.random() > 0.6 ? `SF${String(Math.floor(Math.random() * *********0)).padStart(10, '0')}` : null,
      shippingCompany: Math.random() > 0.6 ? ['顺丰', '圆通', '中通', '申通'][Math.floor(Math.random() * 4)] : null,
      receivedAt: Math.random() > 0.7 ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) : null,
      autoConfirmAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      refundReason: Math.random() > 0.9 ? '商品质量问题' : null,
      refundAmount: Math.random() > 0.95 ? productPrice : null,
      productId: `product-${Math.floor(Math.random() * 10000)}`,
      buyerId: `buyer-${Math.floor(Math.random() * 1000)}`,
      sellerId: `seller-${Math.floor(Math.random() * 1000)}`,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
      ...overrides
    }
  }

  static createBatch(count: number, overrides: Partial<any> = {}): any[] {
    const orders = new Array(count)
    for (let i = 0; i < count; i++) {
      orders[i] = this.create(overrides)
    }
    return orders
  }
}

// 批量数据生成器 - 使用Worker Pool模式
export class BatchDataGenerator {
  static async generateUsers(count: number, batchSize: number = 1000): Promise<any[]> {
    const batches = Math.ceil(count / batchSize)
    const results: any[] = []

    for (let i = 0; i < batches; i++) {
      const currentBatchSize = Math.min(batchSize, count - i * batchSize)
      const batch = OptimizedUserFactory.createBatch(currentBatchSize)
      results.push(...batch)
    }

    return results
  }

  static async generateProducts(count: number, batchSize: number = 1000): Promise<any[]> {
    const batches = Math.ceil(count / batchSize)
    const results: any[] = []

    for (let i = 0; i < batches; i++) {
      const currentBatchSize = Math.min(batchSize, count - i * batchSize)
      const batch = OptimizedProductFactory.createBatch(currentBatchSize)
      results.push(...batch)
    }

    return results
  }

  static async generateOrders(count: number, batchSize: number = 1000): Promise<any[]> {
    const batches = Math.ceil(count / batchSize)
    const results: any[] = []

    for (let i = 0; i < batches; i++) {
      const currentBatchSize = Math.min(batchSize, count - i * batchSize)
      const batch = OptimizedOrderFactory.createBatch(currentBatchSize)
      results.push(...batch)
    }

    return results
  }

  // 并行生成多种类型数据
  static async generateMarketplaceData(
    userCount: number,
    productCount: number,
    orderCount: number
  ): Promise<{ users: any[]; products: any[]; orders: any[] }> {
    const [users, products, orders] = await Promise.all([
      this.generateUsers(userCount),
      this.generateProducts(productCount),
      this.generateOrders(orderCount)
    ])

    return { users, products, orders }
  }
}
