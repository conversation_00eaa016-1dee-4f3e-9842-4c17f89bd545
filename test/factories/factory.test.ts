import { describe, it, expect } from 'vitest'
import { 
  UserFactory, 
  ProductFactory, 
  OrderFactory, 
  MessageFactory,
  ReviewFactory,
  FeedbackFactory,
  DataGenerator 
} from '@/test/factories/data-factory'

describe('数据工厂测试', () => {
  describe('UserFactory', () => {
    it('应该创建有效的用户数据', () => {
      const user = UserFactory.create()
      
      expect(user).toBeDefined()
      expect(user.id).toBeTruthy()
      expect(user.userId).toBeTruthy()
      expect(user.name).toBeTruthy()
      expect(user.email).toContain('@')
      expect(user.creditScore).toBeGreaterThanOrEqual(0)
      expect(user.creditScore).toBeLessThanOrEqual(100)
      expect(['ACTIVE', 'SUSPENDED', 'BANNED']).toContain(user.status)
      expect(['USER', 'ADMIN']).toContain(user.role)
    })

    it('应该创建买家用户', () => {
      const buyer = UserFactory.createBuyer()
      
      expect(buyer.role).toBe('USER')
      expect(buyer.creditScore).toBeGreaterThanOrEqual(70)
    })

    it('应该创建卖家用户', () => {
      const seller = UserFactory.createSeller()
      
      expect(seller.role).toBe('USER')
      expect(seller.creditScore).toBeGreaterThanOrEqual(80)
      expect(seller.depositBalance).toBeGreaterThanOrEqual(100)
    })

    it('应该创建管理员用户', () => {
      const admin = UserFactory.createAdmin()
      
      expect(admin.role).toBe('ADMIN')
      expect(admin.creditScore).toBe(100)
      expect(admin.status).toBe('ACTIVE')
    })

    it('应该批量创建用户', () => {
      const users = UserFactory.createBatch(5)
      
      expect(users).toHaveLength(5)
      users.forEach(user => {
        expect(user.email).toContain('@')
        expect(user.name).toBeTruthy()
      })
    })
  })

  describe('ProductFactory', () => {
    it('应该创建有效的商品数据', () => {
      const product = ProductFactory.create()
      
      expect(product).toBeDefined()
      expect(product.id).toBeTruthy()
      expect(product.title).toBeTruthy()
      expect(product.description).toBeTruthy()
      expect(product.price).toBeGreaterThan(0)
      expect(product.stock).toBeGreaterThanOrEqual(1)
      expect(['AVAILABLE', 'SOLD_OUT', 'DRAFT']).toContain(product.status)
      expect(['PENDING', 'APPROVED', 'REJECTED']).toContain(product.reviewStatus)
    })

    it('应该创建可用商品', () => {
      const product = ProductFactory.createAvailable()
      
      expect(product.status).toBe('AVAILABLE')
      expect(product.reviewStatus).toBe('APPROVED')
      expect(product.stock).toBeGreaterThanOrEqual(1)
    })

    it('应该创建待审核商品', () => {
      const product = ProductFactory.createPending()
      
      expect(product.reviewStatus).toBe('PENDING')
    })
  })

  describe('OrderFactory', () => {
    it('应该创建有效的订单数据', () => {
      const order = OrderFactory.create()
      
      expect(order).toBeDefined()
      expect(order.id).toBeTruthy()
      expect(order.orderNumber).toMatch(/^ORD-[A-Z0-9]{8}$/)
      expect(order.totalAmount).toBeGreaterThan(0)
      expect(order.productPrice).toBeGreaterThan(0)
      expect(order.shippingAddress).toBeDefined()
      expect(order.shippingAddress.name).toBeTruthy()
      expect(order.shippingAddress.phone).toBeTruthy()
    })

    it('应该创建待支付订单', () => {
      const order = OrderFactory.createPending()
      
      expect(order.status).toBe('PENDING_PAYMENT')
      expect(order.paymentMethod).toBeNull()
      expect(order.paymentConfirmed).toBe(false)
      expect(order.escrowStatus).toBe('PENDING')
    })

    it('应该创建已支付订单', () => {
      const order = OrderFactory.createPaid()
      
      expect(order.status).toBe('PAID')
      expect(order.paymentMethod).toBeTruthy()
      expect(order.paymentConfirmed).toBe(true)
      expect(order.escrowStatus).toBe('FUNDED')
      expect(order.paymentTxHash).toBeTruthy()
    })

    it('应该创建已完成订单', () => {
      const order = OrderFactory.createCompleted()
      
      expect(order.status).toBe('COMPLETED')
      expect(order.paymentConfirmed).toBe(true)
      expect(order.escrowStatus).toBe('RELEASED')
      expect(order.receivedAt).toBeTruthy()
      expect(order.trackingNumber).toBeTruthy()
    })
  })

  describe('MessageFactory', () => {
    it('应该创建有效的消息数据', () => {
      const message = MessageFactory.create()
      
      expect(message).toBeDefined()
      expect(message.id).toBeTruthy()
      expect(['TEXT', 'IMAGE', 'VIDEO', 'FILE', 'SYSTEM']).toContain(message.messageType)
      expect(['SENT', 'DELIVERED', 'READ']).toContain(message.status)
      expect(message.senderId).toBeTruthy()
      expect(message.receiverId).toBeTruthy()
    })

    it('应该创建文本消息', () => {
      const message = MessageFactory.createText()
      
      expect(message.messageType).toBe('TEXT')
      expect(message.content).toBeTruthy()
      expect(message.fileUrl).toBeNull()
    })

    it('应该创建图片消息', () => {
      const message = MessageFactory.createImage()
      
      expect(message.messageType).toBe('IMAGE')
      expect(message.content).toBe('')
      expect(message.fileUrl).toBeTruthy()
      expect(message.fileName).toBeTruthy()
      expect(message.fileMimeType).toBe('image/jpeg')
      expect(message.fileMetadata).toBeDefined()
      expect(message.fileMetadata.width).toBeGreaterThan(0)
      expect(message.fileMetadata.height).toBeGreaterThan(0)
    })
  })

  describe('ReviewFactory', () => {
    it('应该创建有效的评价数据', () => {
      const review = ReviewFactory.create()
      
      expect(review).toBeDefined()
      expect(review.rating).toBeGreaterThanOrEqual(1)
      expect(review.rating).toBeLessThanOrEqual(5)
      expect(review.content).toBeTruthy()
      expect(['BUYER_TO_SELLER', 'SELLER_TO_BUYER']).toContain(review.type)
    })

    it('应该创建正面评价', () => {
      const review = ReviewFactory.createPositive()
      
      expect(review.rating).toBeGreaterThanOrEqual(4)
      expect(review.content).toBeTruthy()
    })

    it('应该创建负面评价', () => {
      const review = ReviewFactory.createNegative()
      
      expect(review.rating).toBeLessThanOrEqual(2)
      expect(review.content).toBeTruthy()
    })
  })

  describe('FeedbackFactory', () => {
    it('应该创建有效的反馈数据', () => {
      const feedback = FeedbackFactory.create()
      
      expect(feedback).toBeDefined()
      expect(['BUG_REPORT', 'IMPROVEMENT', 'APPEAL']).toContain(feedback.category)
      expect(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']).toContain(feedback.status)
      expect(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).toContain(feedback.priority)
      expect(feedback.title).toBeTruthy()
      expect(feedback.description).toBeTruthy()
      expect(feedback.contactEmail).toContain('@')
    })

    it('应该创建申诉反馈', () => {
      const feedback = FeedbackFactory.createAppeal()
      
      expect(feedback.category).toBe('APPEAL')
      expect(['HIGH', 'URGENT']).toContain(feedback.priority)
    })

    it('应该创建Bug报告', () => {
      const feedback = FeedbackFactory.createBugReport()
      
      expect(feedback.category).toBe('BUG_REPORT')
      expect(feedback.title).toBeTruthy()
    })
  })

  describe('DataGenerator', () => {
    it('应该生成完整的用户场景', () => {
      const scenario = DataGenerator.generateCompleteUserScenario()
      
      expect(scenario.users.buyer).toBeDefined()
      expect(scenario.users.seller).toBeDefined()
      expect(scenario.users.admin).toBeDefined()
      expect(scenario.products).toHaveLength(5)
      expect(scenario.orders).toHaveLength(3)
      expect(scenario.messages).toHaveLength(10)
      expect(scenario.reviews).toHaveLength(2)
    })

    it('应该生成市场数据', () => {
      const marketData = DataGenerator.generateMarketplaceData(10, 20)
      
      expect(marketData.users).toHaveLength(10)
      expect(marketData.products).toHaveLength(20)
      expect(marketData.orders.length).toBeGreaterThan(0)
    })
  })

  describe('数据一致性测试', () => {
    it('应该生成唯一的ID', () => {
      const users = UserFactory.createBatch(10)
      const ids = users.map(user => user.id)
      const uniqueIds = [...new Set(ids)]
      
      expect(uniqueIds).toHaveLength(ids.length)
    })

    it('应该生成有效的邮箱地址', () => {
      const users = UserFactory.createBatch(5)
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      
      users.forEach(user => {
        expect(emailRegex.test(user.email)).toBe(true)
      })
    })

    it('应该生成合理的价格范围', () => {
      const products = ProductFactory.createBatch(10)
      
      products.forEach(product => {
        expect(product.price).toBeGreaterThan(0)
        expect(product.price).toBeLessThan(100000)
      })
    })
  })
})
