import { faker } from '@faker-js/faker'

// 用户数据工厂
export class UserFactory {
  static create(overrides: Partial<any> = {}) {
    const userId = `user-${faker.string.alphanumeric(12)}`
    return {
      id: faker.string.uuid(),
      userId,
      name: faker.person.fullName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      avatar: faker.image.avatar(),
      binanceUid: faker.string.numeric(9),
      bnbWalletAddress: `0x${faker.string.hexadecimal({ length: 40, prefix: '' })}`,
      city: faker.location.city(),
      district: faker.location.county(),
      creditScore: faker.number.int({ min: 0, max: 100 }),
      depositBalance: faker.number.float({ min: 0, max: 10000, fractionDigits: 2 }),
      status: faker.helpers.arrayElement(['ACTIVE', 'SUSPENDED', 'BANNED']),
      role: faker.helpers.arrayElement(['USER', 'ADMIN']),
      riskLevel: faker.helpers.arrayElement(['NORMAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createBuyer(overrides: Partial<any> = {}) {
    return this.create({
      role: 'USER',
      creditScore: faker.number.int({ min: 70, max: 100 }),
      ...overrides
    })
  }

  static createSeller(overrides: Partial<any> = {}) {
    return this.create({
      role: 'USER',
      creditScore: faker.number.int({ min: 80, max: 100 }),
      depositBalance: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
      ...overrides
    })
  }

  static createAdmin(overrides: Partial<any> = {}) {
    return this.create({
      role: 'ADMIN',
      creditScore: 100,
      status: 'ACTIVE',
      ...overrides
    })
  }

  static createBatch(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.create(overrides))
  }
}

// 商品数据工厂
export class ProductFactory {
  static create(overrides: Partial<any> = {}) {
    const categories = ['ELECTRONICS', 'CLOTHING', 'BOOKS', 'HOME', 'SPORTS', 'BEAUTY']
    const conditions = ['NEW', 'USED_LIKE_NEW', 'USED_GOOD', 'USED_FAIR']
    
    return {
      id: faker.string.uuid(),
      title: faker.commerce.productName(),
      description: faker.commerce.productDescription(),
      images: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => 
        `/uploads/products/${faker.string.uuid()}.jpg`
      ).join(','),
      price: faker.number.float({ min: 10, max: 10000, fractionDigits: 2 }),
      city: faker.location.city(),
      district: faker.location.county(),
      stock: faker.number.int({ min: 1, max: 100 }),
      status: faker.helpers.arrayElement(['AVAILABLE', 'SOLD_OUT', 'DRAFT']),
      category: faker.helpers.arrayElement(categories),
      condition: faker.helpers.arrayElement(conditions),
      shippingFrom: faker.location.city(),
      reviewStatus: faker.helpers.arrayElement(['PENDING', 'APPROVED', 'REJECTED']),
      isDemandGenerated: faker.datatype.boolean(),
      hasVariants: faker.datatype.boolean(),
      sellerId: faker.string.uuid(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createAvailable(overrides: Partial<any> = {}) {
    return this.create({
      status: 'AVAILABLE',
      reviewStatus: 'APPROVED',
      stock: faker.number.int({ min: 1, max: 50 }),
      ...overrides
    })
  }

  static createPending(overrides: Partial<any> = {}) {
    return this.create({
      status: 'AVAILABLE',
      reviewStatus: 'PENDING',
      ...overrides
    })
  }

  static createWithVariants(overrides: Partial<any> = {}) {
    return this.create({
      hasVariants: true,
      ...overrides
    })
  }

  static createBatch(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.create(overrides))
  }
}

// 订单数据工厂
export class OrderFactory {
  static create(overrides: Partial<any> = {}) {
    const statuses = ['PENDING_PAYMENT', 'PAID', 'SHIPPED', 'COMPLETED', 'CANCELLED', 'REFUNDED']
    const paymentMethods = ['BINANCE_PAY', 'BNB_CHAIN']
    const escrowStatuses = ['PENDING', 'FUNDED', 'RELEASED', 'REFUNDED']
    
    return {
      id: faker.string.uuid(),
      orderNumber: `ORD-${faker.string.alphanumeric(8).toUpperCase()}`,
      status: faker.helpers.arrayElement(statuses),
      totalAmount: faker.number.float({ min: 10, max: 5000, fractionDigits: 2 }),
      productPrice: faker.number.float({ min: 10, max: 5000, fractionDigits: 2 }),
      shippingFee: faker.number.float({ min: 0, max: 50, fractionDigits: 2 }),
      platformFee: faker.number.float({ min: 0, max: 100, fractionDigits: 2 }),
      paymentMethod: faker.helpers.arrayElement([null, ...paymentMethods]),
      paymentScreenshot: faker.datatype.boolean() ? `/uploads/payment/${faker.string.uuid()}.jpg` : null,
      paymentTxHash: faker.datatype.boolean() ? `0x${faker.string.hexadecimal({ length: 64, prefix: '' })}` : null,
      paymentConfirmed: faker.datatype.boolean(),
      escrowStatus: faker.helpers.arrayElement(escrowStatuses),
      escrowAmount: faker.number.float({ min: 10, max: 5000, fractionDigits: 2 }),
      shippingAddress: {
        name: faker.person.fullName(),
        phone: faker.phone.number(),
        province: faker.location.state(),
        city: faker.location.city(),
        district: faker.location.county(),
        detail: faker.location.streetAddress()
      },
      trackingNumber: faker.datatype.boolean() ? faker.string.alphanumeric(12).toUpperCase() : null,
      shippingCompany: faker.datatype.boolean() ? faker.helpers.arrayElement(['顺丰', '圆通', '中通', '申通']) : null,
      receivedAt: faker.datatype.boolean() ? faker.date.recent() : null,
      autoConfirmAt: faker.date.future(),
      refundReason: faker.datatype.boolean() ? faker.lorem.sentence() : null,
      refundAmount: faker.datatype.boolean() ? faker.number.float({ min: 10, max: 1000, fractionDigits: 2 }) : null,
      productId: faker.string.uuid(),
      buyerId: faker.string.uuid(),
      sellerId: faker.string.uuid(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createPending(overrides: Partial<any> = {}) {
    return this.create({
      status: 'PENDING_PAYMENT',
      paymentMethod: null,
      paymentConfirmed: false,
      escrowStatus: 'PENDING',
      ...overrides
    })
  }

  static createPaid(overrides: Partial<any> = {}) {
    return this.create({
      status: 'PAID',
      paymentMethod: faker.helpers.arrayElement(['BINANCE_PAY', 'BNB_CHAIN']),
      paymentConfirmed: true,
      escrowStatus: 'FUNDED',
      paymentTxHash: `0x${faker.string.hexadecimal({ length: 64, prefix: '' })}`,
      ...overrides
    })
  }

  static createCompleted(overrides: Partial<any> = {}) {
    return this.create({
      status: 'COMPLETED',
      paymentMethod: faker.helpers.arrayElement(['BINANCE_PAY', 'BNB_CHAIN']),
      paymentConfirmed: true,
      escrowStatus: 'RELEASED',
      receivedAt: faker.date.recent(),
      trackingNumber: faker.string.alphanumeric(12).toUpperCase(),
      shippingCompany: faker.helpers.arrayElement(['顺丰', '圆通', '中通', '申通']),
      ...overrides
    })
  }

  static createBatch(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.create(overrides))
  }
}

// 消息数据工厂
export class MessageFactory {
  static create(overrides: Partial<any> = {}) {
    const messageTypes = ['TEXT', 'IMAGE', 'VIDEO', 'FILE', 'SYSTEM']
    const statuses = ['SENT', 'DELIVERED', 'READ']
    
    return {
      id: faker.string.uuid(),
      content: faker.lorem.sentence(),
      messageType: faker.helpers.arrayElement(messageTypes),
      status: faker.helpers.arrayElement(statuses),
      fileUrl: faker.datatype.boolean() ? `/uploads/chat/${faker.string.uuid()}.jpg` : null,
      fileName: faker.datatype.boolean() ? faker.system.fileName() : null,
      fileSize: faker.datatype.boolean() ? faker.number.int({ min: 1024, max: 10485760 }) : null,
      fileMimeType: faker.datatype.boolean() ? faker.system.mimeType() : null,
      fileMetadata: faker.datatype.boolean() ? {
        width: faker.number.int({ min: 100, max: 1920 }),
        height: faker.number.int({ min: 100, max: 1080 })
      } : null,
      orderId: faker.string.uuid(),
      senderId: faker.string.uuid(),
      receiverId: faker.string.uuid(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createText(overrides: Partial<any> = {}) {
    return this.create({
      messageType: 'TEXT',
      content: faker.lorem.sentence(),
      fileUrl: null,
      fileName: null,
      fileSize: null,
      fileMimeType: null,
      fileMetadata: null,
      ...overrides
    })
  }

  static createImage(overrides: Partial<any> = {}) {
    return this.create({
      messageType: 'IMAGE',
      content: '',
      fileUrl: `/uploads/chat/${faker.string.uuid()}.jpg`,
      fileName: `${faker.string.uuid()}.jpg`,
      fileSize: faker.number.int({ min: 10240, max: 5242880 }),
      fileMimeType: 'image/jpeg',
      fileMetadata: {
        width: faker.number.int({ min: 100, max: 1920 }),
        height: faker.number.int({ min: 100, max: 1080 })
      },
      ...overrides
    })
  }

  static createBatch(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.create(overrides))
  }
}

// 评价数据工厂
export class ReviewFactory {
  static create(overrides: Partial<any> = {}) {
    const types = ['BUYER_TO_SELLER', 'SELLER_TO_BUYER']
    
    return {
      id: faker.string.uuid(),
      rating: faker.number.int({ min: 1, max: 5 }),
      content: faker.lorem.paragraph(),
      images: faker.datatype.boolean() ? 
        Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => 
          `/uploads/reviews/${faker.string.uuid()}.jpg`
        ) : null,
      type: faker.helpers.arrayElement(types),
      orderId: faker.string.uuid(),
      productId: faker.string.uuid(),
      reviewerId: faker.string.uuid(),
      revieweeId: faker.string.uuid(),
      canReviewUntil: faker.date.future(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createPositive(overrides: Partial<any> = {}) {
    return this.create({
      rating: faker.number.int({ min: 4, max: 5 }),
      content: faker.helpers.arrayElement([
        '商品质量很好，卖家服务态度也很棒！',
        '物流很快，包装完好，非常满意！',
        '商品和描述一致，推荐购买！'
      ]),
      ...overrides
    })
  }

  static createNegative(overrides: Partial<any> = {}) {
    return this.create({
      rating: faker.number.int({ min: 1, max: 2 }),
      content: faker.helpers.arrayElement([
        '商品质量不如预期，有些失望',
        '物流太慢了，包装也不够仔细',
        '商品和描述不符，不推荐'
      ]),
      ...overrides
    })
  }

  static createBatch(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.create(overrides))
  }
}

// 反馈数据工厂
export class FeedbackFactory {
  static create(overrides: Partial<any> = {}) {
    const categories = ['BUG_REPORT', 'IMPROVEMENT', 'APPEAL']
    const statuses = ['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']
    const priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT']
    
    return {
      id: faker.string.uuid(),
      userId: faker.string.uuid(),
      category: faker.helpers.arrayElement(categories),
      title: faker.lorem.sentence(),
      description: faker.lorem.paragraphs(2),
      attachments: faker.datatype.boolean() ? 
        Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
          filename: faker.system.fileName(),
          path: `/uploads/feedback/${faker.string.uuid()}.jpg`
        })) : null,
      status: faker.helpers.arrayElement(statuses),
      priority: faker.helpers.arrayElement(priorities),
      assignedToId: faker.datatype.boolean() ? faker.string.uuid() : null,
      adminResponse: faker.datatype.boolean() ? faker.lorem.paragraph() : null,
      resolvedAt: faker.datatype.boolean() ? faker.date.recent() : null,
      contactEmail: faker.internet.email(),
      contactPhone: faker.phone.number(),
      userAgent: faker.internet.userAgent(),
      browserInfo: {
        name: faker.helpers.arrayElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
        version: faker.system.semver()
      },
      pageUrl: faker.internet.url(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createAppeal(overrides: Partial<any> = {}) {
    return this.create({
      category: 'APPEAL',
      priority: faker.helpers.arrayElement(['HIGH', 'URGENT']),
      title: faker.helpers.arrayElement([
        '订单争议申诉',
        '商品质量问题申诉',
        '卖家服务问题申诉',
        '退款申请'
      ]),
      ...overrides
    })
  }

  static createBugReport(overrides: Partial<any> = {}) {
    return this.create({
      category: 'BUG_REPORT',
      title: faker.helpers.arrayElement([
        '页面加载异常',
        '支付功能故障',
        '图片上传失败',
        '消息发送失败'
      ]),
      ...overrides
    })
  }

  static createBatch(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.create(overrides))
  }
}

// 综合数据生成器
export class DataGenerator {
  static generateCompleteUserScenario() {
    const buyer = UserFactory.createBuyer()
    const seller = UserFactory.createSeller()
    const admin = UserFactory.createAdmin()
    
    const products = ProductFactory.createBatch(5, { sellerId: seller.id })
    const orders = OrderFactory.createBatch(3, { 
      buyerId: buyer.id, 
      sellerId: seller.id,
      productId: products[0].id 
    })
    const messages = MessageFactory.createBatch(10, {
      orderId: orders[0].id,
      senderId: buyer.id,
      receiverId: seller.id
    })
    const reviews = ReviewFactory.createBatch(2, {
      orderId: orders[0].id,
      productId: products[0].id,
      reviewerId: buyer.id,
      revieweeId: seller.id
    })
    
    return {
      users: { buyer, seller, admin },
      products,
      orders,
      messages,
      reviews
    }
  }

  static generateMarketplaceData(userCount: number = 50, productCount: number = 200) {
    const users = UserFactory.createBatch(userCount)
    const sellers = users.filter(u => Math.random() > 0.7) // 30% are sellers
    const buyers = users.filter(u => !sellers.includes(u))
    
    const products = ProductFactory.createBatch(productCount, {
      sellerId: () => faker.helpers.arrayElement(sellers).id
    })
    
    const orders = OrderFactory.createBatch(productCount * 0.3, {
      buyerId: () => faker.helpers.arrayElement(buyers).id,
      sellerId: () => faker.helpers.arrayElement(sellers).id,
      productId: () => faker.helpers.arrayElement(products).id
    })
    
    return { users, products, orders }
  }
}
