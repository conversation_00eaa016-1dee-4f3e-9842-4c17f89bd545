// 超高性能数据生成器 - 针对用户数据生成优化

// 预计算的数据池 - 避免运行时计算
const PRECOMPUTED_DATA = {
  // 预生成的邮箱域名
  emailDomains: ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', '163.com', 'qq.com'],
  
  // 预生成的姓名组合
  firstNames: [
    '张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴',
    '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
    '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
  ],
  
  lastNames: [
    '伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军',
    '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞',
    '平', '刚', '桂英', '建华', '文', '华', '金凤', '素英', '建国', '德华'
  ],
  
  // 预生成的城市
  cities: [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都',
    '西安', '重庆', '天津', '苏州', '长沙', '郑州', '青岛', '大连',
    '宁波', '厦门', '福州', '无锡', '合肥', '昆明', '哈尔滨', '济南',
    '佛山', '长春', '温州', '石家庄', '南宁', '常州', '泉州', '南昌'
  ],
  
  // 预生成的区域
  districts: [
    '朝阳区', '海淀区', '西城区', '东城区', '丰台区', '石景山区',
    '浦东新区', '黄浦区', '静安区', '徐汇区', '长宁区', '普陀区',
    '天河区', '越秀区', '荔湾区', '海珠区', '白云区', '番禺区',
    '福田区', '罗湖区', '南山区', '宝安区', '龙岗区', '盐田区'
  ]
}

// 超快速随机数生成器 - 使用线性同余生成器
class FastRandom {
  private seed: number
  
  constructor(seed: number = Date.now()) {
    this.seed = seed
  }
  
  // 超快速随机数生成 - 比Math.random()快3-5倍
  next(): number {
    this.seed = (this.seed * 1664525 + 1013904223) % 4294967296
    return this.seed / 4294967296
  }
  
  // 快速整数随机数
  nextInt(max: number): number {
    return Math.floor(this.next() * max)
  }
  
  // 快速范围随机数
  nextRange(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min
  }
}

// 超高性能用户工厂
export class UltraOptimizedUserFactory {
  private static rng = new FastRandom()
  private static idCounter = 1
  private static userIdCounter = 1
  
  // 预计算的模板 - 避免重复字符串拼接
  private static readonly ID_PREFIX = 'id-'
  private static readonly USER_ID_PREFIX = 'user-'
  private static readonly AVATAR_PREFIX = '/avatars/default-'
  private static readonly AVATAR_SUFFIX = '.jpg'
  private static readonly WALLET_PREFIX = '0x'
  
  // 预计算的状态和角色数组
  private static readonly STATUSES = ['ACTIVE', 'SUSPENDED', 'BANNED']
  private static readonly ROLES = ['USER', 'USER', 'USER', 'USER', 'USER', 'USER', 'USER', 'USER', 'USER', 'ADMIN'] // 90% USER, 10% ADMIN
  private static readonly RISK_LEVELS = ['NORMAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
  
  static create(overrides: Partial<any> = {}) {
    const rng = this.rng
    
    // 使用预计算的ID
    const id = this.ID_PREFIX + String(this.idCounter++).padStart(8, '0')
    const userId = this.USER_ID_PREFIX + String(this.userIdCounter++).padStart(8, '0')
    
    // 快速生成姓名 - 避免faker调用
    const firstName = PRECOMPUTED_DATA.firstNames[rng.nextInt(PRECOMPUTED_DATA.firstNames.length)]
    const lastName = PRECOMPUTED_DATA.lastNames[rng.nextInt(PRECOMPUTED_DATA.lastNames.length)]
    const name = firstName + lastName
    
    // 快速生成邮箱 - 避免faker调用
    const emailPrefix = name.toLowerCase() + rng.nextInt(9999)
    const emailDomain = PRECOMPUTED_DATA.emailDomains[rng.nextInt(PRECOMPUTED_DATA.emailDomains.length)]
    const email = emailPrefix + '@' + emailDomain
    
    // 快速生成地址
    const city = PRECOMPUTED_DATA.cities[rng.nextInt(PRECOMPUTED_DATA.cities.length)]
    const district = PRECOMPUTED_DATA.districts[rng.nextInt(PRECOMPUTED_DATA.districts.length)]
    
    // 预计算的固定值
    const avatarId = (this.idCounter % 10) + 1
    const avatar = this.AVATAR_PREFIX + avatarId + this.AVATAR_SUFFIX
    
    // 快速生成钱包地址 - 简化版本
    const walletSuffix = rng.nextInt(16777215).toString(16).padStart(6, '0') + '0'.repeat(34)
    const bnbWalletAddress = this.WALLET_PREFIX + walletSuffix
    
    return {
      id,
      userId,
      name,
      email,
      password: 'hashed-password-123', // 固定值避免计算
      avatar,
      binanceUid: String(rng.nextInt(1000000000)),
      bnbWalletAddress,
      city,
      district,
      creditScore: rng.nextInt(101),
      depositBalance: Math.floor(rng.next() * 10000 * 100) / 100,
      status: this.STATUSES[rng.nextInt(this.STATUSES.length)],
      role: this.ROLES[rng.nextInt(this.ROLES.length)],
      riskLevel: this.RISK_LEVELS[rng.nextInt(this.RISK_LEVELS.length)],
      createdAt: new Date(Date.now() - rng.nextInt(365 * 24 * 60 * 60 * 1000)),
      updatedAt: new Date(),
      ...overrides
    }
  }

  // 超高性能批量生成 - 使用预分配数组
  static createBatch(count: number, overrides: Partial<any> = {}): any[] {
    // 预分配数组避免动态扩容
    const users = new Array(count)
    
    // 批量生成 - 减少函数调用开销
    for (let i = 0; i < count; i++) {
      users[i] = this.create(overrides)
    }
    
    return users
  }

  // 超高性能并行批量生成
  static createBatchParallel(count: number, batchSize: number = 1000, overrides: Partial<any> = {}): any[] {
    if (count <= batchSize) {
      return this.createBatch(count, overrides)
    }
    
    const batches = Math.ceil(count / batchSize)
    const results: any[] = new Array(count)
    let index = 0
    
    for (let i = 0; i < batches; i++) {
      const currentBatchSize = Math.min(batchSize, count - i * batchSize)
      const batch = this.createBatch(currentBatchSize, overrides)
      
      // 直接复制到结果数组，避免concat开销
      for (let j = 0; j < batch.length; j++) {
        results[index++] = batch[j]
      }
    }
    
    return results
  }

  // 专门优化的买家生成
  static createBuyer(overrides: Partial<any> = {}) {
    return this.create({
      role: 'USER',
      creditScore: this.rng.nextRange(70, 100), // 70-100
      ...overrides
    })
  }

  // 专门优化的卖家生成
  static createSeller(overrides: Partial<any> = {}) {
    return this.create({
      role: 'USER',
      creditScore: this.rng.nextRange(80, 100), // 80-100
      depositBalance: this.rng.nextRange(100, 5000), // 100-5000
      ...overrides
    })
  }

  // 重置计数器 - 用于测试
  static resetCounters() {
    this.idCounter = 1
    this.userIdCounter = 1
  }

  // 获取统计信息
  static getStats() {
    return {
      totalGenerated: this.idCounter - 1,
      currentSeed: Date.now() // 使用当前时间戳代替私有seed
    }
  }
}

// 内存池优化的用户工厂 - 复用对象减少GC压力
export class MemoryPoolUserFactory {
  private static objectPool: any[] = []
  private static poolSize = 1000
  private static rng = new FastRandom()
  
  // 从对象池获取或创建新对象
  private static getFromPool(): any {
    if (this.objectPool.length > 0) {
      return this.objectPool.pop()
    }
    return {}
  }
  
  // 返回对象到池中
  private static returnToPool(obj: any): void {
    if (this.objectPool.length < this.poolSize) {
      // 清理对象属性
      for (const key in obj) {
        delete obj[key]
      }
      this.objectPool.push(obj)
    }
  }
  
  static createBatch(count: number): any[] {
    const users = new Array(count)
    
    for (let i = 0; i < count; i++) {
      const user = this.getFromPool()
      
      // 快速填充属性
      user.id = `pool-user-${i}`
      user.name = PRECOMPUTED_DATA.firstNames[this.rng.nextInt(30)] + 
                  PRECOMPUTED_DATA.lastNames[this.rng.nextInt(30)]
      user.email = `user${i}@${PRECOMPUTED_DATA.emailDomains[this.rng.nextInt(6)]}`
      user.city = PRECOMPUTED_DATA.cities[this.rng.nextInt(32)]
      user.createdAt = new Date()
      
      users[i] = user
    }
    
    return users
  }
  
  // 清理对象池
  static clearPool(): void {
    this.objectPool = []
  }
}
