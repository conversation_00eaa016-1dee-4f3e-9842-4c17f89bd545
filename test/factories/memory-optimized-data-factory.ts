// 内存优化的数据工厂 - 专注于最小内存占用

// 轻量级数据池 - 只保存最必要的数据
const LIGHTWEIGHT_DATA = {
  // 精简的邮箱域名
  domains: ['gmail.com', 'qq.com', '163.com'],
  
  // 精简的姓名
  names: ['张伟', '李娜', '王强', '刘敏', '陈杰'],
  
  // 精简的城市
  cities: ['北京', '上海', '广州', '深圳'],
  
  // 精简的状态
  statuses: ['ACTIVE', 'SUSPENDED'],
  roles: ['USER', 'ADMIN']
}

// 内存优化的随机数生成器
class MemoryEfficientRandom {
  private seed: number
  
  constructor(seed: number = Date.now() % 1000000) {
    this.seed = seed
  }
  
  // 轻量级随机数生成
  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280
    return this.seed / 233280
  }
  
  nextInt(max: number): number {
    return Math.floor(this.next() * max)
  }
}

// 对象池 - 复用对象减少GC压力
class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn: (obj: T) => void
  private maxSize: number
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, maxSize: number = 100) {
    this.createFn = createFn
    this.resetFn = resetFn
    this.maxSize = maxSize
  }
  
  get(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!
    }
    return this.createFn()
  }
  
  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.resetFn(obj)
      this.pool.push(obj)
    }
  }
  
  clear(): void {
    this.pool.length = 0
  }
  
  size(): number {
    return this.pool.length
  }
}

// 内存优化的用户工厂
export class MemoryOptimizedUserFactory {
  private static rng = new MemoryEfficientRandom()
  private static idCounter = 1
  
  // 用户对象池
  private static userPool = new ObjectPool(
    () => ({}),
    (obj: any) => {
      // 清理对象属性
      for (const key in obj) {
        delete obj[key]
      }
    },
    50 // 限制池大小
  )
  
  static create(overrides: Partial<any> = {}): any {
    const user: any = this.userPool.get()
    const rng = this.rng

    // 使用最小的数据结构
    user.id = this.idCounter++
    user.name = LIGHTWEIGHT_DATA.names[rng.nextInt(5)]
    user.email = `u${user.id}@${LIGHTWEIGHT_DATA.domains[rng.nextInt(3)]}`
    user.city = LIGHTWEIGHT_DATA.cities[rng.nextInt(4)]
    user.status = LIGHTWEIGHT_DATA.statuses[rng.nextInt(2)]
    user.role = LIGHTWEIGHT_DATA.roles[rng.nextInt(2)]
    user.score = rng.nextInt(101)
    
    // 应用覆盖
    Object.assign(user, overrides)
    
    return user
  }
  
  static createBatch(count: number, overrides: Partial<any> = {}): any[] {
    const users = new Array(count)
    for (let i = 0; i < count; i++) {
      users[i] = this.create(overrides)
    }
    return users
  }
  
  // 释放用户对象回池中
  static release(user: any): void {
    this.userPool.release(user)
  }
  
  // 批量释放
  static releaseBatch(users: any[]): void {
    for (const user of users) {
      this.release(user)
    }
  }
  
  // 清理池
  static clearPool(): void {
    this.userPool.clear()
  }
  
  // 获取池状态
  static getPoolStats() {
    return {
      poolSize: this.userPool.size(),
      totalGenerated: this.idCounter - 1
    }
  }
  
  static resetCounters(): void {
    this.idCounter = 1
    this.clearPool()
  }
}

// 流式数据生成器 - 避免一次性创建大量对象
export class StreamingDataGenerator {
  private batchSize: number
  private currentBatch: any[] = []
  private totalGenerated: number = 0
  
  constructor(batchSize: number = 1000) {
    this.batchSize = batchSize
  }
  
  // 生成器函数 - 按批次生成数据
  *generateUsers(totalCount: number): Generator<any[], void, unknown> {
    let remaining = totalCount
    
    while (remaining > 0) {
      const currentBatchSize = Math.min(this.batchSize, remaining)
      
      // 清理上一批次
      if (this.currentBatch.length > 0) {
        MemoryOptimizedUserFactory.releaseBatch(this.currentBatch)
        this.currentBatch.length = 0
      }
      
      // 生成当前批次
      this.currentBatch = MemoryOptimizedUserFactory.createBatch(currentBatchSize)
      this.totalGenerated += currentBatchSize
      remaining -= currentBatchSize
      
      yield this.currentBatch
      
      // 强制垃圾回收（如果可用）
      if (global.gc && this.totalGenerated % (this.batchSize * 5) === 0) {
        global.gc()
      }
    }
    
    // 最终清理
    if (this.currentBatch.length > 0) {
      MemoryOptimizedUserFactory.releaseBatch(this.currentBatch)
      this.currentBatch.length = 0
    }
  }
  
  // 异步批量处理
  async processUsersInBatches(
    totalCount: number,
    processor: (batch: any[]) => Promise<void> | void
  ): Promise<void> {
    for (const batch of this.generateUsers(totalCount)) {
      await processor(batch)
      
      // 给事件循环一个机会
      await new Promise(resolve => setImmediate(resolve))
    }
  }
  
  getTotalGenerated(): number {
    return this.totalGenerated
  }
  
  reset(): void {
    if (this.currentBatch.length > 0) {
      MemoryOptimizedUserFactory.releaseBatch(this.currentBatch)
      this.currentBatch.length = 0
    }
    this.totalGenerated = 0
  }
}

// 内存监控的数据生成器
export class MonitoredDataGenerator {
  private memoryThreshold: number // MB
  private checkInterval: number
  private lastMemoryCheck: number = 0
  
  constructor(memoryThresholdMB: number = 100, checkInterval: number = 1000) {
    this.memoryThreshold = memoryThresholdMB * 1024 * 1024 // 转换为字节
    this.checkInterval = checkInterval
  }
  
  // 检查内存使用
  private checkMemoryUsage(): { usage: NodeJS.MemoryUsage; shouldPause: boolean } {
    const usage = process.memoryUsage()
    const shouldPause = usage.heapUsed > this.memoryThreshold
    
    return { usage, shouldPause }
  }
  
  // 等待内存释放
  private async waitForMemoryRelease(): Promise<void> {
    console.log('⚠️  内存使用过高，等待垃圾回收...')
    
    // 强制垃圾回收
    if (global.gc) {
      global.gc()
    }
    
    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 再次检查
    const { shouldPause } = this.checkMemoryUsage()
    if (shouldPause) {
      console.log('⚠️  内存仍然过高，继续等待...')
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }
  
  // 内存安全的批量生成
  async generateUsersWithMemoryControl(count: number): Promise<any[]> {
    const results: any[] = []
    const batchSize = 1000
    let processed = 0
    
    console.log(`🔄 开始生成 ${count.toLocaleString()} 个用户 (内存监控模式)`)
    
    while (processed < count) {
      const currentBatchSize = Math.min(batchSize, count - processed)
      
      // 检查内存使用
      if (Date.now() - this.lastMemoryCheck > this.checkInterval) {
        const { usage, shouldPause } = this.checkMemoryUsage()
        
        if (shouldPause) {
          await this.waitForMemoryRelease()
        }
        
        this.lastMemoryCheck = Date.now()
        
        // 打印内存状态
        if (processed % (batchSize * 5) === 0) {
          console.log(`📊 进度: ${processed}/${count}, 内存: ${(usage.heapUsed / 1024 / 1024).toFixed(1)}MB`)
        }
      }
      
      // 生成当前批次
      const batch = MemoryOptimizedUserFactory.createBatch(currentBatchSize)
      results.push(...batch)
      processed += currentBatchSize
      
      // 给事件循环一个机会
      if (processed % batchSize === 0) {
        await new Promise(resolve => setImmediate(resolve))
      }
    }
    
    console.log(`✅ 完成生成 ${count.toLocaleString()} 个用户`)
    return results
  }
}

// 内存优化的产品工厂
export class MemoryOptimizedProductFactory {
  private static rng = new MemoryEfficientRandom()
  private static idCounter = 1
  
  // 产品对象池
  private static productPool = new ObjectPool(
    () => ({}),
    (obj: any) => {
      for (const key in obj) {
        delete obj[key]
      }
    },
    30
  )
  
  static create(overrides: Partial<any> = {}): any {
    const product: any = this.productPool.get()
    const rng = this.rng

    product.id = this.idCounter++
    product.name = `商品${product.id}`
    product.price = rng.nextInt(1000) + 10
    product.city = LIGHTWEIGHT_DATA.cities[rng.nextInt(4)]
    product.status = 'AVAILABLE'
    
    Object.assign(product, overrides)
    return product
  }
  
  static createBatch(count: number): any[] {
    const products = new Array(count)
    for (let i = 0; i < count; i++) {
      products[i] = this.create()
    }
    return products
  }
  
  static release(product: any): void {
    this.productPool.release(product)
  }
  
  static releaseBatch(products: any[]): void {
    for (const product of products) {
      this.release(product)
    }
  }
  
  static clearPool(): void {
    this.productPool.clear()
  }
  
  static resetCounters(): void {
    this.idCounter = 1
    this.clearPool()
  }
}
