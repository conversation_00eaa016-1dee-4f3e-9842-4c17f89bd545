import '@testing-library/jest-dom'
import { vi } from 'vitest'

// 全局变量设置 - 检查是否已存在
if (typeof global.TextEncoder === 'undefined') {
  const { TextEncoder, TextDecoder } = require('util')
  global.TextEncoder = TextEncoder
  global.TextDecoder = TextDecoder
}

if (typeof global.ReadableStream === 'undefined') {
  const { ReadableStream } = require('stream/web')
  global.ReadableStream = ReadableStream
}

// 环境变量设置
// Set test environment
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test'
}
process.env.DATABASE_URL = 'file:./test.db'
process.env.NEXTAUTH_SECRET = 'test-secret'
process.env.NEXTAUTH_URL = 'http://localhost:3000'
process.env.JWT_SECRET = 'test-jwt-secret'
process.env.UPLOAD_DIR = './test-uploads'
process.env.MAX_FILE_SIZE = '10485760'
process.env.ALLOWED_FILE_TYPES = 'image/jpeg,image/png,image/gif,image/webp'

// Redis测试配置 - 使用内存模拟
process.env.REDIS_HOST = 'localhost'
process.env.REDIS_PORT = '6379'
process.env.REDIS_DB = '1'
// 不设置REDIS_PASSWORD，让Redis连接失败并回退到内存缓存

// Mock Next.js相关模块
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn()
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
  notFound: vi.fn()
}))

vi.mock('next/headers', () => ({
  cookies: () => ({
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn()
  }),
  headers: () => ({
    get: vi.fn()
  })
}))

// Mock NextAuth
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))

// Mock Socket.io
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    connect: vi.fn(),
    disconnect: vi.fn(),
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    once: vi.fn(),
    connected: true
  }))
}))

// Mock file system operations
vi.mock('fs/promises', () => ({
  writeFile: vi.fn(),
  readFile: vi.fn(),
  mkdir: vi.fn(),
  access: vi.fn(),
  unlink: vi.fn()
}))

// Mock sharp for image processing
vi.mock('sharp', () => {
  const mockSharp = vi.fn(() => ({
    resize: vi.fn().mockReturnThis(),
    jpeg: vi.fn().mockReturnThis(),
    png: vi.fn().mockReturnThis(),
    webp: vi.fn().mockReturnThis(),
    toBuffer: vi.fn().mockResolvedValue(Buffer.from('mock-image-data'))
  }))
  return { default: mockSharp }
})

// Mock QR code generation
vi.mock('qrcode', () => ({
  toDataURL: vi.fn().mockResolvedValue('data:image/png;base64,mock-qr-code')
}))

// Mock bcryptjs
vi.mock('bcryptjs', () => ({
  hash: vi.fn().mockResolvedValue('hashed-password'),
  compare: vi.fn().mockResolvedValue(true)
}))

// Mock jsonwebtoken
vi.mock('jsonwebtoken', () => ({
  sign: vi.fn().mockReturnValue('mock-jwt-token'),
  verify: vi.fn().mockReturnValue({ userId: 'test-user-id' })
}))

// Mock Prisma Client
vi.mock('@prisma/client', () => {
  const mockPrisma = {
    user: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn(),
      aggregate: vi.fn(),
      groupBy: vi.fn()
    },
    product: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn(),
      aggregate: vi.fn(),
      groupBy: vi.fn()
    },
    order: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn(),
      aggregate: vi.fn(),
      groupBy: vi.fn()
    },
    orderItem: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      createMany: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    productVariant: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      createMany: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    message: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    announcement: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn()
    },
    review: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn()
    },
    escrowPayment: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn()
    },
    userSession: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn()
    },
    securityLog: {
      findMany: vi.fn(),
      create: vi.fn(),
      count: vi.fn()
    },
    // 添加缺少的托管相关模型
    arbitrationVote: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    escrowDispute: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    mediatorReward: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn(),
      aggregate: vi.fn()
    },
    escrowOrder: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    blockchainTransaction: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    withdrawalVoucher: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      count: vi.fn()
    },
    $transaction: vi.fn(),
    $disconnect: vi.fn(),
    $connect: vi.fn(),
    $executeRaw: vi.fn(),
    $queryRaw: vi.fn()
  }

  // 设置默认Mock返回值
  mockPrisma.user?.findUnique?.mockResolvedValue(null)
  mockPrisma.user?.findMany?.mockResolvedValue([])
  mockPrisma.user?.count?.mockResolvedValue(0)
  mockPrisma.user?.create?.mockResolvedValue({ id: 'mock-user-id' })

  mockPrisma.product?.findUnique?.mockResolvedValue(null)
  mockPrisma.product?.findMany?.mockResolvedValue([])
  mockPrisma.product?.count?.mockResolvedValue(0)
  mockPrisma.product?.create?.mockResolvedValue({ id: 'mock-product-id' })

  mockPrisma.order?.findUnique?.mockResolvedValue(null)
  mockPrisma.order?.findMany?.mockResolvedValue([])
  mockPrisma.order?.count?.mockResolvedValue(0)
  mockPrisma.order?.create?.mockResolvedValue({ id: 'mock-order-id' })

  mockPrisma.message?.findMany?.mockResolvedValue([])
  mockPrisma.message?.count?.mockResolvedValue(0)
  mockPrisma.message?.create?.mockResolvedValue({ id: 'mock-message-id' })
  mockPrisma.message?.updateMany?.mockResolvedValue({ count: 1 })

  mockPrisma.orderItem?.findMany?.mockResolvedValue([])
  mockPrisma.orderItem?.create?.mockResolvedValue({ id: 'mock-order-item-id' })
  mockPrisma.orderItem?.createMany?.mockResolvedValue({ count: 1 })

  mockPrisma.productVariant?.findMany?.mockResolvedValue([])
  mockPrisma.productVariant?.create?.mockResolvedValue({ id: 'mock-variant-id' })
  mockPrisma.productVariant?.createMany?.mockResolvedValue({ count: 1 })

  mockPrisma.order?.updateMany?.mockResolvedValue({ count: 1 })
  mockPrisma.product?.updateMany?.mockResolvedValue({ count: 1 })

  mockPrisma.escrowPayment?.findMany?.mockResolvedValue([])
  mockPrisma.escrowPayment?.create?.mockResolvedValue({ id: 'mock-escrow-id' })

  // 设置新添加模型的默认返回值
  mockPrisma.arbitrationVote?.findMany?.mockResolvedValue([])
  mockPrisma.arbitrationVote?.create?.mockResolvedValue({ id: 'mock-vote-id' })
  mockPrisma.arbitrationVote?.deleteMany?.mockResolvedValue({ count: 0 })

  mockPrisma.escrowDispute?.findMany?.mockResolvedValue([])
  mockPrisma.escrowDispute?.create?.mockResolvedValue({ id: 'mock-dispute-id' })
  mockPrisma.escrowDispute?.deleteMany?.mockResolvedValue({ count: 0 })

  mockPrisma.mediatorReward?.findMany?.mockResolvedValue([])
  mockPrisma.mediatorReward?.create?.mockResolvedValue({ id: 'mock-reward-id' })
  mockPrisma.mediatorReward?.deleteMany?.mockResolvedValue({ count: 0 })
  mockPrisma.mediatorReward?.aggregate?.mockResolvedValue({
    _sum: { amount: 0 },
    _count: { _all: 0 },
    _avg: { amount: 0 },
    _max: { amount: 0 },
    _min: { amount: 0 }
  })

  mockPrisma.escrowOrder?.findMany?.mockResolvedValue([])
  mockPrisma.escrowOrder?.create?.mockResolvedValue({ id: 'mock-escrow-order-id' })
  mockPrisma.escrowOrder?.deleteMany?.mockResolvedValue({ count: 0 })

  mockPrisma.blockchainTransaction?.findMany?.mockResolvedValue([])
  mockPrisma.blockchainTransaction?.create?.mockResolvedValue({ id: 'mock-tx-id' })
  mockPrisma.blockchainTransaction?.deleteMany?.mockResolvedValue({ count: 0 })

  mockPrisma.withdrawalVoucher?.findMany?.mockResolvedValue([])
  mockPrisma.withdrawalVoucher?.findFirst?.mockResolvedValue(null)
  mockPrisma.withdrawalVoucher?.create?.mockResolvedValue({ id: 'mock-voucher-id' })
  mockPrisma.withdrawalVoucher?.deleteMany?.mockResolvedValue({ count: 0 })

  mockPrisma.$transaction.mockImplementation(async (callback) => {
    if (typeof callback === 'function') {
      return await callback(mockPrisma)
    }
    return []
  })

  mockPrisma.$disconnect.mockResolvedValue(undefined)
  mockPrisma.$connect.mockResolvedValue(undefined)

  return {
    PrismaClient: vi.fn(() => mockPrisma),
    Prisma: {
      PrismaClientKnownRequestError: class extends Error {
        constructor(message: string, code: string) {
          super(message)
          this.name = 'PrismaClientKnownRequestError'
          ;(this as any).code = code
        }
      }
    }
  }
})

// Mock Redis
vi.mock('ioredis', () => {
  const mockRedis = {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    setex: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    mget: vi.fn().mockResolvedValue([]),
    keys: vi.fn().mockResolvedValue([]),
    flushdb: vi.fn().mockResolvedValue('OK'),
    ping: vi.fn().mockResolvedValue('PONG'),
    pipeline: vi.fn(() => ({
      setex: vi.fn().mockReturnThis(),
      exec: vi.fn().mockResolvedValue([])
    })),
    disconnect: vi.fn().mockResolvedValue(undefined)
  }

  return {
    default: vi.fn(() => mockRedis)
  }
})

// Mock 优化查询模块
vi.mock('@/lib/prisma-optimized', () => ({
  optimizedQueries: {
    getProducts: vi.fn().mockResolvedValue({
      products: [],
      total: 0,
      page: 1,
      limit: 10,
      totalPages: 0
    }),
    getProductById: vi.fn().mockResolvedValue(null),
    getUserOrders: vi.fn().mockResolvedValue({
      orders: [],
      total: 0,
      page: 1,
      limit: 10
    })
  },
  optimizedTransactions: {
    createOrder: vi.fn().mockResolvedValue({ id: 'mock-order-id' }),
    updateProduct: vi.fn().mockResolvedValue({ id: 'mock-product-id' })
  }
}))

// Mock API缓存中间件
vi.mock('@/lib/api-cache-middleware', () => ({
  withApiCache: vi.fn((config) => (handler) => handler),
  cacheConfigs: {
    products: { ttl: 300 },
    orders: { ttl: 60 }
  },
  CacheInvalidator: {
    invalidateProducts: vi.fn(),
    invalidateOrders: vi.fn()
  }
}))

// Mock 性能监控
vi.mock('@/lib/performance-monitor', () => ({
  monitor: vi.fn((name, fn) => fn()),
  monitorDbQuery: vi.fn((fn, name, params) => fn())
}))

// Mock 缓存回退
vi.mock('@/lib/cache-fallback', () => ({
  cache: {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue(true),
    del: vi.fn().mockResolvedValue(true)
  },
  CACHE_TTL: {
    SHORT: 300,
    MEDIUM: 900,
    LONG: 3600
  }
}))

// 全局测试钩子
import { beforeEach, afterEach } from 'vitest'

beforeEach(() => {
  // 清理所有mock
  vi.clearAllMocks()
})

afterEach(() => {
  // 清理定时器
  vi.clearAllTimers()
})

// 全局错误处理
process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection:', reason)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
})