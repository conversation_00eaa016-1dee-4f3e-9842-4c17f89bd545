import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createMockSession } from '@/test/test-utils'

// Mock Next.js modules
vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

vi.mock('@/lib/prisma', () => ({
  prisma: {
    giftCard: {
      createMany: vi.fn(),
      findMany: vi.fn(),
      groupBy: vi.fn(),
      count: vi.fn(),
      aggregate: vi.fn(),
      findFirst: vi.fn()
    },
    withdrawalVoucher: {
      createMany: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
      update: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'

// 导入API处理器
import * as batchHandler from '@/app/api/admin/giftcards/batch/route'
import * as statsHandler from '@/app/api/admin/giftcards/stats/route'
import * as vouchersHandler from '@/app/api/admin/vouchers/route'

describe('增强礼品卡管理API测试', () => {
  const mockAdminSession = createMockSession({
    id: 'admin-1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'ADMIN'
  })

  const mockUserSession = createMockSession({
    id: 'user-1',
    name: 'Regular User',
    email: '<EMAIL>',
    role: 'USER'
  })

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('礼品卡批次管理API', () => {
    it('应该成功批量生成礼品卡', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)
      vi.mocked(prisma.$transaction).mockResolvedValue({
        createdCards: { count: 10 },
        cards: Array.from({ length: 10 }, (_, i) => ({
          id: `card-${i}`,
          cardCode: `CARD${i.toString().padStart(4, '0')}`,
          faceValue: 100,
          status: 'GENERATED',
          batchId: 'BATCH_123',
          createdAt: new Date(),
          validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          createdBy: {
            id: 'admin-1',
            name: 'Admin User',
            email: '<EMAIL>'
          }
        }))
      })

      const request = new Request('http://localhost:3000/api/admin/giftcards/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          faceValue: 100,
          quantity: 10,
          validDays: 365,
          batchName: 'Test Batch',
          notes: 'Test batch generation'
        })
      })

      const response = await batchHandler.POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toContain('成功生成 10 张礼品卡')
      expect(data.data.quantity).toBe(10)
      expect(data.data.faceValue).toBe(100)
    })

    it('应该拒绝非管理员用户批量生成礼品卡', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockUserSession)

      const request = new Request('http://localhost:3000/api/admin/giftcards/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          faceValue: 100,
          quantity: 10
        })
      })

      const response = await batchHandler.POST(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.success).toBe(false)
      expect(data.error).toBe('权限不足')
    })

    it('应该验证批量生成参数', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)

      const request = new Request('http://localhost:3000/api/admin/giftcards/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          faceValue: 0,
          quantity: 1001
        })
      })

      const response = await batchHandler.POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('面值必须大于0')
    })

    it('应该获取批次列表', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)
      vi.mocked(prisma.giftCard.groupBy).mockResolvedValue([
        {
          batchId: 'BATCH_123',
          _count: { id: 10 },
          _sum: { faceValue: 1000 },
          _min: { createdAt: new Date() }
        }
      ])

      vi.mocked(prisma.giftCard.findFirst).mockResolvedValue({
        id: 'card-1',
        cardCode: 'CARD0001',
        faceValue: 100,
        status: 'GENERATED',
        batchId: 'BATCH_123',
        validUntil: new Date(),
        notes: 'Test batch',
        createdBy: {
          id: 'admin-1',
          name: 'Admin User',
          email: '<EMAIL>'
        }
      })

      const request = new Request('http://localhost:3000/api/admin/giftcards/batch?page=1&limit=20')

      const response = await batchHandler.GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.batches).toHaveLength(1)
      expect(data.data.batches[0].batchId).toBe('BATCH_123')
    })
  })

  describe('礼品卡统计API', () => {
    it('应该获取礼品卡统计数据', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)
      
      // Mock统计数据
      vi.mocked(prisma.giftCard.count).mockResolvedValue(100)
      vi.mocked(prisma.giftCard.aggregate).mockResolvedValue({
        _sum: { faceValue: 10000 }
      })
      vi.mocked(prisma.giftCard.groupBy).mockResolvedValue([
        {
          status: 'GENERATED',
          _count: { id: 50 },
          _sum: { faceValue: 5000 }
        },
        {
          status: 'SOLD',
          _count: { id: 30 },
          _sum: { faceValue: 3000 }
        },
        {
          status: 'REDEEMED',
          _count: { id: 20 },
          _sum: { faceValue: 2000 }
        }
      ])
      vi.mocked(prisma.giftCard.findMany).mockResolvedValue([])

      const request = new Request('http://localhost:3000/api/admin/giftcards/stats?period=30')

      const response = await statsHandler.GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.overview.totalCards).toBe(100)
      expect(data.data.overview.totalValue).toBe(10000)
      expect(data.data.statusDistribution).toHaveLength(3)
    })

    it('应该拒绝非管理员获取统计数据', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockUserSession)

      const request = new Request('http://localhost:3000/api/admin/giftcards/stats')

      const response = await statsHandler.GET(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.success).toBe(false)
      expect(data.error).toBe('权限不足')
    })
  })

  describe('兑换券管理API', () => {
    it('应该成功创建兑换券', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)
      vi.mocked(prisma.$transaction).mockResolvedValue({
        createdVouchers: { count: 5 }
      })

      const request = new Request('http://localhost:3000/api/admin/vouchers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: 50,
          quantity: 5,
          validDays: 30,
          notes: 'Test vouchers'
        })
      })

      const response = await vouchersHandler.POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toContain('成功生成 5 张兑换券')
      expect(data.data.quantity).toBe(5)
      expect(data.data.amount).toBe(50)
    })

    it('应该获取兑换券列表', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)
      vi.mocked(prisma.withdrawalVoucher.findMany).mockResolvedValue([
        {
          id: 'voucher-1',
          code: 'VOUCHER001',
          amount: 50,
          status: 'ACTIVE',
          validUntil: new Date(),
          createdAt: new Date(),
          usedBy: null,
          notes: 'Test voucher'
        }
      ])
      vi.mocked(prisma.withdrawalVoucher.count).mockResolvedValue(1)

      const request = new Request('http://localhost:3000/api/admin/vouchers?page=1&limit=20')

      const response = await vouchersHandler.GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.vouchers).toHaveLength(1)
      expect(data.data.vouchers[0].code).toBe('VOUCHER001')
    })

    it('应该更新兑换券状态', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)
      vi.mocked(prisma.withdrawalVoucher.update).mockResolvedValue({
        id: 'voucher-1',
        code: 'VOUCHER001',
        amount: 50,
        status: 'DISABLED',
        validUntil: new Date(),
        createdAt: new Date(),
        usedBy: null,
        notes: 'Disabled voucher'
      })

      const request = new Request('http://localhost:3000/api/admin/vouchers', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: 'voucher-1',
          status: 'DISABLED',
          notes: 'Disabled voucher'
        })
      })

      const response = await vouchersHandler.PATCH(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('兑换券更新成功')
      expect(data.data.status).toBe('DISABLED')
    })

    it('应该验证兑换券创建参数', async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockAdminSession)

      const request = new Request('http://localhost:3000/api/admin/vouchers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: 0,
          quantity: 101
        })
      })

      const response = await vouchersHandler.POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('金额必须大于0')
    })
  })
})
