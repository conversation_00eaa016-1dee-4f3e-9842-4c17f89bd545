import { describe, it, expect, beforeEach, vi } from 'vitest'
import { GET as getMessagesHandler, POST as sendMessageHandler } from '@/app/api/messages/route'
import {
  createMockRequest,
  createMockSession,
  mockUsers,
  mockOrders,
  mockMessages
} from '@/test/test-utils'
import { getServerSession } from 'next-auth/next'

// Mock next-auth
vi.mock('next-auth/next')
const mockGetServerSession = vi.mocked(getServerSession)

// Mock Prisma
vi.mock('@/lib/prisma', () => ({
  prisma: {
    message: {
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
      count: vi.fn()
    },
    order: {
      findUnique: vi.fn(),
      findMany: vi.fn()
    },
    user: {
      findUnique: vi.fn()
    }
  }
}))

import { prisma } from '@/lib/prisma'
const mockPrisma = vi.mocked(prisma)

describe('消息API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('GET /api/messages - 获取消息列表', () => {
    it('应该返回订单相关的消息', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const orderWithParticipants = {
        ...mockOrders.pending,
        buyerId: session.user.id,
        sellerId: mockUsers.seller.id
      }

      const messagesList = [
        {
          ...mockMessages.text,
          sender: mockUsers.buyer,
          receiver: mockUsers.seller
        },
        {
          ...mockMessages.image,
          sender: mockUsers.seller,
          receiver: mockUsers.buyer
        }
      ]

      mockPrisma.order.findUnique.mockResolvedValue(orderWithParticipants)
      mockPrisma.message.findMany.mockResolvedValue(messagesList)

      const request = createMockRequest('GET', `/api/messages?orderId=${mockOrders.pending.id}`)
      const response = await getMessagesHandler(request)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('messages')
      expect(responseData.messages).toHaveLength(2)
      expect(responseData.messages[0]).toHaveProperty('content', mockMessages.text.content)
      expect(responseData.messages[1]).toHaveProperty('messageType', 'IMAGE')
      expect(responseData).toHaveProperty('pagination')

      expect(mockPrisma.message.findMany).toHaveBeenCalledWith({
        where: { orderId: mockOrders.pending.id },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'asc' },
        skip: 0,
        take: 50
      })
    })

    it('应该拒绝无关用户访问消息', async () => {
      const session = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(session)

      const orderWithParticipants = {
        ...mockOrders.pending,
        buyerId: mockUsers.buyer.id,
        sellerId: mockUsers.seller.id
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderWithParticipants)

      const request = createMockRequest('GET', `/api/messages?orderId=${mockOrders.pending.id}`)
      const response = await getMessagesHandler(request)

      expect(response.status).toBe(403)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '无权限查看此订单的消息')
    })

    it('应该处理订单不存在的情况', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      mockPrisma.order.findUnique.mockResolvedValue(null)

      const request = createMockRequest('GET', '/api/messages?orderId=non-existent-order')
      const response = await getMessagesHandler(request)

      expect(response.status).toBe(404)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '订单不存在')
    })
  })

  describe('POST /api/messages - 发送消息', () => {
    it('应该成功发送文本消息', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const messageData = {
        orderId: mockOrders.pending.id,
        content: 'Hello, is this item still available?',
        messageType: 'TEXT'
      }

      const orderWithParticipants = {
        ...mockOrders.pending,
        buyerId: session.user.id,
        sellerId: mockUsers.seller.id
      }

      const createdMessage = {
        ...mockMessages.text,
        id: 'new-message-id',
        content: messageData.content,
        senderId: session.user.id,
        receiverId: mockUsers.seller.id,
        sender: mockUsers.buyer,
        receiver: mockUsers.seller
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderWithParticipants)
      mockPrisma.message.create.mockResolvedValue(createdMessage)

      const request = createMockRequest('POST', '/api/messages', messageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(201)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('id', 'new-message-id')
      expect(responseData).toHaveProperty('content', messageData.content)
      expect(responseData).toHaveProperty('messageType', 'TEXT')

      expect(mockPrisma.message.create).toHaveBeenCalledWith({
        data: {
          orderId: messageData.orderId,
          content: messageData.content,
          messageType: 'TEXT',
          senderId: session.user.id,
          receiverId: mockUsers.seller.id,
          status: 'SENT',
          fileUrl: undefined,
          fileName: undefined,
          fileSize: undefined,
          fileMimeType: undefined,
          fileMetadata: undefined
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })
    })

    it('应该成功发送图片消息', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)

      const messageData = {
        orderId: mockOrders.pending.id,
        content: '',
        messageType: 'IMAGE',
        fileUrl: '/uploads/chat/image.jpg',
        fileName: 'product-image.jpg',
        fileSize: 2048,
        fileMimeType: 'image/jpeg',
        fileMetadata: { width: 800, height: 600 }
      }

      const orderWithParticipants = {
        ...mockOrders.pending,
        buyerId: mockUsers.buyer.id,
        sellerId: session.user.id
      }

      const createdMessage = {
        ...mockMessages.image,
        id: 'new-image-message-id',
        senderId: session.user.id,
        receiverId: mockUsers.buyer.id,
        fileUrl: messageData.fileUrl,
        fileName: messageData.fileName,
        fileSize: messageData.fileSize,
        fileMimeType: messageData.fileMimeType,
        fileMetadata: messageData.fileMetadata,
        sender: mockUsers.seller,
        receiver: mockUsers.buyer
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderWithParticipants)
      mockPrisma.message.create.mockResolvedValue(createdMessage)

      const request = createMockRequest('POST', '/api/messages', messageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(201)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('id', 'new-image-message-id')
      expect(responseData).toHaveProperty('messageType', 'IMAGE')
      expect(responseData).toHaveProperty('fileUrl', messageData.fileUrl)
      expect(responseData).toHaveProperty('fileName', messageData.fileName)
    })

    it('应该验证文本消息的内容', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const invalidMessageData = {
        orderId: mockOrders.pending.id,
        content: '', // 空内容
        messageType: 'TEXT'
      }

      const request = createMockRequest('POST', '/api/messages', invalidMessageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '消息内容为必填项')
    })

    it('应该验证文件消息的文件URL', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const invalidMessageData = {
        orderId: mockOrders.pending.id,
        content: '',
        messageType: 'IMAGE',
        fileUrl: '' // 空文件URL
      }

      const request = createMockRequest('POST', '/api/messages', invalidMessageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '文件URL为必填项')
    })

    it('应该拒绝未登录用户发送消息', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const messageData = {
        orderId: mockOrders.pending.id,
        content: 'Test message',
        messageType: 'TEXT'
      }

      const request = createMockRequest('POST', '/api/messages', messageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(401)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '未登录')
    })

    it('应该拒绝无关用户发送消息', async () => {
      const session = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(session)

      const messageData = {
        orderId: mockOrders.pending.id,
        content: 'Unauthorized message',
        messageType: 'TEXT'
      }

      const orderWithParticipants = {
        ...mockOrders.pending,
        buyerId: mockUsers.buyer.id,
        sellerId: mockUsers.seller.id
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderWithParticipants)

      const request = createMockRequest('POST', '/api/messages', messageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(403)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '无权限在此订单中发送消息')
    })

    it('应该处理数据库错误', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const messageData = {
        orderId: mockOrders.pending.id,
        content: 'Test message',
        messageType: 'TEXT'
      }

      const orderWithParticipants = {
        ...mockOrders.pending,
        buyerId: session.user.id,
        sellerId: mockUsers.seller.id
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderWithParticipants)
      mockPrisma.message.create.mockRejectedValue(new Error('Database error'))

      const request = createMockRequest('POST', '/api/messages', messageData)
      const response = await sendMessageHandler(request)

      expect(response.status).toBe(500)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '发送消息失败')
    })
  })
})
