import { describe, it, expect, beforeEach, vi } from 'vitest'
import { GET as getProductsHandler, POST as createProductHandler } from '@/app/api/products/route'
import { GET as getProductHandler, PUT as updateProductHandler } from '@/app/api/products/[id]/route'
import {
  createMockRequest,
  createMockSession,
  mockUsers,
  mockProducts,
  generateRandomString
} from '@/test/test-utils'
import { getServerSession } from 'next-auth/next'

// Mock next-auth
vi.mock('next-auth/next')
const mockGetServerSession = vi.mocked(getServerSession)

// Mock optimized queries
vi.mock('@/lib/prisma-optimized', () => ({
  optimizedQueries: {
    getProducts: vi.fn()
  },
  optimizedTransactions: {
    createProduct: vi.fn(),
    updateProduct: vi.fn()
  }
}))

// Mock cache and monitoring
vi.mock('@/lib/api-cache-middleware', () => ({
  withApiCache: (config: any) => (handler: any) => handler,
  cacheConfigs: { products: {} },
  CacheInvalidator: {}
}))

vi.mock('@/lib/performance-monitor', () => ({
  monitor: vi.fn(),
  monitorDbQuery: vi.fn((fn) => fn())
}))

vi.mock('@/lib/cache-fallback', () => ({
  cache: {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue(undefined)
  },
  CACHE_TTL: { SHORT: 300 }
}))

// Mock Prisma
vi.mock('@/lib/prisma', () => ({
  prisma: {
    product: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn()
    },
    productVariant: {
      findMany: vi.fn(),
      create: vi.fn(),
      createMany: vi.fn(),
      deleteMany: vi.fn(),
      update: vi.fn()
    },
    user: {
      findUnique: vi.fn(),
      findMany: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

import { prisma } from '@/lib/prisma'
import { optimizedQueries, optimizedTransactions } from '@/lib/prisma-optimized'

const mockPrisma = vi.mocked(prisma)
const mockOptimizedQueries = vi.mocked(optimizedQueries)
const mockOptimizedTransactions = vi.mocked(optimizedTransactions)

describe('商品API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // 设置简单的Mock返回值，避免序列化问题
    const simpleProduct = {
      id: 'product-id-123',
      title: 'Test Product',
      price: 100,
      sellerId: 'seller-id-123',
      status: 'AVAILABLE',
      reviewStatus: 'APPROVED',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    }

    const simpleUser = {
      id: 'seller-id-123',
      name: 'Test Seller',
      email: '<EMAIL>'
    }

    // 设置默认Mock返回值
    mockPrisma.product.create.mockResolvedValue(simpleProduct)
    mockPrisma.product.findUnique.mockResolvedValue(simpleProduct)
    mockPrisma.product.update.mockResolvedValue(simpleProduct)
    mockPrisma.user.findUnique.mockResolvedValue(simpleUser)

    // Mock事务
    mockPrisma.$transaction.mockImplementation(async (callback) => {
      if (typeof callback === 'function') {
        return await callback(mockPrisma)
      }
      return simpleProduct
    })
  })

  describe('GET /api/products - 获取商品列表', () => {
    it('应该返回已审核通过的商品列表', async () => {
      const mockProductsList = [
        { ...mockProducts.available, seller: mockUsers.seller },
        { 
          ...mockProducts.available, 
          id: 'product-2',
          title: 'Another Product',
          seller: mockUsers.seller 
        }
      ]

      // Mock优化查询返回值
      mockOptimizedQueries.getProducts.mockResolvedValue({
        products: mockProductsList,
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1
      })

      const request = createMockRequest('GET', '/api/products?page=1&limit=10')
      const response = await getProductsHandler(request)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('products')
      expect(responseData).toHaveProperty('total', 2)
      expect(responseData).toHaveProperty('page', 1)
      expect(responseData).toHaveProperty('limit', 10)
      expect(responseData.products).toHaveLength(2)

      // 验证优化查询被调用
      expect(mockOptimizedQueries.getProducts).toHaveBeenCalledWith(1, 10, expect.any(Object))
    })

    it('应该支持搜索功能', async () => {
      const searchTerm = 'iPhone'

      mockOptimizedQueries.getProducts.mockResolvedValue({
        products: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0
      })

      const request = createMockRequest('GET', `/api/products?keyword=${searchTerm}`)
      const response = await getProductsHandler(request)

      expect(response.status).toBe(200)

      expect(mockOptimizedQueries.getProducts).toHaveBeenCalledWith(
        1, 20,
        expect.objectContaining({
          keyword: searchTerm
        })
      )
    })

    it('应该支持分类筛选', async () => {
      const category = 'ELECTRONICS'

      mockOptimizedQueries.getProducts.mockResolvedValue({
        products: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0
      })

      const request = createMockRequest('GET', `/api/products?category=${category}`)
      const response = await getProductsHandler(request)

      expect(response.status).toBe(200)

      expect(mockOptimizedQueries.getProducts).toHaveBeenCalledWith(
        1, 20,
        expect.objectContaining({
          category
        })
      )
    })

    it('应该支持价格范围筛选', async () => {
      const minPrice = 10
      const maxPrice = 100

      mockOptimizedQueries.getProducts.mockResolvedValue({
        products: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0
      })

      const request = createMockRequest('GET', `/api/products?minPrice=${minPrice}&maxPrice=${maxPrice}`)
      const response = await getProductsHandler(request)

      expect(response.status).toBe(200)

      expect(mockOptimizedQueries.getProducts).toHaveBeenCalledWith(
        1, 20,
        expect.objectContaining({
          minPrice: minPrice.toString(),
          maxPrice: maxPrice.toString()
        })
      )
    })
  })

  describe('POST /api/products - 创建商品', () => {
    it('应该成功创建商品', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)

      const productData = {
        title: 'New Test Product',
        description: 'A new test product',
        price: 99.99,
        category: 'ELECTRONICS',
        condition: 'NEW',
        stock: 5,
        city: '上海',
        district: '浦东新区',
        images: '/uploads/products/test1.jpg,/uploads/products/test2.jpg'
      }

      const createdProduct = {
        ...mockProducts.available,
        ...productData,
        id: 'new-product-id',
        sellerId: session.user.id,
        reviewStatus: 'PENDING'
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.seller)

      // Mock $transaction to return the created product
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        if (typeof callback === 'function') {
          // Mock the transaction context
          const txMock = {
            product: {
              create: vi.fn().mockResolvedValue({ id: 'new-product-id' }),
              findUnique: vi.fn().mockResolvedValue(createdProduct)
            },
            productVariant: {
              create: vi.fn(),
              createMany: vi.fn()
            },
            variantAttribute: {
              createMany: vi.fn()
            }
          }
          return await callback(txMock)
        }
        return createdProduct
      })

      const request = createMockRequest('POST', '/api/products', productData)
      const response = await createProductHandler(request)

      expect(response.status).toBe(201)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('id', 'new-product-id')
      expect(responseData).toHaveProperty('title', productData.title)
      expect(responseData).toHaveProperty('reviewStatus', 'PENDING')

      expect(mockPrisma.$transaction).toHaveBeenCalled()
    })

    it('应该拒绝未登录用户创建商品', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const productData = {
        title: 'Test Product',
        price: 50
      }

      const request = createMockRequest('POST', '/api/products', productData)
      const response = await createProductHandler(request)

      expect(response.status).toBe(401)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '未登录')
    })

    it('应该验证必填字段', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.seller)

      const invalidData = {
        title: '', // 空标题
        price: -10 // 负价格
      }

      const request = createMockRequest('POST', '/api/products', invalidData)
      const response = await createProductHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error')
    })
  })

  describe('GET /api/products/[id] - 获取商品详情', () => {
    it('应该返回商品详情', async () => {
      const productWithDetails = {
        ...mockProducts.available,
        seller: mockUsers.seller,
        reviews: [],
        _count: { reviews: 0 }
      }

      mockPrisma.product.findUnique.mockResolvedValue(productWithDetails)

      const request = createMockRequest('GET', `/api/products/${mockProducts.available.id}`)
      const response = await getProductHandler(request, { 
        params: Promise.resolve({ id: mockProducts.available.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('id', mockProducts.available.id)
      expect(responseData).toHaveProperty('seller')
      expect(responseData.seller).not.toHaveProperty('password')
    })

    it('应该处理商品不存在的情况', async () => {
      mockPrisma.product.findUnique.mockResolvedValue(null)

      const request = createMockRequest('GET', '/api/products/non-existent-id')
      const response = await getProductHandler(request, { 
        params: Promise.resolve({ id: 'non-existent-id' }) 
      })

      expect(response.status).toBe(404)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '商品不存在')
    })
  })

  describe('PATCH /api/products/[id] - 更新商品', () => {
    it('应该允许卖家更新自己的商品', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)

      const updateData = {
        title: 'Updated Product Title',
        price: 75.0,
        stock: 8
      }

      const existingProduct = {
        ...mockProducts.available,
        sellerId: session.user.id
      }

      const updatedProduct = {
        ...existingProduct,
        ...updateData
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.seller)
      mockPrisma.product.findUnique.mockResolvedValue(existingProduct)

      // Mock $transaction for update
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        if (typeof callback === 'function') {
          // Mock the transaction context
          const txMock = {
            product: {
              update: vi.fn().mockResolvedValue(updatedProduct),
              findUnique: vi.fn().mockResolvedValue(updatedProduct)
            },
            productVariant: {
              deleteMany: vi.fn(),
              create: vi.fn(),
              createMany: vi.fn()
            },
            variantAttribute: {
              createMany: vi.fn()
            }
          }
          return await callback(txMock)
        }
        return updatedProduct
      })

      const request = createMockRequest('PATCH', `/api/products/${existingProduct.id}`, updateData)
      const response = await updateProductHandler(request, { 
        params: Promise.resolve({ id: existingProduct.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('title', updateData.title)
      expect(responseData).toHaveProperty('price', updateData.price)

      expect(mockPrisma.$transaction).toHaveBeenCalled()
    })

    it('应该拒绝非卖家更新商品', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const existingProduct = {
        ...mockProducts.available,
        sellerId: mockUsers.seller.id // 不同的卖家
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.product.findUnique.mockResolvedValue(existingProduct)

      const request = createMockRequest('PATCH', `/api/products/${existingProduct.id}`, {
        title: 'Unauthorized Update'
      })
      const response = await updateProductHandler(request, { 
        params: Promise.resolve({ id: existingProduct.id }) 
      })

      expect(response.status).toBe(403)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '无权限编辑此商品')
    })
  })
})
