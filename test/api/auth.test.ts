import { describe, it, expect, beforeEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { POST as registerHandler } from '@/app/api/auth/register/route'
import { POST as socketTokenHandler } from '@/app/api/auth/socket-token/route'
import { createMockRequest, expectJsonResponse, mockUsers, generateRandomEmail } from '@/test/test-utils'
import { getServerSession } from 'next-auth/next'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// Mock dependencies
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))
vi.mock('bcryptjs')
vi.mock('jsonwebtoken')

// Mock auth options
vi.mock('@/lib/auth', () => ({
  authOptions: {
    providers: [],
    callbacks: {},
    session: { strategy: 'jwt' }
  }
}))

// Mock Prisma
vi.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn()
    },
    userSession: {
      create: vi.fn(),
      findMany: vi.fn()
    },
    securityLog: {
      create: vi.fn()
    }
  }
}))

import { prisma } from '@/lib/prisma'
const mockPrisma = vi.mocked(prisma)
const mockGetServerSession = vi.mocked(getServerSession)
const mockBcrypt = vi.mocked(bcrypt)
const mockJwt = vi.mocked(jwt)

describe('认证API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // 设置默认的Mock返回值
    mockBcrypt.hash.mockResolvedValue('hashed-password')
    mockBcrypt.compare.mockResolvedValue(true)
    mockJwt.sign.mockReturnValue('mock-jwt-token')
    mockJwt.verify.mockReturnValue({ userId: 'test-user-id' })
  })

  describe('POST /api/auth/register - 用户注册', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        binanceUid: '12345678',
        bnbWalletAddress: '******************************************',
        city: '北京',
        district: '朝阳区'
      }

      // Mock bcrypt hash
      mockBcrypt.hash.mockResolvedValue('hashed-password')

      // Mock prisma operations
      mockPrisma.user.findUnique.mockResolvedValue(null) // 用户不存在
      mockPrisma.user.create.mockResolvedValue({
        ...mockUsers.buyer,
        email: userData.email,
        name: userData.name,
        password: 'hashed-password'
      })

      const request = createMockRequest('POST', '/api/auth/register', userData)
      const response = await registerHandler(request)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('message', '注册成功')
      expect(responseData).toHaveProperty('user')
      expect(responseData.user).not.toHaveProperty('password')
      expect(responseData.user.email).toBe(userData.email)

      // 验证数据库调用
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: userData.email }
      })
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: userData.email,
          password: 'hashed-password',
          name: userData.name,
          binanceUid: userData.binanceUid,
          bnbWalletAddress: userData.bnbWalletAddress,
          city: userData.city,
          district: userData.district
        })
      })
    })

    it('应该拒绝重复邮箱注册', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        binanceUid: '12345678',
        bnbWalletAddress: '******************************************',
        city: '北京',
        district: '朝阳区'
      }

      // Mock existing user
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)

      const request = createMockRequest('POST', '/api/auth/register', userData)
      const response = await registerHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '该邮箱已被注册')
    })

    it('应该验证必填字段', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: '123' // 太短
      }

      const request = createMockRequest('POST', '/api/auth/register', invalidData)
      const response = await registerHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error')
    })

    it('应该处理数据库错误', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        binanceUid: '12345678',
        bnbWalletAddress: '******************************************',
        city: '北京',
        district: '朝阳区'
      }

      mockPrisma.user.findUnique.mockResolvedValue(null)
      mockPrisma.user.create.mockRejectedValue(new Error('Database error'))

      const request = createMockRequest('POST', '/api/auth/register', userData)
      const response = await registerHandler(request)

      expect(response.status).toBe(500)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '注册失败，请稍后重试')
    })
  })

  describe('POST /api/auth/socket-token - Socket认证令牌', () => {
    it('应该为已登录用户生成Socket令牌', async () => {
      const mockSession = {
        user: {
          id: mockUsers.buyer.id,
          email: mockUsers.buyer.email,
          name: mockUsers.buyer.name
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }

      mockGetServerSession.mockResolvedValue(mockSession)
      mockJwt.sign.mockReturnValue('mock-jwt-token')

      // 创建带有模拟会话的请求
      const request = new Request('http://localhost/api/auth/socket-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-mock-session': JSON.stringify(mockSession)
        }
      })
      const response = await socketTokenHandler(request)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('token', 'mock-jwt-token')

      // 验证JWT签名调用
      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          userId: mockSession.user.id,
          email: mockSession.user.email,
          name: mockSession.user.name
        },
        expect.any(String),
        { expiresIn: '24h' }
      )
    })

    it('应该拒绝未登录用户', async () => {
      mockGetServerSession.mockResolvedValue(null)

      // 创建没有模拟会话的请求
      const request = new Request('http://localhost/api/auth/socket-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      const response = await socketTokenHandler(request)

      expect(response.status).toBe(401)

      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '未登录')
    })

    it('应该处理JWT签名错误', async () => {
      const mockSession = {
        user: {
          id: mockUsers.buyer.id,
          email: mockUsers.buyer.email,
          name: mockUsers.buyer.name
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }

      mockGetServerSession.mockResolvedValue(mockSession)
      mockJwt.sign.mockImplementation(() => {
        throw new Error('JWT signing error')
      })

      // 创建带有模拟会话的请求
      const request = new Request('http://localhost/api/auth/socket-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-mock-session': JSON.stringify(mockSession)
        }
      })
      const response = await socketTokenHandler(request)

      expect(response.status).toBe(500)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '生成token失败')
    })
  })

  describe('认证中间件测试', () => {
    it('应该验证有效的JWT令牌', () => {
      const validPayload = {
        userId: mockUsers.buyer.id,
        email: mockUsers.buyer.email,
        name: mockUsers.buyer.name
      }

      mockJwt.verify.mockReturnValue(validPayload)

      const token = 'valid-jwt-token'
      const result = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret')

      expect(result).toEqual(validPayload)
      expect(mockJwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET || 'fallback-secret')
    })

    it('应该拒绝无效的JWT令牌', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token')
      })

      const token = 'invalid-jwt-token'
      
      expect(() => {
        jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret')
      }).toThrow('Invalid token')
    })
  })
})
