import { describe, it, expect, beforeEach, vi } from 'vitest'
import { POST as uploadHandler } from '@/app/api/upload/route'
import { createMockRequest, createMockSession, createMockFile, createMockFormData, mockUsers } from '@/test/test-utils'
import { getServerSession } from 'next-auth/next'
import { writeFile, mkdir, access } from 'fs/promises'
import sharp from 'sharp'

vi.mock('next-auth/next')
vi.mock('fs/promises')
vi.mock('sharp')

const mockGetServerSession = vi.mocked(getServerSession)
const mockWriteFile = vi.mocked(writeFile)
const mockMkdir = vi.mocked(mkdir)
const mockAccess = vi.mocked(access)
const mockSharp = vi.mocked(sharp)

describe('文件上传API测试', () => {
  // 辅助函数：创建带会话的请求
  const createUploadRequest = (formData: FormData, session?: any) => {
    const headers: Record<string, string> = {}
    if (session) {
      headers['x-mock-session'] = JSON.stringify(session)
    }

    // 创建一个Mock请求对象，直接提供formData方法
    const mockRequest = {
      headers: {
        get: (name: string) => headers[name.toLowerCase()] || null
      },
      formData: () => Promise.resolve(formData)
    }

    return mockRequest as any
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock sharp chain
    const mockSharpInstance = {
      resize: vi.fn().mockReturnThis(),
      jpeg: vi.fn().mockReturnThis(),
      png: vi.fn().mockReturnThis(),
      webp: vi.fn().mockReturnThis(),
      toBuffer: vi.fn().mockResolvedValue(Buffer.from('processed-image-data')),
      metadata: vi.fn().mockResolvedValue({
        width: 400,
        height: 400,
        format: 'jpeg'
      })
    }
    mockSharp.mockReturnValue(mockSharpInstance as any)
  })

  describe('POST /api/upload - 文件上传', () => {
    it('应该成功上传商品图片', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('product.jpg', 'image/jpeg', 2048)
      const formData = createMockFormData({
        file,
        type: 'product'
      })

      // Mock file system operations
      mockAccess.mockRejectedValue(new Error('Directory not exists'))
      mockMkdir.mockResolvedValue(undefined)
      mockWriteFile.mockResolvedValue(undefined)

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('success', true)
      expect(responseData).toHaveProperty('url')
      expect(responseData).toHaveProperty('fileName')
      expect(responseData).toHaveProperty('size')
      expect(responseData.url).toMatch(/^\/uploads\/product\//)

      // 验证图片处理
      expect(mockSharp).toHaveBeenCalled()
      expect(mockWriteFile).toHaveBeenCalled()
    })

    it('应该成功上传用户头像', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('avatar.png', 'image/png', 1024)
      const formData = createMockFormData({
        file,
        type: 'avatar'
      })

      mockAccess.mockRejectedValue(new Error('Directory not exists'))
      mockMkdir.mockResolvedValue(undefined)
      mockWriteFile.mockResolvedValue(undefined)

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('success', true)
      expect(responseData.url).toMatch(/^\/uploads\/avatar\//)

      // 验证头像特殊处理（200x200正方形）
      const mockSharpInstance = mockSharp.mock.results[0].value
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(200, 200, {
        fit: 'cover',
        position: 'center'
      })
    })

    it('应该成功上传支付凭证', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('payment-proof.jpg', 'image/jpeg', 3072)
      const formData = createMockFormData({
        file,
        type: 'payment'
      })

      mockAccess.mockRejectedValue(new Error('Directory not exists'))
      mockMkdir.mockResolvedValue(undefined)
      mockWriteFile.mockResolvedValue(undefined)

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('success', true)
      expect(responseData.url).toMatch(/^\/uploads\/payment\//)

      // 验证支付凭证处理（保持比例，最大1200px）
      const mockSharpInstance = mockSharp.mock.results[0].value
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(1200, 1200, {
        fit: 'inside',
        withoutEnlargement: true
      })
    })

    it('应该拒绝未登录用户上传', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const file = createMockFile('test.jpg', 'image/jpeg', 1024)
      const formData = createMockFormData({
        file,
        type: 'product'
      })

      const request = new Request('http://localhost:3000/api/upload', {
        method: 'POST',
        body: formData
      })

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(401)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '未登录')
    })

    it('应该拒绝无文件上传', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const formData = createMockFormData({
        type: 'product'
        // 没有文件
      })

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '未选择文件')
    })

    it('应该拒绝不支持的文件类型', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('document.pdf', 'application/pdf', 1024)
      const formData = createMockFormData({
        file,
        type: 'product'
      })

      const request = createUploadRequest(formData, session)
      const response = await uploadHandler(request as any)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '不支持的文件类型，请上传 JPG、PNG 或 WebP 格式的图片')
    })

    it('应该拒绝过大的文件', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('large-image.jpg', 'image/jpeg', 15 * 1024 * 1024) // 15MB
      const formData = createMockFormData({
        file,
        type: 'product'
      })

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '文件大小不能超过 5MB')
    })

    it('应该处理图片处理错误', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('corrupt.jpg', 'image/jpeg', 1024)
      const formData = createMockFormData({
        file,
        type: 'product'
      })

      // Mock sharp processing error
      const mockSharpInstance = {
        resize: vi.fn().mockReturnThis(),
        jpeg: vi.fn().mockReturnThis(),
        toBuffer: vi.fn().mockRejectedValue(new Error('Image processing failed'))
      }
      mockSharp.mockReturnValue(mockSharpInstance as any)

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(500)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '文件上传失败')
    })

    it('应该处理文件系统错误', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file = createMockFile('test.jpg', 'image/jpeg', 1024)
      const formData = createMockFormData({
        file,
        type: 'product'
      })

      mockAccess.mockRejectedValue(new Error('Directory not exists'))
      mockMkdir.mockResolvedValue(undefined)
      mockWriteFile.mockRejectedValue(new Error('Write failed'))

      const request = createUploadRequest(formData, session)

      const response = await uploadHandler(request as any)

      expect(response.status).toBe(500)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '文件上传失败')
    })

    it('应该生成唯一的文件名', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const file1 = createMockFile('same-name.jpg', 'image/jpeg', 1024)
      const file2 = createMockFile('same-name.jpg', 'image/jpeg', 1024)

      mockAccess.mockRejectedValue(new Error('Directory not exists'))
      mockMkdir.mockResolvedValue(undefined)
      mockWriteFile.mockResolvedValue(undefined)

      // 上传第一个文件
      const formData1 = createMockFormData({ file: file1, type: 'product' })
      const request1 = createUploadRequest(formData1, session)
      const response1 = await uploadHandler(request1 as any)
      const data1 = await response1.json()

      // 上传第二个文件
      const formData2 = createMockFormData({ file: file2, type: 'product' })
      const request2 = createUploadRequest(formData2, session)
      const response2 = await uploadHandler(request2 as any)
      const data2 = await response2.json()

      // 文件名应该不同（包含时间戳）
      expect(data1.fileName).not.toBe(data2.fileName)
      expect(data1.url).not.toBe(data2.url)
    })
  })
})
