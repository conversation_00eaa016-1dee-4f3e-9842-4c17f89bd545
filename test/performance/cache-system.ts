import { performance } from 'perf_hooks'

// 缓存项接口
interface CacheItem<T> {
  key: string
  value: T
  expires: number
  hits: number
  lastAccessed: number
  size: number
}

// 缓存统计
interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  evictions: number
  hitRate: number
  avgResponseTime: number
  memoryUsage: number
}

// LRU缓存实现
export class LRUCache<T> {
  private cache = new Map<string, CacheItem<T>>()
  private maxSize: number
  private maxMemory: number
  private currentMemory: number = 0
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    hitRate: 0,
    avgResponseTime: 0,
    memoryUsage: 0
  }
  private responseTimes: number[] = []

  constructor(maxSize: number = 1000, maxMemoryMB: number = 100) {
    this.maxSize = maxSize
    this.maxMemory = maxMemoryMB * 1024 * 1024 // 转换为字节
  }

  // 获取缓存项
  get(key: string): T | null {
    const startTime = performance.now()
    
    const item = this.cache.get(key)
    
    if (!item) {
      this.stats.misses++
      this.recordResponseTime(performance.now() - startTime)
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expires) {
      this.cache.delete(key)
      this.currentMemory -= item.size
      this.stats.misses++
      this.recordResponseTime(performance.now() - startTime)
      return null
    }

    // 更新访问统计
    item.hits++
    item.lastAccessed = Date.now()
    
    // LRU: 移到最后（重新插入）
    this.cache.delete(key)
    this.cache.set(key, item)
    
    this.stats.hits++
    this.recordResponseTime(performance.now() - startTime)
    this.updateStats()
    
    return item.value
  }

  // 设置缓存项
  set(key: string, value: T, ttlSeconds: number = 3600): boolean {
    const startTime = performance.now()
    
    const size = this.calculateSize(value)
    const expires = Date.now() + ttlSeconds * 1000
    
    // 检查内存限制
    if (size > this.maxMemory) {
      return false // 单个项目太大
    }

    // 如果键已存在，先删除旧值
    if (this.cache.has(key)) {
      const oldItem = this.cache.get(key)!
      this.currentMemory -= oldItem.size
      this.cache.delete(key)
    }

    // 确保有足够空间
    while (
      (this.cache.size >= this.maxSize || this.currentMemory + size > this.maxMemory) &&
      this.cache.size > 0
    ) {
      this.evictLRU()
    }

    // 添加新项
    const item: CacheItem<T> = {
      key,
      value,
      expires,
      hits: 0,
      lastAccessed: Date.now(),
      size
    }

    this.cache.set(key, item)
    this.currentMemory += size
    this.stats.sets++
    
    this.recordResponseTime(performance.now() - startTime)
    this.updateStats()
    
    return true
  }

  // 删除缓存项
  delete(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false

    this.cache.delete(key)
    this.currentMemory -= item.size
    this.stats.deletes++
    this.updateStats()
    
    return true
  }

  // 清空缓存
  clear(): void {
    this.cache.clear()
    this.currentMemory = 0
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
      avgResponseTime: 0,
      memoryUsage: 0
    }
    this.responseTimes = []
  }

  // 批量获取
  mget(keys: string[]): Map<string, T | null> {
    const results = new Map<string, T | null>()
    
    for (const key of keys) {
      results.set(key, this.get(key))
    }
    
    return results
  }

  // 批量设置
  mset(items: Array<{ key: string; value: T; ttl?: number }>): number {
    let successCount = 0
    
    for (const item of items) {
      if (this.set(item.key, item.value, item.ttl)) {
        successCount++
      }
    }
    
    return successCount
  }

  // 获取所有键
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  // 检查键是否存在
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false
    
    // 检查是否过期
    if (Date.now() > item.expires) {
      this.cache.delete(key)
      this.currentMemory -= item.size
      return false
    }
    
    return true
  }

  // 获取缓存大小
  size(): number {
    return this.cache.size
  }

  // 获取内存使用情况
  memoryUsage(): { used: number; max: number; percentage: number } {
    return {
      used: this.currentMemory,
      max: this.maxMemory,
      percentage: (this.currentMemory / this.maxMemory) * 100
    }
  }

  // 获取统计信息
  getStats(): CacheStats {
    return { ...this.stats }
  }

  // 获取热点数据
  getHotKeys(limit: number = 10): Array<{ key: string; hits: number; lastAccessed: number }> {
    return Array.from(this.cache.values())
      .sort((a, b) => b.hits - a.hits)
      .slice(0, limit)
      .map(item => ({
        key: item.key,
        hits: item.hits,
        lastAccessed: item.lastAccessed
      }))
  }

  // 清理过期项
  cleanup(): number {
    const now = Date.now()
    let cleanedCount = 0
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expires) {
        this.cache.delete(key)
        this.currentMemory -= item.size
        cleanedCount++
      }
    }
    
    this.updateStats()
    return cleanedCount
  }

  // 预热缓存
  async warmup(dataLoader: (key: string) => Promise<T>, keys: string[], ttl: number = 3600): Promise<number> {
    let loadedCount = 0
    
    // 并行加载数据
    const loadPromises = keys.map(async (key) => {
      try {
        const value = await dataLoader(key)
        if (this.set(key, value, ttl)) {
          loadedCount++
        }
      } catch (error) {
        console.warn(`Failed to load cache key ${key}:`, error)
      }
    })
    
    await Promise.all(loadPromises)
    return loadedCount
  }

  // 私有方法：淘汰LRU项
  private evictLRU(): void {
    const firstKey = this.cache.keys().next().value
    if (firstKey) {
      const item = this.cache.get(firstKey)!
      this.cache.delete(firstKey)
      this.currentMemory -= item.size
      this.stats.evictions++
    }
  }

  // 私有方法：计算对象大小
  private calculateSize(value: T): number {
    try {
      return JSON.stringify(value).length * 2 // 粗略估算（UTF-16）
    } catch {
      return 1024 // 默认1KB
    }
  }

  // 私有方法：记录响应时间
  private recordResponseTime(time: number): void {
    this.responseTimes.push(time)
    
    // 只保留最近1000次的响应时间
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000)
    }
  }

  // 私有方法：更新统计信息
  private updateStats(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
    
    if (this.responseTimes.length > 0) {
      this.stats.avgResponseTime = this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length
    }
    
    this.stats.memoryUsage = this.currentMemory
  }
}

// 多级缓存系统
export class MultiLevelCache<T> {
  private l1Cache: LRUCache<T> // 内存缓存
  private l2Cache: LRUCache<T> // 二级缓存
  private stats = {
    l1Hits: 0,
    l2Hits: 0,
    misses: 0,
    promotions: 0 // L2到L1的提升次数
  }

  constructor(
    l1Size: number = 500,
    l1MemoryMB: number = 50,
    l2Size: number = 2000,
    l2MemoryMB: number = 200
  ) {
    this.l1Cache = new LRUCache<T>(l1Size, l1MemoryMB)
    this.l2Cache = new LRUCache<T>(l2Size, l2MemoryMB)
  }

  // 获取数据
  get(key: string): T | null {
    // 先查L1缓存
    let value = this.l1Cache.get(key)
    if (value !== null) {
      this.stats.l1Hits++
      return value
    }

    // 再查L2缓存
    value = this.l2Cache.get(key)
    if (value !== null) {
      this.stats.l2Hits++
      // 提升到L1缓存
      this.l1Cache.set(key, value, 3600)
      this.stats.promotions++
      return value
    }

    this.stats.misses++
    return null
  }

  // 设置数据
  set(key: string, value: T, ttl: number = 3600): boolean {
    // 同时设置到两级缓存
    const l1Success = this.l1Cache.set(key, value, ttl)
    const l2Success = this.l2Cache.set(key, value, ttl * 2) // L2缓存时间更长
    
    return l1Success || l2Success
  }

  // 删除数据
  delete(key: string): boolean {
    const l1Deleted = this.l1Cache.delete(key)
    const l2Deleted = this.l2Cache.delete(key)
    
    return l1Deleted || l2Deleted
  }

  // 清空所有缓存
  clear(): void {
    this.l1Cache.clear()
    this.l2Cache.clear()
    this.stats = {
      l1Hits: 0,
      l2Hits: 0,
      misses: 0,
      promotions: 0
    }
  }

  // 获取统计信息
  getStats() {
    const total = this.stats.l1Hits + this.stats.l2Hits + this.stats.misses
    
    return {
      ...this.stats,
      totalRequests: total,
      hitRate: total > 0 ? ((this.stats.l1Hits + this.stats.l2Hits) / total) * 100 : 0,
      l1HitRate: total > 0 ? (this.stats.l1Hits / total) * 100 : 0,
      l2HitRate: total > 0 ? (this.stats.l2Hits / total) * 100 : 0,
      l1Stats: this.l1Cache.getStats(),
      l2Stats: this.l2Cache.getStats()
    }
  }

  // 获取内存使用情况
  getMemoryUsage() {
    return {
      l1: this.l1Cache.memoryUsage(),
      l2: this.l2Cache.memoryUsage(),
      total: {
        used: this.l1Cache.memoryUsage().used + this.l2Cache.memoryUsage().used,
        max: this.l1Cache.memoryUsage().max + this.l2Cache.memoryUsage().max
      }
    }
  }
}

// 缓存性能测试工具
export class CachePerformanceTester {
  static async testCachePerformance<T>(
    cache: LRUCache<T> | MultiLevelCache<T>,
    testData: Array<{ key: string; value: T }>,
    operations: {
      sets: number
      gets: number
      deletes: number
    }
  ): Promise<{
    setPerformance: { avgTime: number; opsPerSecond: number }
    getPerformance: { avgTime: number; opsPerSecond: number }
    deletePerformance: { avgTime: number; opsPerSecond: number }
    memoryEfficiency: number
  }> {
    const results = {
      setPerformance: { avgTime: 0, opsPerSecond: 0 },
      getPerformance: { avgTime: 0, opsPerSecond: 0 },
      deletePerformance: { avgTime: 0, opsPerSecond: 0 },
      memoryEfficiency: 0
    }

    // 测试SET操作
    if (operations.sets > 0) {
      const setTimes: number[] = []
      const startTime = performance.now()
      
      for (let i = 0; i < operations.sets; i++) {
        const item = testData[i % testData.length]
        const opStart = performance.now()
        cache.set(item.key, item.value)
        setTimes.push(performance.now() - opStart)
      }
      
      const totalSetTime = performance.now() - startTime
      results.setPerformance = {
        avgTime: setTimes.reduce((sum, time) => sum + time, 0) / setTimes.length,
        opsPerSecond: operations.sets / (totalSetTime / 1000)
      }
    }

    // 测试GET操作
    if (operations.gets > 0) {
      const getTimes: number[] = []
      const startTime = performance.now()
      
      for (let i = 0; i < operations.gets; i++) {
        const item = testData[i % testData.length]
        const opStart = performance.now()
        cache.get(item.key)
        getTimes.push(performance.now() - opStart)
      }
      
      const totalGetTime = performance.now() - startTime
      results.getPerformance = {
        avgTime: getTimes.reduce((sum, time) => sum + time, 0) / getTimes.length,
        opsPerSecond: operations.gets / (totalGetTime / 1000)
      }
    }

    // 测试DELETE操作
    if (operations.deletes > 0) {
      const deleteTimes: number[] = []
      const startTime = performance.now()
      
      for (let i = 0; i < operations.deletes; i++) {
        const item = testData[i % testData.length]
        const opStart = performance.now()
        cache.delete(item.key)
        deleteTimes.push(performance.now() - opStart)
      }
      
      const totalDeleteTime = performance.now() - startTime
      results.deletePerformance = {
        avgTime: deleteTimes.reduce((sum, time) => sum + time, 0) / deleteTimes.length,
        opsPerSecond: operations.deletes / (totalDeleteTime / 1000)
      }
    }

    // 计算内存效率
    if ('memoryUsage' in cache) {
      const memUsage = cache.memoryUsage()
      results.memoryEfficiency = (memUsage.used / memUsage.max) * 100
    }

    return results
  }
}
