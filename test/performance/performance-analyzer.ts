import { performance } from 'perf_hooks'
import { promises as fs } from 'fs'
import path from 'path'

export interface PerformanceMetric {
  name: string
  duration: number
  memoryUsage: {
    before: NodeJS.MemoryUsage
    after: NodeJS.MemoryUsage
    delta: number
  }
  timestamp: number
  metadata?: Record<string, any>
}

export interface PerformanceBenchmark {
  testName: string
  metrics: PerformanceMetric[]
  summary: {
    avgDuration: number
    minDuration: number
    maxDuration: number
    totalMemoryDelta: number
    iterations: number
  }
}

export class PerformanceAnalyzer {
  private metrics: PerformanceMetric[] = []
  private benchmarks: Map<string, PerformanceBenchmark> = new Map()
  private baselineData: Map<string, PerformanceBenchmark> = new Map()

  constructor(private outputDir: string = 'test-results/performance') {
    this.ensureOutputDir()
  }

  private async ensureOutputDir() {
    try {
      await fs.mkdir(this.outputDir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }
  }

  // 测量单个操作性能
  async measureOperation<T>(
    name: string,
    operation: () => Promise<T> | T,
    metadata?: Record<string, any>
  ): Promise<{ result: T; metric: PerformanceMetric }> {
    const memoryBefore = process.memoryUsage()
    const startTime = performance.now()

    const result = await operation()

    const endTime = performance.now()
    const memoryAfter = process.memoryUsage()

    const metric: PerformanceMetric = {
      name,
      duration: endTime - startTime,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        delta: memoryAfter.heapUsed - memoryBefore.heapUsed
      },
      timestamp: Date.now(),
      metadata
    }

    this.metrics.push(metric)
    return { result, metric }
  }

  // 运行基准测试
  async runBenchmark<T>(
    testName: string,
    operation: () => Promise<T> | T,
    iterations: number = 10,
    metadata?: Record<string, any>
  ): Promise<PerformanceBenchmark> {
    const metrics: PerformanceMetric[] = []

    console.log(`🔄 运行基准测试: ${testName} (${iterations}次迭代)`)

    for (let i = 0; i < iterations; i++) {
      const { metric } = await this.measureOperation(
        `${testName}_iteration_${i + 1}`,
        operation,
        { ...metadata, iteration: i + 1 }
      )
      metrics.push(metric)

      // 每10次迭代显示进度
      if ((i + 1) % Math.max(1, Math.floor(iterations / 10)) === 0) {
        console.log(`  📊 进度: ${i + 1}/${iterations} (${((i + 1) / iterations * 100).toFixed(1)}%)`)
      }
    }

    const durations = metrics.map(m => m.duration)
    const memoryDeltas = metrics.map(m => m.memoryUsage.delta)

    const benchmark: PerformanceBenchmark = {
      testName,
      metrics,
      summary: {
        avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations),
        totalMemoryDelta: memoryDeltas.reduce((a, b) => a + b, 0),
        iterations
      }
    }

    this.benchmarks.set(testName, benchmark)
    console.log(`✅ 基准测试完成: ${testName}`)
    console.log(`   平均耗时: ${benchmark.summary.avgDuration.toFixed(2)}ms`)
    console.log(`   内存使用: ${(benchmark.summary.totalMemoryDelta / 1024 / 1024).toFixed(2)}MB`)

    return benchmark
  }

  // 并行基准测试
  async runParallelBenchmark<T>(
    testName: string,
    operation: () => Promise<T> | T,
    concurrency: number = 10,
    totalOperations: number = 100
  ): Promise<PerformanceBenchmark> {
    console.log(`🚀 运行并行基准测试: ${testName} (${concurrency}并发, ${totalOperations}操作)`)

    const startTime = performance.now()
    const memoryBefore = process.memoryUsage()

    const batches = Math.ceil(totalOperations / concurrency)
    const metrics: PerformanceMetric[] = []

    for (let batch = 0; batch < batches; batch++) {
      const batchSize = Math.min(concurrency, totalOperations - batch * concurrency)
      const batchPromises = Array.from({ length: batchSize }, async (_, i) => {
        const operationIndex = batch * concurrency + i
        return this.measureOperation(
          `${testName}_parallel_${operationIndex}`,
          operation,
          { batch, operationIndex, concurrency }
        )
      })

      const batchResults = await Promise.all(batchPromises)
      metrics.push(...batchResults.map(r => r.metric))

      console.log(`  📊 批次 ${batch + 1}/${batches} 完成`)
    }

    const endTime = performance.now()
    const memoryAfter = process.memoryUsage()

    const durations = metrics.map(m => m.duration)
    const totalDuration = endTime - startTime

    const benchmark: PerformanceBenchmark = {
      testName,
      metrics,
      summary: {
        avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations),
        totalMemoryDelta: memoryAfter.heapUsed - memoryBefore.heapUsed,
        iterations: totalOperations
      }
    }

    this.benchmarks.set(testName, benchmark)
    console.log(`✅ 并行基准测试完成: ${testName}`)
    console.log(`   总耗时: ${totalDuration.toFixed(2)}ms`)
    console.log(`   平均单操作耗时: ${benchmark.summary.avgDuration.toFixed(2)}ms`)
    console.log(`   吞吐量: ${(totalOperations / totalDuration * 1000).toFixed(2)} ops/sec`)

    return benchmark
  }

  // 加载基线数据
  async loadBaseline(baselinePath?: string): Promise<void> {
    const filePath = baselinePath || path.join(this.outputDir, 'baseline.json')
    try {
      const data = await fs.readFile(filePath, 'utf-8')
      const baseline = JSON.parse(data)
      
      for (const [testName, benchmark] of Object.entries(baseline)) {
        this.baselineData.set(testName, benchmark as PerformanceBenchmark)
      }
      
      console.log(`📊 加载基线数据: ${this.baselineData.size}个基准测试`)
    } catch (error) {
      console.log(`⚠️  无法加载基线数据: ${error}`)
    }
  }

  // 保存基线数据
  async saveBaseline(): Promise<void> {
    const baselineData = Object.fromEntries(this.benchmarks)
    const filePath = path.join(this.outputDir, 'baseline.json')
    
    await fs.writeFile(filePath, JSON.stringify(baselineData, null, 2))
    console.log(`💾 基线数据已保存: ${filePath}`)
  }

  // 性能回归检测
  detectRegression(threshold: number = 0.2): Array<{
    testName: string
    current: number
    baseline: number
    regression: number
    isRegression: boolean
  }> {
    const regressions: Array<{
      testName: string
      current: number
      baseline: number
      regression: number
      isRegression: boolean
    }> = []

    for (const [testName, currentBenchmark] of this.benchmarks) {
      const baseline = this.baselineData.get(testName)
      if (!baseline) continue

      const currentAvg = currentBenchmark.summary.avgDuration
      const baselineAvg = baseline.summary.avgDuration
      const regression = (currentAvg - baselineAvg) / baselineAvg

      regressions.push({
        testName,
        current: currentAvg,
        baseline: baselineAvg,
        regression,
        isRegression: regression > threshold
      })
    }

    return regressions
  }

  // 生成性能报告
  async generateReport(): Promise<void> {
    const reportData = {
      timestamp: new Date().toISOString(),
      summary: {
        totalBenchmarks: this.benchmarks.size,
        totalMetrics: this.metrics.length
      },
      benchmarks: Object.fromEntries(this.benchmarks),
      regressions: this.detectRegression()
    }

    // JSON报告
    const jsonPath = path.join(this.outputDir, 'performance-report.json')
    await fs.writeFile(jsonPath, JSON.stringify(reportData, null, 2))

    // HTML报告
    const htmlReport = this.generateHtmlReport(reportData)
    const htmlPath = path.join(this.outputDir, 'performance-report.html')
    await fs.writeFile(htmlPath, htmlReport)

    console.log(`📊 性能报告已生成:`)
    console.log(`   JSON: ${jsonPath}`)
    console.log(`   HTML: ${htmlPath}`)
  }

  private generateHtmlReport(data: any): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 性能分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-width: 150px; text-align: center; }
        .regression { background: #f8d7da; color: #721c24; }
        .improvement { background: #d4edda; color: #155724; }
        .chart-container { width: 100%; height: 400px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ BitMarket 性能分析报告</h1>
            <p>生成时间: ${data.timestamp}</p>
        </div>
        
        <div class="card">
            <h2>📊 总览</h2>
            <div class="metric">
                <h3>${data.summary.totalBenchmarks}</h3>
                <p>基准测试</p>
            </div>
            <div class="metric">
                <h3>${data.summary.totalMetrics}</h3>
                <p>性能指标</p>
            </div>
        </div>
        
        <div class="card">
            <h2>🔍 性能回归检测</h2>
            <table>
                <thead>
                    <tr>
                        <th>测试名称</th>
                        <th>当前性能 (ms)</th>
                        <th>基线性能 (ms)</th>
                        <th>变化率</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.regressions.map(r => `
                        <tr class="${r.isRegression ? 'regression' : r.regression < -0.1 ? 'improvement' : ''}">
                            <td>${r.testName}</td>
                            <td>${r.current.toFixed(2)}</td>
                            <td>${r.baseline.toFixed(2)}</td>
                            <td>${(r.regression * 100).toFixed(1)}%</td>
                            <td>${r.isRegression ? '🔴 回归' : r.regression < -0.1 ? '🟢 改进' : '⚪ 正常'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>📈 基准测试结果</h2>
            ${Object.entries(data.benchmarks).map(([name, benchmark]: [string, any]) => `
                <h3>${name}</h3>
                <div class="metric">
                    <h4>${benchmark.summary.avgDuration.toFixed(2)}ms</h4>
                    <p>平均耗时</p>
                </div>
                <div class="metric">
                    <h4>${benchmark.summary.minDuration.toFixed(2)}ms</h4>
                    <p>最小耗时</p>
                </div>
                <div class="metric">
                    <h4>${benchmark.summary.maxDuration.toFixed(2)}ms</h4>
                    <p>最大耗时</p>
                </div>
                <div class="metric">
                    <h4>${(benchmark.summary.totalMemoryDelta / 1024 / 1024).toFixed(2)}MB</h4>
                    <p>内存使用</p>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>
    `
  }

  // 清理数据
  clear(): void {
    this.metrics = []
    this.benchmarks.clear()
  }

  // 获取统计信息
  getStats() {
    return {
      totalMetrics: this.metrics.length,
      totalBenchmarks: this.benchmarks.size,
      benchmarks: Array.from(this.benchmarks.entries()).map(([name, benchmark]) => ({
        name,
        avgDuration: benchmark.summary.avgDuration,
        iterations: benchmark.summary.iterations,
        memoryUsage: benchmark.summary.totalMemoryDelta
      }))
    }
  }
}
