import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { performance } from 'perf_hooks'
import { 
  MemoryOptimizedUserFactory, 
  StreamingDataGenerator, 
  MonitoredDataGenerator,
  MemoryOptimizedProductFactory 
} from '../factories/memory-optimized-data-factory'

// 内存测试工具
class MemoryTestUtils {
  static getMemoryUsage() {
    const usage = process.memoryUsage()
    return {
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
      rss: usage.rss,
      external: usage.external,
      heapUsedMB: usage.heapUsed / 1024 / 1024,
      rssMB: usage.rss / 1024 / 1024
    }
  }

  static async forceGC() {
    if (global.gc) {
      global.gc()
      // 等待GC完成
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  static calculateMemoryDelta(before: any, after: any) {
    return {
      heapUsedDelta: after.heapUsed - before.heapUsed,
      rssDelta: after.rss - before.rss,
      heapUsedDeltaMB: (after.heapUsed - before.heapUsed) / 1024 / 1024,
      rssDeltaMB: (after.rss - before.rss) / 1024 / 1024
    }
  }
}

describe('🧠 内存优化测试套件', () => {
  beforeEach(async () => {
    // 重置计数器和清理池
    MemoryOptimizedUserFactory.resetCounters()
    MemoryOptimizedProductFactory.resetCounters()
    
    // 强制垃圾回收
    await MemoryTestUtils.forceGC()
  })

  afterEach(async () => {
    // 测试后清理
    MemoryOptimizedUserFactory.clearPool()
    MemoryOptimizedProductFactory.clearPool()
    await MemoryTestUtils.forceGC()
  })

  describe('📊 内存优化数据工厂测试', () => {
    it('应该显著减少内存使用', async () => {
      console.log('\n🧠 内存优化数据工厂测试')
      console.log('=' .repeat(50))

      const memBefore = MemoryTestUtils.getMemoryUsage()
      console.log(`📊 测试前内存: ${memBefore.heapUsedMB.toFixed(2)}MB (堆), ${memBefore.rssMB.toFixed(2)}MB (RSS)`)

      // 生成大量用户数据
      const count = 10000
      const startTime = performance.now()
      const users = MemoryOptimizedUserFactory.createBatch(count)
      const endTime = performance.now()

      const memAfter = MemoryTestUtils.getMemoryUsage()
      const delta = MemoryTestUtils.calculateMemoryDelta(memBefore, memAfter)

      console.log(`📊 生成${count}个用户后内存: ${memAfter.heapUsedMB.toFixed(2)}MB (堆), ${memAfter.rssMB.toFixed(2)}MB (RSS)`)
      console.log(`📈 内存增长: ${delta.heapUsedDeltaMB.toFixed(2)}MB (堆), ${delta.rssDeltaMB.toFixed(2)}MB (RSS)`)
      console.log(`⏱️  生成时间: ${(endTime - startTime).toFixed(2)}ms`)
      console.log(`💾 每用户内存: ${(delta.heapUsedDelta / count / 1024).toFixed(2)}KB`)

      // 验证数据质量
      expect(users).toHaveLength(count)
      expect(users[0]).toHaveProperty('id')
      expect(users[0]).toHaveProperty('name')
      expect(users[0]).toHaveProperty('email')

      // 验证内存效率 - 每用户应该 < 1KB
      const memoryPerUser = delta.heapUsedDelta / count
      expect(memoryPerUser).toBeLessThan(1024) // < 1KB per user

      // 释放对象回池
      MemoryOptimizedUserFactory.releaseBatch(users)
      await MemoryTestUtils.forceGC()

      const memAfterCleanup = MemoryTestUtils.getMemoryUsage()
      const cleanupDelta = MemoryTestUtils.calculateMemoryDelta(memBefore, memAfterCleanup)
      
      console.log(`🧹 清理后内存: ${memAfterCleanup.heapUsedMB.toFixed(2)}MB (堆), ${memAfterCleanup.rssMB.toFixed(2)}MB (RSS)`)
      console.log(`📉 净内存增长: ${cleanupDelta.heapUsedDeltaMB.toFixed(2)}MB (堆), ${cleanupDelta.rssDeltaMB.toFixed(2)}MB (RSS)`)

      // 验证内存回收效果
      expect(cleanupDelta.heapUsedDeltaMB).toBeLessThan(5) // 净增长 < 5MB
    })

    it('应该有效使用对象池', () => {
      console.log('\n🔄 对象池效果测试')
      console.log('-'.repeat(30))

      // 第一次生成
      const users1 = MemoryOptimizedUserFactory.createBatch(100)
      const poolStats1 = MemoryOptimizedUserFactory.getPoolStats()
      console.log(`第一次生成后池状态: ${poolStats1.poolSize}个对象`)

      // 释放对象
      MemoryOptimizedUserFactory.releaseBatch(users1)
      const poolStats2 = MemoryOptimizedUserFactory.getPoolStats()
      console.log(`释放后池状态: ${poolStats2.poolSize}个对象`)

      // 第二次生成（应该复用池中对象）
      const users2 = MemoryOptimizedUserFactory.createBatch(50)
      const poolStats3 = MemoryOptimizedUserFactory.getPoolStats()
      console.log(`第二次生成后池状态: ${poolStats3.poolSize}个对象`)

      // 验证对象池工作正常
      expect(users1).toHaveLength(100)
      expect(users2).toHaveLength(50)
      expect(poolStats2.poolSize).toBeGreaterThan(0) // 池中有对象
      expect(poolStats3.poolSize).toBeLessThan(poolStats2.poolSize) // 对象被复用

      // 清理
      MemoryOptimizedUserFactory.releaseBatch(users2)
    })
  })

  describe('🌊 流式数据生成测试', () => {
    it('应该支持大规模流式数据生成', async () => {
      console.log('\n🌊 流式数据生成测试')
      console.log('=' .repeat(50))

      const generator = new StreamingDataGenerator(1000) // 1000个一批
      const totalCount = 10000
      let processedCount = 0
      let maxMemoryUsage = 0

      const memBefore = MemoryTestUtils.getMemoryUsage()
      console.log(`📊 开始前内存: ${memBefore.heapUsedMB.toFixed(2)}MB`)

      const startTime = performance.now()

      // 流式处理
      for (const batch of generator.generateUsers(totalCount)) {
        processedCount += batch.length
        
        // 监控内存使用
        const currentMem = MemoryTestUtils.getMemoryUsage()
        maxMemoryUsage = Math.max(maxMemoryUsage, currentMem.heapUsedMB)
        
        // 验证批次数据
        expect(batch.length).toBeGreaterThan(0)
        expect(batch.length).toBeLessThanOrEqual(1000)
        expect(batch[0]).toHaveProperty('id')
        
        // 模拟处理时间
        await new Promise(resolve => setImmediate(resolve))
      }

      const endTime = performance.now()
      const memAfter = MemoryTestUtils.getMemoryUsage()

      console.log(`✅ 流式处理完成: ${processedCount}个用户`)
      console.log(`⏱️  总时间: ${(endTime - startTime).toFixed(2)}ms`)
      console.log(`📊 最大内存使用: ${maxMemoryUsage.toFixed(2)}MB`)
      console.log(`📊 结束时内存: ${memAfter.heapUsedMB.toFixed(2)}MB`)

      expect(processedCount).toBe(totalCount)
      expect(generator.getTotalGenerated()).toBe(totalCount)
      
      // 验证内存控制效果 - 最大内存使用应该相对稳定
      const memoryGrowth = maxMemoryUsage - memBefore.heapUsedMB
      expect(memoryGrowth).toBeLessThan(50) // 内存增长 < 50MB

      generator.reset()
    })

    it('应该支持异步批量处理', async () => {
      console.log('\n⚡ 异步批量处理测试')
      console.log('-'.repeat(30))

      const generator = new StreamingDataGenerator(500)
      let totalProcessed = 0
      let batchCount = 0

      const processor = async (batch: any[]) => {
        totalProcessed += batch.length
        batchCount++
        
        // 模拟异步处理
        await new Promise(resolve => setTimeout(resolve, 1))
        
        // 验证批次数据
        expect(batch.length).toBeGreaterThan(0)
        batch.forEach(user => {
          expect(user).toHaveProperty('id')
          expect(user).toHaveProperty('name')
        })
      }

      const startTime = performance.now()
      await generator.processUsersInBatches(2000, processor)
      const endTime = performance.now()

      console.log(`✅ 异步处理完成: ${totalProcessed}个用户, ${batchCount}个批次`)
      console.log(`⏱️  处理时间: ${(endTime - startTime).toFixed(2)}ms`)

      expect(totalProcessed).toBe(2000)
      expect(batchCount).toBe(4) // 2000 / 500 = 4批次
    })
  })

  describe('🔍 内存监控数据生成测试', () => {
    it('应该在内存监控下安全生成数据', async () => {
      console.log('\n🔍 内存监控数据生成测试')
      console.log('=' .repeat(50))

      const generator = new MonitoredDataGenerator(50, 500) // 50MB阈值，500ms检查间隔
      
      const memBefore = MemoryTestUtils.getMemoryUsage()
      console.log(`📊 开始前内存: ${memBefore.heapUsedMB.toFixed(2)}MB`)

      const startTime = performance.now()
      const users = await generator.generateUsersWithMemoryControl(5000)
      const endTime = performance.now()

      const memAfter = MemoryTestUtils.getMemoryUsage()
      const delta = MemoryTestUtils.calculateMemoryDelta(memBefore, memAfter)

      console.log(`✅ 监控生成完成: ${users.length}个用户`)
      console.log(`⏱️  生成时间: ${(endTime - startTime).toFixed(2)}ms`)
      console.log(`📊 内存增长: ${delta.heapUsedDeltaMB.toFixed(2)}MB`)

      expect(users).toHaveLength(5000)
      expect(users[0]).toHaveProperty('id')
      
      // 验证内存控制效果
      expect(delta.heapUsedDeltaMB).toBeLessThan(30) // 内存增长 < 30MB
    })
  })

  describe('📈 内存性能对比测试', () => {
    it('应该比标准工厂更节省内存', async () => {
      console.log('\n📈 内存性能对比测试')
      console.log('=' .repeat(50))

      const count = 5000

      // 测试内存优化工厂
      await MemoryTestUtils.forceGC()
      const memBefore1 = MemoryTestUtils.getMemoryUsage()
      
      const startTime1 = performance.now()
      const optimizedUsers = MemoryOptimizedUserFactory.createBatch(count)
      const endTime1 = performance.now()
      
      const memAfter1 = MemoryTestUtils.getMemoryUsage()
      const delta1 = MemoryTestUtils.calculateMemoryDelta(memBefore1, memAfter1)

      console.log(`🚀 内存优化工厂:`)
      console.log(`   生成时间: ${(endTime1 - startTime1).toFixed(2)}ms`)
      console.log(`   内存使用: ${delta1.heapUsedDeltaMB.toFixed(2)}MB`)
      console.log(`   每用户内存: ${(delta1.heapUsedDelta / count / 1024).toFixed(2)}KB`)

      // 清理第一次测试
      MemoryOptimizedUserFactory.releaseBatch(optimizedUsers)
      await MemoryTestUtils.forceGC()

      // 测试标准对象创建（模拟）
      const memBefore2 = MemoryTestUtils.getMemoryUsage()
      
      const startTime2 = performance.now()
      const standardUsers = Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        userId: `user-${i + 1}`,
        name: `用户${i + 1}`,
        email: `user${i + 1}@example.com`,
        city: '北京市',
        district: '朝阳区',
        creditScore: Math.floor(Math.random() * 101),
        status: 'ACTIVE',
        role: 'USER',
        createdAt: new Date(),
        updatedAt: new Date(),
        avatar: `/avatars/default-${(i % 10) + 1}.jpg`,
        binanceUid: String(Math.floor(Math.random() * 1000000000)),
        bnbWalletAddress: `0x${Math.floor(Math.random() * 16777215).toString(16).padStart(40, '0')}`,
        depositBalance: Math.floor(Math.random() * 10000 * 100) / 100,
        riskLevel: 'NORMAL'
      }))
      const endTime2 = performance.now()
      
      const memAfter2 = MemoryTestUtils.getMemoryUsage()
      const delta2 = MemoryTestUtils.calculateMemoryDelta(memBefore2, memAfter2)

      console.log(`📊 标准对象创建:`)
      console.log(`   生成时间: ${(endTime2 - startTime2).toFixed(2)}ms`)
      console.log(`   内存使用: ${delta2.heapUsedDeltaMB.toFixed(2)}MB`)
      console.log(`   每用户内存: ${(delta2.heapUsedDelta / count / 1024).toFixed(2)}KB`)

      // 计算优化效果
      const memoryImprovement = ((delta2.heapUsedDelta - delta1.heapUsedDelta) / delta2.heapUsedDelta) * 100
      const timeImprovement = ((endTime2 - startTime2) - (endTime1 - startTime1)) / (endTime2 - startTime2) * 100

      console.log(`📈 优化效果:`)
      console.log(`   内存节省: ${memoryImprovement.toFixed(1)}%`)
      console.log(`   时间${timeImprovement > 0 ? '节省' : '增加'}: ${Math.abs(timeImprovement).toFixed(1)}%`)

      // 验证优化效果
      expect(optimizedUsers).toHaveLength(count)
      expect(standardUsers).toHaveLength(count)
      expect(delta1.heapUsedDelta).toBeLessThan(delta2.heapUsedDelta) // 内存优化工厂应该使用更少内存
      expect(memoryImprovement).toBeGreaterThan(30) // 至少节省30%内存

      // 清理
      standardUsers.length = 0
    })
  })
})
