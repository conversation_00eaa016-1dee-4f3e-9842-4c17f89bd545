import { describe, it, expect, beforeEach } from 'vitest'
import { performance } from 'perf_hooks'
import { UltraOptimizedUserFactory, MemoryPoolUserFactory } from '../factories/ultra-optimized-data-factory'
import { OptimizedUserFactory } from '../factories/optimized-data-factory'

describe('🚀 用户数据生成性能优化测试', () => {
  beforeEach(() => {
    // 重置计数器确保测试一致性
    UltraOptimizedUserFactory.resetCounters()
    MemoryPoolUserFactory.clearPool()
  })

  describe('📊 性能基准对比测试', () => {
    it('应该显著提升10,000用户生成性能', async () => {
      console.log('\n🔥 用户数据生成性能对比测试')
      console.log('=' .repeat(50))

      const testCounts = [1000, 5000, 10000]
      const results: any = {}

      for (const count of testCounts) {
        console.log(`\n📊 测试 ${count.toLocaleString()} 个用户生成:`)
        console.log('-'.repeat(30))

        // 1. 原始优化版本测试
        const optimizedStart = performance.now()
        const optimizedUsers = OptimizedUserFactory.createBatch(count)
        const optimizedEnd = performance.now()
        const optimizedTime = optimizedEnd - optimizedStart

        // 2. 超优化版本测试
        const ultraStart = performance.now()
        const ultraUsers = UltraOptimizedUserFactory.createBatch(count)
        const ultraEnd = performance.now()
        const ultraTime = ultraEnd - ultraStart

        // 3. 内存池版本测试
        const poolStart = performance.now()
        const poolUsers = MemoryPoolUserFactory.createBatch(count)
        const poolEnd = performance.now()
        const poolTime = poolEnd - poolStart

        // 计算性能提升
        const ultraImprovement = ((optimizedTime - ultraTime) / optimizedTime) * 100
        const poolImprovement = ((optimizedTime - poolTime) / optimizedTime) * 100

        results[count] = {
          optimized: {
            time: optimizedTime,
            rate: count / (optimizedTime / 1000)
          },
          ultra: {
            time: ultraTime,
            rate: count / (ultraTime / 1000),
            improvement: ultraImprovement
          },
          pool: {
            time: poolTime,
            rate: count / (poolTime / 1000),
            improvement: poolImprovement
          }
        }

        console.log(`✅ 原始优化版本: ${optimizedTime.toFixed(2)}ms (${results[count].optimized.rate.toFixed(0)} users/sec)`)
        console.log(`🚀 超优化版本: ${ultraTime.toFixed(2)}ms (${results[count].ultra.rate.toFixed(0)} users/sec) - 提升 ${ultraImprovement.toFixed(1)}%`)
        console.log(`💾 内存池版本: ${poolTime.toFixed(2)}ms (${results[count].pool.rate.toFixed(0)} users/sec) - 提升 ${poolImprovement.toFixed(1)}%`)

        // 验证数据质量
        expect(optimizedUsers).toHaveLength(count)
        expect(ultraUsers).toHaveLength(count)
        expect(poolUsers).toHaveLength(count)

        // 验证数据结构
        expect(ultraUsers[0]).toHaveProperty('id')
        expect(ultraUsers[0]).toHaveProperty('name')
        expect(ultraUsers[0]).toHaveProperty('email')
        expect(ultraUsers[0]).toHaveProperty('city')
      }

      // 验证10,000用户生成性能目标
      const target10k = results[10000]
      console.log(`\n🎯 10,000用户生成性能目标验证:`)
      console.log(`   目标: < 125ms (原始基线)`)
      console.log(`   超优化版本: ${target10k.ultra.time.toFixed(2)}ms`)
      console.log(`   内存池版本: ${target10k.pool.time.toFixed(2)}ms`)

      // 性能目标：超优化版本应该比原始基线快
      expect(target10k.ultra.time).toBeLessThan(125)
      expect(target10k.pool.time).toBeLessThan(125)

      // 性能目标：内存池版本应该比当前优化版本快至少30%
      // 超优化版本可能因为额外的预计算开销而稍慢，但仍应达到基线目标
      expect(target10k.pool.improvement).toBeGreaterThan(30)
      // 超优化版本至少应该达到原始基线性能
      expect(target10k.ultra.time).toBeLessThan(125)
    })

    it('应该在并行模式下实现更高性能', async () => {
      console.log('\n⚡ 并行用户生成性能测试')
      console.log('=' .repeat(50))

      const count = 10000
      const batchSizes = [1000, 2000, 5000]

      for (const batchSize of batchSizes) {
        console.log(`\n📊 并行批次大小: ${batchSize}`)
        console.log('-'.repeat(20))

        // 串行生成
        const serialStart = performance.now()
        const serialUsers = UltraOptimizedUserFactory.createBatch(count)
        const serialEnd = performance.now()
        const serialTime = serialEnd - serialStart

        // 并行生成
        const parallelStart = performance.now()
        const parallelUsers = UltraOptimizedUserFactory.createBatchParallel(count, batchSize)
        const parallelEnd = performance.now()
        const parallelTime = parallelEnd - parallelStart

        const improvement = ((serialTime - parallelTime) / serialTime) * 100

        console.log(`🔄 串行生成: ${serialTime.toFixed(2)}ms`)
        console.log(`⚡ 并行生成: ${parallelTime.toFixed(2)}ms - 提升 ${improvement.toFixed(1)}%`)

        expect(serialUsers).toHaveLength(count)
        expect(parallelUsers).toHaveLength(count)
        expect(parallelUsers[0]).toHaveProperty('id')
      }
    })
  })

  describe('🔍 内存使用优化测试', () => {
    it('应该优化大批量生成的内存使用', async () => {
      console.log('\n💾 内存使用优化测试')
      console.log('=' .repeat(50))

      const count = 10000

      // 测试超优化版本内存使用
      const memBefore = process.memoryUsage()
      const users = UltraOptimizedUserFactory.createBatch(count)
      const memAfter = process.memoryUsage()

      const memoryUsed = memAfter.heapUsed - memBefore.heapUsed
      const memoryPerUser = memoryUsed / count

      console.log(`📊 生成 ${count.toLocaleString()} 个用户:`)
      console.log(`   总内存使用: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`)
      console.log(`   每用户内存: ${(memoryPerUser / 1024).toFixed(2)}KB`)

      expect(users).toHaveLength(count)
      expect(memoryPerUser).toBeLessThan(2048) // 每用户 < 2KB
      expect(memoryUsed).toBeLessThan(20 * 1024 * 1024) // 总计 < 20MB
    })

    it('应该验证内存池的效果', async () => {
      console.log('\n🔄 内存池效果验证')
      console.log('=' .repeat(50))

      const count = 5000
      const iterations = 3

      console.log(`📊 连续生成 ${iterations} 次，每次 ${count.toLocaleString()} 个用户:`)

      let totalMemoryGrowth = 0

      for (let i = 0; i < iterations; i++) {
        const memBefore = process.memoryUsage()
        const users = MemoryPoolUserFactory.createBatch(count)
        const memAfter = process.memoryUsage()

        const memoryGrowth = memAfter.heapUsed - memBefore.heapUsed
        totalMemoryGrowth += memoryGrowth

        console.log(`   第${i + 1}次: ${(memoryGrowth / 1024 / 1024).toFixed(2)}MB`)

        expect(users).toHaveLength(count)
      }

      const avgMemoryGrowth = totalMemoryGrowth / iterations
      console.log(`   平均内存增长: ${(avgMemoryGrowth / 1024 / 1024).toFixed(2)}MB`)

      // 内存池应该显著减少内存增长
      expect(avgMemoryGrowth).toBeLessThan(10 * 1024 * 1024) // < 10MB
    })
  })

  describe('🎯 数据质量验证', () => {
    it('应该生成高质量的用户数据', () => {
      const users = UltraOptimizedUserFactory.createBatch(100)

      // 验证数据结构完整性
      users.forEach((user, index) => {
        expect(user).toHaveProperty('id')
        expect(user).toHaveProperty('userId')
        expect(user).toHaveProperty('name')
        expect(user).toHaveProperty('email')
        expect(user).toHaveProperty('city')
        expect(user).toHaveProperty('district')
        expect(user).toHaveProperty('creditScore')
        expect(user).toHaveProperty('status')
        expect(user).toHaveProperty('role')

        // 验证数据格式
        expect(user.id).toMatch(/^id-\d{8}$/)
        expect(user.userId).toMatch(/^user-\d{8}$/)
        expect(user.email).toContain('@')
        expect(user.creditScore).toBeGreaterThanOrEqual(0)
        expect(user.creditScore).toBeLessThanOrEqual(100)
        expect(['ACTIVE', 'SUSPENDED', 'BANNED']).toContain(user.status)
        expect(['USER', 'ADMIN']).toContain(user.role)
      })

      // 验证ID唯一性
      const ids = users.map(u => u.id)
      const uniqueIds = new Set(ids)
      expect(uniqueIds.size).toBe(users.length)
    })

    it('应该支持专门的买家和卖家生成', () => {
      const buyers = Array.from({ length: 50 }, () => UltraOptimizedUserFactory.createBuyer())
      const sellers = Array.from({ length: 50 }, () => UltraOptimizedUserFactory.createSeller())

      // 验证买家特征
      buyers.forEach(buyer => {
        expect(buyer.role).toBe('USER')
        expect(buyer.creditScore).toBeGreaterThanOrEqual(70)
        expect(buyer.creditScore).toBeLessThanOrEqual(100)
      })

      // 验证卖家特征
      sellers.forEach(seller => {
        expect(seller.role).toBe('USER')
        expect(seller.creditScore).toBeGreaterThanOrEqual(80)
        expect(seller.creditScore).toBeLessThanOrEqual(100)
        expect(seller.depositBalance).toBeGreaterThanOrEqual(100)
        expect(seller.depositBalance).toBeLessThanOrEqual(5000)
      })
    })
  })

  describe('📈 极限性能测试', () => {
    it('应该在极限条件下保持高性能', async () => {
      console.log('\n🔥 极限性能测试')
      console.log('=' .repeat(50))

      const extremeCounts = [50000, 100000]

      for (const count of extremeCounts) {
        console.log(`\n🚀 极限测试: ${count.toLocaleString()} 个用户`)
        console.log('-'.repeat(30))

        const start = performance.now()
        const users = UltraOptimizedUserFactory.createBatch(count)
        const end = performance.now()

        const duration = end - start
        const rate = count / (duration / 1000)

        console.log(`   生成时间: ${duration.toFixed(2)}ms`)
        console.log(`   生成速度: ${rate.toFixed(0)} users/sec`)
        console.log(`   内存使用: ${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2)}MB`)

        expect(users).toHaveLength(count)
        expect(duration).toBeLessThan(count / 10) // 目标: > 10,000 users/sec
        expect(users[0]).toHaveProperty('id')
        expect(users[count - 1]).toHaveProperty('id')
      }
    })
  })
})
