import { describe, it, expect, vi } from 'vitest'
import { UserFactory, ProductFactory, OrderFactory } from '@/test/factories/data-factory'

describe('简单性能测试', () => {
  describe('数据生成性能', () => {
    it('应该快速生成大量用户数据', () => {
      const startTime = performance.now()
      
      const users = UserFactory.createBatch(1000)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(users).toHaveLength(1000)
      expect(duration).toBeLessThan(1000) // 应该在1秒内完成
      
      console.log(`生成1000个用户耗时: ${duration.toFixed(2)}ms`)
    })

    it('应该快速生成大量商品数据', () => {
      const startTime = performance.now()
      
      const products = ProductFactory.createBatch(500)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(products).toHaveLength(500)
      expect(duration).toBeLessThan(500) // 应该在0.5秒内完成
      
      console.log(`生成500个商品耗时: ${duration.toFixed(2)}ms`)
    })

    it('应该快速生成大量订单数据', () => {
      const startTime = performance.now()
      
      const orders = OrderFactory.createBatch(200)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(orders).toHaveLength(200)
      expect(duration).toBeLessThan(300) // 应该在0.3秒内完成
      
      console.log(`生成200个订单耗时: ${duration.toFixed(2)}ms`)
    })
  })

  describe('并发操作模拟', () => {
    it('应该处理并发数据库查询模拟', async () => {
      const mockDbQuery = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ success: true }), 10))
      )

      const startTime = performance.now()
      
      // 模拟50个并发查询
      const queries = Array.from({ length: 50 }, () => mockDbQuery())
      const results = await Promise.all(queries)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(results).toHaveLength(50)
      expect(results.every(r => r.success)).toBe(true)
      expect(duration).toBeLessThan(100) // 并发执行应该很快
      
      console.log(`50个并发查询耗时: ${duration.toFixed(2)}ms`)
    })

    it('应该处理批量API请求模拟', async () => {
      const mockApiCall = vi.fn().mockImplementation((id) => 
        Promise.resolve({ id, status: 'success', data: `result-${id}` })
      )

      const startTime = performance.now()
      
      // 模拟100个API调用
      const apiCalls = Array.from({ length: 100 }, (_, i) => mockApiCall(i))
      const responses = await Promise.all(apiCalls)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(responses).toHaveLength(100)
      expect(responses.every(r => r.status === 'success')).toBe(true)
      expect(duration).toBeLessThan(50) // 模拟调用应该很快
      
      console.log(`100个API调用耗时: ${duration.toFixed(2)}ms`)
    })
  })

  describe('内存使用测试', () => {
    it('应该有效管理大量数据的内存使用', () => {
      const initialMemory = process.memoryUsage().heapUsed
      
      // 创建大量数据
      const users = UserFactory.createBatch(1000)
      const products = ProductFactory.createBatch(1000)
      const orders = OrderFactory.createBatch(500)
      
      const afterCreationMemory = process.memoryUsage().heapUsed
      const memoryIncrease = afterCreationMemory - initialMemory
      
      expect(users).toHaveLength(1000)
      expect(products).toHaveLength(1000)
      expect(orders).toHaveLength(500)
      
      // 内存增加应该在合理范围内（小于50MB）
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
      
      console.log(`创建2500个对象内存增加: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`)
      
      // 清理数据
      users.length = 0
      products.length = 0
      orders.length = 0
    })
  })

  describe('算法性能测试', () => {
    it('应该快速搜索大量数据', () => {
      const products = ProductFactory.createBatch(10000)
      const searchTerm = 'test'
      
      const startTime = performance.now()
      
      // 模拟搜索操作
      const results = products.filter(product => 
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(results).toBeDefined()
      expect(duration).toBeLessThan(100) // 搜索应该在100ms内完成
      
      console.log(`搜索10000个商品耗时: ${duration.toFixed(2)}ms，找到${results.length}个结果`)
    })

    it('应该快速排序大量数据', () => {
      const products = ProductFactory.createBatch(5000)
      
      const startTime = performance.now()
      
      // 按价格排序
      const sortedProducts = products.sort((a, b) => a.price - b.price)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(sortedProducts).toHaveLength(5000)
      expect(duration).toBeLessThan(50) // 排序应该在50ms内完成
      
      // 验证排序正确性
      for (let i = 1; i < sortedProducts.length; i++) {
        expect(sortedProducts[i].price).toBeGreaterThanOrEqual(sortedProducts[i - 1].price)
      }
      
      console.log(`排序5000个商品耗时: ${duration.toFixed(2)}ms`)
    })

    it('应该快速分页处理大量数据', () => {
      const products = ProductFactory.createBatch(10000)
      const pageSize = 20
      const pageNumber = 50
      
      const startTime = performance.now()
      
      // 模拟分页
      const startIndex = (pageNumber - 1) * pageSize
      const endIndex = startIndex + pageSize
      const pageData = products.slice(startIndex, endIndex)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(pageData).toHaveLength(pageSize)
      expect(duration).toBeLessThan(10) // 分页应该非常快
      
      console.log(`分页处理10000个商品耗时: ${duration.toFixed(2)}ms`)
    })
  })

  describe('缓存性能模拟', () => {
    it('应该模拟缓存命中性能', async () => {
      const cache = new Map()
      const cacheKey = 'test-key'
      const cacheValue = { data: 'cached data' }
      
      // 预填充缓存
      cache.set(cacheKey, cacheValue)
      
      const startTime = performance.now()
      
      // 模拟100次缓存查询
      const cacheHits = Array.from({ length: 100 }, () => cache.get(cacheKey))
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(cacheHits).toHaveLength(100)
      expect(cacheHits.every(hit => hit === cacheValue)).toBe(true)
      expect(duration).toBeLessThan(5) // 缓存查询应该非常快
      
      console.log(`100次缓存查询耗时: ${duration.toFixed(2)}ms`)
    })

    it('应该模拟缓存未命中性能', async () => {
      const cache = new Map()
      const mockDbQuery = vi.fn().mockImplementation(() => 
        Promise.resolve({ data: 'database data' })
      )
      
      const startTime = performance.now()
      
      // 模拟缓存未命中，需要查询数据库
      const queries = Array.from({ length: 10 }, async (_, i) => {
        const key = `key-${i}`
        if (cache.has(key)) {
          return cache.get(key)
        } else {
          const data = await mockDbQuery()
          cache.set(key, data)
          return data
        }
      })
      
      const results = await Promise.all(queries)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(results).toHaveLength(10)
      expect(mockDbQuery).toHaveBeenCalledTimes(10)
      expect(duration).toBeLessThan(100) // 包含数据库查询的操作
      
      console.log(`10次缓存未命中查询耗时: ${duration.toFixed(2)}ms`)
    })
  })
})
