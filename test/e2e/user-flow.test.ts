import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { mockPrisma } from '@/test/db-mock'
import { 
  mockUsers, 
  mockProducts, 
  mockOrders, 
  generateRandomEmail,
  generateRandomUserId,
  cleanupDatabase 
} from '@/test/test-utils'

// Mock Next.js components and hooks
vi.mock('next/navigation')
vi.mock('next-auth/react', () => ({
  useSession: vi.fn(),
  signIn: vi.fn(),
  signOut: vi.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children
}))

import { useSession, signIn } from 'next-auth/react'
const mockUseSession = vi.mocked(useSession)
const mockSignIn = vi.mocked(signIn)

describe('用户功能端到端测试', () => {
  beforeEach(async () => {
    await cleanupDatabase()
    vi.clearAllMocks()
  })

  describe('用户注册和登录流程', () => {
    it('应该完成完整的注册流程', async () => {
      const userData = {
        email: generateRandomEmail(),
        password: 'password123',
        name: 'Test User',
        binanceUid: '123456789',
        bnbWalletAddress: '0x1234567890abcdef',
        city: '北京',
        district: '朝阳区'
      }

      // Mock 注册API响应
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          message: '注册成功',
          user: {
            id: 'new-user-id',
            email: userData.email,
            name: userData.name,
            userId: generateRandomUserId()
          }
        })
      })

      // 模拟注册表单提交
      const registerData = new FormData()
      Object.entries(userData).forEach(([key, value]) => {
        registerData.append(key, value)
      })

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        body: registerData
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.message).toBe('注册成功')
      expect(result.user.email).toBe(userData.email)
    })

    it('应该完成登录流程', async () => {
      const loginData = {
        email: mockUsers.buyer.email,
        password: 'password123'
      }

      mockSignIn.mockResolvedValue({
        ok: true,
        status: 200,
        error: null,
        url: null
      })

      const result = await signIn('credentials', {
        email: loginData.email,
        password: loginData.password,
        redirect: false
      })

      expect(result?.ok).toBe(true)
      expect(mockSignIn).toHaveBeenCalledWith('credentials', {
        email: loginData.email,
        password: loginData.password,
        redirect: false
      })
    })
  })

  describe('商品浏览和搜索流程', () => {
    it('应该能够浏览商品列表', async () => {
      const mockProductsList = [
        { ...mockProducts.available, seller: mockUsers.seller },
        { 
          ...mockProducts.available, 
          id: 'product-2',
          title: 'Another Product',
          seller: mockUsers.seller 
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          products: mockProductsList,
          total: 2,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch('/api/products?page=1&limit=10')
      const data = await response.json()

      expect(data.products).toHaveLength(2)
      expect(data.products[0].title).toBe(mockProducts.available.title)
      expect(data.total).toBe(2)
    })

    it('应该能够搜索商品', async () => {
      const searchTerm = 'iPhone'
      const mockSearchResults = [
        { 
          ...mockProducts.available, 
          title: 'iPhone 15 Pro',
          seller: mockUsers.seller 
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          products: mockSearchResults,
          total: 1,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch(`/api/products?search=${encodeURIComponent(searchTerm)}`)
      const data = await response.json()

      expect(data.products).toHaveLength(1)
      expect(data.products[0].title).toContain('iPhone')
    })

    it('应该能够查看商品详情', async () => {
      const productWithDetails = {
        ...mockProducts.available,
        seller: mockUsers.seller,
        reviews: [],
        _count: { reviews: 0 }
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => productWithDetails
      })

      const response = await fetch(`/api/products/${mockProducts.available.id}`)
      const data = await response.json()

      expect(data.id).toBe(mockProducts.available.id)
      expect(data.title).toBe(mockProducts.available.title)
      expect(data.seller).toBeDefined()
      expect(data.seller.id).toBe(mockUsers.seller.id)
    })
  })

  describe('下单和支付流程', () => {
    beforeEach(() => {
      // Mock 已登录状态
      mockUseSession.mockReturnValue({
        data: {
          user: {
            id: mockUsers.buyer.id,
            email: mockUsers.buyer.email,
            name: mockUsers.buyer.name,
            userId: mockUsers.buyer.userId,
            role: mockUsers.buyer.role
          },
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        status: 'authenticated'
      })
    })

    it('应该能够创建订单', async () => {
      const orderData = {
        productId: mockProducts.available.id,
        quantity: 1,
        shippingAddress: {
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '某某街道123号'
        }
      }

      const createdOrder = {
        ...mockOrders.pending,
        id: 'new-order-id',
        totalAmount: mockProducts.available.price,
        productPrice: mockProducts.available.price
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => createdOrder
      })

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.id).toBe('new-order-id')
      expect(result.status).toBe('PENDING_PAYMENT')
      expect(result.totalAmount).toBe(mockProducts.available.price)
    })

    it('应该能够获取支付信息', async () => {
      const paymentInfo = {
        order: mockOrders.pending,
        escrowPayments: [],
        paymentQRCode: 'data:image/png;base64,mock-qr-code'
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => paymentInfo
      })

      const response = await fetch(`/api/orders/${mockOrders.pending.id}/payment`)
      const data = await response.json()

      expect(data.order).toBeDefined()
      expect(data.paymentQRCode).toBe('data:image/png;base64,mock-qr-code')
    })

    it('应该能够提交支付信息', async () => {
      const paymentData = {
        paymentMethod: 'binance_pay',
        orderNumber: 'BN123456789',
        screenshot: '/uploads/payment/screenshot.jpg'
      }

      const updatedOrder = {
        ...mockOrders.pending,
        status: 'PAID',
        paymentMethod: 'binance_pay',
        paymentTxHash: paymentData.orderNumber,
        paymentScreenshot: paymentData.screenshot
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => updatedOrder
      })

      const response = await fetch(`/api/orders/${mockOrders.pending.id}/payment`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('PAID')
      expect(result.paymentMethod).toBe('binance_pay')
    })
  })

  describe('消息交流流程', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: {
          user: {
            id: mockUsers.buyer.id,
            email: mockUsers.buyer.email,
            name: mockUsers.buyer.name,
            userId: mockUsers.buyer.userId,
            role: mockUsers.buyer.role
          },
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        status: 'authenticated'
      })
    })

    it('应该能够发送文本消息', async () => {
      const messageData = {
        orderId: mockOrders.pending.id,
        content: 'Hello, is this item still available?',
        messageType: 'TEXT'
      }

      const createdMessage = {
        id: 'new-message-id',
        content: messageData.content,
        messageType: 'TEXT',
        senderId: mockUsers.buyer.id,
        receiverId: mockUsers.seller.id,
        createdAt: new Date().toISOString(),
        sender: mockUsers.buyer,
        receiver: mockUsers.seller
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => createdMessage
      })

      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(messageData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.content).toBe(messageData.content)
      expect(result.messageType).toBe('TEXT')
    })

    it('应该能够获取消息历史', async () => {
      const messagesList = [
        {
          id: 'msg-1',
          content: 'Hello',
          messageType: 'TEXT',
          senderId: mockUsers.buyer.id,
          receiverId: mockUsers.seller.id,
          createdAt: new Date().toISOString(),
          sender: mockUsers.buyer,
          receiver: mockUsers.seller
        },
        {
          id: 'msg-2',
          content: 'Hi there!',
          messageType: 'TEXT',
          senderId: mockUsers.seller.id,
          receiverId: mockUsers.buyer.id,
          createdAt: new Date().toISOString(),
          sender: mockUsers.seller,
          receiver: mockUsers.buyer
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => messagesList
      })

      const response = await fetch(`/api/messages?orderId=${mockOrders.pending.id}`)
      const data = await response.json()

      expect(data).toHaveLength(2)
      expect(data[0].content).toBe('Hello')
      expect(data[1].content).toBe('Hi there!')
    })
  })

  describe('订单状态更新流程', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: {
          user: {
            id: mockUsers.buyer.id,
            email: mockUsers.buyer.email,
            name: mockUsers.buyer.name,
            userId: mockUsers.buyer.userId,
            role: mockUsers.buyer.role
          },
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        status: 'authenticated'
      })
    })

    it('应该能够确认收货', async () => {
      const updateData = {
        action: 'confirm_received'
      }

      const updatedOrder = {
        ...mockOrders.paid,
        status: 'COMPLETED',
        receivedAt: new Date().toISOString()
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => updatedOrder
      })

      const response = await fetch(`/api/orders/${mockOrders.paid.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('COMPLETED')
      expect(result.receivedAt).toBeDefined()
    })
  })

  describe('完整用户流程集成测试', () => {
    it('应该完成从注册到交易完成的完整流程', async () => {
      // 1. 用户注册
      const userData = {
        email: generateRandomEmail(),
        password: 'password123',
        name: 'Integration Test User'
      }

      global.fetch = vi.fn()
        // 注册
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            message: '注册成功',
            user: { id: 'new-user-id', email: userData.email }
          })
        })
        // 获取商品列表
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            products: [{ ...mockProducts.available, seller: mockUsers.seller }],
            total: 1
          })
        })
        // 创建订单
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            ...mockOrders.pending,
            id: 'integration-order-id'
          })
        })
        // 提交支付
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            ...mockOrders.pending,
            id: 'integration-order-id',
            status: 'PAID'
          })
        })
        // 发送消息
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            id: 'integration-message-id',
            content: 'Payment submitted',
            messageType: 'TEXT'
          })
        })
        // 确认收货
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            ...mockOrders.pending,
            id: 'integration-order-id',
            status: 'COMPLETED'
          })
        })

      // 执行完整流程
      const steps = [
        // 1. 注册
        () => fetch('/api/auth/register', {
          method: 'POST',
          body: JSON.stringify(userData)
        }),
        // 2. 浏览商品
        () => fetch('/api/products'),
        // 3. 创建订单
        () => fetch('/api/orders', {
          method: 'POST',
          body: JSON.stringify({
            productId: mockProducts.available.id,
            quantity: 1
          })
        }),
        // 4. 支付
        () => fetch('/api/orders/integration-order-id/payment', {
          method: 'POST',
          body: JSON.stringify({
            paymentMethod: 'binance_pay',
            orderNumber: 'BN123456789'
          })
        }),
        // 5. 发送消息
        () => fetch('/api/messages', {
          method: 'POST',
          body: JSON.stringify({
            orderId: 'integration-order-id',
            content: 'Payment submitted',
            messageType: 'TEXT'
          })
        }),
        // 6. 确认收货
        () => fetch('/api/orders/integration-order-id', {
          method: 'PATCH',
          body: JSON.stringify({
            action: 'confirm_received'
          })
        })
      ]

      // 执行所有步骤
      for (const step of steps) {
        const response = await step()
        expect(response.ok).toBe(true)
      }

      // 验证所有API调用都被执行
      expect(global.fetch).toHaveBeenCalledTimes(6)
    })
  })
})
