import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mockPrisma } from '@/test/db-mock'
import { 
  createMockRequest, 
  createMockSession, 
  mockUsers, 
  mockProducts,
  mockOrders,
  cleanupDatabase 
} from '@/test/test-utils'
import { getServerSession } from 'next-auth/next'

vi.mock('next-auth/next')
const mockGetServerSession = vi.mocked(getServerSession)

describe('管理员功能测试', () => {
  beforeEach(async () => {
    await cleanupDatabase()
    vi.clearAllMocks()
  })

  describe('管理员权限验证', () => {
    it('应该验证管理员身份', async () => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)

      // 模拟管理员API调用
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, role: 'ADMIN' })
      })

      const response = await fetch('/api/admin/verify', {
        method: 'GET',
        headers: { 'Authorization': 'Bearer admin-token' }
      })

      expect(response.ok).toBe(true)
      const data = await response.json()
      expect(data.role).toBe('ADMIN')
    })

    it('应该拒绝非管理员用户访问管理功能', async () => {
      const userSession = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(userSession)

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ error: '权限不足' })
      })

      const response = await fetch('/api/admin/users', {
        method: 'GET'
      })

      expect(response.ok).toBe(false)
      expect(response.status).toBe(403)
    })
  })

  describe('商品审核功能', () => {
    beforeEach(() => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
    })

    it('应该获取待审核商品列表', async () => {
      const pendingProducts = [
        { ...mockProducts.pending, seller: mockUsers.seller },
        { 
          ...mockProducts.pending, 
          id: 'product-pending-2',
          title: 'Another Pending Product',
          seller: mockUsers.seller 
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          products: pendingProducts,
          total: 2,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch('/api/admin/products/pending')
      const data = await response.json()

      expect(data.products).toHaveLength(2)
      expect(data.products[0].reviewStatus).toBe('PENDING')
      expect(data.products[1].reviewStatus).toBe('PENDING')
    })

    it('应该能够审核通过商品', async () => {
      const approvalData = {
        action: 'approve',
        reason: '商品信息完整，符合平台规范'
      }

      const approvedProduct = {
        ...mockProducts.pending,
        reviewStatus: 'APPROVED'
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => approvedProduct
      })

      const response = await fetch(`/api/admin/products/${mockProducts.pending.id}/review`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(approvalData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.reviewStatus).toBe('APPROVED')
    })

    it('应该能够拒绝商品', async () => {
      const rejectionData = {
        action: 'reject',
        reason: '商品描述不清晰，图片质量不符合要求'
      }

      const rejectedProduct = {
        ...mockProducts.pending,
        reviewStatus: 'REJECTED'
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => rejectedProduct
      })

      const response = await fetch(`/api/admin/products/${mockProducts.pending.id}/review`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(rejectionData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.reviewStatus).toBe('REJECTED')
    })
  })

  describe('用户管理功能', () => {
    beforeEach(() => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
    })

    it('应该获取用户列表', async () => {
      const usersList = [
        mockUsers.buyer,
        mockUsers.seller,
        { 
          ...mockUsers.buyer, 
          id: 'user-3',
          email: '<EMAIL>',
          name: 'User 3' 
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          users: usersList,
          total: 3,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch('/api/admin/users?page=1&limit=10')
      const data = await response.json()

      expect(data.users).toHaveLength(3)
      expect(data.total).toBe(3)
    })

    it('应该能够封禁用户', async () => {
      const banData = {
        action: 'ban',
        reason: '违反平台规则',
        duration: 7 // 7天
      }

      const bannedUser = {
        ...mockUsers.buyer,
        status: 'BANNED',
        bannedAt: new Date().toISOString(),
        bannedUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        banReason: banData.reason,
        bannedBy: mockUsers.admin.id
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => bannedUser
      })

      const response = await fetch(`/api/admin/users/${mockUsers.buyer.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(banData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('BANNED')
      expect(result.banReason).toBe(banData.reason)
    })

    it('应该能够解封用户', async () => {
      const unbanData = {
        action: 'unban',
        reason: '申诉成功，恢复账户'
      }

      const unbannedUser = {
        ...mockUsers.buyer,
        status: 'ACTIVE',
        bannedAt: null,
        bannedUntil: null,
        banReason: null,
        bannedBy: null
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => unbannedUser
      })

      const response = await fetch(`/api/admin/users/${mockUsers.buyer.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(unbanData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('ACTIVE')
      expect(result.banReason).toBeNull()
    })

    it('应该能够调整用户信用分', async () => {
      const creditData = {
        action: 'adjust_credit',
        changeScore: -10,
        reason: '交易纠纷处理'
      }

      const updatedUser = {
        ...mockUsers.buyer,
        creditScore: mockUsers.buyer.creditScore - 10
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => updatedUser
      })

      const response = await fetch(`/api/admin/users/${mockUsers.buyer.id}/credit`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(creditData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.creditScore).toBe(mockUsers.buyer.creditScore - 10)
    })
  })

  describe('订单监控功能', () => {
    beforeEach(() => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
    })

    it('应该获取所有订单列表', async () => {
      const ordersList = [
        { ...mockOrders.pending, buyer: mockUsers.buyer, seller: mockUsers.seller },
        { ...mockOrders.paid, buyer: mockUsers.buyer, seller: mockUsers.seller }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          orders: ordersList,
          total: 2,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch('/api/admin/orders?page=1&limit=10')
      const data = await response.json()

      expect(data.orders).toHaveLength(2)
      expect(data.orders[0].buyer).toBeDefined()
      expect(data.orders[0].seller).toBeDefined()
    })

    it('应该能够强制完成订单', async () => {
      const forceCompleteData = {
        action: 'force_complete',
        reason: '买家长时间未确认收货，系统自动完成'
      }

      const completedOrder = {
        ...mockOrders.paid,
        status: 'COMPLETED',
        receivedAt: new Date().toISOString()
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => completedOrder
      })

      const response = await fetch(`/api/admin/orders/${mockOrders.paid.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(forceCompleteData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('COMPLETED')
      expect(result.receivedAt).toBeDefined()
    })

    it('应该能够处理退款申请', async () => {
      const refundData = {
        action: 'approve_refund',
        refundAmount: 50.0,
        reason: '商品质量问题，同意退款'
      }

      const refundedOrder = {
        ...mockOrders.paid,
        status: 'REFUNDED',
        refundAmount: refundData.refundAmount,
        refundReason: refundData.reason
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => refundedOrder
      })

      const response = await fetch(`/api/admin/orders/${mockOrders.paid.id}/refund`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(refundData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('REFUNDED')
      expect(result.refundAmount).toBe(refundData.refundAmount)
    })
  })

  describe('申诉处理功能', () => {
    beforeEach(() => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
    })

    it('应该获取待处理申诉列表', async () => {
      const feedbackList = [
        {
          id: 'feedback-1',
          userId: mockUsers.buyer.id,
          category: 'APPEAL',
          title: '订单争议申诉',
          description: '卖家发货的商品与描述不符',
          status: 'PENDING',
          priority: 'HIGH',
          user: mockUsers.buyer,
          createdAt: new Date().toISOString()
        },
        {
          id: 'feedback-2',
          userId: mockUsers.seller.id,
          category: 'APPEAL',
          title: '买家恶意退款',
          description: '买家收货后恶意申请退款',
          status: 'PENDING',
          priority: 'MEDIUM',
          user: mockUsers.seller,
          createdAt: new Date().toISOString()
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          feedbacks: feedbackList,
          total: 2,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch('/api/admin/feedbacks?category=APPEAL&status=PENDING')
      const data = await response.json()

      expect(data.feedbacks).toHaveLength(2)
      expect(data.feedbacks[0].category).toBe('APPEAL')
      expect(data.feedbacks[1].category).toBe('APPEAL')
    })

    it('应该能够处理申诉', async () => {
      const appealResponse = {
        action: 'resolve',
        adminResponse: '经调查，卖家确实存在描述不符的问题，支持买家申诉',
        resolution: 'BUYER_FAVOR'
      }

      const resolvedFeedback = {
        id: 'feedback-1',
        status: 'RESOLVED',
        adminResponse: appealResponse.adminResponse,
        assignedToId: mockUsers.admin.id,
        resolvedAt: new Date().toISOString()
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => resolvedFeedback
      })

      const response = await fetch('/api/admin/feedbacks/feedback-1/resolve', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(appealResponse)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.status).toBe('RESOLVED')
      expect(result.adminResponse).toBe(appealResponse.adminResponse)
    })
  })

  describe('系统配置管理', () => {
    beforeEach(() => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
    })

    it('应该能够更新系统配置', async () => {
      const configData = {
        platformFeeRate: 0.02, // 2%
        minDepositAmount: 10.0,
        maxDepositAmount: 10000.0,
        autoConfirmDays: 7
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          config: configData
        })
      })

      const response = await fetch('/api/admin/config', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(configData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.success).toBe(true)
      expect(result.config.platformFeeRate).toBe(0.02)
    })

    it('应该能够获取系统统计数据', async () => {
      const statsData = {
        totalUsers: 1250,
        totalProducts: 3420,
        totalOrders: 8750,
        totalRevenue: 125000.50,
        activeUsers: 890,
        pendingOrders: 45,
        completedOrders: 8200,
        disputedOrders: 12
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => statsData
      })

      const response = await fetch('/api/admin/stats')
      const data = await response.json()

      expect(data.totalUsers).toBe(1250)
      expect(data.totalProducts).toBe(3420)
      expect(data.totalOrders).toBe(8750)
      expect(data.totalRevenue).toBe(125000.50)
    })
  })

  describe('管理员操作日志', () => {
    beforeEach(() => {
      const adminSession = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(adminSession)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
    })

    it('应该记录管理员操作', async () => {
      const operationData = {
        action: 'USER_BANNED',
        targetId: mockUsers.buyer.id,
        reason: '违反平台规则',
        details: { duration: 7, banReason: '恶意刷单' }
      }

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          logId: 'admin-log-123'
        })
      })

      const response = await fetch('/api/admin/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(operationData)
      })

      expect(response.ok).toBe(true)
      const result = await response.json()
      expect(result.success).toBe(true)
      expect(result.logId).toBeDefined()
    })

    it('应该获取操作日志列表', async () => {
      const logsList = [
        {
          id: 'log-1',
          adminId: mockUsers.admin.id,
          action: 'USER_BANNED',
          targetId: mockUsers.buyer.id,
          reason: '违反平台规则',
          createdAt: new Date().toISOString(),
          admin: mockUsers.admin
        },
        {
          id: 'log-2',
          adminId: mockUsers.admin.id,
          action: 'PRODUCT_APPROVED',
          targetId: mockProducts.pending.id,
          reason: '商品审核通过',
          createdAt: new Date().toISOString(),
          admin: mockUsers.admin
        }
      ]

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          logs: logsList,
          total: 2,
          page: 1,
          limit: 10
        })
      })

      const response = await fetch('/api/admin/logs?page=1&limit=10')
      const data = await response.json()

      expect(data.logs).toHaveLength(2)
      expect(data.logs[0].action).toBe('USER_BANNED')
      expect(data.logs[1].action).toBe('PRODUCT_APPROVED')
    })
  })
})
