# BitMarket Release 自动化脚本
# 完整的release流程：版本更新、打包、git操作、release内容生成

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("major", "minor", "patch", "current")]
    [string]$VersionType = "patch",

    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,

    [Parameter(Mandatory=$false)]
    [switch]$SkipGit,

    [Parameter(Mandatory=$false)]
    [switch]$DryRun,

    [Parameter(Mandatory=$false)]
    [string]$ReleaseMessage = ""
)

# 颜色输出函数
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "`n🔄 $Message" "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

# 检查Git状态
function Test-GitStatus {
    Write-Step "检查Git工作目录状态..."
    
    $gitStatus = git status --porcelain
    if ($gitStatus) {
        Write-Warning "工作目录有未提交的更改："
        git status --short
        
        $response = Read-Host "`n是否继续？未提交的更改将被包含在release中 (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Error "Release已取消"
            exit 1
        }
    } else {
        Write-Success "工作目录干净"
    }
}

# 版本号处理
function Get-NextVersion {
    param([string]$CurrentVersion, [string]$Type)
    
    $versionParts = $CurrentVersion.Split('.')
    $major = [int]$versionParts[0]
    $minor = [int]$versionParts[1]
    $patch = [int]$versionParts[2]
    
    switch ($Type) {
        "major" { 
            $major++
            $minor = 0
            $patch = 0
        }
        "minor" { 
            $minor++
            $patch = 0
        }
        "patch" { 
            $patch++
        }
        "current" {
            # 保持当前版本
        }
    }
    
    return "$major.$minor.$patch"
}

# 更新package.json版本
function Update-PackageVersion {
    param([string]$NewVersion)
    
    Write-Step "更新package.json版本到 v$NewVersion..."
    
    $packageJson = Get-Content "package.json" | ConvertFrom-Json
    $packageJson.version = $NewVersion
    $packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json" -Encoding UTF8
    
    Write-Success "package.json版本已更新"
}

# 运行测试
function Invoke-Tests {
    if ($SkipTests) {
        Write-Warning "跳过测试"
        return
    }
    
    Write-Step "运行项目测试..."
    
    try {
        npm run test:ci
        Write-Success "所有测试通过"
    } catch {
        Write-Error "测试失败: $($_.Exception.Message)"
        $response = Read-Host "是否继续release？(y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            exit 1
        }
    }
}

# 构建项目
function Invoke-Build {
    Write-Step "构建项目..."
    
    try {
        npm run build
        Write-Success "项目构建成功"
    } catch {
        Write-Error "项目构建失败: $($_.Exception.Message)"
        exit 1
    }
}

# 更新CHANGELOG
function Update-Changelog {
    param([string]$Version, [string]$ReleaseMessage)
    
    Write-Step "更新CHANGELOG.md..."
    
    $date = Get-Date -Format "yyyy-MM-dd"
    $changelogEntry = @"

## [$Version] - $date

### 🚀 新功能 | New Features
$ReleaseMessage

### 🔧 技术改进 | Technical Improvements
- 版本更新到 v$Version
- 构建系统优化
- 依赖管理完善

---

"@
    
    $changelog = Get-Content "CHANGELOG.md" -Raw
    $updatedChangelog = $changelog -replace "(# 更新日志 \| Changelog\s+本文档记录了BitMarket项目的所有重要变更。)", "`$1$changelogEntry"
    
    Set-Content "CHANGELOG.md" -Value $updatedChangelog -Encoding UTF8
    Write-Success "CHANGELOG.md已更新"
}

# 创建发布包
function New-ReleasePackage {
    Write-Step "创建发布包..."
    
    try {
        node scripts/create-release-package.js
        Write-Success "发布包创建成功"
    } catch {
        Write-Error "发布包创建失败: $($_.Exception.Message)"
        exit 1
    }
}

# Git操作
function Invoke-GitOperations {
    param([string]$Version)
    
    if ($SkipGit) {
        Write-Warning "跳过Git操作"
        return
    }
    
    Write-Step "执行Git操作..."
    
    # 添加所有更改
    git add .
    
    # 提交更改
    $commitMessage = "chore: release v$Version"
    git commit -m $commitMessage
    
    # 创建标签
    $tagMessage = "Release v$Version"
    git tag -a "v$Version" -m $tagMessage
    
    # 推送到远程
    git push origin main
    git push origin "v$Version"
    
    Write-Success "Git操作完成"
}

# 生成Release Notes
function New-ReleaseNotes {
    param([string]$Version)
    
    Write-Step "生成Release Notes..."
    
    $releaseNotes = @"
# BitMarket v$Version Release Notes

## 📋 版本信息
- **版本号**: v$Version
- **发布日期**: $(Get-Date -Format "yyyy-MM-dd")
- **构建状态**: ✅ 通过

## 🚀 主要更新

$ReleaseMessage

## 📦 下载
- [源码包 (ZIP)](https://github.com/liusu-ally/bitmarket/releases/download/v$Version/bitmarket-v$Version-source.zip)
- [源码包 (TAR.GZ)](https://github.com/liusu-ally/bitmarket/releases/download/v$Version/bitmarket-v$Version-source.tar.gz)

## 🔧 安装说明
1. 下载并解压源码包
2. 运行 ``npm install`` 安装依赖
3. 配置环境变量（参考.env.example）
4. 运行 ``npm run build`` 构建项目
5. 运行 ``npm start`` 启动服务

## 📚 文档
- [安装指南](./INSTALL.md)
- [更新日志](./CHANGELOG.md)
- [项目文档](./README.md)

## 🆘 支持
如有问题，请访问 [GitHub Issues](https://github.com/liusu-ally/bitmarket/issues)
"@
    
    $releaseNotesPath = "release/RELEASE_NOTES_v$Version.md"
    New-Item -ItemType Directory -Path "release" -Force | Out-Null
    Set-Content $releaseNotesPath -Value $releaseNotes -Encoding UTF8
    
    Write-Success "Release Notes已生成: $releaseNotesPath"
}

# 主函数
function Main {
    Write-ColorOutput "🚀 BitMarket Release 自动化脚本" "Green"
    Write-ColorOutput "=================================" "Green"
    
    # 检查是否在项目根目录
    if (!(Test-Path "package.json")) {
        Write-Error "请在项目根目录运行此脚本"
        exit 1
    }
    
    # 读取当前版本
    $packageJson = Get-Content "package.json" | ConvertFrom-Json
    $currentVersion = $packageJson.version
    
    # 计算新版本
    $newVersion = Get-NextVersion $currentVersion $VersionType
    
    Write-ColorOutput "`n📋 Release信息:" "Cyan"
    Write-ColorOutput "   当前版本: v$currentVersion" "White"
    Write-ColorOutput "   新版本: v$newVersion" "White"
    Write-ColorOutput "   版本类型: $VersionType" "White"
    Write-ColorOutput "   跳过测试: $SkipTests" "White"
    Write-ColorOutput "   跳过Git: $SkipGit" "White"
    Write-ColorOutput "   演练模式: $DryRun" "White"
    
    if ($DryRun) {
        Write-Warning "演练模式：不会执行实际操作"
        return
    }
    
    # 确认继续
    $response = Read-Host "`n是否继续执行release？(y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Warning "Release已取消"
        exit 0
    }
    
    try {
        # 执行release流程
        Test-GitStatus
        
        if ($VersionType -ne "current") {
            Update-PackageVersion $newVersion
        }
        
        Invoke-Tests
        Invoke-Build
        
        if ($ReleaseMessage) {
            Update-Changelog $newVersion $ReleaseMessage
        }
        
        New-ReleasePackage
        Invoke-GitOperations $newVersion
        New-ReleaseNotes $newVersion
        
        Write-ColorOutput "`n🎉 Release v$newVersion 完成！" "Green"
        Write-ColorOutput "📦 发布包位置: ./release/" "Cyan"
        Write-ColorOutput "📝 Release Notes: ./release/RELEASE_NOTES_v$newVersion.md" "Cyan"
        
    } catch {
        Write-Error "Release失败: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main
