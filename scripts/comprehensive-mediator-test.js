const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function comprehensiveMediatorTest() {
  try {
    console.log('=== 中间人系统综合测试 ===\n');
    
    // 1. 测试数据一致性
    console.log('1. 检查数据一致性...');
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        mediatorStatus: true,
        mediatorDeposit: true,
        depositBalance: true,
        frozenBalance: true,
        availableBalance: true
      }
    });
    
    console.log(`总中间人数: ${mediators.length}`);
    
    let dataConsistencyIssues = 0;
    mediators.forEach(m => {
      const expectedAvailable = Math.max(0, m.depositBalance - m.frozenBalance);
      const isConsistent = Math.abs(m.availableBalance - expectedAvailable) < 0.01;
      
      if (!isConsistent) {
        console.log(`❌ ${m.name}: availableBalance 不一致 (${m.availableBalance} vs 期望 ${expectedAvailable})`);
        dataConsistencyIssues++;
      }
    });
    
    if (dataConsistencyIssues === 0) {
      console.log('✅ 所有中间人的余额数据一致');
    } else {
      console.log(`❌ 发现 ${dataConsistencyIssues} 个数据一致性问题`);
    }
    
    // 2. 测试自动分配逻辑
    console.log('\n2. 测试自动分配逻辑...');
    const testAmounts = [100, 500, 1000, 5000, 10000];
    
    for (const amount of testAmounts) {
      console.log(`\n测试金额: ${amount} USDT`);
      
      // 模拟 auto-assign 逻辑
      const activeMediators = await prisma.user.findMany({
        where: {
          isMediator: true,
          mediatorStatus: 'ACTIVE'
        },
        select: {
          id: true,
          name: true,
          mediatorFeeRate: true,
          mediatorReputation: true,
          mediatorSuccessRate: true,
          mediatorTotalOrders: true,
          mediatorDeposit: true
        }
      });
      
      if (activeMediators.length === 0) {
        console.log('  ❌ 没有活跃中间人');
        continue;
      }
      
      const candidates = [];
      
      for (const mediator of activeMediators) {
        const mediatorBalance = await prisma.user.findUnique({
          where: { id: mediator.id },
          select: {
            depositBalance: true,
            frozenBalance: true,
            availableBalance: true
          }
        });

        if (!mediatorBalance) continue;

        const activeOrders = await prisma.order.findMany({
          where: {
            mediatorId: mediator.id,
            status: {
              in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
            }
          },
          select: {
            escrowAmount: true
          }
        });

        const lockedAmount = activeOrders.reduce((sum, order) => sum + (order.escrowAmount || 0), 0);
        const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount);

        if (availableAmount >= amount) {
          candidates.push({
            ...mediator,
            availableAmount,
            activeOrderCount: activeOrders.length
          });
        }
      }
      
      if (candidates.length > 0) {
        console.log(`  ✅ 找到 ${candidates.length} 个符合条件的中间人`);
        candidates.forEach(c => {
          console.log(`    - ${c.name}: 可用 ${c.availableAmount} USDT`);
        });
      } else {
        console.log(`  ❌ 没有保证金充足的中间人 (需要 ${amount} USDT)`);
      }
    }
    
    // 3. 测试 mediator/list API 逻辑
    console.log('\n3. 测试 mediator/list API 逻辑...');
    const minAmount = 1000;
    
    const listMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        availableBalance: true
      }
    });

    const qualifiedIds = [];
    for (const mediator of listMediators) {
      const activeOrdersSum = await prisma.order.aggregate({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'PAID', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          },
          useEscrow: true
        },
        _sum: {
          escrowAmount: true
        }
      });

      const lockedAmount = activeOrdersSum._sum.escrowAmount || 0;
      const availableAmount = Math.max(0, mediator.availableBalance - lockedAmount);

      if (availableAmount >= minAmount) {
        qualifiedIds.push(mediator.id);
      }
    }
    
    console.log(`符合 list API 条件的中间人 (>= ${minAmount} USDT): ${qualifiedIds.length}`);
    
    // 4. 测试 escrow/create API 逻辑
    console.log('\n4. 测试 escrow/create API 逻辑...');
    const orderAmount = 1000;
    
    const escrowMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        mediatorFeeRate: true,
        mediatorReputation: true
      }
    });

    const availableEscrowMediators = [];
    
    for (const mediator of escrowMediators) {
      const mediatorBalance = await prisma.user.findUnique({
        where: { id: mediator.id },
        select: {
          availableBalance: true
        }
      });

      if (!mediatorBalance) continue;

      const activeOrdersSum = await prisma.order.aggregate({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        },
        _sum: {
          escrowAmount: true
        }
      });

      const lockedAmount = activeOrdersSum._sum.escrowAmount || 0;
      const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount);
      
      if (availableAmount >= orderAmount) {
        availableEscrowMediators.push({
          ...mediator,
          availableDeposit: availableAmount,
          estimatedFee: orderAmount * (mediator.mediatorFeeRate || 0.02)
        });
      }
    }
    
    console.log(`符合 escrow/create API 条件的中间人 (>= ${orderAmount} USDT): ${availableEscrowMediators.length}`);
    
    // 5. 总结
    console.log('\n=== 测试总结 ===');
    
    const activeMediatorCount = mediators.filter(m => m.mediatorStatus === 'ACTIVE').length;
    const hasBalanceMediatorCount = mediators.filter(m => m.availableBalance > 0).length;
    
    console.log(`活跃中间人数量: ${activeMediatorCount}`);
    console.log(`有可用余额的中间人数量: ${hasBalanceMediatorCount}`);
    console.log(`数据一致性问题: ${dataConsistencyIssues}`);
    
    if (activeMediatorCount > 0 && hasBalanceMediatorCount > 0 && dataConsistencyIssues === 0) {
      console.log('\n✅ 中间人系统修复成功！');
      console.log('   - 数据一致性正常');
      console.log('   - 有活跃的中间人');
      console.log('   - 有可用余额的中间人');
      console.log('   - 自动分配逻辑正常工作');
    } else {
      console.log('\n❌ 中间人系统仍有问题需要解决');
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

comprehensiveMediatorTest();
