#!/usr/bin/env node

/**
 * 系统健康检查脚本
 * 检查数据库、缓存、API等服务状态
 */

const http = require('http')
const https = require('https')

// 健康检查结果
const healthStatus = {
  timestamp: new Date().toISOString(),
  status: 'healthy',
  checks: {},
  performance: {},
  recommendations: []
}

// 检查数据库连接
async function checkDatabase() {
  console.log('🗄️  检查数据库连接...')

  try {
    // 使用标准Prisma客户端
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()

    const startTime = Date.now()
    await prisma.$queryRaw`SELECT 1`
    const responseTime = Date.now() - startTime

    await prisma.$disconnect()
    const isHealthy = true
    
    healthStatus.checks.database = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      message: isHealthy ? '数据库连接正常' : '数据库连接失败'
    }
    
    if (responseTime > 1000) {
      healthStatus.recommendations.push('数据库响应时间较慢，建议优化查询或升级硬件')
    }
    
    console.log(isHealthy ? '✅ 数据库健康' : '❌ 数据库异常')
    return isHealthy
  } catch (error) {
    healthStatus.checks.database = {
      status: 'error',
      message: error.message
    }
    console.log('❌ 数据库检查失败:', error.message)
    return false
  }
}

// 检查Redis缓存
async function checkRedis() {
  console.log('🔍 检查Redis缓存...')
  
  try {
    const { cache } = require('../lib/cache-fallback')
    const startTime = Date.now()
    const isHealthy = await cache.healthCheck()
    const responseTime = Date.now() - startTime
    
    healthStatus.checks.redis = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      message: isHealthy ? 'Redis连接正常' : 'Redis连接失败'
    }
    
    if (responseTime > 100) {
      healthStatus.recommendations.push('Redis响应时间较慢，检查网络连接或Redis配置')
    }
    
    console.log(isHealthy ? '✅ Redis健康' : '❌ Redis异常')
    return isHealthy
  } catch (error) {
    healthStatus.checks.redis = {
      status: 'not_configured',
      message: 'Redis未配置或连接失败'
    }
    console.log('⚠️  Redis未配置')
    return false
  }
}

// 检查API端点
async function checkApiEndpoints() {
  console.log('🌐 检查API端点...')
  
  const endpoints = [
    { path: '/api/products', method: 'GET' },
    { path: '/api/health', method: 'GET' }
  ]
  
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
  
  for (const endpoint of endpoints) {
    try {
      const startTime = Date.now()
      const response = await makeRequest(baseUrl + endpoint.path, endpoint.method)
      const responseTime = Date.now() - startTime
      
      const isHealthy = response.statusCode >= 200 && response.statusCode < 400
      
      healthStatus.checks[`api_${endpoint.path.replace(/\//g, '_')}`] = {
        status: isHealthy ? 'healthy' : 'unhealthy',
        statusCode: response.statusCode,
        responseTime,
        message: `${endpoint.method} ${endpoint.path}`
      }
      
      if (responseTime > 2000) {
        healthStatus.recommendations.push(`API端点 ${endpoint.path} 响应时间过长`)
      }
      
      console.log(isHealthy ? `✅ ${endpoint.path} 健康` : `❌ ${endpoint.path} 异常`)
    } catch (error) {
      healthStatus.checks[`api_${endpoint.path.replace(/\//g, '_')}`] = {
        status: 'error',
        message: error.message
      }
      console.log(`❌ ${endpoint.path} 检查失败:`, error.message)
    }
  }
}

// 检查系统资源
function checkSystemResources() {
  console.log('💻 检查系统资源...')
  
  const memoryUsage = process.memoryUsage()
  const cpuUsage = process.cpuUsage()
  
  // 内存使用情况（MB）
  const memoryMB = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024)
  }
  
  healthStatus.performance.memory = memoryMB
  healthStatus.performance.cpu = {
    user: cpuUsage.user / 1000,
    system: cpuUsage.system / 1000
  }
  healthStatus.performance.uptime = process.uptime()
  
  // 内存使用警告
  if (memoryMB.heapUsed > 512) {
    healthStatus.recommendations.push('内存使用量较高，建议优化内存使用或增加内存')
  }
  
  console.log(`✅ 内存使用: ${memoryMB.heapUsed}MB / ${memoryMB.heapTotal}MB`)
  console.log(`✅ 运行时间: ${Math.round(process.uptime())}秒`)
}

// 检查性能指标
async function checkPerformanceMetrics() {
  console.log('📊 检查性能指标...')

  try {
    // 简化的性能检查
    const stats = {
      api: { avg: 0 },
      database: { avg: 0 },
      slowQueries: 0
    }
    
    healthStatus.performance.stats = stats
    
    // 性能警告
    if (stats.api.avg > 2000) {
      healthStatus.recommendations.push('API平均响应时间过长，需要优化')
    }
    
    if (stats.database.avg > 500) {
      healthStatus.recommendations.push('数据库查询平均时间过长，需要优化查询或添加索引')
    }
    
    if (stats.slowQueries > 10) {
      healthStatus.recommendations.push('存在较多慢查询，建议优化数据库查询')
    }
    
    console.log('✅ 性能指标收集完成')
  } catch (error) {
    console.log('⚠️  性能指标收集失败:', error.message)
  }
}

// HTTP请求工具函数
function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method,
      timeout: 5000
    }
    
    const client = urlObj.protocol === 'https:' ? https : http
    
    const req = client.request(options, (res) => {
      resolve({
        statusCode: res.statusCode,
        headers: res.headers
      })
    })
    
    req.on('error', reject)
    req.on('timeout', () => reject(new Error('Request timeout')))
    req.end()
  })
}

// 生成健康报告
function generateHealthReport() {
  const healthyChecks = Object.values(healthStatus.checks).filter(check => check.status === 'healthy').length
  const totalChecks = Object.keys(healthStatus.checks).length
  
  if (healthyChecks === totalChecks) {
    healthStatus.status = 'healthy'
  } else if (healthyChecks > totalChecks / 2) {
    healthStatus.status = 'degraded'
  } else {
    healthStatus.status = 'unhealthy'
  }
  
  console.log('\n📋 健康检查报告:')
  console.log(`状态: ${healthStatus.status}`)
  console.log(`健康检查: ${healthyChecks}/${totalChecks}`)
  
  if (healthStatus.recommendations.length > 0) {
    console.log('\n💡 优化建议:')
    healthStatus.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`)
    })
  }
  
  // 保存报告
  const fs = require('fs')
  const path = require('path')
  const logsDir = path.join(process.cwd(), 'logs')
  
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true })
  }
  
  const reportPath = path.join(logsDir, `health-check-${Date.now()}.json`)
  fs.writeFileSync(reportPath, JSON.stringify(healthStatus, null, 2))
  
  console.log(`\n📄 详细报告已保存: ${reportPath}`)
}

// 主函数
async function main() {
  console.log('🏥 开始系统健康检查...\n')
  
  try {
    await checkDatabase()
    await checkRedis()
    checkSystemResources()
    await checkPerformanceMetrics()
    await checkApiEndpoints()
    
    generateHealthReport()
    
    // 根据健康状态设置退出码
    if (healthStatus.status === 'unhealthy') {
      process.exit(1)
    } else if (healthStatus.status === 'degraded') {
      process.exit(2)
    } else {
      process.exit(0)
    }
    
  } catch (error) {
    console.error('❌ 健康检查过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  checkDatabase,
  checkRedis,
  checkApiEndpoints,
  checkSystemResources,
  checkPerformanceMetrics,
  generateHealthReport,
  healthStatus
}
