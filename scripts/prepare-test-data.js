const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function prepareTestData() {
  try {
    console.log('🚀 开始准备测试数据...')
    
    // 1. 为买家用户添加余额
    console.log('💰 为买家用户添加测试余额...')
    await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: {
        depositBalance: 1000, // 给买家1000 USDT用于测试
        availableBalance: 1000
      }
    })
    console.log('✅ 买家用户余额已更新为 1000 USDT')
    
    // 2. 创建测试商品
    console.log('📦 创建测试商品...')
    
    // 获取管理员用户ID作为卖家
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    const testProducts = [
      {
        title: '测试商品 - iPhone 15 Pro',
        description: '全新未拆封的iPhone 15 Pro，256GB，深空黑色。包装完整，配件齐全。',
        price: 899.99,
        category: 'ELECTRONICS',
        condition: 'NEW',
        images: 'https://example.com/iphone15pro.jpg',
        sellerId: admin.id,
        status: 'AVAILABLE',
        stock: 5,
        city: '北京市',
        district: '朝阳区',
        shippingFrom: '北京市朝阳区'
      },
      {
        title: '测试商品 - MacBook Air M2',
        description: '2023款MacBook Air，M2芯片，8GB内存，256GB存储。轻薄便携，性能强劲。',
        price: 1199.99,
        category: 'ELECTRONICS',
        condition: 'NEW',
        images: 'https://example.com/macbook-air-m2.jpg',
        sellerId: admin.id,
        status: 'AVAILABLE',
        stock: 3,
        city: '上海市',
        district: '浦东新区',
        shippingFrom: '上海市浦东新区'
      },
      {
        title: '测试商品 - AirPods Pro 2',
        description: '苹果AirPods Pro第二代，主动降噪，空间音频，无线充电盒。',
        price: 249.99,
        category: 'ELECTRONICS',
        condition: 'NEW',
        images: 'https://example.com/airpods-pro-2.jpg',
        sellerId: admin.id,
        status: 'AVAILABLE',
        stock: 10,
        city: '广州市',
        district: '天河区',
        shippingFrom: '广州市天河区'
      },
      {
        title: '测试商品 - 游戏手柄',
        description: '无线蓝牙游戏手柄，兼容PC、手机、平板。人体工学设计，手感舒适。',
        price: 59.99,
        category: 'ELECTRONICS',
        condition: 'NEW',
        images: 'https://example.com/gamepad.jpg',
        sellerId: admin.id,
        status: 'AVAILABLE',
        stock: 20,
        city: '深圳市',
        district: '南山区',
        shippingFrom: '深圳市南山区'
      },
      {
        title: '测试商品 - 运动鞋',
        description: '知名品牌运动鞋，透气舒适，适合跑步和日常穿着。多种颜色可选。',
        price: 129.99,
        category: 'FASHION',
        condition: 'NEW',
        images: 'https://example.com/sneakers.jpg',
        sellerId: admin.id,
        status: 'AVAILABLE',
        stock: 15,
        city: '成都市',
        district: '锦江区',
        shippingFrom: '成都市锦江区'
      }
    ]
    
    const createdProducts = []
    for (const productData of testProducts) {
      const product = await prisma.product.create({
        data: productData
      })
      createdProducts.push(product)
      console.log(`✅ 创建商品: ${product.title} (ID: ${product.id})`)
    }
    
    // 3. 跳过评论创建，因为需要订单ID
    console.log('⏭️ 跳过评论创建（需要先有订单）')
    
    // 4. 更新用户统计信息
    console.log('📊 更新用户统计信息...')
    await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: {
        totalEarnings: 0,
        totalWithdrawals: 0
      }
    })
    
    console.log('🎉 测试数据准备完成！')
    console.log('\n📋 测试数据摘要:')
    console.log(`- 创建了 ${createdProducts.length} 个测试商品`)
    console.log('- 买家用户 (<EMAIL>) 余额: 1000 USDT')
    console.log('- 管理员用户 (<EMAIL>) 作为卖家')
    console.log('- 中间人用户 (<EMAIL>) 可用于托管交易')
    
    return {
      products: createdProducts,
      buyerBalance: 1000,
      success: true
    }
    
  } catch (error) {
    console.error('❌ 准备测试数据失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
prepareTestData()
