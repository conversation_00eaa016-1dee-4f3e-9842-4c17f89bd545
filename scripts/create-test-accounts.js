#!/usr/bin/env node

/**
 * BitMarket 测试账号创建脚本
 * 创建管理员和用户测试账号
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// 测试账号配置
const testAccounts = [
  {
    type: 'admin',
    userId: 'admin-001',
    name: '管理员用户',
    email: '<EMAIL>',
    password: '123456',
    role: 'ADMIN',
    creditScore: 100,
    depositBalance: 10000,
    creditLevel: 'DIAMOND',
    creditPoints: 1000,
    isGuarantor: true,
    status: 'ACTIVE'
  },
  {
    type: 'mediator',
    userId: 'mediator-001',
    name: '中间人用户',
    email: '<EMAIL>',
    password: '123456',
    role: 'MEDIATOR',
    creditScore: 90,
    depositBalance: 5000,
    creditLevel: 'PLATINUM',
    creditPoints: 800,
    city: '北京市',
    district: '朝阳区',
    isGuarantor: true,
    status: 'ACTIVE'
  },
  {
    type: 'user',
    userId: 'user-001',
    name: '普通用户',
    email: '<EMAIL>',
    password: '123456',
    role: 'USER',
    creditScore: 75,
    depositBalance: 1000,
    creditLevel: 'GOLD',
    creditPoints: 300,
    city: '上海市',
    district: '浦东新区',
    isGuarantor: false,
    status: 'ACTIVE'
  }
];

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 生成用户ID
function generateUserId(type, index) {
  const timestamp = Date.now().toString().slice(-6);
  return `${type}-${String(index).padStart(3, '0')}-${timestamp}`;
}

// 加密密码
async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

// 检查用户是否已存在
async function checkUserExists(email, userId) {
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { email: email },
        { userId: userId }
      ]
    }
  });
  return existingUser;
}

// 创建单个用户
async function createUser(accountData) {
  try {
    // 检查用户是否已存在
    const existingUser = await checkUserExists(accountData.email, accountData.userId);
    if (existingUser) {
      log(`⚠️  用户已存在: ${accountData.email} (${accountData.userId})`, 'yellow');
      return existingUser;
    }

    // 加密密码
    const hashedPassword = await hashPassword(accountData.password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        userId: accountData.userId,
        name: accountData.name,
        email: accountData.email,
        password: hashedPassword,
        emailVerified: new Date(), // 设置为已验证
        role: accountData.role,
        creditScore: accountData.creditScore,
        depositBalance: accountData.depositBalance,
        creditLevel: accountData.creditLevel,
        creditPoints: accountData.creditPoints,
        city: accountData.city || null,
        district: accountData.district || null,
        isGuarantor: accountData.isGuarantor,
        status: accountData.status,
        lastCreditUpdate: new Date()
      }
    });

    log(`✅ 创建${accountData.type === 'admin' ? '管理员' : '用户'}账号成功: ${user.name} (${user.email})`, 'green');
    return user;

  } catch (error) {
    log(`❌ 创建用户失败: ${accountData.email}`, 'red');
    log(`错误信息: ${error.message}`, 'red');
    throw error;
  }
}

// 创建初始信用记录
async function createCreditHistory(userId, initialScore) {
  try {
    await prisma.creditHistory.create({
      data: {
        userId: userId,
        changeType: 'ADMIN_ADJUST',
        changeScore: initialScore,
        reason: '账号初始化信用分数',
        beforeScore: 0,
        afterScore: initialScore,
        metadata: {
          source: 'test_account_creation',
          timestamp: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    log(`⚠️  创建信用记录失败: ${error.message}`, 'yellow');
  }
}

// 创建安全日志
async function createSecurityLog(userId, action, description) {
  try {
    await prisma.securityLog.create({
      data: {
        userId: userId,
        action: action,
        description: description,
        status: 'SUCCESS',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Account Creation Script',
        metadata: {
          source: 'test_script',
          timestamp: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    log(`⚠️  创建安全日志失败: ${error.message}`, 'yellow');
  }
}

// 主函数
async function main() {
  try {
    log('🚀 开始创建 BitMarket 测试账号...', 'cyan');
    log('', 'reset');

    const createdUsers = [];

    for (let i = 0; i < testAccounts.length; i++) {
      const accountData = testAccounts[i];
      
      log(`📝 创建账号 ${i + 1}/${testAccounts.length}: ${accountData.name}`, 'blue');
      
      // 创建用户
      const user = await createUser(accountData);
      createdUsers.push(user);

      // 创建信用记录
      await createCreditHistory(user.id, accountData.creditScore);

      // 创建安全日志
      await createSecurityLog(user.id, 'ACCOUNT_CREATED', '测试账号创建');

      log('', 'reset');
    }

    // 显示创建结果
    log('🎉 测试账号创建完成！', 'green');
    log('', 'reset');
    log('📋 账号信息汇总:', 'bright');
    log('=' * 60, 'cyan');

    createdUsers.forEach((user, index) => {
      const account = testAccounts[index];
      log(`${index + 1}. ${user.role === 'ADMIN' ? '👑 管理员' : '👤 用户'}: ${user.name}`, 'bright');
      log(`   📧 邮箱: ${user.email}`, 'reset');
      log(`   🆔 用户ID: ${user.userId}`, 'reset');
      log(`   🔑 密码: ${account.password}`, 'reset');
      log(`   ⭐ 信用分数: ${user.creditScore}`, 'reset');
      log(`   💰 保证金: ${user.depositBalance} USDT`, 'reset');
      log(`   🏆 信用等级: ${user.creditLevel}`, 'reset');
      if (user.city) {
        log(`   📍 地区: ${user.city} ${user.district}`, 'reset');
      }
      log('', 'reset');
    });

    log('🔗 登录地址: http://localhost:3000', 'cyan');
    log('', 'reset');
    log('💡 使用提示:', 'yellow');
    log('  - 管理员账号可以访问管理后台', 'reset');
    log('  - 用户账号可以进行正常的买卖操作', 'reset');
    log('  - 所有账号的邮箱都已验证', 'reset');
    log('  - 密码都是明文显示，请在生产环境中修改', 'reset');

  } catch (error) {
    log('❌ 创建测试账号失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main, testAccounts };
