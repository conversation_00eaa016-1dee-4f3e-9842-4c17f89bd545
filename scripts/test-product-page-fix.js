const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testProductPageFix() {
  try {
    console.log('=== 测试商品页面修复效果 ===\n');
    
    // 1. 查找一个有库存不足的商品
    console.log('1. 查找库存不足的商品...');
    const lowStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lte: 5,
          gt: 0
        },
        status: 'AVAILABLE'
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        }
      },
      take: 3
    });
    
    console.log(`找到 ${lowStockProducts.length} 个库存不足的商品:`);
    lowStockProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
      console.log(`  库存: ${product.stock} 件`);
      console.log(`  卖家: ${product.seller.name}`);
      console.log(`  卖家ID: ${product.seller.id}`);
      console.log('');
    });
    
    // 2. 查找一个已售完的商品
    console.log('2. 查找已售完的商品...');
    const outOfStockProducts = await prisma.product.findMany({
      where: {
        stock: 0,
        status: 'AVAILABLE'
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        }
      },
      take: 3
    });
    
    console.log(`找到 ${outOfStockProducts.length} 个已售完的商品:`);
    outOfStockProducts.forEach(product => {
      console.log(`- ${product.title} (ID: ${product.id})`);
      console.log(`  库存: ${product.stock} 件`);
      console.log(`  卖家: ${product.seller.name}`);
      console.log(`  卖家ID: ${product.seller.id}`);
      console.log('');
    });
    
    // 3. 创建一个测试商品（如果没有合适的商品）
    if (lowStockProducts.length === 0 && outOfStockProducts.length === 0) {
      console.log('3. 创建测试商品...');
      
      // 查找一个用户作为卖家
      const testSeller = await prisma.user.findFirst({
        where: {
          name: {
            not: null
          }
        }
      });
      
      if (testSeller) {
        const testProduct = await prisma.product.create({
          data: {
            title: '库存不足测试商品',
            description: '这是一个用于测试库存不足显示的商品',
            price: 99.99,
            stock: 2, // 设置为低库存
            status: 'AVAILABLE',
            category: 'GENERAL',
            condition: 'NEW',
            images: '[]',
            sellerId: testSeller.id,
            city: '深圳市',
            district: '南山区'
          }
        });
        
        console.log(`创建测试商品成功:`);
        console.log(`- 商品ID: ${testProduct.id}`);
        console.log(`- 商品标题: ${testProduct.title}`);
        console.log(`- 库存: ${testProduct.stock} 件`);
        console.log(`- 卖家ID: ${testSeller.id}`);
        console.log(`- 卖家名称: ${testSeller.name}`);
        
        lowStockProducts.push({
          ...testProduct,
          seller: {
            id: testSeller.id,
            name: testSeller.name,
            creditScore: testSeller.creditScore || 0
          }
        });
      }
    }
    
    // 4. 生成测试URL
    console.log('\n4. 测试URL生成:');
    
    if (lowStockProducts.length > 0) {
      const product = lowStockProducts[0];
      console.log('\n库存不足商品测试:');
      console.log(`商品页面: http://localhost:3000/products/${product.id}`);
      console.log(`预期行为:`);
      console.log(`- 商品所有者 (${product.seller.name}, ID: ${product.seller.id}) 访问时:`);
      console.log(`  ✅ 应该看到库存不足警告`);
      console.log(`  ✅ 应该看到商品管理按钮`);
      console.log(`- 其他用户访问时:`);
      console.log(`  ✅ 不应该看到库存不足警告`);
      console.log(`  ✅ 不应该看到"联系卖家"按钮`);
      console.log(`  ✅ 不应该看到"购买前可先联系卖家了解详情"文字`);
    }
    
    if (outOfStockProducts.length > 0) {
      const product = outOfStockProducts[0];
      console.log('\n已售完商品测试:');
      console.log(`商品页面: http://localhost:3000/products/${product.id}`);
      console.log(`预期行为:`);
      console.log(`- 商品所有者 (${product.seller.name}, ID: ${product.seller.id}) 访问时:`);
      console.log(`  ✅ 应该看到"商品已售完"警告`);
      console.log(`- 其他用户访问时:`);
      console.log(`  ✅ 不应该看到库存警告`);
      console.log(`  ✅ 购买按钮应该显示"库存不足"或"商品已下架"`);
    }
    
    // 5. 验证修复点
    console.log('\n=== 修复验证清单 ===');
    console.log('请手动验证以下修复点:');
    console.log('');
    console.log('✅ 库存不足提醒只对商家可见:');
    console.log('   - 用商品所有者账号登录，应该看到库存警告');
    console.log('   - 用其他账号登录，不应该看到库存警告');
    console.log('');
    console.log('✅ 删除联系商家功能:');
    console.log('   - 卖家信息区域不应该有"联系卖家"按钮');
    console.log('   - 购买区域不应该有"购买前可先联系卖家了解详情"文字');
    console.log('');
    console.log('✅ 购买流程说明修改:');
    console.log('   - 购买按钮下方不应该有联系提示');
    console.log('   - 用户需要点击购买后建单才能联系卖家');
    
    // 6. 检查代码修改
    console.log('\n=== 代码修改验证 ===');
    console.log('已修改的文件:');
    console.log('1. components/inventory/StockAlert.tsx');
    console.log('   - ProductStockStatus 组件新增 isOwner 参数');
    console.log('   - 只有 isOwner=true 时才显示库存警告');
    console.log('');
    console.log('2. app/products/[id]/page.tsx');
    console.log('   - 传递 isOwner 参数给 ProductStockStatus 组件');
    console.log('   - 删除了"购买前可先联系卖家了解详情"文字');
    console.log('   - 删除了卖家信息区域的"联系卖家"按钮');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testProductPageFix();
