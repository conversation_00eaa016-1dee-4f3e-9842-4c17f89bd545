const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createCompleteTestDeposits() {
  try {
    console.log('🔄 创建完整的测试充值记录...')

    // 获取测试用户
    const users = await prisma.user.findMany({
      take: 3
    })

    if (users.length === 0) {
      console.log('❌ 没有找到用户，请先创建用户')
      return
    }

    // 删除之前的测试数据
    await prisma.depositRecord.deleteMany({
      where: {
        notes: { contains: '测试' }
      }
    })

    // 创建包含完整信息的测试充值记录
    const testDeposits = [
      // 币安支付 - 包含PIN码和订单号
      {
        userId: users[0].id,
        amount: 500.00,
        originalAmount: 500.00,
        method: 'BINANCE_PAY',
        status: 'PENDING',
        paymentOrderId: 'BINANCE_ORDER_' + Date.now() + '_001',
        pinCode: '123456',
        notes: '币安支付充值 - 需要核实PIN码',
        metadata: {
          binanceOrderId: 'BIN_' + Date.now(),
          paymentMethod: 'BINANCE_PAY'
        }
      },
      // BNB链 - 包含交易hash
      {
        userId: users[1].id,
        amount: 300.00,
        originalAmount: 0.09,
        method: 'BNB',
        status: 'PENDING',
        transactionHash: '******************************************90abcdef1234567890abcdef12',
        notes: 'BNB链充值 - 需要确认区块链交易',
        metadata: {
          walletAddress: '******************************************',
          network: 'BSC',
          originalCurrency: 'BNB',
          exchangeRate: 3333.33,
          confirmations: 12
        }
      },
      // USDT - 包含交易hash
      {
        userId: users[2] ? users[2].id : users[0].id,
        amount: 200.00,
        originalAmount: 200.00,
        method: 'USDT',
        status: 'PENDING',
        transactionHash: 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
        txHash: 'TRX_backup_hash_12345',
        notes: 'USDT(TRC20)充值 - 需要确认区块链交易',
        metadata: {
          walletAddress: 'TRX7n2oDKGKnTFTQjSwhHKAUAyiYK9vWhr',
          network: 'TRC20',
          confirmations: 15
        }
      },
      // 另一个币安支付 - 不同PIN码
      {
        userId: users[1].id,
        amount: 1000.00,
        originalAmount: 1000.00,
        method: 'BINANCE_PAY',
        status: 'PENDING',
        paymentOrderId: 'BINANCE_ORDER_' + (Date.now() + 1000) + '_002',
        pinCode: '789012',
        notes: '币安支付充值 - 大额充值需要特别核实',
        metadata: {
          binanceOrderId: 'BIN_' + (Date.now() + 1000),
          paymentMethod: 'BINANCE_PAY',
          isLargeAmount: true
        }
      },
      // 另一个BNB链 - 不同hash
      {
        userId: users[0].id,
        amount: 150.00,
        originalAmount: 0.045,
        method: 'BNB',
        status: 'PENDING',
        transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
        notes: 'BNB链充值 - 小额测试交易',
        metadata: {
          walletAddress: '******************************************',
          network: 'BSC',
          originalCurrency: 'BNB',
          exchangeRate: 3333.33,
          confirmations: 8
        }
      }
    ]

    for (const depositData of testDeposits) {
      const deposit = await prisma.depositRecord.create({
        data: depositData,
        include: {
          user: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      console.log(`✅ 创建充值记录: ${deposit.id}`)
      console.log(`   用户: ${deposit.user.name} (${deposit.user.email})`)
      console.log(`   金额: ${deposit.amount} USDT`)
      console.log(`   支付方式: ${deposit.method}`)
      
      if (deposit.pinCode) {
        console.log(`   PIN码: ${deposit.pinCode}`)
      }
      if (deposit.paymentOrderId) {
        console.log(`   支付订单号: ${deposit.paymentOrderId}`)
      }
      if (deposit.transactionHash) {
        console.log(`   交易Hash: ${deposit.transactionHash.slice(0, 20)}...`)
      }
      console.log('')
    }

    console.log('🎉 完整测试充值记录创建完成!')
    console.log('')
    console.log('📋 测试数据总结:')
    console.log('✅ 币安支付记录: 2 条 (包含PIN码和订单号)')
    console.log('✅ BNB链记录: 2 条 (包含交易hash)')
    console.log('✅ USDT记录: 1 条 (包含交易hash)')
    console.log('')
    console.log('🔍 现在可以在管理后台查看这些充值申请的详细信息了')
    console.log('💡 币安支付会显示PIN码和订单号')
    console.log('💡 BNB链和USDT会显示交易hash和区块链浏览器链接')

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

createCompleteTestDeposits()
