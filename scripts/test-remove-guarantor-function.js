const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testRemoveGuarantorFunction() {
  console.log('🔧 测试移除担保人功能后的系统完整性...\n')

  try {
    // 1. 检查现有用户状态
    console.log('1. 检查现有用户状态...')
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isGuarantor: true,
        isMediator: true,
        mediatorStatus: true
      },
      take: 10
    })

    console.log(`✅ 找到 ${users.length} 个用户`)
    users.forEach((user, index) => {
      console.log(`\n   ${index + 1}. ${user.name} (${user.email})`)
      console.log(`      角色: ${user.role}`)
      console.log(`      担保人: ${user.isGuarantor} (数据库中仍存在)`)
      console.log(`      中间人: ${user.isMediator}`)
      console.log(`      中间人状态: ${user.mediatorStatus}`)
    })

    // 2. 创建测试用户
    console.log('\n2. 创建测试用户...')
    
    let testUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testUser) {
      console.log('   创建新的测试用户...')
      testUser = await prisma.user.create({
        data: {
          name: '测试用户-无担保人功能',
          email: '<EMAIL>',
          isGuarantor: false, // 数据库字段仍存在，但管理员界面不再使用
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          depositBalance: 5000,
          creditScore: 85,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testUser.name}`)
    } else {
      // 重置测试用户状态
      await prisma.user.update({
        where: { id: testUser.id },
        data: {
          isGuarantor: false,
          isMediator: false,
          mediatorStatus: 'INACTIVE'
        }
      })
      console.log(`   ✅ 重置测试用户: ${testUser.name}`)
    }

    // 3. 测试中间人功能（应该正常工作）
    console.log('\n3. 测试中间人功能...')
    
    console.log('   设置前状态:')
    console.log(`     isGuarantor: ${testUser.isGuarantor} (不再在管理员界面显示)`)
    console.log(`     isMediator: ${testUser.isMediator}`)
    console.log(`     mediatorStatus: ${testUser.mediatorStatus}`)
    
    // 模拟管理员设置中间人
    const updatedUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        mediatorFeeRate: 0.025,
        mediatorReputation: 0,
        mediatorSuccessRate: 0,
        mediatorTotalOrders: 0,
        mediatorVerifiedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        isGuarantor: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true
      }
    })
    
    console.log('   设置后状态:')
    console.log(`     isGuarantor: ${updatedUser.isGuarantor} (数据库中存在但界面不显示)`)
    console.log(`     isMediator: ${updatedUser.isMediator} ✅`)
    console.log(`     mediatorStatus: ${updatedUser.mediatorStatus} ✅`)
    console.log(`     mediatorFeeRate: ${updatedUser.mediatorFeeRate} ✅`)
    console.log(`     mediatorReputation: ${updatedUser.mediatorReputation} ✅`)
    console.log(`     mediatorSuccessRate: ${updatedUser.mediatorSuccessRate} ✅`)
    console.log(`     mediatorTotalOrders: ${updatedUser.mediatorTotalOrders} ✅`)

    // 4. 验证管理员界面应该显示的内容
    console.log('\n4. 验证管理员界面显示逻辑...')
    
    console.log('   ✅ 应该显示的内容:')
    console.log('     - 用户基本信息 (姓名、邮箱、角色)')
    console.log('     - 中间人标签 (橙色) ✅')
    console.log('     - "设为中间人"/"取消中间人" 按钮 ✅')
    console.log('     - 其他管理功能 (设为管理员、修改信用分等)')
    
    console.log('   ❌ 不应该显示的内容:')
    console.log('     - 担保人标签 (绿色) ❌ 已移除')
    console.log('     - "设为担保人"/"取消担保人" 按钮 ❌ 已移除')

    // 5. 验证个人资料页面功能
    console.log('\n5. 验证个人资料页面功能...')
    
    const shouldShowMediatorConsole = updatedUser.isMediator
    console.log(`   中间人控制台显示条件: profile?.isMediator = ${shouldShowMediatorConsole}`)
    console.log(`   应显示中间人控制台: ${shouldShowMediatorConsole ? '是' : '否'} ✅`)
    
    if (shouldShowMediatorConsole) {
      console.log('   中间人控制台内容:')
      console.log(`     - 状态标签: ${updatedUser.mediatorStatus === 'ACTIVE' ? '已认证' : '待认证'} ✅`)
      console.log(`     - 调解订单: ${updatedUser.mediatorTotalOrders} ✅`)
      console.log(`     - 成功率: ${updatedUser.mediatorSuccessRate}% ✅`)
      console.log(`     - 信誉值: ${updatedUser.mediatorReputation} ✅`)
    }

    // 6. 测试取消中间人功能
    console.log('\n6. 测试取消中间人功能...')
    
    const canceledUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        isMediator: false,
        mediatorStatus: 'INACTIVE'
      },
      select: {
        id: true,
        name: true,
        isGuarantor: true,
        isMediator: true,
        mediatorStatus: true
      }
    })
    
    console.log('   取消后状态:')
    console.log(`     isGuarantor: ${canceledUser.isGuarantor} (不影响)`)
    console.log(`     isMediator: ${canceledUser.isMediator} ✅`)
    console.log(`     mediatorStatus: ${canceledUser.mediatorStatus} ✅`)
    console.log(`     应显示中间人控制台: ${canceledUser.isMediator ? '是' : '否'} ✅`)

    // 7. 检查数据库完整性
    console.log('\n7. 检查数据库完整性...')
    
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        isGuarantor: true,
        isMediator: true
      }
    })
    
    const guarantorCount = allUsers.filter(u => u.isGuarantor).length
    const mediatorCount = allUsers.filter(u => u.isMediator).length
    
    console.log(`   ✅ 数据库状态:`)
    console.log(`     - 总用户数: ${allUsers.length}`)
    console.log(`     - 担保人数量: ${guarantorCount} (数据库字段保留，但界面不使用)`)
    console.log(`     - 中间人数量: ${mediatorCount} (正常使用)`)

    // 8. API接口验证
    console.log('\n8. API接口验证...')
    
    try {
      const fetch = require('node-fetch')
      
      // 测试获取用户列表API
      const response = await fetch('http://localhost:3000/api/admin/users')
      console.log(`   用户列表API状态: ${response.status}`)
      
      if (response.status === 403) {
        console.log('   ✅ API权限控制正常 (需要管理员权限)')
      } else if (response.ok) {
        console.log('   ✅ API接口正常')
      } else {
        console.log(`   ⚠️  API响应异常: ${response.status}`)
      }
    } catch (error) {
      console.log(`   ⚠️  API测试跳过: ${error.message}`)
    }

    console.log('\n🎉 测试完成！')
    console.log('\n📝 手动测试步骤:')
    
    console.log('\n【步骤1: 验证管理员界面】')
    console.log('1. 管理员登录')
    console.log('2. 访问用户管理页面: http://localhost:3000/admin/users')
    console.log('3. 检查用户列表:')
    console.log('   - ❌ 不应该看到绿色"担保人"标签')
    console.log('   - ✅ 应该看到橙色"中间人"标签')
    console.log('   - ❌ 不应该看到"设为担保人"按钮')
    console.log('   - ✅ 应该看到"设为中间人"按钮')
    
    console.log('\n【步骤2: 测试中间人功能】')
    console.log(`1. 找到测试用户: ${testUser.email}`)
    console.log('2. 点击"设为中间人"按钮')
    console.log('3. 确认显示橙色"中间人"标签')
    console.log('4. 用户登录后访问个人资料页面')
    console.log('5. 验证显示中间人控制台')
    
    console.log('\n【步骤3: 测试取消功能】')
    console.log('1. 管理员点击"取消中间人"按钮')
    console.log('2. 确认标签消失')
    console.log('3. 用户个人资料页面不再显示中间人控制台')
    
    console.log('\n💡 重要说明:')
    console.log('   - 担保人功能已从管理员界面完全移除')
    console.log('   - 数据库中的 isGuarantor 字段仍然存在（为了数据完整性）')
    console.log('   - 中间人功能完全独立，不受担保人功能移除影响')
    console.log('   - 所有现有的担保人数据保持不变，只是界面不再显示')

    console.log('\n🔧 如果发现问题:')
    console.log('   1. 检查是否还有担保人相关的UI元素')
    console.log('   2. 确认中间人功能正常工作')
    console.log('   3. 验证API接口不再包含担保人操作')
    console.log('   4. 检查TypeScript类型定义是否正确')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testRemoveGuarantorFunction().catch(console.error)
}

module.exports = { testRemoveGuarantorFunction }
