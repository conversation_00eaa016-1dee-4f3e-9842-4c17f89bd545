const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function deleteAllData() {
  try {
    console.log('🗑️ 开始删除所有数据...\n')

    // 按照外键依赖关系的顺序删除数据

    console.log('1. 删除聊天和消息数据...')
    await prisma.escrowChatMessage.deleteMany()
    await prisma.escrowChatRoom.deleteMany()
    await prisma.message.deleteMany()
    console.log('✅ 聊天数据已删除')

    console.log('2. 删除托管相关数据...')
    await prisma.arbitrationVote.deleteMany()
    await prisma.escrowDispute.deleteMany()
    await prisma.escrowOrder.deleteMany()
    await prisma.arbitrationCase.deleteMany()
    console.log('✅ 托管数据已删除')

    console.log('3. 删除订单相关数据...')
    await prisma.orderLog.deleteMany()
    await prisma.adminNote.deleteMany()
    await prisma.orderItem.deleteMany()
    await prisma.order.deleteMany()
    await prisma.paymentPinVerification.deleteMany()
    console.log('✅ 订单数据已删除')

    console.log('4. 删除商品相关数据...')
    await prisma.review.deleteMany()
    await prisma.favorite.deleteMany()
    await prisma.variantAttribute.deleteMany()
    await prisma.productVariant.deleteMany()
    await prisma.product.deleteMany()
    console.log('✅ 商品数据已删除')

    console.log('5. 删除需求相关数据...')
    await prisma.demandOffer.deleteMany()
    await prisma.demand.deleteMany()
    console.log('✅ 需求数据已删除')

    console.log('6. 删除金融相关数据...')
    await prisma.blockchainTransaction.deleteMany()
    await prisma.escrowTransaction.deleteMany()
    await prisma.escrowPayment.deleteMany()
    await prisma.giftCardTransaction.deleteMany()
    await prisma.redemptionTransaction.deleteMany()
    await prisma.fundTransaction.deleteMany()
    await prisma.settlementItem.deleteMany()
    await prisma.batchSettlement.deleteMany()
    await prisma.fundFreeze.deleteMany()
    await prisma.depositRecord.deleteMany()
    await prisma.depositOperation.deleteMany()
    await prisma.withdrawal.deleteMany()
    console.log('✅ 金融数据已删除')

    console.log('7. 删除礼品卡和奖励数据...')
    await prisma.giftCard.deleteMany()
    await prisma.giftCardOrder.deleteMany()
    await prisma.giftCardProduct.deleteMany()
    await prisma.redemptionCode.deleteMany()
    await prisma.rewardCoupon.deleteMany()
    await prisma.withdrawalVoucher.deleteMany()
    await prisma.mediatorReward.deleteMany()
    console.log('✅ 礼品卡和奖励数据已删除')

    console.log('8. 删除通知和公告...')
    await prisma.notification.deleteMany()
    await prisma.announcement.deleteMany()
    await prisma.helpMediaFile.deleteMany()
    await prisma.helpArticle.deleteMany()
    console.log('✅ 通知数据已删除')

    console.log('9. 删除用户相关数据...')
    await prisma.userSession.deleteMany()
    await prisma.securityLog.deleteMany()
    await prisma.creditHistory.deleteMany()
    await prisma.address.deleteMany()
    await prisma.guarantorApplication.deleteMany()
    await prisma.mediatorApplication.deleteMany()
    await prisma.mediatorVerification.deleteMany()
    await prisma.mediatorCommittee.deleteMany()
    console.log('✅ 用户相关数据已删除')

    console.log('10. 删除反馈和争议数据...')
    await prisma.userFeedback.deleteMany()
    await prisma.disputeReport.deleteMany()
    console.log('✅ 反馈数据已删除')

    console.log('11. 删除用户数据...')
    await prisma.user.deleteMany()
    console.log('✅ 用户数据已删除')

    console.log('12. 删除系统数据...')
    await prisma.systemConfig.deleteMany()
    await prisma.systemSetting.deleteMany()
    await prisma.walletConfig.deleteMany()
    await prisma.feeConfig.deleteMany()
    await prisma.fundPoolStats.deleteMany()
    await prisma.searchKeyword.deleteMany()
    console.log('✅ 系统配置已删除')

    console.log('\n🎉 所有数据删除完成!')
    console.log('📊 数据库已完全清空')

  } catch (error) {
    console.error('❌ 删除数据失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function confirmDeletion() {
  console.log('⚠️  警告：此操作将删除数据库中的所有数据！')
  console.log('📋 将删除以下数据：')
  console.log('   - 所有用户账号和个人信息')
  console.log('   - 所有商品和需求信息')
  console.log('   - 所有订单和托管记录')
  console.log('   - 所有聊天记录和消息')
  console.log('   - 所有金融交易记录')
  console.log('   - 所有系统配置和通知')
  console.log('')
  
  // 在脚本环境中直接执行删除
  const shouldDelete = process.argv.includes('--confirm')
  
  if (shouldDelete) {
    await deleteAllData()
  } else {
    console.log('❌ 操作已取消')
    console.log('💡 如要确认删除，请运行：')
    console.log('   node scripts/delete-all-data.js --confirm')
  }
}

async function main() {
  try {
    await confirmDeletion()
  } catch (error) {
    console.error('❌ 操作失败:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  deleteAllData
}
