const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function setUserAsAdmin(email) {
  try {
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: email }
    })

    if (!user) {
      console.log(`❌ 用户 ${email} 不存在`)
      return
    }

    // 更新用户角色为管理员
    const updatedUser = await prisma.user.update({
      where: { email: email },
      data: { role: 'ADMIN' }
    })

    console.log(`✅ 用户 ${email} 已设置为管理员`)
    console.log(`用户信息:`)
    console.log(`- ID: ${updatedUser.id}`)
    console.log(`- 姓名: ${updatedUser.name || '未设置'}`)
    console.log(`- 邮箱: ${updatedUser.email}`)
    console.log(`- 角色: ${updatedUser.role}`)
    
  } catch (error) {
    console.error('❌ 设置管理员失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 获取命令行参数
const email = process.argv[2]

if (!email) {
  console.log('使用方法: node set-admin.js <用户邮箱>')
  console.log('示例: node set-admin.js <EMAIL>')
  process.exit(1)
}

setUserAsAdmin(email)
