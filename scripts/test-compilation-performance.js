#!/usr/bin/env node

const { spawn } = require('child_process')
const { performance } = require('perf_hooks')
const os = require('os')
const fs = require('fs')

class CompilationPerformanceTester {
  constructor() {
    this.results = {
      timestamp: Date.now(),
      tests: [],
      summary: {}
    }
    this.devServer = null
  }

  // 清理缓存
  async cleanCache() {
    console.log('🧹 清理编译缓存...')
    
    const cacheDirs = ['.next', 'node_modules/.cache']
    
    for (const dir of cacheDirs) {
      if (fs.existsSync(dir)) {
        try {
          fs.rmSync(dir, { recursive: true, force: true })
          console.log(`  ✅ 已删除: ${dir}`)
        } catch (error) {
          console.log(`  ⚠️  删除失败: ${dir} - ${error.message}`)
        }
      } else {
        console.log(`  ✅ 不存在: ${dir}`)
      }
    }
  }

  // 测试首次编译性能
  async testFirstCompilation() {
    console.log('\n⚡ 测试首次编译性能...')
    
    return new Promise((resolve, reject) => {
      const startTime = performance.now()
      let compilationStarted = false
      let compilationCompleted = false
      
      const isWindows = os.platform() === 'win32'
      const npmCommand = isWindows ? 'npm.cmd' : 'npm'
      
      this.devServer = spawn(npmCommand, ['run', 'dev'], {
        stdio: 'pipe',
        shell: isWindows
      })

      let serverOutput = ''
      let compilationTime = null
      let moduleCount = null
      let warningCount = 0

      this.devServer.stdout.on('data', (data) => {
        const output = data.toString()
        serverOutput += output
        
        // 检测编译开始
        if (output.includes('Compiling') && !compilationStarted) {
          compilationStarted = true
          console.log('  🔄 编译开始...')
        }

        // 检测编译完成
        const compileMatch = output.match(/✓ Compiled .+ in ([\d.]+)([ms]+) \\((\\d+) modules\\)/)
        if (compileMatch && !compilationCompleted) {
          compilationCompleted = true
          const time = parseFloat(compileMatch[1])
          const unit = compileMatch[2]
          const modules = parseInt(compileMatch[3])
          
          compilationTime = unit === 's' ? time * 1000 : time
          moduleCount = modules
          
          console.log(`  ✅ 首次编译完成: ${compilationTime}ms (${moduleCount} 模块)`)
          
          // 评估性能
          let performance = 'poor'
          if (compilationTime < 5000) performance = 'excellent'
          else if (compilationTime < 10000) performance = 'good'
          else if (compilationTime < 15000) performance = 'fair'
          
          const result = {
            type: 'first-compilation',
            time: compilationTime,
            modules: moduleCount,
            performance,
            warnings: warningCount
          }
          
          this.results.tests.push(result)
          
          // 等待服务器完全启动后停止
          setTimeout(() => {
            this.devServer.kill()
            resolve(result)
          }, 2000)
        }

        // 检测警告
        if (output.includes('⚠') || output.includes('warning')) {
          warningCount++
        }

        // 检测服务器启动
        if (output.includes('Ready on http://localhost:3000')) {
          console.log('  🚀 服务器已启动')
        }
      })

      this.devServer.stderr.on('data', (data) => {
        const output = data.toString()
        if (output.includes('warning')) {
          warningCount++
        }
      })

      this.devServer.on('error', (error) => {
        console.error('  ❌ 编译测试失败:', error)
        reject(error)
      })

      // 30秒超时
      setTimeout(() => {
        if (!compilationCompleted) {
          console.log('  ⚠️  编译测试超时')
          if (this.devServer) {
            this.devServer.kill()
          }
          reject(new Error('编译测试超时'))
        }
      }, 30000)
    })
  }

  // 测试增量编译性能
  async testIncrementalCompilation() {
    console.log('\n🔄 测试增量编译性能...')
    
    return new Promise((resolve, reject) => {
      const isWindows = os.platform() === 'win32'
      const npmCommand = isWindows ? 'npm.cmd' : 'npm'
      
      this.devServer = spawn(npmCommand, ['run', 'dev'], {
        stdio: 'pipe',
        shell: isWindows
      })

      let serverReady = false
      let incrementalTests = []
      
      this.devServer.stdout.on('data', (data) => {
        const output = data.toString()
        
        // 等待服务器启动
        if (output.includes('Ready on http://localhost:3000') && !serverReady) {
          serverReady = true
          console.log('  🚀 服务器已启动，开始增量编译测试...')
          
          // 延迟后开始增量编译测试
          setTimeout(() => {
            this.triggerIncrementalCompilation()
          }, 2000)
        }

        // 检测增量编译
        const incrementalMatch = output.match(/✓ Compiled .+ in ([\d.]+)([ms]+) \\((\\d+) modules\\)/)
        if (incrementalMatch && serverReady) {
          const time = parseFloat(incrementalMatch[1])
          const unit = incrementalMatch[2]
          const modules = parseInt(incrementalMatch[3])
          
          const compilationTime = unit === 's' ? time * 1000 : time
          
          console.log(`  ⚡ 增量编译: ${compilationTime}ms (${modules} 模块)`)
          
          incrementalTests.push({
            time: compilationTime,
            modules: modules
          })
          
          // 收集3次增量编译数据后结束
          if (incrementalTests.length >= 3) {
            const avgTime = incrementalTests.reduce((sum, test) => sum + test.time, 0) / incrementalTests.length
            const avgModules = incrementalTests.reduce((sum, test) => sum + test.modules, 0) / incrementalTests.length
            
            let performance = 'poor'
            if (avgTime < 500) performance = 'excellent'
            else if (avgTime < 1000) performance = 'good'
            else if (avgTime < 2000) performance = 'fair'
            
            const result = {
              type: 'incremental-compilation',
              averageTime: avgTime,
              averageModules: avgModules,
              tests: incrementalTests,
              performance
            }
            
            this.results.tests.push(result)
            
            this.devServer.kill()
            resolve(result)
          }
        }
      })

      this.devServer.on('error', (error) => {
        console.error('  ❌ 增量编译测试失败:', error)
        reject(error)
      })

      // 60秒超时
      setTimeout(() => {
        console.log('  ⚠️  增量编译测试超时')
        if (this.devServer) {
          this.devServer.kill()
        }
        reject(new Error('增量编译测试超时'))
      }, 60000)
    })
  }

  // 触发增量编译
  triggerIncrementalCompilation() {
    // 修改一个简单的文件来触发增量编译
    const testFiles = [
      'app/page.tsx',
      'components/Navbar.tsx',
      'lib/utils.ts'
    ]

    let fileIndex = 0
    const triggerNext = () => {
      if (fileIndex < testFiles.length) {
        const file = testFiles[fileIndex]
        if (fs.existsSync(file)) {
          try {
            // 添加一个空行来触发重编译
            const content = fs.readFileSync(file, 'utf8')
            fs.writeFileSync(file, content + '\n// Incremental compilation test')
            
            console.log(`  📝 修改文件: ${file}`)
            
            // 2秒后恢复文件并触发下一次测试
            setTimeout(() => {
              fs.writeFileSync(file, content)
              fileIndex++
              setTimeout(triggerNext, 3000)
            }, 2000)
          } catch (error) {
            console.log(`  ⚠️  无法修改文件: ${file}`)
            fileIndex++
            setTimeout(triggerNext, 1000)
          }
        } else {
          fileIndex++
          setTimeout(triggerNext, 1000)
        }
      }
    }

    triggerNext()
  }

  // 生成性能报告
  generateReport() {
    console.log('\n📊 编译性能测试报告')
    console.log('='.repeat(60))
    
    const firstCompilation = this.results.tests.find(t => t.type === 'first-compilation')
    const incrementalCompilation = this.results.tests.find(t => t.type === 'incremental-compilation')
    
    if (firstCompilation) {
      console.log('\n⚡ 首次编译性能:')
      console.log(`  编译时间: ${firstCompilation.time}ms`)
      console.log(`  模块数量: ${firstCompilation.modules}`)
      console.log(`  警告数量: ${firstCompilation.warnings}`)
      
      const icon = firstCompilation.performance === 'excellent' ? '🟢' : 
                   firstCompilation.performance === 'good' ? '🟡' : 
                   firstCompilation.performance === 'fair' ? '🟠' : '🔴'
      console.log(`  ${icon} 性能评级: ${firstCompilation.performance}`)
    }

    if (incrementalCompilation) {
      console.log('\n🔄 增量编译性能:')
      console.log(`  平均编译时间: ${incrementalCompilation.averageTime.toFixed(2)}ms`)
      console.log(`  平均模块数量: ${incrementalCompilation.averageModules.toFixed(0)}`)
      console.log(`  测试次数: ${incrementalCompilation.tests.length}`)
      
      const icon = incrementalCompilation.performance === 'excellent' ? '🟢' : 
                   incrementalCompilation.performance === 'good' ? '🟡' : 
                   incrementalCompilation.performance === 'fair' ? '🟠' : '🔴'
      console.log(`  ${icon} 性能评级: ${incrementalCompilation.performance}`)
    }

    // 计算改进效果
    const improvement = this.calculateImprovement()
    console.log('\n🎯 优化效果评估:')
    console.log(`  总体评分: ${improvement.score}/100 (${improvement.grade})`)
    console.log(`  评估: ${improvement.description}`)
    
    if (improvement.achievements.length > 0) {
      console.log('\n✅ 优化成果:')
      improvement.achievements.forEach(achievement => {
        console.log(`  • ${achievement}`)
      })
    }

    if (improvement.recommendations.length > 0) {
      console.log('\n💡 进一步优化建议:')
      improvement.recommendations.forEach(rec => {
        console.log(`  • ${rec}`)
      })
    }

    this.results.summary = improvement
    return this.results
  }

  // 计算改进效果
  calculateImprovement() {
    let score = 100
    const achievements = []
    const recommendations = []

    const firstCompilation = this.results.tests.find(t => t.type === 'first-compilation')
    const incrementalCompilation = this.results.tests.find(t => t.type === 'incremental-compilation')

    // 首次编译评分
    if (firstCompilation) {
      if (firstCompilation.time < 10000) {
        achievements.push(`首次编译时间优秀 (${firstCompilation.time}ms < 10秒)`)
      } else if (firstCompilation.time < 15000) {
        achievements.push(`首次编译时间良好 (${firstCompilation.time}ms < 15秒)`)
        score -= 10
      } else if (firstCompilation.time < 25000) {
        score -= 20
        recommendations.push('首次编译时间仍需优化，考虑进一步减少依赖')
      } else {
        score -= 30
        recommendations.push('首次编译时间过长，需要重新评估架构')
      }

      if (firstCompilation.warnings === 0) {
        achievements.push('无编译警告')
      } else if (firstCompilation.warnings < 3) {
        score -= 5
        recommendations.push('减少编译警告')
      } else {
        score -= 15
        recommendations.push('修复编译警告以提高性能')
      }
    }

    // 增量编译评分
    if (incrementalCompilation) {
      if (incrementalCompilation.averageTime < 500) {
        achievements.push(`增量编译性能优秀 (${incrementalCompilation.averageTime.toFixed(2)}ms < 500ms)`)
      } else if (incrementalCompilation.averageTime < 1000) {
        achievements.push(`增量编译性能良好 (${incrementalCompilation.averageTime.toFixed(2)}ms < 1秒)`)
        score -= 5
      } else {
        score -= 15
        recommendations.push('增量编译时间需要优化')
      }
    }

    // 确定等级
    let grade, description
    if (score >= 90) {
      grade = 'A+'
      description = '编译性能优化效果显著，达到优秀水平'
    } else if (score >= 80) {
      grade = 'A'
      description = '编译性能优化效果良好，系统响应快速'
    } else if (score >= 70) {
      grade = 'B'
      description = '编译性能有所改善，仍有优化空间'
    } else if (score >= 60) {
      grade = 'C'
      description = '编译性能改善有限，需要进一步优化'
    } else {
      grade = 'D'
      description = '编译性能改善不明显，需要重新评估优化策略'
    }

    return {
      score: Math.max(0, score),
      grade,
      description,
      achievements,
      recommendations
    }
  }

  // 保存测试结果
  async saveResults() {
    try {
      await fs.promises.mkdir('test-results/performance', { recursive: true })
      
      const filename = `compilation-performance-test-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.promises.writeFile(
        `test-results/performance/${filename}`,
        JSON.stringify(this.results, null, 2)
      )
      
      console.log(`\n💾 测试结果已保存: test-results/performance/${filename}`)
    } catch (error) {
      console.error('保存测试结果失败:', error)
    }
  }

  // 清理资源
  cleanup() {
    if (this.devServer) {
      this.devServer.kill()
    }
  }
}

// 运行编译性能测试
async function runCompilationPerformanceTest() {
  const tester = new CompilationPerformanceTester()
  
  try {
    console.log('🚀 开始编译性能测试')
    console.log('='.repeat(60))
    
    // 清理缓存
    await tester.cleanCache()
    
    // 测试首次编译
    await tester.testFirstCompilation()
    
    // 等待一段时间
    console.log('\n⏳ 等待系统稳定...')
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 测试增量编译
    await tester.testIncrementalCompilation()
    
    // 生成报告
    const results = tester.generateReport()
    await tester.saveResults()
    
    console.log('\n✅ 编译性能测试完成')
    return results
    
  } catch (error) {
    console.error('❌ 编译性能测试失败:', error)
    throw error
  } finally {
    tester.cleanup()
  }
}

// 命令行运行
if (require.main === module) {
  runCompilationPerformanceTest()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { CompilationPerformanceTester, runCompilationPerformanceTest }
