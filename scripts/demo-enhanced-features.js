#!/usr/bin/env node

/**
 * BitMarket 增强功能演示脚本
 * 创建示例商品来展示新的分类、成色和地理位置功能
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 示例商品数据
const demoProducts = [
  {
    title: 'iPhone 15 Pro Max 256GB 深空黑色',
    description: '全新未拆封的iPhone 15 Pro Max，256GB存储，深空黑色。包装完整，配件齐全，支持全国联保。',
    price: 8999,
    category: 'ELECTRONICS',
    condition: 'NEW',
    city: '北京市',
    district: '朝阳区',
    stock: 1,
    images: 'https://example.com/iphone15.jpg'
  },
  {
    title: 'Nike Air Jordan 1 复古高帮篮球鞋',
    description: '经典的Air Jordan 1设计，黑红配色，尺码42。穿过几次，鞋底磨损轻微，整体保存良好。',
    price: 1299,
    category: 'CLOTHING',
    condition: 'LIKE_NEW',
    city: '上海市',
    district: '浦东新区',
    stock: 1,
    images: 'https://example.com/jordan1.jpg'
  },
  {
    title: '宜家北欧风格实木餐桌',
    description: '宜家购买的实木餐桌，可坐4-6人。使用一年多，有轻微使用痕迹但不影响使用。搬家急售。',
    price: 800,
    category: 'HOME',
    condition: 'GOOD',
    city: '广州市',
    district: '天河区',
    stock: 1,
    images: 'https://example.com/table.jpg'
  },
  {
    title: '《算法导论》第三版 中文版',
    description: '计算机科学经典教材，第三版中文版。书页有少量笔记和标记，但内容完整清晰。',
    price: 89,
    category: 'BOOKS',
    condition: 'GOOD',
    city: '深圳市',
    district: '南山区',
    stock: 1,
    images: 'https://example.com/algorithm-book.jpg'
  },
  {
    title: '迪卡侬专业跑步机 家用静音',
    description: '迪卡侬购买的家用跑步机，使用半年。功能正常，静音效果好。因搬家转让，可上门自提。',
    price: 2800,
    category: 'SPORTS',
    condition: 'LIKE_NEW',
    city: '杭州市',
    district: '西湖区',
    stock: 1,
    images: 'https://example.com/treadmill.jpg'
  },
  {
    title: 'SK-II 神仙水 230ml 正品',
    description: '专柜购买的SK-II神仙水，230ml大瓶装。全新未开封，有购买小票，保证正品。',
    price: 1680,
    category: 'BEAUTY',
    condition: 'NEW',
    city: '南京市',
    district: '鼓楼区',
    stock: 1,
    images: 'https://example.com/skii.jpg'
  },
  {
    title: '原装宝马3系车载充电器',
    description: '宝马3系原装车载充电器，支持快充。车辆置换后闲置，功能完好，接口无磨损。',
    price: 299,
    category: 'AUTOMOTIVE',
    condition: 'LIKE_NEW',
    city: '北京市',
    district: '海淀区',
    stock: 1,
    images: 'https://example.com/bmw-charger.jpg'
  },
  {
    title: '乐高积木 星球大战千年隼号',
    description: '乐高星球大战系列千年隼号，7541片。已拼装完成，所有零件齐全，带原装说明书和包装盒。',
    price: 4999,
    category: 'TOYS',
    condition: 'LIKE_NEW',
    city: '上海市',
    district: '徐汇区',
    stock: 1,
    images: 'https://example.com/lego-falcon.jpg'
  },
  {
    title: '欧姆龙电子血压计 家用上臂式',
    description: '欧姆龙家用电子血压计，上臂式测量，精度高。购买一年，使用频率不高，功能正常。',
    price: 399,
    category: 'HEALTH',
    condition: 'GOOD',
    city: '广州市',
    district: '越秀区',
    stock: 1,
    images: 'https://example.com/blood-pressure.jpg'
  },
  {
    title: '王者荣耀 满级账号 多款皮肤',
    description: '王者荣耀满级账号，拥有多个英雄和限定皮肤。段位王者，战力较高。因不再游戏转让。',
    price: 1200,
    category: 'VIRTUAL',
    condition: 'NOT_APPLICABLE',
    city: '深圳市',
    district: '福田区',
    stock: 1,
    images: 'https://example.com/game-account.jpg'
  }
];

// 获取测试用户
async function getTestUser() {
  const user = await prisma.user.findFirst({
    where: {
      email: '<EMAIL>'
    }
  });
  
  if (!user) {
    throw new Error('测试用户不存在，请先运行账号创建脚本');
  }
  
  return user;
}

// 创建演示商品
async function createDemoProducts() {
  log('🛍️ 开始创建演示商品...', 'cyan');
  
  try {
    const user = await getTestUser();
    log(`👤 使用测试用户: ${user.name} (${user.email})`, 'blue');
    
    const createdProducts = [];
    
    for (const productData of demoProducts) {
      const product = await prisma.product.create({
        data: {
          ...productData,
          sellerId: user.id,
          status: 'AVAILABLE',
          reviewStatus: 'APPROVED',
          shippingFrom: productData.city
        }
      });
      
      createdProducts.push(product);
      
      log(`✅ 创建商品: ${product.title}`, 'green');
      log(`   分类: ${product.category} | 成色: ${product.condition} | 位置: ${product.city}-${product.district}`, 'reset');
    }
    
    log(`\n🎉 成功创建 ${createdProducts.length} 个演示商品！`, 'green');
    return createdProducts;
    
  } catch (error) {
    log(`❌ 创建演示商品失败: ${error.message}`, 'red');
    throw error;
  }
}

// 显示功能统计
async function showFeatureStats() {
  log('\n📊 功能使用统计:', 'cyan');
  
  try {
    // 分类统计
    const categoryStats = await prisma.product.groupBy({
      by: ['category'],
      _count: {
        category: true
      },
      orderBy: {
        _count: {
          category: 'desc'
        }
      }
    });
    
    log('\n🏷️ 分类分布:', 'blue');
    categoryStats.forEach(stat => {
      const categoryNames = {
        'ELECTRONICS': '📱 电子产品',
        'CLOTHING': '👕 服装配饰',
        'HOME': '🏠 家居用品',
        'BOOKS': '📚 图书文具',
        'SPORTS': '⚽ 运动户外',
        'BEAUTY': '💄 美妆护肤',
        'AUTOMOTIVE': '🚗 汽车用品',
        'TOYS': '🧸 玩具母婴',
        'HEALTH': '💊 健康保健',
        'VIRTUAL': '💾 虚拟物品',
        'GENERAL': '📦 其他商品'
      };
      const name = categoryNames[stat.category] || stat.category;
      log(`  ${name}: ${stat._count.category} 个商品`, 'reset');
    });
    
    // 成色统计
    const conditionStats = await prisma.product.groupBy({
      by: ['condition'],
      _count: {
        condition: true
      },
      orderBy: {
        _count: {
          condition: 'desc'
        }
      }
    });
    
    log('\n⭐ 成色分布:', 'blue');
    conditionStats.forEach(stat => {
      const conditionNames = {
        'NEW': '✨ 全新',
        'LIKE_NEW': '🌟 几乎全新',
        'GOOD': '👍 轻微使用痕迹',
        'FAIR': '👌 明显使用痕迹',
        'POOR': '🔧 需要维修',
        'NOT_APPLICABLE': '➖ 无成色类型'
      };
      const name = conditionNames[stat.condition] || stat.condition;
      log(`  ${name}: ${stat._count.condition} 个商品`, 'reset');
    });
    
    // 地理位置统计
    const locationStats = await prisma.product.groupBy({
      by: ['city'],
      _count: {
        city: true
      },
      orderBy: {
        _count: {
          city: 'desc'
        }
      }
    });
    
    log('\n🗺️ 地理位置分布:', 'blue');
    locationStats.forEach(stat => {
      if (stat.city) {
        log(`  ${stat.city}: ${stat._count.city} 个商品`, 'reset');
      }
    });
    
  } catch (error) {
    log(`❌ 统计数据获取失败: ${error.message}`, 'red');
    throw error;
  }
}

// 显示测试链接
function showTestLinks() {
  log('\n🌐 功能测试链接:', 'cyan');
  log('', 'reset');
  log('📝 商品发布页面:', 'blue');
  log('  http://localhost:3000/products/create', 'reset');
  log('  - 测试新的分类选择器', 'reset');
  log('  - 测试成色选择器', 'reset');
  log('  - 测试城市-区县级联选择', 'reset');
  log('', 'reset');
  
  log('🛍️ 商品列表页面:', 'blue');
  log('  http://localhost:3000/products', 'reset');
  log('  - 测试分类筛选功能', 'reset');
  log('  - 测试成色筛选功能', 'reset');
  log('  - 查看演示商品展示', 'reset');
  log('', 'reset');
  
  log('🔍 搜索功能测试:', 'blue');
  log('  - 搜索 "iPhone" 测试电子产品', 'reset');
  log('  - 搜索 "Nike" 测试服装配饰', 'reset');
  log('  - 搜索 "宜家" 测试家居用品', 'reset');
  log('', 'reset');
  
  log('📍 地理位置测试:', 'blue');
  log('  - 筛选北京市商品', 'reset');
  log('  - 筛选上海市商品', 'reset');
  log('  - 筛选广州市商品', 'reset');
}

// 主函数
async function main() {
  try {
    log('🚀 开始 BitMarket 增强功能演示...', 'cyan');
    log('', 'reset');

    // 1. 创建演示商品
    await createDemoProducts();

    // 2. 显示功能统计
    await showFeatureStats();

    // 3. 显示测试链接
    showTestLinks();

    log('\n🎉 演示数据创建完成！', 'green');
    log('', 'reset');
    log('💡 提示:', 'yellow');
    log('  1. 使用测试账号登录: <EMAIL> / user123456', 'reset');
    log('  2. 访问上述链接测试新功能', 'reset');
    log('  3. 尝试创建新商品体验增强的选择器', 'reset');
    log('  4. 使用筛选功能查看不同分类的商品', 'reset');

  } catch (error) {
    log('❌ 演示脚本执行失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main, createDemoProducts, showFeatureStats };
