const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function migrateEscrowSystem() {
  try {
    console.log('🚀 开始迁移托管系统数据库...')

    // 1. 更新User表，添加中间人相关字段
    console.log('📝 1. 更新User表字段...')
    
    // 检查字段是否已存在
    const userFields = await prisma.$queryRaw`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'User' AND TABLE_SCHEMA = DATABASE()
    `
    
    const existingFields = userFields.map(field => field.COLUMN_NAME)
    
    // 添加缺失的字段
    const fieldsToAdd = [
      { name: 'bnbWalletAddress', sql: 'ADD COLUMN bnbWalletAddress VARCHAR(191)' },
      { name: 'bnbWalletVerified', sql: 'ADD COLUMN bnbWalletVerified BOOLEAN DEFAULT FALSE' },
      { name: 'bnbWalletVerifiedAt', sql: 'ADD COLUMN bnbWalletVerifiedAt DATETIME(3)' },
      { name: 'mediatorFeeRate', sql: 'ADD COLUMN mediatorFeeRate DOUBLE' },
      { name: 'mediatorDeposit', sql: 'ADD COLUMN mediatorDeposit DOUBLE' },
      { name: 'mediatorReputation', sql: 'ADD COLUMN mediatorReputation DOUBLE' },
      { name: 'mediatorSuccessRate', sql: 'ADD COLUMN mediatorSuccessRate DOUBLE' },
      { name: 'mediatorTotalOrders', sql: 'ADD COLUMN mediatorTotalOrders INT DEFAULT 0' },
      { name: 'mediatorExperience', sql: 'ADD COLUMN mediatorExperience TEXT' },
      { name: 'mediatorIntroduction', sql: 'ADD COLUMN mediatorIntroduction TEXT' },
      { name: 'mediatorLastActiveAt', sql: 'ADD COLUMN mediatorLastActiveAt DATETIME(3)' },
      { name: 'mediatorVerifiedAt', sql: 'ADD COLUMN mediatorVerifiedAt DATETIME(3)' }
    ]

    for (const field of fieldsToAdd) {
      if (!existingFields.includes(field.name)) {
        try {
          await prisma.$executeRawUnsafe(`ALTER TABLE User ${field.sql}`)
          console.log(`  ✅ 添加字段: ${field.name}`)
        } catch (error) {
          console.log(`  ⚠️  字段 ${field.name} 可能已存在`)
        }
      } else {
        console.log(`  ✅ 字段 ${field.name} 已存在`)
      }
    }

    // 2. 更新Order表，添加托管相关字段
    console.log('📝 2. 更新Order表字段...')
    
    const orderFields = await prisma.$queryRaw`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Order' AND TABLE_SCHEMA = DATABASE()
    `
    
    const existingOrderFields = orderFields.map(field => field.COLUMN_NAME)
    
    const orderFieldsToAdd = [
      { name: 'useEscrow', sql: 'ADD COLUMN useEscrow BOOLEAN DEFAULT FALSE' },
      { name: 'escrowFee', sql: 'ADD COLUMN escrowFee DOUBLE' },
      { name: 'escrowFeeRate', sql: 'ADD COLUMN escrowFeeRate DOUBLE' },
      { name: 'disputeStatus', sql: 'ADD COLUMN disputeStatus VARCHAR(191)' },
      { name: 'disputeReportedAt', sql: 'ADD COLUMN disputeReportedAt DATETIME(3)' },
      { name: 'disputeResolvedAt', sql: 'ADD COLUMN disputeResolvedAt DATETIME(3)' },
      { name: 'disputeReason', sql: 'ADD COLUMN disputeReason VARCHAR(191)' }
    ]

    for (const field of orderFieldsToAdd) {
      if (!existingOrderFields.includes(field.name)) {
        try {
          await prisma.$executeRawUnsafe(`ALTER TABLE \`Order\` ${field.sql}`)
          console.log(`  ✅ 添加字段: ${field.name}`)
        } catch (error) {
          console.log(`  ⚠️  字段 ${field.name} 可能已存在`)
        }
      } else {
        console.log(`  ✅ 字段 ${field.name} 已存在`)
      }
    }

    // 3. 创建新表
    console.log('📝 3. 创建新的托管相关表...')

    // 检查表是否已存在
    const tables = await prisma.$queryRaw`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
    `
    
    const existingTables = tables.map(table => table.TABLE_NAME)

    // 创建EscrowOrder表
    if (!existingTables.includes('EscrowOrder')) {
      await prisma.$executeRaw`
        CREATE TABLE EscrowOrder (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          orderId VARCHAR(191) NOT NULL UNIQUE,
          mediatorId VARCHAR(191) NOT NULL,
          buyerId VARCHAR(191) NOT NULL,
          sellerId VARCHAR(191) NOT NULL,
          amount DOUBLE NOT NULL,
          mediatorFee DOUBLE NOT NULL,
          platformFee DOUBLE NOT NULL,
          status VARCHAR(191) NOT NULL DEFAULT 'PENDING',
          bnbTransactionHash VARCHAR(191),
          mediatorWalletAddress VARCHAR(191) NOT NULL,
          fundedAt DATETIME(3),
          shippedAt DATETIME(3),
          deliveredAt DATETIME(3),
          completedAt DATETIME(3),
          disputedAt DATETIME(3),
          cancelledAt DATETIME(3),
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_mediatorId (mediatorId),
          INDEX idx_buyerId (buyerId),
          INDEX idx_sellerId (sellerId),
          INDEX idx_status (status),
          INDEX idx_createdAt (createdAt),
          
          FOREIGN KEY (orderId) REFERENCES \`Order\`(id) ON DELETE CASCADE,
          FOREIGN KEY (mediatorId) REFERENCES User(id),
          FOREIGN KEY (buyerId) REFERENCES User(id),
          FOREIGN KEY (sellerId) REFERENCES User(id)
        )
      `
      console.log('  ✅ 创建表: EscrowOrder')
    } else {
      console.log('  ✅ 表 EscrowOrder 已存在')
    }

    // 创建EscrowChatRoom表
    if (!existingTables.includes('EscrowChatRoom')) {
      await prisma.$executeRaw`
        CREATE TABLE EscrowChatRoom (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          escrowOrderId VARCHAR(191) NOT NULL UNIQUE,
          roomCode VARCHAR(191) NOT NULL UNIQUE,
          isActive BOOLEAN NOT NULL DEFAULT TRUE,
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_roomCode (roomCode),
          INDEX idx_isActive (isActive),
          
          FOREIGN KEY (escrowOrderId) REFERENCES EscrowOrder(id) ON DELETE CASCADE
        )
      `
      console.log('  ✅ 创建表: EscrowChatRoom')
    } else {
      console.log('  ✅ 表 EscrowChatRoom 已存在')
    }

    // 创建EscrowDispute表
    if (!existingTables.includes('EscrowDispute')) {
      await prisma.$executeRaw`
        CREATE TABLE EscrowDispute (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          escrowOrderId VARCHAR(191) NOT NULL,
          reporterId VARCHAR(191) NOT NULL,
          reportedId VARCHAR(191) NOT NULL,
          reason VARCHAR(191) NOT NULL,
          description TEXT NOT NULL,
          evidence JSON,
          status VARCHAR(191) NOT NULL DEFAULT 'PENDING',
          priority VARCHAR(191) NOT NULL DEFAULT 'MEDIUM',
          adminAssigned VARCHAR(191),
          resolution TEXT,
          resolvedAt DATETIME(3),
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_escrowOrderId (escrowOrderId),
          INDEX idx_reporterId (reporterId),
          INDEX idx_status (status),
          INDEX idx_priority (priority),
          INDEX idx_createdAt (createdAt),
          
          FOREIGN KEY (escrowOrderId) REFERENCES EscrowOrder(id) ON DELETE CASCADE,
          FOREIGN KEY (reporterId) REFERENCES User(id),
          FOREIGN KEY (reportedId) REFERENCES User(id),
          FOREIGN KEY (adminAssigned) REFERENCES User(id)
        )
      `
      console.log('  ✅ 创建表: EscrowDispute')
    } else {
      console.log('  ✅ 表 EscrowDispute 已存在')
    }

    // 创建MediatorApplication表
    if (!existingTables.includes('MediatorApplication')) {
      await prisma.$executeRaw`
        CREATE TABLE MediatorApplication (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          userId VARCHAR(191) NOT NULL UNIQUE,
          bnbWalletAddress VARCHAR(191) NOT NULL,
          depositAmount DOUBLE NOT NULL,
          feeRate DOUBLE NOT NULL,
          experience TEXT NOT NULL,
          introduction TEXT NOT NULL,
          status VARCHAR(191) NOT NULL DEFAULT 'PENDING',
          reviewedBy VARCHAR(191),
          reviewNotes TEXT,
          approvedAt DATETIME(3),
          rejectedAt DATETIME(3),
          suspendedAt DATETIME(3),
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_userId (userId),
          INDEX idx_status (status),
          INDEX idx_createdAt (createdAt),
          
          FOREIGN KEY (userId) REFERENCES User(id) ON DELETE CASCADE,
          FOREIGN KEY (reviewedBy) REFERENCES User(id)
        )
      `
      console.log('  ✅ 创建表: MediatorApplication')
    } else {
      console.log('  ✅ 表 MediatorApplication 已存在')
    }

    // 创建WithdrawalVoucher表
    if (!existingTables.includes('WithdrawalVoucher')) {
      await prisma.$executeRaw`
        CREATE TABLE WithdrawalVoucher (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          code VARCHAR(191) NOT NULL UNIQUE,
          amount DOUBLE NOT NULL,
          description VARCHAR(191),
          validUntil DATETIME(3) NOT NULL,
          isUsed BOOLEAN NOT NULL DEFAULT FALSE,
          usedBy VARCHAR(191),
          usedAt DATETIME(3),
          issuedBy VARCHAR(191) NOT NULL,
          issuedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_code (code),
          INDEX idx_isUsed (isUsed),
          INDEX idx_validUntil (validUntil),
          INDEX idx_usedBy (usedBy),
          
          FOREIGN KEY (usedBy) REFERENCES User(id),
          FOREIGN KEY (issuedBy) REFERENCES User(id)
        )
      `
      console.log('  ✅ 创建表: WithdrawalVoucher')
    } else {
      console.log('  ✅ 表 WithdrawalVoucher 已存在')
    }

    // 创建MediatorReward表
    if (!existingTables.includes('MediatorReward')) {
      await prisma.$executeRaw`
        CREATE TABLE MediatorReward (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          mediatorId VARCHAR(191) NOT NULL,
          rewardType VARCHAR(191) NOT NULL,
          amount DOUBLE,
          voucherId VARCHAR(191),
          description VARCHAR(191) NOT NULL,
          earnedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          claimedAt DATETIME(3),
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_mediatorId (mediatorId),
          INDEX idx_rewardType (rewardType),
          INDEX idx_earnedAt (earnedAt),
          
          FOREIGN KEY (mediatorId) REFERENCES User(id),
          FOREIGN KEY (voucherId) REFERENCES WithdrawalVoucher(id)
        )
      `
      console.log('  ✅ 创建表: MediatorReward')
    } else {
      console.log('  ✅ 表 MediatorReward 已存在')
    }

    // 创建BlockchainTransaction表
    if (!existingTables.includes('BlockchainTransaction')) {
      await prisma.$executeRaw`
        CREATE TABLE BlockchainTransaction (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          txHash VARCHAR(191) NOT NULL UNIQUE,
          network VARCHAR(191) NOT NULL DEFAULT 'BNB_CHAIN',
          fromAddress VARCHAR(191) NOT NULL,
          toAddress VARCHAR(191) NOT NULL,
          amount DOUBLE NOT NULL,
          tokenSymbol VARCHAR(191) NOT NULL DEFAULT 'USDT',
          status VARCHAR(191) NOT NULL DEFAULT 'PENDING',
          blockNumber INT,
          gasUsed DOUBLE,
          gasFee DOUBLE,
          confirmations INT NOT NULL DEFAULT 0,
          relatedOrderId VARCHAR(191),
          relatedEscrowId VARCHAR(191),
          createdAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updatedAt DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          
          INDEX idx_txHash (txHash),
          INDEX idx_network (network),
          INDEX idx_status (status),
          INDEX idx_relatedOrderId (relatedOrderId),
          INDEX idx_relatedEscrowId (relatedEscrowId),
          
          FOREIGN KEY (relatedOrderId) REFERENCES \`Order\`(id),
          FOREIGN KEY (relatedEscrowId) REFERENCES EscrowOrder(id)
        )
      `
      console.log('  ✅ 创建表: BlockchainTransaction')
    } else {
      console.log('  ✅ 表 BlockchainTransaction 已存在')
    }

    console.log('🎉 托管系统数据库迁移完成！')
    console.log('\n📋 迁移总结:')
    console.log('  - 更新了User表，添加中间人相关字段')
    console.log('  - 更新了Order表，添加托管相关字段')
    console.log('  - 创建了EscrowOrder表（托管订单）')
    console.log('  - 创建了EscrowChatRoom表（托管聊天室）')
    console.log('  - 创建了EscrowDispute表（托管争议）')
    console.log('  - 创建了MediatorApplication表（中间人申请）')
    console.log('  - 创建了WithdrawalVoucher表（提现券）')
    console.log('  - 创建了MediatorReward表（中间人奖励）')
    console.log('  - 创建了BlockchainTransaction表（区块链交易）')

  } catch (error) {
    console.error('❌ 迁移失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 运行迁移
migrateEscrowSystem().catch(console.error)
