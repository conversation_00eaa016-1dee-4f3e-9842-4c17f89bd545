/**
 * 手续费改革测试脚本
 * 验证阶梯式提现费率和信誉展示系统
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// 测试用户数据
const testUsers = [
  {
    email: '<EMAIL>',
    name: '小额用户',
    depositBalance: 80,
    creditLevel: 'BRONZE'
  },
  {
    email: '<EMAIL>',
    name: '中额用户',
    depositBalance: 300,
    creditLevel: 'SILVER'
  },
  {
    email: '<EMAIL>',
    name: '大额用户',
    depositBalance: 1500,
    creditLevel: 'GOLD'
  },
  {
    email: '<EMAIL>',
    name: '超大额用户',
    depositBalance: 5000,
    creditLevel: 'DIAMOND'
  }
]

// 测试提现金额
const testAmounts = [50, 150, 800, 2500]

async function createTestUsers() {
  console.log('🔧 创建测试用户...')
  
  const users = {}
  
  for (const userData of testUsers) {
    try {
      const user = await prisma.user.upsert({
        where: { email: userData.email },
        update: userData,
        create: userData
      })
      users[userData.email] = user
      console.log(`✅ 创建用户: ${user.name} - ${user.depositBalance} USDT (${user.creditLevel})`)
    } catch (error) {
      console.error(`❌ 创建用户失败: ${userData.email}`, error.message)
    }
  }
  
  return users
}

function calculateTieredWithdrawalFee(amount) {
  // 阶梯式费率配置
  const tiers = [
    { min: 0, max: 100, type: 'FIXED', value: 0.5 },
    { min: 101, max: 500, type: 'PERCENTAGE', value: 0.005 },
    { min: 501, max: 2000, type: 'PERCENTAGE', value: 0.003 },
    { min: 2001, max: Infinity, type: 'FREE', value: 0 }
  ]
  
  const tier = tiers.find(t => amount >= t.min && amount <= t.max)
  
  let fee = 0
  switch (tier.type) {
    case 'FIXED':
      fee = tier.value
      break
    case 'PERCENTAGE':
      fee = amount * tier.value
      break
    case 'FREE':
      fee = 0
      break
  }
  
  return { fee, tier }
}

function getCreditDiscount(creditLevel) {
  const discounts = {
    'BRONZE': 0,
    'SILVER': 0.1,
    'GOLD': 0.2,
    'PLATINUM': 0.3,
    'DIAMOND': 0.5
  }
  return discounts[creditLevel] || 0
}

async function testTieredWithdrawalFees() {
  console.log('\n💰 测试阶梯式提现手续费...')
  
  try {
    console.log('提现金额档位测试:')
    console.log('─'.repeat(80))
    console.log('金额(USDT)\t档位\t\t基础费用\t有效费率')
    console.log('─'.repeat(80))
    
    for (const amount of testAmounts) {
      const { fee, tier } = calculateTieredWithdrawalFee(amount)
      const effectiveRate = amount > 0 ? (fee / amount) * 100 : 0
      
      let tierDesc = ''
      if (tier.type === 'FIXED') {
        tierDesc = `固定${tier.value} USDT`
      } else if (tier.type === 'PERCENTAGE') {
        tierDesc = `${(tier.value * 100).toFixed(1)}%费率`
      } else {
        tierDesc = '免手续费'
      }
      
      console.log(`${amount}\t\t${tierDesc}\t\t${fee.toFixed(4)}\t\t${effectiveRate.toFixed(3)}%`)
    }
    
  } catch (error) {
    console.error('❌ 测试阶梯式提现手续费失败:', error.message)
  }
}

async function testCreditLevelDiscounts() {
  console.log('\n🏆 测试信用等级折扣...')
  
  try {
    const testAmount = 200 // 测试200 USDT提现
    
    console.log(`\n${testAmount} USDT 提现费用对比:`)
    console.log('─'.repeat(60))
    console.log('等级\t\t基础费用\t折扣\t最终费用\t节省')
    console.log('─'.repeat(60))
    
    for (const userData of testUsers) {
      const { fee: baseFee } = calculateTieredWithdrawalFee(testAmount)
      const discount = getCreditDiscount(userData.creditLevel)
      const finalFee = baseFee * (1 - discount)
      const savings = baseFee - finalFee
      
      console.log(`${userData.creditLevel}\t\t${baseFee.toFixed(4)}\t\t${(discount * 100).toFixed(0)}%\t${finalFee.toFixed(4)}\t\t${savings.toFixed(4)}`)
    }
    
  } catch (error) {
    console.error('❌ 测试信用等级折扣失败:', error.message)
  }
}

async function testReformComparison() {
  console.log('\n📊 测试改革前后对比...')
  
  try {
    const testAmount = 500
    
    // 旧系统：交易5%手续费 + 提现1%手续费
    const oldTransactionFee = testAmount * 0.05
    const oldWithdrawalFee = testAmount * 0.01
    const oldTotalFee = oldTransactionFee + oldWithdrawalFee
    
    // 新系统：交易0手续费 + 阶梯式提现费率
    const { fee: newWithdrawalFee } = calculateTieredWithdrawalFee(testAmount)
    const newTransactionFee = 0
    const newTotalFee = newTransactionFee + newWithdrawalFee
    
    const savings = oldTotalFee - newTotalFee
    const savingsRate = (savings / oldTotalFee) * 100
    
    console.log(`\n${testAmount} USDT 交易+提现费用对比:`)
    console.log('─'.repeat(50))
    console.log('项目\t\t旧系统\t\t新系统')
    console.log('─'.repeat(50))
    console.log(`交易手续费\t${oldTransactionFee.toFixed(2)} USDT\t\t0.0000 USDT`)
    console.log(`提现手续费\t${oldWithdrawalFee.toFixed(4)} USDT\t\t${newWithdrawalFee.toFixed(4)} USDT`)
    console.log(`总费用\t\t${oldTotalFee.toFixed(4)} USDT\t\t${newTotalFee.toFixed(4)} USDT`)
    console.log('─'.repeat(50))
    console.log(`💰 节省费用: ${savings.toFixed(4)} USDT (${savingsRate.toFixed(1)}%)`)
    
  } catch (error) {
    console.error('❌ 测试改革对比失败:', error.message)
  }
}

async function testBatchWithdrawalOptimization() {
  console.log('\n⏰ 测试批量提现优化...')
  
  try {
    const amounts = [100, 200, 300, 150] // 模拟多笔提现
    const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0)
    
    // 分别提现的费用
    const individualFees = amounts.map(amount => {
      const { fee } = calculateTieredWithdrawalFee(amount)
      return fee
    })
    const totalIndividualFees = individualFees.reduce((sum, fee) => sum + fee, 0)
    
    // 合并提现的费用
    const { fee: batchFee } = calculateTieredWithdrawalFee(totalAmount)
    const batchDiscount = 0.1 // 10%批量折扣
    const finalBatchFee = batchFee * (1 - batchDiscount)
    
    const savings = totalIndividualFees - finalBatchFee
    const savingsRate = (savings / totalIndividualFees) * 100
    
    console.log('批量提现优化效果:')
    console.log('─'.repeat(40))
    console.log(`分别提现: ${amounts.join(' + ')} USDT`)
    console.log(`分别费用: ${individualFees.map(f => f.toFixed(4)).join(' + ')} = ${totalIndividualFees.toFixed(4)} USDT`)
    console.log(`合并提现: ${totalAmount} USDT`)
    console.log(`合并费用: ${finalBatchFee.toFixed(4)} USDT (含10%批量折扣)`)
    console.log(`节省费用: ${savings.toFixed(4)} USDT (${savingsRate.toFixed(1)}%)`)
    
  } catch (error) {
    console.error('❌ 测试批量提现优化失败:', error.message)
  }
}

async function testReputationSystem() {
  console.log('\n⭐ 测试信誉展示系统...')
  
  try {
    // 模拟用户信誉数据
    const reputationData = [
      {
        name: '新手用户',
        depositBalance: 50,
        totalOrders: 2,
        fulfillmentRate: 100,
        avgRating: 4.5,
        level: 'NEWCOMER'
      },
      {
        name: '可信用户',
        depositBalance: 300,
        totalOrders: 15,
        fulfillmentRate: 96,
        avgRating: 4.7,
        level: 'TRUSTED'
      },
      {
        name: '专家用户',
        depositBalance: 2000,
        totalOrders: 80,
        fulfillmentRate: 98,
        avgRating: 4.9,
        level: 'EXPERT'
      }
    ]
    
    console.log('用户信誉展示:')
    console.log('─'.repeat(70))
    console.log('用户\t\t担保金\t订单数\t履约率\t评分\t等级')
    console.log('─'.repeat(70))
    
    for (const user of reputationData) {
      const stars = user.level === 'NEWCOMER' ? '⭐☆☆☆☆' :
                   user.level === 'TRUSTED' ? '⭐⭐☆☆☆' :
                   user.level === 'EXPERT' ? '⭐⭐⭐⭐☆' : '⭐⭐⭐⭐⭐'
      
      console.log(`${user.name}\t${user.depositBalance} USDT\t${user.totalOrders}笔\t${user.fulfillmentRate}%\t${user.avgRating}\t${stars}`)
    }
    
  } catch (error) {
    console.error('❌ 测试信誉展示系统失败:', error.message)
  }
}

async function runFeeReformTests() {
  console.log('🚀 开始手续费改革测试\n')
  
  try {
    // 创建测试用户
    const users = await createTestUsers()
    
    // 测试阶梯式提现费率
    await testTieredWithdrawalFees()
    
    // 测试信用等级折扣
    await testCreditLevelDiscounts()
    
    // 测试改革前后对比
    await testReformComparison()
    
    // 测试批量提现优化
    await testBatchWithdrawalOptimization()
    
    // 测试信誉展示系统
    await testReputationSystem()
    
    console.log('\n🎉 手续费改革测试完成！')
    console.log('\n📋 改革效果总结:')
    console.log('✅ 交易零手续费，提升用户体验')
    console.log('✅ 阶梯式提现费率，大额用户受益')
    console.log('✅ 信用等级折扣，激励长期使用')
    console.log('✅ 批量提现优化，降低运营成本')
    console.log('✅ 信誉可视化展示，增强信任度')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  runFeeReformTests()
}

module.exports = {
  createTestUsers,
  testTieredWithdrawalFees,
  testCreditLevelDiscounts,
  testReformComparison,
  testBatchWithdrawalOptimization,
  testReputationSystem,
  runFeeReformTests
}
