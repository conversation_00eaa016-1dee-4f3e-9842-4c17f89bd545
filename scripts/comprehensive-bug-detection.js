#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

class ComprehensiveBugDetector {
  constructor() {
    this.bugs = []
    this.warnings = []
    this.suggestions = []
    this.testResults = {
      syntax: [],
      imports: [],
      types: [],
      runtime: [],
      security: [],
      performance: []
    }
  }

  // 检查语法错误
  async checkSyntaxErrors() {
    console.log('🔍 检查语法错误...')
    
    const files = this.getAllTSXFiles()
    
    for (const file of files.slice(0, 20)) { // 限制检查前20个文件
      try {
        const content = fs.readFileSync(file, 'utf8')
        
        // 检查常见语法问题
        const syntaxIssues = this.detectSyntaxIssues(content, file)
        this.testResults.syntax.push(...syntaxIssues)
        
      } catch (error) {
        this.bugs.push({
          type: 'file-access',
          file: file,
          message: `无法读取文件: ${error.message}`,
          severity: 'high'
        })
      }
    }
    
    console.log(`  📊 检查了 ${Math.min(files.length, 20)} 个文件`)
    console.log(`  🚨 发现语法问题: ${this.testResults.syntax.length}个`)
  }

  // 检测语法问题
  detectSyntaxIssues(content, filePath) {
    const issues = []
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // 检查'use client'位置
      if (line.includes("'use client'") && index > 0) {
        const prevLines = lines.slice(0, index)
        const hasImportsOrExports = prevLines.some(l => 
          l.trim().startsWith('import ') || 
          l.trim().startsWith('export ') ||
          l.trim().startsWith('//')
        )
        
        if (hasImportsOrExports) {
          issues.push({
            type: 'use-client-position',
            file: filePath,
            line: lineNum,
            message: "'use client' 指令必须在文件顶部",
            severity: 'high',
            code: line.trim()
          })
        }
      }
      
      // 检查未闭合的JSX标签
      if (line.includes('<') && !line.includes('</') && !line.includes('/>')) {
        const tagMatch = line.match(/<(\w+)/)
        if (tagMatch && !['img', 'br', 'hr', 'input', 'meta', 'link'].includes(tagMatch[1])) {
          const restOfFile = lines.slice(index).join('\n')
          if (!restOfFile.includes(`</${tagMatch[1]}>`)) {
            issues.push({
              type: 'unclosed-jsx',
              file: filePath,
              line: lineNum,
              message: `可能未闭合的JSX标签: ${tagMatch[1]}`,
              severity: 'medium',
              code: line.trim()
            })
          }
        }
      }
      
      // 检查未定义的变量使用
      const undefinedVarMatch = line.match(/\b(\w+)\s*\.\s*\w+/)
      if (undefinedVarMatch && !line.includes('const ') && !line.includes('let ') && !line.includes('var ')) {
        const varName = undefinedVarMatch[1]
        if (!content.includes(`const ${varName}`) && 
            !content.includes(`let ${varName}`) && 
            !content.includes(`var ${varName}`) &&
            !content.includes(`import`) &&
            !['console', 'window', 'document', 'process', 'global'].includes(varName)) {
          issues.push({
            type: 'undefined-variable',
            file: filePath,
            line: lineNum,
            message: `可能使用了未定义的变量: ${varName}`,
            severity: 'medium',
            code: line.trim()
          })
        }
      }
    })
    
    return issues
  }

  // 检查导入错误
  async checkImportErrors() {
    console.log('\n📦 检查导入错误...')
    
    const files = this.getAllTSXFiles()
    
    for (const file of files.slice(0, 15)) {
      try {
        const content = fs.readFileSync(file, 'utf8')
        const importIssues = this.detectImportIssues(content, file)
        this.testResults.imports.push(...importIssues)
        
      } catch (error) {
        // 忽略文件读取错误
      }
    }
    
    console.log(`  📊 检查了导入语句`)
    console.log(`  🚨 发现导入问题: ${this.testResults.imports.length}个`)
  }

  // 检测导入问题
  detectImportIssues(content, filePath) {
    const issues = []
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // 检查相对路径导入
      if (line.includes('import') && line.includes('./')) {
        const pathMatch = line.match(/from\s+['"](.+)['"]/)
        if (pathMatch) {
          const importPath = pathMatch[1]
          if (importPath.startsWith('./') || importPath.startsWith('../')) {
            const fullPath = path.resolve(path.dirname(filePath), importPath)
            const possibleExtensions = ['.tsx', '.ts', '.js', '.jsx']
            
            let fileExists = false
            for (const ext of possibleExtensions) {
              if (fs.existsSync(fullPath + ext) || fs.existsSync(fullPath + '/index' + ext)) {
                fileExists = true
                break
              }
            }
            
            if (!fileExists && !fs.existsSync(fullPath)) {
              issues.push({
                type: 'missing-import',
                file: filePath,
                line: lineNum,
                message: `导入的文件可能不存在: ${importPath}`,
                severity: 'high',
                code: line.trim()
              })
            }
          }
        }
      }
      
      // 检查未使用的导入
      if (line.includes('import') && line.includes('{')) {
        const importMatch = line.match(/import\s*{\s*([^}]+)\s*}/)
        if (importMatch) {
          const imports = importMatch[1].split(',').map(i => i.trim())
          imports.forEach(importName => {
            if (importName && !content.includes(importName.replace(/\s+as\s+\w+/, ''))) {
              issues.push({
                type: 'unused-import',
                file: filePath,
                line: lineNum,
                message: `可能未使用的导入: ${importName}`,
                severity: 'low',
                code: line.trim()
              })
            }
          })
        }
      }
    })
    
    return issues
  }

  // 检查TypeScript类型错误
  async checkTypeErrors() {
    console.log('\n📝 检查TypeScript类型错误...')
    
    return new Promise((resolve) => {
      const tsc = spawn('npx', ['tsc', '--noEmit', '--skipLibCheck'], {
        stdio: 'pipe',
        shell: process.platform === 'win32'
      })
      
      let output = ''
      let errorOutput = ''
      
      tsc.stdout.on('data', (data) => {
        output += data.toString()
      })
      
      tsc.stderr.on('data', (data) => {
        errorOutput += data.toString()
      })
      
      tsc.on('close', (code) => {
        if (code !== 0) {
          const typeErrors = this.parseTypeScriptErrors(errorOutput + output)
          this.testResults.types.push(...typeErrors)
          console.log(`  🚨 发现TypeScript错误: ${typeErrors.length}个`)
        } else {
          console.log('  ✅ 没有TypeScript类型错误')
        }
        resolve()
      })
      
      tsc.on('error', (error) => {
        console.log('  ⚠️  无法运行TypeScript检查:', error.message)
        resolve()
      })
      
      // 10秒超时
      setTimeout(() => {
        tsc.kill()
        console.log('  ⚠️  TypeScript检查超时')
        resolve()
      }, 10000)
    })
  }

  // 解析TypeScript错误
  parseTypeScriptErrors(output) {
    const errors = []
    const lines = output.split('\n')
    
    lines.forEach(line => {
      if (line.includes('error TS')) {
        const match = line.match(/(.+)\((\d+),(\d+)\):\s*error\s+(TS\d+):\s*(.+)/)
        if (match) {
          errors.push({
            type: 'typescript-error',
            file: match[1],
            line: parseInt(match[2]),
            column: parseInt(match[3]),
            code: match[4],
            message: match[5],
            severity: 'high'
          })
        }
      }
    })
    
    return errors
  }

  // 检查运行时错误
  async checkRuntimeErrors() {
    console.log('\n🏃 检查潜在运行时错误...')
    
    const files = this.getAllTSXFiles()
    
    for (const file of files.slice(0, 10)) {
      try {
        const content = fs.readFileSync(file, 'utf8')
        const runtimeIssues = this.detectRuntimeIssues(content, file)
        this.testResults.runtime.push(...runtimeIssues)
        
      } catch (error) {
        // 忽略文件读取错误
      }
    }
    
    console.log(`  🚨 发现潜在运行时问题: ${this.testResults.runtime.length}个`)
  }

  // 检测运行时问题
  detectRuntimeIssues(content, filePath) {
    const issues = []
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // 检查可能的空值引用
      if (line.includes('.') && !line.includes('?.')) {
        const nullableMatch = line.match(/(\w+)\.\w+/)
        if (nullableMatch && 
            (content.includes(`${nullableMatch[1]} = null`) || 
             content.includes(`${nullableMatch[1]} = undefined`) ||
             content.includes(`${nullableMatch[1]}?`))) {
          issues.push({
            type: 'potential-null-reference',
            file: filePath,
            line: lineNum,
            message: `可能的空值引用: ${nullableMatch[1]}`,
            severity: 'medium',
            code: line.trim()
          })
        }
      }
      
      // 检查未处理的Promise
      if (line.includes('await') && !line.includes('try') && !line.includes('catch')) {
        const functionContext = this.getFunctionContext(lines, index)
        if (!functionContext.includes('try') && !functionContext.includes('catch')) {
          issues.push({
            type: 'unhandled-promise',
            file: filePath,
            line: lineNum,
            message: 'await调用缺少错误处理',
            severity: 'medium',
            code: line.trim()
          })
        }
      }
      
      // 检查数组访问
      if (line.includes('[') && line.includes(']')) {
        const arrayMatch = line.match(/(\w+)\[(\d+|\w+)\]/)
        if (arrayMatch && !line.includes('?.')) {
          issues.push({
            type: 'unsafe-array-access',
            file: filePath,
            line: lineNum,
            message: `不安全的数组访问: ${arrayMatch[1]}[${arrayMatch[2]}]`,
            severity: 'low',
            code: line.trim()
          })
        }
      }
    })
    
    return issues
  }

  // 检查安全问题
  async checkSecurityIssues() {
    console.log('\n🔒 检查安全问题...')
    
    const files = this.getAllTSXFiles()
    
    for (const file of files.slice(0, 10)) {
      try {
        const content = fs.readFileSync(file, 'utf8')
        const securityIssues = this.detectSecurityIssues(content, file)
        this.testResults.security.push(...securityIssues)
        
      } catch (error) {
        // 忽略文件读取错误
      }
    }
    
    console.log(`  🚨 发现安全问题: ${this.testResults.security.length}个`)
  }

  // 检测安全问题
  detectSecurityIssues(content, filePath) {
    const issues = []
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // 检查硬编码的敏感信息
      const sensitivePatterns = [
        /password\s*[:=]\s*['"][^'"]+['"]/i,
        /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
        /secret\s*[:=]\s*['"][^'"]+['"]/i,
        /token\s*[:=]\s*['"][^'"]+['"]/i
      ]
      
      sensitivePatterns.forEach(pattern => {
        if (pattern.test(line)) {
          issues.push({
            type: 'hardcoded-secret',
            file: filePath,
            line: lineNum,
            message: '可能包含硬编码的敏感信息',
            severity: 'high',
            code: line.trim().replace(/['"][^'"]+['"]/, '"***"')
          })
        }
      })
      
      // 检查不安全的HTML渲染
      if (line.includes('dangerouslySetInnerHTML')) {
        issues.push({
          type: 'dangerous-html',
          file: filePath,
          line: lineNum,
          message: '使用了dangerouslySetInnerHTML，可能存在XSS风险',
          severity: 'high',
          code: line.trim()
        })
      }
      
      // 检查eval使用
      if (line.includes('eval(')) {
        issues.push({
          type: 'eval-usage',
          file: filePath,
          line: lineNum,
          message: '使用了eval()，存在代码注入风险',
          severity: 'high',
          code: line.trim()
        })
      }
    })
    
    return issues
  }

  // 检查性能问题
  async checkPerformanceIssues() {
    console.log('\n⚡ 检查性能问题...')
    
    const files = this.getAllTSXFiles()
    
    for (const file of files.slice(0, 10)) {
      try {
        const content = fs.readFileSync(file, 'utf8')
        const performanceIssues = this.detectPerformanceIssues(content, file)
        this.testResults.performance.push(...performanceIssues)
        
      } catch (error) {
        // 忽略文件读取错误
      }
    }
    
    console.log(`  🚨 发现性能问题: ${this.testResults.performance.length}个`)
  }

  // 检测性能问题
  detectPerformanceIssues(content, filePath) {
    const issues = []
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // 检查在render中创建对象
      if (line.includes('return') && line.includes('{') && 
          (content.includes('function ') || content.includes('const ') && content.includes('= ()'))) {
        if (line.includes('{}') || line.includes('[]')) {
          issues.push({
            type: 'object-creation-in-render',
            file: filePath,
            line: lineNum,
            message: '在render中创建新对象，可能影响性能',
            severity: 'low',
            code: line.trim()
          })
        }
      }
      
      // 检查缺少React.memo
      if (content.includes('export default function') && 
          !content.includes('React.memo') && 
          !content.includes('memo(') &&
          content.includes('props')) {
        issues.push({
          type: 'missing-memo',
          file: filePath,
          line: 1,
          message: '组件可能需要使用React.memo优化',
          severity: 'low',
          code: '建议使用React.memo包装组件'
        })
      }
      
      // 检查大型导入
      if (line.includes('import') && line.includes('*')) {
        issues.push({
          type: 'wildcard-import',
          file: filePath,
          line: lineNum,
          message: '使用了通配符导入，可能影响tree-shaking',
          severity: 'low',
          code: line.trim()
        })
      }
    })
    
    return issues
  }

  // 获取所有TSX文件
  getAllTSXFiles() {
    const files = []
    const extensions = ['.tsx', '.ts', '.jsx', '.js']
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir)
        items.forEach(item => {
          const fullPath = path.join(dir, item)
          const stat = fs.statSync(fullPath)
          
          if (stat.isDirectory() && 
              !item.startsWith('.') && 
              item !== 'node_modules' && 
              item !== 'test-results') {
            scanDir(fullPath)
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            files.push(fullPath)
          }
        })
      } catch (error) {
        // 忽略访问错误
      }
    }
    
    scanDir('app')
    scanDir('components')
    scanDir('lib')
    
    return files
  }

  // 获取函数上下文
  getFunctionContext(lines, currentIndex) {
    const start = Math.max(0, currentIndex - 10)
    const end = Math.min(lines.length, currentIndex + 10)
    return lines.slice(start, end).join('\n')
  }

  // 生成Bug报告
  generateBugReport() {
    console.log('\n📊 Bug检测报告')
    console.log('='.repeat(60))
    
    const allIssues = [
      ...this.testResults.syntax,
      ...this.testResults.imports,
      ...this.testResults.types,
      ...this.testResults.runtime,
      ...this.testResults.security,
      ...this.testResults.performance
    ]
    
    const highSeverity = allIssues.filter(i => i.severity === 'high')
    const mediumSeverity = allIssues.filter(i => i.severity === 'medium')
    const lowSeverity = allIssues.filter(i => i.severity === 'low')
    
    console.log(`🚨 严重问题: ${highSeverity.length}个`)
    console.log(`⚠️  中等问题: ${mediumSeverity.length}个`)
    console.log(`💡 轻微问题: ${lowSeverity.length}个`)
    console.log(`📊 总计问题: ${allIssues.length}个`)
    
    if (highSeverity.length > 0) {
      console.log('\n🚨 严重问题详情:')
      highSeverity.slice(0, 10).forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.type.toUpperCase()}] ${issue.message}`)
        console.log(`   📁 文件: ${path.relative(process.cwd(), issue.file)}:${issue.line}`)
        if (issue.code) {
          console.log(`   💻 代码: ${issue.code}`)
        }
        console.log('')
      })
    }
    
    if (mediumSeverity.length > 0) {
      console.log('\n⚠️  中等问题详情:')
      mediumSeverity.slice(0, 5).forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.type.toUpperCase()}] ${issue.message}`)
        console.log(`   📁 文件: ${path.relative(process.cwd(), issue.file)}:${issue.line}`)
        console.log('')
      })
    }
    
    // 生成修复建议
    this.generateFixSuggestions(allIssues)
    
    return {
      total: allIssues.length,
      high: highSeverity.length,
      medium: mediumSeverity.length,
      low: lowSeverity.length,
      issues: allIssues
    }
  }

  // 生成修复建议
  generateFixSuggestions(issues) {
    console.log('\n💡 修复建议:')
    
    const suggestions = new Map()
    
    issues.forEach(issue => {
      if (!suggestions.has(issue.type)) {
        suggestions.set(issue.type, {
          count: 0,
          suggestion: this.getFixSuggestion(issue.type)
        })
      }
      suggestions.get(issue.type).count++
    })
    
    Array.from(suggestions.entries())
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 10)
      .forEach(([type, data]) => {
        console.log(`• ${data.suggestion} (${data.count}个问题)`)
      })
  }

  // 获取修复建议
  getFixSuggestion(issueType) {
    const suggestions = {
      'use-client-position': '将"use client"指令移到文件顶部',
      'unclosed-jsx': '检查并闭合JSX标签',
      'undefined-variable': '确保变量已定义或正确导入',
      'missing-import': '检查导入路径是否正确',
      'unused-import': '移除未使用的导入',
      'typescript-error': '修复TypeScript类型错误',
      'potential-null-reference': '使用可选链操作符(?.)或空值检查',
      'unhandled-promise': '添加try-catch错误处理',
      'unsafe-array-access': '添加数组长度检查或使用可选链',
      'hardcoded-secret': '将敏感信息移到环境变量',
      'dangerous-html': '验证HTML内容或使用安全的替代方案',
      'eval-usage': '避免使用eval()，寻找安全替代方案',
      'object-creation-in-render': '将对象创建移到组件外部或使用useMemo',
      'missing-memo': '考虑使用React.memo优化组件',
      'wildcard-import': '使用具名导入替代通配符导入'
    }
    
    return suggestions[issueType] || '检查并修复相关问题'
  }

  // 保存检测结果
  async saveResults(report) {
    try {
      await fs.promises.mkdir('test-results/bug-detection', { recursive: true })
      
      const filename = `bug-detection-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.promises.writeFile(
        `test-results/bug-detection/${filename}`,
        JSON.stringify({
          timestamp: Date.now(),
          summary: report,
          details: this.testResults
        }, null, 2)
      )
      
      console.log(`\n💾 检测结果已保存: test-results/bug-detection/${filename}`)
      
    } catch (error) {
      console.error('保存检测结果失败:', error)
    }
  }

  // 运行全面检测
  async runComprehensiveDetection() {
    console.log('🔍 开始全面Bug检测')
    console.log('='.repeat(60))
    
    await this.checkSyntaxErrors()
    await this.checkImportErrors()
    await this.checkTypeErrors()
    await this.checkRuntimeErrors()
    await this.checkSecurityIssues()
    await this.checkPerformanceIssues()
    
    const report = this.generateBugReport()
    await this.saveResults(report)
    
    return report
  }
}

// 运行检测
if (require.main === module) {
  const detector = new ComprehensiveBugDetector()
  detector.runComprehensiveDetection()
    .then((report) => {
      console.log('\n✅ Bug检测完成')
      if (report.high > 0) {
        console.log('⚠️  发现严重问题，建议立即修复')
        process.exit(1)
      } else {
        console.log('🎉 没有发现严重问题')
        process.exit(0)
      }
    })
    .catch(error => {
      console.error('\n❌ Bug检测失败:', error)
      process.exit(1)
    })
}

module.exports = { ComprehensiveBugDetector }
