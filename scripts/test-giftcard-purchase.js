const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testGiftCardPurchase() {
  try {
    console.log('🧪 测试礼品卡购买功能...')
    
    // 查找买家用户
    const buyerUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!buyerUser) {
      console.log('❌ 未找到买家用户，请先运行 create-fresh-test-data.js')
      return
    }
    
    console.log('✅ 找到买家用户:', buyerUser.email)
    console.log('💰 当前余额:', buyerUser.depositBalance, 'USDT')
    
    // 查找可用的礼品卡商品
    const giftCardProducts = await prisma.giftCardProduct.findMany({
      where: { isActive: true },
      orderBy: { salePrice: 'asc' }
    })
    
    if (giftCardProducts.length === 0) {
      console.log('❌ 没有可用的礼品卡商品，请先运行 create-test-giftcard-products.js')
      return
    }
    
    console.log(`✅ 找到 ${giftCardProducts.length} 个可用的礼品卡商品`)
    
    // 选择最便宜的商品进行测试
    const testProduct = giftCardProducts[0]
    console.log(`🎯 选择测试商品: ${testProduct.name} (面值: ${testProduct.faceValue} USDT, 售价: ${testProduct.salePrice} USDT)`)
    
    // 检查用户余额是否足够
    if (buyerUser.depositBalance < testProduct.salePrice) {
      console.log('💰 余额不足，先充值...')
      await prisma.user.update({
        where: { id: buyerUser.id },
        data: {
          depositBalance: {
            increment: 100
          }
        }
      })
      console.log('✅ 已充值 100 USDT')
    }
    
    // 创建一些测试礼品卡
    console.log('🎁 创建测试礼品卡...')
    const testGiftCards = []
    for (let i = 0; i < 5; i++) {
      const giftCard = await prisma.giftCard.create({
        data: {
          cardCode: `TEST${Date.now()}${i}`,
          faceValue: testProduct.faceValue,
          status: 'GENERATED',
          validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年有效期
          createdById: buyerUser.id,
          productId: testProduct.id
        }
      })
      testGiftCards.push(giftCard)
    }
    console.log(`✅ 创建了 ${testGiftCards.length} 张测试礼品卡`)
    
    console.log('\n📊 测试结果摘要:')
    console.log('- 买家用户: <EMAIL>')
    console.log(`- 当前余额: ${(await prisma.user.findUnique({ where: { id: buyerUser.id } })).depositBalance} USDT`)
    console.log(`- 测试商品: ${testProduct.name}`)
    console.log(`- 商品价格: ${testProduct.salePrice} USDT`)
    console.log(`- 可用礼品卡: ${testGiftCards.length} 张`)
    
    console.log('\n🔗 测试步骤:')
    console.log('1. 登录买家账号: <EMAIL> / buyer123')
    console.log('2. 访问充值页面: http://localhost:3001/deposit')
    console.log('3. 点击"礼品卡"标签页')
    console.log('4. 选择商品并测试不同的发货方式:')
    console.log('   - 兑换码: 生成可分享的兑换码')
    console.log('   - 礼品卡: 生成礼品卡号')
    console.log('   - 直充账号: 直接充值到账户余额')
    
    console.log('\n👨‍💼 管理员测试:')
    console.log('1. 登录管理员账号: <EMAIL> / admin123')
    console.log('2. 访问管理员页面: http://localhost:3001/admin')
    console.log('3. 查看以下管理功能:')
    console.log('   - 礼品卡商品管理: /admin/giftcard-products')
    console.log('   - 礼品卡管理: /admin/giftcards')
    console.log('   - 兑换码管理: /admin/redemption-codes')
    
    console.log('\n💡 功能说明:')
    console.log('- 兑换码发货: 生成兑换码记录，存储在 redemption_codes 表')
    console.log('- 礼品卡发货: 更新礼品卡状态为 ISSUED，生成卡号')
    console.log('- 直充账号: 直接增加用户余额，礼品卡状态为 REDEEMED')
    console.log('- 管理员可在后台查看所有生成的兑换码和礼品卡')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testGiftCardPurchase()
