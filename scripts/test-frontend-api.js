const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// 模拟前端API调用逻辑
async function simulateFrontendAPI() {
  try {
    const userId = 'cmd8desog0002v9rwq6iekv1c';
    const activeTab = 'INACTIVE'; // 测试"已下架"标签页
    
    console.log('🔍 模拟前端API调用');
    console.log('='.repeat(50));
    console.log(`用户ID: ${userId}`);
    console.log(`活动标签: ${activeTab}`);
    
    // 模拟API路由中的查询逻辑
    const where = {
      reviewStatus: 'APPROVED',
      sellerId: userId
    };
    
    // 状态筛选逻辑（来自API路由）
    if (activeTab === 'INACTIVE') {
      // INACTIVE状态包括SOLD_OUT和INACTIVE
      where.status = {
        in: ['INACTIVE', 'SOLD_OUT']
      };
    } else {
      where.status = activeTab;
    }
    
    console.log('\n📊 查询条件:');
    console.log(JSON.stringify(where, null, 2));
    
    // 执行查询
    const products = await prisma.product.findMany({
      where,
      select: {
        id: true,
        title: true,
        description: true,
        images: true,
        price: true,
        category: true,
        condition: true,
        city: true,
        district: true,
        status: true,
        createdAt: true,
        stock: true,
        seller: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`\n📋 查询结果: 找到 ${products.length} 个商品`);
    
    if (products.length > 0) {
      products.forEach((product, index) => {
        console.log(`\n${index + 1}. ${product.title}`);
        console.log(`   ID: ${product.id}`);
        console.log(`   状态: ${product.status}`);
        console.log(`   库存: ${product.stock}`);
        console.log(`   价格: ${product.price} USDT`);
        console.log(`   创建时间: ${product.createdAt}`);
        console.log(`   卖家: ${product.seller.name} (信用: ${product.seller.creditScore})`);
      });
    } else {
      console.log('   没有找到商品');
    }
    
    // 测试所有标签页
    console.log('\n🔄 测试所有标签页:');
    const tabs = ['AVAILABLE', 'SOLD', 'INACTIVE'];
    
    for (const tab of tabs) {
      const tabWhere = {
        reviewStatus: 'APPROVED',
        sellerId: userId
      };
      
      if (tab === 'INACTIVE') {
        tabWhere.status = {
          in: ['INACTIVE', 'SOLD_OUT']
        };
      } else {
        tabWhere.status = tab;
      }
      
      const tabProducts = await prisma.product.findMany({
        where: tabWhere,
        select: {
          id: true,
          title: true,
          status: true,
          stock: true
        }
      });
      
      console.log(`\n📊 ${tab} 标签页: ${tabProducts.length} 个商品`);
      tabProducts.forEach(p => {
        console.log(`   - ${p.title} (状态: ${p.status}, 库存: ${p.stock})`);
      });
    }
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ 错误:', error);
    await prisma.$disconnect();
  }
}

simulateFrontendAPI();
