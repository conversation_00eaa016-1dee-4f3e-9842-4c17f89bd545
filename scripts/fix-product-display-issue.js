#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixProductDisplayIssue() {
  try {
    console.log('🔧 修复商品显示问题');
    console.log('='.repeat(50));
    
    const userId = 'cmd8desog0002v9rwq6iekv1c';
    
    // 1. 确认问题
    console.log('\n🔍 确认问题:');
    const soldOutProducts = await prisma.product.findMany({
      where: {
        sellerId: userId,
        status: 'SOLD_OUT',
        reviewStatus: 'APPROVED'
      },
      select: {
        id: true,
        title: true,
        status: true,
        stock: true,
        reviewStatus: true
      }
    });
    
    console.log(`找到 ${soldOutProducts.length} 个SOLD_OUT状态的商品:`);
    soldOutProducts.forEach(p => {
      console.log(`  - ${p.title} (状态: ${p.status}, 库存: ${p.stock})`);
    });
    
    // 2. 验证API查询逻辑
    console.log('\n✅ 验证API查询逻辑:');
    const inactiveQuery = await prisma.product.findMany({
      where: {
        sellerId: userId,
        reviewStatus: 'APPROVED',
        status: {
          in: ['INACTIVE', 'SOLD_OUT']
        }
      },
      select: {
        id: true,
        title: true,
        status: true
      }
    });
    
    console.log(`INACTIVE标签页查询结果: ${inactiveQuery.length} 个商品`);
    inactiveQuery.forEach(p => {
      console.log(`  - ${p.title} (状态: ${p.status})`);
    });
    
    // 3. 生成解决方案
    console.log('\n💡 解决方案:');
    console.log('问题确认: 0库存商品状态为SOLD_OUT，应该显示在"已下架"标签页');
    console.log('根本原因: 前端缓存导致商品没有显示');
    
    console.log('\n🔧 立即解决方法:');
    console.log('1. 访问以下URL清除缓存:');
    console.log(`   http://localhost:3000/products/user/${userId}?nocache=1`);
    console.log('2. 或者刷新浏览器页面 (Ctrl+F5 强制刷新)');
    console.log('3. 或者清除浏览器缓存');
    
    // 4. 创建测试URL
    const testUrls = [
      `http://localhost:3000/api/products?sellerId=${userId}&status=INACTIVE&nocache=1`,
      `http://localhost:3000/api/products?sellerId=${userId}&status=SOLD_OUT&nocache=1`,
      `http://localhost:3000/products/user/${userId}?nocache=1`
    ];
    
    console.log('\n🧪 测试URL (带nocache参数):');
    testUrls.forEach((url, index) => {
      console.log(`${index + 1}. ${url}`);
    });
    
    // 5. 检查是否需要状态修复
    console.log('\n🔍 检查是否需要状态修复:');
    const needsStatusFix = await prisma.product.findMany({
      where: {
        sellerId: userId,
        stock: { lte: 0 },
        status: 'AVAILABLE'
      }
    });
    
    if (needsStatusFix.length > 0) {
      console.log(`⚠️  发现 ${needsStatusFix.length} 个0库存但仍在售的商品，正在修复...`);
      const fixResult = await prisma.product.updateMany({
        where: {
          id: { in: needsStatusFix.map(p => p.id) }
        },
        data: { status: 'SOLD_OUT' }
      });
      console.log(`✅ 已修复 ${fixResult.count} 个商品的状态`);
    } else {
      console.log('✅ 所有商品状态正确，无需修复');
    }
    
    // 6. 最终状态报告
    console.log('\n📊 最终状态报告:');
    const finalStats = await prisma.product.groupBy({
      by: ['status'],
      where: { sellerId: userId },
      _count: { id: true }
    });
    
    console.log('用户商品状态分布:');
    finalStats.forEach(s => {
      const statusText = {
        'AVAILABLE': '在售',
        'SOLD': '已售出', 
        'INACTIVE': '已下架',
        'SOLD_OUT': '已下架 (库存不足)',
        'DRAFT': '草稿'
      }[s.status] || s.status;
      
      console.log(`  ${statusText}: ${s._count.id} 个`);
    });
    
    console.log('\n🎯 预期结果:');
    console.log('- "在售" 标签页: 0 个商品');
    console.log('- "已售出" 标签页: 0 个商品'); 
    console.log('- "已下架" 标签页: 1 个商品 (iPhone 15 Pro)');
    
    console.log('\n✅ 修复完成！');
    console.log('如果前端仍然没有显示，请:');
    console.log('1. 使用上面提供的nocache URL');
    console.log('2. 等待1分钟让缓存自动过期');
    console.log('3. 强制刷新浏览器页面');
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ 修复失败:', error);
    await prisma.$disconnect();
  }
}

fixProductDisplayIssue();
