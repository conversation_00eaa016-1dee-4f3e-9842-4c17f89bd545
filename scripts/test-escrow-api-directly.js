const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testEscrowApiDirectly() {
  try {
    console.log('=== 直接测试托管订单API ===\n');
    
    // 1. 获取最新的待支付订单
    console.log('1. 获取最新的待支付订单...');
    const latestOrder = await prisma.order.findFirst({
      where: {
        productId: 'cmdr1z4vv00078oqqm2k4gq7v',
        status: 'PENDING_PAYMENT'
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        buyer: true
      }
    });
    
    if (!latestOrder) {
      console.log('❌ 没有找到待支付的订单');
      return;
    }
    
    console.log(`找到订单: ${latestOrder.id}`);
    console.log(`金额: ${latestOrder.totalAmount} USDT`);
    console.log(`买家: ${latestOrder.buyer.name}`);
    
    // 2. 获取活跃中间人
    console.log('\n2. 获取活跃中间人...');
    const mediator = await prisma.user.findFirst({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      }
    });
    
    if (!mediator) {
      console.log('❌ 没有找到活跃中间人');
      return;
    }
    
    console.log(`找到中间人: ${mediator.name} (ID: ${mediator.id})`);
    
    // 3. 测试自动分配中间人API
    console.log('\n3. 测试自动分配中间人API...');
    try {
      const autoAssignResponse = await axios.post('http://localhost:3000/api/mediator/auto-assign', {
        orderAmount: latestOrder.totalAmount
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      if (autoAssignResponse.data.success) {
        console.log('✅ 自动分配中间人成功');
        console.log(`分配的中间人: ${autoAssignResponse.data.data.mediator.name}`);
        console.log(`托管费用: ${autoAssignResponse.data.data.escrowFee} USDT`);
      } else {
        console.log('❌ 自动分配中间人失败');
        console.log(`错误: ${autoAssignResponse.data.error}`);
      }
    } catch (error) {
      console.log('❌ 自动分配中间人API请求失败');
      if (error.response) {
        console.log(`状态码: ${error.response.status}`);
        console.log(`错误信息: ${error.response.data.error || JSON.stringify(error.response.data)}`);
      } else {
        console.log(`错误: ${error.message}`);
      }
    }
    
    // 4. 测试创建托管订单API
    console.log('\n4. 测试创建托管订单API...');
    try {
      const escrowResponse = await axios.post('http://localhost:3000/api/escrow/create', {
        orderId: latestOrder.id,
        mediatorId: mediator.id,
        escrowAmount: latestOrder.totalAmount,
        escrowFee: latestOrder.totalAmount * 0.025 // 2.5% 费率
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      if (escrowResponse.data.success) {
        console.log('✅ 创建托管订单成功');
        console.log(`托管订单ID: ${escrowResponse.data.data.escrowId}`);
        console.log(`聊天室代码: ${escrowResponse.data.data.chatRoomCode}`);
      } else {
        console.log('❌ 创建托管订单失败');
        console.log(`错误: ${escrowResponse.data.error}`);
      }
    } catch (error) {
      console.log('❌ 创建托管订单API请求失败');
      if (error.response) {
        console.log(`状态码: ${error.response.status}`);
        console.log(`错误信息: ${error.response.data.error || JSON.stringify(error.response.data)}`);
        
        // 详细分析401错误
        if (error.response.status === 401) {
          console.log('\n🔍 401错误分析:');
          console.log('这是认证错误，可能的原因:');
          console.log('1. 用户未登录');
          console.log('2. Session已过期');
          console.log('3. Cookie未正确发送');
          console.log('4. 服务端session配置问题');
        }
        
        // 详细分析403错误
        if (error.response.status === 403) {
          console.log('\n🔍 403错误分析:');
          console.log('这是权限错误，可能的原因:');
          console.log('1. 不是订单的买家');
          console.log('2. 用户权限不足');
          console.log('3. 订单状态不允许创建托管');
        }
        
        // 详细分析400错误
        if (error.response.status === 400) {
          console.log('\n🔍 400错误分析:');
          console.log('这是参数错误，可能的原因:');
          console.log('1. 缺少必要参数');
          console.log('2. 订单金额不满足要求');
          console.log('3. 中间人不可用');
          console.log('4. 订单状态不正确');
        }
      } else {
        console.log(`错误: ${error.message}`);
      }
    }
    
    // 5. 检查现有的托管订单
    console.log('\n5. 检查现有的托管订单...');
    const existingEscrow = await prisma.escrowOrder.findFirst({
      where: {
        orderId: latestOrder.id
      }
    });
    
    if (existingEscrow) {
      console.log('✅ 找到现有的托管订单');
      console.log(`托管订单ID: ${existingEscrow.id}`);
      console.log(`状态: ${existingEscrow.status}`);
    } else {
      console.log('❌ 没有找到托管订单');
    }
    
    // 6. 生成解决方案
    console.log('\n=== 解决方案 ===');
    console.log('基于测试结果，建议:');
    console.log('');
    console.log('1. 如果是401认证错误:');
    console.log('   - 确保用户已正确登录');
    console.log('   - 检查浏览器是否阻止了cookies');
    console.log('   - 尝试清除浏览器缓存和cookies后重新登录');
    console.log('');
    console.log('2. 如果是403权限错误:');
    console.log('   - 确保使用买家账号操作');
    console.log('   - 检查订单的买家ID是否正确');
    console.log('');
    console.log('3. 如果是400参数错误:');
    console.log('   - 检查传递的参数是否正确');
    console.log('   - 确保中间人ID有效');
    console.log('   - 确保订单状态为PENDING_PAYMENT');
    console.log('');
    console.log('4. 前端调试:');
    console.log('   - 在浏览器中打开开发者工具');
    console.log('   - 查看Network标签页的API请求');
    console.log('   - 检查请求头是否包含正确的cookies');
    console.log('   - 查看响应的详细错误信息');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEscrowApiDirectly();
