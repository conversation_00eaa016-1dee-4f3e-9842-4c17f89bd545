const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testEscrowFix() {
  try {
    console.log('=== 测试中间人订单修复效果 ===\n');
    
    const productId = 'cmdr1z4vv00078oqqm2k4gq7v';
    
    // 1. 验证商品信息
    console.log('1. 验证商品信息...');
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    if (!product) {
      console.log('❌ 商品不存在');
      return;
    }
    
    console.log(`商品: ${product.title}`);
    console.log(`价格: ${product.price} USDT`);
    console.log(`库存: ${product.stock}`);
    console.log(`卖家: ${product.seller.name}`);
    
    // 2. 验证中间人条件
    console.log('\n2. 验证中间人订单条件...');
    const meetsMinAmount = product.price >= 100;
    console.log(`价格满足最低要求 (>= 100 USDT): ${meetsMinAmount ? '✅' : '❌'}`);
    
    if (!meetsMinAmount) {
      console.log('❌ 商品价格不满足中间人订单要求');
      return;
    }
    
    // 3. 检查活跃中间人
    console.log('\n3. 检查活跃中间人...');
    const activeMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        availableBalance: true
      }
    });
    
    console.log(`活跃中间人数量: ${activeMediators.length}`);
    
    if (activeMediators.length === 0) {
      console.log('❌ 没有活跃的中间人');
      return;
    }
    
    activeMediators.forEach((mediator, index) => {
      console.log(`  ${index + 1}. ${mediator.name} - 可用余额: ${mediator.availableBalance} USDT`);
    });
    
    // 4. 检查符合条件的中间人
    console.log('\n4. 检查符合条件的中间人...');
    const qualifiedMediators = activeMediators.filter(m => m.availableBalance >= product.price);
    console.log(`符合条件的中间人数量: ${qualifiedMediators.length}`);
    
    if (qualifiedMediators.length === 0) {
      console.log('❌ 没有保证金充足的中间人');
      console.log('   需要的金额:', product.price, 'USDT');
      activeMediators.forEach(m => {
        console.log(`   ${m.name}: ${m.availableBalance} USDT (不足 ${product.price - m.availableBalance} USDT)`);
      });
      return;
    }
    
    // 5. 生成测试指南
    console.log('\n=== 修复验证指南 ===');
    console.log('请按以下步骤测试修复效果:');
    console.log('');
    console.log('1. 打开商品页面:');
    console.log(`   http://localhost:3000/products/${productId}`);
    console.log('');
    console.log('2. 确保已登录用户账号 (不是商品所有者)');
    console.log(`   商品所有者: ${product.seller.name} (ID: ${product.seller.id})`);
    console.log('');
    console.log('3. 在购买区域勾选"使用中间人托管服务"');
    console.log('   ✅ 应该能看到托管服务选项 (价格 >= 100 USDT)');
    console.log('   ✅ 应该能自动分配中间人');
    console.log('   ✅ 应该显示托管费用和总费用');
    console.log('');
    console.log('4. 点击"立即购买"按钮');
    console.log('   ✅ 应该能成功创建普通订单');
    console.log('   ✅ 应该能成功创建托管订单');
    console.log('   ✅ 应该跳转到托管订单详情页面');
    console.log('');
    console.log('5. 检查浏览器控制台');
    console.log('   ✅ 不应该有401未登录错误');
    console.log('   ✅ 不应该有其他API错误');
    
    // 6. 检查修复的代码
    console.log('\n=== 代码修复验证 ===');
    console.log('已修复的问题:');
    console.log('1. ✅ 自动分配中间人API调用添加了 credentials: "include"');
    console.log('2. ✅ 创建托管订单API调用添加了 credentials: "include"');
    console.log('3. ✅ 创建普通订单API调用添加了 credentials: "include"');
    console.log('4. ✅ 用户状态检查API调用添加了 credentials: "include"');
    console.log('');
    console.log('修复原理:');
    console.log('- Next.js客户端fetch请求默认不包含cookies');
    console.log('- 添加 credentials: "include" 确保session cookie被发送');
    console.log('- 这样服务端就能正确识别用户身份');
    
    // 7. 常见问题排查
    console.log('\n=== 常见问题排查 ===');
    console.log('如果仍然无法创建中间人订单，请检查:');
    console.log('');
    console.log('1. 用户登录状态:');
    console.log('   - 确保用户已正确登录');
    console.log('   - 检查session是否有效');
    console.log('');
    console.log('2. 商品条件:');
    console.log('   - 商品价格 >= 100 USDT');
    console.log('   - 商品有库存且状态为AVAILABLE');
    console.log('');
    console.log('3. 中间人条件:');
    console.log('   - 至少有一个活跃的中间人');
    console.log('   - 中间人有足够的保证金');
    console.log('');
    console.log('4. 网络和服务:');
    console.log('   - 应用服务正在运行');
    console.log('   - 数据库连接正常');
    console.log('   - Redis连接正常 (如果使用)');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEscrowFix();
