const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function resetUserPassword() {
  console.log('🔧 重置用户密码...\n')

  try {
    const email = '<EMAIL>'
    const newPassword = '123456'
    
    // 1. 查找用户
    console.log(`1. 查找用户: ${email}`)
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true
      }
    })

    if (!user) {
      console.log('❌ 用户不存在')
      return
    }

    console.log('✅ 找到用户:')
    console.log(`   ID: ${user.id}`)
    console.log(`   姓名: ${user.name || '未设置'}`)
    console.log(`   邮箱: ${user.email}`)
    console.log(`   角色: ${user.role}`)
    console.log(`   状态: ${user.status}`)
    console.log(`   创建时间: ${user.createdAt.toLocaleDateString('zh-CN')}`)

    // 2. 生成新密码哈希
    console.log('\n2. 生成新密码哈希...')
    const saltRounds = 12
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds)
    console.log('✅ 密码哈希生成完成')

    // 3. 更新用户密码
    console.log('\n3. 更新用户密码...')
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        updatedAt: new Date()
      },
      select: {
        id: true,
        email: true,
        updatedAt: true
      }
    })

    console.log('✅ 密码更新成功')
    console.log(`   用户ID: ${updatedUser.id}`)
    console.log(`   邮箱: ${updatedUser.email}`)
    console.log(`   更新时间: ${updatedUser.updatedAt.toLocaleString('zh-CN')}`)

    // 4. 验证新密码
    console.log('\n4. 验证新密码...')
    const isValid = await bcrypt.compare(newPassword, hashedPassword)
    console.log(`✅ 密码验证: ${isValid ? '成功' : '失败'}`)

    // 5. 检查用户账户状态
    console.log('\n5. 检查用户账户状态...')
    const finalUser = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        isMediator: true,
        mediatorStatus: true,
        depositBalance: true,
        creditScore: true
      }
    })

    console.log('✅ 用户账户信息:')
    console.log(`   姓名: ${finalUser.name || '未设置'}`)
    console.log(`   邮箱: ${finalUser.email}`)
    console.log(`   角色: ${finalUser.role}`)
    console.log(`   状态: ${finalUser.status}`)
    console.log(`   中间人: ${finalUser.isMediator ? '是' : '否'}`)
    if (finalUser.isMediator) {
      console.log(`   中间人状态: ${finalUser.mediatorStatus}`)
    }
    console.log(`   保证金余额: ${finalUser.depositBalance || 0} USDT`)
    console.log(`   信用分: ${finalUser.creditScore || 0}/100`)

    console.log('\n🎉 密码重置完成！')
    console.log('\n📝 登录信息:')
    console.log(`   邮箱: ${email}`)
    console.log(`   密码: ${newPassword}`)
    console.log(`   登录地址: http://localhost:3000/auth/signin`)

    console.log('\n💡 使用说明:')
    console.log('   1. 访问登录页面')
    console.log('   2. 输入上述邮箱和密码')
    console.log('   3. 点击登录按钮')
    console.log('   4. 登录成功后可以访问相应功能')

    if (finalUser.role === 'ADMIN') {
      console.log('\n👨‍💼 管理员功能:')
      console.log('   - 用户管理: http://localhost:3000/admin/users')
      console.log('   - 中间人管理: http://localhost:3000/admin/mediators')
      console.log('   - 系统管理: http://localhost:3000/admin')
    }

    if (finalUser.isMediator) {
      console.log('\n🛡️ 中间人功能:')
      console.log('   - 中间人控制台: http://localhost:3000/mediator/dashboard')
      console.log('   - 个人资料: http://localhost:3000/profile')
    }

    console.log('\n🔒 安全提醒:')
    console.log('   - 请在首次登录后修改密码')
    console.log('   - 不要在生产环境使用简单密码')
    console.log('   - 定期更新密码以确保账户安全')

  } catch (error) {
    console.error('❌ 密码重置失败:', error)
    
    if (error.code === 'P2025') {
      console.log('💡 可能的原因:')
      console.log('   - 用户不存在')
      console.log('   - 邮箱地址输入错误')
    } else if (error.code === 'P2002') {
      console.log('💡 可能的原因:')
      console.log('   - 数据库约束冲突')
      console.log('   - 邮箱已被其他用户使用')
    } else {
      console.log('💡 请检查:')
      console.log('   - 数据库连接是否正常')
      console.log('   - bcrypt 模块是否正确安装')
      console.log('   - 用户表结构是否正确')
    }
  } finally {
    await prisma.$disconnect()
  }
}

// 运行密码重置
if (require.main === module) {
  resetUserPassword().catch(console.error)
}

module.exports = { resetUserPassword }
