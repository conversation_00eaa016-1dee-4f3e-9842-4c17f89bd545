const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    console.log('👥 检查用户账号...\n')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        emailVerified: true,
        password: true
      },
      orderBy: { createdAt: 'desc' }
    })
    
    console.log(`找到 ${users.length} 个用户账号:\n`)
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. 📧 邮箱: ${user.email}`)
      console.log(`   👤 姓名: ${user.name}`)
      console.log(`   🔑 角色: ${user.role}`)
      console.log(`   📊 状态: ${user.status}`)
      console.log(`   ✉️ 邮箱验证: ${user.emailVerified ? '已验证' : '未验证'}`)
      console.log(`   🔒 密码: ${user.password ? '已设置' : '未设置'}`)
      console.log('')
    })
    
    return users
    
  } catch (error) {
    console.error('❌ 检查用户失败:', error)
    throw error
  }
}

async function fixUserPasswords() {
  try {
    console.log('🔧 修复用户密码...\n')
    
    // 标准密码哈希 (password)
    const passwordHash = await bcrypt.hash('password', 10)
    const adminPasswordHash = await bcrypt.hash('admin123456', 10)
    
    // 更新测试用户密码
    const testUsers = [
      { email: '<EMAIL>', password: passwordHash, name: '测试买家' },
      { email: '<EMAIL>', password: passwordHash, name: '测试卖家' },
      { email: '<EMAIL>', password: passwordHash, name: '测试中间人' },
      { email: '<EMAIL>', password: passwordHash, name: '测试中间人' },
      { email: '<EMAIL>', password: passwordHash, name: '测试买家' },
      { email: '<EMAIL>', password: passwordHash, name: '测试卖家' },
      { email: '<EMAIL>', password: adminPasswordHash, name: '系统管理员' }
    ]
    
    for (const userData of testUsers) {
      try {
        const user = await prisma.user.findUnique({
          where: { email: userData.email }
        })
        
        if (user) {
          await prisma.user.update({
            where: { email: userData.email },
            data: {
              password: userData.password,
              emailVerified: new Date(),
              status: 'ACTIVE'
            }
          })
          console.log(`✅ 更新用户: ${userData.email}`)
        } else {
          console.log(`⚠️ 用户不存在: ${userData.email}`)
        }
      } catch (error) {
        console.log(`❌ 更新用户失败 ${userData.email}:`, error.message)
      }
    }
    
    console.log('\n🎉 密码修复完成!')
    
  } catch (error) {
    console.error('❌ 修复密码失败:', error)
    throw error
  }
}

async function createMissingUsers() {
  try {
    console.log('👥 创建缺失的用户...\n')
    
    const passwordHash = await bcrypt.hash('password', 10)
    const adminPasswordHash = await bcrypt.hash('admin123456', 10)
    
    const requiredUsers = [
      {
        email: '<EMAIL>',
        password: adminPasswordHash,
        name: '系统管理员',
        role: 'ADMIN',
        depositBalance: 10000,
        creditScore: 100
      },
      {
        email: '<EMAIL>',
        password: passwordHash,
        name: '测试买家',
        role: 'USER',
        depositBalance: 5000,
        creditScore: 85
      },
      {
        email: '<EMAIL>',
        password: passwordHash,
        name: '测试卖家',
        role: 'USER',
        depositBalance: 2000,
        creditScore: 90
      },
      {
        email: '<EMAIL>',
        password: passwordHash,
        name: '测试中间人',
        role: 'USER',
        depositBalance: 10000,
        creditScore: 95,
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        mediatorFeeRate: 0.05,
        mediatorDeposit: 5000,
        mediatorReputation: 4.8,
        bnbWalletAddress: '******************************************'
      }
    ]
    
    for (const userData of requiredUsers) {
      try {
        const existingUser = await prisma.user.findUnique({
          where: { email: userData.email }
        })
        
        if (!existingUser) {
          await prisma.user.create({
            data: {
              ...userData,
              emailVerified: new Date(),
              status: 'ACTIVE'
            }
          })
          console.log(`✅ 创建用户: ${userData.email}`)
        } else {
          console.log(`⚠️ 用户已存在: ${userData.email}`)
        }
      } catch (error) {
        console.log(`❌ 创建用户失败 ${userData.email}:`, error.message)
      }
    }
    
    console.log('\n🎉 用户创建完成!')
    
  } catch (error) {
    console.error('❌ 创建用户失败:', error)
    throw error
  }
}

async function testLogin() {
  try {
    console.log('🧪 测试登录功能...\n')
    
    const testCredentials = [
      { email: '<EMAIL>', password: 'admin123456' },
      { email: '<EMAIL>', password: 'password' },
      { email: '<EMAIL>', password: 'password' },
      { email: '<EMAIL>', password: 'password' }
    ]
    
    for (const cred of testCredentials) {
      try {
        const user = await prisma.user.findUnique({
          where: { email: cred.email },
          select: {
            id: true,
            email: true,
            name: true,
            password: true,
            status: true,
            emailVerified: true
          }
        })
        
        if (!user) {
          console.log(`❌ 用户不存在: ${cred.email}`)
          continue
        }
        
        if (!user.password) {
          console.log(`❌ 用户无密码: ${cred.email}`)
          continue
        }
        
        const isValidPassword = await bcrypt.compare(cred.password, user.password)
        
        if (isValidPassword) {
          console.log(`✅ 登录测试成功: ${cred.email}`)
          console.log(`   状态: ${user.status}`)
          console.log(`   邮箱验证: ${user.emailVerified ? '已验证' : '未验证'}`)
        } else {
          console.log(`❌ 密码错误: ${cred.email}`)
        }
        
      } catch (error) {
        console.log(`❌ 测试登录失败 ${cred.email}:`, error.message)
      }
    }
    
    console.log('\n🎉 登录测试完成!')
    
  } catch (error) {
    console.error('❌ 测试登录失败:', error)
    throw error
  }
}

async function main() {
  try {
    const action = process.argv[2]
    
    switch (action) {
      case 'check':
        await checkUsers()
        break
        
      case 'fix':
        await fixUserPasswords()
        break
        
      case 'create':
        await createMissingUsers()
        break
        
      case 'test':
        await testLogin()
        break
        
      case 'all':
        await createMissingUsers()
        await fixUserPasswords()
        await testLogin()
        break
        
      default:
        console.log('🔧 用户登录修复工具')
        console.log('')
        console.log('用法:')
        console.log('  node scripts/fix-user-login.js check   - 检查现有用户')
        console.log('  node scripts/fix-user-login.js create  - 创建缺失用户')
        console.log('  node scripts/fix-user-login.js fix     - 修复用户密码')
        console.log('  node scripts/fix-user-login.js test    - 测试登录功能')
        console.log('  node scripts/fix-user-login.js all     - 执行所有操作')
        console.log('')
        console.log('修复后的登录信息:')
        console.log('  管理员: <EMAIL> / admin123456')
        console.log('  买家: <EMAIL> / password')
        console.log('  卖家: <EMAIL> / password')
        console.log('  中间人: <EMAIL> / password')
        break
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  checkUsers,
  fixUserPasswords,
  createMissingUsers,
  testLogin
}
