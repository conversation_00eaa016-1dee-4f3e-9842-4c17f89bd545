const fs = require('fs');
const path = require('path');

// 检查支付相关文件中是否还有¥符号
const paymentFiles = [
  'app/order/[id]/payment/balance-pay/page.tsx',
  'app/order/[id]/payment/bsc-pay/page.tsx',
  'app/order/[id]/payment/binancepay-QRcode/page.tsx',
  'app/order/[id]/payment/successful/page.tsx',
  'app/order/[id]/info-collect/page.tsx'
];

console.log('=== 检查支付页面中的货币符号 ===\n');

paymentFiles.forEach(filePath => {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');
      
      console.log(`检查文件: ${filePath}`);
      
      // 检查¥符号
      const yenLines = [];
      lines.forEach((line, index) => {
        if (line.includes('¥')) {
          yenLines.push({ lineNumber: index + 1, content: line.trim() });
        }
      });
      
      if (yenLines.length > 0) {
        console.log(`❌ 发现 ${yenLines.length} 处¥符号:`);
        yenLines.forEach(({ lineNumber, content }) => {
          console.log(`   第${lineNumber}行: ${content}`);
        });
      } else {
        console.log('✅ 未发现¥符号');
      }
      
      // 检查formatUSDT导入
      const hasFormatUSDTImport = content.includes("import { formatUSDT } from '@/lib/utils'");
      const usesFormatUSDT = content.includes('formatUSDT(');
      
      if (usesFormatUSDT && !hasFormatUSDTImport) {
        console.log('⚠️  使用了formatUSDT但未导入');
      } else if (usesFormatUSDT && hasFormatUSDTImport) {
        console.log('✅ 正确导入并使用formatUSDT');
      }
      
      console.log('');
    } else {
      console.log(`⚠️  文件不存在: ${filePath}\n`);
    }
  } catch (error) {
    console.log(`❌ 检查文件失败: ${filePath} - ${error.message}\n`);
  }
});

console.log('=== 检查完成 ===');
