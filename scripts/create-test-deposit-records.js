const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestDepositRecords() {
  try {
    console.log('🔍 查找测试用户...')
    
    // 查找买家用户
    const buyerUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!buyerUser) {
      console.log('❌ 未找到买家用户，请先运行 create-fresh-test-data.js')
      return
    }
    
    console.log('✅ 找到买家用户:', buyerUser.email)
    
    console.log('💰 创建测试充值记录...')
    
    // 创建不同状态的充值记录
    const testRecords = [
      {
        userId: buyerUser.id,
        amount: 100.00,
        originalAmount: 100.00,
        method: 'chain',
        status: 'COMPLETED',
        txHash: '******************************************',
        notes: '测试充值 - 已完成',
        metadata: {
          userAgent: 'Test Browser',
          ip: '127.0.0.1',
          paymentPin: 'ABC12345',
          paymentPinExpiry: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          walletAddress: 'TYQEuRHZxZvKJT8PnM8aCdNKhgeVLSbPAU',
          pinUsed: true,
          pinUsedAt: new Date().toISOString(),
          verifiedTxHash: '******************************************'
        },
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },
      {
        userId: buyerUser.id,
        amount: 50.00,
        originalAmount: 50.00,
        method: 'binance_qr',
        status: 'PENDING_APPROVAL',
        txHash: 'BN2024012612345678',
        notes: '测试充值 - 等待确认',
        metadata: {
          userAgent: 'Test Browser',
          ip: '127.0.0.1',
          paymentPin: 'XYZ67890',
          paymentPinExpiry: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          paymentUrl: '/binance-qr-code.png',
          pinUsed: true,
          pinUsedAt: new Date().toISOString(),
          verifiedTxHash: 'BN2024012612345678',
          submittedForApproval: true,
          submittedAt: new Date().toISOString()
        },
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1小时前
        updatedAt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
      },
      {
        userId: buyerUser.id,
        amount: 200.00,
        originalAmount: 200.00,
        method: 'chain',
        status: 'PENDING',
        notes: '测试充值 - 待验证PIN码',
        metadata: {
          userAgent: 'Test Browser',
          ip: '127.0.0.1',
          paymentPin: 'DEF45678',
          paymentPinExpiry: new Date(Date.now() + 25 * 60 * 1000).toISOString(),
          walletAddress: 'TYQEuRHZxZvKJT8PnM8aCdNKhgeVLSbPAU'
        },
        createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
        updatedAt: new Date(Date.now() - 5 * 60 * 1000)
      },
      {
        userId: buyerUser.id,
        amount: 75.00,
        originalAmount: 75.00,
        method: 'binance_qr',
        status: 'REJECTED',
        txHash: 'BN2024012600000000',
        notes: '测试充值 - 已拒绝',
        metadata: {
          userAgent: 'Test Browser',
          ip: '127.0.0.1',
          paymentPin: 'GHI90123',
          paymentPinExpiry: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          paymentUrl: '/binance-qr-code.png',
          pinUsed: true,
          pinUsedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          verifiedTxHash: 'BN2024012600000000',
          rejectedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          rejectionReason: '无效的支付凭证'
        },
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3天前
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      }
    ]
    
    // 创建充值记录
    for (const record of testRecords) {
      const depositRecord = await prisma.depositRecord.create({
        data: record
      })
      console.log(`✅ 创建充值记录: ${record.amount} USDT (${record.status})`)
    }
    
    console.log('✅ 测试充值记录创建完成!')
    console.log('\n📋 充值记录摘要:')
    console.log('- 已完成: 100 USDT (链上转账)')
    console.log('- 等待确认: 50 USDT (币安支付)')
    console.log('- 待验证: 200 USDT (链上转账)')
    console.log('- 已拒绝: 75 USDT (币安支付)')
    
    console.log('\n🔗 测试链接:')
    console.log('1. 登录买家账号: <EMAIL> / buyer123')
    console.log('2. 访问充值页面: http://localhost:3001/deposit')
    console.log('3. 点击"历史记录"标签页查看充值记录')
    
  } catch (error) {
    console.error('❌ 创建测试充值记录失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
createTestDepositRecords()
