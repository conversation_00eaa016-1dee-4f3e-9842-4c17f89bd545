const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkProductImages() {
  try {
    const productId = 'cmd8dcwa60001v9rwn3qtmzqg'
    
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        title: true,
        images: true,
        description: true
      }
    })
    
    if (!product) {
      console.log('❌ 商品不存在')
      return
    }
    
    console.log('📋 商品信息:')
    console.log(`   ID: ${product.id}`)
    console.log(`   标题: ${product.title}`)
    console.log(`   描述: ${product.description?.substring(0, 100)}...`)
    
    console.log('\n🖼️ 图片字段分析:')
    console.log(`   类型: ${typeof product.images}`)
    console.log(`   内容: ${product.images}`)
    
    if (product.images) {
      if (typeof product.images === 'string') {
        try {
          // 尝试解析为JSON
          const parsed = JSON.parse(product.images)
          console.log('   ✅ 成功解析为JSON数组:')
          console.log('   ', parsed)
          
          if (Array.isArray(parsed)) {
            console.log(`   📊 图片数量: ${parsed.length}`)
            parsed.forEach((img, index) => {
              console.log(`   ${index + 1}. ${img}`)
            })
          }
        } catch (e) {
          console.log('   ⚠️ 不是有效的JSON，尝试按逗号分割:')
          const split = product.images.split(',').map(s => s.trim()).filter(s => s)
          console.log('   ', split)
          console.log(`   📊 图片数量: ${split.length}`)
          split.forEach((img, index) => {
            console.log(`   ${index + 1}. ${img}`)
          })
        }
      } else {
        console.log('   ℹ️ 图片字段不是字符串类型')
      }
    } else {
      console.log('   ❌ 图片字段为空')
    }
    
  } catch (error) {
    console.error('❌ 查询失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkProductImages()
  .then(() => {
    console.log('\n🎉 检查完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error)
    process.exit(1)
  })
