#!/usr/bin/env node

const { spawn } = require('child_process')
const { performance } = require('perf_hooks')
const os = require('os')
const http = require('http')

class PerformanceVerifier {
  constructor() {
    this.devServer = null
    this.monitorServer = null
    this.compilationTimes = []
    this.startTime = performance.now()
  }

  // 启动开发服务器
  async startDevServer() {
    console.log('🚀 启动开发服务器...')
    
    const isWindows = os.platform() === 'win32'
    const npmCommand = isWindows ? 'npm.cmd' : 'npm'
    
    return new Promise((resolve, reject) => {
      this.devServer = spawn(npmCommand, ['run', 'dev'], {
        stdio: 'pipe',
        shell: isWindows
      })

      let serverReady = false
      let compilationStarted = false
      let firstCompilationTime = null

      this.devServer.stdout.on('data', (data) => {
        const output = data.toString()
        process.stdout.write(data) // 显示原始输出

        // 检测服务器启动
        if (output.includes('Ready on http://localhost:3000') && !serverReady) {
          serverReady = true
          console.log('\n✅ 开发服务器已启动')
          resolve()
        }

        // 检测编译开始
        if (output.includes('Compiling') && !compilationStarted) {
          compilationStarted = true
          this.compilationStartTime = performance.now()
          console.log('\n⚡ 检测到编译开始...')
        }

        // 检测编译完成
        const compileMatch = output.match(/✓ Compiled .+ in ([\d.]+)([ms]+)/g)
        if (compileMatch) {
          compileMatch.forEach(match => {
            const timeMatch = match.match(/([\d.]+)([ms]+)/)
            if (timeMatch) {
              const time = parseFloat(timeMatch[1])
              const unit = timeMatch[2]
              const timeInMs = unit === 's' ? time * 1000 : time
              
              this.compilationTimes.push(timeInMs)
              
              if (!firstCompilationTime) {
                firstCompilationTime = timeInMs
                console.log(`\n📊 首次编译时间: ${timeInMs}ms`)
                
                // 评估编译性能
                if (timeInMs < 5000) {
                  console.log('🟢 编译性能: 优秀 (< 5秒)')
                } else if (timeInMs < 10000) {
                  console.log('🟡 编译性能: 良好 (< 10秒)')
                } else if (timeInMs < 15000) {
                  console.log('🟠 编译性能: 一般 (< 15秒)')
                } else {
                  console.log('🔴 编译性能: 较差 (> 15秒)')
                }
              }
            }
          })
        }
      })

      this.devServer.stderr.on('data', (data) => {
        process.stderr.write(data)
      })

      this.devServer.on('error', (error) => {
        console.error('❌ 开发服务器启动失败:', error)
        reject(error)
      })

      // 30秒超时
      setTimeout(() => {
        if (!serverReady) {
          console.log('⚠️  服务器启动超时，但可能仍在启动中...')
          resolve()
        }
      }, 30000)
    })
  }

  // 启动性能监控服务器
  async startMonitorServer() {
    console.log('\n🔍 启动性能监控服务器...')
    
    const isWindows = os.platform() === 'win32'
    const nodeCommand = isWindows ? 'node.exe' : 'node'
    
    return new Promise((resolve) => {
      this.monitorServer = spawn(nodeCommand, ['scripts/simple-performance-monitor.js'], {
        stdio: 'pipe',
        shell: isWindows
      })

      let monitorReady = false

      this.monitorServer.stdout.on('data', (data) => {
        const output = data.toString()
        
        if (output.includes('Performance monitor server running') && !monitorReady) {
          monitorReady = true
          console.log('✅ 性能监控服务器已启动')
          resolve()
        }
      })

      this.monitorServer.stderr.on('data', (data) => {
        // 忽略监控服务器的错误输出，因为它可能不存在
      })

      // 5秒后继续，不管监控服务器是否启动成功
      setTimeout(() => {
        if (!monitorReady) {
          console.log('⚠️  性能监控服务器启动失败或不存在，继续验证...')
        }
        resolve()
      }, 5000)
    })
  }

  // 等待服务器完全启动
  async waitForServers() {
    console.log('\n⏳ 等待服务器完全启动...')
    
    // 等待主服务器
    for (let i = 0; i < 30; i++) {
      try {
        await this.makeRequest('http://localhost:3000', 2000)
        console.log('✅ 主服务器响应正常')
        break
      } catch (error) {
        if (i === 29) {
          console.log('⚠️  主服务器可能仍在启动中')
        }
        await this.sleep(1000)
      }
    }

    // 等待监控服务器
    try {
      await this.makeRequest('http://localhost:3001', 2000)
      console.log('✅ 监控服务器响应正常')
    } catch (error) {
      console.log('⚠️  监控服务器未响应')
    }
  }

  // 验证性能改进
  async verifyPerformance() {
    console.log('\n📊 验证性能改进...')
    console.log('='.repeat(50))

    const results = {
      compilation: {},
      api: {},
      database: {},
      overall: {}
    }

    // 1. 编译性能验证
    if (this.compilationTimes.length > 0) {
      const avgCompilation = this.compilationTimes.reduce((a, b) => a + b, 0) / this.compilationTimes.length
      const maxCompilation = Math.max(...this.compilationTimes)
      
      results.compilation = {
        averageTime: avgCompilation,
        maxTime: maxCompilation,
        compilationCount: this.compilationTimes.length,
        performance: avgCompilation < 5000 ? 'excellent' : 
                    avgCompilation < 10000 ? 'good' : 
                    avgCompilation < 15000 ? 'fair' : 'poor'
      }

      console.log('\n⚡ 编译性能:')
      console.log(`  平均编译时间: ${avgCompilation.toFixed(2)}ms`)
      console.log(`  最长编译时间: ${maxCompilation.toFixed(2)}ms`)
      console.log(`  编译次数: ${this.compilationTimes.length}`)
      
      const icon = results.compilation.performance === 'excellent' ? '🟢' : 
                   results.compilation.performance === 'good' ? '🟡' : 
                   results.compilation.performance === 'fair' ? '🟠' : '🔴'
      console.log(`  ${icon} 编译性能: ${results.compilation.performance}`)
    }

    // 2. API性能验证
    console.log('\n🌐 API性能:')
    const apiTests = [
      { name: '主页', url: 'http://localhost:3000' },
      { name: '健康检查', url: 'http://localhost:3000/api/health' },
      { name: '监控仪表板', url: 'http://localhost:3001' }
    ]

    for (const test of apiTests) {
      try {
        const start = performance.now()
        const response = await this.makeRequest(test.url, 5000)
        const time = performance.now() - start
        
        results.api[test.name] = {
          time: time,
          status: response.statusCode,
          performance: time < 100 ? 'excellent' : 
                      time < 500 ? 'good' : 
                      time < 1000 ? 'fair' : 'poor'
        }

        const icon = results.api[test.name].performance === 'excellent' ? '🟢' : 
                     results.api[test.name].performance === 'good' ? '🟡' : 
                     results.api[test.name].performance === 'fair' ? '🟠' : '🔴'
        console.log(`  ${icon} ${test.name}: ${time.toFixed(2)}ms (${response.statusCode})`)
      } catch (error) {
        results.api[test.name] = {
          time: null,
          status: 'error',
          performance: 'error'
        }
        console.log(`  🔴 ${test.name}: 错误 - ${error.message}`)
      }
    }

    // 3. 系统资源检查
    console.log('\n💾 系统资源:')
    const memUsage = process.memoryUsage()
    const systemInfo = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    }

    results.system = systemInfo
    console.log(`  RSS内存: ${systemInfo.rss}MB`)
    console.log(`  堆内存: ${systemInfo.heapUsed}MB / ${systemInfo.heapTotal}MB`)

    // 4. 总体评估
    const overallScore = this.calculateOverallScore(results)
    results.overall = overallScore

    console.log('\n🏆 性能验证结果:')
    console.log(`  总体评分: ${overallScore.score}/100 (${overallScore.grade})`)
    console.log(`  评估: ${overallScore.description}`)

    if (overallScore.improvements.length > 0) {
      console.log('\n✅ 检测到的改进:')
      overallScore.improvements.forEach(improvement => {
        console.log(`  • ${improvement}`)
      })
    }

    if (overallScore.recommendations.length > 0) {
      console.log('\n💡 进一步优化建议:')
      overallScore.recommendations.forEach(rec => {
        console.log(`  • ${rec}`)
      })
    }

    return results
  }

  // 计算总体评分
  calculateOverallScore(results) {
    let score = 100
    const improvements = []
    const recommendations = []

    // 编译性能评分
    if (results.compilation.performance) {
      if (results.compilation.performance === 'excellent') {
        improvements.push('编译性能优秀，平均编译时间 < 5秒')
      } else if (results.compilation.performance === 'good') {
        improvements.push('编译性能良好，平均编译时间 < 10秒')
        score -= 5
      } else if (results.compilation.performance === 'fair') {
        score -= 15
        recommendations.push('编译时间较长，建议进一步优化组件导入')
      } else {
        score -= 25
        recommendations.push('编译时间过长，需要优化代码结构和依赖')
      }
    }

    // API性能评分
    const apiPerformances = Object.values(results.api).map(r => r.performance)
    const excellentAPIs = apiPerformances.filter(p => p === 'excellent').length
    const goodAPIs = apiPerformances.filter(p => p === 'good').length
    const errorAPIs = apiPerformances.filter(p => p === 'error').length

    if (excellentAPIs > 0) {
      improvements.push(`${excellentAPIs}个API响应时间 < 100ms`)
    }
    if (goodAPIs > 0) {
      improvements.push(`${goodAPIs}个API响应时间 < 500ms`)
    }
    if (errorAPIs > 0) {
      score -= errorAPIs * 10
      recommendations.push('部分API无法访问，检查服务器状态')
    }

    // 系统资源评分
    if (results.system.rss < 100) {
      improvements.push('内存使用优秀 (< 100MB)')
    } else if (results.system.rss > 500) {
      score -= 20
      recommendations.push('内存使用过高，考虑优化')
    }

    // 确定等级
    let grade, description
    if (score >= 90) {
      grade = 'A+'
      description = '性能优化效果显著，系统运行优秀'
    } else if (score >= 80) {
      grade = 'A'
      description = '性能优化效果良好，系统运行稳定'
    } else if (score >= 70) {
      grade = 'B'
      description = '性能有所改善，仍有优化空间'
    } else if (score >= 60) {
      grade = 'C'
      description = '性能改善有限，需要进一步优化'
    } else {
      grade = 'D'
      description = '性能改善不明显，需要重新评估优化策略'
    }

    return {
      score: Math.max(0, score),
      grade,
      description,
      improvements,
      recommendations
    }
  }

  // HTTP请求辅助函数
  makeRequest(url, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url)
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname,
        method: 'GET',
        timeout: timeout
      }

      const req = http.request(options, (res) => {
        let data = ''
        res.on('data', chunk => data += chunk)
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            data: data
          })
        })
      })

      req.on('error', reject)
      req.on('timeout', () => {
        req.destroy()
        reject(new Error('Request timeout'))
      })
      
      req.end()
    })
  }

  // 睡眠函数
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 清理资源
  cleanup() {
    console.log('\n🧹 清理资源...')
    
    if (this.devServer) {
      this.devServer.kill()
      console.log('✅ 开发服务器已停止')
    }
    
    if (this.monitorServer) {
      this.monitorServer.kill()
      console.log('✅ 监控服务器已停止')
    }
  }
}

// 主函数
async function main() {
  const verifier = new PerformanceVerifier()
  
  try {
    console.log('🚀 BitMarket 性能验证启动')
    console.log('='.repeat(50))
    
    // 启动服务器
    await verifier.startDevServer()
    await verifier.startMonitorServer()
    
    // 等待服务器完全启动
    await verifier.waitForServers()
    
    // 验证性能
    const results = await verifier.verifyPerformance()
    
    console.log('\n✅ 性能验证完成')
    console.log('\n📝 下一步操作:')
    console.log('1. 访问主应用: http://localhost:3000')
    console.log('2. 访问性能监控: http://localhost:3001')
    console.log('3. 测试各项功能的响应速度')
    console.log('4. 观察编译时间改进')
    
    console.log('\n⚠️  注意: 服务器将继续运行，按 Ctrl+C 停止')
    
    // 保持服务器运行
    process.on('SIGINT', () => {
      console.log('\n\n🛑 收到停止信号，正在清理...')
      verifier.cleanup()
      process.exit(0)
    })
    
    // 保持进程运行
    await new Promise(() => {})
    
  } catch (error) {
    console.error('❌ 性能验证失败:', error)
    verifier.cleanup()
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = { PerformanceVerifier }
