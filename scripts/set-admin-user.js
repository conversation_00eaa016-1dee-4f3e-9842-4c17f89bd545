const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function setAdminUser() {
  try {
    console.log('🔧 开始设置管理员用户权限...')
    
    const email = '<EMAIL>'
    
    // 检查用户是否存在
    let user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      console.log(`📝 用户 ${email} 不存在，正在创建...`)
      
      // 创建新用户
      const hashedPassword = await bcrypt.hash('password123', 12)
      const userId = 'user-' + Math.random().toString(36).substring(2, 14)
      
      user = await prisma.user.create({
        data: {
          userId,
          email,
          name: '测试管理员',
          password: hashedPassword,
          role: 'ADMIN',
          emailVerified: new Date(),
          status: 'ACTIVE',
          creditScore: 100
        }
      })
      
      console.log(`✅ 成功创建管理员用户: ${email}`)
      console.log(`   用户ID: ${user.userId}`)
      console.log(`   默认密码: password123`)
    } else {
      console.log(`📋 用户 ${email} 已存在，正在更新权限...`)
      
      // 更新用户角色为管理员
      user = await prisma.user.update({
        where: { email },
        data: {
          role: 'ADMIN',
          status: 'ACTIVE'
        }
      })
      
      console.log(`✅ 成功将用户 ${email} 设置为管理员`)
    }
    
    // 显示用户信息
    console.log('\n📊 用户信息:')
    console.log(`   ID: ${user.id}`)
    console.log(`   用户ID: ${user.userId}`)
    console.log(`   邮箱: ${user.email}`)
    console.log(`   姓名: ${user.name}`)
    console.log(`   角色: ${user.role}`)
    console.log(`   状态: ${user.status}`)
    console.log(`   邮箱验证: ${user.emailVerified ? '已验证' : '未验证'}`)
    
    // 验证管理员权限
    console.log('\n🔍 验证管理员权限...')
    
    const adminUsers = await prisma.user.findMany({
      where: { role: 'ADMIN' },
      select: {
        id: true,
        userId: true,
        email: true,
        name: true,
        role: true,
        status: true
      }
    })
    
    console.log(`✅ 当前系统中共有 ${adminUsers.length} 个管理员用户:`)
    adminUsers.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.email} (${admin.name || '未设置姓名'}) - ${admin.status}`)
    })
    
    console.log('\n🎯 管理员权限设置完成！')
    console.log('\n📋 可访问的管理功能:')
    console.log('   • 帮助内容管理: /admin/help')
    console.log('   • 媒体文件管理: /admin/help/media')
    console.log('   • 创建帮助文章: /admin/help/create')
    console.log('   • 编辑帮助文章: /admin/help/[id]/edit')
    console.log('   • 媒体上传和管理功能')
    console.log('   • 文章发布和审核功能')
    
    console.log('\n🔐 登录信息:')
    console.log(`   邮箱: ${email}`)
    if (!user.password && user.id) {
      console.log('   密码: 请使用现有密码或重置密码')
    } else if (user.password) {
      console.log('   密码: password123 (如果是新创建的用户)')
    }
    
  } catch (error) {
    console.error('❌ 设置管理员用户失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
setAdminUser()
  .then(() => {
    console.log('\n🎉 管理员权限设置完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error)
    process.exit(1)
  })
