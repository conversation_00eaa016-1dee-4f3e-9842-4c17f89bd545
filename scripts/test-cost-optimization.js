/**
 * 担保金成本优化测试脚本
 * 验证信用等级、批量结算、智能提现等功能
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// 模拟不同等级的用户数据
const testUsers = [
  {
    email: '<EMAIL>',
    name: '青铜用户',
    depositBalance: 50,
    creditLevel: 'BRONZE',
    creditPoints: 10
  },
  {
    email: '<EMAIL>',
    name: '白银用户',
    depositBalance: 200,
    creditLevel: 'SILVER',
    creditPoints: 80
  },
  {
    email: '<EMAIL>',
    name: '黄金用户',
    depositBalance: 800,
    creditLevel: 'GOLD',
    creditPoints: 300
  },
  {
    email: '<EMAIL>',
    name: '铂金用户',
    depositBalance: 3000,
    creditLevel: 'PLATINUM',
    creditPoints: 800
  },
  {
    email: '<EMAIL>',
    name: '钻石用户',
    depositBalance: 8000,
    creditLevel: 'DIAMOND',
    creditPoints: 1500
  }
]

async function createTestUsers() {
  console.log('🔧 创建不同等级的测试用户...')
  
  const users = {}
  
  for (const userData of testUsers) {
    try {
      const user = await prisma.user.upsert({
        where: { email: userData.email },
        update: userData,
        create: userData
      })
      users[userData.email] = user
      console.log(`✅ 创建用户: ${user.name} (${user.creditLevel}) - ${user.depositBalance} USDT`)
    } catch (error) {
      console.error(`❌ 创建用户失败: ${userData.email}`, error.message)
    }
  }
  
  return users
}

async function testCreditLevelBenefits() {
  console.log('\n🏆 测试信用等级权益...')
  
  try {
    // 模拟不同等级用户的提现费率
    const testAmount = 100 // 提现100 USDT
    
    for (const userData of testUsers) {
      const user = await prisma.user.findUnique({
        where: { email: userData.email }
      })
      
      if (!user) continue
      
      // 计算基础费率和智能费率
      const baseFeeRate = 0.01 // 1%
      const baseFee = testAmount * baseFeeRate
      
      // 根据等级计算折扣
      let discount = 0
      switch (user.creditLevel) {
        case 'BRONZE': discount = 0; break
        case 'SILVER': discount = 0.1; break
        case 'GOLD': discount = 0.2; break
        case 'PLATINUM': discount = 0.3; break
        case 'DIAMOND': discount = 0.5; break
      }
      
      const smartFee = baseFee * (1 - discount)
      const savings = baseFee - smartFee
      
      console.log(`${user.name}: 基础费率 ${baseFee.toFixed(4)} USDT → 智能费率 ${smartFee.toFixed(4)} USDT (节省 ${savings.toFixed(4)} USDT, ${(discount * 100).toFixed(0)}%)`)
    }
    
  } catch (error) {
    console.error('❌ 测试信用等级权益失败:', error.message)
  }
}

async function testBatchSettlement() {
  console.log('\n📦 测试批量结算成本优化...')
  
  try {
    // 创建多笔模拟交易
    const transactions = []
    const users = await prisma.user.findMany({
      where: {
        email: {
          in: testUsers.map(u => u.email)
        }
      }
    })
    
    for (let i = 0; i < 10; i++) {
      const user = users[i % users.length]
      const amount = 20 + Math.random() * 80 // 20-100 USDT随机金额
      
      const freeze = await prisma.fundFreeze.create({
        data: {
          userId: user.id,
          amount: amount,
          purpose: 'PURCHASE',
          status: 'CONFIRMED',
          confirmedAt: new Date(),
          notes: `模拟交易 ${i + 1}`
        }
      })
      
      transactions.push(freeze)
    }
    
    console.log(`✅ 创建了 ${transactions.length} 笔模拟交易`)
    
    // 计算单独处理的成本
    const individualCost = transactions.reduce((sum, tx) => {
      return sum + (tx.amount * 0.01) // 假设单独处理费率为1%
    }, 0)
    
    // 计算批量处理的成本
    const batchDiscount = 0.3 // 30% 批量折扣
    const batchCost = transactions.reduce((sum, tx) => {
      const baseFee = tx.amount * 0.005 // 批量处理基础费率0.5%
      return sum + (baseFee * (1 - batchDiscount))
    }, 0)
    
    const totalSavings = individualCost - batchCost
    const savingsRate = (totalSavings / individualCost) * 100
    
    console.log(`💰 成本对比:`)
    console.log(`   单独处理总成本: ${individualCost.toFixed(4)} USDT`)
    console.log(`   批量处理总成本: ${batchCost.toFixed(4)} USDT`)
    console.log(`   节省成本: ${totalSavings.toFixed(4)} USDT (${savingsRate.toFixed(1)}%)`)
    
    return { transactions, savings: totalSavings }
    
  } catch (error) {
    console.error('❌ 测试批量结算失败:', error.message)
    throw error
  }
}

async function testFundPoolStats() {
  console.log('\n📊 测试资金池统计...')
  
  try {
    // 计算总担保金
    const totalDepositsResult = await prisma.user.aggregate({
      _sum: {
        depositBalance: true
      }
    })
    const totalDeposits = totalDepositsResult._sum.depositBalance || 0
    
    // 计算冻结金额
    const totalFrozenResult = await prisma.fundFreeze.aggregate({
      where: {
        status: {
          in: ['FROZEN', 'CONFIRMED']
        }
      },
      _sum: {
        amount: true
      }
    })
    const totalFrozen = totalFrozenResult._sum.amount || 0
    
    // 计算可用金额
    const totalAvailable = totalDeposits - totalFrozen
    
    // 计算资金沉淀率
    const depositRate = totalDeposits > 0 ? (totalAvailable / totalDeposits) * 100 : 0
    
    // 模拟年化收益
    const annualRate = 0.05 // 5%年化收益率
    const projectedAnnualRevenue = totalAvailable * annualRate
    const dailyRevenue = projectedAnnualRevenue / 365
    
    console.log(`💳 资金池统计:`)
    console.log(`   总担保金: ${totalDeposits.toFixed(2)} USDT`)
    console.log(`   冻结金额: ${totalFrozen.toFixed(2)} USDT`)
    console.log(`   可用金额: ${totalAvailable.toFixed(2)} USDT`)
    console.log(`   资金沉淀率: ${depositRate.toFixed(1)}%`)
    console.log(`   预计年收益: ${projectedAnnualRevenue.toFixed(2)} USDT`)
    console.log(`   预计日收益: ${dailyRevenue.toFixed(4)} USDT`)
    
    return {
      totalDeposits,
      totalAvailable,
      projectedRevenue: projectedAnnualRevenue
    }
    
  } catch (error) {
    console.error('❌ 测试资金池统计失败:', error.message)
    throw error
  }
}

async function testDelayedWithdrawal() {
  console.log('\n⏰ 测试延迟提现优化...')
  
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!user) {
      throw new Error('测试用户不存在')
    }
    
    const withdrawalAmount = 200
    
    // 模拟立即提现的成本
    const immediateFeeRate = 0.01
    const immediateFee = withdrawalAmount * immediateFeeRate
    
    // 模拟延迟提现的成本（错峰折扣）
    const delayedFeeRate = immediateFeeRate * 0.9 // 10%错峰折扣
    const delayedFee = withdrawalAmount * delayedFeeRate
    
    // 模拟批量提现的成本
    const batchFeeRate = immediateFeeRate * 0.7 // 30%批量折扣
    const batchFee = withdrawalAmount * batchFeeRate
    
    const delaySavings = immediateFee - delayedFee
    const batchSavings = immediateFee - batchFee
    
    console.log(`💸 提现成本优化 (${withdrawalAmount} USDT):`)
    console.log(`   立即提现: ${immediateFee.toFixed(4)} USDT`)
    console.log(`   错峰提现: ${delayedFee.toFixed(4)} USDT (节省 ${delaySavings.toFixed(4)} USDT)`)
    console.log(`   批量提现: ${batchFee.toFixed(4)} USDT (节省 ${batchSavings.toFixed(4)} USDT)`)
    
    return {
      immediateFee,
      delayedFee,
      batchFee,
      maxSavings: batchSavings
    }
    
  } catch (error) {
    console.error('❌ 测试延迟提现优化失败:', error.message)
    throw error
  }
}

async function runCostOptimizationTests() {
  console.log('🚀 开始担保金成本优化测试\n')
  
  try {
    // 创建测试用户
    const users = await createTestUsers()
    
    // 测试信用等级权益
    await testCreditLevelBenefits()
    
    // 测试批量结算
    const batchResult = await testBatchSettlement()
    
    // 测试资金池统计
    const poolStats = await testFundPoolStats()
    
    // 测试延迟提现
    const withdrawalResult = await testDelayedWithdrawal()
    
    // 总结成本优化效果
    console.log('\n🎯 成本优化总结:')
    console.log(`   批量结算节省: ${batchResult.savings.toFixed(4)} USDT`)
    console.log(`   智能提现节省: ${withdrawalResult.maxSavings.toFixed(4)} USDT`)
    console.log(`   资金池年收益: ${poolStats.projectedRevenue.toFixed(2)} USDT`)
    console.log(`   总优化价值: ${(batchResult.savings + withdrawalResult.maxSavings + poolStats.projectedRevenue).toFixed(2)} USDT`)
    
    console.log('\n🎉 所有测试完成！担保金机制成功实现极限成本压缩')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  runCostOptimizationTests()
}

module.exports = {
  createTestUsers,
  testCreditLevelBenefits,
  testBatchSettlement,
  testFundPoolStats,
  testDelayedWithdrawal,
  runCostOptimizationTests
}
