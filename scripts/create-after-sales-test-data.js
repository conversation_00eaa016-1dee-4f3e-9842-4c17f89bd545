#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createAfterSalesTestData() {
  try {
    console.log('🔄 开始创建售后申请测试数据...')

    // 获取现有用户
    const existingUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!existingUser) {
      console.log('❌ 未找到测试用户')
      return
    }

    console.log('✅ 找到测试用户:', existingUser.email)

    // 创建一个测试卖家
    let seller = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!seller) {
      seller = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test Seller',
          password: 'hashed_password',
          role: 'USER',
          isMediator: false,
          creditScore: 100,
          status: 'ACTIVE',
          emailVerified: new Date(),
          availableBalance: 0,
          frozenBalance: 0,
          totalEarnings: 0,
          totalWithdrawals: 0,
          balanceVersion: 1,
          guaranteeLevel: 'BRONZE',
          lastBalanceUpdate: new Date()
        }
      })
      console.log('✅ 创建测试卖家:', seller.email)
    } else {
      console.log('✅ 找到测试卖家:', seller.email)
    }

    // 创建测试商品
    let product = await prisma.product.findFirst({
      where: { sellerId: seller.id }
    })

    if (!product) {
      product = await prisma.product.create({
        data: {
          title: '测试商品 - iPhone 15',
          description: '这是一个测试商品，用于测试售后申请功能',
          price: 999.99,
          images: JSON.stringify(['https://example.com/iphone15.jpg']),
          category: 'ELECTRONICS',
          condition: 'NEW',
          stock: 10,
          city: '深圳市',
          district: '南山区',
          status: 'ACTIVE',
          reviewStatus: 'APPROVED',
          sellerId: seller.id
        }
      })
      console.log('✅ 创建测试商品:', product.title)
    } else {
      console.log('✅ 找到测试商品:', product.title)
    }

    // 创建测试订单
    let order = await prisma.order.findFirst({
      where: { 
        buyerId: existingUser.id,
        sellerId: seller.id 
      }
    })

    if (!order) {
      order = await prisma.order.create({
        data: {
          orderNumber: `TEST${Date.now()}`,
          buyerId: existingUser.id,
          sellerId: seller.id,
          productId: product.id,
          totalAmount: product.price,
          productPrice: product.price,
          status: 'COMPLETED',
          paymentMethod: 'USDT',
          paymentConfirmed: true,
          shippingAddress: JSON.stringify({
            name: '测试用户',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detail: '测试地址详情'
          }),
          shippingFee: 0
        }
      })
      console.log('✅ 创建测试订单:', order.orderNumber)
    } else {
      console.log('✅ 找到测试订单:', order.orderNumber)
    }

    // 创建测试售后申请
    const existingAfterSales = await prisma.afterSalesRequest.findFirst({
      where: { orderId: order.id }
    })

    if (!existingAfterSales) {
      const afterSalesRequest = await prisma.afterSalesRequest.create({
        data: {
          orderId: order.id,
          buyerId: existingUser.id,
          sellerId: seller.id,
          type: 'REFUND',
          reason: '商品质量问题',
          description: '收到的商品与描述不符，存在质量问题，申请退款',
          requestedAmount: product.price,
          status: 'PENDING',
          images: JSON.stringify(['https://example.com/issue1.jpg', 'https://example.com/issue2.jpg'])
        }
      })
      console.log('✅ 创建测试售后申请:', afterSalesRequest.id)

      // 创建一些测试消息
      await prisma.afterSalesMessage.create({
        data: {
          afterSalesId: afterSalesRequest.id,
          senderId: existingUser.id,
          content: '您好，我收到的商品存在质量问题，希望能够退款处理。',
          messageType: 'TEXT'
        }
      })

      await prisma.afterSalesMessage.create({
        data: {
          afterSalesId: afterSalesRequest.id,
          senderId: seller.id,
          content: '您好，我们已经收到您的售后申请，正在处理中，请稍等。',
          messageType: 'TEXT',
          isRead: false
        }
      })

      console.log('✅ 创建测试消息')
    } else {
      console.log('✅ 找到测试售后申请:', existingAfterSales.id)
    }

    console.log('🎉 售后申请测试数据创建完成！')
    console.log('\n📋 测试数据摘要:')
    console.log(`- 买家: ${existingUser.email} (ID: ${existingUser.id})`)
    console.log(`- 卖家: ${seller.email} (ID: ${seller.id})`)
    console.log(`- 商品: ${product.title}`)
    console.log(`- 订单: ${order.orderNumber}`)
    console.log('\n🔗 现在可以访问 http://localhost:3000/after-sales 查看售后申请页面')

  } catch (error) {
    console.error('❌ 创建售后申请测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAfterSalesTestData()
