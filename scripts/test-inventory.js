const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testInventoryManagement() {
  try {
    console.log('开始测试库存管理功能...')
    
    // 1. 创建一个测试商品，库存为0
    const testProduct = await prisma.product.create({
      data: {
        title: '测试商品 - 库存管理',
        description: '这是一个用于测试库存管理功能的商品',
        price: 100,
        stock: 0, // 设置库存为0
        category: 'GENERAL',
        condition: 'NEW',
        status: 'AVAILABLE', // 状态为可用，但库存为0
        city: '北京',
        district: '朝阳区',
        shippingFrom: '北京朝阳区',
        sellerId: 'cmd346qyh0000v9m8dqnc6duj', // 使用测试用户
        isDemandGenerated: false
      }
    })
    
    console.log('创建测试商品成功:', {
      id: testProduct.id,
      title: testProduct.title,
      stock: testProduct.stock,
      status: testProduct.status
    })
    
    // 2. 运行库存检查
    const { checkInventory } = require('./check-inventory.js')
    await checkInventory()
    
    // 3. 检查商品状态是否已更新
    const updatedProduct = await prisma.product.findUnique({
      where: { id: testProduct.id },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true
      }
    })
    
    console.log('检查后的商品状态:', updatedProduct)
    
    if (updatedProduct.status === 'SOLD_OUT') {
      console.log('✅ 库存管理功能正常：商品已自动下架')
    } else {
      console.log('❌ 库存管理功能异常：商品未自动下架')
    }
    
    // 4. 测试库存恢复功能
    console.log('\n测试库存恢复功能...')
    
    const restoredProduct = await prisma.product.update({
      where: { id: testProduct.id },
      data: {
        stock: 5 // 恢复库存
      }
    })
    
    console.log('恢复库存后:', {
      stock: restoredProduct.stock,
      status: restoredProduct.status
    })
    
    // 5. 测试库存管理API
    const { checkAndUpdateProductStock } = require('../lib/inventory.js')
    const stockResult = await checkAndUpdateProductStock(testProduct.id)
    
    console.log('库存检查结果:', stockResult)
    
    // 6. 清理测试数据
    await prisma.product.delete({
      where: { id: testProduct.id }
    })
    
    console.log('测试完成，已清理测试数据')
    
  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testInventoryManagement()
