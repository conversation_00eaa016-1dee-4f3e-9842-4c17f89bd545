#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class ReleasePackageCreator {
  constructor() {
    this.projectRoot = process.cwd()
    this.packageInfo = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    this.version = this.packageInfo.version
    this.releaseDir = path.join(this.projectRoot, 'release')
    this.packageName = `bitmarket-v${this.version}-source`
  }

  // 创建发布目录
  createReleaseDirectory() {
    console.log('📁 创建发布目录...')
    
    if (fs.existsSync(this.releaseDir)) {
      fs.rmSync(this.releaseDir, { recursive: true, force: true })
    }
    fs.mkdirSync(this.releaseDir, { recursive: true })
    
    console.log(`✅ 发布目录已创建: ${this.releaseDir}`)
  }

  // 获取需要包含的文件和目录
  getIncludeList() {
    return [
      // 核心源码目录
      'app/',
      'components/',
      'lib/',
      'prisma/',
      'public/',
      'scripts/',
      
      // 配置文件
      'package.json',
      'package-lock.json',
      'next.config.ts',
      'tsconfig.json',
      'tailwind.config.js',
      'postcss.config.js',
      'server.js',
      
      // 环境配置模板
      '.env.example',
      
      // 文档文件
      'README.md',
      'CHANGELOG.md',
      'LICENSE',
      
      // 发布相关文档
      'RELEASE_SUMMARY_v1.3.0.md',
      'RELEASE_CONFIRMATION_v1.3.0.md',
      
      // 其他重要文件
      '.gitignore',
      '.eslintrc.json',
      'vitest.config.ts'
    ]
  }

  // 获取需要排除的文件和目录
  getExcludeList() {
    // 缓存排除列表，避免重复读取
    if (this.excludeList) {
      return this.excludeList
    }

    // 首先尝试从exclude-list.txt读取
    const excludeListPath = path.join(this.projectRoot, 'exclude-list.txt')
    let excludeList = []

    if (fs.existsSync(excludeListPath)) {
      console.log('📋 从exclude-list.txt读取排除规则...')
      const excludeContent = fs.readFileSync(excludeListPath, 'utf8')
      excludeList = excludeContent
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('#'))
    }

    // 添加额外的排除规则
    const additionalExcludes = [
      // 发布相关
      'release/',
      '*.tgz',
      '*.tar.gz',
      '*.zip',

      // 测试结果
      'test-results/',

      // 其他临时文件
      '*.tmp',
      '*.temp'
    ]

    this.excludeList = [...excludeList, ...additionalExcludes]
    return this.excludeList
  }

  // 复制文件到发布目录
  copySourceFiles() {
    console.log('📋 复制源码文件...')
    
    const sourceDir = path.join(this.releaseDir, this.packageName)
    fs.mkdirSync(sourceDir, { recursive: true })
    
    const includeList = this.getIncludeList()
    let copiedFiles = 0
    
    includeList.forEach(item => {
      const sourcePath = path.join(this.projectRoot, item)
      const targetPath = path.join(sourceDir, item)
      
      if (fs.existsSync(sourcePath)) {
        const stat = fs.statSync(sourcePath)
        
        if (stat.isDirectory()) {
          this.copyDirectory(sourcePath, targetPath)
          console.log(`  📁 ${item}`)
        } else {
          fs.mkdirSync(path.dirname(targetPath), { recursive: true })
          fs.copyFileSync(sourcePath, targetPath)
          console.log(`  📄 ${item}`)
          copiedFiles++
        }
      } else {
        console.log(`  ⚠️  跳过不存在的文件: ${item}`)
      }
    })
    
    console.log(`✅ 源码文件复制完成，共处理 ${copiedFiles} 个文件`)
  }

  // 递归复制目录
  copyDirectory(source, target) {
    const excludeList = this.getExcludeList()
    
    if (!fs.existsSync(target)) {
      fs.mkdirSync(target, { recursive: true })
    }
    
    const items = fs.readdirSync(source)
    
    items.forEach(item => {
      const sourcePath = path.join(source, item)
      const targetPath = path.join(target, item)
      const relativePath = path.relative(this.projectRoot, sourcePath)
      
      // 检查是否应该排除
      const shouldExclude = excludeList.some(pattern => {
        if (pattern.endsWith('/')) {
          return relativePath.startsWith(pattern) || relativePath.includes('/' + pattern)
        }
        return relativePath === pattern || relativePath.endsWith(pattern)
      })
      
      if (shouldExclude) {
        return
      }
      
      const stat = fs.statSync(sourcePath)
      
      if (stat.isDirectory()) {
        this.copyDirectory(sourcePath, targetPath)
      } else {
        fs.copyFileSync(sourcePath, targetPath)
      }
    })
  }

  // 创建发布信息文件
  createReleaseInfo() {
    console.log('📝 创建发布信息文件...')
    
    const releaseInfo = {
      name: 'BitMarket',
      version: this.version,
      releaseDate: new Date().toISOString().split('T')[0],
      description: 'BitMarket - 基于USDT的去中心化C2C交易平台',
      features: [
        '保证金机制更新 - 优化中间人担保系统',
        '页面系统更新 - 全面改善用户界面',
        '导航栏重构 - logo图片+BitMarket文字组合',
        'UI组件库 - 完整的可复用组件系统'
      ],
      technicalImprovements: [
        '构建系统优化',
        '依赖管理完善',
        '代码质量提升',
        '性能优化'
      ],
      requirements: {
        node: '>=18.0.0',
        npm: '>=8.0.0'
      },
      installation: [
        '1. 解压源码包',
        '2. 运行 npm install 安装依赖',
        '3. 配置环境变量（参考.env.example）',
        '4. 运行 npm run build 构建项目',
        '5. 运行 npm start 启动生产服务器'
      ]
    }
    
    const releaseInfoPath = path.join(this.releaseDir, this.packageName, 'RELEASE_INFO.json')
    fs.writeFileSync(releaseInfoPath, JSON.stringify(releaseInfo, null, 2))
    
    console.log('✅ 发布信息文件已创建')
  }

  // 创建安装说明文件
  createInstallGuide() {
    console.log('📖 创建安装说明文件...')
    
    const installGuide = `# BitMarket v${this.version} 安装指南

## 📋 系统要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- 数据库: MySQL 8.0+ 或 PostgreSQL 13+

## 🚀 快速安装

### 1. 解压源码包
\`\`\`bash
tar -xzf bitmarket-v${this.version}-source.tar.gz
cd bitmarket-v${this.version}-source
\`\`\`

### 2. 安装依赖
\`\`\`bash
npm install
\`\`\`

### 3. 环境配置
\`\`\`bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
\`\`\`

### 4. 数据库设置
\`\`\`bash
npx prisma generate
npx prisma db push
\`\`\`

### 5. 构建项目
\`\`\`bash
npm run build
\`\`\`

### 6. 启动服务
\`\`\`bash
# 开发环境
npm run dev

# 生产环境
npm start
\`\`\`

## 🔧 配置说明

### 环境变量配置
请参考 \`.env.example\` 文件中的配置项说明。

### 数据库配置
支持 MySQL 和 PostgreSQL，请在 \`.env\` 文件中配置 \`DATABASE_URL\`。

## 📚 更多信息

- 详细文档: 查看 README.md
- 更新日志: 查看 CHANGELOG.md
- 发布说明: 查看 RELEASE_SUMMARY_v${this.version}.md

## 🆘 获取帮助

如果遇到问题，请查看：
1. README.md 中的常见问题解答
2. GitHub Issues: https://github.com/liusu-ally/bitmarket/issues
3. 邮箱支持: <EMAIL>
`
    
    const installGuidePath = path.join(this.releaseDir, this.packageName, 'INSTALL.md')
    fs.writeFileSync(installGuidePath, installGuide)
    
    console.log('✅ 安装说明文件已创建')
  }

  // 创建压缩包
  createArchive() {
    console.log('📦 创建压缩包...')
    
    const archiveName = `${this.packageName}.tar.gz`
    const archivePath = path.join(this.releaseDir, archiveName)
    
    try {
      // 使用tar命令创建压缩包
      execSync(`cd "${this.releaseDir}" && tar -czf "${archiveName}" "${this.packageName}"`, {
        stdio: 'inherit'
      })
      
      const stats = fs.statSync(archivePath)
      const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2)
      
      console.log(`✅ 压缩包已创建: ${archivePath}`)
      console.log(`📊 压缩包大小: ${fileSizeMB} MB`)
      
      return archivePath
    } catch (error) {
      console.error('❌ 创建压缩包失败:', error.message)
      throw error
    }
  }

  // 生成校验和
  generateChecksum(archivePath) {
    console.log('🔐 生成校验和...')

    try {
      let checksum
      let checksumCommand

      // 检测操作系统并使用相应的命令
      if (process.platform === 'win32') {
        // Windows使用PowerShell的Get-FileHash
        checksumCommand = `powershell -Command "Get-FileHash '${archivePath}' -Algorithm SHA256 | Select-Object -ExpandProperty Hash"`
        checksum = execSync(checksumCommand, { encoding: 'utf8' }).trim().toLowerCase()
      } else {
        // Linux/macOS使用sha256sum
        checksumCommand = `sha256sum "${archivePath}"`
        const output = execSync(checksumCommand, { encoding: 'utf8' }).trim()
        checksum = output.split(' ')[0]
      }

      const checksumPath = archivePath + '.sha256'
      const checksumContent = `${checksum}  ${path.basename(archivePath)}`

      fs.writeFileSync(checksumPath, checksumContent)

      console.log(`✅ 校验和已生成: ${checksumPath}`)
      console.log(`🔐 SHA256: ${checksum}`)

      return checksum
    } catch (error) {
      console.error('❌ 生成校验和失败:', error.message)
      // 校验和生成失败不应该阻止整个release流程
      console.log('⚠️  跳过校验和生成，继续执行...')
      return null
    }
  }

  // 执行完整的打包流程
  async createRelease() {
    console.log(`🚀 开始创建 BitMarket v${this.version} 发布包...\n`)
    
    try {
      this.createReleaseDirectory()
      this.copySourceFiles()
      this.createReleaseInfo()
      this.createInstallGuide()
      
      const archivePath = this.createArchive()
      const checksum = this.generateChecksum(archivePath)
      
      console.log('\n🎉 发布包创建完成!')
      console.log('📦 发布包信息:')
      console.log(`   版本: v${this.version}`)
      console.log(`   文件: ${path.basename(archivePath)}`)
      console.log(`   路径: ${archivePath}`)
      console.log(`   校验: ${checksum}`)
      
      return {
        version: this.version,
        archivePath,
        checksum,
        size: fs.statSync(archivePath).size
      }
      
    } catch (error) {
      console.error('❌ 创建发布包失败:', error.message)
      throw error
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const creator = new ReleasePackageCreator()
  creator.createRelease().catch(console.error)
}

module.exports = ReleasePackageCreator
