const fs = require('fs');
const path = require('path');

// 检查支付相关链接是否正确
const filesToCheck = [
  'app/orders/[id]/page.tsx',
  'app/order/[id]/info-collect/page.tsx'
];

console.log('=== 检查支付流程链接 ===\n');

filesToCheck.forEach(filePath => {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');
      
      console.log(`检查文件: ${filePath}`);
      
      // 检查支付相关链接
      const paymentLinks = [];
      lines.forEach((line, index) => {
        // 检查各种支付链接模式
        const patterns = [
          /href=.*\/payment/,
          /href=.*\/info-collect/,
          /href=.*\/balance-pay/,
          /href=.*\/bsc-pay/,
          /href=.*\/binancepay-QRcode/
        ];
        
        patterns.forEach(pattern => {
          if (pattern.test(line)) {
            paymentLinks.push({ 
              lineNumber: index + 1, 
              content: line.trim(),
              type: line.includes('info-collect') ? 'info-collect' :
                    line.includes('balance-pay') ? 'balance-pay' :
                    line.includes('bsc-pay') ? 'bsc-pay' :
                    line.includes('binancepay-QRcode') ? 'binancepay' :
                    line.includes('/payment') ? 'payment' : 'other'
            });
          }
        });
      });
      
      if (paymentLinks.length > 0) {
        console.log(`找到 ${paymentLinks.length} 个支付相关链接:`);
        paymentLinks.forEach(({ lineNumber, content, type }) => {
          const status = type === 'info-collect' ? '✅' : 
                        type === 'payment' ? '⚠️' : '✅';
          console.log(`   ${status} 第${lineNumber}行 [${type}]: ${content}`);
        });
      } else {
        console.log('未找到支付相关链接');
      }
      
      console.log('');
    } else {
      console.log(`⚠️  文件不存在: ${filePath}\n`);
    }
  } catch (error) {
    console.log(`❌ 检查文件失败: ${filePath} - ${error.message}\n`);
  }
});

console.log('=== 支付流程说明 ===');
console.log('正确的支付流程应该是:');
console.log('1. 订单详情页 → /order/{id}/info-collect (信息收集页面)');
console.log('2. 信息收集页 → 选择支付方式:');
console.log('   - /order/{id}/payment/balance-pay (余额支付)');
console.log('   - /order/{id}/payment/bsc-pay (BSC支付)');
console.log('   - /order/{id}/payment/binancepay-QRcode (币安支付)');
console.log('3. 支付完成 → /order/{id}/payment/successful (支付成功页)');
console.log('\n=== 检查完成 ===');
