const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testTotalBalanceCalculation() {
  console.log('💰 测试总余额计算逻辑...\n')

  try {
    // 1. 获取测试用户
    console.log('1. 获取测试用户数据...')
    
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true
      }
    })

    if (testUsers.length === 0) {
      console.log('❌ 没有找到测试用户')
      return
    }

    console.log(`✅ 找到 ${testUsers.length} 个测试用户`)

    // 2. 为每个用户验证总余额计算
    for (const user of testUsers) {
      console.log(`\n📊 验证用户: ${user.name} (${user.email})`)
      console.log(`   数据库保证金余额: ${user.depositBalance} USDT`)

      // 计算冻结资金
      const guaranteeDeposits = await prisma.fundTransaction.aggregate({
        where: {
          userId: user.id,
          type: 'GUARANTEE_DEPOSIT'
        },
        _sum: { amount: true }
      })

      const guaranteeWithdrawals = await prisma.fundTransaction.aggregate({
        where: {
          userId: user.id,
          type: 'GUARANTEE_WITHDRAWAL'
        },
        _sum: { amount: true }
      })

      const frozenFunds = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)

      // 计算待提现金额
      const pendingWithdrawals = await prisma.withdrawal.aggregate({
        where: {
          userId: user.id,
          status: {
            in: ['PENDING', 'APPROVED', 'PROCESSING']
          }
        },
        _sum: { amount: true }
      })

      const pendingWithdrawalAmount = pendingWithdrawals._sum.amount || 0

      // 按照修复后的逻辑计算
      const availableBalance = Math.max(0, user.depositBalance - pendingWithdrawalAmount)
      const totalBalance = availableBalance + frozenFunds

      console.log('\n   计算详情:')
      console.log(`     冻结资金: ${frozenFunds} USDT`)
      console.log(`     待提现金额: ${pendingWithdrawalAmount} USDT`)
      console.log(`     可用余额: ${availableBalance} USDT`)
      console.log(`     总余额: ${totalBalance} USDT`)

      // 验证数学关系
      const calculatedTotal = availableBalance + frozenFunds
      const isCorrect = Math.abs(totalBalance - calculatedTotal) < 0.01

      console.log('\n   数学关系验证:')
      console.log(`     总余额 = 可用余额 + 冻结资金`)
      console.log(`     ${totalBalance} = ${availableBalance} + ${frozenFunds}`)
      console.log(`     验证结果: ${isCorrect ? '✅ 正确' : '❌ 错误'}`)

      // 模拟API响应
      const apiResponse = {
        balance: {
          total: totalBalance,
          available: availableBalance,
          guaranteePool: frozenFunds,
          pendingWithdrawal: pendingWithdrawalAmount
        }
      }

      console.log('\n   API响应数据:')
      console.log(`     total: ${apiResponse.balance.total} USDT`)
      console.log(`     available: ${apiResponse.balance.available} USDT`)
      console.log(`     guaranteePool: ${apiResponse.balance.guaranteePool} USDT`)
      console.log(`     pendingWithdrawal: ${apiResponse.balance.pendingWithdrawal} USDT`)
    }

    // 3. 模拟冻结资金操作对总余额的影响
    console.log('\n3. 模拟冻结资金操作影响...')
    
    const testUser = testUsers[0]
    console.log(`\n   使用用户: ${testUser.name}`)
    
    // 获取当前状态
    const currentDeposits = await prisma.fundTransaction.aggregate({
      where: { userId: testUser.id, type: 'GUARANTEE_DEPOSIT' },
      _sum: { amount: true }
    })
    
    const currentWithdrawals = await prisma.fundTransaction.aggregate({
      where: { userId: testUser.id, type: 'GUARANTEE_WITHDRAWAL' },
      _sum: { amount: true }
    })
    
    const currentFrozenFunds = Math.abs(currentDeposits._sum.amount || 0) - Math.abs(currentWithdrawals._sum.amount || 0)
    const currentAvailableBalance = testUser.depositBalance
    const currentTotalBalance = currentAvailableBalance + currentFrozenFunds
    
    console.log('\n   操作前状态:')
    console.log(`     可用余额: ${currentAvailableBalance} USDT`)
    console.log(`     冻结资金: ${currentFrozenFunds} USDT`)
    console.log(`     总余额: ${currentTotalBalance} USDT`)

    // 模拟充值到冻结资金 1000 USDT
    const chargeAmount = 1000
    console.log(`\n   模拟操作: 充值 ${chargeAmount} USDT 到冻结资金`)
    
    const newAvailableBalance = currentAvailableBalance - chargeAmount
    const newFrozenFunds = currentFrozenFunds + chargeAmount
    const newTotalBalance = newAvailableBalance + newFrozenFunds
    
    console.log('\n   操作后状态:')
    console.log(`     可用余额: ${newAvailableBalance} USDT (减少 ${chargeAmount})`)
    console.log(`     冻结资金: ${newFrozenFunds} USDT (增加 ${chargeAmount})`)
    console.log(`     总余额: ${newTotalBalance} USDT`)
    
    const totalBalanceUnchanged = Math.abs(newTotalBalance - currentTotalBalance) < 0.01
    console.log(`\n   总余额变化验证: ${totalBalanceUnchanged ? '✅ 总余额保持不变' : '❌ 总余额发生变化'}`)

    // 4. 边界情况测试
    console.log('\n4. 边界情况测试...')
    
    const edgeCases = [
      { name: '无冻结资金', available: 1000, frozen: 0, pending: 0 },
      { name: '无可用余额', available: 0, frozen: 1000, pending: 0 },
      { name: '有待提现', available: 800, frozen: 500, pending: 200 },
      { name: '全部冻结', available: 0, frozen: 2000, pending: 0 }
    ]
    
    edgeCases.forEach(testCase => {
      const total = testCase.available + testCase.frozen
      console.log(`\n   ${testCase.name}:`)
      console.log(`     可用余额: ${testCase.available} USDT`)
      console.log(`     冻结资金: ${testCase.frozen} USDT`)
      console.log(`     待提现: ${testCase.pending} USDT`)
      console.log(`     总余额: ${total} USDT`)
      console.log(`     验证: ${total === testCase.available + testCase.frozen ? '✅' : '❌'}`)
    })

    console.log('\n🎉 总余额计算逻辑测试完成！')
    
    console.log('\n📝 修复总结:')
    console.log('   ✅ 修复前: 总余额 = user.depositBalance (不正确)')
    console.log('   ✅ 修复后: 总余额 = 可用余额 + 冻结资金 (正确)')
    console.log('   ✅ 数学关系: totalBalance = availableBalance + frozenFunds')
    console.log('   ✅ 操作不变性: 冻结资金操作不改变总余额')

    console.log('\n🔧 手动验证步骤:')
    console.log('1. 访问保证金页面: http://localhost:3000/deposit')
    console.log('2. 查看三个金额数值:')
    console.log('   - 总余额')
    console.log('   - 可用保证金')
    console.log('   - 冻结资金')
    console.log('3. 验证数学关系: 总余额 = 可用保证金 + 冻结资金')
    console.log('4. 进行冻结资金充值/提现操作')
    console.log('5. 确认总余额保持不变，只在可用和冻结间转移')

    console.log('\n💡 预期结果:')
    console.log('   - 三个金额数值满足数学关系')
    console.log('   - 冻结资金操作不改变总余额')
    console.log('   - 所有金额显示一致且准确')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testTotalBalanceCalculation().catch(console.error)
}

module.exports = { testTotalBalanceCalculation }
