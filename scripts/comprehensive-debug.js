const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const prisma = new PrismaClient();

async function comprehensiveDebug() {
  try {
    console.log('=== 综合调试：中间人托管订单创建问题 ===\n');
    
    const productId = 'cmdr1z4vv00078oqqm2k4gq7v';
    
    // 1. 检查数据库当前状态
    console.log('1. 检查数据库当前状态...');
    
    // 检查商品
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: { id: true, name: true, role: true }
        }
      }
    });
    
    if (!product) {
      console.log('❌ 商品不存在');
      return;
    }
    
    console.log(`✅ 商品: ${product.title} (${product.price} USDT)`);
    console.log(`   卖家: ${product.seller.name} (${product.seller.role})`);
    
    // 检查最新订单
    const latestOrders = await prisma.order.findMany({
      where: { productId },
      include: {
        buyer: { select: { id: true, name: true, email: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    });
    
    console.log(`\n最近的订单 (${latestOrders.length}个):`);
    latestOrders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.id} - ${order.status} - ${order.totalAmount} USDT`);
      console.log(`   买家: ${order.buyer.name} (${order.buyer.email})`);
      console.log(`   创建时间: ${order.createdAt.toISOString()}`);
    });
    
    // 检查托管订单
    const escrowOrders = await prisma.escrowOrder.findMany({
      where: {
        orderId: { in: latestOrders.map(o => o.id) }
      },
      include: {
        mediator: { select: { id: true, name: true } }
      }
    });
    
    console.log(`\n托管订单 (${escrowOrders.length}个):`);
    escrowOrders.forEach((escrow, index) => {
      console.log(`${index + 1}. ${escrow.id} - ${escrow.status} - ${escrow.amount} USDT`);
      console.log(`   中间人: ${escrow.mediator.name}`);
    });
    
    // 检查中间人状态
    const mediators = await prisma.user.findMany({
      where: { isMediator: true },
      select: {
        id: true,
        name: true,
        mediatorStatus: true,
        availableBalance: true,
        mediatorFeeRate: true
      }
    });
    
    console.log(`\n中间人状态 (${mediators.length}个):`);
    mediators.forEach((mediator, index) => {
      console.log(`${index + 1}. ${mediator.name} - ${mediator.mediatorStatus}`);
      console.log(`   可用余额: ${mediator.availableBalance} USDT`);
      console.log(`   费率: ${(mediator.mediatorFeeRate * 100).toFixed(2)}%`);
    });
    
    // 2. 测试API端点（不需要认证的测试）
    console.log('\n2. 测试API端点...');
    
    // 测试中间人自动分配API
    console.log('\n测试中间人自动分配API...');
    try {
      const autoAssignResponse = await axios.post('http://localhost:3000/api/mediator/auto-assign', {
        orderAmount: product.price
      }, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });
      
      if (autoAssignResponse.data.success) {
        console.log('✅ 中间人自动分配API响应成功');
        console.log(`   分配的中间人: ${autoAssignResponse.data.data.mediator.name}`);
        console.log(`   托管费用: ${autoAssignResponse.data.data.escrowFee} USDT`);
      } else {
        console.log('❌ 中间人自动分配API失败');
        console.log(`   错误: ${autoAssignResponse.data.error}`);
      }
    } catch (error) {
      console.log('❌ 中间人自动分配API请求失败');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 3. 检查认证测试API
    console.log('\n3. 测试认证API...');
    try {
      const authTestResponse = await axios.get('http://localhost:3000/api/test-auth', {
        timeout: 10000
      });
      
      console.log('✅ 认证测试API响应成功');
      console.log(`   响应: ${JSON.stringify(authTestResponse.data, null, 2)}`);
    } catch (error) {
      console.log('❌ 认证测试API请求失败');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 4. 检查用户状态
    console.log('\n4. 检查用户状态...');
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { email: { contains: 'test' } },
          { email: { contains: '<EMAIL>' } },
          { role: 'ADMIN' }
        ]
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isMediator: true,
        status: true
      },
      take: 5
    });
    
    console.log(`相关用户 (${users.length}个):`);
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   角色: ${user.role}, 状态: ${user.status}`);
      console.log(`   是否中间人: ${user.isMediator}`);
    });
    
    // 5. 生成调试报告
    console.log('\n=== 调试报告 ===');
    
    const issues = [];
    const recommendations = [];
    
    // 检查基本条件
    if (product.price < 100) {
      issues.push(`商品价格 ${product.price} USDT 低于最低要求 100 USDT`);
    }
    
    const activeMediators = mediators.filter(m => m.mediatorStatus === 'ACTIVE');
    if (activeMediators.length === 0) {
      issues.push('没有活跃的中间人');
      recommendations.push('激活至少一个中间人账号');
    }
    
    const qualifiedMediators = activeMediators.filter(m => m.availableBalance >= product.price);
    if (qualifiedMediators.length === 0) {
      issues.push('没有保证金充足的中间人');
      recommendations.push('为中间人账号充值足够的保证金');
    }
    
    const pendingOrders = latestOrders.filter(o => o.status === 'PENDING_PAYMENT');
    if (pendingOrders.length === 0) {
      issues.push('没有待支付的订单');
      recommendations.push('先创建一个普通订单');
    }
    
    if (escrowOrders.length === 0 && pendingOrders.length > 0) {
      issues.push('有待支付订单但没有托管订单');
      recommendations.push('检查托管订单创建API的认证和参数');
    }
    
    console.log('\n发现的问题:');
    if (issues.length > 0) {
      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    } else {
      console.log('✅ 基本条件都满足');
    }
    
    console.log('\n建议的解决方案:');
    if (recommendations.length > 0) {
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    } else {
      console.log('✅ 系统配置正常，问题可能在认证或API调用');
    }
    
    // 6. 下一步调试建议
    console.log('\n=== 下一步调试建议 ===');
    console.log('1. 在浏览器中访问 http://localhost:3000/test-auth');
    console.log('2. 检查用户登录状态和Session信息');
    console.log('3. 测试API认证功能');
    console.log('4. 使用浏览器开发者工具检查网络请求');
    console.log('5. 查看具体的错误信息和状态码');
    
  } catch (error) {
    console.error('综合调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

comprehensiveDebug();
