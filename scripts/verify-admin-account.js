const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function verifyAdminAccount() {
  console.log('🔍 验证管理员账号...\n')

  try {
    const email = '<EMAIL>'
    
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        userId: true,
        email: true,
        name: true,
        role: true,
        creditScore: true,
        emailVerified: true,
        binanceUid: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      console.log('❌ 用户不存在')
      return
    }

    console.log('✅ 用户信息验证:')
    console.log(`   - 数据库ID: ${user.id}`)
    console.log(`   - 用户ID: ${user.userId}`)
    console.log(`   - 邮箱: ${user.email}`)
    console.log(`   - 姓名: ${user.name}`)
    console.log(`   - 角色: ${user.role}`)
    console.log(`   - 信用分: ${user.creditScore}`)
    console.log(`   - 邮箱验证: ${user.emailVerified ? '已验证' : '未验证'}`)
    console.log(`   - 币安UID: ${user.binanceUid || '未绑定'}`)
    console.log(`   - 创建时间: ${user.createdAt}`)
    console.log(`   - 更新时间: ${user.updatedAt}`)

    // 验证管理员权限
    console.log('\n🔐 权限验证:')
    if (user.role === 'ADMIN') {
      console.log('✅ 管理员权限: 已授予')
      console.log('✅ 可以访问管理后台')
      console.log('✅ 可以管理用户和订单')
      console.log('✅ 可以查看系统统计')
    } else {
      console.log('❌ 管理员权限: 未授予')
      console.log(`   当前角色: ${user.role}`)
    }

    // 检查密码
    console.log('\n🔑 密码验证:')
    const userWithPassword = await prisma.user.findUnique({
      where: { email },
      select: { password: true }
    })

    if (userWithPassword) {
      const passwordMatch = await bcrypt.compare('123456', userWithPassword.password)
      console.log(`✅ 密码验证: ${passwordMatch ? '正确' : '错误'}`)
    }

    // 统计信息
    console.log('\n📊 系统统计:')
    const totalUsers = await prisma.user.count()
    const adminUsers = await prisma.user.count({
      where: { role: 'ADMIN' }
    })
    const totalOrders = await prisma.order.count()
    const totalProducts = await prisma.product.count()

    console.log(`   - 总用户数: ${totalUsers}`)
    console.log(`   - 管理员数: ${adminUsers}`)
    console.log(`   - 总订单数: ${totalOrders}`)
    console.log(`   - 总商品数: ${totalProducts}`)

    console.log('\n🎯 登录测试:')
    console.log('请使用以下信息登录:')
    console.log(`   邮箱: ${email}`)
    console.log(`   密码: 123456`)
    console.log(`   登录地址: http://localhost:3000/auth/signin`)
    console.log(`   管理后台: http://localhost:3000/admin`)

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行验证
verifyAdminAccount().catch(console.error)
