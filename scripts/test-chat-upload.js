const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

async function testChatUpload() {
  try {
    console.log('🧪 测试聊天文件上传功能...')
    
    // 1. 检查上传目录
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'chat')
    console.log(`📁 检查上传目录: ${uploadDir}`)
    
    if (!fs.existsSync(uploadDir)) {
      console.log('❌ 上传目录不存在，正在创建...')
      fs.mkdirSync(uploadDir, { recursive: true })
      console.log('✅ 上传目录创建成功')
    } else {
      console.log('✅ 上传目录已存在')
    }

    // 2. 检查现有订单
    const order = await prisma.order.findFirst({
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    if (!order) {
      console.log('❌ 没有找到测试订单')
      return
    }

    console.log(`✅ 找到测试订单: ${order.id}`)
    console.log(`   订单号: ${order.orderNumber}`)
    console.log(`   商品: ${order.product.title}`)
    console.log(`   买家: ${order.buyer.email}`)
    console.log(`   卖家: ${order.seller.email}`)

    // 3. 检查现有消息
    const messages = await prisma.message.findMany({
      where: { orderId: order.id },
      include: {
        sender: true,
        receiver: true
      },
      orderBy: { createdAt: 'desc' }
    })

    console.log(`📨 订单中现有消息数量: ${messages.length}`)
    
    if (messages.length > 0) {
      console.log('最近的消息:')
      messages.slice(0, 3).forEach((msg, index) => {
        console.log(`  ${index + 1}. [${msg.messageType}] ${msg.content || '文件消息'}`)
        console.log(`     发送者: ${msg.sender.email}`)
        console.log(`     时间: ${msg.createdAt}`)
        if (msg.fileUrl) {
          console.log(`     文件: ${msg.fileName} (${msg.fileSize} bytes)`)
        }
      })
    }

    // 4. 创建测试文本消息
    console.log('\n📝 创建测试文本消息...')
    const testMessage = await prisma.message.create({
      data: {
        orderId: order.id,
        content: '这是一条测试消息 - ' + new Date().toLocaleString(),
        messageType: 'TEXT',
        senderId: order.buyerId,
        receiverId: order.sellerId,
        status: 'SENT'
      },
      include: {
        sender: true,
        receiver: true
      }
    })

    console.log(`✅ 测试消息创建成功: ${testMessage.id}`)
    console.log(`   内容: ${testMessage.content}`)

    // 5. 测试文件消息结构
    console.log('\n📎 测试文件消息数据结构...')
    
    // 模拟文件上传后的消息
    const fileMessage = await prisma.message.create({
      data: {
        orderId: order.id,
        content: '',
        messageType: 'IMAGE',
        senderId: order.sellerId,
        receiverId: order.buyerId,
        status: 'SENT',
        fileUrl: '/uploads/chat/test_image.jpg',
        fileName: 'test_image.jpg',
        fileSize: 1024000,
        fileMimeType: 'image/jpeg',
        fileMetadata: {
          width: 1920,
          height: 1080,
          originalSize: 2048000
        }
      },
      include: {
        sender: true,
        receiver: true
      }
    })

    console.log(`✅ 文件消息创建成功: ${fileMessage.id}`)
    console.log(`   文件类型: ${fileMessage.messageType}`)
    console.log(`   文件名: ${fileMessage.fileName}`)
    console.log(`   文件大小: ${fileMessage.fileSize} bytes`)
    console.log(`   文件URL: ${fileMessage.fileUrl}`)

    console.log('\n🎉 聊天文件上传功能测试完成！')
    console.log('\n📋 测试结果总结:')
    console.log('✅ 上传目录检查通过')
    console.log('✅ 订单数据获取成功')
    console.log('✅ 文本消息创建成功')
    console.log('✅ 文件消息数据结构测试成功')
    console.log('\n🌐 可以访问以下URL测试聊天界面:')
    console.log(`   http://localhost:3000/chat/${order.id}`)

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testChatUpload()
