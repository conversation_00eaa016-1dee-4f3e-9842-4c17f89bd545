#!/usr/bin/env node

const fs = require('fs/promises')
const path = require('path')
const { spawn } = require('child_process')

class PerformanceRegressionDetector {
  constructor(options = {}) {
    this.baselineDir = options.baselineDir || 'test-results/performance/baselines'
    this.reportDir = options.reportDir || 'test-results/performance'
    this.thresholds = {
      warning: options.warningThreshold || 0.15, // 15%
      critical: options.criticalThreshold || 0.30, // 30%
      improvement: options.improvementThreshold || -0.10 // -10%
    }
    this.config = {
      testCommand: options.testCommand || 'npm run test:performance',
      baselineBranch: options.baselineBranch || 'main',
      currentBranch: options.currentBranch || 'HEAD',
      maxHistory: options.maxHistory || 50
    }
  }

  // 运行性能回归检测
  async runDetection() {
    console.log('🔍 开始性能回归检测...')
    
    try {
      // 1. 运行当前性能测试
      console.log('📊 运行当前性能测试...')
      const currentResults = await this.runPerformanceTests()
      
      // 2. 加载基线数据
      console.log('📋 加载基线数据...')
      const baselineResults = await this.loadBaseline()
      
      // 3. 比较性能数据
      console.log('🔄 比较性能数据...')
      const comparison = await this.compareResults(currentResults, baselineResults)
      
      // 4. 生成回归报告
      console.log('📝 生成回归报告...')
      const report = await this.generateRegressionReport(comparison)
      
      // 5. 保存结果
      await this.saveResults(currentResults, comparison, report)
      
      // 6. 检查是否需要告警
      const shouldAlert = this.shouldTriggerAlert(comparison)
      
      console.log('✅ 性能回归检测完成')
      console.log(`📊 检测到 ${comparison.regressions.length} 个回归`)
      console.log(`📈 检测到 ${comparison.improvements.length} 个改进`)
      
      if (shouldAlert) {
        console.log('🚨 发现严重性能回归，建议立即处理')
        await this.sendAlert(comparison)
      }
      
      return {
        success: true,
        regressions: comparison.regressions.length,
        improvements: comparison.improvements.length,
        shouldAlert,
        reportPath: path.join(this.reportDir, 'regression-report.html')
      }
      
    } catch (error) {
      console.error('❌ 性能回归检测失败:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 运行性能测试
  async runPerformanceTests() {
    return new Promise((resolve, reject) => {
      const child = spawn('npm', ['run', 'test:performance'], {
        stdio: 'pipe',
        shell: true
      })

      let stdout = ''
      let stderr = ''

      child.stdout.on('data', (data) => {
        stdout += data.toString()
      })

      child.stderr.on('data', (data) => {
        stderr += data.toString()
      })

      child.on('close', async (code) => {
        if (code !== 0) {
          reject(new Error(`性能测试失败: ${stderr}`))
          return
        }

        try {
          // 尝试加载测试结果
          const resultsPath = path.join(this.reportDir, 'performance-report.json')
          const results = await this.loadJsonFile(resultsPath)
          resolve(results)
        } catch (error) {
          reject(new Error(`无法加载测试结果: ${error.message}`))
        }
      })
    })
  }

  // 加载基线数据
  async loadBaseline() {
    try {
      const baselinePath = path.join(this.baselineDir, 'baseline.json')
      return await this.loadJsonFile(baselinePath)
    } catch (error) {
      console.warn('⚠️  无法加载基线数据，将使用当前结果作为基线')
      return null
    }
  }

  // 比较性能结果
  async compareResults(current, baseline) {
    if (!baseline) {
      return {
        regressions: [],
        improvements: [],
        stable: [],
        newTests: Object.keys(current.benchmarks || {})
      }
    }

    const regressions = []
    const improvements = []
    const stable = []
    const newTests = []

    const currentBenchmarks = current.benchmarks || {}
    const baselineBenchmarks = baseline.benchmarks || {}

    for (const [testName, currentData] of Object.entries(currentBenchmarks)) {
      const baselineData = baselineBenchmarks[testName]
      
      if (!baselineData) {
        newTests.push(testName)
        continue
      }

      const currentAvg = currentData.summary.avgDuration
      const baselineAvg = baselineData.summary.avgDuration
      const change = (currentAvg - baselineAvg) / baselineAvg

      const comparison = {
        testName,
        current: currentAvg,
        baseline: baselineAvg,
        change,
        changePercent: change * 100,
        status: 'stable'
      }

      if (change > this.thresholds.critical) {
        comparison.status = 'critical'
        comparison.severity = 'critical'
        regressions.push(comparison)
      } else if (change > this.thresholds.warning) {
        comparison.status = 'warning'
        comparison.severity = 'warning'
        regressions.push(comparison)
      } else if (change < this.thresholds.improvement) {
        comparison.status = 'improvement'
        improvements.push(comparison)
      } else {
        stable.push(comparison)
      }
    }

    return {
      regressions,
      improvements,
      stable,
      newTests,
      summary: {
        totalTests: Object.keys(currentBenchmarks).length,
        regressionCount: regressions.length,
        improvementCount: improvements.length,
        stableCount: stable.length,
        newTestCount: newTests.length
      }
    }
  }

  // 生成回归报告
  async generateRegressionReport(comparison) {
    const timestamp = new Date().toISOString()
    
    const report = {
      timestamp,
      summary: comparison.summary,
      regressions: comparison.regressions,
      improvements: comparison.improvements,
      stable: comparison.stable,
      newTests: comparison.newTests,
      thresholds: this.thresholds,
      recommendations: this.generateRecommendations(comparison)
    }

    // 生成HTML报告
    const htmlReport = this.generateHtmlReport(report)
    const htmlPath = path.join(this.reportDir, 'regression-report.html')
    await fs.writeFile(htmlPath, htmlReport)

    // 生成JSON报告
    const jsonPath = path.join(this.reportDir, 'regression-report.json')
    await fs.writeFile(jsonPath, JSON.stringify(report, null, 2))

    return report
  }

  // 生成优化建议
  generateRecommendations(comparison) {
    const recommendations = []

    if (comparison.regressions.length > 0) {
      const criticalRegressions = comparison.regressions.filter(r => r.severity === 'critical')
      
      if (criticalRegressions.length > 0) {
        recommendations.push({
          type: 'critical',
          message: `发现 ${criticalRegressions.length} 个严重性能回归，建议立即回滚或修复`,
          tests: criticalRegressions.map(r => r.testName)
        })
      }

      const warningRegressions = comparison.regressions.filter(r => r.severity === 'warning')
      if (warningRegressions.length > 0) {
        recommendations.push({
          type: 'warning',
          message: `发现 ${warningRegressions.length} 个性能回归，建议在下次发布前优化`,
          tests: warningRegressions.map(r => r.testName)
        })
      }
    }

    if (comparison.improvements.length > 0) {
      recommendations.push({
        type: 'info',
        message: `发现 ${comparison.improvements.length} 个性能改进，可以更新基线数据`,
        tests: comparison.improvements.map(r => r.testName)
      })
    }

    if (comparison.newTests.length > 0) {
      recommendations.push({
        type: 'info',
        message: `发现 ${comparison.newTests.length} 个新的性能测试，建议建立基线`,
        tests: comparison.newTests
      })
    }

    return recommendations
  }

  // 生成HTML报告
  generateHtmlReport(report) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能回归检测报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .metric { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .metric h3 { margin: 0; font-size: 2em; }
        .metric.critical { border-left: 4px solid #dc3545; }
        .metric.warning { border-left: 4px solid #ffc107; }
        .metric.improvement { border-left: 4px solid #28a745; }
        .metric.stable { border-left: 4px solid #6c757d; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .regression { background-color: #f8d7da; }
        .improvement { background-color: #d4edda; }
        .recommendation { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .recommendation.critical { background: #f8d7da; border-left: 4px solid #dc3545; }
        .recommendation.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .recommendation.info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 性能回归检测报告</h1>
            <p>生成时间: ${report.timestamp}</p>
        </div>
        
        <div class="card">
            <h2>📊 检测摘要</h2>
            <div class="summary">
                <div class="metric">
                    <h3>${report.summary.totalTests}</h3>
                    <p>总测试数</p>
                </div>
                <div class="metric critical">
                    <h3>${report.summary.regressionCount}</h3>
                    <p>性能回归</p>
                </div>
                <div class="metric improvement">
                    <h3>${report.summary.improvementCount}</h3>
                    <p>性能改进</p>
                </div>
                <div class="metric stable">
                    <h3>${report.summary.stableCount}</h3>
                    <p>性能稳定</p>
                </div>
            </div>
        </div>
        
        ${report.recommendations.length > 0 ? `
        <div class="card">
            <h2>💡 优化建议</h2>
            ${report.recommendations.map(rec => `
                <div class="recommendation ${rec.type}">
                    <strong>${rec.type.toUpperCase()}</strong>: ${rec.message}
                    ${rec.tests ? `<br><small>涉及测试: ${rec.tests.join(', ')}</small>` : ''}
                </div>
            `).join('')}
        </div>
        ` : ''}
        
        ${report.regressions.length > 0 ? `
        <div class="card">
            <h2>🔴 性能回归详情</h2>
            <table>
                <thead>
                    <tr>
                        <th>测试名称</th>
                        <th>当前性能 (ms)</th>
                        <th>基线性能 (ms)</th>
                        <th>变化</th>
                        <th>严重程度</th>
                    </tr>
                </thead>
                <tbody>
                    ${report.regressions.map(reg => `
                        <tr class="regression">
                            <td>${reg.testName}</td>
                            <td>${reg.current.toFixed(2)}</td>
                            <td>${reg.baseline.toFixed(2)}</td>
                            <td>+${reg.changePercent.toFixed(1)}%</td>
                            <td>${reg.severity}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}
        
        ${report.improvements.length > 0 ? `
        <div class="card">
            <h2>🟢 性能改进详情</h2>
            <table>
                <thead>
                    <tr>
                        <th>测试名称</th>
                        <th>当前性能 (ms)</th>
                        <th>基线性能 (ms)</th>
                        <th>改进</th>
                    </tr>
                </thead>
                <tbody>
                    ${report.improvements.map(imp => `
                        <tr class="improvement">
                            <td>${imp.testName}</td>
                            <td>${imp.current.toFixed(2)}</td>
                            <td>${imp.baseline.toFixed(2)}</td>
                            <td>${Math.abs(imp.changePercent).toFixed(1)}%</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}
    </div>
</body>
</html>
    `
  }

  // 保存结果
  async saveResults(currentResults, comparison, report) {
    // 确保目录存在
    await fs.mkdir(this.reportDir, { recursive: true })
    await fs.mkdir(this.baselineDir, { recursive: true })

    // 保存当前结果作为历史记录
    const historyPath = path.join(this.reportDir, `history-${Date.now()}.json`)
    await fs.writeFile(historyPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      results: currentResults,
      comparison,
      report
    }, null, 2))

    // 如果没有严重回归，更新基线
    const hasCriticalRegressions = comparison.regressions.some(r => r.severity === 'critical')
    if (!hasCriticalRegressions) {
      const baselinePath = path.join(this.baselineDir, 'baseline.json')
      await fs.writeFile(baselinePath, JSON.stringify(currentResults, null, 2))
      console.log('📊 基线数据已更新')
    }
  }

  // 检查是否需要告警
  shouldTriggerAlert(comparison) {
    return comparison.regressions.some(r => r.severity === 'critical')
  }

  // 发送告警
  async sendAlert(comparison) {
    const criticalRegressions = comparison.regressions.filter(r => r.severity === 'critical')
    
    console.log('🚨 性能告警:')
    criticalRegressions.forEach(reg => {
      console.log(`   ${reg.testName}: ${reg.changePercent.toFixed(1)}% 回归`)
    })

    // 这里可以集成邮件、Slack、钉钉等告警系统
    // 示例：发送到Webhook
    if (process.env.ALERT_WEBHOOK_URL) {
      try {
        const message = {
          text: `🚨 BitMarket性能告警: 发现${criticalRegressions.length}个严重性能回归`,
          attachments: criticalRegressions.map(reg => ({
            color: 'danger',
            fields: [{
              title: reg.testName,
              value: `性能回归 ${reg.changePercent.toFixed(1)}%`,
              short: true
            }]
          }))
        }

        // 发送Webhook请求的代码...
        console.log('📤 告警已发送到Webhook')
      } catch (error) {
        console.error('❌ 发送告警失败:', error.message)
      }
    }
  }

  // 工具方法：加载JSON文件
  async loadJsonFile(filePath) {
    const data = await fs.readFile(filePath, 'utf-8')
    return JSON.parse(data)
  }
}

// 命令行接口
if (require.main === module) {
  const detector = new PerformanceRegressionDetector()
  
  detector.runDetection()
    .then(result => {
      if (result.success) {
        console.log('✅ 性能回归检测完成')
        process.exit(0)
      } else {
        console.error('❌ 性能回归检测失败')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('❌ 执行失败:', error)
      process.exit(1)
    })
}

module.exports = { PerformanceRegressionDetector }
