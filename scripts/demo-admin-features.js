const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function demoAdminFeatures() {
  try {
    console.log('🎬 BitMarket 管理员功能演示')
    console.log('=' .repeat(50))

    // 获取测试数据
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })
    
    const order = await prisma.order.findFirst({
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    if (!admin || !order) {
      console.log('❌ 缺少测试数据')
      return
    }

    console.log(`📋 演示订单: ${order.id}`)
    console.log(`📱 商品: ${order.product.title}`)
    console.log(`💰 金额: ${order.totalAmount} USDT`)
    console.log(`👤 管理员: ${admin.name || admin.email}`)
    console.log('')

    // 演示场景：处理一个问题订单
    console.log('📖 演示场景: 处理一个有争议的订单')
    console.log('-'.repeat(30))

    // 1. 管理员添加初始备注
    console.log('\n📝 步骤1: 管理员添加初始处理备注')
    const note1 = await prisma.adminNote.create({
      data: {
        orderId: order.id,
        adminId: admin.id,
        content: '收到买家投诉，商品与描述不符。开始调查处理。',
        isPrivate: true
      },
      include: {
        admin: {
          select: { name: true, email: true }
        }
      }
    })

    // 记录备注添加日志
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: admin.id,
        action: 'ADMIN_NOTE_ADDED',
        description: '管理员添加备注: 收到买家投诉，商品与描述不符...',
        newValue: JSON.stringify({ noteId: note1.id, content: note1.content })
      }
    })

    console.log(`✅ 备注已添加: "${note1.content}"`)
    console.log(`   创建者: ${note1.admin.name || note1.admin.email}`)
    console.log(`   时间: ${new Date(note1.createdAt).toLocaleString()}`)

    // 2. 调整卖家信用分
    console.log('\n⭐ 步骤2: 调整卖家信用分（因商品描述不准确）')
    const originalSellerCredit = order.seller.creditScore
    const newSellerCredit = Math.max(0, originalSellerCredit - 10)

    const updatedSeller = await prisma.user.update({
      where: { id: order.seller.id },
      data: { creditScore: newSellerCredit }
    })

    // 记录信用分调整日志
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: admin.id,
        action: 'CREDIT_ADJUSTED',
        description: `调整卖家信用分: ${order.seller.name || order.seller.email} (${originalSellerCredit} → ${newSellerCredit})，原因: 商品描述不准确`,
        oldValue: JSON.stringify({ userId: order.seller.id, creditScore: originalSellerCredit }),
        newValue: JSON.stringify({ userId: order.seller.id, creditScore: newSellerCredit, reason: '商品描述不准确' })
      }
    })

    console.log(`✅ 卖家信用分已调整: ${originalSellerCredit} → ${newSellerCredit}`)
    console.log(`   原因: 商品描述不准确`)

    // 3. 添加处理进展备注
    console.log('\n📝 步骤3: 添加处理进展备注')
    const note2 = await prisma.adminNote.create({
      data: {
        orderId: order.id,
        adminId: admin.id,
        content: '已联系卖家核实情况，卖家承认商品描述有误。已扣除卖家10分信用分。',
        isPrivate: true
      },
      include: {
        admin: {
          select: { name: true, email: true }
        }
      }
    })

    // 记录备注添加日志
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: admin.id,
        action: 'ADMIN_NOTE_ADDED',
        description: '管理员添加备注: 已联系卖家核实情况，卖家承认商品描述有误...',
        newValue: JSON.stringify({ noteId: note2.id, content: note2.content })
      }
    })

    console.log(`✅ 进展备注已添加: "${note2.content}"`)

    // 4. 手动处理订单状态
    console.log('\n🔧 步骤4: 手动处理订单（同意退款）')
    
    const originalStatus = order.status
    const updatedOrder = await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'CANCELLED',
        refundAmount: order.totalAmount - order.platformFee
      }
    })

    // 记录订单状态变更日志
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: admin.id,
        action: 'APPROVE_REFUND',
        description: '管理员同意退款',
        oldValue: JSON.stringify({ status: originalStatus }),
        newValue: JSON.stringify({ status: 'CANCELLED', refundAmount: updatedOrder.refundAmount })
      }
    })

    console.log(`✅ 订单状态已更新: ${originalStatus} → CANCELLED`)
    console.log(`   退款金额: ${updatedOrder.refundAmount} USDT`)

    // 5. 添加最终处理备注
    console.log('\n📝 步骤5: 添加最终处理结果备注')
    const note3 = await prisma.adminNote.create({
      data: {
        orderId: order.id,
        adminId: admin.id,
        content: `争议处理完成。已同意买家退款 ${updatedOrder.refundAmount} USDT，扣除卖家信用分10分。案件结案。`,
        isPrivate: true
      },
      include: {
        admin: {
          select: { name: true, email: true }
        }
      }
    })

    // 记录备注添加日志
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: admin.id,
        action: 'ADMIN_NOTE_ADDED',
        description: '管理员添加备注: 争议处理完成。已同意买家退款...',
        newValue: JSON.stringify({ noteId: note3.id, content: note3.content })
      }
    })

    console.log(`✅ 最终备注已添加: "${note3.content}"`)

    // 6. 展示完整的处理记录
    console.log('\n📊 步骤6: 查看完整的处理记录')
    console.log('-'.repeat(30))

    // 获取所有备注
    const allNotes = await prisma.adminNote.findMany({
      where: { orderId: order.id },
      include: {
        admin: {
          select: { name: true, email: true }
        }
      },
      orderBy: { createdAt: 'asc' }
    })

    console.log(`\n📝 管理员备注记录 (${allNotes.length}条):`)
    allNotes.forEach((note, index) => {
      console.log(`${index + 1}. [${new Date(note.createdAt).toLocaleString()}] ${note.admin.name || note.admin.email}`)
      console.log(`   ${note.content}`)
    })

    // 获取所有操作日志
    const allLogs = await prisma.orderLog.findMany({
      where: { orderId: order.id },
      include: {
        operator: {
          select: { name: true, email: true, role: true }
        }
      },
      orderBy: { createdAt: 'asc' }
    })

    console.log(`\n📊 操作日志记录 (${allLogs.length}条):`)
    allLogs.forEach((log, index) => {
      const operatorName = log.operator ? (log.operator.name || log.operator.email) : '系统'
      console.log(`${index + 1}. [${new Date(log.createdAt).toLocaleString()}] ${log.action}`)
      console.log(`   操作者: ${operatorName}`)
      console.log(`   描述: ${log.description}`)
    })

    // 7. 恢复测试数据
    console.log('\n🔄 步骤7: 恢复测试数据')
    
    // 恢复订单状态
    await prisma.order.update({
      where: { id: order.id },
      data: {
        status: originalStatus,
        refundAmount: null
      }
    })

    // 恢复卖家信用分
    await prisma.user.update({
      where: { id: order.seller.id },
      data: { creditScore: originalSellerCredit }
    })

    // 清理演示数据
    await prisma.adminNote.deleteMany({
      where: { 
        orderId: order.id,
        content: { contains: '收到买家投诉' }
      }
    })

    await prisma.orderLog.deleteMany({
      where: { 
        orderId: order.id,
        description: { contains: '管理员添加备注: 收到买家投诉' }
      }
    })

    console.log(`✅ 测试数据已恢复`)

    console.log('\n🎉 管理员功能演示完成！')
    console.log('=' .repeat(50))
    console.log('✅ 演示功能总结:')
    console.log('   📝 备注管理 - 完整的备注添加、查看、历史记录')
    console.log('   📊 操作日志 - 自动记录所有管理员操作')
    console.log('   ⭐ 信用分调整 - 灵活的用户信用分管理')
    console.log('   🔧 订单处理 - 完整的订单状态管理')
    console.log('   🔍 数据追踪 - 完整的操作审计轨迹')

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

demoAdminFeatures()
