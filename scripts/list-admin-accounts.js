const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function listAdminAccounts() {
  console.log('👑 管理员账号列表\n')

  try {
    // 获取所有管理员用户
    const adminUsers = await prisma.user.findMany({
      where: {
        role: 'ADMIN'
      },
      select: {
        id: true,
        userId: true,
        email: true,
        name: true,
        role: true,
        status: true,
        creditScore: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,

        binanceUid: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    if (adminUsers.length === 0) {
      console.log('❌ 系统中没有管理员账号')
      console.log('💡 请使用以下命令创建管理员账号:')
      console.log('   node scripts/create-admin-account.js')
      console.log('   node scripts/create-custom-admin.js')
      return
    }

    console.log(`✅ 找到 ${adminUsers.length} 个管理员账号:\n`)

    adminUsers.forEach((admin, index) => {
      console.log(`${index + 1}. 管理员信息:`)
      console.log(`   📧 邮箱: ${admin.email}`)
      console.log(`   👤 姓名: ${admin.name || '未设置'}`)
      console.log(`   🆔 用户ID: ${admin.userId || '未设置'}`)
      console.log(`   🔑 数据库ID: ${admin.id}`)
      console.log(`   📊 角色: ${admin.role}`)
      console.log(`   📈 状态: ${admin.status}`)
      console.log(`   ⭐ 信用分: ${admin.creditScore}`)
      console.log(`   ✅ 邮箱验证: ${admin.emailVerified ? '已验证' : '未验证'}`)
      console.log(`   💰 币安UID: ${admin.binanceUid || '未绑定'}`)
      console.log(`   📅 创建时间: ${admin.createdAt.toLocaleString('zh-CN')}`)
      console.log(`   🔄 更新时间: ${admin.updatedAt.toLocaleString('zh-CN')}`)
      console.log(`   🕐 最后登录: 从未登录`)
      
      if (index < adminUsers.length - 1) {
        console.log('\n' + '─'.repeat(50) + '\n')
      }
    })

    // 统计信息
    console.log('\n' + '═'.repeat(50))
    console.log('📊 统计信息:')
    console.log(`   👑 管理员总数: ${adminUsers.length}`)
    
    const activeAdmins = adminUsers.filter(admin => admin.status === 'ACTIVE')
    console.log(`   ✅ 活跃管理员: ${activeAdmins.length}`)
    
    const verifiedAdmins = adminUsers.filter(admin => admin.emailVerified)
    console.log(`   📧 已验证邮箱: ${verifiedAdmins.length}`)
    
    const recentAdmins = adminUsers.filter(admin => {
      const daysDiff = (new Date() - admin.createdAt) / (1000 * 60 * 60 * 24)
      return daysDiff <= 7
    })
    console.log(`   🆕 近7天创建: ${recentAdmins.length}`)

    // 获取总用户数
    const totalUsers = await prisma.user.count()
    console.log(`   👥 总用户数: ${totalUsers}`)
    console.log(`   📊 管理员占比: ${((adminUsers.length / totalUsers) * 100).toFixed(2)}%`)

    console.log('\n🔗 登录信息:')
    console.log('   登录页面: http://localhost:3000/auth/signin')
    console.log('   管理后台: http://localhost:3000/admin')

  } catch (error) {
    console.error('❌ 获取管理员账号列表失败:', error)
    
    if (error.code === 'P1001') {
      console.log('💡 提示: 数据库连接失败，请检查数据库是否正常运行')
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
if (require.main === module) {
  listAdminAccounts().catch(console.error)
}

module.exports = { listAdminAccounts }
