const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanupAllTestData() {
  console.log('🧹 开始清理所有测试数据...')
  
  try {
    // 首先获取要删除的商品ID
    const testProducts = await prisma.product.findMany({
      where: {
        title: {
          startsWith: '测试商品'
        }
      },
      select: { id: true }
    })
    const testProductIds = testProducts.map(p => p.id)

    // 1. 删除测试订单日志
    console.log('🗑️ 删除测试订单日志...')
    const deletedOrderLogs = await prisma.orderLog.deleteMany({
      where: {
        OR: [
          { description: { contains: '测试' } },
          { description: { contains: 'TEST' } },
          { description: { contains: 'SF1234567890' } }
        ]
      }
    })
    console.log(`✅ 删除了 ${deletedOrderLogs.count} 个订单日志`)

    // 2. 删除测试收藏记录
    console.log('🗑️ 删除测试收藏记录...')
    const deletedFavorites = await prisma.favorite.deleteMany({
      where: {
        productId: {
          in: testProductIds
        }
      }
    })
    console.log(`✅ 删除了 ${deletedFavorites.count} 个收藏记录`)

    // 3. 删除测试评论
    console.log('🗑️ 删除测试评论...')
    const deletedReviews = await prisma.review.deleteMany({
      where: {
        productId: {
          in: testProductIds
        }
      }
    })
    console.log(`✅ 删除了 ${deletedReviews.count} 个评论`)

    // 4. 删除测试商品变体
    console.log('🗑️ 删除测试商品变体...')
    const deletedVariants = await prisma.productVariant.deleteMany({
      where: {
        productId: {
          in: testProductIds
        }
      }
    })
    console.log(`✅ 删除了 ${deletedVariants.count} 个商品变体`)

    // 5. 删除测试订单项
    console.log('🗑️ 删除测试订单项...')
    const deletedOrderItems = await prisma.orderItem.deleteMany({
      where: {
        productId: {
          in: testProductIds
        }
      }
    })
    console.log(`✅ 删除了 ${deletedOrderItems.count} 个订单项`)

    // 6. 删除测试订单
    console.log('🗑️ 删除测试订单...')
    const deletedOrders = await prisma.order.deleteMany({
      where: {
        OR: [
          { orderNumber: { startsWith: 'TEST-' } },
          { orderNumber: { startsWith: 'cmdr' } },
          { productId: { in: testProductIds } }
        ]
      }
    })
    console.log(`✅ 删除了 ${deletedOrders.count} 个测试订单`)

    // 7. 删除测试商品
    console.log('🗑️ 删除测试商品...')
    const deletedProducts = await prisma.product.deleteMany({
      where: {
        title: {
          startsWith: '测试商品'
        }
      }
    })
    console.log(`✅ 删除了 ${deletedProducts.count} 个测试商品`)
    
    // 8. 删除测试地址
    console.log('🗑️ 删除测试地址...')
    const deletedAddresses = await prisma.address.deleteMany({
      where: {
        OR: [
          { name: '张三' },
          { phone: '13800138000' }
        ]
      }
    })
    console.log(`✅ 删除了 ${deletedAddresses.count} 个测试地址`)
    
    // 9. 重置用户余额
    console.log('💰 重置用户余额...')
    
    // 重置买家用户余额为0
    await prisma.user.updateMany({
      where: {
        email: '<EMAIL>'
      },
      data: {
        depositBalance: 0
      }
    })
    
    // 重置管理员余额为1000
    await prisma.user.updateMany({
      where: {
        email: '<EMAIL>'
      },
      data: {
        depositBalance: 1000
      }
    })
    
    // 重置中间人用户余额为0
    await prisma.user.updateMany({
      where: {
        email: '<EMAIL>'
      },
      data: {
        depositBalance: 0
      }
    })
    
    console.log('✅ 用户余额已重置')
    
    // 10. 验证清理结果
    
    console.log('🔍 验证清理结果...')
    
    const remainingProducts = await prisma.product.count({
      where: {
        title: {
          startsWith: '测试商品'
        }
      }
    })
    
    const remainingOrders = await prisma.order.count({
      where: {
        OR: [
          { orderNumber: { startsWith: 'TEST-' } },
          { orderNumber: { startsWith: 'cmdr' } }
        ]
      }
    })
    
    const remainingAddresses = await prisma.address.count({
      where: {
        OR: [
          { name: '张三' },
          { phone: '13800138000' }
        ]
      }
    })
    
    const userBalances = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      },
      select: {
        email: true,
        depositBalance: true
      }
    })
    
    console.log('\n📋 清理结果摘要:')
    console.log(`- 剩余测试商品: ${remainingProducts} 个`)
    console.log(`- 剩余测试订单: ${remainingOrders} 个`)
    console.log(`- 剩余测试地址: ${remainingAddresses} 个`)
    console.log('- 用户余额状态:')
    userBalances.forEach(user => {
      console.log(`  ${user.email}: ${user.depositBalance} USDT`)
    })
    
    if (remainingProducts === 0 && remainingOrders === 0 && remainingAddresses === 0) {
      console.log('\n🎉 所有测试数据清理完成！系统已恢复到初始状态。')
    } else {
      console.log('\n⚠️ 部分测试数据可能未完全清理，请检查。')
    }
    
  } catch (error) {
    console.error('❌ 清理测试数据时发生错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行清理脚本
cleanupAllTestData()
