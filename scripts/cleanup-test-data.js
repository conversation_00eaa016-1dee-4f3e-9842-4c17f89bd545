const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanupTestData() {
  console.log('🧹 开始清理测试数据...')
  
  try {
    // 1. 删除测试订单
    console.log('🗑️ 删除测试订单...')
    const deletedOrders = await prisma.order.deleteMany({
      where: {
        OR: [
          { buyerId: { contains: 'cmdref' } },
          { sellerId: { contains: 'cmdref' } }
        ]
      }
    })
    console.log(`✅ 删除了 ${deletedOrders.count} 个测试订单`)
    
    // 2. 删除测试商品
    console.log('🗑️ 删除测试商品...')
    const deletedProducts = await prisma.product.deleteMany({
      where: {
        title: {
          startsWith: '测试商品'
        }
      }
    })
    console.log(`✅ 删除了 ${deletedProducts.count} 个测试商品`)
    
    // 3. 重置用户余额（除了管理员保持1000）
    console.log('💰 重置用户余额...')
    
    // 重置买家用户余额为0
    await prisma.user.updateMany({
      where: {
        email: '<EMAIL>'
      },
      data: {
        depositBalance: 0
      }
    })
    
    // 重置管理员余额为1000
    await prisma.user.updateMany({
      where: {
        email: '<EMAIL>'
      },
      data: {
        depositBalance: 1000
      }
    })
    
    // 重置中间人用户余额为0
    await prisma.user.updateMany({
      where: {
        email: '<EMAIL>'
      },
      data: {
        depositBalance: 0
      }
    })
    
    console.log('✅ 用户余额已重置')
    
    // 4. 删除测试收藏记录
    console.log('🗑️ 删除测试收藏记录...')
    const deletedFavorites = await prisma.favorite.deleteMany({
      where: {
        productId: {
          in: [] // 由于商品已删除，这里会自动清理
        }
      }
    })
    console.log(`✅ 删除了 ${deletedFavorites.count} 个收藏记录`)
    
    // 5. 删除测试评论
    console.log('🗑️ 删除测试评论...')
    const deletedReviews = await prisma.review.deleteMany({
      where: {
        productId: {
          in: [] // 由于商品已删除，这里会自动清理
        }
      }
    })
    console.log(`✅ 删除了 ${deletedReviews.count} 个评论`)
    
    // 6. 更新用户统计信息
    console.log('📊 更新用户统计信息...')
    const users = await prisma.user.findMany()
    
    for (const user of users) {
      // 重新计算用户的商品数量、订单数量等
      const productCount = await prisma.product.count({
        where: { sellerId: user.id }
      })
      
      const buyOrderCount = await prisma.order.count({
        where: { buyerId: user.id }
      })
      
      const sellOrderCount = await prisma.order.count({
        where: { sellerId: user.id }
      })
      
      await prisma.user.update({
        where: { id: user.id },
        data: {
          // 这里可以添加统计字段的更新，如果有的话
        }
      })
    }
    
    console.log('✅ 用户统计信息已更新')
    
    // 7. 验证清理结果
    console.log('🔍 验证清理结果...')
    const remainingProducts = await prisma.product.count({
      where: {
        title: {
          startsWith: '测试商品'
        }
      }
    })
    
    const remainingOrders = await prisma.order.count({
      where: {
        OR: [
          { buyerId: { contains: 'cmdref' } },
          { sellerId: { contains: 'cmdref' } }
        ]
      }
    })
    
    const userBalances = await prisma.user.findMany({
      select: {
        email: true,
        depositBalance: true
      }
    })
    
    console.log('\n📋 清理结果摘要:')
    console.log(`- 剩余测试商品: ${remainingProducts} 个`)
    console.log(`- 剩余测试订单: ${remainingOrders} 个`)
    console.log('- 用户余额状态:')
    userBalances.forEach(user => {
      console.log(`  ${user.email}: ${user.depositBalance} USDT`)
    })
    
    if (remainingProducts === 0 && remainingOrders === 0) {
      console.log('\n🎉 测试数据清理完成！系统已恢复到初始状态。')
    } else {
      console.log('\n⚠️ 部分测试数据可能未完全清理，请检查。')
    }
    
  } catch (error) {
    console.error('❌ 清理测试数据时发生错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行清理脚本
cleanupTestData()
