#!/usr/bin/env node

/**
 * BitMarket Release 自动化脚本 (Node.js版本)
 * 完整的release流程：版本更新、打包、git操作、release内容生成
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const readline = require('readline')

class ReleaseManager {
  constructor(options = {}) {
    this.options = {
      versionType: options.versionType || 'patch',
      skipTests: options.skipTests || false,
      skipGit: options.skipGit || false,
      dryRun: options.dryRun || false,
      releaseMessage: options.releaseMessage || ''
    }
    
    this.projectRoot = process.cwd()
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
  }

  // 颜色输出
  log(message, color = 'white') {
    const colors = {
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      cyan: '\x1b[36m',
      white: '\x1b[37m',
      reset: '\x1b[0m'
    }
    console.log(`${colors[color]}${message}${colors.reset}`)
  }

  step(message) {
    this.log(`\n🔄 ${message}`, 'cyan')
  }

  success(message) {
    this.log(`✅ ${message}`, 'green')
  }

  warning(message) {
    this.log(`⚠️  ${message}`, 'yellow')
  }

  error(message) {
    this.log(`❌ ${message}`, 'red')
  }

  // 异步输入
  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve)
    })
  }

  // 执行命令
  exec(command, options = {}) {
    try {
      return execSync(command, { 
        encoding: 'utf8', 
        stdio: options.silent ? 'pipe' : 'inherit',
        ...options 
      })
    } catch (error) {
      throw new Error(`命令执行失败: ${command}\n${error.message}`)
    }
  }

  // 检查Git状态
  async checkGitStatus() {
    this.step('检查Git工作目录状态...')
    
    try {
      const status = this.exec('git status --porcelain', { silent: true }).trim()
      
      if (status) {
        this.warning('工作目录有未提交的更改：')
        console.log(this.exec('git status --short', { silent: true }))
        
        const response = await this.question('\n是否继续？未提交的更改将被包含在release中 (y/N): ')
        if (response.toLowerCase() !== 'y') {
          this.error('Release已取消')
          process.exit(1)
        }
      } else {
        this.success('工作目录干净')
      }
    } catch (error) {
      this.error(`Git状态检查失败: ${error.message}`)
      process.exit(1)
    }
  }

  // 版本号处理
  getNextVersion(currentVersion, type) {
    const parts = currentVersion.split('.').map(Number)
    let [major, minor, patch] = parts

    switch (type) {
      case 'major':
        major++
        minor = 0
        patch = 0
        break
      case 'minor':
        minor++
        patch = 0
        break
      case 'patch':
        patch++
        break
      case 'current':
        // 保持当前版本
        break
    }

    return `${major}.${minor}.${patch}`
  }

  // 更新package.json版本
  updatePackageVersion(newVersion) {
    this.step(`更新package.json版本到 v${newVersion}...`)
    
    const packagePath = path.join(this.projectRoot, 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    
    packageJson.version = newVersion
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n')
    
    this.success('package.json版本已更新')
  }

  // 运行测试
  async runTests() {
    if (this.options.skipTests) {
      this.warning('跳过测试')
      return
    }

    this.step('运行项目测试...')
    
    try {
      this.exec('npm run test:ci')
      this.success('所有测试通过')
    } catch (error) {
      this.error(`测试失败: ${error.message}`)
      const response = await this.question('是否继续release？(y/N): ')
      if (response.toLowerCase() !== 'y') {
        process.exit(1)
      }
    }
  }

  // 构建项目
  buildProject() {
    this.step('构建项目...')
    
    try {
      this.exec('npm run build')
      this.success('项目构建成功')
    } catch (error) {
      this.error(`项目构建失败: ${error.message}`)
      process.exit(1)
    }
  }

  // 更新CHANGELOG
  updateChangelog(version, releaseMessage) {
    this.step('更新CHANGELOG.md...')
    
    const changelogPath = path.join(this.projectRoot, 'CHANGELOG.md')
    const date = new Date().toISOString().split('T')[0]
    
    const changelogEntry = `

## [${version}] - ${date}

### 🚀 新功能 | New Features
${releaseMessage}

### 🔧 技术改进 | Technical Improvements
- 版本更新到 v${version}
- 构建系统优化
- 依赖管理完善

---

`
    
    const changelog = fs.readFileSync(changelogPath, 'utf8')
    const updatedChangelog = changelog.replace(
      /(# 更新日志 \| Changelog\s+本文档记录了BitMarket项目的所有重要变更。)/,
      `$1${changelogEntry}`
    )
    
    fs.writeFileSync(changelogPath, updatedChangelog)
    this.success('CHANGELOG.md已更新')
  }

  // 创建发布包
  createReleasePackage() {
    this.step('创建发布包...')
    
    try {
      this.exec('node scripts/create-release-package.js')
      this.success('发布包创建成功')
    } catch (error) {
      this.error(`发布包创建失败: ${error.message}`)
      process.exit(1)
    }
  }

  // Git操作
  performGitOperations(version) {
    if (this.options.skipGit) {
      this.warning('跳过Git操作')
      return
    }

    this.step('执行Git操作...')
    
    try {
      // 添加所有更改
      this.exec('git add .')
      
      // 提交更改
      const commitMessage = `chore: release v${version}`
      this.exec(`git commit -m "${commitMessage}"`)
      
      // 创建标签
      const tagMessage = `Release v${version}`
      this.exec(`git tag -a "v${version}" -m "${tagMessage}"`)
      
      // 推送到远程
      this.exec('git push origin main')
      this.exec(`git push origin "v${version}"`)
      
      this.success('Git操作完成')
    } catch (error) {
      this.error(`Git操作失败: ${error.message}`)
      process.exit(1)
    }
  }

  // 生成Release Notes
  generateReleaseNotes(version) {
    this.step('生成Release Notes...')
    
    const date = new Date().toISOString().split('T')[0]
    const releaseNotes = `# BitMarket v${version} Release Notes

## 📋 版本信息
- **版本号**: v${version}
- **发布日期**: ${date}
- **构建状态**: ✅ 通过

## 🚀 主要更新

${this.options.releaseMessage}

## 📦 下载
- [源码包 (ZIP)](https://github.com/liusu-ally/bitmarket/releases/download/v${version}/bitmarket-v${version}-source.zip)
- [源码包 (TAR.GZ)](https://github.com/liusu-ally/bitmarket/releases/download/v${version}/bitmarket-v${version}-source.tar.gz)

## 🔧 安装说明
1. 下载并解压源码包
2. 运行 \`npm install\` 安装依赖
3. 配置环境变量（参考.env.example）
4. 运行 \`npm run build\` 构建项目
5. 运行 \`npm start\` 启动服务

## 📚 文档
- [安装指南](./INSTALL.md)
- [更新日志](./CHANGELOG.md)
- [项目文档](./README.md)

## 🆘 支持
如有问题，请访问 [GitHub Issues](https://github.com/liusu-ally/bitmarket/issues)
`
    
    const releaseDir = path.join(this.projectRoot, 'release')
    if (!fs.existsSync(releaseDir)) {
      fs.mkdirSync(releaseDir, { recursive: true })
    }
    
    const releaseNotesPath = path.join(releaseDir, `RELEASE_NOTES_v${version}.md`)
    fs.writeFileSync(releaseNotesPath, releaseNotes)
    
    this.success(`Release Notes已生成: ${releaseNotesPath}`)
  }

  // 主执行函数
  async run() {
    this.log('🚀 BitMarket Release 自动化脚本', 'green')
    this.log('=================================', 'green')
    
    // 检查是否在项目根目录
    if (!fs.existsSync(path.join(this.projectRoot, 'package.json'))) {
      this.error('请在项目根目录运行此脚本')
      process.exit(1)
    }
    
    // 读取当前版本
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const currentVersion = packageJson.version
    
    // 计算新版本
    const newVersion = this.getNextVersion(currentVersion, this.options.versionType)
    
    this.log('\n📋 Release信息:', 'cyan')
    this.log(`   当前版本: v${currentVersion}`, 'white')
    this.log(`   新版本: v${newVersion}`, 'white')
    this.log(`   版本类型: ${this.options.versionType}`, 'white')
    this.log(`   跳过测试: ${this.options.skipTests}`, 'white')
    this.log(`   跳过Git: ${this.options.skipGit}`, 'white')
    this.log(`   演练模式: ${this.options.dryRun}`, 'white')
    
    if (this.options.dryRun) {
      this.warning('演练模式：不会执行实际操作')
      this.rl.close()
      return
    }
    
    // 确认继续
    const response = await this.question('\n是否继续执行release？(y/N): ')
    if (response.toLowerCase() !== 'y') {
      this.warning('Release已取消')
      this.rl.close()
      process.exit(0)
    }
    
    try {
      // 执行release流程
      await this.checkGitStatus()
      
      if (this.options.versionType !== 'current') {
        this.updatePackageVersion(newVersion)
      }
      
      await this.runTests()
      this.buildProject()
      
      if (this.options.releaseMessage) {
        this.updateChangelog(newVersion, this.options.releaseMessage)
      }
      
      this.createReleasePackage()
      this.performGitOperations(newVersion)
      this.generateReleaseNotes(newVersion)
      
      this.log(`\n🎉 Release v${newVersion} 完成！`, 'green')
      this.log('📦 发布包位置: ./release/', 'cyan')
      this.log(`📝 Release Notes: ./release/RELEASE_NOTES_v${newVersion}.md`, 'cyan')
      
    } catch (error) {
      this.error(`Release失败: ${error.message}`)
      process.exit(1)
    } finally {
      this.rl.close()
    }
  }
}

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2)
  const options = {}
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--version-type':
        options.versionType = args[++i]
        break
      case '--skip-tests':
        options.skipTests = true
        break
      case '--skip-git':
        options.skipGit = true
        break
      case '--dry-run':
        options.dryRun = true
        break
      case '--message':
        options.releaseMessage = args[++i]
        break
      case '--help':
        console.log(`
BitMarket Release 自动化脚本

用法: node scripts/release.js [选项]

选项:
  --version-type <type>  版本类型 (major|minor|patch|current) [默认: patch]
  --skip-tests          跳过测试
  --skip-git            跳过Git操作
  --dry-run             演练模式，不执行实际操作
  --message <msg>       Release消息
  --help                显示帮助信息

示例:
  node scripts/release.js --version-type minor --message "新功能更新"
  node scripts/release.js --dry-run
        `)
        process.exit(0)
    }
  }
  
  return options
}

// 如果直接运行此脚本
if (require.main === module) {
  const options = parseArgs()
  const releaseManager = new ReleaseManager(options)
  releaseManager.run().catch(console.error)
}

module.exports = ReleaseManager
