#!/usr/bin/env node

/**
 * 数据库优化脚本
 * 分析查询模式，提供索引优化建议
 */

const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

// 分析结果
const analysisResult = {
  timestamp: new Date().toISOString(),
  recommendations: [],
  currentIndexes: [],
  queryPatterns: [],
  performanceIssues: []
}

// 分析现有索引
async function analyzeCurrentIndexes() {
  console.log('📊 分析现有索引...')
  
  // 从 schema.prisma 中提取索引信息
  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma')
  const schemaContent = fs.readFileSync(schemaPath, 'utf8')
  
  // 解析索引
  const indexMatches = schemaContent.match(/@@index\(\[([^\]]+)\]\)/g) || []
  const uniqueMatches = schemaContent.match(/@@unique\(\[([^\]]+)\]\)/g) || []
  
  analysisResult.currentIndexes = [
    ...indexMatches.map(match => ({
      type: 'index',
      fields: match.match(/\[([^\]]+)\]/)[1].split(',').map(f => f.trim())
    })),
    ...uniqueMatches.map(match => ({
      type: 'unique',
      fields: match.match(/\[([^\]]+)\]/)[1].split(',').map(f => f.trim())
    }))
  ]
  
  console.log(`✅ 发现 ${analysisResult.currentIndexes.length} 个现有索引`)
}

// 分析查询模式
async function analyzeQueryPatterns() {
  console.log('🔍 分析查询模式...')
  
  // 常见查询模式分析
  const queryPatterns = [
    {
      table: 'Product',
      description: '商品列表查询',
      fields: ['status', 'reviewStatus', 'city', 'category', 'condition', 'createdAt'],
      frequency: 'high',
      recommendation: '复合索引 (status, reviewStatus, city, createdAt)'
    },
    {
      table: 'Product',
      description: '商品搜索',
      fields: ['title'],
      frequency: 'medium',
      recommendation: '全文搜索索引或使用专门的搜索引擎'
    },
    {
      table: 'Order',
      description: '用户订单查询',
      fields: ['buyerId', 'sellerId', 'status', 'createdAt'],
      frequency: 'high',
      recommendation: '复合索引 (buyerId, status, createdAt) 和 (sellerId, status, createdAt)'
    },
    {
      table: 'Message',
      description: '订单消息查询',
      fields: ['orderId', 'createdAt'],
      frequency: 'high',
      recommendation: '复合索引 (orderId, createdAt) - 已存在'
    },
    {
      table: 'User',
      description: '用户认证查询',
      fields: ['email', 'userId'],
      frequency: 'high',
      recommendation: '唯一索引 - 已存在'
    },
    {
      table: 'Review',
      description: '评价查询',
      fields: ['orderId', 'reviewerId', 'revieweeId'],
      frequency: 'medium',
      recommendation: '复合索引 (revieweeId, createdAt)'
    },
    {
      table: 'Favorite',
      description: '收藏查询',
      fields: ['userId', 'productId', 'createdAt'],
      frequency: 'medium',
      recommendation: '复合索引 (userId, createdAt) - 已存在'
    },
    {
      table: 'Demand',
      description: '需求查询',
      fields: ['status', 'demandType', 'createdAt'],
      frequency: 'medium',
      recommendation: '复合索引 (status, createdAt) 和 (demandType, status) - 已存在'
    }
  ]
  
  analysisResult.queryPatterns = queryPatterns
  console.log(`✅ 分析了 ${queryPatterns.length} 种查询模式`)
}

// 生成索引优化建议
function generateIndexRecommendations() {
  console.log('💡 生成索引优化建议...')
  
  const recommendations = [
    {
      priority: 'high',
      table: 'Product',
      action: 'add_composite_index',
      fields: ['status', 'reviewStatus', 'city', 'createdAt'],
      reason: '商品列表查询的主要筛选条件',
      impact: '显著提升商品列表查询性能',
      sql: 'CREATE INDEX idx_product_list ON Product(status, reviewStatus, city, createdAt);'
    },
    {
      priority: 'high',
      table: 'Product',
      action: 'add_composite_index',
      fields: ['sellerId', 'status', 'createdAt'],
      reason: '用户商品查询',
      impact: '提升用户商品页面加载速度',
      sql: 'CREATE INDEX idx_product_seller ON Product(sellerId, status, createdAt);'
    },
    {
      priority: 'high',
      table: 'Order',
      action: 'add_composite_index',
      fields: ['buyerId', 'status', 'createdAt'],
      reason: '买家订单查询',
      impact: '提升用户订单页面性能',
      sql: 'CREATE INDEX idx_order_buyer ON "Order"(buyerId, status, createdAt);'
    },
    {
      priority: 'high',
      table: 'Order',
      action: 'add_composite_index',
      fields: ['sellerId', 'status', 'createdAt'],
      reason: '卖家订单查询',
      impact: '提升卖家订单管理性能',
      sql: 'CREATE INDEX idx_order_seller ON "Order"(sellerId, status, createdAt);'
    },
    {
      priority: 'medium',
      table: 'Product',
      action: 'add_fulltext_index',
      fields: ['title', 'description'],
      reason: '商品搜索功能',
      impact: '提升搜索性能和准确性',
      sql: '-- 考虑使用 PostgreSQL 的全文搜索或 Elasticsearch'
    },
    {
      priority: 'medium',
      table: 'Review',
      action: 'add_composite_index',
      fields: ['revieweeId', 'createdAt'],
      reason: '用户评价历史查询',
      impact: '提升用户信誉页面性能',
      sql: 'CREATE INDEX idx_review_reviewee ON Review(revieweeId, createdAt);'
    },
    {
      priority: 'medium',
      table: 'CreditHistory',
      action: 'add_composite_index',
      fields: ['userId', 'changeType', 'createdAt'],
      reason: '用户信用记录查询',
      impact: '提升信用历史查询性能',
      sql: 'CREATE INDEX idx_credit_history ON CreditHistory(userId, changeType, createdAt);'
    },
    {
      priority: 'low',
      table: 'SecurityLog',
      action: 'add_composite_index',
      fields: ['userId', 'action', 'createdAt'],
      reason: '安全日志查询',
      impact: '提升安全审计性能',
      sql: 'CREATE INDEX idx_security_log ON SecurityLog(userId, action, createdAt);'
    },
    {
      priority: 'low',
      table: 'UserSession',
      action: 'add_composite_index',
      fields: ['userId', 'isActive', 'lastActivity'],
      reason: '活跃会话查询',
      impact: '提升会话管理性能',
      sql: 'CREATE INDEX idx_user_session_active ON UserSession(userId, isActive, lastActivity);'
    }
  ]
  
  analysisResult.recommendations = recommendations
  console.log(`✅ 生成了 ${recommendations.length} 条优化建议`)
}

// 检查性能问题
async function checkPerformanceIssues() {
  console.log('⚠️  检查潜在性能问题...')
  
  const issues = []
  
  try {
    // 检查大表数据量
    const productCount = await prisma.product.count()
    const orderCount = await prisma.order.count()
    const messageCount = await prisma.message.count()
    
    if (productCount > 10000) {
      issues.push({
        type: 'large_table',
        table: 'Product',
        count: productCount,
        recommendation: '考虑数据分区或归档策略'
      })
    }
    
    if (orderCount > 50000) {
      issues.push({
        type: 'large_table',
        table: 'Order',
        count: orderCount,
        recommendation: '考虑按时间分区或历史数据归档'
      })
    }
    
    if (messageCount > 100000) {
      issues.push({
        type: 'large_table',
        table: 'Message',
        count: messageCount,
        recommendation: '考虑消息数据清理策略'
      })
    }
    
    // 检查缺失的外键索引
    issues.push({
      type: 'missing_fk_index',
      description: '某些外键字段可能缺少索引',
      recommendation: '为高频查询的外键字段添加索引'
    })
    
  } catch (error) {
    issues.push({
      type: 'query_error',
      error: error.message,
      recommendation: '检查数据库连接和权限'
    })
  }
  
  analysisResult.performanceIssues = issues
  console.log(`✅ 发现 ${issues.length} 个潜在问题`)
}

// 生成 Prisma 迁移文件
function generateMigrationFile() {
  console.log('📝 生成数据库迁移文件...')
  
  const highPriorityRecommendations = analysisResult.recommendations
    .filter(rec => rec.priority === 'high')
  
  if (highPriorityRecommendations.length === 0) {
    console.log('✅ 无需生成迁移文件')
    return
  }
  
  const migrationSQL = highPriorityRecommendations
    .map(rec => `-- ${rec.reason}\n${rec.sql}`)
    .join('\n\n')
  
  const migrationContent = `-- 性能优化索引迁移
-- 生成时间: ${new Date().toISOString()}

${migrationSQL}
`
  
  const migrationsDir = path.join(process.cwd(), 'prisma', 'migrations')
  const migrationName = `${Date.now()}_performance_indexes`
  const migrationDir = path.join(migrationsDir, migrationName)
  
  if (!fs.existsSync(migrationDir)) {
    fs.mkdirSync(migrationDir, { recursive: true })
  }
  
  const migrationFile = path.join(migrationDir, 'migration.sql')
  fs.writeFileSync(migrationFile, migrationContent)
  
  console.log(`✅ 迁移文件已生成: ${migrationFile}`)
}

// 生成优化报告
function generateOptimizationReport() {
  console.log('📊 生成优化报告...')
  
  const reportContent = `# 数据库性能优化报告

生成时间: ${analysisResult.timestamp}

## 📋 现有索引 (${analysisResult.currentIndexes.length})

${analysisResult.currentIndexes.map(idx => 
  `- ${idx.type.toUpperCase()}: [${idx.fields.join(', ')}]`
).join('\n')}

## 🎯 优化建议

### 高优先级 (${analysisResult.recommendations.filter(r => r.priority === 'high').length})

${analysisResult.recommendations
  .filter(r => r.priority === 'high')
  .map(rec => `
#### ${rec.table} - ${rec.action}
- **字段**: ${rec.fields.join(', ')}
- **原因**: ${rec.reason}
- **影响**: ${rec.impact}
- **SQL**: \`${rec.sql}\`
`).join('\n')}

### 中优先级 (${analysisResult.recommendations.filter(r => r.priority === 'medium').length})

${analysisResult.recommendations
  .filter(r => r.priority === 'medium')
  .map(rec => `
#### ${rec.table} - ${rec.action}
- **字段**: ${rec.fields.join(', ')}
- **原因**: ${rec.reason}
- **影响**: ${rec.impact}
`).join('\n')}

## ⚠️ 性能问题 (${analysisResult.performanceIssues.length})

${analysisResult.performanceIssues.map(issue => `
- **类型**: ${issue.type}
- **描述**: ${issue.description || issue.table}
- **建议**: ${issue.recommendation}
`).join('\n')}

## 📈 实施建议

1. **立即实施**: 高优先级索引优化
2. **短期计划**: 中优先级索引和查询优化
3. **长期规划**: 数据库架构升级和分区策略

## 🔧 实施步骤

1. 备份数据库
2. 在测试环境中应用迁移
3. 进行性能测试
4. 在生产环境中应用
5. 监控性能改善情况
`
  
  const reportsDir = path.join(process.cwd(), 'docs')
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true })
  }
  
  const reportPath = path.join(reportsDir, 'database-optimization-report.md')
  fs.writeFileSync(reportPath, reportContent)
  
  // 同时保存 JSON 格式
  const jsonReportPath = path.join(process.cwd(), 'logs', `db-analysis-${Date.now()}.json`)
  const logsDir = path.dirname(jsonReportPath)
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true })
  }
  fs.writeFileSync(jsonReportPath, JSON.stringify(analysisResult, null, 2))
  
  console.log(`✅ 优化报告已生成:`)
  console.log(`   - Markdown: ${reportPath}`)
  console.log(`   - JSON: ${jsonReportPath}`)
}

// 主函数
async function main() {
  console.log('🔧 开始数据库优化分析...\n')
  
  try {
    await analyzeCurrentIndexes()
    await analyzeQueryPatterns()
    generateIndexRecommendations()
    await checkPerformanceIssues()
    generateMigrationFile()
    generateOptimizationReport()
    
    console.log('\n🎉 数据库优化分析完成!')
    console.log('\n💡 下一步:')
    console.log('   1. 查看生成的优化报告')
    console.log('   2. 在测试环境中应用高优先级索引')
    console.log('   3. 进行性能测试验证')
    console.log('   4. 监控查询性能改善')
    
  } catch (error) {
    console.error('❌ 分析过程中出现错误:', error.message)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  analyzeCurrentIndexes,
  analyzeQueryPatterns,
  generateIndexRecommendations,
  checkPerformanceIssues,
  generateOptimizationReport,
  analysisResult
}
