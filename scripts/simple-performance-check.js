#!/usr/bin/env node

const { performance } = require('perf_hooks')
const { PrismaClient } = require('@prisma/client')
const http = require('http')

const prisma = new PrismaClient()

class SimplePerformanceCheck {
  constructor() {
    this.results = {
      timestamp: Date.now(),
      checks: {},
      summary: {}
    }
  }

  // 检查数据库性能
  async checkDatabasePerformance() {
    console.log('🗄️  检查数据库性能...')
    
    const tests = [
      {
        name: '用户查询',
        test: async () => {
          const start = performance.now()
          await prisma.user.findMany({ take: 5 })
          return performance.now() - start
        }
      },
      {
        name: '产品查询',
        test: async () => {
          const start = performance.now()
          await prisma.product.findMany({ 
            where: { status: 'AVAILABLE' },
            take: 5 
          })
          return performance.now() - start
        }
      },
      {
        name: '安全日志查询',
        test: async () => {
          const start = performance.now()
          await prisma.securityLog.findMany({ 
            take: 10,
            orderBy: { createdAt: 'desc' }
          })
          return performance.now() - start
        }
      }
    ]

    const dbResults = {}
    for (const test of tests) {
      try {
        const time = await test.test()
        dbResults[test.name] = {
          time: time,
          status: time < 50 ? 'excellent' : time < 100 ? 'good' : time < 200 ? 'fair' : 'poor'
        }
        console.log(`  ${test.name}: ${time.toFixed(2)}ms (${dbResults[test.name].status})`)
      } catch (error) {
        dbResults[test.name] = {
          time: null,
          status: 'error',
          error: error.message
        }
        console.log(`  ${test.name}: 错误 - ${error.message}`)
      }
    }

    this.results.checks.database = dbResults
    return dbResults
  }

  // 检查API性能
  async checkAPIPerformance() {
    console.log('\n🌐 检查API性能...')
    
    const apiTests = [
      {
        name: '健康检查',
        url: 'http://localhost:3000/api/health',
        timeout: 5000
      },
      {
        name: '性能监控API',
        url: 'http://localhost:3001/api/health',
        timeout: 3000
      }
    ]

    const apiResults = {}
    for (const test of apiTests) {
      try {
        const start = performance.now()
        const response = await this.makeRequest(test.url, test.timeout)
        const time = performance.now() - start
        
        apiResults[test.name] = {
          time: time,
          status: response.statusCode === 200 ? 'success' : 'error',
          statusCode: response.statusCode,
          performance: time < 100 ? 'excellent' : time < 500 ? 'good' : time < 1000 ? 'fair' : 'poor'
        }
        
        console.log(`  ${test.name}: ${time.toFixed(2)}ms (${response.statusCode}) - ${apiResults[test.name].performance}`)
      } catch (error) {
        apiResults[test.name] = {
          time: null,
          status: 'error',
          error: error.message,
          performance: 'error'
        }
        console.log(`  ${test.name}: 错误 - ${error.message}`)
      }
    }

    this.results.checks.api = apiResults
    return apiResults
  }

  // 检查系统资源
  checkSystemResources() {
    console.log('\n💾 检查系统资源...')
    
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    const systemInfo = {
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
        heapUsagePercent: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: Math.round(process.uptime()),
      nodeVersion: process.version,
      platform: process.platform
    }

    console.log(`  RSS内存: ${systemInfo.memory.rss}MB`)
    console.log(`  堆内存: ${systemInfo.memory.heapUsed}MB / ${systemInfo.memory.heapTotal}MB (${systemInfo.memory.heapUsagePercent}%)`)
    console.log(`  外部内存: ${systemInfo.memory.external}MB`)
    console.log(`  运行时间: ${systemInfo.uptime}秒`)
    console.log(`  Node.js版本: ${systemInfo.nodeVersion}`)
    console.log(`  平台: ${systemInfo.platform}`)

    // 评估系统状态
    let memoryStatus = 'good'
    if (systemInfo.memory.rss > 500) memoryStatus = 'poor'
    else if (systemInfo.memory.rss > 200) memoryStatus = 'fair'
    else if (systemInfo.memory.rss < 100) memoryStatus = 'excellent'

    systemInfo.status = {
      memory: memoryStatus,
      overall: memoryStatus
    }

    this.results.checks.system = systemInfo
    return systemInfo
  }

  // HTTP请求辅助函数
  makeRequest(url, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url)
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname,
        method: 'GET',
        timeout: timeout
      }

      const req = http.request(options, (res) => {
        let data = ''
        res.on('data', chunk => data += chunk)
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            data: data
          })
        })
      })

      req.on('error', reject)
      req.on('timeout', () => {
        req.destroy()
        reject(new Error('Request timeout'))
      })
      
      req.end()
    })
  }

  // 生成性能报告
  generateReport() {
    console.log('\n📊 性能检查报告')
    console.log('='.repeat(50))

    const { database, api, system } = this.results.checks

    // 数据库性能总结
    if (database) {
      console.log('\n🗄️  数据库性能:')
      Object.entries(database).forEach(([name, result]) => {
        if (result.time !== null) {
          const icon = result.status === 'excellent' ? '🟢' : 
                      result.status === 'good' ? '🟡' : 
                      result.status === 'fair' ? '🟠' : '🔴'
          console.log(`  ${icon} ${name}: ${result.time.toFixed(2)}ms`)
        } else {
          console.log(`  🔴 ${name}: 错误`)
        }
      })
    }

    // API性能总结
    if (api) {
      console.log('\n🌐 API性能:')
      Object.entries(api).forEach(([name, result]) => {
        if (result.time !== null) {
          const icon = result.performance === 'excellent' ? '🟢' : 
                      result.performance === 'good' ? '🟡' : 
                      result.performance === 'fair' ? '🟠' : '🔴'
          console.log(`  ${icon} ${name}: ${result.time.toFixed(2)}ms (${result.statusCode})`)
        } else {
          console.log(`  🔴 ${name}: 错误`)
        }
      })
    }

    // 系统资源总结
    if (system) {
      console.log('\n💾 系统资源:')
      const memIcon = system.status.memory === 'excellent' ? '🟢' : 
                     system.status.memory === 'good' ? '🟡' : 
                     system.status.memory === 'fair' ? '🟠' : '🔴'
      console.log(`  ${memIcon} 内存使用: ${system.memory.rss}MB (${system.status.memory})`)
      console.log(`  📊 堆使用率: ${system.memory.heapUsagePercent}%`)
    }

    // 总体评估
    const overallScore = this.calculateOverallScore()
    console.log(`\n🏆 总体性能评分: ${overallScore.score}/100 (${overallScore.grade})`)
    console.log(`📝 评估: ${overallScore.description}`)

    if (overallScore.recommendations.length > 0) {
      console.log('\n💡 优化建议:')
      overallScore.recommendations.forEach(rec => {
        console.log(`  • ${rec}`)
      })
    }

    this.results.summary = overallScore
    return this.results
  }

  // 计算总体评分
  calculateOverallScore() {
    let score = 100
    const recommendations = []

    const { database, api, system } = this.results.checks

    // 数据库性能评分
    if (database) {
      Object.values(database).forEach(result => {
        if (result.status === 'poor') score -= 15
        else if (result.status === 'fair') score -= 10
        else if (result.status === 'good') score -= 5
        else if (result.status === 'error') score -= 20
      })
    }

    // API性能评分
    if (api) {
      Object.values(api).forEach(result => {
        if (result.performance === 'poor') score -= 15
        else if (result.performance === 'fair') score -= 10
        else if (result.performance === 'good') score -= 5
        else if (result.performance === 'error') score -= 20
      })
    }

    // 系统资源评分
    if (system) {
      if (system.status.memory === 'poor') {
        score -= 20
        recommendations.push('内存使用过高，考虑优化内存使用或增加服务器内存')
      } else if (system.status.memory === 'fair') {
        score -= 10
        recommendations.push('内存使用较高，建议监控内存使用情况')
      }
    }

    // 确定等级
    let grade, description
    if (score >= 90) {
      grade = 'A+'
      description = '性能优秀，系统运行非常稳定'
    } else if (score >= 80) {
      grade = 'A'
      description = '性能良好，系统运行稳定'
    } else if (score >= 70) {
      grade = 'B'
      description = '性能一般，建议进行优化'
    } else if (score >= 60) {
      grade = 'C'
      description = '性能较差，需要优化'
    } else {
      grade = 'D'
      description = '性能很差，急需优化'
    }

    return {
      score: Math.max(0, score),
      grade,
      description,
      recommendations
    }
  }

  // 保存结果
  async saveResults() {
    try {
      const fs = require('fs').promises
      await fs.mkdir('test-results/performance', { recursive: true })
      
      const filename = `simple-performance-check-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.writeFile(
        `test-results/performance/${filename}`,
        JSON.stringify(this.results, null, 2)
      )
      
      console.log(`\n💾 检查结果已保存: test-results/performance/${filename}`)
    } catch (error) {
      console.error('保存结果失败:', error)
    }
  }
}

// 运行性能检查
async function runSimplePerformanceCheck() {
  const checker = new SimplePerformanceCheck()
  
  try {
    console.log('🔍 开始简单性能检查')
    console.log('='.repeat(50))
    
    await checker.checkDatabasePerformance()
    await checker.checkAPIPerformance()
    checker.checkSystemResources()
    
    const report = checker.generateReport()
    await checker.saveResults()
    
    console.log('\n✅ 性能检查完成')
    return report
    
  } catch (error) {
    console.error('❌ 性能检查失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 命令行运行
if (require.main === module) {
  runSimplePerformanceCheck()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { SimplePerformanceCheck, runSimplePerformanceCheck }
