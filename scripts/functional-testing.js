#!/usr/bin/env node

const { spawn } = require('child_process')
const http = require('http')
const fs = require('fs')

class FunctionalTester {
  constructor() {
    this.testResults = {
      build: null,
      server: null,
      pages: [],
      apis: [],
      database: null,
      performance: []
    }
    this.devServer = null
  }

  // 测试构建
  async testBuild() {
    console.log('🏗️  测试构建...')
    
    return new Promise((resolve) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        stdio: 'pipe',
        shell: process.platform === 'win32'
      })
      
      let output = ''
      let errorOutput = ''
      
      buildProcess.stdout.on('data', (data) => {
        output += data.toString()
      })
      
      buildProcess.stderr.on('data', (data) => {
        errorOutput += data.toString()
      })
      
      buildProcess.on('close', (code) => {
        const success = code === 0
        this.testResults.build = {
          success,
          exitCode: code,
          output: output.slice(-1000), // 保留最后1000字符
          errors: errorOutput.slice(-1000)
        }
        
        if (success) {
          console.log('  ✅ 构建成功')
        } else {
          console.log('  ❌ 构建失败')
          console.log('  错误:', errorOutput.slice(-200))
        }
        
        resolve(success)
      })
      
      buildProcess.on('error', (error) => {
        console.log('  ❌ 构建过程出错:', error.message)
        this.testResults.build = {
          success: false,
          error: error.message
        }
        resolve(false)
      })
      
      // 60秒超时
      setTimeout(() => {
        buildProcess.kill()
        console.log('  ⚠️  构建超时')
        this.testResults.build = {
          success: false,
          error: 'Build timeout'
        }
        resolve(false)
      }, 60000)
    })
  }

  // 启动开发服务器
  async startDevServer() {
    console.log('\n🚀 启动开发服务器...')
    
    return new Promise((resolve) => {
      const isWindows = process.platform === 'win32'
      const npmCommand = isWindows ? 'npm.cmd' : 'npm'
      
      this.devServer = spawn(npmCommand, ['run', 'dev'], {
        stdio: 'pipe',
        shell: isWindows
      })
      
      let serverReady = false
      
      this.devServer.stdout.on('data', (data) => {
        const output = data.toString()
        
        if (output.includes('Ready on http://localhost:3000') && !serverReady) {
          serverReady = true
          console.log('  ✅ 开发服务器已启动')
          this.testResults.server = {
            success: true,
            port: 3000
          }
          resolve(true)
        }
        
        if (output.includes('Error') || output.includes('Failed')) {
          console.log('  ⚠️  服务器输出错误:', output.slice(-200))
        }
      })
      
      this.devServer.stderr.on('data', (data) => {
        const errorOutput = data.toString()
        if (errorOutput.includes('Error') && !serverReady) {
          console.log('  ❌ 服务器启动错误:', errorOutput.slice(-200))
        }
      })
      
      this.devServer.on('error', (error) => {
        console.log('  ❌ 服务器启动失败:', error.message)
        this.testResults.server = {
          success: false,
          error: error.message
        }
        resolve(false)
      })
      
      // 30秒超时
      setTimeout(() => {
        if (!serverReady) {
          console.log('  ⚠️  服务器启动超时，但可能仍在启动中...')
          this.testResults.server = {
            success: false,
            error: 'Server start timeout'
          }
          resolve(false)
        }
      }, 30000)
    })
  }

  // 测试页面访问
  async testPages() {
    console.log('\n📄 测试页面访问...')
    
    const pages = [
      { path: '/', name: '首页' },
      { path: '/about', name: '关于页面' },
      { path: '/privacy', name: '隐私政策' },
      { path: '/terms', name: '服务条款' },
      { path: '/contact', name: '联系我们' },
      { path: '/help', name: '帮助页面' }
    ]
    
    for (const page of pages) {
      try {
        const result = await this.testPage(page.path, page.name)
        this.testResults.pages.push(result)
      } catch (error) {
        this.testResults.pages.push({
          path: page.path,
          name: page.name,
          success: false,
          error: error.message
        })
      }
    }
    
    const successCount = this.testResults.pages.filter(p => p.success).length
    console.log(`  📊 页面测试结果: ${successCount}/${pages.length} 成功`)
  }

  // 测试单个页面
  async testPage(path, name) {
    const startTime = Date.now()
    
    try {
      const response = await this.makeRequest(`http://localhost:3000${path}`, 10000)
      const responseTime = Date.now() - startTime
      
      const success = response.statusCode >= 200 && response.statusCode < 400
      const result = {
        path,
        name,
        success,
        statusCode: response.statusCode,
        responseTime,
        contentLength: response.data ? response.data.length : 0
      }
      
      if (success) {
        console.log(`  ✅ ${name}: ${response.statusCode} (${responseTime}ms)`)
      } else {
        console.log(`  ❌ ${name}: ${response.statusCode} (${responseTime}ms)`)
      }
      
      return result
    } catch (error) {
      console.log(`  ❌ ${name}: 错误 - ${error.message}`)
      return {
        path,
        name,
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      }
    }
  }

  // 测试API端点
  async testAPIs() {
    console.log('\n🌐 测试API端点...')
    
    const apis = [
      { path: '/api/health', name: '健康检查' },
      { path: '/api/auth/session', name: '会话检查' }
    ]
    
    for (const api of apis) {
      try {
        const result = await this.testAPI(api.path, api.name)
        this.testResults.apis.push(result)
      } catch (error) {
        this.testResults.apis.push({
          path: api.path,
          name: api.name,
          success: false,
          error: error.message
        })
      }
    }
    
    const successCount = this.testResults.apis.filter(a => a.success).length
    console.log(`  📊 API测试结果: ${successCount}/${apis.length} 成功`)
  }

  // 测试单个API
  async testAPI(path, name) {
    const startTime = Date.now()
    
    try {
      const response = await this.makeRequest(`http://localhost:3000${path}`, 5000)
      const responseTime = Date.now() - startTime
      
      const success = response.statusCode >= 200 && response.statusCode < 400
      const result = {
        path,
        name,
        success,
        statusCode: response.statusCode,
        responseTime,
        contentType: response.headers?.['content-type'] || 'unknown'
      }
      
      if (success) {
        console.log(`  ✅ ${name}: ${response.statusCode} (${responseTime}ms)`)
      } else {
        console.log(`  ❌ ${name}: ${response.statusCode} (${responseTime}ms)`)
      }
      
      return result
    } catch (error) {
      console.log(`  ❌ ${name}: 错误 - ${error.message}`)
      return {
        path,
        name,
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      }
    }
  }

  // 测试数据库连接
  async testDatabase() {
    console.log('\n🗄️  测试数据库连接...')
    
    try {
      // 尝试访问一个需要数据库的API
      const response = await this.makeRequest('http://localhost:3000/api/health', 5000)
      
      if (response.statusCode === 200) {
        console.log('  ✅ 数据库连接正常')
        this.testResults.database = {
          success: true,
          responseTime: response.responseTime
        }
      } else {
        console.log('  ⚠️  数据库连接可能有问题')
        this.testResults.database = {
          success: false,
          statusCode: response.statusCode
        }
      }
    } catch (error) {
      console.log('  ❌ 数据库连接测试失败:', error.message)
      this.testResults.database = {
        success: false,
        error: error.message
      }
    }
  }

  // 性能基准测试
  async performanceTest() {
    console.log('\n⚡ 性能基准测试...')
    
    const testPages = [
      '/about',
      '/privacy', 
      '/terms',
      '/contact'
    ]
    
    for (const page of testPages) {
      const times = []
      
      // 测试5次取平均值
      for (let i = 0; i < 5; i++) {
        try {
          const startTime = Date.now()
          await this.makeRequest(`http://localhost:3000${page}`, 10000)
          const responseTime = Date.now() - startTime
          times.push(responseTime)
        } catch (error) {
          // 忽略单次失败
        }
      }
      
      if (times.length > 0) {
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length
        const minTime = Math.min(...times)
        const maxTime = Math.max(...times)
        
        this.testResults.performance.push({
          page,
          averageTime: avgTime,
          minTime,
          maxTime,
          tests: times.length
        })
        
        console.log(`  📊 ${page}: 平均 ${avgTime.toFixed(2)}ms (${minTime}-${maxTime}ms)`)
      }
    }
  }

  // HTTP请求辅助函数
  makeRequest(url, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url)
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        timeout: timeout,
        headers: {
          'User-Agent': 'BitMarket-Test-Client'
        }
      }

      const req = http.request(options, (res) => {
        let data = ''
        res.on('data', chunk => data += chunk)
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          })
        })
      })

      req.on('error', reject)
      req.on('timeout', () => {
        req.destroy()
        reject(new Error('Request timeout'))
      })
      
      req.end()
    })
  }

  // 生成测试报告
  generateTestReport() {
    console.log('\n📊 功能测试报告')
    console.log('='.repeat(60))
    
    // 构建测试结果
    if (this.testResults.build) {
      console.log(`🏗️  构建测试: ${this.testResults.build.success ? '✅ 成功' : '❌ 失败'}`)
    }
    
    // 服务器测试结果
    if (this.testResults.server) {
      console.log(`🚀 服务器测试: ${this.testResults.server.success ? '✅ 成功' : '❌ 失败'}`)
    }
    
    // 页面测试结果
    const pageSuccess = this.testResults.pages.filter(p => p.success).length
    const pageTotal = this.testResults.pages.length
    console.log(`📄 页面测试: ${pageSuccess}/${pageTotal} 成功`)
    
    // API测试结果
    const apiSuccess = this.testResults.apis.filter(a => a.success).length
    const apiTotal = this.testResults.apis.length
    console.log(`🌐 API测试: ${apiSuccess}/${apiTotal} 成功`)
    
    // 数据库测试结果
    if (this.testResults.database) {
      console.log(`🗄️  数据库测试: ${this.testResults.database.success ? '✅ 成功' : '❌ 失败'}`)
    }
    
    // 性能测试结果
    if (this.testResults.performance.length > 0) {
      console.log(`⚡ 性能测试: ${this.testResults.performance.length}个页面`)
      const avgPerformance = this.testResults.performance.reduce((sum, p) => sum + p.averageTime, 0) / this.testResults.performance.length
      console.log(`   平均响应时间: ${avgPerformance.toFixed(2)}ms`)
    }
    
    // 计算总体评分
    const totalTests = pageTotal + apiTotal + (this.testResults.build ? 1 : 0) + (this.testResults.server ? 1 : 0) + (this.testResults.database ? 1 : 0)
    const successTests = pageSuccess + apiSuccess + 
                        (this.testResults.build?.success ? 1 : 0) + 
                        (this.testResults.server?.success ? 1 : 0) + 
                        (this.testResults.database?.success ? 1 : 0)
    
    const successRate = totalTests > 0 ? (successTests / totalTests * 100).toFixed(1) : 0
    
    console.log(`\n🎯 总体成功率: ${successRate}% (${successTests}/${totalTests})`)
    
    if (successRate >= 90) {
      console.log('🟢 系统状态: 优秀')
    } else if (successRate >= 80) {
      console.log('🟡 系统状态: 良好')
    } else if (successRate >= 70) {
      console.log('🟠 系统状态: 一般')
    } else {
      console.log('🔴 系统状态: 需要改进')
    }
    
    return {
      successRate: parseFloat(successRate),
      totalTests,
      successTests,
      details: this.testResults
    }
  }

  // 保存测试结果
  async saveResults(report) {
    try {
      await fs.promises.mkdir('test-results/functional-tests', { recursive: true })
      
      const filename = `functional-test-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.promises.writeFile(
        `test-results/functional-tests/${filename}`,
        JSON.stringify({
          timestamp: Date.now(),
          summary: report,
          details: this.testResults
        }, null, 2)
      )
      
      console.log(`\n💾 测试结果已保存: test-results/functional-tests/${filename}`)
      
    } catch (error) {
      console.error('保存测试结果失败:', error)
    }
  }

  // 清理资源
  cleanup() {
    if (this.devServer) {
      this.devServer.kill()
      console.log('\n🧹 开发服务器已停止')
    }
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🧪 开始功能测试')
    console.log('='.repeat(60))
    
    try {
      // 1. 测试构建
      const buildSuccess = await this.testBuild()
      
      if (!buildSuccess) {
        console.log('\n❌ 构建失败，跳过其他测试')
        return this.generateTestReport()
      }
      
      // 2. 启动开发服务器
      const serverSuccess = await this.startDevServer()
      
      if (!serverSuccess) {
        console.log('\n❌ 服务器启动失败，跳过页面测试')
        return this.generateTestReport()
      }
      
      // 等待服务器稳定
      await new Promise(resolve => setTimeout(resolve, 5000))
      
      // 3. 测试页面
      await this.testPages()
      
      // 4. 测试API
      await this.testAPIs()
      
      // 5. 测试数据库
      await this.testDatabase()
      
      // 6. 性能测试
      await this.performanceTest()
      
      // 生成报告
      const report = this.generateTestReport()
      await this.saveResults(report)
      
      return report
      
    } catch (error) {
      console.error('\n❌ 测试过程出错:', error)
      throw error
    } finally {
      this.cleanup()
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new FunctionalTester()
  tester.runAllTests()
    .then((report) => {
      console.log('\n✅ 功能测试完成')
      if (report.successRate >= 80) {
        console.log('🎉 系统功能正常')
        process.exit(0)
      } else {
        console.log('⚠️  系统存在问题，需要修复')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n❌ 功能测试失败:', error)
      process.exit(1)
    })
}

module.exports = { FunctionalTester }
