const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testUserDeletion() {
  try {
    console.log('🗑️ 测试用户物理删除功能...')
    
    // 1. 创建一个测试用户
    console.log('\n1. 创建测试用户...')
    const hashedPassword = await bcrypt.hash('testpassword', 12)
    
    const testUser = await prisma.user.create({
      data: {
        userId: 'test-delete-' + Math.random().toString(36).substring(2, 10),
        name: '待删除测试用户',
        email: `delete-test-${Date.now()}@test.com`,
        password: hashedPassword,
        emailVerified: new Date(),
        role: 'USER',
        status: 'ACTIVE',
        creditScore: 50,
        city: '测试城市',
        district: '测试区域'
      }
    })
    
    console.log(`✅ 测试用户创建成功:`)
    console.log(`   ID: ${testUser.id}`)
    console.log(`   姓名: ${testUser.name}`)
    console.log(`   邮箱: ${testUser.email}`)
    console.log(`   用户ID: ${testUser.userId}`)
    
    // 2. 为测试用户创建一些关联数据
    console.log('\n2. 创建关联数据...')
    
    // 创建地址
    const address = await prisma.address.create({
      data: {
        userId: testUser.id,
        name: '测试收货人',
        phone: '13800138000',
        province: '测试省',
        city: '测试市',
        district: '测试区',
        detail: '测试街道123号',
        isDefault: true
      }
    })
    console.log(`   ✅ 创建地址: ${address.id}`)
    
    // 创建用户会话
    const session = await prisma.userSession.create({
      data: {
        userId: testUser.id,
        sessionId: 'test-session-' + Math.random().toString(36).substring(2, 10),
        deviceName: '测试设备',
        deviceType: 'desktop',
        browser: '测试浏览器',
        ipAddress: '127.0.0.1',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
      }
    })
    console.log(`   ✅ 创建会话: ${session.id}`)
    
    // 创建收藏记录
    const existingProduct = await prisma.product.findFirst({
      where: { status: 'AVAILABLE' }
    })
    
    if (existingProduct) {
      const favorite = await prisma.favorite.create({
        data: {
          userId: testUser.id,
          productId: existingProduct.id
        }
      })
      console.log(`   ✅ 创建收藏: ${favorite.id}`)
    }
    
    // 3. 验证用户和关联数据存在
    console.log('\n3. 验证数据存在...')
    const userExists = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: {
        addresses: true,
        userSessions: true,
        favorites: true
      }
    })
    
    console.log(`   用户记录: ${userExists ? '✅ 存在' : '❌ 不存在'}`)
    console.log(`   地址记录: ${userExists?.addresses.length || 0} 条`)
    console.log(`   会话记录: ${userExists?.userSessions.length || 0} 条`)
    console.log(`   收藏记录: ${userExists?.favorites.length || 0} 条`)
    
    // 4. 检查是否可以删除（没有未完成订单）
    console.log('\n4. 检查删除条件...')
    const activeOrders = await prisma.order.count({
      where: {
        OR: [
          { buyerId: testUser.id },
          { sellerId: testUser.id }
        ],
        status: {
          in: ['PENDING_PAYMENT', 'PAID', 'SHIPPED']
        }
      }
    })
    
    console.log(`   未完成订单: ${activeOrders} 个`)
    
    if (activeOrders > 0) {
      console.log('   ⚠️ 用户有未完成订单，无法删除')
      return
    } else {
      console.log('   ✅ 满足删除条件')
    }
    
    console.log('\n💡 测试用户已准备就绪，可以在管理员页面测试删除功能：')
    console.log(`   1. 访问: http://localhost:3000/admin/users`)
    console.log(`   2. 找到用户: ${testUser.name} (${testUser.email})`)
    console.log(`   3. 点击"🗑️ 永久删除"按钮`)
    console.log(`   4. 按照提示完成删除确认`)
    console.log(`   5. 验证用户是否被完全删除`)
    
    console.log('\n📋 删除后应该发生的变化：')
    console.log('   • 用户记录完全从数据库中移除')
    console.log('   • 地址、会话、收藏等关联数据被删除')
    console.log('   • 邮箱可以重新注册使用')
    console.log('   • 如果有订单/评价，会被匿名化处理')
    
  } catch (error) {
    console.error('测试准备失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testUserDeletion()
  .then(() => {
    console.log('\n🎉 测试用户创建完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 测试失败:', error)
    process.exit(1)
  })
