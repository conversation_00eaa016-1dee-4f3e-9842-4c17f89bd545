#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

async function fixSessionPersistence() {
  console.log('🔧 修复用户登录状态持久性问题')
  console.log('='.repeat(60))

  // 1. 检查环境变量配置
  console.log('\n📋 1. 检查环境变量配置...')
  
  const envFiles = ['.env.local', '.env']
  let envContent = ''
  let envFile = ''
  
  for (const file of envFiles) {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      envContent = fs.readFileSync(filePath, 'utf8')
      envFile = file
      break
    }
  }
  
  if (!envContent) {
    console.log('❌ 未找到环境变量文件，创建 .env.local...')
    const envTemplate = `# NextAuth.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-super-secret-key-change-this-in-production-${Math.random().toString(36).substring(2)}"
JWT_SECRET="your-jwt-secret-${Math.random().toString(36).substring(2)}"

# 数据库配置
DATABASE_URL="file:./dev.db"
`
    fs.writeFileSync(path.join(process.cwd(), '.env.local'), envTemplate)
    console.log('✅ 已创建 .env.local 文件')
    envFile = '.env.local'
    envContent = envTemplate
  } else {
    console.log(`✅ 找到环境变量文件: ${envFile}`)
  }
  
  // 检查必要的环境变量
  const requiredVars = ['NEXTAUTH_URL', 'NEXTAUTH_SECRET', 'JWT_SECRET']
  const missingVars = []
  
  for (const varName of requiredVars) {
    if (!envContent.includes(varName)) {
      missingVars.push(varName)
    }
  }
  
  if (missingVars.length > 0) {
    console.log(`⚠️  缺少环境变量: ${missingVars.join(', ')}`)
    
    // 添加缺少的环境变量
    let additions = '\n# 添加的NextAuth配置\n'
    if (missingVars.includes('NEXTAUTH_URL')) {
      additions += 'NEXTAUTH_URL="http://localhost:3000"\n'
    }
    if (missingVars.includes('NEXTAUTH_SECRET')) {
      additions += `NEXTAUTH_SECRET="your-super-secret-key-${Math.random().toString(36).substring(2)}"\n`
    }
    if (missingVars.includes('JWT_SECRET')) {
      additions += `JWT_SECRET="your-jwt-secret-${Math.random().toString(36).substring(2)}"\n`
    }
    
    fs.appendFileSync(path.join(process.cwd(), envFile), additions)
    console.log('✅ 已添加缺少的环境变量')
  } else {
    console.log('✅ 所有必要的环境变量都已配置')
  }

  // 2. 检查和修复 NextAuth 配置
  console.log('\n🔐 2. 检查和修复 NextAuth 配置...')
  
  const authConfigPath = path.join(process.cwd(), 'lib/auth.ts')
  if (fs.existsSync(authConfigPath)) {
    let authContent = fs.readFileSync(authConfigPath, 'utf8')
    let needsUpdate = false
    
    // 检查会话策略
    if (!authContent.includes('strategy: \'jwt\'')) {
      console.log('⚠️  会话策略配置可能有问题')
    } else {
      console.log('✅ 会话策略配置正确 (JWT)')
    }
    
    // 检查会话过期时间
    if (!authContent.includes('maxAge:')) {
      console.log('⚠️  缺少会话过期时间配置')
    } else {
      console.log('✅ 会话过期时间已配置')
    }
    
    // 检查 cookie 配置
    if (!authContent.includes('cookies:')) {
      console.log('⚠️  缺少 cookie 配置，添加中...')
      
      const cookieConfig = `  cookies: {
    sessionToken: {
      name: \`next-auth.session-token\`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 30 * 24 * 60 * 60, // 30 days
      },
    },
  },`
      
      // 在 session 配置后添加 cookie 配置
      authContent = authContent.replace(
        /session:\s*{[^}]*},/,
        match => match + '\n' + cookieConfig
      )
      needsUpdate = true
    } else {
      console.log('✅ Cookie 配置已存在')
    }
    
    if (needsUpdate) {
      fs.writeFileSync(authConfigPath, authContent)
      console.log('✅ NextAuth 配置已更新')
    }
  } else {
    console.log('❌ NextAuth 配置文件不存在')
  }

  // 3. 检查 SessionProvider 配置
  console.log('\n🔄 3. 检查 SessionProvider 配置...')
  
  const sessionProviderPath = path.join(process.cwd(), 'components/providers/session-provider.tsx')
  if (fs.existsSync(sessionProviderPath)) {
    const providerContent = fs.readFileSync(sessionProviderPath, 'utf8')
    
    if (providerContent.includes('refetchInterval') && 
        providerContent.includes('refetchOnWindowFocus')) {
      console.log('✅ SessionProvider 配置完整')
    } else {
      console.log('⚠️  SessionProvider 配置不完整，正在修复...')
      
      const fixedProvider = `'use client'

import { SessionProvider } from 'next-auth/react'
import type { Session } from 'next-auth'
import type { ReactNode } from 'react'

interface AuthSessionProviderProps {
  children: ReactNode
  session?: Session | null
}

export default function AuthSessionProvider({
  children,
  session
}: AuthSessionProviderProps) {
  return (
    <SessionProvider
      session={session}
      refetchInterval={5 * 60} // 5分钟刷新一次
      refetchOnWindowFocus={true}
      refetchWhenOffline={false}
      basePath="/api/auth"
    >
      {children}
    </SessionProvider>
  )
}
`
      
      fs.writeFileSync(sessionProviderPath, fixedProvider)
      console.log('✅ SessionProvider 已修复')
    }
  } else {
    console.log('❌ SessionProvider 文件不存在')
  }

  // 4. 检查 layout.tsx
  console.log('\n📄 4. 检查 layout.tsx...')
  
  const layoutPath = path.join(process.cwd(), 'app/layout.tsx')
  if (fs.existsSync(layoutPath)) {
    const layoutContent = fs.readFileSync(layoutPath, 'utf8')
    
    if (layoutContent.includes('AuthSessionProvider')) {
      console.log('✅ layout.tsx 包含 AuthSessionProvider')
    } else {
      console.log('❌ layout.tsx 缺少 AuthSessionProvider')
    }
  }

  console.log('\n🎯 5. 生成测试脚本...')
  
  const testScript = `// 测试登录状态持久性
// 在浏览器控制台运行此脚本

console.log('🧪 测试登录状态持久性...')

// 检查当前会话
fetch('/api/auth/session')
  .then(res => res.json())
  .then(session => {
    console.log('当前会话:', session)
    
    if (session?.user) {
      console.log('✅ 用户已登录:', session.user.email)
      
      // 检查 cookie
      const cookies = document.cookie.split(';')
      const authCookies = cookies.filter(cookie => 
        cookie.includes('next-auth') || cookie.includes('session')
      )
      
      console.log('认证相关 cookies:', authCookies)
      
      if (authCookies.length > 0) {
        console.log('✅ 找到认证 cookies')
      } else {
        console.log('❌ 未找到认证 cookies')
      }
    } else {
      console.log('❌ 用户未登录')
    }
  })
  .catch(err => {
    console.error('❌ 会话检查失败:', err)
  })
`
  
  fs.writeFileSync(path.join(process.cwd(), 'test-session-persistence.js'), testScript)
  console.log('✅ 测试脚本已生成: test-session-persistence.js')

  console.log('\n' + '='.repeat(60))
  console.log('🎉 登录状态持久性修复完成！')
  console.log('\n📋 修复内容:')
  console.log('1. ✅ 检查并配置环境变量')
  console.log('2. ✅ 优化 NextAuth 配置')
  console.log('3. ✅ 修复 SessionProvider 配置')
  console.log('4. ✅ 生成测试脚本')
  
  console.log('\n🔍 测试步骤:')
  console.log('1. 重启开发服务器: npm run dev')
  console.log('2. 登录用户账户')
  console.log('3. 刷新页面或重新打开标签页')
  console.log('4. 检查登录状态是否保持')
  console.log('5. 在浏览器控制台运行测试脚本')
}

fixSessionPersistence().catch(console.error)
