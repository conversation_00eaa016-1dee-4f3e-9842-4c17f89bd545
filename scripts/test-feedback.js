// 测试反馈提交功能
async function testFeedbackSubmission() {
  console.log('🧪 开始测试反馈提交功能...\n');

  const baseUrl = 'http://localhost:3000';
  
  // 测试用例
  const testCases = [
    {
      name: '✅ 正常Bug反馈（包含所有字段）',
      data: {
        category: 'BUG_REPORT',
        title: '登录页面无法正常显示',
        description: '当我尝试访问登录页面时，页面显示空白，控制台显示JavaScript错误。这个问题在Chrome和Firefox浏览器上都会出现。',
        contactEmail: '<EMAIL>',
        contactPhone: '13800138000'
      },
      shouldSucceed: true
    },
    {
      name: '✅ 正常改进建议（仅必填字段）',
      data: {
        category: 'IMPROVEMENT',
        title: '建议增加暗色主题',
        description: '希望平台能够支持暗色主题，这样在夜间使用时对眼睛更友好。可以在用户设置中添加主题切换选项。'
      },
      shouldSucceed: true
    },
    {
      name: '✅ 正常申诉（空邮箱字段）',
      data: {
        category: 'APPEAL',
        title: '账户被误封申诉',
        description: '我的账户在昨天被误封，但我没有违反任何平台规则。希望能够重新审核我的账户状态。',
        contactEmail: '',
        contactPhone: '13900139000'
      },
      shouldSucceed: true
    },
    {
      name: '❌ 标题为空',
      data: {
        category: 'BUG_REPORT',
        title: '',
        description: '这是一个测试描述，用于验证标题为空时的验证逻辑。',
        contactEmail: '<EMAIL>'
      },
      shouldSucceed: false,
      expectedError: '标题不能为空'
    },
    {
      name: '❌ 描述太短',
      data: {
        category: 'BUG_REPORT',
        title: '测试标题',
        description: '太短了',
        contactEmail: '<EMAIL>'
      },
      shouldSucceed: false,
      expectedError: '描述至少需要10个字符'
    },
    {
      name: '❌ 邮箱格式错误',
      data: {
        category: 'BUG_REPORT',
        title: '测试标题',
        description: '这是一个足够长的描述，用于测试邮箱格式验证功能。',
        contactEmail: 'invalid-email-format'
      },
      shouldSucceed: false,
      expectedError: '邮箱格式不正确'
    },
    {
      name: '❌ 标题过长',
      data: {
        category: 'BUG_REPORT',
        title: 'A'.repeat(201), // 201个字符，超过200字符限制
        description: '这是一个测试描述，用于验证标题长度限制。',
        contactEmail: '<EMAIL>'
      },
      shouldSucceed: false,
      expectedError: '标题不能超过200字符'
    },
    {
      name: '❌ 描述过长',
      data: {
        category: 'BUG_REPORT',
        title: '测试标题',
        description: 'A'.repeat(2001), // 2001个字符，超过2000字符限制
        contactEmail: '<EMAIL>'
      },
      shouldSucceed: false,
      expectedError: '描述不能超过2000字符'
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`📋 测试: ${testCase.name}`);
    
    try {
      const response = await fetch(`${baseUrl}/api/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 模拟登录用户的Cookie（实际测试时需要真实的session）
          'Cookie': 'next-auth.session-token=test-session'
        },
        body: JSON.stringify(testCase.data)
      });

      const result = await response.json();
      
      if (testCase.shouldSucceed) {
        if (response.ok) {
          console.log(`   ✅ 成功: ${result.message}`);
          console.log(`   📝 反馈ID: ${result.feedback?.id}`);
          passedTests++;
        } else {
          console.log(`   ❌ 失败: 期望成功但收到错误 - ${result.error}`);
          if (result.details) {
            console.log(`   📋 详细错误:`, result.details);
          }
        }
      } else {
        if (!response.ok) {
          const hasExpectedError = result.error?.includes(testCase.expectedError) || 
                                 result.details?.some(detail => detail.message?.includes(testCase.expectedError));
          
          if (hasExpectedError) {
            console.log(`   ✅ 成功: 正确捕获了预期错误 - ${testCase.expectedError}`);
            passedTests++;
          } else {
            console.log(`   ❌ 失败: 错误信息不匹配`);
            console.log(`   📋 期望错误: ${testCase.expectedError}`);
            console.log(`   📋 实际错误: ${result.error}`);
            if (result.details) {
              console.log(`   📋 详细错误:`, result.details);
            }
          }
        } else {
          console.log(`   ❌ 失败: 期望失败但请求成功了`);
        }
      }
    } catch (error) {
      console.log(`   ❌ 网络错误: ${error.message}`);
    }
    
    console.log(''); // 空行分隔
  }

  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！反馈功能工作正常。');
  } else {
    console.log('⚠️  部分测试失败，请检查相关功能。');
  }
}

// 运行测试
testFeedbackSubmission().catch(console.error);
