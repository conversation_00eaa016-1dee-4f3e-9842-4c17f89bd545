const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestDeposits() {
  try {
    console.log('🔄 创建测试充值记录...')

    // 获取测试用户
    const users = await prisma.user.findMany({
      take: 3
    })

    if (users.length === 0) {
      console.log('❌ 没有找到用户，请先创建用户')
      return
    }

    // 创建测试充值记录
    const testDeposits = [
      {
        userId: users[0].id,
        amount: 100.00,
        originalAmount: 100.00,
        method: 'USDT',
        status: 'PENDING',
        txHash: 'TRX_1234567890abcdef1234567890abcdef12345678',
        transactionHash: 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
        notes: '用户通过USDT(TRC20)充值，等待管理员确认到账',
        metadata: {
          walletAddress: 'TRX7n2oDKGKnTFTQjSwhHKAUAyiYK9vWhr',
          network: 'TRC20',
          confirmations: 12
        }
      },
      {
        userId: users[1].id,
        amount: 500.00,
        originalAmount: 0.15,
        method: 'BNB',
        status: 'PENDING',
        txHash: 'BSC_abcdef1234567890abcdef1234567890abcdef12',
        transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        notes: '用户通过BNB链充值，需要确认汇率和到账金额',
        metadata: {
          walletAddress: '******************************************',
          network: 'BSC',
          originalCurrency: 'BNB',
          exchangeRate: 3333.33
        }
      },
      {
        userId: users[2] ? users[2].id : users[0].id,
        amount: 1000.00,
        originalAmount: 1000.00,
        method: 'BINANCE_PAY',
        status: 'PENDING',
        paymentOrderId: 'BINANCE_ORDER_' + Date.now() + '_003',
        pinCode: '345678',
        notes: '用户通过币安支付充值，等待支付确认',
        metadata: {
          binanceOrderId: 'BIN_' + Date.now(),
          paymentMethod: 'BINANCE_PAY'
        }
      },
      {
        userId: users[0].id,
        amount: 200.00,
        originalAmount: 200.00,
        method: 'BINANCE_PAY',
        status: 'PENDING',
        paymentOrderId: 'BINANCE_ORDER_' + (Date.now() + 1000) + '_004',
        pinCode: '567890',
        notes: '币安支付充值 - 需要核实PIN码',
        metadata: {
          binanceOrderId: 'BIN_' + (Date.now() + 1000),
          paymentMethod: 'BINANCE_PAY'
        }
      },
      {
        userId: users[1].id,
        amount: 300.00,
        originalAmount: 0.09,
        method: 'BNB',
        status: 'PENDING',
        transactionHash: '******************************************10fedcba9876543210fedcba9876',
        notes: 'BNB链充值 - 需要确认区块链交易',
        metadata: {
          walletAddress: '******************************************',
          network: 'BSC',
          originalCurrency: 'BNB',
          exchangeRate: 3333.33
        }
      }
    ]

    for (const depositData of testDeposits) {
      const deposit = await prisma.depositRecord.create({
        data: depositData,
        include: {
          user: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      console.log(`✅ 创建充值记录: ${deposit.id}`)
      console.log(`   用户: ${deposit.user.name} (${deposit.user.email})`)
      console.log(`   金额: ${deposit.amount} USDT`)
      console.log(`   支付方式: ${deposit.method}`)
      console.log(`   PIN码: ${deposit.pinCode}`)
      console.log(`   订单号: ${deposit.paymentOrderId}`)
      console.log('')
    }

    console.log('🎉 测试充值记录创建完成!')
    console.log('现在可以在管理后台查看这些充值申请了')

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

createTestDeposits()
