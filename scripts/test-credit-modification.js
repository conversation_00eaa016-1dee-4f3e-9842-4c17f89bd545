const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testCreditModification() {
  try {
    console.log('💳 测试信用分数修改功能...')
    
    // 查看所有用户的信用分数和历史
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        creditScore: true,
        creditHistory: true
      },
      orderBy: { createdAt: 'desc' }
    })
    
    console.log('\n📋 用户信用分数状态:')
    console.log('=' .repeat(60))
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. 用户: ${user.name || '未设置'} (${user.email})`)
      console.log(`   ID: ${user.id}`)
      console.log(`   当前信用分数: ${user.creditScore}`)
      
      if (user.creditHistory && Array.isArray(user.creditHistory)) {
        console.log(`   信用历史记录 (${user.creditHistory.length}条):`)
        user.creditHistory.forEach((record, recordIndex) => {
          console.log(`     ${recordIndex + 1}. 操作类型: ${record.adjustmentType}`)
          console.log(`        原分数: ${record.originalScore} -> 新分数: ${record.newScore}`)
          console.log(`        原因: ${record.reason}`)
          console.log(`        操作者: ${record.adjustedBy}`)
          console.log(`        时间: ${record.adjustedAt}`)
        })
      } else {
        console.log(`   信用历史记录: 无`)
      }
      
      console.log('-'.repeat(40))
    })

    // 统计信用分数分布
    const creditStats = await prisma.user.groupBy({
      by: ['creditScore'],
      _count: {
        id: true
      },
      orderBy: {
        creditScore: 'desc'
      }
    })
    
    console.log('\n📊 信用分数分布:')
    console.log('=' .repeat(40))
    creditStats.forEach(stat => {
      console.log(`${stat.creditScore}分: ${stat._count.id} 个用户`)
    })

    // 查找有信用历史的用户
    const usersWithHistory = await prisma.user.findMany({
      where: {
        creditHistory: {
          not: null
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        creditScore: true,
        creditHistory: true
      }
    })
    
    console.log('\n📈 有信用修改历史的用户:')
    console.log('=' .repeat(40))
    if (usersWithHistory.length === 0) {
      console.log('暂无用户有信用修改历史')
    } else {
      usersWithHistory.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name || '未设置'} (${user.email})`)
        console.log(`   当前分数: ${user.creditScore}`)
        console.log(`   历史记录数: ${user.creditHistory?.length || 0}`)
        
        if (user.creditHistory && user.creditHistory.length > 0) {
          const lastRecord = user.creditHistory[user.creditHistory.length - 1]
          console.log(`   最近修改: ${lastRecord.adjustmentType} (${lastRecord.originalScore} -> ${lastRecord.newScore})`)
          console.log(`   修改时间: ${lastRecord.adjustedAt}`)
        }
      })
    }

    console.log('\n💡 测试建议:')
    console.log('1. 在管理员页面找到一个用户')
    console.log('2. 点击"💳 修改信用"按钮')
    console.log('3. 选择操作类型（设置/增加/减少）')
    console.log('4. 输入新的分数值和修改原因')
    console.log('5. 确认修改并查看结果')
    console.log('6. 验证信用历史记录是否正确保存')

  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testCreditModification()
  .then(() => {
    console.log('\n🎉 测试完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 测试失败:', error)
    process.exit(1)
  })
