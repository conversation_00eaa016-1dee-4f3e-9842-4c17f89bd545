const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function deleteAllEscrowOrders() {
  try {
    console.log('🗑️ 删除所有托管订单...')

    // 1. 删除托管聊天消息
    try {
      const chatMessagesDeleted = await prisma.escrowChatMessage.deleteMany()
      console.log(`✅ 删除了 ${chatMessagesDeleted.count} 个托管聊天消息`)
    } catch (e) {
      console.log('⚠️ 跳过聊天消息删除 (可能不存在)')
    }

    // 2. 删除托管聊天室
    try {
      const chatRoomsDeleted = await prisma.escrowChatRoom.deleteMany()
      console.log(`✅ 删除了 ${chatRoomsDeleted.count} 个托管聊天室`)
    } catch (e) {
      console.log('⚠️ 跳过聊天室删除 (可能不存在)')
    }

    // 3. 删除争议记录
    try {
      const disputesDeleted = await prisma.escrowDispute.deleteMany()
      console.log(`✅ 删除了 ${disputesDeleted.count} 个争议记录`)
    } catch (e) {
      console.log('⚠️ 跳过争议记录删除 (可能不存在)')
    }

    // 4. 删除仲裁投票
    try {
      const votesDeleted = await prisma.arbitrationVote.deleteMany()
      console.log(`✅ 删除了 ${votesDeleted.count} 个仲裁投票`)
    } catch (e) {
      console.log('⚠️ 跳过仲裁投票删除 (可能不存在)')
    }

    // 5. 删除区块链交易记录
    try {
      const blockchainTxDeleted = await prisma.blockchainTransaction.deleteMany({
        where: {
          relatedEscrowId: { not: null }
        }
      })
      console.log(`✅ 删除了 ${blockchainTxDeleted.count} 个区块链交易记录`)
    } catch (e) {
      console.log('⚠️ 跳过区块链交易记录删除 (可能不存在)')
    }

    // 6. 删除资金冻结记录
    try {
      const freezeDeleted = await prisma.fundFreeze.deleteMany({
        where: {
          relatedType: 'ESCROW_ORDER'
        }
      })
      console.log(`✅ 删除了 ${freezeDeleted.count} 个资金冻结记录`)
    } catch (e) {
      console.log('⚠️ 跳过资金冻结记录删除 (可能不存在)')
    }

    // 7. 删除托管订单
    try {
      const escrowOrdersDeleted = await prisma.escrowOrder.deleteMany()
      console.log(`✅ 删除了 ${escrowOrdersDeleted.count} 个托管订单`)
    } catch (e) {
      console.log('⚠️ 跳过托管订单删除 (可能不存在)')
    }

    // 8. 删除相关的普通订单
    try {
      const ordersDeleted = await prisma.order.deleteMany({
        where: {
          useEscrow: true
        }
      })
      console.log(`✅ 删除了 ${ordersDeleted.count} 个相关订单`)
    } catch (e) {
      console.log('⚠️ 跳过订单删除 (可能不存在)')
    }

    console.log('\n🎉 所有托管订单已删除完成!')

  } catch (error) {
    console.error('❌ 删除托管订单失败:', error)
    throw error
  }
}

async function checkExistingData() {
  try {
    console.log('📊 检查现有数据...\n')
    
    // 检查托管订单
    const escrowOrders = await prisma.escrowOrder.findMany({
      include: {
        order: { select: { orderNumber: true } },
        buyer: { select: { name: true, email: true } },
        seller: { select: { name: true, email: true } },
        mediator: { select: { name: true, email: true } }
      }
    })
    
    console.log('🛡️ 现有托管订单:')
    if (escrowOrders.length === 0) {
      console.log('  无托管订单')
    } else {
      escrowOrders.forEach(order => {
        console.log(`  - ${order.order.orderNumber} (状态: ${order.status})`)
        console.log(`    买家: ${order.buyer.name} (${order.buyer.email})`)
        console.log(`    卖家: ${order.seller.name} (${order.seller.email})`)
        console.log(`    中间人: ${order.mediator.name} (${order.mediator.email})`)
        console.log('')
      })
    }
    
    // 检查用户账号
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isMediator: true,
        mediatorStatus: true,
        depositBalance: true
      },
      orderBy: { createdAt: 'desc' }
    })
    
    console.log('👥 现有用户账号:')
    users.forEach(user => {
      const mediatorInfo = user.isMediator ? ` (中间人: ${user.mediatorStatus})` : ''
      console.log(`  - ${user.name} (${user.email}) - ${user.role}${mediatorInfo} - 余额: ${user.depositBalance} USDT`)
    })
    
    return { escrowOrders, users }
    
  } catch (error) {
    console.error('❌ 检查数据失败:', error)
    throw error
  }
}

async function createTestEscrowOrder() {
  try {
    console.log('\n🚀 创建测试托管订单...')
    
    // 1. 确保有足够的用户
    let users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isMediator: true,
        mediatorStatus: true,
        depositBalance: true
      }
    })
    
    if (users.length < 3) {
      console.log('👥 用户数量不足，创建测试用户...')
      
      // 创建买家
      const buyer = await prisma.user.create({
        data: {
          name: '测试买家',
          email: '<EMAIL>',
          password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
          role: 'USER',
          depositBalance: 5000,
          creditScore: 85,
          emailVerified: new Date(),
          status: 'ACTIVE'
        }
      })
      
      // 创建卖家
      const seller = await prisma.user.create({
        data: {
          name: '测试卖家',
          email: '<EMAIL>',
          password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
          role: 'USER',
          depositBalance: 2000,
          creditScore: 90,
          emailVerified: new Date(),
          status: 'ACTIVE'
        }
      })
      
      // 创建中间人
      const mediator = await prisma.user.create({
        data: {
          name: '测试中间人',
          email: '<EMAIL>',
          password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
          role: 'USER',
          depositBalance: 10000,
          creditScore: 95,
          emailVerified: new Date(),
          status: 'ACTIVE',
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.05, // 5%
          mediatorDeposit: 5000,
          mediatorReputation: 4.8,
          bnbWalletAddress: '******************************************'
        }
      })
      
      console.log('✅ 创建了测试用户')
      users = [buyer, seller, mediator]
    }
    
    // 2. 获取用户角色
    const buyer = users.find(u => !u.isMediator && u.email.includes('buyer')) || users.find(u => !u.isMediator)
    const seller = users.find(u => !u.isMediator && u.email.includes('seller')) || users.filter(u => !u.isMediator)[1] || users.find(u => !u.isMediator && u.id !== buyer.id)
    const mediator = users.find(u => u.isMediator && u.mediatorStatus === 'ACTIVE')
    
    if (!buyer || !seller || !mediator) {
      console.log('❌ 无法找到合适的用户角色')
      console.log('买家:', buyer?.email)
      console.log('卖家:', seller?.email)
      console.log('中间人:', mediator?.email)
      return
    }
    
    console.log(`👤 买家: ${buyer.name} (${buyer.email})`)
    console.log(`👤 卖家: ${seller.name} (${seller.email})`)
    console.log(`👤 中间人: ${mediator.name} (${mediator.email})`)
    
    // 3. 创建测试商品
    const product = await prisma.product.create({
      data: {
        title: '测试商品 - MacBook Pro 16寸',
        description: '这是一个用于测试托管系统的商品。全新MacBook Pro 16寸，M3 Max芯片，32GB内存，1TB存储。',
        price: 2500,
        category: 'ELECTRONICS',
        condition: 'NEW',
        city: '北京市',
        district: '朝阳区',
        address: '测试地址123号',
        sellerId: seller.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        images: JSON.stringify(['https://via.placeholder.com/400x300?text=MacBook+Pro'])
      }
    })
    
    console.log(`📦 创建商品: ${product.title}`)
    
    // 4. 创建订单
    const orderNumber = `ESC${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`
    const amount = product.price
    const mediatorFee = amount * mediator.mediatorFeeRate
    const platformFee = mediatorFee * 0.3
    
    const order = await prisma.order.create({
      data: {
        orderNumber,
        productId: product.id,
        buyerId: buyer.id,
        sellerId: seller.id,
        mediatorId: mediator.id,
        totalAmount: amount,
        productPrice: amount,
        status: 'PENDING',
        useEscrow: true,
        escrowFee: mediatorFee || 0,
        escrowFeeRate: mediator.mediatorFeeRate || 0.05,
        paymentMethod: 'USDT',
        shippingAddress: {
          name: buyer.name,
          phone: '13800138000',
          address: '北京市朝阳区测试街道123号',
          city: '北京市',
          district: '朝阳区'
        }
      }
    })
    
    console.log(`📋 创建订单: ${order.orderNumber}`)
    
    // 5. 创建托管订单
    const escrowOrder = await prisma.escrowOrder.create({
      data: {
        orderId: order.id,
        mediatorId: mediator.id,
        buyerId: buyer.id,
        sellerId: seller.id,
        amount,
        mediatorFee: mediatorFee || 0,
        platformFee: platformFee || 0,
        status: 'PENDING',
        mediatorWalletAddress: mediator.bnbWalletAddress || '******************************************'
      }
    })
    
    console.log(`🛡️ 创建托管订单: ${escrowOrder.id}`)
    
    // 6. 创建多方聊天室
    const chatRoom = await prisma.escrowChatRoom.create({
      data: {
        escrowOrderId: escrowOrder.id,
        roomCode: `ESCROW_${escrowOrder.id}`,
        isActive: true
      }
    })

    console.log(`💬 创建聊天室: ${chatRoom.roomCode}`)
    
    console.log('\n🎉 测试托管订单创建成功!')
    console.log(`📍 访问链接: http://localhost:3000/escrow/orders/${escrowOrder.id}`)
    
    return {
      escrowOrder,
      order,
      product,
      buyer,
      seller,
      mediator,
      chatRoom
    }
    
  } catch (error) {
    console.error('❌ 创建测试托管订单失败:', error)
    throw error
  }
}

async function main() {
  try {
    const action = process.argv[2]
    
    switch (action) {
      case 'check':
        await checkExistingData()
        break
        
      case 'delete':
        await deleteAllEscrowOrders()
        break
        
      case 'create':
        await createTestEscrowOrder()
        break
        
      case 'reset':
        await deleteAllEscrowOrders()
        await createTestEscrowOrder()
        break
        
      default:
        console.log('📋 托管订单管理工具')
        console.log('')
        console.log('用法:')
        console.log('  node scripts/manage-escrow-orders.js check   - 检查现有数据')
        console.log('  node scripts/manage-escrow-orders.js delete  - 删除所有托管订单')
        console.log('  node scripts/manage-escrow-orders.js create  - 创建测试托管订单')
        console.log('  node scripts/manage-escrow-orders.js reset   - 删除并重新创建')
        break
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  deleteAllEscrowOrders,
  checkExistingData,
  createTestEscrowOrder
}
