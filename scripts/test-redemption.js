const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testRedemption() {
  try {
    console.log('🧪 开始测试兑换功能...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('❌ 未找到管理员用户')
      return
    }

    console.log('👤 找到管理员:', admin.email)
    console.log('💰 当前余额:', admin.depositBalance)

    // 1. 测试礼品卡验证
    console.log('\n📋 测试礼品卡验证...')
    const testGiftCardCode = 'RQ9V1KY6U4ZS7J5C'
    
    const giftCard = await prisma.giftCard.findUnique({
      where: { cardCode: testGiftCardCode },
      include: {
        product: true,
        soldTo: { select: { name: true } },
        redeemedBy: { select: { name: true } },
        assignedTo: { select: { name: true, email: true } }
      }
    })

    if (!giftCard) {
      console.error('❌ 礼品卡不存在:', testGiftCardCode)
      return
    }

    console.log('✅ 礼品卡信息:')
    console.log('  - 卡号:', giftCard.cardCode)
    console.log('  - 面值:', giftCard.faceValue, 'USDT')
    console.log('  - 状态:', giftCard.status)
    console.log('  - 有效期:', giftCard.validUntil)
    console.log('  - 商品:', giftCard.product?.name || '无关联商品')

    // 2. 测试兑换码验证
    console.log('\n🎫 测试兑换码验证...')
    const testRedemptionCode = 'HRCE5Z9CSOW8'
    
    const redemptionCode = await prisma.redemptionCode.findUnique({
      where: { codeValue: testRedemptionCode },
      include: {
        createdBy: { select: { name: true } },
        targetUser: { select: { name: true, email: true } }
      }
    })

    if (!redemptionCode) {
      console.error('❌ 兑换码不存在:', testRedemptionCode)
      return
    }

    console.log('✅ 兑换码信息:')
    console.log('  - 代码:', redemptionCode.codeValue)
    console.log('  - 奖励类型:', redemptionCode.rewardType)
    console.log('  - 奖励值:', redemptionCode.rewardValue, redemptionCode.rewardUnit)
    console.log('  - 状态:', redemptionCode.status)
    console.log('  - 有效期:', redemptionCode.validUntil)
    console.log('  - 最大使用次数:', redemptionCode.maxUses)
    console.log('  - 已使用次数:', redemptionCode.usedCount)

    // 3. 检查礼品卡商品
    console.log('\n🛍️ 检查礼品卡商品...')
    const giftCardProducts = await prisma.giftCardProduct.findMany({
      where: { isActive: true },
      include: {
        createdBy: { select: { name: true } },
        _count: {
          select: {
            giftCards: true,
            orders: true
          }
        }
      }
    })

    console.log(`✅ 找到 ${giftCardProducts.length} 个活跃的礼品卡商品:`)
    giftCardProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name}`)
      console.log(`     - 类型: ${product.productType}`)
      console.log(`     - 面值: ${product.faceValue} USDT`)
      console.log(`     - 售价: ${product.salePrice} USDT`)
      console.log(`     - 库存: ${product.stock}`)
      console.log(`     - 关联礼品卡: ${product._count.giftCards} 张`)
      console.log(`     - 订单数: ${product._count.orders} 个`)
    })

    // 4. 检查数据库模型
    console.log('\n🔍 检查数据库模型...')
    
    // 检查FundTransaction模型
    const fundTransactionCount = await prisma.fundTransaction.count()
    console.log('✅ FundTransaction 表存在，记录数:', fundTransactionCount)

    // 检查GiftCardTransaction模型
    const giftCardTransactionCount = await prisma.giftCardTransaction.count()
    console.log('✅ GiftCardTransaction 表存在，记录数:', giftCardTransactionCount)

    console.log('\n🎉 测试完成！所有模型和数据都正常。')
    console.log('\n💡 现在可以在浏览器中测试兑换功能:')
    console.log('1. 访问 http://localhost:3000/deposit')
    console.log('2. 点击"兑换码"标签页')
    console.log('3. 测试礼品卡兑换:', testGiftCardCode)
    console.log('4. 测试兑换码使用:', testRedemptionCode)

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testRedemption()
