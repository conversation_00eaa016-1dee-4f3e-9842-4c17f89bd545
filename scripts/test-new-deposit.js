/**
 * 测试新的充值功能
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testNewDepositFeatures() {
  console.log('🧪 测试新的充值功能...')
  console.log('='.repeat(50))

  try {
    // 1. 测试链上支付充值
    console.log('\n1️⃣ 测试链上支付充值...')
    
    const user1 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (user1) {
      // 模拟创建链上支付充值记录
      const chainDeposit = await prisma.depositRecord.create({
        data: {
          userId: user1.id,
          amount: 100,
          originalAmount: 100,
          method: 'chain',
          status: 'PENDING',
          notes: '测试链上支付',
          metadata: {
            paymentPin: 'ABC12345',
            paymentPinExpiry: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
            walletAddress: 'TYQEuRHZxZvKJT8PnM8aCdNKhgeVLSbPAU',
            test: true
          }
        }
      })

      console.log(`✅ 创建链上支付充值记录: ${chainDeposit.id}`)
      console.log(`   PIN码: ${chainDeposit.metadata.paymentPin}`)
      console.log(`   钱包地址: ${chainDeposit.metadata.walletAddress}`)
    }

    // 2. 测试币安扫码支付充值
    console.log('\n2️⃣ 测试币安扫码支付充值...')
    
    if (user1) {
      // 模拟创建币安扫码支付充值记录
      const binanceDeposit = await prisma.depositRecord.create({
        data: {
          userId: user1.id,
          amount: 200,
          originalAmount: 200,
          method: 'binance_qr',
          status: 'PENDING',
          notes: '测试币安扫码支付',
          metadata: {
            paymentPin: 'XYZ98765',
            paymentPinExpiry: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
            paymentUrl: '/binance-qr-code.png',
            test: true
          }
        }
      })

      console.log(`✅ 创建币安扫码支付充值记录: ${binanceDeposit.id}`)
      console.log(`   PIN码: ${binanceDeposit.metadata.paymentPin}`)
      console.log(`   二维码: ${binanceDeposit.metadata.paymentUrl}`)
    }

    // 3. 测试PIN码验证
    console.log('\n3️⃣ 测试PIN码验证功能...')
    
    const testPinValidation = (pin, expectedPin) => {
      const isValid = pin.toUpperCase() === expectedPin
      console.log(`   PIN码验证: ${pin} -> ${isValid ? '✅ 有效' : '❌ 无效'}`)
      return isValid
    }

    testPinValidation('abc12345', 'ABC12345')
    testPinValidation('ABC12345', 'ABC12345')
    testPinValidation('wrong123', 'ABC12345')

    // 4. 测试PIN码过期检查
    console.log('\n4️⃣ 测试PIN码过期检查...')
    
    const checkPinExpiry = (expiryTime) => {
      const now = new Date()
      const expiry = new Date(expiryTime)
      const isExpired = now > expiry
      const timeLeft = Math.max(0, Math.floor((expiry - now) / 1000))
      
      console.log(`   过期时间: ${expiry.toLocaleString()}`)
      console.log(`   当前时间: ${now.toLocaleString()}`)
      console.log(`   状态: ${isExpired ? '❌ 已过期' : `✅ 有效 (剩余${timeLeft}秒)`}`)
      
      return !isExpired
    }

    // 测试未过期的PIN码
    const futureTime = new Date(Date.now() + 30 * 60 * 1000)
    checkPinExpiry(futureTime)

    // 测试已过期的PIN码
    const pastTime = new Date(Date.now() - 5 * 60 * 1000)
    checkPinExpiry(pastTime)

    // 5. 查看充值记录统计
    console.log('\n5️⃣ 充值记录统计...')
    
    const depositStats = await prisma.depositRecord.groupBy({
      by: ['method', 'status'],
      _count: {
        id: true
      },
      _sum: {
        amount: true
      }
    })

    console.log('📊 充值统计:')
    depositStats.forEach(stat => {
      console.log(`   ${stat.method} (${stat.status}): ${stat._count.id}笔, 总额${stat._sum.amount || 0} USDT`)
    })

    // 6. 测试支付方式说明
    console.log('\n6️⃣ 支付方式说明...')
    
    const paymentMethods = {
      'chain': {
        name: '链上支付',
        description: 'USDT-TRC20转账',
        features: ['手续费低', '到账快', 'PIN码验证', '需要交易哈希']
      },
      'binance_qr': {
        name: '币安扫码',
        description: '扫码支付',
        features: ['便捷快速', '多币种', 'PIN码验证', '需要订单号']
      }
    }

    Object.entries(paymentMethods).forEach(([key, method]) => {
      console.log(`💰 ${method.name} (${key}):`)
      console.log(`   描述: ${method.description}`)
      console.log(`   特点: ${method.features.join(', ')}`)
      console.log('')
    })

    // 7. 测试数据清理提示
    console.log('\n7️⃣ 测试数据清理...')
    
    const testRecords = await prisma.depositRecord.findMany({
      where: {
        metadata: {
          path: ['test'],
          equals: true
        }
      }
    })

    console.log(`📋 找到 ${testRecords.length} 条测试充值记录`)
    
    if (testRecords.length > 0) {
      console.log('💡 提示: 可以使用以下命令清理测试数据:')
      console.log('   DELETE FROM DepositRecord WHERE JSON_EXTRACT(metadata, "$.test") = true;')
    }

    console.log('\n🎉 新充值功能测试完成!')

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

async function showNewFeaturesSummary() {
  console.log('\n📋 新充值功能总结:')
  console.log('='.repeat(50))
  
  console.log('\n🔄 主要改进:')
  console.log('✅ 删除了原来的USDT、币安支付、BNB支付方式')
  console.log('✅ 新增链上支付 (USDT-TRC20)')
  console.log('✅ 新增币安扫码支付')
  console.log('✅ 采用PIN码验证系统')
  console.log('✅ 支持交易哈希和订单号验证')
  
  console.log('\n🔐 PIN码验证系统:')
  console.log('• 每笔充值生成唯一8位PIN码')
  console.log('• 有效期30分钟')
  console.log('• 支付后输入PIN码和交易信息')
  console.log('• 验证成功后立即到账')
  
  console.log('\n💰 支付方式:')
  console.log('1. 链上支付:')
  console.log('   • 向指定钱包地址转账USDT')
  console.log('   • 需要提供交易哈希')
  console.log('   • 手续费低，到账快')
  
  console.log('\n2. 币安扫码支付:')
  console.log('   • 扫描二维码使用币安APP支付')
  console.log('   • 需要提供币安订单号')
  console.log('   • 便捷快速，支持多币种')
  
  console.log('\n🛡️ 安全特性:')
  console.log('• PIN码唯一性保证')
  console.log('• 时间限制防止滥用')
  console.log('• 交易信息双重验证')
  console.log('• 完整的审计日志')
  
  console.log('\n🧪 测试步骤:')
  console.log('1. 访问 http://localhost:3000/deposit')
  console.log('2. 登录测试账户 (<EMAIL> / 123456)')
  console.log('3. 选择充值方式并输入金额')
  console.log('4. 获取PIN码和支付信息')
  console.log('5. 完成支付后使用PIN码验证')
  console.log('6. 验证成功后余额更新')
}

async function main() {
  console.log('🚀 BitMarket 新充值功能测试')
  console.log('='.repeat(50))
  
  try {
    await testNewDepositFeatures()
    showNewFeaturesSummary()
    
    console.log('\n🎯 测试完成!')
    console.log('现在可以访问 http://localhost:3000/deposit 体验新的充值功能')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  testNewDepositFeatures
}
