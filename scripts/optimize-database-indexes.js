#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')
const { performance } = require('perf_hooks')

const prisma = new PrismaClient()

async function optimizeDatabaseIndexes() {
  try {
    console.log('🗄️  优化数据库索引')
    console.log('='.repeat(50))

    // 1. 检查当前查询性能
    console.log('\n📊 检查当前查询性能...')
    
    const tests = [
      {
        name: '用户邮箱查询',
        query: () => prisma.user.findUnique({ where: { email: '<EMAIL>' } })
      },
      {
        name: '用户ID查询',
        query: () => prisma.user.findUnique({ where: { userId: 'user-2jfxqypdeky' } })
      },
      {
        name: '产品状态查询',
        query: () => prisma.product.findMany({ where: { status: 'AVAILABLE' }, take: 10 })
      },
      {
        name: '产品卖家查询',
        query: () => prisma.product.findMany({ where: { sellerId: 'cmd8desog0002v9rwq6iekv1c' }, take: 10 })
      },
      {
        name: '安全日志查询',
        query: () => prisma.securityLog.findMany({ take: 20, orderBy: { createdAt: 'desc' } })
      }
    ]

    const beforeOptimization = {}
    for (const test of tests) {
      const start = performance.now()
      await test.query()
      const time = performance.now() - start
      beforeOptimization[test.name] = time
      console.log(`  ${test.name}: ${time.toFixed(2)}ms`)
    }

    // 2. 创建数据库索引
    console.log('\n🔧 创建数据库索引...')
    
    const indexQueries = [
      // 用户表索引
      'CREATE INDEX IF NOT EXISTS idx_user_email ON "User"(email);',
      'CREATE INDEX IF NOT EXISTS idx_user_userId ON "User"("userId");',
      'CREATE INDEX IF NOT EXISTS idx_user_role ON "User"(role);',
      'CREATE INDEX IF NOT EXISTS idx_user_status ON "User"(status);',
      
      // 产品表索引
      'CREATE INDEX IF NOT EXISTS idx_product_status ON "Product"(status);',
      'CREATE INDEX IF NOT EXISTS idx_product_sellerId ON "Product"("sellerId");',
      'CREATE INDEX IF NOT EXISTS idx_product_category ON "Product"(category);',
      'CREATE INDEX IF NOT EXISTS idx_product_city ON "Product"(city);',
      'CREATE INDEX IF NOT EXISTS idx_product_createdAt ON "Product"("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_product_status_createdAt ON "Product"(status, "createdAt");',
      
      // 安全日志表索引
      'CREATE INDEX IF NOT EXISTS idx_securityLog_userId ON "SecurityLog"("userId");',
      'CREATE INDEX IF NOT EXISTS idx_securityLog_createdAt ON "SecurityLog"("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_securityLog_action ON "SecurityLog"(action);',
      'CREATE INDEX IF NOT EXISTS idx_securityLog_userId_createdAt ON "SecurityLog"("userId", "createdAt");',
      
      // 订单表索引
      'CREATE INDEX IF NOT EXISTS idx_order_buyerId ON "Order"("buyerId");',
      'CREATE INDEX IF NOT EXISTS idx_order_sellerId ON "Order"("sellerId");',
      'CREATE INDEX IF NOT EXISTS idx_order_status ON "Order"(status);',
      'CREATE INDEX IF NOT EXISTS idx_order_createdAt ON "Order"("createdAt");',
      
      // 消息表索引
      'CREATE INDEX IF NOT EXISTS idx_message_senderId ON "Message"("senderId");',
      'CREATE INDEX IF NOT EXISTS idx_message_receiverId ON "Message"("receiverId");',
      'CREATE INDEX IF NOT EXISTS idx_message_createdAt ON "Message"("createdAt");',
      
      // 评价表索引
      'CREATE INDEX IF NOT EXISTS idx_review_productId ON "Review"("productId");',
      'CREATE INDEX IF NOT EXISTS idx_review_reviewerId ON "Review"("reviewerId");',
      'CREATE INDEX IF NOT EXISTS idx_review_createdAt ON "Review"("createdAt");'
    ]

    for (const query of indexQueries) {
      try {
        await prisma.$executeRawUnsafe(query)
        const indexName = query.match(/idx_\w+/)?.[0] || 'unknown'
        console.log(`  ✅ 创建索引: ${indexName}`)
      } catch (error) {
        if (error.message.includes('already exists')) {
          const indexName = query.match(/idx_\w+/)?.[0] || 'unknown'
          console.log(`  ✅ 索引已存在: ${indexName}`)
        } else {
          console.log(`  ❌ 索引创建失败: ${error.message}`)
        }
      }
    }

    // 3. 测试优化后的性能
    console.log('\n📈 测试优化后的性能...')
    
    const afterOptimization = {}
    for (const test of tests) {
      const start = performance.now()
      await test.query()
      const time = performance.now() - start
      afterOptimization[test.name] = time
      console.log(`  ${test.name}: ${time.toFixed(2)}ms`)
    }

    // 4. 性能对比
    console.log('\n📊 性能优化对比:')
    console.log('='.repeat(50))
    
    let totalImprovement = 0
    let improvedQueries = 0
    
    for (const testName of Object.keys(beforeOptimization)) {
      const before = beforeOptimization[testName]
      const after = afterOptimization[testName]
      const improvement = ((before - after) / before) * 100
      
      if (improvement > 0) {
        console.log(`✅ ${testName}:`)
        console.log(`   优化前: ${before.toFixed(2)}ms`)
        console.log(`   优化后: ${after.toFixed(2)}ms`)
        console.log(`   提升: ${improvement.toFixed(1)}%`)
        totalImprovement += improvement
        improvedQueries++
      } else {
        console.log(`➖ ${testName}: ${before.toFixed(2)}ms → ${after.toFixed(2)}ms (无明显变化)`)
      }
      console.log('')
    }

    const avgImprovement = improvedQueries > 0 ? totalImprovement / improvedQueries : 0
    console.log(`📈 平均性能提升: ${avgImprovement.toFixed(1)}%`)
    console.log(`🎯 优化查询数量: ${improvedQueries}/${Object.keys(beforeOptimization).length}`)

    // 5. 数据库统计信息更新
    console.log('\n🔄 更新数据库统计信息...')
    try {
      await prisma.$executeRaw`ANALYZE;`
      console.log('✅ 数据库统计信息已更新')
    } catch (error) {
      console.log('⚠️  统计信息更新失败:', error.message)
    }

    // 6. 生成优化报告
    const report = {
      timestamp: new Date().toISOString(),
      indexesCreated: indexQueries.length,
      performanceTests: Object.keys(beforeOptimization).length,
      averageImprovement: avgImprovement,
      improvedQueries,
      beforeOptimization,
      afterOptimization
    }

    // 保存报告
    const fs = require('fs').promises
    await fs.mkdir('test-results/performance', { recursive: true })
    await fs.writeFile(
      'test-results/performance/database-optimization-report.json',
      JSON.stringify(report, null, 2)
    )

    console.log('\n✅ 数据库索引优化完成')
    console.log(`📊 创建了 ${indexQueries.length} 个索引`)
    console.log(`📈 平均性能提升 ${avgImprovement.toFixed(1)}%`)
    console.log('💾 优化报告已保存: test-results/performance/database-optimization-report.json')

    return report

  } catch (error) {
    console.error('❌ 数据库优化失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 运行优化
if (require.main === module) {
  optimizeDatabaseIndexes()
    .then(() => {
      console.log('\n🎉 数据库优化成功完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n💥 数据库优化失败:', error)
      process.exit(1)
    })
}

module.exports = { optimizeDatabaseIndexes }
