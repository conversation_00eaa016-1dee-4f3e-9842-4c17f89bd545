const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDepositDisplayLogic() {
  console.log('🔧 测试保证金页面显示逻辑...\n')

  try {
    // 1. 检查现有用户的交易数据
    console.log('1. 检查现有用户的交易数据...')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        creditScore: true,
        depositBalance: true
      },
      take: 5
    })

    console.log(`✅ 找到 ${users.length} 个用户`)
    
    for (const user of users) {
      console.log(`\n   用户: ${user.name} (${user.email})`)
      
      // 计算用户的订单统计
      const totalOrders = await prisma.order.count({
        where: {
          OR: [
            { buyerId: user.id },
            { sellerId: user.id }
          ]
        }
      })

      const completedOrders = await prisma.order.count({
        where: {
          OR: [
            { buyerId: user.id },
            { sellerId: user.id }
          ],
          status: {
            in: ['COMPLETED', 'DELIVERED', 'CONFIRMED']
          }
        }
      })

      const positiveReviews = Math.floor(completedOrders * 0.85) // 模拟85%好评率

      console.log(`     总订单数: ${totalOrders}`)
      console.log(`     成功订单数: ${completedOrders}`)
      console.log(`     好评数: ${positiveReviews}`)
      console.log(`     信用分: ${user.creditScore}`)

      // 模拟前端显示逻辑
      console.log('\n     前端显示效果:')
      
      // 信誉等级显示
      if (totalOrders === 0) {
        console.log(`     信誉等级: 无 (暂无交易数据)`)
      } else {
        const trustLevel = Math.min(5, Math.max(1, Math.floor((user.creditScore + (completedOrders / totalOrders * 100)) / 40)))
        const stars = '⭐'.repeat(Math.min(trustLevel, 5))
        const emptyStars = '☆'.repeat(Math.max(0, 5 - trustLevel))
        console.log(`     信誉等级: ${stars}${emptyStars} (等级 ${trustLevel}/5)`)
      }
      
      // 履约率显示
      if (totalOrders === 0) {
        console.log(`     履约率: 暂无 (暂无交易数据)`)
      } else {
        const fulfillmentRate = (completedOrders / totalOrders) * 100
        console.log(`     履约率: ${fulfillmentRate.toFixed(1)}% (共${totalOrders}笔)`)
      }
      
      // 综合信任评分显示
      if (totalOrders === 0) {
        console.log(`     综合信任评分: 无 (暂无评价数据)`)
      } else {
        const trustScore = (positiveReviews / totalOrders) * 100
        console.log(`     综合信任评分: ${trustScore.toFixed(1)} (基于用户评价)`)
      }
    }

    // 2. 创建测试用户场景
    console.log('\n2. 创建测试用户场景...')
    
    // 场景1: 无交易数据的新用户
    console.log('\n   场景1: 无交易数据的新用户')
    let newUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!newUser) {
      newUser = await prisma.user.create({
        data: {
          name: '新用户',
          email: '<EMAIL>',
          creditScore: 30,
          depositBalance: 1000,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建新用户: ${newUser.name}`)
    } else {
      console.log(`   ✅ 已有新用户: ${newUser.name}`)
    }
    
    console.log('   显示效果:')
    console.log('     信誉等级: 无 (暂无交易数据)')
    console.log('     履约率: 暂无 (暂无交易数据)')
    console.log('     综合信任评分: 无 (暂无评价数据)')

    // 场景2: 有交易数据的用户
    console.log('\n   场景2: 有交易数据的用户')
    let experiencedUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!experiencedUser) {
      experiencedUser = await prisma.user.create({
        data: {
          name: '经验用户',
          email: '<EMAIL>',
          creditScore: 85,
          depositBalance: 5000,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建经验用户: ${experiencedUser.name}`)
    } else {
      console.log(`   ✅ 已有经验用户: ${experiencedUser.name}`)
    }
    
    // 模拟有交易数据的情况
    const mockTotalOrders = 20
    const mockCompletedOrders = 18
    const mockPositiveReviews = 15
    
    console.log('   模拟数据:')
    console.log(`     总订单: ${mockTotalOrders}`)
    console.log(`     成功订单: ${mockCompletedOrders}`)
    console.log(`     好评数: ${mockPositiveReviews}`)
    
    console.log('   显示效果:')
    const mockTrustLevel = Math.min(5, Math.max(1, Math.floor((85 + (mockCompletedOrders / mockTotalOrders * 100)) / 40)))
    const mockStars = '⭐'.repeat(Math.min(mockTrustLevel, 5))
    const mockEmptyStars = '☆'.repeat(Math.max(0, 5 - mockTrustLevel))
    console.log(`     信誉等级: ${mockStars}${mockEmptyStars} (等级 ${mockTrustLevel}/5)`)
    
    const mockFulfillmentRate = (mockCompletedOrders / mockTotalOrders) * 100
    console.log(`     履约率: ${mockFulfillmentRate.toFixed(1)}% (共${mockTotalOrders}笔)`)
    
    const mockTrustScore = (mockPositiveReviews / mockTotalOrders) * 100
    console.log(`     综合信任评分: ${mockTrustScore.toFixed(1)} (基于用户评价)`)

    // 3. 验证API数据结构
    console.log('\n3. 验证API数据结构...')
    
    console.log('   ✅ API应该返回的字段:')
    console.log('     user.totalOrders - 总订单数')
    console.log('     user.successfulOrders - 成功订单数')
    console.log('     user.positiveReviews - 好评数')
    console.log('     user.trustLevel - 信誉等级')
    console.log('     user.fulfillmentRate - 履约率')
    
    console.log('   ✅ 前端计算逻辑:')
    console.log('     信誉等级: totalOrders > 0 ? 显示星级 : "无"')
    console.log('     履约率: totalOrders > 0 ? (successfulOrders/totalOrders)*100 : "暂无"')
    console.log('     信任评分: totalOrders > 0 ? (positiveReviews/totalOrders)*100 : "无"')

    // 4. 边界情况测试
    console.log('\n4. 边界情况测试...')
    
    const testCases = [
      { name: '完全新用户', totalOrders: 0, successfulOrders: 0, positiveReviews: 0 },
      { name: '单笔成功', totalOrders: 1, successfulOrders: 1, positiveReviews: 1 },
      { name: '部分成功', totalOrders: 10, successfulOrders: 8, positiveReviews: 7 },
      { name: '全部成功', totalOrders: 5, successfulOrders: 5, positiveReviews: 5 },
      { name: '无好评', totalOrders: 3, successfulOrders: 3, positiveReviews: 0 }
    ]
    
    testCases.forEach(testCase => {
      console.log(`\n   ${testCase.name}:`)
      console.log(`     数据: ${testCase.totalOrders}总/${testCase.successfulOrders}成功/${testCase.positiveReviews}好评`)
      
      if (testCase.totalOrders === 0) {
        console.log(`     信誉等级: 无`)
        console.log(`     履约率: 暂无`)
        console.log(`     信任评分: 无`)
      } else {
        const fulfillmentRate = (testCase.successfulOrders / testCase.totalOrders) * 100
        const trustScore = (testCase.positiveReviews / testCase.totalOrders) * 100
        console.log(`     履约率: ${fulfillmentRate.toFixed(1)}%`)
        console.log(`     信任评分: ${trustScore.toFixed(1)}`)
      }
    })

    console.log('\n🎉 测试完成！')
    console.log('\n📝 手动测试步骤:')
    
    console.log('\n【步骤1: 测试新用户显示】')
    console.log('1. 使用新用户账户登录 (<EMAIL>)')
    console.log('2. 访问保证金页面: http://localhost:3000/deposit')
    console.log('3. 查看信誉等级卡片')
    console.log('4. 确认显示:')
    console.log('   - 信誉等级: "无" (暂无交易数据)')
    console.log('   - 履约率: "暂无" (暂无交易数据)')
    console.log('   - 综合信任评分: "无" (暂无评价数据)')
    
    console.log('\n【步骤2: 测试有数据用户显示】')
    console.log('1. 使用有交易记录的用户登录')
    console.log('2. 访问保证金页面')
    console.log('3. 确认显示实际计算的数值和百分比')
    
    console.log('\n【步骤3: 验证计算准确性】')
    console.log('1. 检查履约率公式: (成功订单数 ÷ 总订单数) × 100%')
    console.log('2. 检查信任评分公式: (好评数 ÷ 总订单数) × 100%')
    console.log('3. 确认除零保护正常工作')

    console.log('\n💡 显示规则总结:')
    console.log('   信誉等级: 无交易数据时显示"无"，有数据时显示星级')
    console.log('   履约率: 无交易数据时显示"暂无"，有数据时显示百分比')
    console.log('   信任评分: 无评价数据时显示"无"，有数据时显示分数')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testDepositDisplayLogic().catch(console.error)
}

module.exports = { testDepositDisplayLogic }
