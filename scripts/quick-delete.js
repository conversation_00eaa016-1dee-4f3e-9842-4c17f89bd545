/**
 * 快速删除脚本
 * 提供常用的快速删除命令
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// 删除所有测试数据
async function deleteTestData() {
  console.log('🧪 删除测试数据...')
  
  try {
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
    
    // 删除测试用户的相关数据
    const result = await prisma.user.deleteMany({
      where: {
        email: {
          in: testEmails
        }
      }
    })
    
    console.log(`✅ 删除了 ${result.count} 个测试用户`)
    
    // 删除孤立的数据
    await prisma.review.deleteMany({
      where: {
        reviewer: null
      }
    })
    
    await prisma.message.deleteMany({
      where: {
        sender: null
      }
    })
    
    await prisma.product.deleteMany({
      where: {
        seller: null
      }
    })
    
    await prisma.order.deleteMany({
      where: {
        buyer: null
      }
    })
    
    await prisma.demand.deleteMany({
      where: {
        user: null
      }
    })
    
    console.log('✅ 清理了孤立数据')
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 删除所有数据
async function deleteAllData() {
  console.log('🗑️  删除所有数据...')
  
  try {
    // 按依赖关系顺序删除
    const tables = [
      'review',
      'message', 
      'demandOffer',
      'demand',
      'orderItem',
      'order',
      'product',
      'account',
      'user'
    ]
    
    for (const table of tables) {
      const result = await prisma[table].deleteMany()
      console.log(`✅ ${table}: 删除了 ${result.count} 条记录`)
    }
    
    console.log('🎉 所有数据删除完成!')
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 清理特定用户的数据
async function deleteUserData(email) {
  console.log(`🗑️  删除用户数据: ${email}`)
  
  try {
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      console.log('❌ 用户不存在')
      return
    }
    
    // 删除用户相关数据
    await prisma.review.deleteMany({
      where: {
        OR: [
          { reviewerId: user.id },
          { revieweeId: user.id }
        ]
      }
    })
    
    await prisma.message.deleteMany({
      where: {
        OR: [
          { senderId: user.id },
          { receiverId: user.id }
        ]
      }
    })
    
    await prisma.demandOffer.deleteMany({
      where: {
        demand: {
          userId: user.id
        }
      }
    })
    
    await prisma.demand.deleteMany({
      where: { userId: user.id }
    })
    
    await prisma.orderItem.deleteMany({
      where: {
        order: {
          OR: [
            { buyerId: user.id },
            { sellerId: user.id }
          ]
        }
      }
    })
    
    await prisma.order.deleteMany({
      where: {
        OR: [
          { buyerId: user.id },
          { sellerId: user.id }
        ]
      }
    })
    
    await prisma.product.deleteMany({
      where: { sellerId: user.id }
    })
    
    await prisma.account.deleteMany({
      where: { userId: user.id }
    })
    
    await prisma.user.delete({
      where: { id: user.id }
    })
    
    console.log(`✅ 用户 ${email} 及相关数据已删除`)
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 重置数据库到初始状态
async function resetToClean() {
  console.log('🔄 重置数据库到干净状态...')
  
  try {
    await deleteAllData()
    
    console.log('✅ 数据库已重置为干净状态')
    
  } catch (error) {
    console.error('❌ 重置失败:', error.message)
  }
}

// 主函数
async function main() {
  const command = process.argv[2]
  const param = process.argv[3]
  
  switch (command) {
    case 'test':
      await deleteTestData()
      break
      
    case 'all':
      await deleteAllData()
      break
      
    case 'user':
      if (!param) {
        console.log('❌ 请提供用户邮箱: node quick-delete.<NAME_EMAIL>')
        return
      }
      await deleteUserData(param)
      break
      
    case 'reset':
      await resetToClean()
      break
      
    default:
      console.log('🗑️  快速删除工具')
      console.log('='.repeat(30))
      console.log('使用方法:')
      console.log('  node quick-delete.js test     # 删除测试数据')
      console.log('  node quick-delete.js all      # 删除所有数据')
      console.log('  node quick-delete.js user <email>  # 删除特定用户')
      console.log('  node quick-delete.js reset    # 重置数据库')
      console.log('')
      console.log('示例:')
      console.log('  node quick-delete.js test')
      console.log('  node quick-delete.<NAME_EMAIL>')
  }
  
  await prisma.$disconnect()
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  deleteTestData,
  deleteAllData,
  deleteUserData,
  resetToClean
}
