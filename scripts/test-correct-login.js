const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testCorrectLogin() {
  try {
    console.log('=== 测试正确的NextAuth登录流程 ===\n');
    
    // 创建axios实例来保持cookies
    const client = axios.create({
      baseURL: 'http://localhost:3000',
      timeout: 10000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // 1. 获取CSRF token
    console.log('1. 获取CSRF token...');
    try {
      const csrfResponse = await client.get('/api/auth/csrf');
      console.log('CSRF响应状态:', csrfResponse.status);
      console.log('CSRF数据:', JSON.stringify(csrfResponse.data, null, 2));
      
      const csrfToken = csrfResponse.data.csrfToken;
      
      // 2. 使用正确的NextAuth登录端点
      console.log('\n2. 使用正确的登录端点...');
      const loginData = new URLSearchParams({
        email: '<EMAIL>',
        password: '123456',
        csrfToken: csrfToken,
        callbackUrl: 'http://localhost:3000',
        redirect: 'false'
      });
      
      const loginResponse = await client.post('/api/auth/callback/credentials', loginData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      console.log('登录响应状态:', loginResponse.status);
      console.log('登录响应头:', loginResponse.headers);
      
      // 检查是否有重定向
      if (loginResponse.status === 302 || loginResponse.headers.location) {
        console.log('登录重定向到:', loginResponse.headers.location);
      }
      
      // 检查cookies
      const setCookieHeaders = loginResponse.headers['set-cookie'];
      if (setCookieHeaders) {
        console.log('设置的Cookies:');
        setCookieHeaders.forEach((cookie, index) => {
          console.log(`${index + 1}. ${cookie}`);
        });
      } else {
        console.log('❌ 没有设置cookies');
      }
      
    } catch (error) {
      console.log('❌ 登录流程失败');
      if (error.response) {
        console.log(`状态码: ${error.response.status}`);
        console.log(`响应头:`, error.response.headers);
        console.log(`错误信息:`, error.response.data);
      } else {
        console.log(`错误: ${error.message}`);
      }
    }
    
    // 3. 测试session
    console.log('\n3. 测试session...');
    try {
      const sessionResponse = await client.get('/api/auth/session');
      console.log('Session响应状态:', sessionResponse.status);
      console.log('Session数据:', JSON.stringify(sessionResponse.data, null, 2));
      
      if (sessionResponse.data && sessionResponse.data.user) {
        console.log('✅ 登录成功！');
        
        // 4. 测试我们的API
        console.log('\n4. 测试认证API...');
        const authTestResponse = await client.get('/api/test-auth');
        console.log('认证测试响应:', JSON.stringify(authTestResponse.data, null, 2));
        
        // 5. 测试中间人分配API
        console.log('\n5. 测试中间人分配API...');
        const mediatorResponse = await client.post('/api/mediator/auto-assign', {
          orderAmount: 999
        });
        console.log('中间人分配响应:', JSON.stringify(mediatorResponse.data, null, 2));
        
      } else {
        console.log('❌ 登录失败，session为空');
      }
      
    } catch (error) {
      console.log('❌ Session测试失败');
      if (error.response) {
        console.log(`状态码: ${error.response.status}`);
        console.log(`错误信息:`, JSON.stringify(error.response.data, null, 2));
      } else {
        console.log(`错误: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCorrectLogin();
