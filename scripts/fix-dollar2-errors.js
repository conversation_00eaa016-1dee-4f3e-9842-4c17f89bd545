#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

class Dollar2ErrorFixer {
  constructor() {
    this.fixes = []
    this.errors = []
  }

  // 获取所有需要检查的文件
  getAllFiles() {
    const files = []
    const extensions = ['.tsx', '.ts', '.jsx', '.js']
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir)
        items.forEach(item => {
          const fullPath = path.join(dir, item)
          const stat = fs.statSync(fullPath)
          
          if (stat.isDirectory() && 
              !item.startsWith('.') && 
              item !== 'node_modules' && 
              item !== 'test-results') {
            scanDir(fullPath)
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            files.push(fullPath)
          }
        })
      } catch (error) {
        // 忽略访问错误
      }
    }
    
    scanDir('app')
    scanDir('components')
    scanDir('lib')
    
    return files
  }

  // 修复单个文件中的 $2 错误
  fixFileErrors(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8')
      let modified = false
      
      // 查找并修复所有错误模式
      const patterns = [
        {
          regex: /(\w+)\?\.\$2/g,
          replacement: '$1.length',
          description: '修复 ?.$2 为 .length'
        },
        {
          regex: /(\w+)\.\$2/g,
          replacement: '$1.length',
          description: '修复 .$2 为 .length'
        },
        {
          regex: /(\w+)\.length\(/g,
          replacement: '$1.map(',
          description: '修复 .length( 为 .map('
        }
      ]
      
      patterns.forEach(pattern => {
        const matches = content.match(pattern.regex)
        if (matches) {
          content = content.replace(pattern.regex, pattern.replacement)
          modified = true
          
          matches.forEach(match => {
            console.log(`  ✅ 修复: ${match} → ${match.replace(pattern.regex, pattern.replacement)}`)
            this.fixes.push({
              file: filePath,
              original: match,
              fixed: match.replace(pattern.regex, pattern.replacement),
              description: pattern.description
            })
          })
        }
      })
      
      if (modified) {
        fs.writeFileSync(filePath, content)
        console.log(`✅ 文件已修复: ${path.relative(process.cwd(), filePath)}`)
      }
      
    } catch (error) {
      console.log(`❌ 修复失败: ${filePath} - ${error.message}`)
      this.errors.push({
        file: filePath,
        error: error.message
      })
    }
  }

  // 运行修复
  async run() {
    console.log('🔧 开始修复 $2 错误')
    console.log('='.repeat(50))
    
    const files = this.getAllFiles()
    console.log(`📊 检查 ${files.length} 个文件...`)
    
    for (const file of files) {
      console.log(`🔍 检查: ${path.relative(process.cwd(), file)}`)
      this.fixFileErrors(file)
    }
    
    console.log('\n📊 修复报告')
    console.log('='.repeat(30))
    console.log(`✅ 成功修复: ${this.fixes.length} 个问题`)
    console.log(`❌ 修复失败: ${this.errors.length} 个问题`)
    
    if (this.fixes.length > 0) {
      console.log('\n✅ 修复详情:')
      this.fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.original} → ${fix.fixed}`)
        console.log(`   📁 ${path.relative(process.cwd(), fix.file)}`)
      })
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 修复失败:')
      this.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.error}`)
        console.log(`   📁 ${path.relative(process.cwd(), error.file)}`)
      })
    }
    
    return {
      fixes: this.fixes.length,
      errors: this.errors.length
    }
  }
}

// 运行修复
if (require.main === module) {
  const fixer = new Dollar2ErrorFixer()
  fixer.run()
    .then((result) => {
      console.log('\n🎉 $2 错误修复完成')
      if (result.errors > 0) {
        console.log('⚠️  部分修复失败，请检查错误日志')
        process.exit(1)
      } else {
        console.log('✅ 所有 $2 错误修复成功')
        process.exit(0)
      }
    })
    .catch(error => {
      console.error('\n❌ 修复过程失败:', error)
      process.exit(1)
    })
}

module.exports = { Dollar2ErrorFixer }
