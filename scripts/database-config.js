/**
 * 数据库配置管理工具
 * 显示当前配置、切换数据库类型、管理端口等
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// 读取当前数据库配置
function getCurrentDatabaseConfig() {
  const envPath = path.join(process.cwd(), '.env')
  const envLocalPath = path.join(process.cwd(), '.env.local')
  
  let envContent = ''
  
  // 优先读取 .env.local，然后是 .env
  if (fs.existsSync(envLocalPath)) {
    envContent = fs.readFileSync(envLocalPath, 'utf8')
  } else if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8')
  } else {
    return null
  }
  
  // 解析 DATABASE_URL
  const databaseUrlMatch = envContent.match(/DATABASE_URL\s*=\s*["']?([^"'\n]+)["']?/)
  
  if (!databaseUrlMatch) {
    return null
  }
  
  const databaseUrl = databaseUrlMatch[1]
  
  // 分析数据库类型和配置
  let config = {
    url: databaseUrl,
    type: 'unknown',
    host: null,
    port: null,
    database: null,
    username: null,
    file: null
  }
  
  if (databaseUrl.startsWith('file:')) {
    config.type = 'sqlite'
    config.file = databaseUrl.replace('file:', '')
  } else if (databaseUrl.startsWith('postgresql://')) {
    config.type = 'postgresql'
    const match = databaseUrl.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/)
    if (match) {
      config.username = match[1]
      config.host = match[3]
      config.port = parseInt(match[4])
      config.database = match[5]
    }
  } else if (databaseUrl.startsWith('mysql://')) {
    config.type = 'mysql'
    const match = databaseUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/)
    if (match) {
      config.username = match[1]
      config.host = match[3]
      config.port = parseInt(match[4])
      config.database = match[5]
    }
  }
  
  return config
}

// 显示当前数据库配置
function showCurrentConfig() {
  console.log('📊 当前数据库配置')
  console.log('='.repeat(40))
  
  const config = getCurrentDatabaseConfig()
  
  if (!config) {
    console.log('❌ 未找到数据库配置')
    return
  }
  
  console.log(`数据库类型: ${config.type.toUpperCase()}`)
  console.log(`连接URL: ${config.url}`)
  
  if (config.type === 'sqlite') {
    console.log(`数据库文件: ${config.file}`)
    console.log('端口号: 不适用 (文件数据库)')
    
    // 检查文件是否存在
    const dbPath = path.join(process.cwd(), 'prisma', config.file.replace('./', ''))
    const exists = fs.existsSync(dbPath)
    console.log(`文件状态: ${exists ? '存在' : '不存在'}`)
    
    if (exists) {
      const stats = fs.statSync(dbPath)
      console.log(`文件大小: ${(stats.size / 1024).toFixed(2)} KB`)
      console.log(`最后修改: ${stats.mtime.toLocaleString()}`)
    }
    
  } else if (config.type === 'postgresql' || config.type === 'mysql') {
    console.log(`主机地址: ${config.host}`)
    console.log(`端口号: ${config.port}`)
    console.log(`数据库名: ${config.database}`)
    console.log(`用户名: ${config.username}`)
    
    // 显示默认端口信息
    const defaultPorts = {
      postgresql: 5432,
      mysql: 3306
    }
    
    if (config.port === defaultPorts[config.type]) {
      console.log(`✅ 使用默认端口`)
    } else {
      console.log(`⚠️  使用自定义端口 (默认: ${defaultPorts[config.type]})`)
    }
  }
  
  return config
}

// 显示数据库端口信息
function showPortInfo() {
  console.log('\n🔌 数据库端口信息')
  console.log('='.repeat(40))
  
  const portInfo = {
    'SQLite': {
      port: '不适用',
      description: '文件数据库，不需要网络端口',
      advantages: ['无需配置', '轻量级', '适合开发环境'],
      disadvantages: ['不支持并发写入', '不适合生产环境']
    },
    'PostgreSQL': {
      port: '5432 (默认)',
      description: '企业级关系数据库',
      advantages: ['高性能', '支持复杂查询', '适合生产环境'],
      disadvantages: ['需要额外安装', '配置相对复杂']
    },
    'MySQL': {
      port: '3306 (默认)',
      description: '流行的关系数据库',
      advantages: ['广泛使用', '性能良好', '社区支持好'],
      disadvantages: ['某些高级功能有限', '需要额外安装']
    },
    'Redis': {
      port: '6379 (默认)',
      description: '内存数据库，用于缓存',
      advantages: ['极高性能', '支持多种数据结构'],
      disadvantages: ['数据存储在内存中', '主要用于缓存']
    }
  }
  
  Object.entries(portInfo).forEach(([db, info]) => {
    console.log(`\n${db}:`)
    console.log(`  端口: ${info.port}`)
    console.log(`  描述: ${info.description}`)
    console.log(`  优势: ${info.advantages.join(', ')}`)
    console.log(`  劣势: ${info.disadvantages.join(', ')}`)
  })
}

// 切换数据库配置
async function switchDatabase() {
  console.log('\n🔄 切换数据库配置')
  console.log('='.repeat(40))
  
  console.log('选择数据库类型:')
  console.log('1. SQLite (推荐用于开发)')
  console.log('2. PostgreSQL (推荐用于生产)')
  console.log('3. MySQL')
  console.log('4. 返回主菜单')
  
  const choice = await askQuestion('\n请选择 (1-4): ')
  
  let newConfig = ''
  
  switch (choice) {
    case '1':
      newConfig = 'DATABASE_URL="file:./dev.db"'
      break
      
    case '2':
      const pgHost = await askQuestion('PostgreSQL 主机地址 (默认: localhost): ') || 'localhost'
      const pgPort = await askQuestion('PostgreSQL 端口 (默认: 5432): ') || '5432'
      const pgUser = await askQuestion('用户名 (默认: postgres): ') || 'postgres'
      const pgPass = await askQuestion('密码: ')
      const pgDb = await askQuestion('数据库名 (默认: bitmarket): ') || 'bitmarket'
      
      newConfig = `DATABASE_URL="postgresql://${pgUser}:${pgPass}@${pgHost}:${pgPort}/${pgDb}"`
      break
      
    case '3':
      const mysqlHost = await askQuestion('MySQL 主机地址 (默认: localhost): ') || 'localhost'
      const mysqlPort = await askQuestion('MySQL 端口 (默认: 3306): ') || '3306'
      const mysqlUser = await askQuestion('用户名 (默认: root): ') || 'root'
      const mysqlPass = await askQuestion('密码: ')
      const mysqlDb = await askQuestion('数据库名 (默认: bitmarket): ') || 'bitmarket'
      
      newConfig = `DATABASE_URL="mysql://${mysqlUser}:${mysqlPass}@${mysqlHost}:${mysqlPort}/${mysqlDb}"`
      break
      
    case '4':
      return
      
    default:
      console.log('❌ 无效选择')
      return
  }
  
  // 更新配置文件
  const envLocalPath = path.join(process.cwd(), '.env.local')
  let envContent = ''
  
  if (fs.existsSync(envLocalPath)) {
    envContent = fs.readFileSync(envLocalPath, 'utf8')
  }
  
  // 替换或添加 DATABASE_URL
  if (envContent.includes('DATABASE_URL=')) {
    envContent = envContent.replace(/DATABASE_URL\s*=\s*["']?[^"'\n]+["']?/, newConfig)
  } else {
    envContent = `${newConfig}\n${envContent}`
  }
  
  fs.writeFileSync(envLocalPath, envContent)
  
  console.log('\n✅ 数据库配置已更新')
  console.log(`新配置: ${newConfig}`)
  console.log('\n⚠️  请注意:')
  console.log('1. 重新启动应用以使配置生效')
  console.log('2. 运行数据库迁移: npx prisma migrate dev')
  console.log('3. 如果切换数据库类型，需要更新 schema.prisma 中的 provider')
}

// 测试数据库连接
async function testConnection() {
  console.log('\n🔍 测试数据库连接')
  console.log('='.repeat(40))
  
  try {
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()
    
    console.log('正在连接数据库...')
    
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    // 尝试查询用户数量
    const userCount = await prisma.user.count()
    console.log(`📊 用户数量: ${userCount}`)
    
    await prisma.$disconnect()
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 可能的解决方案:')
      console.log('1. 检查数据库服务是否启动')
      console.log('2. 确认主机地址和端口号正确')
      console.log('3. 检查防火墙设置')
    } else if (error.message.includes('authentication')) {
      console.log('\n💡 可能的解决方案:')
      console.log('1. 检查用户名和密码')
      console.log('2. 确认用户有访问权限')
    }
  }
}

// 主菜单
async function showMenu() {
  console.log('\n🗃️  数据库配置管理工具')
  console.log('='.repeat(40))
  console.log('1. 显示当前配置')
  console.log('2. 显示端口信息')
  console.log('3. 切换数据库')
  console.log('4. 测试连接')
  console.log('5. 退出')
  
  return await askQuestion('\n请选择操作 (1-5): ')
}

// 主函数
async function main() {
  const command = process.argv[2]
  
  if (command) {
    // 命令行模式
    switch (command) {
      case 'show':
      case 'config':
        showCurrentConfig()
        break
        
      case 'port':
      case 'ports':
        showPortInfo()
        break
        
      case 'test':
        await testConnection()
        break
        
      default:
        console.log('🗃️  数据库配置管理工具')
        console.log('='.repeat(30))
        console.log('使用方法:')
        console.log('  node database-config.js show    # 显示当前配置')
        console.log('  node database-config.js port    # 显示端口信息')
        console.log('  node database-config.js test    # 测试连接')
        console.log('')
        console.log('或者直接运行 node database-config.js 进入交互模式')
    }
  } else {
    // 交互模式
    while (true) {
      const choice = await showMenu()
      
      switch (choice) {
        case '1':
          showCurrentConfig()
          break
          
        case '2':
          showPortInfo()
          break
          
        case '3':
          await switchDatabase()
          break
          
        case '4':
          await testConnection()
          break
          
        case '5':
          console.log('👋 再见!')
          rl.close()
          return
          
        default:
          console.log('❌ 无效选择，请重试')
      }
      
      // 暂停一下让用户看到结果
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }
  
  rl.close()
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  getCurrentDatabaseConfig,
  showCurrentConfig,
  showPortInfo,
  testConnection
}
