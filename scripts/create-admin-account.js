const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdminAccount() {
  console.log('🔧 创建管理员账号...\n')

  try {
    const email = '<EMAIL>'
    const password = '123456'
    const name = '系统管理员'

    // 检查用户是否已存在
    console.log('1. 检查用户是否已存在...')
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      console.log(`⚠️  用户 ${email} 已存在`)
      
      // 更新为管理员权限
      console.log('2. 更新用户为管理员权限...')
      const updatedUser = await prisma.user.update({
        where: { email },
        data: {
          role: 'ADMIN',
          name: name
        }
      })
      
      console.log(`✅ 用户 ${email} 已更新为管理员`)
      console.log(`📋 用户信息:`)
      console.log(`   - ID: ${updatedUser.id}`)
      console.log(`   - 用户ID: ${updatedUser.userId}`)
      console.log(`   - 邮箱: ${updatedUser.email}`)
      console.log(`   - 姓名: ${updatedUser.name}`)
      console.log(`   - 角色: ${updatedUser.role}`)
      console.log(`   - 信用分: ${updatedUser.creditScore}`)
      
      return updatedUser
    }

    // 生成用户ID
    console.log('2. 生成用户ID...')
    const generateUserId = () => {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let result = 'user-'
      for (let i = 0; i < 12; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }

    let userId = generateUserId()
    
    // 确保用户ID唯一
    let existingUserId = await prisma.user.findUnique({
      where: { userId }
    })
    
    while (existingUserId) {
      userId = generateUserId()
      existingUserId = await prisma.user.findUnique({
        where: { userId }
      })
    }
    
    console.log(`✅ 生成用户ID: ${userId}`)

    // 加密密码
    console.log('3. 加密密码...')
    const hashedPassword = await bcrypt.hash(password, 12)
    console.log('✅ 密码加密完成')

    // 创建用户
    console.log('4. 创建管理员用户...')
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        userId,
        role: 'ADMIN',
        creditScore: 50, // 管理员默认信用分
        emailVerified: new Date(), // 管理员账号默认已验证
        binanceUid: null
      }
    })

    console.log(`✅ 管理员账号创建成功！`)
    console.log(`📋 账号信息:`)
    console.log(`   - ID: ${newUser.id}`)
    console.log(`   - 用户ID: ${newUser.userId}`)
    console.log(`   - 邮箱: ${newUser.email}`)
    console.log(`   - 密码: ${password}`)
    console.log(`   - 姓名: ${newUser.name}`)
    console.log(`   - 角色: ${newUser.role}`)
    console.log(`   - 信用分: ${newUser.creditScore}`)
    console.log(`   - 邮箱验证: ${newUser.emailVerified ? '已验证' : '未验证'}`)

    // 验证登录
    console.log('\n5. 验证登录功能...')
    const loginTest = await bcrypt.compare(password, newUser.password)
    console.log(`✅ 密码验证: ${loginTest ? '成功' : '失败'}`)

    // 检查管理员权限
    console.log('\n6. 验证管理员权限...')
    const adminUser = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        userId: true,
        creditScore: true
      }
    })

    if (adminUser && adminUser.role === 'ADMIN') {
      console.log('✅ 管理员权限验证成功')
    } else {
      console.log('❌ 管理员权限验证失败')
    }

    console.log('\n🎉 管理员账号创建完成！')
    console.log('\n📝 登录信息:')
    console.log(`   邮箱: ${email}`)
    console.log(`   密码: ${password}`)
    console.log('\n🔗 可以使用以下链接登录:')
    console.log('   http://localhost:3000/auth/signin')

    return newUser

  } catch (error) {
    console.error('❌ 创建管理员账号失败:', error)
    
    if (error.code === 'P2002') {
      console.log('💡 提示: 该邮箱或用户ID已被使用')
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
if (require.main === module) {
  createAdminAccount().catch(console.error)
}

module.exports = { createAdminAccount }
