#!/bin/bash

# ============================================================================
# BitMarket Docker 配置验证脚本
# Docker Configuration Test Script for BitMarket
# ============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "============================================================================"
echo "BitMarket Docker 配置验证"
echo "============================================================================"

# 1. 检查必需文件
log_info "检查 Docker 配置文件..."

required_files=(
    "docker-compose.yml"
    ".env.docker"
    "mysql-config/my.cnf"
    "mysql-init/01-init.sql"
    "scripts/docker-setup.sh"
    "scripts/docker-manage.sh"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        log_success "✓ $file 存在"
    else
        log_error "✗ $file 缺失"
    fi
done

# 2. 检查 Docker Compose 配置语法
log_info "验证 Docker Compose 配置语法..."
if docker-compose config > /dev/null 2>&1; then
    log_success "✓ docker-compose.yml 语法正确"
else
    log_warning "⚠ 无法验证 docker-compose.yml (Docker daemon 未运行)"
fi

# 3. 检查环境变量配置
log_info "检查环境变量配置..."
if [ -f ".env.docker" ]; then
    # 检查关键环境变量
    required_vars=(
        "MYSQL_ROOT_PASSWORD"
        "MYSQL_DATABASE"
        "MYSQL_USER"
        "MYSQL_PASSWORD"
        "DATABASE_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" .env.docker; then
            log_success "✓ $var 已配置"
        else
            log_error "✗ $var 未配置"
        fi
    done
else
    log_error "✗ .env.docker 文件不存在"
fi

# 4. 检查 MySQL 配置文件
log_info "检查 MySQL 配置文件..."
if [ -f "mysql-config/my.cnf" ]; then
    # 检查关键配置项
    config_items=(
        "character-set-server"
        "collation-server"
        "innodb_buffer_pool_size"
        "max_connections"
    )
    
    for item in "${config_items[@]}"; do
        if grep -q "$item" mysql-config/my.cnf; then
            log_success "✓ $item 已配置"
        else
            log_warning "⚠ $item 未找到"
        fi
    done
else
    log_error "✗ mysql-config/my.cnf 文件不存在"
fi

# 5. 检查初始化脚本
log_info "检查数据库初始化脚本..."
if [ -f "mysql-init/01-init.sql" ]; then
    if grep -q "CREATE DATABASE" mysql-init/01-init.sql; then
        log_success "✓ 数据库创建脚本存在"
    else
        log_warning "⚠ 数据库创建脚本可能不完整"
    fi
else
    log_error "✗ mysql-init/01-init.sql 文件不存在"
fi

# 6. 检查脚本权限
log_info "检查脚本执行权限..."
scripts=(
    "scripts/docker-setup.sh"
    "scripts/docker-manage.sh"
)

for script in "${scripts[@]}"; do
    if [ -x "$script" ]; then
        log_success "✓ $script 有执行权限"
    else
        log_warning "⚠ $script 没有执行权限"
        chmod +x "$script" 2>/dev/null && log_success "  已修复执行权限" || log_error "  无法修复执行权限"
    fi
done

# 7. 检查 package.json 脚本
log_info "检查 npm 脚本配置..."
if [ -f "package.json" ]; then
    docker_scripts=(
        "docker:setup"
        "docker:start"
        "docker:stop"
        "docker:status"
        "docker:logs"
    )
    
    for script in "${docker_scripts[@]}"; do
        if grep -q "\"$script\":" package.json; then
            log_success "✓ npm run $script 已配置"
        else
            log_error "✗ npm run $script 未配置"
        fi
    done
else
    log_error "✗ package.json 文件不存在"
fi

# 8. 检查 Prisma 配置
log_info "检查 Prisma 配置..."
if [ -f "prisma/schema.prisma" ]; then
    if grep -q "provider.*=.*\"mysql\"" prisma/schema.prisma; then
        log_success "✓ Prisma 配置为 MySQL"
    else
        log_warning "⚠ Prisma 可能未配置为 MySQL"
    fi
    
    if grep -q "binaryTargets.*=.*\[" prisma/schema.prisma; then
        log_success "✓ Prisma binaryTargets 已配置"
    else
        log_warning "⚠ Prisma binaryTargets 可能未配置"
    fi
else
    log_error "✗ prisma/schema.prisma 文件不存在"
fi

# 9. 生成配置摘要
echo ""
log_info "配置摘要："
echo "  数据库类型: MySQL 8.0"
echo "  数据库名称: bitmarket"
echo "  数据库用户: bitmarket_user"
echo "  数据库端口: 3306"
echo "  Redis 端口: 6379 (可选)"
echo "  数据持久化: 已配置"
echo "  备份功能: 已配置"

# 10. 提供下一步指导
echo ""
log_info "下一步操作指南："
echo "  1. 启动 Docker Desktop 应用程序"
echo "  2. 运行: npm run docker:setup"
echo "  3. 复制环境配置: cp .env.docker .env.local"
echo "  4. 启动应用: npm run dev"

echo ""
log_info "故障排除："
echo "  - 如果 Docker daemon 未运行，请启动 Docker Desktop"
echo "  - 如果端口冲突，请修改 .env.docker 中的端口配置"
echo "  - 如果权限问题，请运行: sudo usermod -aG docker \$USER"

echo ""
log_success "Docker 配置验证完成！"
