#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

class SyntaxChecker {
  constructor() {
    this.errors = []
  }

  // 检查特定文件的语法
  checkFile(filePath) {
    console.log(`🔍 检查文件: ${filePath}`)
    
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const lines = content.split('\n')
      
      // 检查括号匹配
      this.checkBrackets(content, filePath)
      
      // 检查模板字符串
      this.checkTemplateStrings(content, filePath)
      
      // 检查JSX语法
      this.checkJSXSyntax(lines, filePath)
      
      // 检查函数定义
      this.checkFunctionSyntax(lines, filePath)
      
    } catch (error) {
      this.errors.push({
        file: filePath,
        type: 'file-read',
        message: `无法读取文件: ${error.message}`
      })
    }
  }

  // 检查括号匹配
  checkBrackets(content, filePath) {
    const brackets = {
      '(': ')',
      '[': ']',
      '{': '}'
    }
    
    const stack = []
    const lines = content.split('\n')
    
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      
      for (let charIndex = 0; charIndex < line.length; charIndex++) {
        const char = line[charIndex]
        
        if (brackets[char]) {
          stack.push({
            char,
            line: lineIndex + 1,
            column: charIndex + 1,
            expected: brackets[char]
          })
        } else if (Object.values(brackets).includes(char)) {
          if (stack.length === 0) {
            this.errors.push({
              file: filePath,
              type: 'unmatched-bracket',
              line: lineIndex + 1,
              column: charIndex + 1,
              message: `意外的闭合括号: ${char}`
            })
          } else {
            const last = stack.pop()
            if (last.expected !== char) {
              this.errors.push({
                file: filePath,
                type: 'mismatched-bracket',
                line: lineIndex + 1,
                column: charIndex + 1,
                message: `括号不匹配: 期望 ${last.expected}，但找到 ${char}`,
                openLine: last.line
              })
            }
          }
        }
      }
    }
    
    // 检查未闭合的括号
    if (stack.length > 0) {
      stack.forEach(bracket => {
        this.errors.push({
          file: filePath,
          type: 'unclosed-bracket',
          line: bracket.line,
          column: bracket.column,
          message: `未闭合的括号: ${bracket.char}`
        })
      })
    }
  }

  // 检查模板字符串
  checkTemplateStrings(content, filePath) {
    const lines = content.split('\n')
    
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      
      // 检查模板字符串的开始和结束
      let inTemplate = false
      let templateStart = -1
      
      for (let charIndex = 0; charIndex < line.length; charIndex++) {
        const char = line[charIndex]
        
        if (char === '`') {
          if (!inTemplate) {
            inTemplate = true
            templateStart = charIndex
          } else {
            inTemplate = false
          }
        }
      }
      
      // 如果行末仍在模板字符串中，检查是否正确
      if (inTemplate && !line.includes('`', templateStart + 1)) {
        // 这可能是多行模板字符串，检查后续行
        let found = false
        for (let nextLine = lineIndex + 1; nextLine < lines.length; nextLine++) {
          if (lines[nextLine].includes('`')) {
            found = true
            break
          }
          if (nextLine - lineIndex > 50) { // 避免检查太多行
            break
          }
        }
        
        if (!found) {
          this.errors.push({
            file: filePath,
            type: 'unclosed-template',
            line: lineIndex + 1,
            column: templateStart + 1,
            message: '未闭合的模板字符串'
          })
        }
      }
    }
  }

  // 检查JSX语法
  checkJSXSyntax(lines, filePath) {
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      const lineNum = lineIndex + 1
      
      // 检查JSX标签
      const jsxTagRegex = /<(\w+)([^>]*?)>/g
      let match
      
      while ((match = jsxTagRegex.exec(line)) !== null) {
        const tagName = match[1]
        const attributes = match[2]
        
        // 检查自闭合标签
        const selfClosingTags = ['img', 'input', 'br', 'hr', 'meta', 'link']
        if (selfClosingTags.includes(tagName) && !attributes.includes('/')) {
          this.errors.push({
            file: filePath,
            type: 'jsx-self-closing',
            line: lineNum,
            message: `${tagName} 标签应该自闭合`,
            suggestion: `<${tagName}${attributes} />`
          })
        }
        
        // 检查属性语法
        if (attributes.includes('=') && !attributes.includes('"') && !attributes.includes("'") && !attributes.includes('{')) {
          this.errors.push({
            file: filePath,
            type: 'jsx-attribute',
            line: lineNum,
            message: 'JSX属性值需要引号或大括号'
          })
        }
      }
    }
  }

  // 检查函数语法
  checkFunctionSyntax(lines, filePath) {
    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      const lineNum = lineIndex + 1
      
      // 检查函数定义
      if (line.includes('function ') || (line.includes('const ') && line.includes('= ('))) {
        // 检查函数体是否有对应的闭合括号
        let braceCount = 0
        let foundOpenBrace = false
        
        for (let i = lineIndex; i < lines.length && i < lineIndex + 100; i++) {
          const currentLine = lines[i]
          
          for (const char of currentLine) {
            if (char === '{') {
              braceCount++
              foundOpenBrace = true
            } else if (char === '}') {
              braceCount--
              if (braceCount === 0 && foundOpenBrace) {
                // 函数正确闭合
                return
              }
            }
          }
        }
        
        if (foundOpenBrace && braceCount > 0) {
          this.errors.push({
            file: filePath,
            type: 'unclosed-function',
            line: lineNum,
            message: '函数可能没有正确闭合'
          })
        }
      }
    }
  }

  // 生成报告
  generateReport() {
    console.log('\n📊 语法检查报告')
    console.log('='.repeat(50))
    
    if (this.errors.length === 0) {
      console.log('✅ 没有发现语法错误')
      return { success: true, errors: [] }
    }
    
    console.log(`🚨 发现 ${this.errors.length} 个语法问题:`)
    
    // 按文件分组
    const errorsByFile = {}
    this.errors.forEach(error => {
      if (!errorsByFile[error.file]) {
        errorsByFile[error.file] = []
      }
      errorsByFile[error.file].push(error)
    })
    
    Object.entries(errorsByFile).forEach(([file, errors]) => {
      console.log(`\n📁 ${path.relative(process.cwd(), file)}:`)
      errors.forEach((error, index) => {
        console.log(`  ${index + 1}. [${error.type.toUpperCase()}] ${error.message}`)
        if (error.line) {
          console.log(`     📍 行 ${error.line}${error.column ? `:${error.column}` : ''}`)
        }
        if (error.suggestion) {
          console.log(`     💡 建议: ${error.suggestion}`)
        }
      })
    })
    
    return { success: false, errors: this.errors }
  }

  // 运行检查
  async run() {
    console.log('🔍 开始语法检查')
    console.log('='.repeat(50))
    
    // 检查问题文件
    const problemFile = 'app/admin/announcements/create/page.tsx'
    this.checkFile(problemFile)
    
    const report = this.generateReport()
    
    return report
  }
}

// 运行检查
if (require.main === module) {
  const checker = new SyntaxChecker()
  checker.run()
    .then((report) => {
      if (report.success) {
        console.log('\n✅ 语法检查通过')
        process.exit(0)
      } else {
        console.log('\n❌ 发现语法错误')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n❌ 语法检查失败:', error)
      process.exit(1)
    })
}

module.exports = { SyntaxChecker }
