const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testFrozenFundsFeature() {
  console.log('🔧 测试冻结资金功能...\n')

  try {
    // 1. 检查现有用户数据
    console.log('1. 检查现有用户数据...')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true
      },
      take: 5
    })

    console.log(`✅ 找到 ${users.length} 个用户`)
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (${user.email}) - 余额: ${user.depositBalance} USDT`)
    })

    // 2. 创建测试用户（如果需要）
    console.log('\n2. 确保有测试用户...')
    
    let testUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          name: '冻结资金测试用户',
          email: '<EMAIL>',
          depositBalance: 10000, // 给足够的余额用于测试
          creditScore: 80,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建测试用户: ${testUser.name}`)
    } else {
      // 确保测试用户有足够余额
      await prisma.user.update({
        where: { id: testUser.id },
        data: { depositBalance: 10000 }
      })
      console.log(`   ✅ 更新测试用户余额: ${testUser.name}`)
    }

    // 3. 模拟冻结资金操作
    console.log('\n3. 模拟冻结资金操作...')
    
    // 创建一些担保充值记录
    const depositAmount = 5000
    await prisma.fundTransaction.create({
      data: {
        userId: testUser.id,
        type: 'GUARANTEE_DEPOSIT',
        amount: -depositAmount, // 负数表示从保证金转出
        description: '测试充值到冻结资金'
      }
    })
    console.log(`   ✅ 创建担保充值记录: ${depositAmount} USDT`)

    // 先创建一个测试产品
    let testProduct = await prisma.product.findFirst({
      where: { title: '测试冻结资金产品' }
    })

    if (!testProduct) {
      testProduct = await prisma.product.create({
        data: {
          title: '测试冻结资金产品',
          description: '用于测试冻结资金功能的产品',
          price: 100,
          sellerId: testUser.id,
          category: 'TEST',
          status: 'AVAILABLE'
        }
      })
    }

    // 创建一些活跃担保订单（使用现有字段）
    const guaranteeOrder = await prisma.order.create({
      data: {
        orderNumber: `TEST-${Date.now()}`,
        buyerId: testUser.id,
        sellerId: testUser.id, // 简化测试，使用同一用户
        mediatorId: testUser.id, // 使用mediatorId作为担保人
        productId: testProduct.id,
        productPrice: 100,
        totalAmount: 100,
        escrowAmount: 2000, // 使用escrowAmount作为担保金额
        status: 'PENDING',
        paymentMethod: 'USDT'
      }
    })
    console.log(`   ✅ 创建活跃担保订单: ${guaranteeOrder.orderNumber} (锁定 2000 USDT)`)

    // 4. 计算资金状态
    console.log('\n4. 计算资金状态...')
    
    const guaranteeDeposits = await prisma.fundTransaction.aggregate({
      where: {
        userId: testUser.id,
        type: 'GUARANTEE_DEPOSIT'
      },
      _sum: { amount: true }
    })

    const guaranteeWithdrawals = await prisma.fundTransaction.aggregate({
      where: {
        userId: testUser.id,
        type: 'GUARANTEE_WITHDRAWAL'
      },
      _sum: { amount: true }
    })

    const totalFrozenFunds = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)

    const activeGuarantees = await prisma.order.aggregate({
      where: {
        mediatorId: testUser.id,
        status: {
          in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS']
        }
      },
      _sum: { escrowAmount: true }
    })

    const lockedAmount = Math.abs(activeGuarantees._sum.escrowAmount || 0)
    const availableAmount = Math.max(0, totalFrozenFunds - lockedAmount)

    console.log('   资金状态:')
    console.log(`     总冻结资金: ${totalFrozenFunds} USDT`)
    console.log(`     已锁定金额: ${lockedAmount} USDT`)
    console.log(`     可用金额: ${availableAmount} USDT`)

    // 5. 验证业务逻辑
    console.log('\n5. 验证业务逻辑...')
    
    console.log('   ✅ 资金约束规则:')
    console.log('     - 最低余额约束: 冻结资金余额 ≥ 活跃担保订单总额')
    console.log('     - 提现限制: 可提现金额 = 总冻结资金 - 锁定金额')
    console.log('     - 实时验证: 提现前检查剩余资金是否足够')
    
    const isConstraintSatisfied = totalFrozenFunds >= lockedAmount
    console.log(`   约束检查: ${isConstraintSatisfied ? '✅ 满足' : '❌ 不满足'}`)
    
    if (availableAmount > 0) {
      console.log(`   可以提现: ${availableAmount} USDT`)
    } else {
      console.log('   当前无法提现 (所有资金已锁定)')
    }

    // 6. 测试API数据结构
    console.log('\n6. 测试API数据结构...')
    
    const mockApiResponse = {
      balance: {
        total: totalFrozenFunds,
        available: availableAmount,
        locked: lockedAmount
      },
      activeGuarantees: [
        {
          id: guaranteeOrder.id,
          orderNumber: guaranteeOrder.orderNumber,
          amount: guaranteeOrder.escrowAmount,
          status: guaranteeOrder.status,
          createdAt: guaranteeOrder.createdAt
        }
      ],
      transactions: [
        {
          id: 'test-tx-1',
          type: 'DEPOSIT',
          amount: depositAmount,
          status: 'COMPLETED',
          createdAt: new Date()
        }
      ]
    }
    
    console.log('   ✅ API响应结构:')
    console.log(`     balance.total: ${mockApiResponse.balance.total}`)
    console.log(`     balance.available: ${mockApiResponse.balance.available}`)
    console.log(`     balance.locked: ${mockApiResponse.balance.locked}`)
    console.log(`     activeGuarantees: ${mockApiResponse.activeGuarantees.length} 个`)
    console.log(`     transactions: ${mockApiResponse.transactions.length} 个`)

    // 7. 操作场景测试
    console.log('\n7. 操作场景测试...')
    
    const testScenarios = [
      {
        name: '充值操作',
        type: 'deposit',
        amount: 1000,
        userBalance: 10000,
        expectedResult: '成功'
      },
      {
        name: '正常提现',
        type: 'withdraw',
        amount: availableAmount > 0 ? Math.min(1000, availableAmount) : 0,
        expectedResult: availableAmount > 0 ? '成功' : '失败 (无可用金额)'
      },
      {
        name: '超额提现',
        type: 'withdraw',
        amount: availableAmount + 1000,
        expectedResult: '失败 (超过可用金额)'
      }
    ]
    
    testScenarios.forEach((scenario, index) => {
      console.log(`\n   场景 ${index + 1}: ${scenario.name}`)
      console.log(`     操作: ${scenario.type === 'deposit' ? '充值' : '提现'} ${scenario.amount} USDT`)
      console.log(`     预期结果: ${scenario.expectedResult}`)
      
      if (scenario.type === 'deposit') {
        console.log(`     验证: 用户余额 (${scenario.userBalance || 0}) >= 充值金额 (${scenario.amount})`)
      } else {
        console.log(`     验证: 可用金额 (${availableAmount}) >= 提现金额 (${scenario.amount})`)
      }
    })

    console.log('\n🎉 测试完成！')
    console.log('\n📝 手动测试步骤:')
    
    console.log('\n【步骤1: 访问保证金页面】')
    console.log(`1. 使用测试账户登录: ${testUser.email}`)
    console.log('2. 访问保证金页面: http://localhost:3000/deposit')
    console.log('3. 确认"担保池"已改名为"冻结资金"')
    console.log('4. 点击"冻结资金"卡片')
    
    console.log('\n【步骤2: 测试冻结资金管理页面】')
    console.log('1. 确认跳转到: http://localhost:3000/funds/frozen')
    console.log('2. 查看资金概览卡片显示')
    console.log('3. 验证数据准确性:')
    console.log(`   - 总冻结资金: ${totalFrozenFunds} USDT`)
    console.log(`   - 可用金额: ${availableAmount} USDT`)
    console.log(`   - 已锁定金额: ${lockedAmount} USDT`)
    
    console.log('\n【步骤3: 测试充值功能】')
    console.log('1. 点击"充值到冻结资金"按钮')
    console.log('2. 输入充值金额 (如 1000)')
    console.log('3. 点击"确认充值"')
    console.log('4. 验证操作成功和数据更新')
    
    console.log('\n【步骤4: 测试提现功能】')
    console.log('1. 点击"提现到可用余额"按钮')
    console.log(`2. 输入提现金额 (最多 ${availableAmount} USDT)`)
    console.log('3. 点击"确认提现"')
    console.log('4. 验证操作成功和数据更新')
    
    console.log('\n【步骤5: 测试约束验证】')
    console.log('1. 尝试提现超过可用金额')
    console.log('2. 确认显示错误提示')
    console.log('3. 验证活跃担保订单显示')
    console.log('4. 检查交易记录列表')

    console.log('\n💡 功能特点:')
    console.log('   - 术语更新: "担保池" → "冻结资金"')
    console.log('   - 实时操作: 随时充值/提现')
    console.log('   - 资金约束: 自动计算可用金额')
    console.log('   - 安全验证: 防止超额提现')
    console.log('   - 用户友好: 清晰的界面和提示')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testFrozenFundsFeature().catch(console.error)
}

module.exports = { testFrozenFundsFeature }
