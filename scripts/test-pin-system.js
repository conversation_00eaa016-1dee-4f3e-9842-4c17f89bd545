const { PrismaClient } = require('@prisma/client')
const { generatePinForOrder, verifyPaymentPin } = require('../lib/payment-pin')

const prisma = new PrismaClient()

async function testPinSystem() {
  console.log('🧪 测试PIN验证系统...\n')

  try {
    // 1. 创建测试用户
    console.log('1. 创建测试用户...')
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'PIN测试用户',
        userId: 'user-pintest123',
        creditScore: 30
      }
    })
    console.log(`✅ 测试用户创建成功: ${testUser.email}`)

    // 2. 创建测试商品
    console.log('\n2. 创建测试商品...')
    const testProduct = await prisma.product.create({
      data: {
        title: 'PIN测试商品',
        description: '用于测试PIN验证系统的商品',
        price: 100.00,
        sellerId: testUser.id,
        category: 'GENERAL',
        condition: 'NEW',
        stock: 10,
        status: 'AVAILABLE'
      }
    })
    console.log(`✅ 测试商品创建成功: ${testProduct.title}`)

    // 3. 创建测试订单
    console.log('\n3. 创建测试订单...')
    const testOrder = await prisma.order.create({
      data: {
        orderNumber: 'PIN-TEST-' + Date.now(),
        productId: testProduct.id,
        buyerId: testUser.id,
        sellerId: testUser.id,
        status: 'PENDING_PAYMENT',
        totalAmount: 100.00,
        productPrice: 100.00,
        shippingFee: 0,
        platformFee: 0
      }
    })
    console.log(`✅ 测试订单创建成功: ${testOrder.orderNumber}`)

    // 4. 生成PIN码
    console.log('\n4. 为订单生成PIN码...')
    const pin = await generatePinForOrder(testOrder.id)
    console.log(`✅ PIN码生成成功: ${pin}`)

    // 5. 验证订单PIN信息
    console.log('\n5. 验证订单PIN信息...')
    const orderWithPin = await prisma.order.findUnique({
      where: { id: testOrder.id }
    })
    console.log(`📌 PIN码: ${orderWithPin.paymentPin}`)
    console.log(`⏰ 过期时间: ${orderWithPin.paymentPinExpiry}`)
    console.log(`🔒 是否已使用: ${orderWithPin.paymentPinUsed}`)
    console.log(`🔢 验证尝试次数: ${orderWithPin.verificationAttempts}`)

    // 6. 测试PIN验证 - 错误的PIN
    console.log('\n6. 测试错误PIN验证...')
    const wrongPinResult = await verifyPaymentPin(
      testOrder.id,
      'WRONGPIN',
      'TEST123456789',
      '127.0.0.1',
      'test-agent'
    )
    console.log(`❌ 错误PIN验证结果: ${wrongPinResult.status} - ${wrongPinResult.message}`)

    // 7. 测试PIN验证 - 正确的PIN
    console.log('\n7. 测试正确PIN验证...')
    const correctPinResult = await verifyPaymentPin(
      testOrder.id,
      pin,
      'BINANCE123456789',
      '127.0.0.1',
      'test-agent'
    )
    console.log(`✅ 正确PIN验证结果: ${correctPinResult.status} - ${correctPinResult.message}`)

    // 8. 验证订单状态更新
    console.log('\n8. 验证订单状态更新...')
    const updatedOrder = await prisma.order.findUnique({
      where: { id: testOrder.id }
    })
    console.log(`📋 订单状态: ${updatedOrder.status}`)
    console.log(`💳 支付方式: ${updatedOrder.paymentMethod || '未设置'}`)
    console.log(`🔒 PIN已使用: ${updatedOrder.paymentPinUsed}`)
    console.log(`📝 币安订单号: ${updatedOrder.paymentTxHash}`)

    // 9. 查看验证记录
    console.log('\n9. 查看PIN验证记录...')
    const verificationRecords = await prisma.paymentPinVerification.findMany({
      where: { orderId: testOrder.id },
      orderBy: { verifiedAt: 'desc' }
    })
    
    console.log(`📊 验证记录数量: ${verificationRecords.length}`)
    verificationRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. 状态: ${record.verificationStatus}, PIN: ${record.submittedPin}, 订单号: ${record.submittedOrderNumber}, 时间: ${record.verifiedAt}`)
    })

    // 10. 测试重复验证（应该失败）
    console.log('\n10. 测试重复验证（PIN已使用）...')
    const duplicateResult = await verifyPaymentPin(
      testOrder.id,
      pin,
      'BINANCE987654321',
      '127.0.0.1',
      'test-agent'
    )
    console.log(`❌ 重复验证结果: ${duplicateResult.status} - ${duplicateResult.message}`)

    console.log('\n🎉 PIN验证系统测试完成！')

    // 清理测试数据
    console.log('\n🧹 清理测试数据...')
    await prisma.paymentPinVerification.deleteMany({
      where: { orderId: testOrder.id }
    })
    await prisma.order.delete({
      where: { id: testOrder.id }
    })
    await prisma.product.delete({
      where: { id: testProduct.id }
    })
    await prisma.user.delete({
      where: { id: testUser.id }
    })
    console.log('✅ 测试数据清理完成')

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testPinSystem().catch(console.error)
