#!/usr/bin/env node

/**
 * 测试中间人访问控制
 * 验证不同用户类型的访问权限
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testMediatorAccess() {
  console.log('🔐 测试中间人访问控制...\n')

  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功\n')

    // 1. 创建普通用户（非中间人）
    console.log('1. 创建普通用户（非中间人）...')
    
    let normalUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!normalUser) {
      const normalPassword = await bcrypt.hash('123456', 12)
      normalUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '普通用户',
          password: normalPassword,
          isMediator: false, // 明确设置为非中间人
          creditScore: 60,
          status: 'ACTIVE'
        }
      })
      console.log('✅ 创建普通用户成功')
    } else {
      // 确保用户不是中间人
      normalUser = await prisma.user.update({
        where: { id: normalUser.id },
        data: {
          isMediator: false,
          mediatorStatus: null,
          mediatorFeeRate: null,
          mediatorDeposit: 0
        }
      })
      console.log('✅ 更新普通用户状态成功')
    }

    console.log(`   邮箱: ${normalUser.email}`)
    console.log(`   ID: ${normalUser.id}`)
    console.log(`   是否为中间人: ${normalUser.isMediator}`)
    console.log()

    // 2. 验证中间人用户
    console.log('2. 验证中间人用户...')
    
    const mediatorUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (mediatorUser) {
      console.log('✅ 中间人用户存在')
      console.log(`   邮箱: ${mediatorUser.email}`)
      console.log(`   ID: ${mediatorUser.id}`)
      console.log(`   是否为中间人: ${mediatorUser.isMediator}`)
      console.log(`   中间人状态: ${mediatorUser.mediatorStatus}`)
    } else {
      console.log('❌ 中间人用户不存在，请先运行 setup-mediator-user.js')
    }
    console.log()

    // 3. 测试访问权限逻辑
    console.log('3. 测试访问权限逻辑...')
    
    const users = [
      { user: normalUser, type: '普通用户' },
      { user: mediatorUser, type: '中间人用户' }
    ]

    for (const { user, type } of users) {
      if (!user) continue
      
      console.log(`\n   ${type} (${user.email}):`)
      
      // 模拟 /profile 页面的显示逻辑
      if (user.isMediator) {
        console.log('     ✅ 在 /profile 页面会显示"中间人控制台"模块')
        console.log('     ✅ 可以访问 /mediator 页面')
        console.log('     ✅ 会显示完整的中间人面板')
      } else {
        console.log('     ❌ 在 /profile 页面不会显示"中间人控制台"模块')
        console.log('     ❌ 访问 /mediator 页面会被重定向到申请页面')
        console.log('     ℹ️  可以通过 /mediator/apply 申请成为中间人')
      }
    }

    // 4. 验证数据库查询
    console.log('\n4. 验证数据库查询...')
    
    const mediatorCount = await prisma.user.count({
      where: { isMediator: true }
    })
    
    const normalUserCount = await prisma.user.count({
      where: { isMediator: false }
    })

    console.log(`   中间人用户数量: ${mediatorCount}`)
    console.log(`   普通用户数量: ${normalUserCount}`)

    // 5. 测试结果总结
    console.log('\n🎉 访问控制测试完成!')
    console.log('\n📋 测试结果:')
    console.log('✅ 中间人用户可以访问 /mediator 页面')
    console.log('✅ 普通用户会被重定向到申请页面')
    console.log('✅ /profile 页面根据用户类型显示不同内容')
    console.log('✅ 数据库权限控制正常工作')

    console.log('\n🔗 测试账号:')
    console.log('   中间人账号: <EMAIL> (密码: 123456)')
    console.log('   普通用户账号: <EMAIL> (密码: 123456)')

    console.log('\n📝 测试步骤:')
    console.log('   1. 使用普通用户登录，访问 /profile，确认没有中间人模块')
    console.log('   2. 直接访问 /mediator，确认被重定向到申请页面')
    console.log('   3. 使用中间人账号登录，访问 /profile，确认有中间人模块')
    console.log('   4. 点击中间人模块，确认可以正常访问 /mediator 页面')

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testMediatorAccess().catch(console.error)
