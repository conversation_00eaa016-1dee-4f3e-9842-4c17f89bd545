#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function setPasswordForTestUser() {
  try {
    console.log('🔧 为测试用户设置密码...\n')

    const email = '<EMAIL>'
    const password = '123456'
    
    // 1. 查找用户
    console.log(`1. 查找用户: ${email}`)
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        password: true,
        createdAt: true
      }
    })

    if (!user) {
      console.log('❌ 用户不存在')
      return
    }

    console.log('✅ 找到用户:')
    console.log(`   ID: ${user.id}`)
    console.log(`   姓名: ${user.name || '未设置'}`)
    console.log(`   邮箱: ${user.email}`)
    console.log(`   角色: ${user.role}`)
    console.log(`   状态: ${user.status}`)
    console.log(`   当前密码: ${user.password ? '已设置' : '未设置'}`)
    console.log(`   创建时间: ${user.createdAt.toLocaleDateString('zh-CN')}`)

    // 2. 生成新密码哈希
    console.log('\n2. 生成密码哈希...')
    const saltRounds = 12
    const hashedPassword = await bcrypt.hash(password, saltRounds)
    console.log('✅ 密码哈希生成完成')

    // 3. 更新用户密码
    console.log('\n3. 更新用户密码...')
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        emailVerified: new Date(), // 确保邮箱已验证
        status: 'ACTIVE', // 确保账户激活
        updatedAt: new Date()
      },
      select: {
        id: true,
        email: true,
        name: true,
        status: true,
        emailVerified: true,
        updatedAt: true
      }
    })

    console.log('✅ 密码更新成功')
    console.log(`   用户ID: ${updatedUser.id}`)
    console.log(`   邮箱: ${updatedUser.email}`)
    console.log(`   姓名: ${updatedUser.name}`)
    console.log(`   状态: ${updatedUser.status}`)
    console.log(`   邮箱验证: ${updatedUser.emailVerified ? '已验证' : '未验证'}`)
    console.log(`   更新时间: ${updatedUser.updatedAt.toLocaleString('zh-CN')}`)

    // 4. 验证新密码
    console.log('\n4. 验证新密码...')
    const isValid = await bcrypt.compare(password, hashedPassword)
    console.log(`✅ 密码验证: ${isValid ? '成功' : '失败'}`)

    console.log('\n🎉 密码设置完成！')
    console.log('\n📝 登录信息:')
    console.log(`   邮箱: ${email}`)
    console.log(`   密码: ${password}`)
    console.log(`   登录地址: http://localhost:3000/auth/signin`)

    console.log('\n💡 使用说明:')
    console.log('   1. 访问登录页面')
    console.log('   2. 输入上述邮箱和密码')
    console.log('   3. 点击登录按钮')
    console.log('   4. 登录成功后可以访问售后申请页面: http://localhost:3000/after-sales')

  } catch (error) {
    console.error('❌ 密码设置失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setPasswordForTestUser()
