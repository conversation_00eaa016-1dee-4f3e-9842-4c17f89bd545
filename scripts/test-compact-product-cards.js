const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testCompactProductCards() {
  console.log('🔍 测试紧凑型商品卡片功能...\n')

  try {
    // 1. 检查商品数据
    console.log('1. 检查商品数据...')
    const products = await prisma.product.findMany({
      where: {
        status: 'AVAILABLE'
      },
      include: {
        seller: {
          select: {
            id: true,
            userId: true,
            name: true,
            avatar: true
          }
        },
        _count: {
          select: {
            favorites: true
          }
        }
      },
      take: 10
    })

    console.log(`✅ 找到 ${products.length} 个可用商品`)

    if (products.length === 0) {
      console.log('⚠️  没有可用商品，创建一些测试商品...')
      
      // 获取用户
      const users = await prisma.user.findMany({ take: 3 })
      if (users.length === 0) {
        console.log('❌ 没有用户，无法创建测试商品')
        return
      }

      // 创建测试商品
      const testProducts = [
        {
          title: 'iPhone 14 Pro Max 256GB',
          description: '全新未拆封，深空黑色，支持5G网络，拍照效果极佳。',
          price: 8999,
          images: '/uploads/iphone14.jpg,/uploads/iphone14-2.jpg',
          category: 'electronics',
          condition: 'new',
          stock: 5,
          city: '北京',
          district: '朝阳区',
          sellerId: users[0].id
        },
        {
          title: 'MacBook Air M2 13英寸',
          description: '轻薄便携，性能强劲，适合办公和学习使用。',
          price: 7999,
          images: '/uploads/macbook.jpg',
          category: 'electronics',
          condition: 'new',
          stock: 3,
          city: '上海',
          district: '浦东新区',
          sellerId: users[1].id
        },
        {
          title: '小米13 Ultra 512GB',
          description: '徕卡影像，骁龙8 Gen2处理器，拍照旗舰。',
          price: 5999,
          images: '/uploads/xiaomi13.jpg',
          category: 'electronics',
          condition: 'like_new',
          stock: 2,
          city: '深圳',
          district: '南山区',
          sellerId: users[2].id
        }
      ]

      for (const productData of testProducts) {
        try {
          const newProduct = await prisma.product.create({
            data: productData,
            include: {
              seller: {
                select: {
                  name: true,
                  userId: true
                }
              }
            }
          })
          console.log(`   ✅ 创建商品: ${newProduct.title} - ${newProduct.seller.name}`)
        } catch (error) {
          console.log(`   ❌ 创建商品失败: ${productData.title}`)
        }
      }
    }

    // 2. 检查商品显示数据
    console.log('\n2. 检查商品显示数据...')
    const displayProducts = await prisma.product.findMany({
      where: {
        status: 'AVAILABLE'
      },
      select: {
        id: true,
        title: true,
        price: true,
        images: true,
        seller: {
          select: {
            name: true,
            userId: true,
            avatar: true
          }
        },
        _count: {
          select: {
            favorites: true
          }
        }
      },
      take: 6
    })

    console.log(`✅ 显示数据准备完成，共 ${displayProducts.length} 个商品`)
    
    displayProducts.forEach((product, index) => {
      const hasImage = product.images && product.images.length > 0
      const favoriteCount = product._count.favorites
      const sellerName = product.seller.name || '匿名用户'
      
      console.log(`   ${index + 1}. ${product.title}`)
      console.log(`      价格: ${product.price} USDT`)
      console.log(`      卖家: ${sellerName}`)
      console.log(`      头像: ${product.seller.avatar ? '有' : '无'}`)
      console.log(`      图片: ${hasImage ? '有' : '无'}`)
      console.log(`      收藏: ${favoriteCount}`)
    })

    // 3. 测试收藏功能数据
    console.log('\n3. 检查收藏功能数据...')
    const favoriteStats = await prisma.favorite.groupBy({
      by: ['productId'],
      _count: {
        productId: true
      },
      orderBy: {
        _count: {
          productId: 'desc'
        }
      },
      take: 5
    })

    console.log(`✅ 收藏统计: ${favoriteStats.length} 个商品有收藏`)
    favoriteStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. 商品ID ${stat.productId}: ${stat._count.productId} 个收藏`)
    })

    // 4. 如果收藏数据不足，创建一些测试收藏
    if (favoriteStats.length < 3) {
      console.log('\n4. 创建测试收藏数据...')
      
      const users = await prisma.user.findMany({ take: 5 })
      const products = await prisma.product.findMany({ take: 3 })
      
      if (users.length > 0 && products.length > 0) {
        // 为每个商品创建一些随机收藏
        for (const product of products) {
          const favoriteCount = Math.floor(Math.random() * 5) + 1
          
          for (let i = 0; i < Math.min(favoriteCount, users.length); i++) {
            try {
              await prisma.favorite.create({
                data: {
                  userId: users[i].id,
                  productId: product.id
                }
              })
            } catch (error) {
              // 忽略重复收藏错误
            }
          }
        }
        
        console.log('✅ 测试收藏数据创建完成')
      }
    }

    // 5. 测试API接口
    console.log('\n5. 测试首页商品API...')
    try {
      const fetch = require('node-fetch')
      const response = await fetch('http://localhost:3000/api/products?page=1&limit=12&sortBy=newest')
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ API接口正常，返回 ${data.products?.length || 0} 个商品`)
        
        if (data.products && data.products.length > 0) {
          const firstProduct = data.products[0]
          console.log('   首个商品数据结构:')
          console.log(`   - 标题: ${firstProduct.title}`)
          console.log(`   - 价格: ${firstProduct.price}`)
          console.log(`   - 卖家: ${firstProduct.seller?.name || '无'}`)
          console.log(`   - 头像: ${firstProduct.seller?.avatar ? '有' : '无'}`)
          console.log(`   - 收藏数: ${firstProduct._count?.favorites || 0}`)
        }
      } else {
        console.log(`❌ API接口错误: ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ API接口测试失败: ${error.message}`)
    }

    // 6. 布局测试建议
    console.log('\n6. 布局测试建议...')
    console.log('✅ 紧凑型商品卡片设计要点:')
    console.log('   - 正方形图片比例 (aspect-square)')
    console.log('   - 网格布局: 手机2列，平板3列，桌面6列')
    console.log('   - 显示内容: 图片、标题、价格、用户头像、用户名、收藏数')
    console.log('   - 交互功能: 收藏按钮、商品链接、用户链接')

    console.log('\n🎉 测试完成！')
    console.log('\n📝 访问信息:')
    console.log('   首页: http://localhost:3000')
    console.log('   商品页: http://localhost:3000/products')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testCompactProductCards().catch(console.error)
}

module.exports = { testCompactProductCards }
