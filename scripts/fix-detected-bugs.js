#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

class BugFixer {
  constructor() {
    this.fixes = []
    this.errors = []
  }

  // 修复JSX标签问题
  async fixJSXIssues() {
    console.log('🔧 修复JSX标签问题...')
    
    const problematicFiles = [
      'app/admin/announcements/create/page.tsx'
    ]
    
    for (const filePath of problematicFiles) {
      if (fs.existsSync(filePath)) {
        try {
          await this.fixJSXInFile(filePath)
        } catch (error) {
          this.errors.push({
            file: filePath,
            error: error.message,
            type: 'jsx-fix'
          })
        }
      }
    }
  }

  // 修复单个文件的JSX问题
  async fixJSXInFile(filePath) {
    console.log(`  🔧 修复文件: ${filePath}`)
    
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    
    // 修复常见的JSX问题
    const fixes = [
      // 修复自闭合标签
      {
        pattern: /<input([^>]*[^\/])>/g,
        replacement: '<input$1 />',
        description: '修复input标签自闭合'
      },
      {
        pattern: /<img([^>]*[^\/])>/g,
        replacement: '<img$1 />',
        description: '修复img标签自闭合'
      },
      {
        pattern: /<br([^>]*[^\/])>/g,
        replacement: '<br$1 />',
        description: '修复br标签自闭合'
      },
      {
        pattern: /<hr([^>]*[^\/])>/g,
        replacement: '<hr$1 />',
        description: '修复hr标签自闭合'
      },
      // 修复textarea标签
      {
        pattern: /<textarea([^>]*)>\s*<\/textarea>/g,
        replacement: '<textarea$1></textarea>',
        description: '规范化textarea标签'
      }
    ]
    
    fixes.forEach(fix => {
      const originalContent = content
      content = content.replace(fix.pattern, fix.replacement)
      if (content !== originalContent) {
        modified = true
        console.log(`    ✅ ${fix.description}`)
        this.fixes.push({
          file: filePath,
          type: 'jsx',
          description: fix.description
        })
      }
    })
    
    if (modified) {
      fs.writeFileSync(filePath, content)
      console.log(`  ✅ 文件已修复: ${filePath}`)
    } else {
      console.log(`  ℹ️  文件无需修复: ${filePath}`)
    }
  }

  // 修复空值引用问题
  async fixNullReferenceIssues() {
    console.log('\n🔧 修复空值引用问题...')
    
    const files = this.getReactFiles()
    
    for (const filePath of files.slice(0, 10)) {
      if (fs.existsSync(filePath)) {
        try {
          await this.fixNullReferencesInFile(filePath)
        } catch (error) {
          this.errors.push({
            file: filePath,
            error: error.message,
            type: 'null-reference-fix'
          })
        }
      }
    }
  }

  // 修复单个文件的空值引用问题
  async fixNullReferencesInFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    
    // 常见的空值引用模式修复
    const fixes = [
      // 修复对象属性访问
      {
        pattern: /(\w+)\.(\w+)(?!\?)/g,
        check: (match, obj, prop) => {
          // 只修复明显可能为空的情况
          return content.includes(`${obj} = null`) || 
                 content.includes(`${obj} = undefined`) ||
                 content.includes(`${obj}?`) ||
                 content.includes(`useState<`) && content.includes(`${obj}`)
        },
        replacement: '$1?.$2',
        description: '添加可选链操作符'
      }
    ]
    
    // 这里我们采用保守的修复策略，只修复明显的问题
    const lines = content.split('\n')
    let hasChanges = false
    
    const modifiedLines = lines.map((line, index) => {
      // 检查是否包含可能的空值引用
      if (line.includes('.') && !line.includes('?.') && !line.includes('//')) {
        // 查找常见的空值引用模式
        const patterns = [
          /(\w+)\.length/g,
          /(\w+)\.map/g,
          /(\w+)\.filter/g,
          /(\w+)\.find/g
        ]
        
        let modifiedLine = line
        patterns.forEach(pattern => {
          if (pattern.test(line)) {
            const match = line.match(pattern)
            if (match) {
              const varName = match[1]
              // 检查这个变量是否可能为空
              if (content.includes(`${varName} = null`) || 
                  content.includes(`${varName} = undefined`) ||
                  content.includes(`useState<`) ||
                  content.includes(`${varName}?`)) {
                modifiedLine = modifiedLine.replace(pattern, `$1?.$2`)
                hasChanges = true
              }
            }
          }
        })
        
        return modifiedLine
      }
      return line
    })
    
    if (hasChanges) {
      const newContent = modifiedLines.join('\n')
      fs.writeFileSync(filePath, newContent)
      console.log(`  ✅ 修复空值引用: ${path.relative(process.cwd(), filePath)}`)
      this.fixes.push({
        file: filePath,
        type: 'null-reference',
        description: '添加可选链操作符'
      })
    }
  }

  // 修复未处理的Promise
  async fixUnhandledPromises() {
    console.log('\n🔧 修复未处理的Promise...')
    
    const files = this.getReactFiles()
    
    for (const filePath of files.slice(0, 5)) {
      if (fs.existsSync(filePath)) {
        try {
          await this.fixPromisesInFile(filePath)
        } catch (error) {
          this.errors.push({
            file: filePath,
            error: error.message,
            type: 'promise-fix'
          })
        }
      }
    }
  }

  // 修复单个文件的Promise问题
  async fixPromisesInFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    let modified = false
    
    const modifiedLines = lines.map((line, index) => {
      // 查找没有错误处理的await调用
      if (line.includes('await') && !line.includes('try') && !line.includes('catch')) {
        // 检查函数上下文是否有try-catch
        const functionStart = this.findFunctionStart(lines, index)
        const functionEnd = this.findFunctionEnd(lines, index)
        const functionContext = lines.slice(functionStart, functionEnd + 1).join('\n')
        
        if (!functionContext.includes('try') && !functionContext.includes('catch')) {
          // 添加错误处理注释提醒
          if (!line.includes('// TODO: Add error handling')) {
            modified = true
            return line + ' // TODO: Add error handling'
          }
        }
      }
      return line
    })
    
    if (modified) {
      const newContent = modifiedLines.join('\n')
      fs.writeFileSync(filePath, newContent)
      console.log(`  ✅ 添加错误处理提醒: ${path.relative(process.cwd(), filePath)}`)
      this.fixes.push({
        file: filePath,
        type: 'promise',
        description: '添加错误处理提醒'
      })
    }
  }

  // 创建安全的组件模板
  async createSafeComponentTemplate() {
    console.log('\n📝 创建安全的组件模板...')
    
    const templateContent = `// 安全的React组件模板
import React, { memo, useCallback, useMemo } from 'react'

interface SafeComponentProps {
  data?: any[]
  title?: string
  onAction?: (item: any) => void
}

const SafeComponent = memo(({ data = [], title, onAction }: SafeComponentProps) => {
  // 使用useMemo优化计算
  const processedData = useMemo(() => {
    return data?.filter(Boolean) || []
  }, [data])

  // 使用useCallback优化事件处理
  const handleAction = useCallback((item: any) => {
    try {
      onAction?.(item)
    } catch (error) {
      console.error('Action failed:', error)
    }
  }, [onAction])

  // 安全的渲染
  return (
    <div className="safe-component">
      {title && <h2>{title}</h2>}
      
      {processedData.length > 0 ? (
        <ul>
          {processedData.map((item, index) => (
            <li key={item?.id || index}>
              <span>{item?.name || '未知项目'}</span>
              {onAction && (
                <button 
                  onClick={() => handleAction(item)}
                  type="button"
                >
                  操作
                </button>
              )}
            </li>
          ))}
        </ul>
      ) : (
        <p>暂无数据</p>
      )}
    </div>
  )
})

SafeComponent.displayName = 'SafeComponent'

export default SafeComponent

// 使用示例:
// <SafeComponent 
//   data={items} 
//   title="项目列表"
//   onAction={(item) => console.log('Action:', item)}
// />
`

    try {
      await fs.promises.mkdir('components/templates', { recursive: true })
      await fs.promises.writeFile('components/templates/SafeComponent.tsx', templateContent)
      
      console.log('  ✅ 安全组件模板已创建: components/templates/SafeComponent.tsx')
      this.fixes.push({
        file: 'components/templates/SafeComponent.tsx',
        type: 'template',
        description: '创建安全的React组件模板'
      })
    } catch (error) {
      this.errors.push({
        file: 'components/templates/SafeComponent.tsx',
        error: error.message,
        type: 'template-creation'
      })
    }
  }

  // 获取React文件列表
  getReactFiles() {
    const files = []
    const extensions = ['.tsx', '.ts', '.jsx', '.js']
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir)
        items.forEach(item => {
          const fullPath = path.join(dir, item)
          const stat = fs.statSync(fullPath)
          
          if (stat.isDirectory() && 
              !item.startsWith('.') && 
              item !== 'node_modules' && 
              item !== 'test-results') {
            scanDir(fullPath)
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            files.push(fullPath)
          }
        })
      } catch (error) {
        // 忽略访问错误
      }
    }
    
    scanDir('app')
    scanDir('components')
    
    return files
  }

  // 查找函数开始位置
  findFunctionStart(lines, currentIndex) {
    for (let i = currentIndex; i >= 0; i--) {
      if (lines[i].includes('function ') || 
          lines[i].includes('const ') && lines[i].includes('= (') ||
          lines[i].includes('const ') && lines[i].includes('= async (')) {
        return i
      }
    }
    return Math.max(0, currentIndex - 10)
  }

  // 查找函数结束位置
  findFunctionEnd(lines, currentIndex) {
    let braceCount = 0
    for (let i = currentIndex; i < lines.length; i++) {
      const line = lines[i]
      braceCount += (line.match(/{/g) || []).length
      braceCount -= (line.match(/}/g) || []).length
      
      if (braceCount === 0 && i > currentIndex) {
        return i
      }
    }
    return Math.min(lines.length - 1, currentIndex + 20)
  }

  // 生成修复报告
  generateFixReport() {
    console.log('\n📊 Bug修复报告')
    console.log('='.repeat(50))
    
    console.log(`✅ 成功修复: ${this.fixes.length}个问题`)
    console.log(`❌ 修复失败: ${this.errors.length}个问题`)
    
    if (this.fixes.length > 0) {
      console.log('\n✅ 修复详情:')
      const fixTypes = {}
      this.fixes.forEach(fix => {
        if (!fixTypes[fix.type]) {
          fixTypes[fix.type] = []
        }
        fixTypes[fix.type].push(fix)
      })
      
      Object.entries(fixTypes).forEach(([type, fixes]) => {
        console.log(`\n${type.toUpperCase()}: ${fixes.length}个修复`)
        fixes.slice(0, 5).forEach(fix => {
          console.log(`  • ${fix.description}`)
          console.log(`    📁 ${path.relative(process.cwd(), fix.file)}`)
        })
      })
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 修复失败:')
      this.errors.forEach(error => {
        console.log(`  • ${error.type}: ${error.error}`)
        console.log(`    📁 ${path.relative(process.cwd(), error.file)}`)
      })
    }
    
    console.log('\n💡 修复建议:')
    console.log('• 定期运行Bug检测脚本')
    console.log('• 使用TypeScript严格模式')
    console.log('• 启用ESLint和Prettier')
    console.log('• 编写单元测试')
    console.log('• 使用安全的组件模板')
    
    return {
      fixes: this.fixes.length,
      errors: this.errors.length,
      details: {
        fixes: this.fixes,
        errors: this.errors
      }
    }
  }

  // 保存修复结果
  async saveResults(report) {
    try {
      await fs.promises.mkdir('test-results/bug-fixes', { recursive: true })
      
      const filename = `bug-fixes-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.promises.writeFile(
        `test-results/bug-fixes/${filename}`,
        JSON.stringify({
          timestamp: Date.now(),
          summary: report,
          details: report.details
        }, null, 2)
      )
      
      console.log(`\n💾 修复结果已保存: test-results/bug-fixes/${filename}`)
      
    } catch (error) {
      console.error('保存修复结果失败:', error)
    }
  }

  // 运行所有修复
  async runAllFixes() {
    console.log('🔧 开始Bug修复')
    console.log('='.repeat(50))
    
    await this.fixJSXIssues()
    await this.fixNullReferenceIssues()
    await this.fixUnhandledPromises()
    await this.createSafeComponentTemplate()
    
    const report = this.generateFixReport()
    await this.saveResults(report)
    
    return report
  }
}

// 运行修复
if (require.main === module) {
  const fixer = new BugFixer()
  fixer.runAllFixes()
    .then((report) => {
      console.log('\n✅ Bug修复完成')
      if (report.errors > 0) {
        console.log('⚠️  部分修复失败，请检查错误日志')
        process.exit(1)
      } else {
        console.log('🎉 所有Bug修复成功')
        process.exit(0)
      }
    })
    .catch(error => {
      console.error('\n❌ Bug修复失败:', error)
      process.exit(1)
    })
}

module.exports = { BugFixer }
