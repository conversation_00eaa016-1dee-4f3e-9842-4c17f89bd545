const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testCompleteFlow() {
  try {
    console.log('=== 测试完整购买流程 ===\n');
    
    const productId = 'cmdr1z4vv00078oqqm2k4gq7v';
    
    // 1. 检查基础数据
    console.log('1. 检查基础数据...');
    
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: { select: { id: true, name: true, role: true } }
      }
    });
    
    if (!product) {
      console.log('❌ 商品不存在');
      return;
    }
    
    console.log(`✅ 商品: ${product.title} (${product.price} USDT)`);
    console.log(`   卖家: ${product.seller.name}`);
    
    // 检查买家用户
    const buyer = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: { id: true, name: true, email: true }
    });
    
    if (!buyer) {
      console.log('❌ 买家用户不存在');
      return;
    }
    
    console.log(`✅ 买家: ${buyer.name} (${buyer.email})`);
    
    // 检查中间人
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        availableBalance: true,
        mediatorFeeRate: true
      }
    });
    
    console.log(`✅ 活跃中间人: ${mediators.length}个`);
    mediators.forEach((m, i) => {
      console.log(`   ${i + 1}. ${m.name} - 余额: ${m.availableBalance} USDT`);
    });
    
    // 2. 检查地址管理功能
    console.log('\n2. 检查地址管理功能...');
    
    const addresses = await prisma.address.findMany({
      where: { userId: buyer.id },
      orderBy: { isDefault: 'desc' }
    });
    
    console.log(`✅ 买家地址数量: ${addresses.length}个`);
    addresses.forEach((addr, i) => {
      console.log(`   ${i + 1}. ${addr.name} - ${addr.province}${addr.city}${addr.district} ${addr.isDefault ? '(默认)' : ''}`);
    });
    
    // 如果没有地址，创建一个测试地址
    if (addresses.length === 0) {
      console.log('   创建测试地址...');
      const newAddress = await prisma.address.create({
        data: {
          name: '测试收件人',
          phone: '13800138000',
          province: '广东省',
          city: '深圳市',
          district: '南山区',
          detail: '科技园南区测试地址123号',
          isDefault: true,
          userId: buyer.id
        }
      });
      console.log(`   ✅ 创建地址成功: ${newAddress.id}`);
    }
    
    // 3. 检查最近的订单状态
    console.log('\n3. 检查最近的订单状态...');
    
    const recentOrders = await prisma.order.findMany({
      where: {
        productId: productId,
        buyerId: buyer.id
      },
      include: {
        escrowOrder: {
          include: {
            mediator: { select: { name: true } }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    });
    
    console.log(`最近订单数量: ${recentOrders.length}个`);
    recentOrders.forEach((order, i) => {
      console.log(`${i + 1}. 订单ID: ${order.id}`);
      console.log(`   状态: ${order.status}`);
      console.log(`   金额: ${order.totalAmount} USDT`);
      console.log(`   创建时间: ${order.createdAt.toISOString()}`);
      if (order.escrowOrder) {
        console.log(`   托管订单: ${order.escrowOrder.id} (${order.escrowOrder.status})`);
        console.log(`   中间人: ${order.escrowOrder.mediator.name}`);
      } else {
        console.log(`   托管订单: 无`);
      }
      console.log('');
    });
    
    // 4. 检查发货管理功能
    console.log('4. 检查发货管理功能...');
    
    const shippedOrders = await prisma.order.findMany({
      where: {
        status: 'SHIPPED',
        sellerId: product.seller.id
      },
      select: {
        id: true,
        orderNumber: true,
        shippingProofText: true,
        shippingProofImages: true,
        trackingNumber: true,
        shippingCompany: true,
        updatedAt: true
      },
      take: 3
    });
    
    console.log(`已发货订单数量: ${shippedOrders.length}个`);
    shippedOrders.forEach((order, i) => {
      console.log(`${i + 1}. 订单: ${order.orderNumber}`);
      console.log(`   更新时间: ${order.updatedAt ? order.updatedAt.toISOString() : '未知'}`);
      console.log(`   快递信息: ${order.shippingCompany || '无'} - ${order.trackingNumber || '无'}`);
      console.log(`   文字凭证: ${order.shippingProofText ? '有' : '无'}`);
      console.log(`   图片凭证: ${order.shippingProofImages ? JSON.parse(order.shippingProofImages).length + '张' : '无'}`);
      console.log('');
    });
    
    // 5. 生成测试报告
    console.log('=== 测试报告 ===');
    
    const issues = [];
    const recommendations = [];
    
    if (product.price < 100) {
      issues.push('商品价格低于托管服务最低要求');
    }
    
    if (mediators.length === 0) {
      issues.push('没有活跃的中间人');
      recommendations.push('激活至少一个中间人账号');
    }
    
    const qualifiedMediators = mediators.filter(m => m.availableBalance >= product.price);
    if (qualifiedMediators.length === 0) {
      issues.push('没有保证金充足的中间人');
      recommendations.push('为中间人账号充值');
    }
    
    if (addresses.length === 0) {
      issues.push('买家没有收货地址');
      recommendations.push('买家需要添加收货地址');
    }
    
    console.log('\n发现的问题:');
    if (issues.length > 0) {
      issues.forEach((issue, i) => {
        console.log(`${i + 1}. ${issue}`);
      });
    } else {
      console.log('✅ 没有发现问题');
    }
    
    console.log('\n建议:');
    if (recommendations.length > 0) {
      recommendations.forEach((rec, i) => {
        console.log(`${i + 1}. ${rec}`);
      });
    } else {
      console.log('✅ 系统配置正常');
    }
    
    // 6. 测试流程步骤
    console.log('\n=== 测试流程步骤 ===');
    console.log('请按以下步骤测试完整流程:');
    console.log('');
    console.log('1. 用户登录:');
    console.log('   - 访问: http://localhost:3000/auth/signin');
    console.log('   - 使用账号: <EMAIL> / 123456');
    console.log('');
    console.log('2. 商品购买:');
    console.log(`   - 访问: http://localhost:3000/products/${productId}`);
    console.log('   - 勾选"使用中间人托管服务"');
    console.log('   - 点击"立即购买"');
    console.log('');
    console.log('3. 地址选择:');
    console.log('   - 应该跳转到地址选择页面');
    console.log('   - 选择或添加收货地址');
    console.log('   - 点击"确认地址，继续下单"');
    console.log('');
    console.log('4. 订单确认:');
    console.log('   - 应该跳转到订单确认页面');
    console.log('   - 检查商品、地址、费用信息');
    console.log('   - 点击"确认下单"');
    console.log('');
    console.log('5. 验证结果:');
    console.log('   - 应该创建普通订单和托管订单');
    console.log('   - 跳转到托管订单详情页面');
    console.log('   - 检查订单状态和信息');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCompleteFlow();
