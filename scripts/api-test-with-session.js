const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args))

// 测试结果统计
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
}

// 通用API请求函数
async function makeRequest(url, options = {}) {
  const baseURL = 'http://localhost:3000'
  const fullURL = `${baseURL}${url}`
  
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'API-Test-Script/1.0',
      ...options.headers
    }
  }
  
  try {
    console.log(`  🔗 请求: ${options.method || 'GET'} ${url}`)
    const response = await fetch(fullURL, { ...defaultOptions, ...options })
    
    let data
    const contentType = response.headers.get('content-type')
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text()
    }
    
    return {
      ok: response.ok,
      status: response.status,
      statusText: response.statusText,
      data
    }
  } catch (error) {
    console.log(`  ❌ 请求失败: ${error.message}`)
    return {
      ok: false,
      status: 0,
      statusText: 'Network Error',
      data: { error: error.message }
    }
  }
}

// 测试API函数
async function testAPI(testName, testFunction) {
  console.log(`🧪 测试: ${testName}`)
  try {
    await testFunction()
    console.log(`✅ ${testName} - 通过`)
    testResults.passed++
  } catch (error) {
    console.log(`❌ ${testName} - 失败: ${error.message}`)
    testResults.failed++
    testResults.errors.push({
      test: testName,
      error: error.message
    })
  }
}

// 1. 公开API测试（不需要认证）
async function testPublicAPIs() {
  await testAPI('获取商品列表API', async () => {
    const response = await makeRequest('/api/products')
    
    if (!response.ok) {
      throw new Error(`获取商品列表失败: ${response.status} ${response.statusText}`)
    }
    
    if (!response.data.products || !Array.isArray(response.data.products)) {
      throw new Error('商品列表响应格式错误')
    }
    
    console.log(`  📝 获取到 ${response.data.products.length} 个商品`)
  })
  
  await testAPI('获取商品详情API', async () => {
    // 先获取商品列表
    const productsResponse = await makeRequest('/api/products')
    if (!productsResponse.ok || !productsResponse.data.products.length) {
      throw new Error('无法获取商品列表')
    }
    
    const productId = productsResponse.data.products[0].id
    const response = await makeRequest(`/api/products/${productId}`)
    
    if (!response.ok) {
      throw new Error(`获取商品详情失败: ${response.status} ${response.statusText}`)
    }
    
    if (!response.data.id || !response.data.title) {
      throw new Error('商品详情响应格式错误')
    }
    
    console.log(`  📝 获取商品详情成功: ${response.data.title}`)
  })
}

// 2. 认证相关API测试
async function testAuthAPIs() {
  await testAPI('NextAuth配置检查', async () => {
    const response = await makeRequest('/api/auth/providers')
    
    if (!response.ok) {
      throw new Error(`获取认证提供者失败: ${response.status} ${response.statusText}`)
    }
    
    console.log('  📝 NextAuth配置正常')
  })
  
  await testAPI('用户注册API', async () => {
    const testEmail = `test-${Date.now()}@example.com`
    const response = await makeRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
        password: '123456',
        name: 'Test User'
      })
    })
    
    if (!response.ok) {
      // 如果是邮箱已存在的错误，也算正常
      if (response.data.error && response.data.error.includes('已存在')) {
        console.log('  📝 注册API正常工作（邮箱已存在）')
        return
      }
      throw new Error(`用户注册失败: ${response.data.error || response.statusText}`)
    }
    
    console.log('  📝 用户注册成功')
  })
}

// 3. 系统状态API测试
async function testSystemAPIs() {
  await testAPI('健康检查API', async () => {
    const response = await makeRequest('/api/health')
    
    // 如果没有健康检查API，返回404是正常的
    if (response.status === 404) {
      console.log('  📝 健康检查API未实现（正常）')
      return
    }
    
    if (!response.ok) {
      throw new Error(`健康检查失败: ${response.status} ${response.statusText}`)
    }
    
    console.log('  📝 系统健康状态正常')
  })
  
  await testAPI('管理员创建状态API', async () => {
    const response = await makeRequest('/api/admin/create')
    
    if (!response.ok) {
      throw new Error(`获取管理员状态失败: ${response.status} ${response.statusText}`)
    }
    
    if (typeof response.data.hasAdmin !== 'boolean') {
      throw new Error('管理员状态响应格式错误')
    }
    
    console.log(`  📝 系统管理员状态: ${response.data.hasAdmin ? '已存在' : '未创建'}`)
  })
}

// 4. 数据库连接测试
async function testDatabaseAPIs() {
  await testAPI('数据库连接测试', async () => {
    // 通过获取商品数量来测试数据库连接
    const response = await makeRequest('/api/products?limit=1')
    
    if (!response.ok) {
      throw new Error(`数据库连接测试失败: ${response.status} ${response.statusText}`)
    }
    
    console.log('  📝 数据库连接正常')
  })
}

// 5. 错误处理测试
async function testErrorHandling() {
  await testAPI('404错误处理', async () => {
    const response = await makeRequest('/api/nonexistent-endpoint')
    
    if (response.status !== 404) {
      throw new Error(`期望404错误，但得到: ${response.status}`)
    }
    
    console.log('  📝 404错误处理正常')
  })
  
  await testAPI('无效商品ID处理', async () => {
    const response = await makeRequest('/api/products/invalid-id')
    
    if (response.ok) {
      throw new Error('期望错误响应，但请求成功了')
    }
    
    console.log('  📝 无效ID错误处理正常')
  })
}

// 主测试函数
async function runAPITests() {
  console.log('🚀 开始API功能测试（无需认证版本）...\n')
  
  try {
    console.log('📋 1. 公开API测试')
    await testPublicAPIs()
    
    console.log('\n📋 2. 认证相关API测试')
    await testAuthAPIs()
    
    console.log('\n📋 3. 系统状态API测试')
    await testSystemAPIs()
    
    console.log('\n📋 4. 数据库连接测试')
    await testDatabaseAPIs()
    
    console.log('\n📋 5. 错误处理测试')
    await testErrorHandling()
    
  } catch (error) {
    console.error('❌ API测试过程中发生错误:', error)
  }
  
  console.log('\n📊 API测试结果统计:')
  console.log(`✅ 通过: ${testResults.passed}`)
  console.log(`❌ 失败: ${testResults.failed}`)
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试详情:')
    testResults.errors.forEach(error => {
      console.log(`  - ${error.test}: ${error.error}`)
    })
  }
  
  console.log('\n🎉 API测试完成!')
  
  // 返回测试结果
  return {
    passed: testResults.passed,
    failed: testResults.failed,
    total: testResults.passed + testResults.failed,
    success: testResults.failed === 0
  }
}

// 运行测试
runAPITests().then(result => {
  process.exit(result.success ? 0 : 1)
}).catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})
