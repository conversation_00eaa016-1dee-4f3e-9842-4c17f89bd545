/**
 * 地图集成功能测试脚本
 * 验证地图组件、API集成、性能等
 */

const fs = require('fs')
const path = require('path')

class MapIntegrationTester {
  constructor() {
    this.testResults = {
      components: [],
      apis: [],
      configs: [],
      performance: [],
      errors: []
    }
  }

  // 检查地图组件文件
  checkMapComponents() {
    console.log('🗺️ 检查地图组件...')
    
    const components = [
      {
        name: 'MapView (模拟地图)',
        path: 'components/location/MapView.tsx',
        required: true
      },
      {
        name: 'AMapView (高德地图)',
        path: 'components/location/AMapView.tsx',
        required: true
      },
      {
        name: 'NearbySearch (附近搜索)',
        path: 'components/location/NearbySearch.tsx',
        required: true
      },
      {
        name: 'LocationPicker (位置选择)',
        path: 'components/location/LocationPicker.tsx',
        required: true
      }
    ]

    components.forEach(component => {
      const fullPath = path.join(process.cwd(), component.path)
      const exists = fs.existsSync(fullPath)
      
      if (exists) {
        const content = fs.readFileSync(fullPath, 'utf8')
        const analysis = this.analyzeComponentContent(content, component.name)
        
        this.testResults.components.push({
          name: component.name,
          path: component.path,
          exists: true,
          analysis
        })
        
        console.log(`  ✅ ${component.name}`)
        console.log(`     📁 ${component.path}`)
        console.log(`     📊 ${analysis.lines} 行代码`)
        console.log(`     🔧 ${analysis.features.length} 个功能特性`)
        
      } else {
        this.testResults.components.push({
          name: component.name,
          path: component.path,
          exists: false,
          error: '文件不存在'
        })
        
        console.log(`  ❌ ${component.name} - 文件不存在`)
        if (component.required) {
          this.testResults.errors.push(`缺少必需组件: ${component.name}`)
        }
      }
    })
  }

  // 分析组件内容
  analyzeComponentContent(content, componentName) {
    const lines = content.split('\n').length
    const features = []

    // 检查功能特性
    if (content.includes('useState')) features.push('状态管理')
    if (content.includes('useEffect')) features.push('生命周期')
    if (content.includes('useRef')) features.push('DOM引用')
    if (content.includes('window.AMap')) features.push('高德地图API')
    if (content.includes('navigator.geolocation')) features.push('地理定位')
    if (content.includes('calculateDistance')) features.push('距离计算')
    if (content.includes('Marker')) features.push('地图标记')
    if (content.includes('Circle')) features.push('圆形覆盖物')
    if (content.includes('InfoWindow')) features.push('信息窗口')
    if (content.includes('onClick')) features.push('交互事件')
    if (content.includes('zoom')) features.push('缩放控制')
    if (content.includes('responsive')) features.push('响应式设计')

    return { lines, features }
  }

  // 检查API接口
  checkMapAPIs() {
    console.log('\n🔗 检查地图相关API...')
    
    const apis = [
      {
        name: '附近搜索API',
        path: 'app/api/location/nearby/route.ts',
        endpoints: ['GET', 'POST']
      },
      {
        name: '位置管理API',
        path: 'app/api/location/manage/route.ts',
        endpoints: ['GET', 'PUT', 'POST']
      }
    ]

    apis.forEach(api => {
      const fullPath = path.join(process.cwd(), api.path)
      const exists = fs.existsSync(fullPath)
      
      if (exists) {
        const content = fs.readFileSync(fullPath, 'utf8')
        const analysis = this.analyzeAPIContent(content, api.endpoints)
        
        this.testResults.apis.push({
          name: api.name,
          path: api.path,
          exists: true,
          analysis
        })
        
        console.log(`  ✅ ${api.name}`)
        console.log(`     📁 ${api.path}`)
        console.log(`     🔧 支持方法: ${analysis.methods.join(', ')}`)
        console.log(`     🛡️ 权限验证: ${analysis.hasAuth ? '是' : '否'}`)
        
      } else {
        this.testResults.apis.push({
          name: api.name,
          path: api.path,
          exists: false,
          error: '文件不存在'
        })
        
        console.log(`  ❌ ${api.name} - 文件不存在`)
        this.testResults.errors.push(`缺少API接口: ${api.name}`)
      }
    })
  }

  // 分析API内容
  analyzeAPIContent(content, expectedMethods) {
    const methods = []
    const features = []

    // 检查HTTP方法
    if (content.includes('export async function GET')) methods.push('GET')
    if (content.includes('export async function POST')) methods.push('POST')
    if (content.includes('export async function PUT')) methods.push('PUT')
    if (content.includes('export async function DELETE')) methods.push('DELETE')

    // 检查功能特性
    const hasAuth = content.includes('getServerSession') || content.includes('requireAdmin')
    const hasValidation = content.includes('searchParams') && content.includes('parseInt')
    const hasErrorHandling = content.includes('try') && content.includes('catch')
    const hasPagination = content.includes('limit') && content.includes('offset')

    if (hasAuth) features.push('身份验证')
    if (hasValidation) features.push('参数验证')
    if (hasErrorHandling) features.push('错误处理')
    if (hasPagination) features.push('分页支持')

    return { methods, features, hasAuth, hasValidation, hasErrorHandling, hasPagination }
  }

  // 检查配置文件
  checkMapConfigs() {
    console.log('\n⚙️ 检查地图配置...')
    
    const configs = [
      {
        name: '环境变量示例',
        path: '.env.example',
        required: false
      },
      {
        name: '地图集成文档',
        path: 'docs/map-integration-guide.md',
        required: true
      },
      {
        name: 'TypeScript配置',
        path: 'tsconfig.json',
        required: true
      }
    ]

    configs.forEach(config => {
      const fullPath = path.join(process.cwd(), config.path)
      const exists = fs.existsSync(fullPath)
      
      if (exists) {
        let analysis = {}
        
        if (config.path.includes('.env')) {
          const content = fs.readFileSync(fullPath, 'utf8')
          analysis = this.analyzeEnvConfig(content)
        } else if (config.path.includes('.md')) {
          const content = fs.readFileSync(fullPath, 'utf8')
          analysis = this.analyzeDocumentation(content)
        }
        
        this.testResults.configs.push({
          name: config.name,
          path: config.path,
          exists: true,
          analysis
        })
        
        console.log(`  ✅ ${config.name}`)
        
      } else {
        this.testResults.configs.push({
          name: config.name,
          path: config.path,
          exists: false,
          error: '文件不存在'
        })
        
        console.log(`  ❌ ${config.name} - 文件不存在`)
        if (config.required) {
          this.testResults.errors.push(`缺少配置文件: ${config.name}`)
        }
      }
    })
  }

  // 分析环境变量配置
  analyzeEnvConfig(content) {
    const mapKeys = []
    
    if (content.includes('AMAP_API_KEY')) mapKeys.push('高德地图')
    if (content.includes('BAIDU_MAP_API_KEY')) mapKeys.push('百度地图')
    if (content.includes('GOOGLE_MAPS_API_KEY')) mapKeys.push('谷歌地图')
    
    return { mapKeys }
  }

  // 分析文档内容
  analyzeDocumentation(content) {
    const sections = []
    const lines = content.split('\n')
    
    lines.forEach(line => {
      if (line.startsWith('## ')) {
        sections.push(line.replace('## ', ''))
      }
    })
    
    return { sections, wordCount: content.length }
  }

  // 性能测试
  testMapPerformance() {
    console.log('\n⚡ 地图性能测试...')
    
    const performanceTests = [
      {
        name: '组件加载时间',
        test: () => this.measureComponentLoadTime()
      },
      {
        name: '距离计算性能',
        test: () => this.measureDistanceCalculation()
      },
      {
        name: '标记渲染性能',
        test: () => this.measureMarkerRendering()
      }
    ]

    performanceTests.forEach(test => {
      try {
        const startTime = Date.now()
        const result = test.test()
        const duration = Date.now() - startTime
        
        this.testResults.performance.push({
          name: test.name,
          duration,
          result,
          status: 'success'
        })
        
        console.log(`  ✅ ${test.name}: ${duration}ms`)
        
      } catch (error) {
        this.testResults.performance.push({
          name: test.name,
          error: error.message,
          status: 'failed'
        })
        
        console.log(`  ❌ ${test.name}: ${error.message}`)
      }
    })
  }

  // 测量组件加载时间
  measureComponentLoadTime() {
    // 模拟组件加载
    const componentSize = 50000 // 假设组件大小
    const loadTime = Math.random() * 100 + 50 // 50-150ms
    
    return {
      componentSize,
      loadTime,
      status: loadTime < 100 ? 'good' : 'needs_optimization'
    }
  }

  // 测量距离计算性能
  measureDistanceCalculation() {
    const iterations = 1000
    const startTime = Date.now()
    
    // 模拟距离计算
    for (let i = 0; i < iterations; i++) {
      const lat1 = 39.9042 + Math.random() * 0.1
      const lng1 = 116.4074 + Math.random() * 0.1
      const lat2 = 39.9042 + Math.random() * 0.1
      const lng2 = 116.4074 + Math.random() * 0.1
      
      // Haversine公式计算
      const R = 6371
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLng = (lng2 - lng1) * Math.PI / 180
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
      const distance = R * c
    }
    
    const duration = Date.now() - startTime
    const avgTime = duration / iterations
    
    return {
      iterations,
      totalTime: duration,
      avgTime,
      performance: avgTime < 0.1 ? 'excellent' : avgTime < 0.5 ? 'good' : 'needs_optimization'
    }
  }

  // 测量标记渲染性能
  measureMarkerRendering() {
    const markerCount = 100
    const renderTime = Math.random() * 200 + 100 // 100-300ms
    
    return {
      markerCount,
      renderTime,
      avgPerMarker: renderTime / markerCount,
      performance: renderTime < 200 ? 'good' : 'needs_optimization'
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 生成地图集成测试报告...')
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalComponents: this.testResults.components.length,
        workingComponents: this.testResults.components.filter(c => c.exists).length,
        totalAPIs: this.testResults.apis.length,
        workingAPIs: this.testResults.apis.filter(a => a.exists).length,
        totalErrors: this.testResults.errors.length,
        performanceTests: this.testResults.performance.length
      },
      details: this.testResults,
      recommendations: this.generateRecommendations()
    }

    return report
  }

  // 生成优化建议
  generateRecommendations() {
    const recommendations = []

    // 检查缺失组件
    const missingComponents = this.testResults.components.filter(c => !c.exists)
    if (missingComponents.length > 0) {
      recommendations.push({
        type: 'error',
        title: '缺失关键组件',
        description: `缺少 ${missingComponents.length} 个地图组件`,
        action: '请确保所有地图组件文件存在'
      })
    }

    // 检查API配置
    const missingAPIs = this.testResults.apis.filter(a => !a.exists)
    if (missingAPIs.length > 0) {
      recommendations.push({
        type: 'error',
        title: '缺失API接口',
        description: `缺少 ${missingAPIs.length} 个地图API接口`,
        action: '请实现所有必需的API端点'
      })
    }

    // 性能优化建议
    const slowPerformance = this.testResults.performance.filter(p => 
      p.result && (p.result.performance === 'needs_optimization' || p.duration > 100)
    )
    if (slowPerformance.length > 0) {
      recommendations.push({
        type: 'warning',
        title: '性能优化建议',
        description: `${slowPerformance.length} 个性能测试需要优化`,
        action: '考虑使用懒加载、缓存或标记聚合'
      })
    }

    // 功能完善建议
    if (this.testResults.errors.length === 0) {
      recommendations.push({
        type: 'success',
        title: '地图集成完成',
        description: '所有核心功能已实现',
        action: '可以考虑添加高级功能如路径规划、热力图等'
      })
    }

    return recommendations
  }

  // 保存测试结果
  async saveResults(report) {
    const resultsDir = 'test-results'
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true })
    }

    const filename = `${resultsDir}/map-integration-test-${Date.now()}.json`
    fs.writeFileSync(filename, JSON.stringify(report, null, 2))
    
    console.log(`\n💾 测试结果已保存: ${filename}`)
  }

  // 运行完整测试
  async runCompleteTest() {
    console.log('🚀 开始地图集成功能测试')
    console.log('='.repeat(60))
    
    this.checkMapComponents()
    this.checkMapAPIs()
    this.checkMapConfigs()
    this.testMapPerformance()
    
    const report = this.generateReport()
    await this.saveResults(report)
    
    // 输出总结
    console.log('\n📋 测试总结:')
    console.log('='.repeat(40))
    console.log(`✅ 组件完成度: ${report.summary.workingComponents}/${report.summary.totalComponents}`)
    console.log(`✅ API完成度: ${report.summary.workingAPIs}/${report.summary.totalAPIs}`)
    console.log(`⚠️  发现问题: ${report.summary.totalErrors} 个`)
    console.log(`⚡ 性能测试: ${report.summary.performanceTests} 项`)
    
    // 输出建议
    if (report.recommendations.length > 0) {
      console.log('\n💡 优化建议:')
      report.recommendations.forEach((rec, index) => {
        const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : '✅'
        console.log(`${index + 1}. ${icon} ${rec.title}`)
        console.log(`   ${rec.description}`)
        console.log(`   建议: ${rec.action}`)
      })
    }
    
    return report
  }
}

// 运行测试
if (require.main === module) {
  const tester = new MapIntegrationTester()
  tester.runCompleteTest()
    .then((report) => {
      console.log('\n🎉 地图集成测试完成!')
      if (report.summary.totalErrors === 0) {
        console.log('✨ 所有功能正常，地图集成成功!')
        process.exit(0)
      } else {
        console.log('⚠️  发现问题，请查看测试报告')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 测试失败:', error)
      process.exit(1)
    })
}

module.exports = { MapIntegrationTester }
