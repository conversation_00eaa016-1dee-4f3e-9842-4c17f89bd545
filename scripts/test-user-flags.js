const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testUserFlags() {
  try {
    console.log('🔍 测试用户标记功能...')
    
    // 查看所有用户的标记状态
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        riskFlags: true,
        riskLevel: true,
        flaggedAt: true,
        flaggedBy: true,
        flagNotes: true
      },
      orderBy: { createdAt: 'desc' }
    })
    
    console.log('\n📋 用户标记状态:')
    console.log('=' .repeat(60))
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. 用户: ${user.name || '未设置'} (${user.email})`)
      console.log(`   ID: ${user.id}`)
      console.log(`   风险等级: ${user.riskLevel}`)
      console.log(`   标记时间: ${user.flaggedAt ? user.flaggedAt.toLocaleString() : '无'}`)
      console.log(`   标记者: ${user.flaggedBy || '无'}`)
      console.log(`   标记备注: ${user.flagNotes || '无'}`)
      
      if (user.riskFlags && Array.isArray(user.riskFlags)) {
        console.log(`   风险标记 (${user.riskFlags.length}个):`)
        user.riskFlags.forEach((flag, flagIndex) => {
          console.log(`     ${flagIndex + 1}. 类型: ${flag.type}`)
          console.log(`        原因: ${flag.reason}`)
          console.log(`        时间: ${flag.flaggedAt}`)
          console.log(`        操作者: ${flag.flaggedBy}`)
        })
      } else {
        console.log(`   风险标记: 无`)
      }
      
      console.log('-'.repeat(40))
    })

    // 统计标记情况
    const riskStats = await prisma.user.groupBy({
      by: ['riskLevel'],
      _count: {
        id: true
      }
    })
    
    console.log('\n📊 风险等级统计:')
    console.log('=' .repeat(40))
    riskStats.forEach(stat => {
      const levelText = {
        'NORMAL': '正常',
        'LOW': '低风险',
        'MEDIUM': '中风险',
        'HIGH': '高风险',
        'CRITICAL': '极高风险'
      }[stat.riskLevel] || stat.riskLevel
      
      console.log(`${levelText}: ${stat._count.id} 个用户`)
    })

    // 查找有标记的用户
    const flaggedUsers = await prisma.user.findMany({
      where: {
        riskLevel: {
          not: 'NORMAL'
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        riskLevel: true,
        flaggedAt: true
      }
    })
    
    console.log('\n🚩 已标记的风险用户:')
    console.log('=' .repeat(40))
    if (flaggedUsers.length === 0) {
      console.log('暂无风险用户标记')
    } else {
      flaggedUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name || '未设置'} (${user.email})`)
        console.log(`   风险等级: ${user.riskLevel}`)
        console.log(`   标记时间: ${user.flaggedAt?.toLocaleString()}`)
      })
    }

  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testUserFlags()
  .then(() => {
    console.log('\n🎉 测试完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 测试失败:', error)
    process.exit(1)
  })
