#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

async function verifySessionPersistence() {
  console.log('🔍 验证会话持久性修复')
  console.log('='.repeat(60))

  let allChecksPass = true

  // 1. 检查 NextAuth 配置
  console.log('\n📋 1. 检查 NextAuth 配置...')
  
  const authConfigPath = path.join(process.cwd(), 'lib/auth.ts')
  if (fs.existsSync(authConfigPath)) {
    const authContent = fs.readFileSync(authConfigPath, 'utf8')
    
    const checks = [
      { name: 'JWT 策略', pattern: /strategy:\s*['"]jwt['"]/, required: true },
      { name: '会话过期时间', pattern: /maxAge:\s*30\s*\*\s*24\s*\*\s*60\s*\*\s*60/, required: true },
      { name: 'JWT 配置', pattern: /jwt:\s*{/, required: true },
      { name: '<PERSON><PERSON> 配置', pattern: /cookies:\s*{/, required: true },
      { name: '<PERSON><PERSON> maxAge', pattern: /maxAge:\s*30\s*\*\s*24\s*\*\s*60\s*\*\s*60/, required: true },
      { name: 'HttpOnly cookie', pattern: /httpOnly:\s*true/, required: true },
      { name: 'SameSite 设置', pattern: /sameSite:\s*['"]lax['"]/, required: true },
      { name: 'Secure 设置', pattern: /secure:\s*process\.env\.NODE_ENV\s*===\s*['"]production['"]/, required: true }
    ]
    
    checks.forEach(check => {
      const found = check.pattern.test(authContent)
      const status = found ? '✅' : '❌'
      console.log(`   ${status} ${check.name}`)
      if (!found && check.required) {
        allChecksPass = false
      }
    })
  } else {
    console.log('❌ NextAuth 配置文件不存在')
    allChecksPass = false
  }

  // 2. 检查 SessionProvider 配置
  console.log('\n🔄 2. 检查 SessionProvider 配置...')
  
  const sessionProviderPath = path.join(process.cwd(), 'components/providers/session-provider.tsx')
  if (fs.existsSync(sessionProviderPath)) {
    const providerContent = fs.readFileSync(sessionProviderPath, 'utf8')
    
    const providerChecks = [
      { name: 'refetchInterval', pattern: /refetchInterval=\{5\s*\*\s*60\}/, required: true },
      { name: 'refetchOnWindowFocus', pattern: /refetchOnWindowFocus=\{true\}/, required: true },
      { name: 'refetchWhenOffline', pattern: /refetchWhenOffline=\{false\}/, required: true },
      { name: 'basePath', pattern: /basePath=["']\/api\/auth["']/, required: true }
    ]
    
    providerChecks.forEach(check => {
      const found = check.pattern.test(providerContent)
      const status = found ? '✅' : '❌'
      console.log(`   ${status} ${check.name}`)
      if (!found && check.required) {
        allChecksPass = false
      }
    })
  } else {
    console.log('❌ SessionProvider 文件不存在')
    allChecksPass = false
  }

  // 3. 检查中间件
  console.log('\n🛡️ 3. 检查中间件...')
  
  const middlewarePath = path.join(process.cwd(), 'middleware.ts')
  if (fs.existsSync(middlewarePath)) {
    const middlewareContent = fs.readFileSync(middlewarePath, 'utf8')
    
    if (middlewareContent.includes('withAuth')) {
      console.log('   ✅ 中间件配置存在')
    } else {
      console.log('   ❌ 中间件配置缺失')
      allChecksPass = false
    }
  } else {
    console.log('   ⚠️  中间件文件不存在（可选）')
  }

  // 4. 检查环境变量
  console.log('\n🔐 4. 检查环境变量...')
  
  const envFiles = ['.env.local', '.env']
  let envFound = false
  
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile)
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8')
      envFound = true
      
      const envChecks = [
        { name: 'NEXTAUTH_URL', pattern: /NEXTAUTH_URL=/, required: true },
        { name: 'NEXTAUTH_SECRET', pattern: /NEXTAUTH_SECRET=/, required: true },
        { name: 'JWT_SECRET', pattern: /JWT_SECRET=/, required: true }
      ]
      
      envChecks.forEach(check => {
        const found = check.pattern.test(envContent)
        const status = found ? '✅' : '❌'
        console.log(`   ${status} ${check.name} (${envFile})`)
        if (!found && check.required) {
          allChecksPass = false
        }
      })
      break
    }
  }
  
  if (!envFound) {
    console.log('   ❌ 环境变量文件不存在')
    allChecksPass = false
  }

  // 5. 检查 layout.tsx
  console.log('\n📄 5. 检查 layout.tsx...')
  
  const layoutPath = path.join(process.cwd(), 'app/layout.tsx')
  if (fs.existsSync(layoutPath)) {
    const layoutContent = fs.readFileSync(layoutPath, 'utf8')
    
    if (layoutContent.includes('AuthSessionProvider')) {
      console.log('   ✅ AuthSessionProvider 已包含')
    } else {
      console.log('   ❌ AuthSessionProvider 缺失')
      allChecksPass = false
    }
  } else {
    console.log('   ❌ layout.tsx 不存在')
    allChecksPass = false
  }

  // 6. 检查测试页面
  console.log('\n🧪 6. 检查测试页面...')
  
  const testPagePath = path.join(process.cwd(), 'app/test-session/page.tsx')
  if (fs.existsSync(testPagePath)) {
    console.log('   ✅ 测试页面存在')
    console.log('   📍 访问地址: http://localhost:3000/test-session')
  } else {
    console.log('   ⚠️  测试页面不存在')
  }

  // 7. 检查 hooks
  console.log('\n🎣 7. 检查自定义 hooks...')
  
  const hooksPath = path.join(process.cwd(), 'hooks/useSessionPersistence.ts')
  if (fs.existsSync(hooksPath)) {
    console.log('   ✅ useSessionPersistence hook 存在')
  } else {
    console.log('   ⚠️  useSessionPersistence hook 不存在')
  }

  // 总结
  console.log('\n' + '='.repeat(60))
  if (allChecksPass) {
    console.log('🎉 所有检查通过！会话持久性配置正确')
    console.log('\n📋 下一步测试:')
    console.log('1. 重启开发服务器: npm run dev')
    console.log('2. 访问测试页面: http://localhost:3000/test-session')
    console.log('3. 登录用户账户')
    console.log('4. 刷新页面测试持久性')
    console.log('5. 关闭重新打开标签页测试')
  } else {
    console.log('❌ 部分检查未通过，请检查上述问题')
    console.log('\n🔧 建议操作:')
    console.log('1. 重新运行修复脚本: node scripts/fix-session-persistence.js')
    console.log('2. 检查环境变量配置')
    console.log('3. 确保所有必要文件存在')
  }

  console.log('\n🔍 调试提示:')
  console.log('- 检查浏览器开发者工具 → Application → Cookies')
  console.log('- 查看控制台中的 NextAuth 相关日志')
  console.log('- 确保 NEXTAUTH_SECRET 在生产环境中是唯一的')
  console.log('- 检查服务器时间是否正确（影响 JWT 过期时间）')
}

verifySessionPersistence().catch(console.error)
