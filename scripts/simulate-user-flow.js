const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function simulateUserFlow() {
  try {
    console.log('🎭 开始模拟用户使用流程...')
    console.log('=' .repeat(60))

    // 1. 系统初始化检查
    console.log('\n📊 1. 系统状态检查')
    await checkSystemStatus()

    // 2. 模拟新用户注册和设置
    console.log('\n👤 2. 模拟新用户注册')
    const newUser = await simulateUserRegistration()

    // 3. 模拟用户浏览商品
    console.log('\n🛍️ 3. 模拟用户浏览商品')
    await simulateProductBrowsing(newUser)

    // 4. 模拟用户充值
    console.log('\n💰 4. 模拟用户充值')
    await simulateUserDeposit(newUser)

    // 5. 模拟礼品卡兑换
    console.log('\n🎁 5. 模拟礼品卡兑换')
    await simulateGiftCardRedemption(newUser)

    // 6. 模拟兑换码使用
    console.log('\n🎫 6. 模拟兑换码使用')
    await simulateRedemptionCodeUsage(newUser)

    // 7. 模拟商品购买流程
    console.log('\n🛒 7. 模拟商品购买流程')
    await simulatePurchaseFlow(newUser)

    // 8. 模拟用户间转账
    console.log('\n💸 8. 模拟用户间转账')
    await simulateUserTransfer(newUser)

    // 9. 模拟管理员操作
    console.log('\n👨‍💼 9. 模拟管理员操作')
    await simulateAdminOperations()

    // 10. 生成最终报告
    console.log('\n📈 10. 生成最终报告')
    await generateFinalReport()

    console.log('\n🎉 用户流程模拟完成！')

  } catch (error) {
    console.error('❌ 模拟流程失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 1. 系统状态检查
async function checkSystemStatus() {
  try {
    const stats = {
      users: await prisma.user.count(),
      products: await prisma.product.count(),
      orders: await prisma.order.count(),
      giftCards: await prisma.giftCard.count(),
      redemptionCodes: await prisma.redemptionCode.count(),
      notifications: await prisma.notification.count().catch(() => 0)
    }

    console.log('✅ 系统数据统计:')
    Object.entries(stats).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`)
    })

    return stats
  } catch (error) {
    console.error('系统状态检查失败:', error)
    return {}
  }
}

// 2. 模拟用户注册
async function simulateUserRegistration() {
  try {
    const timestamp = Date.now()
    const userData = {
      email: `testuser${timestamp}@example.com`,
      name: `测试用户${timestamp}`,
      password: 'password123',
      role: 'USER',
      emailVerified: new Date(),
      depositBalance: 0,
      creditPoints: 100
    }

    const user = await prisma.user.create({
      data: userData
    })

    console.log('✅ 新用户注册成功:')
    console.log(`  - ID: ${user.id}`)
    console.log(`  - 邮箱: ${user.email}`)
    console.log(`  - 姓名: ${user.name}`)
    console.log(`  - 初始余额: ${user.depositBalance} USDT`)
    console.log(`  - 信用积分: ${user.creditPoints}`)

    return user
  } catch (error) {
    console.error('用户注册失败:', error)
    return null
  }
}

// 3. 模拟商品浏览
async function simulateProductBrowsing(user) {
  try {
    if (!user) return

    // 获取可用商品
    const products = await prisma.product.findMany({
      where: { 
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED'
      },
      take: 5,
      include: {
        seller: { select: { name: true, email: true } }
      }
    })

    console.log(`✅ 找到 ${products.length} 个可用商品:`)
    products.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.title}`)
      console.log(`     价格: ${product.price} USDT`)
      console.log(`     卖家: ${product.seller.name || product.seller.email}`)
      console.log(`     状态: ${product.status}`)
    })

    // 模拟添加收藏
    if (products.length > 0) {
      const favoriteProduct = products[0]
      
      await prisma.favorite.create({
        data: {
          userId: user.id,
          productId: favoriteProduct.id
        }
      })

      console.log(`💖 已添加商品到收藏: ${favoriteProduct.title}`)
    }

    return products
  } catch (error) {
    console.error('商品浏览失败:', error)
    return []
  }
}

// 4. 模拟用户充值
async function simulateUserDeposit(user) {
  try {
    if (!user) return

    const depositAmount = 100
    
    // 创建充值记录
    const depositRecord = await prisma.depositRecord.create({
      data: {
        userId: user.id,
        amount: depositAmount,
        originalAmount: depositAmount,
        method: 'BANK_TRANSFER',
        status: 'PENDING',
        txHash: `mock_tx_${Date.now()}`,
        notes: '模拟充值测试',
        metadata: {
          screenshot: 'mock_screenshot.jpg',
          testData: true
        }
      }
    })

    console.log('✅ 充值申请已提交:')
    console.log(`  - 金额: ${depositAmount} USDT`)
    console.log(`  - 方式: 银行转账`)
    console.log(`  - 状态: 待审核`)
    console.log(`  - 记录ID: ${depositRecord.id}`)

    // 模拟管理员批准充值
    await prisma.$transaction(async (tx) => {
      // 更新充值记录状态
      await tx.depositRecord.update({
        where: { id: depositRecord.id },
        data: { 
          status: 'APPROVED',
          approvedAt: new Date()
        }
      })

      // 增加用户余额
      await tx.user.update({
        where: { id: user.id },
        data: {
          depositBalance: {
            increment: depositAmount
          }
        }
      })

      // 创建资金交易记录
      await tx.fundTransaction.create({
        data: {
          userId: user.id,
          type: 'DEPOSIT',
          amount: depositAmount,
          description: `充值 ${depositAmount} USDT`,
          relatedId: depositRecord.id,
          metadata: {
            relatedType: 'DEPOSIT_RECORD',
            method: 'BANK_TRANSFER'
          }
        }
      })
    })

    console.log('✅ 充值已批准，余额已更新')

    return depositRecord
  } catch (error) {
    console.error('用户充值失败:', error)
    return null
  }
}

// 5. 模拟礼品卡兑换
async function simulateGiftCardRedemption(user) {
  try {
    if (!user) return

    // 查找可兑换的礼品卡
    const availableGiftCard = await prisma.giftCard.findFirst({
      where: {
        status: 'GENERATED',
        validUntil: { gt: new Date() }
      },
      include: {
        product: true
      }
    })

    if (!availableGiftCard) {
      console.log('⚠️  没有可兑换的礼品卡')
      return null
    }

    console.log(`🎁 找到可兑换礼品卡: ${availableGiftCard.cardCode}`)
    console.log(`  - 面值: ${availableGiftCard.faceValue} USDT`)

    // 执行兑换
    const result = await prisma.$transaction(async (tx) => {
      // 更新礼品卡状态
      const updatedGiftCard = await tx.giftCard.update({
        where: { id: availableGiftCard.id },
        data: {
          status: 'REDEEMED',
          redeemedAt: new Date(),
          redeemedById: user.id,
          redeemedValue: availableGiftCard.faceValue
        }
      })

      // 增加用户余额
      await tx.user.update({
        where: { id: user.id },
        data: {
          depositBalance: {
            increment: availableGiftCard.faceValue
          }
        }
      })

      // 创建交易记录
      await tx.giftCardTransaction.create({
        data: {
          giftCardId: availableGiftCard.id,
          transactionType: 'REDEMPTION',
          amount: availableGiftCard.faceValue,
          userId: user.id,
          description: `用户兑换礼品卡，获得 ${availableGiftCard.faceValue} USDT`,
          metadata: {
            cardCode: availableGiftCard.cardCode,
            originalFaceValue: availableGiftCard.faceValue
          }
        }
      })

      return updatedGiftCard
    })

    console.log('✅ 礼品卡兑换成功!')
    console.log(`  - 获得金额: ${availableGiftCard.faceValue} USDT`)

    return result
  } catch (error) {
    console.error('礼品卡兑换失败:', error)
    return null
  }
}

// 6. 模拟兑换码使用
async function simulateRedemptionCodeUsage(user) {
  try {
    if (!user) return

    // 查找可用的兑换码
    const availableCode = await prisma.redemptionCode.findFirst({
      where: {
        status: 'ACTIVE',
        usedCount: { lt: prisma.redemptionCode.fields.maxUses },
        validUntil: { gt: new Date() }
      }
    })

    if (!availableCode) {
      console.log('⚠️  没有可用的兑换码')
      return null
    }

    console.log(`🎫 找到可用兑换码: ${availableCode.codeValue}`)
    console.log(`  - 奖励: ${availableCode.rewardValue} ${availableCode.rewardUnit}`)

    // 执行兑换
    const result = await prisma.$transaction(async (tx) => {
      // 更新兑换码状态
      const updatedCode = await tx.redemptionCode.update({
        where: { id: availableCode.id },
        data: {
          usedCount: { increment: 1 },
          lastUsedAt: new Date()
        }
      })

      // 创建兑换交易记录
      const transaction = await tx.redemptionTransaction.create({
        data: {
          userId: user.id,
          redemptionCodeId: availableCode.id,
          transactionType: 'REDEMPTION',
          rewardValue: availableCode.rewardValue,
          rewardUnit: availableCode.rewardUnit,
          description: `兑换码使用: ${availableCode.codeValue}`
        }
      })

      // 如果是现金奖励，增加用户余额
      if (availableCode.rewardType === 'CASH_CREDIT') {
        await tx.user.update({
          where: { id: user.id },
          data: {
            depositBalance: { increment: availableCode.rewardValue }
          }
        })

        await tx.fundTransaction.create({
          data: {
            userId: user.id,
            type: 'REDEMPTION_REWARD',
            amount: availableCode.rewardValue,
            description: `兑换码奖励: ${availableCode.rewardValue} ${availableCode.rewardUnit}`,
            relatedId: availableCode.id,
            metadata: {
              relatedType: 'REDEMPTION_CODE',
              codeValue: availableCode.codeValue
            }
          }
        })
      }

      return { code: updatedCode, transaction }
    })

    console.log('✅ 兑换码使用成功!')
    console.log(`  - 获得奖励: ${availableCode.rewardValue} ${availableCode.rewardUnit}`)

    return result
  } catch (error) {
    console.error('兑换码使用失败:', error)
    return null
  }
}

// 7. 模拟商品购买流程
async function simulatePurchaseFlow(user) {
  try {
    if (!user) return

    // 获取用户当前余额
    const currentUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { depositBalance: true }
    })

    console.log(`💰 用户当前余额: ${currentUser.depositBalance} USDT`)

    // 查找价格合适的商品
    const affordableProduct = await prisma.product.findFirst({
      where: {
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        price: { lte: currentUser.depositBalance }
      },
      include: {
        seller: { select: { name: true, email: true } }
      }
    })

    if (!affordableProduct) {
      console.log('⚠️  没有找到价格合适的商品')
      return null
    }

    console.log(`🛒 选择购买商品: ${affordableProduct.title}`)
    console.log(`  - 价格: ${affordableProduct.price} USDT`)
    console.log(`  - 卖家: ${affordableProduct.seller.name || affordableProduct.seller.email}`)

    // 创建订单
    const order = await prisma.order.create({
      data: {
        orderNumber: `ORD${Date.now()}`,
        buyerId: user.id,
        sellerId: affordableProduct.sellerId,
        productId: affordableProduct.id,
        quantity: 1,
        totalAmount: affordableProduct.price,
        productPrice: affordableProduct.price,
        status: 'PENDING_PAYMENT',
        paymentMethod: 'BALANCE'
      }
    })

    console.log('✅ 订单创建成功:')
    console.log(`  - 订单号: ${order.orderNumber}`)
    console.log(`  - 状态: ${order.status}`)

    // 模拟支付
    await prisma.$transaction(async (tx) => {
      // 扣除买家余额
      await tx.user.update({
        where: { id: user.id },
        data: {
          depositBalance: { decrement: affordableProduct.price }
        }
      })

      // 更新订单状态
      await tx.order.update({
        where: { id: order.id },
        data: {
          status: 'PAID',
          paymentConfirmed: true,
          paidAt: new Date()
        }
      })

      // 创建支付记录
      await tx.fundTransaction.create({
        data: {
          userId: user.id,
          type: 'PURCHASE',
          amount: -affordableProduct.price,
          description: `购买商品: ${affordableProduct.title}`,
          relatedId: order.id,
          metadata: {
            relatedType: 'ORDER',
            productTitle: affordableProduct.title,
            orderNumber: order.orderNumber
          }
        }
      })
    })

    console.log('✅ 支付完成，订单状态已更新')

    return order
  } catch (error) {
    console.error('商品购买失败:', error)
    return null
  }
}

// 8. 模拟用户间转账
async function simulateUserTransfer(user) {
  try {
    if (!user) return

    // 查找另一个用户作为转账目标
    const targetUser = await prisma.user.findFirst({
      where: {
        id: { not: user.id },
        role: 'USER'
      },
      select: { id: true, name: true, email: true, depositBalance: true }
    })

    if (!targetUser) {
      console.log('⚠️  没有找到转账目标用户')
      return null
    }

    // 获取当前用户余额
    const currentUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { depositBalance: true }
    })

    const transferAmount = 10 // 转账10 USDT

    if (currentUser.depositBalance < transferAmount) {
      console.log('⚠️  余额不足，无法转账')
      return null
    }

    console.log(`💸 准备转账给用户: ${targetUser.name || targetUser.email}`)
    console.log(`  - 转账金额: ${transferAmount} USDT`)
    console.log(`  - 当前余额: ${currentUser.depositBalance} USDT`)

    // 执行转账
    const result = await prisma.$transaction(async (tx) => {
      // 扣除发送方余额
      const updatedSender = await tx.user.update({
        where: { id: user.id },
        data: { depositBalance: { decrement: transferAmount } }
      })

      // 增加接收方余额
      const updatedRecipient = await tx.user.update({
        where: { id: targetUser.id },
        data: { depositBalance: { increment: transferAmount } }
      })

      // 创建发送方交易记录
      await tx.fundTransaction.create({
        data: {
          userId: user.id,
          type: 'TRANSFER_OUT',
          amount: -transferAmount,
          description: `转账给 ${targetUser.name || targetUser.email}`,
          relatedId: targetUser.id,
          metadata: {
            relatedType: 'USER_TRANSFER',
            recipientId: targetUser.id,
            recipientName: targetUser.name,
            recipientEmail: targetUser.email
          }
        }
      })

      // 创建接收方交易记录
      await tx.fundTransaction.create({
        data: {
          userId: targetUser.id,
          type: 'TRANSFER_IN',
          amount: transferAmount,
          description: `收到来自 ${user.name || user.email} 的转账`,
          relatedId: user.id,
          metadata: {
            relatedType: 'USER_TRANSFER',
            senderId: user.id,
            senderName: user.name,
            senderEmail: user.email
          }
        }
      })

      return { updatedSender, updatedRecipient }
    })

    console.log('✅ 转账成功!')
    console.log(`  - 发送方新余额: ${result.updatedSender.depositBalance} USDT`)
    console.log(`  - 接收方新余额: ${result.updatedRecipient.depositBalance} USDT`)

    return result
  } catch (error) {
    console.error('用户转账失败:', error)
    return null
  }
}

// 9. 模拟管理员操作
async function simulateAdminOperations() {
  try {
    // 查找管理员
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.log('⚠️  没有找到管理员用户')
      return null
    }

    console.log(`👨‍💼 管理员: ${admin.name || admin.email}`)

    // 1. 查看待审核的充值记录
    const pendingDeposits = await prisma.depositRecord.findMany({
      where: { status: 'PENDING' },
      include: {
        user: { select: { name: true, email: true } }
      },
      take: 3
    })

    console.log(`📋 待审核充值记录: ${pendingDeposits.length} 条`)

    // 2. 创建系统公告
    const announcement = await prisma.announcement.create({
      data: {
        title: '系统维护通知',
        content: '系统将于今晚进行例行维护，预计维护时间2小时，期间可能影响部分功能使用。',
        summary: '今晚系统维护通知',
        category: 'SYSTEM',
        priority: 'HIGH',
        status: 'PUBLISHED',
        showOnHome: true,
        authorId: admin.id,
        publishAt: new Date()
      }
    })

    console.log('✅ 系统公告已发布:')
    console.log(`  - 标题: ${announcement.title}`)
    console.log(`  - 类别: ${announcement.category}`)
    console.log(`  - 优先级: ${announcement.priority}`)

    // 3. 查看系统统计
    const systemStats = await getSystemStatistics()
    console.log('📊 系统统计信息:')
    Object.entries(systemStats).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`)
    })

    return { admin, announcement, systemStats }
  } catch (error) {
    console.error('管理员操作失败:', error)
    return null
  }
}

// 10. 生成最终报告
async function generateFinalReport() {
  try {
    const report = {
      timestamp: new Date(),
      systemStats: await getSystemStatistics(),
      recentActivity: await getRecentActivity(),
      userStats: await getUserStatistics()
    }

    console.log('📈 最终报告:')
    console.log('=' .repeat(50))

    console.log('\n🏢 系统统计:')
    Object.entries(report.systemStats).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`)
    })

    console.log('\n👥 用户统计:')
    Object.entries(report.userStats).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`)
    })

    console.log('\n📊 最近活动:')
    Object.entries(report.recentActivity).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`)
    })

    return report
  } catch (error) {
    console.error('生成报告失败:', error)
    return null
  }
}

// 辅助函数：获取系统统计
async function getSystemStatistics() {
  try {
    return {
      totalUsers: await prisma.user.count(),
      totalProducts: await prisma.product.count(),
      totalOrders: await prisma.order.count(),
      totalGiftCards: await prisma.giftCard.count(),
      totalRedemptionCodes: await prisma.redemptionCode.count(),
      totalTransactions: await prisma.fundTransaction.count(),
      totalAnnouncements: await prisma.announcement.count()
    }
  } catch (error) {
    console.error('获取系统统计失败:', error)
    return {}
  }
}

// 辅助函数：获取最近活动
async function getRecentActivity() {
  try {
    const oneDayAgo = new Date()
    oneDayAgo.setDate(oneDayAgo.getDate() - 1)

    return {
      newUsersToday: await prisma.user.count({
        where: { createdAt: { gte: oneDayAgo } }
      }),
      newOrdersToday: await prisma.order.count({
        where: { createdAt: { gte: oneDayAgo } }
      }),
      transactionsToday: await prisma.fundTransaction.count({
        where: { createdAt: { gte: oneDayAgo } }
      }),
      giftCardsRedeemedToday: await prisma.giftCard.count({
        where: { redeemedAt: { gte: oneDayAgo } }
      })
    }
  } catch (error) {
    console.error('获取最近活动失败:', error)
    return {}
  }
}

// 辅助函数：获取用户统计
async function getUserStatistics() {
  try {
    return {
      adminUsers: await prisma.user.count({ where: { role: 'ADMIN' } }),
      regularUsers: await prisma.user.count({ where: { role: 'USER' } }),
      verifiedUsers: await prisma.user.count({ where: { emailVerified: { not: null } } }),
      activeUsers: await prisma.user.count({
        where: {
          OR: [
            { ordersAsBuyer: { some: {} } },
            { ordersAsSeller: { some: {} } }
          ]
        }
      })
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {}
  }
}

// 运行模拟
simulateUserFlow()
