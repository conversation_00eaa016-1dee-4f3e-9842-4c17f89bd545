const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testNewAPIs() {
  try {
    console.log('🧪 开始测试新实现的API功能...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('❌ 未找到管理员用户')
      return
    }

    console.log('👤 管理员信息:')
    console.log('  - ID:', admin.id)
    console.log('  - 邮箱:', admin.email)

    // 1. 测试系统设置功能
    console.log('\n⚙️ 测试系统设置功能...')
    
    // 检查是否有SystemSetting模型（如果数据库已更新）
    try {
      const settingsCount = await prisma.systemSetting.count()
      console.log(`✅ 系统设置表存在，当前有 ${settingsCount} 条设置`)
      
      // 如果没有设置，创建一些测试设置
      if (settingsCount === 0) {
        console.log('📝 创建测试系统设置...')
        
        const testSettings = [
          {
            category: 'platform',
            key: 'name',
            value: '比特市场',
            description: '平台名称',
            dataType: 'string',
            isPublic: true,
            isEditable: true,
            createdBy: admin.id,
            updatedBy: admin.id
          },
          {
            category: 'fees',
            key: 'platformFeeRate',
            value: 0.02,
            description: '平台手续费率',
            dataType: 'number',
            isPublic: true,
            isEditable: true,
            createdBy: admin.id,
            updatedBy: admin.id
          },
          {
            category: 'security',
            key: 'maxLoginAttempts',
            value: 5,
            description: '最大登录尝试次数',
            dataType: 'number',
            isPublic: false,
            isEditable: true,
            createdBy: admin.id,
            updatedBy: admin.id
          }
        ]

        await prisma.systemSetting.createMany({
          data: testSettings
        })

        console.log('✅ 测试系统设置已创建')
      }

      // 测试获取设置
      const allSettings = await prisma.systemSetting.findMany({
        orderBy: [
          { category: 'asc' },
          { key: 'asc' }
        ]
      })

      console.log('📋 当前系统设置:')
      allSettings.forEach(setting => {
        console.log(`  - ${setting.category}.${setting.key}: ${JSON.stringify(setting.value)}`)
      })

    } catch (error) {
      console.log('⚠️  SystemSetting模型不存在，跳过系统设置测试')
    }

    // 2. 测试搜索关键词功能
    console.log('\n🔍 测试搜索关键词功能...')
    
    try {
      const keywordsCount = await prisma.searchKeyword.count()
      console.log(`✅ 搜索关键词表存在，当前有 ${keywordsCount} 个关键词`)
      
      // 创建一些测试关键词
      const testKeywords = [
        { keyword: '手机', searchCount: 15, resultCount: 25, isHot: true, category: '电子产品' },
        { keyword: '电脑', searchCount: 12, resultCount: 18, isHot: true, category: '电子产品' },
        { keyword: '耳机', searchCount: 8, resultCount: 12, isHot: false, category: '电子产品' },
        { keyword: '键盘', searchCount: 6, resultCount: 9, isHot: false, category: '电子产品' },
        { keyword: '游戏', searchCount: 10, resultCount: 15, isHot: true, category: '游戏娱乐' }
      ]

      for (const keyword of testKeywords) {
        await prisma.searchKeyword.upsert({
          where: { keyword: keyword.keyword },
          update: keyword,
          create: keyword
        })
      }

      console.log('✅ 测试搜索关键词已创建/更新')

      // 获取热门关键词
      const hotKeywords = await prisma.searchKeyword.findMany({
        where: { isHot: true },
        orderBy: { searchCount: 'desc' },
        take: 5
      })

      console.log('🔥 热门关键词:')
      hotKeywords.forEach(keyword => {
        console.log(`  - ${keyword.keyword}: ${keyword.searchCount} 次搜索`)
      })

    } catch (error) {
      console.log('⚠️  SearchKeyword模型不存在，跳过搜索关键词测试')
    }

    // 3. 测试通知功能
    console.log('\n📢 测试通知功能...')
    
    try {
      const notificationsCount = await prisma.notification.count()
      console.log(`✅ 通知表存在，当前有 ${notificationsCount} 条通知`)
      
      // 创建一些测试通知
      const testNotifications = [
        {
          userId: admin.id,
          type: 'SYSTEM',
          title: '系统维护通知',
          message: '系统将于今晚进行维护，预计维护时间2小时',
          priority: 'HIGH',
          data: { maintenanceTime: '22:00-24:00' }
        },
        {
          userId: admin.id,
          type: 'ORDER',
          title: '新订单通知',
          message: '您有一个新的订单需要处理',
          priority: 'NORMAL',
          relatedId: 'test-order-id',
          relatedType: 'ORDER'
        },
        {
          userId: admin.id,
          type: 'PAYMENT',
          title: '支付成功',
          message: '您的支付已成功处理',
          priority: 'HIGH',
          data: { amount: 100, currency: 'USDT' }
        }
      ]

      for (const notification of testNotifications) {
        await prisma.notification.create({
          data: notification
        })
      }

      console.log('✅ 测试通知已创建')

      // 获取用户通知
      const userNotifications = await prisma.notification.findMany({
        where: { userId: admin.id },
        orderBy: { createdAt: 'desc' },
        take: 5
      })

      console.log('📬 用户通知:')
      userNotifications.forEach(notification => {
        console.log(`  - ${notification.type}: ${notification.title}`)
        console.log(`    ${notification.message}`)
        console.log(`    优先级: ${notification.priority}, 已读: ${notification.isRead}`)
      })

      // 获取未读通知数量
      const unreadCount = await prisma.notification.count({
        where: {
          userId: admin.id,
          isRead: false
        }
      })

      console.log(`📊 未读通知数量: ${unreadCount}`)

    } catch (error) {
      console.log('⚠️  Notification模型不存在，跳过通知测试')
    }

    // 4. 测试数据统计
    console.log('\n📊 系统数据统计:')
    
    const stats = {
      users: await prisma.user.count(),
      products: await prisma.product.count(),
      orders: await prisma.order.count(),
      giftCards: await prisma.giftCard.count(),
      redemptionCodes: await prisma.redemptionCode.count()
    }

    console.log('✅ 当前系统数据:')
    console.log(`  - 用户数: ${stats.users}`)
    console.log(`  - 商品数: ${stats.products}`)
    console.log(`  - 订单数: ${stats.orders}`)
    console.log(`  - 礼品卡数: ${stats.giftCards}`)
    console.log(`  - 兑换码数: ${stats.redemptionCodes}`)

    console.log('\n🎉 新API功能测试完成！')
    console.log('\n💡 测试建议:')
    console.log('1. 如果数据库模型不存在，请运行: npx prisma db push')
    console.log('2. 访问 http://localhost:3000/api/admin/settings 测试系统设置API')
    console.log('3. 访问 http://localhost:3000/api/products/search 测试搜索API')
    console.log('4. 访问 http://localhost:3000/api/notifications 测试通知API')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testNewAPIs()
