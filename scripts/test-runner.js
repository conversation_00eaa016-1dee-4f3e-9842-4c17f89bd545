#!/usr/bin/env node

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 测试配置
const TEST_CONFIG = {
  unit: {
    pattern: 'test/**/*.test.ts',
    timeout: 30000,
    coverage: true
  },
  integration: {
    pattern: 'test/api/**/*.test.ts',
    timeout: 60000,
    coverage: true
  },
  e2e: {
    pattern: 'test/e2e/**/*.test.ts',
    timeout: 120000,
    coverage: false
  },
  performance: {
    pattern: 'test/performance/**/*.test.ts',
    timeout: 300000,
    coverage: false
  }
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan')
  log(`  ${message}`, 'bright')
  log(`${'='.repeat(60)}`, 'cyan')
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

// 运行命令
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    })

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code)
      } else {
        reject(new Error(`Command failed with exit code ${code}`))
      }
    })

    child.on('error', (error) => {
      reject(error)
    })
  })
}

// 检查依赖
async function checkDependencies() {
  logHeader('检查测试依赖')
  
  const requiredPackages = [
    'vitest',
    '@testing-library/react',
    '@testing-library/jest-dom',
    'jsdom'
  ]

  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies }

  for (const pkg of requiredPackages) {
    if (allDeps[pkg]) {
      logSuccess(`${pkg} 已安装`)
    } else {
      logError(`${pkg} 未安装`)
      throw new Error(`缺少依赖: ${pkg}`)
    }
  }
}

// 设置测试环境
async function setupTestEnvironment() {
  logHeader('设置测试环境')
  
  // 创建测试结果目录
  const testResultsDir = 'test-results'
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true })
    logSuccess('创建测试结果目录')
  }

  // 创建覆盖率目录
  const coverageDir = 'coverage'
  if (!fs.existsSync(coverageDir)) {
    fs.mkdirSync(coverageDir, { recursive: true })
    logSuccess('创建覆盖率目录')
  }

  // 设置环境变量
  process.env.NODE_ENV = 'test'
  process.env.DATABASE_URL = 'file:./test.db'
  logSuccess('设置环境变量')
}

// 运行特定类型的测试
async function runTestSuite(type, config) {
  logHeader(`运行 ${type.toUpperCase()} 测试`)
  
  const args = [
    'run',
    config.pattern,
    '--reporter=verbose',
    `--testTimeout=${config.timeout}`
  ]

  if (config.coverage) {
    args.push('--coverage')
  }

  try {
    await runCommand('npx', ['vitest', ...args])
    logSuccess(`${type} 测试完成`)
    return true
  } catch (error) {
    logError(`${type} 测试失败: ${error.message}`)
    return false
  }
}

// 生成测试报告
async function generateTestReport() {
  logHeader('生成测试报告')
  
  const reportData = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    },
    testResults: {}
  }

  // 读取测试结果
  const resultsFile = 'test-results/results.json'
  if (fs.existsSync(resultsFile)) {
    try {
      const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'))
      reportData.testResults = results
      logSuccess('读取测试结果')
    } catch (error) {
      logWarning('无法读取测试结果文件')
    }
  }

  // 生成HTML报告
  const htmlReport = generateHtmlReport(reportData)
  fs.writeFileSync('test-results/report.html', htmlReport)
  logSuccess('生成HTML测试报告')

  // 生成JSON报告
  fs.writeFileSync('test-results/report.json', JSON.stringify(reportData, null, 2))
  logSuccess('生成JSON测试报告')
}

// 生成HTML报告模板
function generateHtmlReport(data) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="header">
        <h1>BitMarket 测试报告</h1>
        <p>生成时间: ${data.timestamp}</p>
        <p>Node.js 版本: ${data.environment.nodeVersion}</p>
        <p>平台: ${data.environment.platform} (${data.environment.arch})</p>
    </div>
    
    <div class="section">
        <h2>测试结果概览</h2>
        <p>详细的测试结果请查看 test-results/results.json 文件</p>
    </div>
    
    <div class="section">
        <h2>覆盖率报告</h2>
        <p>覆盖率详情请查看 coverage/index.html 文件</p>
    </div>
</body>
</html>
  `
}

// 清理测试环境
async function cleanup() {
  logHeader('清理测试环境')
  
  // 删除测试数据库
  const testDbPath = 'test.db'
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath)
    logSuccess('删除测试数据库')
  }

  logSuccess('清理完成')
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  const testType = args[0] || 'all'
  const watch = args.includes('--watch')
  const coverage = args.includes('--coverage')

  try {
    logHeader('BitMarket 测试套件')
    logInfo(`测试类型: ${testType}`)
    logInfo(`监视模式: ${watch ? '开启' : '关闭'}`)
    logInfo(`覆盖率: ${coverage ? '开启' : '关闭'}`)

    await checkDependencies()
    await setupTestEnvironment()

    let allTestsPassed = true

    if (testType === 'all') {
      // 运行所有测试
      for (const [type, config] of Object.entries(TEST_CONFIG)) {
        const passed = await runTestSuite(type, config)
        if (!passed) allTestsPassed = false
      }
    } else if (TEST_CONFIG[testType]) {
      // 运行特定类型的测试
      const passed = await runTestSuite(testType, TEST_CONFIG[testType])
      if (!passed) allTestsPassed = false
    } else {
      throw new Error(`未知的测试类型: ${testType}`)
    }

    await generateTestReport()
    await cleanup()

    if (allTestsPassed) {
      logSuccess('所有测试通过！')
      process.exit(0)
    } else {
      logError('部分测试失败')
      process.exit(1)
    }

  } catch (error) {
    logError(`测试运行失败: ${error.message}`)
    process.exit(1)
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  logError(`未处理的Promise拒绝: ${reason}`)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  logError(`未捕获的异常: ${error.message}`)
  process.exit(1)
})

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  runTestSuite,
  generateTestReport,
  checkDependencies,
  setupTestEnvironment,
  cleanup
}
