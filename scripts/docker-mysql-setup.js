/**
 * Docker MySQL 设置脚本
 * 帮助用户在Windows上使用Docker运行MySQL
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

function showDockerInstallGuide() {
  console.log('🐳 Docker MySQL 设置指南')
  console.log('='.repeat(50))
  
  console.log('\n📋 优势:')
  console.log('  ✅ 无需安装MySQL服务器')
  console.log('  ✅ 一键启动和停止')
  console.log('  ✅ 环境隔离，不影响系统')
  console.log('  ✅ 易于备份和迁移')
  console.log('  ✅ 支持多版本MySQL')
  
  console.log('\n🔧 安装 Docker Desktop:')
  console.log('1. 下载 Docker Desktop for Windows:')
  console.log('   https://www.docker.com/products/docker-desktop/')
  console.log('2. 运行安装程序')
  console.log('3. 重启计算机')
  console.log('4. 启动 Docker Desktop')
  console.log('5. 确保 Docker 正在运行 (系统托盘图标)')
  
  console.log('\n🐬 MySQL Docker 配置:')
  console.log('  容器名称: bitmarket-mysql')
  console.log('  MySQL版本: 8.0')
  console.log('  端口映射: 3306:3306')
  console.log('  root密码: password')
  console.log('  数据库名: bitmarket')
  console.log('  数据持久化: ./mysql-data')
  
  console.log('\n⚡ 快速命令:')
  console.log('  node docker-mysql-setup.js start    # 启动MySQL容器')
  console.log('  node docker-mysql-setup.js stop     # 停止MySQL容器')
  console.log('  node docker-mysql-setup.js status   # 查看容器状态')
  console.log('  node docker-mysql-setup.js logs     # 查看MySQL日志')
  console.log('  node docker-mysql-setup.js connect  # 连接到MySQL')
}

function createDockerCompose() {
  const dockerCompose = `version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: bitmarket-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: bitmarket
      MYSQL_USER: bitmarket_user
      MYSQL_PASSWORD: bitmarket_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

volumes:
  mysql_data:
    driver: local
`

  const composePath = path.join(process.cwd(), 'docker-compose.yml')
  fs.writeFileSync(composePath, dockerCompose)
  console.log(`✅ Docker Compose 文件已创建: ${composePath}`)
  
  // 创建初始化SQL脚本
  const initSQL = `-- BitMarket 数据库初始化脚本

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS bitmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE bitmarket;

-- 显示创建结果
SELECT 'BitMarket 数据库初始化完成' AS message;
SHOW DATABASES;
`

  const initDir = path.join(process.cwd(), 'mysql-init')
  if (!fs.existsSync(initDir)) {
    fs.mkdirSync(initDir, { recursive: true })
  }
  
  const initPath = path.join(initDir, '01-init.sql')
  fs.writeFileSync(initPath, initSQL)
  console.log(`✅ 初始化SQL脚本已创建: ${initPath}`)
}

async function checkDockerInstalled() {
  console.log('🔍 检查 Docker 安装状态...')
  
  return new Promise((resolve) => {
    const docker = spawn('docker', ['--version'], { shell: true })
    
    docker.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Docker 已安装')
        resolve(true)
      } else {
        console.log('❌ Docker 未安装或未启动')
        console.log('\n💡 请先安装 Docker Desktop:')
        console.log('   https://www.docker.com/products/docker-desktop/')
        resolve(false)
      }
    })
    
    docker.on('error', () => {
      console.log('❌ Docker 未安装')
      resolve(false)
    })
  })
}

async function startMySQLContainer() {
  console.log('🚀 启动 MySQL Docker 容器...')
  
  const dockerInstalled = await checkDockerInstalled()
  if (!dockerInstalled) {
    return false
  }
  
  return new Promise((resolve) => {
    const dockerUp = spawn('docker-compose', ['up', '-d'], {
      stdio: 'inherit',
      shell: true
    })
    
    dockerUp.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ MySQL 容器启动成功!')
        console.log('\n📊 连接信息:')
        console.log('  主机: localhost')
        console.log('  端口: 3306')
        console.log('  用户: root')
        console.log('  密码: password')
        console.log('  数据库: bitmarket')
        console.log('\n⏳ 等待MySQL完全启动 (约30秒)...')
        resolve(true)
      } else {
        console.log('❌ MySQL 容器启动失败')
        resolve(false)
      }
    })
  })
}

async function stopMySQLContainer() {
  console.log('🛑 停止 MySQL Docker 容器...')
  
  return new Promise((resolve) => {
    const dockerDown = spawn('docker-compose', ['down'], {
      stdio: 'inherit',
      shell: true
    })
    
    dockerDown.on('close', (code) => {
      if (code === 0) {
        console.log('✅ MySQL 容器已停止')
        resolve(true)
      } else {
        console.log('❌ 停止容器失败')
        resolve(false)
      }
    })
  })
}

async function showContainerStatus() {
  console.log('📊 Docker 容器状态:')
  
  return new Promise((resolve) => {
    const dockerPs = spawn('docker', ['ps', '-a', '--filter', 'name=bitmarket-mysql'], {
      stdio: 'inherit',
      shell: true
    })
    
    dockerPs.on('close', () => {
      resolve(true)
    })
  })
}

async function showMySQLLogs() {
  console.log('📋 MySQL 容器日志:')
  
  return new Promise((resolve) => {
    const dockerLogs = spawn('docker', ['logs', 'bitmarket-mysql', '--tail', '50'], {
      stdio: 'inherit',
      shell: true
    })
    
    dockerLogs.on('close', () => {
      resolve(true)
    })
  })
}

async function connectToMySQL() {
  console.log('🔗 连接到 MySQL 容器...')
  console.log('💡 使用 Ctrl+C 退出MySQL命令行')
  
  return new Promise((resolve) => {
    const mysqlConnect = spawn('docker', [
      'exec', '-it', 'bitmarket-mysql',
      'mysql', '-u', 'root', '-ppassword', 'bitmarket'
    ], {
      stdio: 'inherit',
      shell: true
    })
    
    mysqlConnect.on('close', () => {
      console.log('👋 已断开MySQL连接')
      resolve(true)
    })
  })
}

async function testConnection() {
  console.log('🔍 测试 MySQL 连接...')
  
  try {
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()
    
    console.log('正在连接 Docker MySQL...')
    
    await prisma.$connect()
    console.log('✅ Docker MySQL 连接成功!')
    
    const result = await prisma.$queryRaw`SELECT VERSION() as version, DATABASE() as database_name`
    console.log(`📊 MySQL 版本: ${result[0].version}`)
    console.log(`📊 当前数据库: ${result[0].database_name || 'bitmarket'}`)
    
    await prisma.$disconnect()
    
  } catch (error) {
    console.error('❌ MySQL 连接失败:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 解决方案:')
      console.log('1. 确保 Docker 容器正在运行:')
      console.log('   node docker-mysql-setup.js status')
      console.log('2. 启动 MySQL 容器:')
      console.log('   node docker-mysql-setup.js start')
      console.log('3. 等待容器完全启动 (约30秒)')
    }
  }
}

async function setupComplete() {
  console.log('\n🎉 Docker MySQL 完整设置...')
  
  try {
    // 1. 检查Docker
    const dockerOk = await checkDockerInstalled()
    if (!dockerOk) return false
    
    // 2. 创建配置文件
    createDockerCompose()
    
    // 3. 启动容器
    const startOk = await startMySQLContainer()
    if (!startOk) return false
    
    // 4. 等待MySQL启动
    console.log('\n⏳ 等待MySQL完全启动...')
    await new Promise(resolve => setTimeout(resolve, 30000))
    
    // 5. 测试连接
    await testConnection()
    
    // 6. 运行Prisma迁移
    console.log('\n🔄 运行数据库迁移...')
    const migrate = spawn('npx', ['prisma', 'migrate', 'dev', '--name', 'docker-mysql-init'], {
      stdio: 'inherit',
      shell: true
    })
    
    await new Promise((resolve) => {
      migrate.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 数据库迁移完成!')
        } else {
          console.log('⚠️  迁移可能有问题，请手动检查')
        }
        resolve()
      })
    })
    
    console.log('\n🎉 Docker MySQL 设置完成!')
    console.log('\n📋 管理命令:')
    console.log('  启动: node docker-mysql-setup.js start')
    console.log('  停止: node docker-mysql-setup.js stop')
    console.log('  状态: node docker-mysql-setup.js status')
    console.log('  日志: node docker-mysql-setup.js logs')
    console.log('  连接: node docker-mysql-setup.js connect')
    
    return true
    
  } catch (error) {
    console.error('❌ 设置失败:', error.message)
    return false
  }
}

async function main() {
  const command = process.argv[2]
  
  switch (command) {
    case 'guide':
      showDockerInstallGuide()
      break
      
    case 'create':
      createDockerCompose()
      break
      
    case 'start':
      await startMySQLContainer()
      break
      
    case 'stop':
      await stopMySQLContainer()
      break
      
    case 'status':
      await showContainerStatus()
      break
      
    case 'logs':
      await showMySQLLogs()
      break
      
    case 'connect':
      await connectToMySQL()
      break
      
    case 'test':
      await testConnection()
      break
      
    case 'setup':
      await setupComplete()
      break
      
    default:
      console.log('🐳 Docker MySQL 管理工具')
      console.log('='.repeat(30))
      console.log('使用方法:')
      console.log('  node docker-mysql-setup.js guide    # 显示安装指南')
      console.log('  node docker-mysql-setup.js create   # 创建配置文件')
      console.log('  node docker-mysql-setup.js start    # 启动MySQL容器')
      console.log('  node docker-mysql-setup.js stop     # 停止MySQL容器')
      console.log('  node docker-mysql-setup.js status   # 查看容器状态')
      console.log('  node docker-mysql-setup.js logs     # 查看MySQL日志')
      console.log('  node docker-mysql-setup.js connect  # 连接到MySQL')
      console.log('  node docker-mysql-setup.js test     # 测试连接')
      console.log('  node docker-mysql-setup.js setup    # 完整设置')
      console.log('')
      console.log('推荐步骤:')
      console.log('1. 安装 Docker Desktop')
      console.log('2. node docker-mysql-setup.js setup  # 一键设置')
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  checkDockerInstalled,
  startMySQLContainer,
  stopMySQLContainer,
  testConnection,
  setupComplete
}
