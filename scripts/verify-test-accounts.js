#!/usr/bin/env node

/**
 * BitMarket 测试账号验证脚本
 * 验证测试账号是否可以正常登录
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// 测试账号列表
const testCredentials = [
  {
    email: '<EMAIL>',
    password: 'admin123456',
    expectedRole: 'ADMIN',
    name: '系统管理员'
  },
  {
    email: '<EMAIL>',
    password: 'user123456',
    expectedRole: 'USER',
    name: '张三'
  },
  {
    email: '<EMAIL>',
    password: 'user123456',
    expectedRole: 'USER',
    name: '李四'
  }
];

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 验证密码
async function verifyPassword(plainPassword, hashedPassword) {
  return await bcrypt.compare(plainPassword, hashedPassword);
}

// 验证单个账号
async function verifyAccount(credentials) {
  try {
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: credentials.email },
      include: {
        creditHistories: {
          take: 1,
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!user) {
      log(`❌ 用户不存在: ${credentials.email}`, 'red');
      return false;
    }

    // 验证密码
    const passwordValid = await verifyPassword(credentials.password, user.password);
    if (!passwordValid) {
      log(`❌ 密码错误: ${credentials.email}`, 'red');
      return false;
    }

    // 验证角色
    if (user.role !== credentials.expectedRole) {
      log(`❌ 角色不匹配: ${credentials.email} (期望: ${credentials.expectedRole}, 实际: ${user.role})`, 'red');
      return false;
    }

    // 验证邮箱是否已验证
    if (!user.emailVerified) {
      log(`⚠️  邮箱未验证: ${credentials.email}`, 'yellow');
    }

    log(`✅ 账号验证成功: ${user.name} (${credentials.email})`, 'green');
    log(`   🆔 用户ID: ${user.userId}`, 'reset');
    log(`   👤 角色: ${user.role}`, 'reset');
    log(`   ⭐ 信用分数: ${user.creditScore}`, 'reset');
    log(`   💰 保证金: ${user.depositBalance} USDT`, 'reset');
    log(`   🏆 信用等级: ${user.creditLevel}`, 'reset');
    log(`   📧 邮箱验证: ${user.emailVerified ? '已验证' : '未验证'}`, 'reset');
    log(`   📅 创建时间: ${user.createdAt.toLocaleString('zh-CN')}`, 'reset');

    return true;

  } catch (error) {
    log(`❌ 验证账号失败: ${credentials.email}`, 'red');
    log(`错误信息: ${error.message}`, 'red');
    return false;
  }
}

// 获取数据库统计信息
async function getDatabaseStats() {
  try {
    const userCount = await prisma.user.count();
    const adminCount = await prisma.user.count({ where: { role: 'ADMIN' } });
    const userRoleCount = await prisma.user.count({ where: { role: 'USER' } });
    const creditHistoryCount = await prisma.creditHistory.count();
    const securityLogCount = await prisma.securityLog.count();

    return {
      userCount,
      adminCount,
      userRoleCount,
      creditHistoryCount,
      securityLogCount
    };
  } catch (error) {
    log(`⚠️  获取数据库统计失败: ${error.message}`, 'yellow');
    return null;
  }
}

// 主函数
async function main() {
  try {
    log('🔍 开始验证 BitMarket 测试账号...', 'cyan');
    log('', 'reset');

    let successCount = 0;
    let totalCount = testCredentials.length;

    // 验证每个账号
    for (let i = 0; i < testCredentials.length; i++) {
      const credentials = testCredentials[i];
      log(`📝 验证账号 ${i + 1}/${totalCount}: ${credentials.name}`, 'blue');
      
      const success = await verifyAccount(credentials);
      if (success) {
        successCount++;
      }
      
      log('', 'reset');
    }

    // 显示验证结果
    log('📊 验证结果汇总:', 'bright');
    log('=' * 50, 'cyan');
    log(`✅ 成功: ${successCount}/${totalCount}`, successCount === totalCount ? 'green' : 'yellow');
    log(`❌ 失败: ${totalCount - successCount}/${totalCount}`, totalCount - successCount === 0 ? 'green' : 'red');
    log('', 'reset');

    // 显示数据库统计
    const stats = await getDatabaseStats();
    if (stats) {
      log('📈 数据库统计:', 'bright');
      log('=' * 50, 'cyan');
      log(`👥 总用户数: ${stats.userCount}`, 'reset');
      log(`👑 管理员数: ${stats.adminCount}`, 'reset');
      log(`👤 普通用户数: ${stats.userRoleCount}`, 'reset');
      log(`📋 信用记录数: ${stats.creditHistoryCount}`, 'reset');
      log(`🔒 安全日志数: ${stats.securityLogCount}`, 'reset');
      log('', 'reset');
    }

    // 显示登录信息
    log('🔗 登录信息:', 'bright');
    log('=' * 50, 'cyan');
    log('应用地址: http://localhost:3000', 'cyan');
    log('', 'reset');
    log('管理员登录:', 'yellow');
    log('  邮箱: <EMAIL>', 'reset');
    log('  密码: admin123456', 'reset');
    log('', 'reset');
    log('用户登录:', 'yellow');
    log('  邮箱: <EMAIL> 或 <EMAIL>', 'reset');
    log('  密码: user123456', 'reset');
    log('', 'reset');

    if (successCount === totalCount) {
      log('🎉 所有测试账号验证通过！可以正常使用。', 'green');
    } else {
      log('⚠️  部分账号验证失败，请检查数据库或重新创建账号。', 'yellow');
      log('重新创建命令: npm run test:accounts:reset', 'cyan');
    }

  } catch (error) {
    log('❌ 验证过程失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main, verifyAccount };
