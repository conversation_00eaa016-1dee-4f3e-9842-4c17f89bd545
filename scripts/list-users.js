const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function listUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        userId: true,
        name: true,
        email: true,
        role: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`📋 用户列表 (共 ${users.length} 个用户):\n`)
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.role === 'ADMIN' ? '👑' : '👤'} ${user.name || '未设置姓名'}`)
      console.log(`   邮箱: ${user.email}`)
      console.log(`   用户ID: ${user.userId || '未设置'}`)
      console.log(`   角色: ${user.role}`)
      console.log(`   注册时间: ${user.createdAt.toLocaleString()}`)
      console.log('')
    })

    const adminUsers = users.filter(user => user.role === 'ADMIN')
    console.log(`👑 管理员用户: ${adminUsers.length} 个`)
    
    if (adminUsers.length === 0) {
      console.log('\n⚠️  当前没有管理员用户，请使用以下命令设置管理员:')
      console.log('node scripts/set-admin.js <用户邮箱>')
    }
    
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

listUsers()
