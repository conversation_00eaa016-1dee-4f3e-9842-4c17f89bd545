const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testMediatorManagerButton() {
  console.log('🔧 测试中间人管理器按钮简化...\n')

  try {
    // 1. 检查中间人用户
    console.log('1. 检查中间人用户...')
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true
      }
    })

    console.log(`✅ 找到 ${mediators.length} 个中间人用户`)
    mediators.forEach((mediator, index) => {
      console.log(`\n   ${index + 1}. ${mediator.name} (${mediator.email})`)
      console.log(`      状态: ${mediator.mediatorStatus}`)
      console.log(`      信誉: ${mediator.mediatorReputation}`)
      console.log(`      成功率: ${mediator.mediatorSuccessRate}%`)
      console.log(`      订单数: ${mediator.mediatorTotalOrders}`)
    })

    // 2. 确保有测试中间人用户
    console.log('\n2. 确保有测试中间人用户...')
    
    let testMediatorUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testMediatorUser) {
      console.log('   创建测试中间人用户...')
      testMediatorUser = await prisma.user.create({
        data: {
          name: '测试中间人',
          email: '<EMAIL>',
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.025,
          mediatorReputation: 95.5,
          mediatorVerifiedAt: new Date(),
          bnbWalletVerified: true,
          mediatorExperience: '具有丰富的交易调解经验',
          mediatorIntroduction: '专业的交易调解员',
          mediatorSuccessRate: 98.5,
          mediatorTotalOrders: 156,
          depositBalance: 10000,
          creditScore: 95,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testMediatorUser.name}`)
    } else {
      console.log(`   ✅ 已有测试中间人: ${testMediatorUser.name}`)
    }

    // 3. 创建普通用户用于对比
    console.log('\n3. 确保有普通用户用于对比...')
    
    let testNormalUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testNormalUser) {
      console.log('   创建测试普通用户...')
      testNormalUser = await prisma.user.create({
        data: {
          name: '测试普通用户',
          email: '<EMAIL>',
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          depositBalance: 1000,
          creditScore: 80,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testNormalUser.name}`)
    } else {
      console.log(`   ✅ 已有普通用户: ${testNormalUser.name}`)
    }

    // 4. 验证个人资料页面显示逻辑
    console.log('\n4. 验证个人资料页面显示逻辑...')
    
    console.log('   ✅ 中间人用户应该看到:')
    console.log('     - 中间人管理器卡片')
    console.log('     - 橙色盾牌图标')
    console.log('     - "中间人管理器"标题')
    console.log('     - "管理调解服务"描述')
    console.log('     - 点击跳转到 /mediator/dashboard')
    
    console.log('   ❌ 中间人用户不应该看到:')
    console.log('     - 统计数据网格 (调解订单、成功率、信誉值)')
    console.log('     - 状态标签 (已认证/待认证)')
    console.log('     - 橙色边框')
    
    console.log('   ❌ 普通用户应该:')
    console.log('     - 完全不显示中间人管理器卡片')

    // 5. 模拟前端显示逻辑
    console.log('\n5. 模拟前端显示逻辑...')
    
    const users = [testMediatorUser, testNormalUser]
    
    users.forEach(user => {
      console.log(`\n   用户: ${user.name} (${user.email})`)
      console.log(`   isMediator: ${user.isMediator}`)
      
      if (user.isMediator) {
        console.log('   ✅ 显示中间人管理器:')
        console.log('     ┌─────────────────────────────────┐')
        console.log('     │ 🛡️  中间人管理器                │')
        console.log('     │     管理调解服务                │')
        console.log('     └─────────────────────────────────┘')
        console.log('     点击跳转: /mediator/dashboard')
      } else {
        console.log('   ❌ 不显示中间人管理器')
      }
    })

    // 6. 与其他快捷操作的一致性检查
    console.log('\n6. 与其他快捷操作的一致性检查...')
    
    console.log('   ✅ 快捷操作卡片设计一致性:')
    console.log('     - 信用评级: 简单图标 + 标题 + 描述')
    console.log('     - 我的订单: 简单图标 + 标题 + 描述')
    console.log('     - 我的商品: 简单图标 + 标题 + 描述')
    console.log('     - 中间人管理器: 简单图标 + 标题 + 描述 ✅ 现在一致')
    console.log('     - 反馈助手: 简单图标 + 标题 + 描述')
    console.log('     - 账户设置: 简单图标 + 标题 + 描述')
    
    console.log('   ✅ 设计原则:')
    console.log('     - 所有快捷操作都是简单的跳转按钮')
    console.log('     - 不在个人资料页面显示详细数据')
    console.log('     - 详细信息在对应的专门页面显示')
    console.log('     - 保持界面简洁和一致性')

    // 7. 功能验证清单
    console.log('\n7. 功能验证清单...')
    
    console.log('   ✅ 需要验证的功能:')
    console.log('     □ 中间人用户登录后看到中间人管理器卡片')
    console.log('     □ 卡片显示橙色盾牌图标')
    console.log('     □ 卡片标题为"中间人管理器"')
    console.log('     □ 卡片描述为"管理调解服务"')
    console.log('     □ 点击卡片跳转到中间人控制台页面')
    console.log('     □ 普通用户不显示该卡片')
    console.log('     □ 卡片样式与其他快捷操作一致')
    console.log('     □ 不显示统计数据和状态标签')

    console.log('\n🎉 测试准备完成！')
    console.log('\n📝 手动测试步骤:')
    
    console.log('\n【步骤1: 测试中间人用户】')
    console.log(`1. 使用中间人账户登录: ${testMediatorUser.email}`)
    console.log('2. 访问个人资料页面: http://localhost:3000/profile')
    console.log('3. 查看快捷操作区域')
    console.log('4. 确认显示简洁的"中间人管理器"卡片')
    console.log('5. 确认不显示统计数据')
    console.log('6. 点击卡片跳转到中间人控制台')
    
    console.log('\n【步骤2: 测试普通用户】')
    console.log(`1. 使用普通用户账户登录: ${testNormalUser.email}`)
    console.log('2. 访问个人资料页面: http://localhost:3000/profile')
    console.log('3. 确认不显示中间人管理器卡片')
    
    console.log('\n【步骤3: 验证设计一致性】')
    console.log('1. 对比中间人管理器与其他快捷操作卡片')
    console.log('2. 确认样式、布局、交互方式一致')
    console.log('3. 确认所有卡片都是简单的跳转按钮')

    console.log('\n💡 设计理念:')
    console.log('   - 个人资料页面专注于快捷导航')
    console.log('   - 详细数据在专门页面显示')
    console.log('   - 保持界面简洁和用户体验一致')
    console.log('   - 中间人管理器现在与其他功能保持一致的设计风格')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testMediatorManagerButton().catch(console.error)
}

module.exports = { testMediatorManagerButton }
