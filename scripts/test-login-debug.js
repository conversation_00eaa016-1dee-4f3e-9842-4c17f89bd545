#!/usr/bin/env node

/**
 * BitMarket 登录功能调试脚本
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// 测试账号
const testCredentials = [
  {
    email: '<EMAIL>',
    password: 'admin123456',
    expectedRole: 'ADMIN',
    name: '系统管理员'
  },
  {
    email: '<EMAIL>',
    password: 'user123456',
    expectedRole: 'USER',
    name: '张三'
  }
];

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 模拟 NextAuth 的认证逻辑
async function simulateAuth(credentials) {
  try {
    log(`🔍 开始认证: ${credentials.email}`, 'blue');
    
    if (!credentials?.email || !credentials?.password) {
      return { success: false, error: '邮箱或密码为空' };
    }

    // 查找用户
    log(`📋 查找用户...`, 'reset');
    const user = await prisma.user.findUnique({
      where: { email: credentials.email }
    });

    if (!user) {
      return { success: false, error: '用户不存在' };
    }
    
    log(`✅ 用户找到: ${user.name} (ID: ${user.id})`, 'green');

    if (!user.password) {
      return { success: false, error: '密码未设置' };
    }
    
    log(`🔐 验证密码...`, 'reset');
    log(`存储的密码哈希: ${user.password.substring(0, 20)}...`, 'reset');

    // 验证密码
    const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
    
    log(`密码验证结果: ${isPasswordValid ? '✅ 正确' : '❌ 错误'}`, isPasswordValid ? 'green' : 'red');

    if (!isPasswordValid) {
      return { success: false, error: '密码错误' };
    }

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email || '',
        name: user.name || '',
        userId: user.userId || '',
        role: user.role || 'USER',
      }
    };

  } catch (error) {
    log(`❌ 认证过程出错: ${error.message}`, 'red');
    return { success: false, error: `认证过程出错: ${error.message}` };
  }
}

// 主函数
async function main() {
  try {
    log('🔍 开始 BitMarket 登录调试...', 'cyan');
    log('', 'reset');

    for (const cred of testCredentials) {
      log(`\n📝 测试账号: ${cred.name}`, 'blue');
      log('=' * 40, 'cyan');
      
      const result = await simulateAuth(cred);
      
      if (result.success) {
        log(`🎉 登录成功!`, 'green');
        log(`用户信息:`, 'reset');
        log(`  ID: ${result.user.id}`, 'reset');
        log(`  用户ID: ${result.user.userId}`, 'reset');
        log(`  邮箱: ${result.user.email}`, 'reset');
        log(`  姓名: ${result.user.name}`, 'reset');
        log(`  角色: ${result.user.role}`, 'reset');
      } else {
        log(`❌ 登录失败: ${result.error}`, 'red');
      }
      
      log('', 'reset');
    }

    log('🌐 浏览器登录测试:', 'cyan');
    log('地址: http://localhost:3000/auth/signin', 'reset');
    log('管理员: <EMAIL> / admin123456', 'reset');
    log('用户: <EMAIL> / user123456', 'reset');

  } catch (error) {
    log('❌ 测试失败:', 'red');
    log(error.message, 'red');
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
main().catch(console.error);
