const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testCompletePurchase() {
  console.log('🛒 开始完整购买流程测试...')
  
  try {
    // 1. 获取买家用户信息
    const buyer = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!buyer) {
      console.error('❌ 买家用户不存在')
      return
    }
    
    console.log(`👤 买家用户: ${buyer.email}, 余额: ${buyer.depositBalance} USDT`)
    
    // 2. 获取测试商品
    const product = await prisma.product.findFirst({
      where: {
        title: '测试商品 - AirPods Pro 2',
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED'
      },
      include: {
        seller: true
      }
    })
    
    if (!product) {
      console.error('❌ 测试商品不存在')
      return
    }
    
    console.log(`📦 测试商品: ${product.title}, 价格: ${product.price} USDT`)
    
    // 3. 获取买家地址
    const address = await prisma.address.findFirst({
      where: {
        userId: buyer.id,
        isDefault: true
      }
    })
    
    if (!address) {
      console.error('❌ 买家地址不存在')
      return
    }
    
    console.log(`📍 收货地址: ${address.name} - ${address.province}${address.city}${address.district}`)
    
    // 4. 创建测试订单
    console.log('📝 创建测试订单...')
    
    const order = await prisma.order.create({
      data: {
        orderNumber: `TEST-${Date.now()}`,
        productId: product.id,
        buyerId: buyer.id,
        sellerId: product.sellerId,
        totalAmount: product.price,
        productPrice: product.price,
        status: 'PENDING_PAYMENT',
        paymentMethod: 'balance-pay',
        shippingAddress: {
          name: address.name,
          phone: address.phone,
          province: address.province,
          city: address.city,
          district: address.district,
          detail: address.detail,
          remark: address.remark
        },
        metadata: {
          quantity: 1,
          itemPrice: product.price
        }
      }
    })
    
    console.log(`✅ 订单创建成功: ${order.orderNumber} (ID: ${order.id})`)
    
    // 5. 模拟余额支付
    console.log('💰 模拟余额支付...')
    
    if (buyer.depositBalance < product.price) {
      console.error(`❌ 余额不足: ${buyer.depositBalance} < ${product.price}`)
      return
    }
    
    // 使用事务处理支付
    await prisma.$transaction(async (tx) => {
      // 扣除买家余额
      await tx.user.update({
        where: { id: buyer.id },
        data: {
          depositBalance: {
            decrement: product.price
          }
        }
      })
      
      // 增加卖家余额
      await tx.user.update({
        where: { id: product.sellerId },
        data: {
          depositBalance: {
            increment: product.price
          }
        }
      })
      
      // 更新订单状态
      await tx.order.update({
        where: { id: order.id },
        data: {
          status: 'PAID',
          paymentConfirmed: true
        }
      })
      
      // 减少商品库存
      await tx.product.update({
        where: { id: product.id },
        data: {
          stock: {
            decrement: 1
          }
        }
      })
      
      // 记录操作日志
      await tx.orderLog.create({
        data: {
          orderId: order.id,
          operatorId: buyer.id,
          action: 'BALANCE_PAYMENT',
          description: `买家使用余额支付 ${product.price} USDT`
        }
      })
    })
    
    console.log('✅ 余额支付成功')
    
    // 6. 模拟卖家发货
    console.log('📦 模拟卖家发货...')
    
    await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'SHIPPED',
        trackingNumber: 'SF1234567890',
        shippingCompany: '顺丰快递',
        shippedAt: new Date()
      }
    })
    
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: product.sellerId,
        action: 'SHIP_ORDER',
        description: '卖家发货，运单号：SF1234567890'
      }
    })
    
    console.log('✅ 发货成功')
    
    // 7. 模拟买家确认收货
    console.log('✅ 模拟买家确认收货...')
    
    await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'COMPLETED',
        receivedAt: new Date()
      }
    })
    
    await prisma.orderLog.create({
      data: {
        orderId: order.id,
        operatorId: buyer.id,
        action: 'CONFIRM_RECEIPT',
        description: '买家确认收货'
      }
    })
    
    console.log('✅ 确认收货成功')
    
    // 8. 验证最终状态
    console.log('🔍 验证最终状态...')
    
    const finalOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        product: true,
        buyer: true,
        seller: true
      }
    })
    
    const finalBuyer = await prisma.user.findUnique({
      where: { id: buyer.id }
    })
    
    const finalSeller = await prisma.user.findUnique({
      where: { id: product.sellerId }
    })
    
    const finalProduct = await prisma.product.findUnique({
      where: { id: product.id }
    })
    
    console.log('\n🎉 完整购买流程测试成功！')
    console.log('\n📊 最终状态:')
    console.log(`- 订单状态: ${finalOrder.status}`)
    console.log(`- 买家余额: ${finalBuyer.depositBalance} USDT (减少 ${product.price})`)
    console.log(`- 卖家余额: ${finalSeller.depositBalance} USDT (增加 ${product.price})`)
    console.log(`- 商品库存: ${finalProduct.stock} 件 (减少 1)`)
    console.log(`- 订单号: ${finalOrder.orderNumber}`)
    console.log(`- 支付时间: ${finalOrder.paidAt}`)
    console.log(`- 发货时间: ${finalOrder.shippedAt}`)
    console.log(`- 收货时间: ${finalOrder.receivedAt}`)
    
    return {
      success: true,
      orderId: order.id,
      orderNumber: order.orderNumber
    }
    
  } catch (error) {
    console.error('❌ 完整购买流程测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testCompletePurchase()
