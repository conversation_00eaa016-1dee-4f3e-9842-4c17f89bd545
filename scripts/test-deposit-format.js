// 测试保证金格式化函数
function formatDeposit(amount) {
  if (amount < 1000) {
    return amount.toString()
  } else if (amount < 1000000) {
    const k = Math.floor(amount / 1000)
    const remainder = amount % 1000
    if (remainder === 0) {
      return `${k}k`
    } else {
      return `${(amount / 1000).toFixed(1)}k`.replace('.0', '')
    }
  } else if (amount < 1000000000) {
    const m = Math.floor(amount / 1000000)
    const remainder = amount % 1000000
    if (remainder === 0) {
      return `${m}m`
    } else {
      return `${(amount / 1000000).toFixed(1)}m`.replace('.0', '')
    }
  } else {
    const b = Math.floor(amount / 1000000000)
    const remainder = amount % 1000000000
    if (remainder === 0) {
      return `${b}b`
    } else {
      return `${(amount / 1000000000).toFixed(1)}b`.replace('.0', '')
    }
  }
}

console.log('🧪 测试保证金格式化函数...\n')

// 测试用例
const testCases = [
  // 0-999 范围
  { input: 0, expected: '0' },
  { input: 50, expected: '50' },
  { input: 100, expected: '100' },
  { input: 999, expected: '999' },
  
  // 1k-999k 范围
  { input: 1000, expected: '1k' },
  { input: 1500, expected: '1.5k' },
  { input: 2000, expected: '2k' },
  { input: 10000, expected: '10k' },
  { input: 25500, expected: '25.5k' },
  { input: 100000, expected: '100k' },
  { input: 999000, expected: '999k' },
  { input: 999999, expected: '1000k' },
  
  // 1m-999m 范围
  { input: 1000000, expected: '1m' },
  { input: 1500000, expected: '1.5m' },
  { input: 2000000, expected: '2m' },
  { input: 10000000, expected: '10m' },
  { input: 100000000, expected: '100m' },
  { input: 999000000, expected: '999m' },
  { input: 999999999, expected: '1000m' },
  
  // 1b+ 范围
  { input: 1000000000, expected: '1b' },
  { input: 1500000000, expected: '1.5b' },
  { input: 10000000000, expected: '10b' },
  { input: 100000000000, expected: '100b' }
]

let passedTests = 0
let failedTests = 0

testCases.forEach((testCase, index) => {
  const result = formatDeposit(testCase.input)
  const passed = result === testCase.expected
  
  if (passed) {
    console.log(`✅ 测试 ${index + 1}: ${testCase.input} → ${result}`)
    passedTests++
  } else {
    console.log(`❌ 测试 ${index + 1}: ${testCase.input} → ${result} (期望: ${testCase.expected})`)
    failedTests++
  }
})

console.log(`\n📊 测试结果:`)
console.log(`   ✅ 通过: ${passedTests}`)
console.log(`   ❌ 失败: ${failedTests}`)
console.log(`   📈 成功率: ${((passedTests / testCases.length) * 100).toFixed(1)}%`)

if (failedTests === 0) {
  console.log('\n🎉 所有测试通过！')
} else {
  console.log('\n⚠️  有测试失败，请检查格式化逻辑')
}

// 额外的边界测试
console.log('\n🔍 边界值测试:')
const boundaryTests = [
  999,    // 最大的不带k的数
  1000,   // 最小的k数
  999999, // 最大的k数
  1000000, // 最小的m数
  999999999, // 最大的m数
  1000000000 // 最小的b数
]

boundaryTests.forEach(value => {
  console.log(`   ${value.toLocaleString()} → ${formatDeposit(value)}`)
})

console.log('\n💡 使用示例:')
console.log('   用户保证金 50 USDT → 显示: 50')
console.log('   用户保证金 1,500 USDT → 显示: 1.5k')
console.log('   用户保证金 2,500,000 USDT → 显示: 2.5m')
console.log('   用户保证金 10,000,000,000 USDT → 显示: 10b')
