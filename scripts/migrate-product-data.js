#!/usr/bin/env node

/**
 * BitMarket 商品数据迁移脚本
 * 更新现有商品的分类和成色数据
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 成色映射 - 将旧的成色值映射到新的成色值
const conditionMapping = {
  'NEW': 'NEW',
  'USED_LIKE_NEW': 'LIKE_NEW',
  'USED_GOOD': 'GOOD',
  'USED_FAIR': 'FAIR'
};

// 分类关键词映射
const categoryKeywordMapping = {
  'ELECTRONICS': ['手机', '电脑', '数码', '电子', '平板', '耳机', '音响', '相机', '电视', '笔记本'],
  'CLOTHING': ['衣服', '鞋子', '包包', '配饰', '首饰', '帽子', '围巾', '手表', '服装', '时装'],
  'HOME': ['家具', '家电', '装饰', '厨具', '床上用品', '收纳', '灯具', '沙发', '桌子', '椅子'],
  'BOOKS': ['书籍', '文具', '办公', '笔记本', '教材', '小说', '工具书', '字典', '杂志'],
  'SPORTS': ['运动', '健身', '户外', '球类', '器材', '装备', '鞋服', '跑步', '游泳', '篮球'],
  'BEAUTY': ['化妆品', '护肤', '个护', '香水', '美容', '洗护', '彩妆', '面膜', '口红'],
  'AUTOMOTIVE': ['汽车', '配件', '装饰', '保养', '改装', '工具', '用品', '轮胎', '机油'],
  'TOYS': ['玩具', '母婴', '儿童', '婴儿', '益智', '模型', '游戏', '娃娃', '积木'],
  'HEALTH': ['保健', '医疗', '健康', '器械', '营养', '药品', '康复', '按摩', '理疗'],
  'VIRTUAL': ['游戏', '软件', '数字', '虚拟', '道具', '账号', '会员', '充值', '点卡']
};

// 智能分类建议
function suggestCategory(title, description = '') {
  const text = `${title} ${description}`.toLowerCase();
  
  for (const [category, keywords] of Object.entries(categoryKeywordMapping)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return category;
    }
  }
  
  return 'GENERAL';
}

// 迁移商品分类
async function migrateCategories() {
  log('🔄 开始迁移商品分类...', 'cyan');
  
  try {
    // 获取所有需要更新分类的商品
    const products = await prisma.product.findMany({
      where: {
        category: 'GENERAL'
      },
      select: {
        id: true,
        title: true,
        description: true,
        category: true
      }
    });

    log(`📋 找到 ${products.length} 个需要更新分类的商品`, 'blue');

    let updatedCount = 0;
    const categoryStats = {};

    for (const product of products) {
      const suggestedCategory = suggestCategory(product.title, product.description);
      
      if (suggestedCategory !== product.category) {
        await prisma.product.update({
          where: { id: product.id },
          data: { category: suggestedCategory }
        });
        
        updatedCount++;
        categoryStats[suggestedCategory] = (categoryStats[suggestedCategory] || 0) + 1;
        
        log(`✅ 更新商品: ${product.title.substring(0, 30)}... -> ${suggestedCategory}`, 'green');
      }
    }

    log(`\n📊 分类迁移统计:`, 'cyan');
    for (const [category, count] of Object.entries(categoryStats)) {
      log(`  ${category}: ${count} 个商品`, 'reset');
    }
    
    log(`\n🎉 分类迁移完成! 共更新 ${updatedCount} 个商品`, 'green');
    
  } catch (error) {
    log(`❌ 分类迁移失败: ${error.message}`, 'red');
    throw error;
  }
}

// 迁移商品成色
async function migrateConditions() {
  log('\n🔄 开始迁移商品成色...', 'cyan');
  
  try {
    // 获取所有需要更新成色的商品
    const products = await prisma.product.findMany({
      select: {
        id: true,
        title: true,
        condition: true
      }
    });

    log(`📋 找到 ${products.length} 个商品需要检查成色`, 'blue');

    let updatedCount = 0;
    const conditionStats = {};

    for (const product of products) {
      const oldCondition = product.condition;
      const newCondition = conditionMapping[oldCondition] || oldCondition;
      
      if (newCondition !== oldCondition) {
        await prisma.product.update({
          where: { id: product.id },
          data: { condition: newCondition }
        });
        
        updatedCount++;
        conditionStats[newCondition] = (conditionStats[newCondition] || 0) + 1;
        
        log(`✅ 更新成色: ${product.title.substring(0, 30)}... ${oldCondition} -> ${newCondition}`, 'green');
      }
    }

    log(`\n📊 成色迁移统计:`, 'cyan');
    for (const [condition, count] of Object.entries(conditionStats)) {
      log(`  ${condition}: ${count} 个商品`, 'reset');
    }
    
    log(`\n🎉 成色迁移完成! 共更新 ${updatedCount} 个商品`, 'green');
    
  } catch (error) {
    log(`❌ 成色迁移失败: ${error.message}`, 'red');
    throw error;
  }
}

// 验证数据完整性
async function validateData() {
  log('\n🔍 验证数据完整性...', 'cyan');
  
  try {
    // 检查分类分布
    const categoryStats = await prisma.product.groupBy({
      by: ['category'],
      _count: {
        category: true
      }
    });

    log('📊 分类分布:', 'blue');
    categoryStats.forEach(stat => {
      log(`  ${stat.category}: ${stat._count.category} 个商品`, 'reset');
    });

    // 检查成色分布
    const conditionStats = await prisma.product.groupBy({
      by: ['condition'],
      _count: {
        condition: true
      }
    });

    log('\n📊 成色分布:', 'blue');
    conditionStats.forEach(stat => {
      log(`  ${stat.condition}: ${stat._count.condition} 个商品`, 'reset');
    });

    // 检查是否有无效数据
    const invalidCategories = await prisma.product.count({
      where: {
        category: {
          notIn: ['GENERAL', 'ELECTRONICS', 'CLOTHING', 'BOOKS', 'HOME', 'SPORTS', 'BEAUTY', 'VIRTUAL', 'AUTOMOTIVE', 'TOYS', 'HEALTH']
        }
      }
    });

    const invalidConditions = await prisma.product.count({
      where: {
        condition: {
          notIn: ['NEW', 'LIKE_NEW', 'GOOD', 'FAIR', 'POOR', 'NOT_APPLICABLE']
        }
      }
    });

    if (invalidCategories > 0) {
      log(`⚠️  发现 ${invalidCategories} 个无效分类`, 'yellow');
    }

    if (invalidConditions > 0) {
      log(`⚠️  发现 ${invalidConditions} 个无效成色`, 'yellow');
    }

    if (invalidCategories === 0 && invalidConditions === 0) {
      log('✅ 数据验证通过，所有数据都有效', 'green');
    }

  } catch (error) {
    log(`❌ 数据验证失败: ${error.message}`, 'red');
    throw error;
  }
}

// 主函数
async function main() {
  try {
    log('🚀 开始 BitMarket 商品数据迁移...', 'cyan');
    log('', 'reset');

    // 1. 迁移分类
    await migrateCategories();

    // 2. 迁移成色
    await migrateConditions();

    // 3. 验证数据
    await validateData();

    log('\n🎉 数据迁移完成！', 'green');
    log('', 'reset');
    log('📝 迁移内容:', 'cyan');
    log('  ✅ 商品分类系统已扩展', 'reset');
    log('  ✅ 商品成色系统已重构', 'reset');
    log('  ✅ 现有数据已智能分类', 'reset');
    log('  ✅ 数据完整性已验证', 'reset');

  } catch (error) {
    log('❌ 数据迁移失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main, migrateCategories, migrateConditions, validateData };
