@echo off
setlocal enabledelayedexpansion

REM BitMarket 依赖安装脚本 (Windows)
REM 自动安装所有必需的依赖包

title BitMarket 依赖安装

echo.
echo 🚀 BitMarket 依赖安装脚本
echo ================================
echo.

REM 检查是否在项目根目录
if not exist "package.json" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 检查 Node.js 是否安装
echo ℹ️  检查 Node.js 版本...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Node.js 未安装，请先安装 Node.js ^>= 18.0.0
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 获取 Node.js 版本
for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

REM 检查包管理器
echo ℹ️  检查包管理器...
set PACKAGE_MANAGER=npm

pnpm --version >nul 2>&1
if not errorlevel 1 (
    set PACKAGE_MANAGER=pnpm
    echo ✅ 使用 pnpm 作为包管理器
    goto :package_manager_found
)

yarn --version >nul 2>&1
if not errorlevel 1 (
    set PACKAGE_MANAGER=yarn
    echo ✅ 使用 yarn 作为包管理器
    goto :package_manager_found
)

npm --version >nul 2>&1
if not errorlevel 1 (
    set PACKAGE_MANAGER=npm
    echo ✅ 使用 npm 作为包管理器
    goto :package_manager_found
)

echo ❌ 错误: 未找到包管理器，请安装 npm、yarn 或 pnpm
pause
exit /b 1

:package_manager_found

REM 询问是否清理旧依赖
echo.
set /p CLEAN_DEPS="是否清理旧的依赖？(y/N): "
if /i "%CLEAN_DEPS%"=="y" (
    echo ⚠️  清理旧的依赖...
    
    if exist "node_modules" (
        echo 删除 node_modules 目录...
        rmdir /s /q "node_modules"
    )
    
    if exist "package-lock.json" (
        echo 删除 package-lock.json...
        del "package-lock.json"
    )
    
    if exist "yarn.lock" (
        echo 删除 yarn.lock...
        del "yarn.lock"
    )
    
    if exist "pnpm-lock.yaml" (
        echo 删除 pnpm-lock.yaml...
        del "pnpm-lock.yaml"
    )
    
    echo ✅ 清理完成
)

REM 安装依赖
echo.
echo ℹ️  开始安装依赖...

if "%PACKAGE_MANAGER%"=="pnpm" (
    pnpm install
) else if "%PACKAGE_MANAGER%"=="yarn" (
    yarn install
) else (
    npm install
)

if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装成功

REM 生成 Prisma 客户端
echo.
echo ℹ️  生成 Prisma 客户端...

if exist "prisma\schema.prisma" (
    npx prisma generate
    if errorlevel 1 (
        echo ❌ Prisma 客户端生成失败
        pause
        exit /b 1
    )
    echo ✅ Prisma 客户端生成成功
) else (
    echo ⚠️  未找到 Prisma schema 文件，跳过客户端生成
)

REM 验证安装
echo.
echo ℹ️  验证安装...

set VERIFICATION_FAILED=0

if not exist "node_modules\next" (
    echo ❌ Next.js 安装失败
    set VERIFICATION_FAILED=1
)

if not exist "node_modules\react" (
    echo ❌ React 安装失败
    set VERIFICATION_FAILED=1
)

if not exist "node_modules\@prisma\client" (
    echo ❌ Prisma Client 安装失败
    set VERIFICATION_FAILED=1
)

if not exist "node_modules\socket.io" (
    echo ❌ Socket.IO 安装失败
    set VERIFICATION_FAILED=1
)

if not exist "node_modules\tailwindcss" (
    echo ❌ Tailwind CSS 安装失败
    set VERIFICATION_FAILED=1
)

if %VERIFICATION_FAILED%==1 (
    echo ❌ 部分关键依赖安装失败，请检查错误信息
    pause
    exit /b 1
)

echo ✅ 所有关键依赖验证通过

REM 显示安装统计
echo.
echo ℹ️  安装统计信息:
echo   📦 生产依赖: 38 个包
echo   🛠️  开发依赖: 19 个包
echo   📊 总计: 57 个直接依赖

if exist "node_modules" (
    for /f %%i in ('dir /ad /b node_modules ^| find /c /v ""') do set TOTAL_PACKAGES=%%i
    echo   🗂️  总安装包数: !TOTAL_PACKAGES!
)

REM 显示下一步操作
echo.
echo 🎉 BitMarket 依赖安装完成！
echo.
echo 📋 下一步操作:
echo   1. 配置环境变量: copy .env.example .env.local
echo   2. 配置数据库连接
echo   3. 运行数据库迁移: npx prisma migrate dev
echo   4. 启动开发服务器: npm run dev
echo.
echo 📚 更多信息请查看:
echo   - 开发环境搭建: docs\deployment\development-setup.md
echo   - 依赖详细说明: docs\deployment\dependencies-installation.md
echo.

pause
