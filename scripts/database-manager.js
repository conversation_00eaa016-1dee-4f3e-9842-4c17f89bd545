/**
 * 数据库管理工具
 * 提供可视化和命令行管理选项
 */

const { spawn } = require('child_process')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// 启动 Prisma Studio
function startPrismaStudio() {
  console.log('🎨 启动 Prisma Studio...')
  console.log('浏览器将自动打开 http://localhost:5555')
  console.log('按 Ctrl+C 停止服务')
  
  const studio = spawn('npx', ['prisma', 'studio'], {
    stdio: 'inherit',
    shell: true
  })
  
  studio.on('close', (code) => {
    console.log(`\n👋 Prisma Studio 已关闭 (退出码: ${code})`)
  })
  
  // 处理 Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭 Prisma Studio...')
    studio.kill('SIGINT')
  })
}

// 显示数据库信息
async function showDatabaseInfo() {
  console.log('📊 数据库信息')
  console.log('='.repeat(40))
  
  try {
    // 获取表统计
    const stats = {}

    try {
      stats.users = await prisma.user.count()
    } catch (e) {
      stats.users = 0
    }

    try {
      stats.products = await prisma.product.count()
    } catch (e) {
      stats.products = 0
    }

    try {
      stats.orders = await prisma.order.count()
    } catch (e) {
      stats.orders = 0
    }

    try {
      stats.demands = await prisma.demand.count()
    } catch (e) {
      stats.demands = 0
    }

    try {
      stats.reviews = await prisma.review.count()
    } catch (e) {
      stats.reviews = 0
    }

    try {
      stats.messages = await prisma.message.count()
    } catch (e) {
      stats.messages = 0
    }

    try {
      stats.accounts = await prisma.account.count()
    } catch (e) {
      stats.accounts = 0
    }
    
    console.log('📋 表记录统计:')
    Object.entries(stats).forEach(([table, count]) => {
      console.log(`  ${table.padEnd(15)}: ${count.toLocaleString()} 条`)
    })
    
    // 获取最近活动
    console.log('\n📅 最近活动:')
    
    const recentUsers = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3,
      select: {
        email: true,
        name: true,
        createdAt: true
      }
    })
    
    if (recentUsers.length > 0) {
      console.log('  最新用户:')
      recentUsers.forEach(user => {
        console.log(`    • ${user.name || user.email} (${user.createdAt.toLocaleDateString()})`)
      })
    }
    
    const recentProducts = await prisma.product.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3,
      select: {
        title: true,
        price: true,
        createdAt: true
      }
    })
    
    if (recentProducts.length > 0) {
      console.log('  最新商品:')
      recentProducts.forEach(product => {
        console.log(`    • ${product.title} - ${product.price} USDT (${product.createdAt.toLocaleDateString()})`)
      })
    }
    
    // 数据库大小估算
    const totalRecords = Object.values(stats).reduce((sum, count) => sum + count, 0)
    console.log(`\n💾 总记录数: ${totalRecords.toLocaleString()}`)
    
  } catch (error) {
    console.error('❌ 获取数据库信息失败:', error.message)
  }
}

// 备份数据库
async function backupDatabase() {
  console.log('💾 备份数据库...')
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupFile = `backup-${timestamp}.json`
    
    console.log('📦 导出数据...')
    
    const data = {
      timestamp: new Date().toISOString(),
      users: await prisma.user.findMany(),
      products: await prisma.product.findMany(),
      orders: await prisma.order.findMany(),
      demands: await prisma.demand.findMany(),
      reviews: await prisma.review.findMany(),
      messages: await prisma.message.findMany(),
      accounts: await prisma.account.findMany()
    }
    
    const fs = require('fs')
    const path = require('path')
    
    const backupDir = path.join(process.cwd(), 'backups')
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true })
    }
    
    const backupPath = path.join(backupDir, backupFile)
    fs.writeFileSync(backupPath, JSON.stringify(data, null, 2))
    
    console.log(`✅ 备份完成: ${backupPath}`)
    console.log(`📊 备份大小: ${(fs.statSync(backupPath).size / 1024 / 1024).toFixed(2)} MB`)
    
  } catch (error) {
    console.error('❌ 备份失败:', error.message)
  }
}

// 生成测试数据
async function generateTestData() {
  console.log('🧪 生成测试数据...')
  
  try {
    // 创建测试用户
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '测试用户',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.HL9S.',
        depositBalance: 1000,
        creditScore: 750,
        status: 'ACTIVE'
      }
    })
    
    console.log(`✅ 测试用户: ${testUser.email}`)
    
    // 创建测试商品
    const testProduct = await prisma.product.create({
      data: {
        title: '测试商品 - iPhone 15',
        description: '全新未拆封的iPhone 15，颜色随机',
        price: 5999,
        category: 'electronics',
        sellerId: testUser.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        stock: 1
      }
    })
    
    console.log(`✅ 测试商品: ${testProduct.title}`)
    
    // 创建测试需求
    const testDemand = await prisma.demand.create({
      data: {
        title: '求购 MacBook Pro',
        description: '需要一台MacBook Pro，配置不限',
        budget: 8000,
        demandType: 'BUY',
        deliveryMethod: 'delivery',
        expirationTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        userId: testUser.id
      }
    })
    
    console.log(`✅ 测试需求: ${testDemand.title}`)
    
    console.log('\n🎉 测试数据生成完成!')
    console.log('可以使用以下账户登录:')
    console.log('  邮箱: <EMAIL>')
    console.log('  密码: password')
    
  } catch (error) {
    console.error('❌ 生成测试数据失败:', error.message)
  }
}

// 主菜单
async function showMenu() {
  console.log('\n🛠️  数据库管理工具')
  console.log('='.repeat(40))
  console.log('1. 启动 Prisma Studio (可视化管理)')
  console.log('2. 显示数据库信息')
  console.log('3. 备份数据库')
  console.log('4. 生成测试数据')
  console.log('5. 删除所有数据')
  console.log('6. 删除测试数据')
  console.log('7. 退出')
  
  const readline = require('readline')
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  return new Promise((resolve) => {
    rl.question('\n请选择操作 (1-7): ', (answer) => {
      rl.close()
      resolve(answer)
    })
  })
}

// 主函数
async function main() {
  const command = process.argv[2]
  
  if (command) {
    // 命令行模式
    switch (command) {
      case 'studio':
        startPrismaStudio()
        return
        
      case 'info':
        await showDatabaseInfo()
        break
        
      case 'backup':
        await backupDatabase()
        break
        
      case 'test':
        await generateTestData()
        break
        
      case 'clean':
        const { deleteAllData } = require('./quick-delete')
        await deleteAllData()
        break
        
      default:
        console.log('🛠️  数据库管理工具')
        console.log('='.repeat(30))
        console.log('使用方法:')
        console.log('  node database-manager.js studio   # 启动 Prisma Studio')
        console.log('  node database-manager.js info     # 显示数据库信息')
        console.log('  node database-manager.js backup   # 备份数据库')
        console.log('  node database-manager.js test     # 生成测试数据')
        console.log('  node database-manager.js clean    # 清空数据库')
        console.log('')
        console.log('或者直接运行 node database-manager.js 进入交互模式')
    }
  } else {
    // 交互模式
    while (true) {
      const choice = await showMenu()
      
      switch (choice) {
        case '1':
          startPrismaStudio()
          return
          
        case '2':
          await showDatabaseInfo()
          break
          
        case '3':
          await backupDatabase()
          break
          
        case '4':
          await generateTestData()
          break
          
        case '5':
          const { deleteAllData } = require('./quick-delete')
          await deleteAllData()
          break
          
        case '6':
          const { deleteTestData } = require('./quick-delete')
          await deleteTestData()
          break
          
        case '7':
          console.log('👋 再见!')
          return
          
        default:
          console.log('❌ 无效选择，请重试')
      }
      
      // 暂停一下让用户看到结果
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }
  
  await prisma.$disconnect()
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  startPrismaStudio,
  showDatabaseInfo,
  backupDatabase,
  generateTestData
}
