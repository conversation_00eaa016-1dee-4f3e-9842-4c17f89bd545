const fs = require('fs');
const path = require('path');

// 检查订单状态显示逻辑
const filePath = 'app/orders/[id]/page.tsx';

console.log('=== 检查订单状态显示逻辑 ===\n');

try {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    const lines = content.split('\n');
    
    console.log(`检查文件: ${filePath}\n`);
    
    // 检查完善订单信息的显示条件
    const orderInfoConditions = [];
    lines.forEach((line, index) => {
      if (line.includes('完善订单信息') || line.includes('编辑订单信息')) {
        // 查找前面几行的条件判断
        for (let i = Math.max(0, index - 10); i <= index; i++) {
          if (lines[i].includes('order.status') && (lines[i].includes('DRAFT') || lines[i].includes('PENDING_PAYMENT'))) {
            orderInfoConditions.push({
              lineNumber: i + 1,
              condition: lines[i].trim(),
              content: line.trim()
            });
            break;
          }
        }
      }
    });
    
    // 检查订单支付的显示条件
    const paymentConditions = [];
    lines.forEach((line, index) => {
      if (line.includes('订单支付') && line.includes('h4')) {
        // 查找前面几行的条件判断
        for (let i = Math.max(0, index - 5); i <= index; i++) {
          if (lines[i].includes('order.status') && lines[i].includes('PENDING_PAYMENT')) {
            paymentConditions.push({
              lineNumber: i + 1,
              condition: lines[i].trim(),
              content: line.trim()
            });
            break;
          }
        }
      }
    });
    
    console.log('📋 完善订单信息显示条件:');
    if (orderInfoConditions.length > 0) {
      orderInfoConditions.forEach(({ lineNumber, condition, content }) => {
        console.log(`   第${lineNumber}行: ${condition}`);
        const isDraftOnly = condition.includes('DRAFT') && !condition.includes('PENDING_PAYMENT');
        const status = isDraftOnly ? '✅ 正确 (仅DRAFT状态)' : '❌ 错误 (包含其他状态)';
        console.log(`   ${status}`);
      });
    } else {
      console.log('   未找到相关条件');
    }
    
    console.log('\n💳 订单支付显示条件:');
    if (paymentConditions.length > 0) {
      paymentConditions.forEach(({ lineNumber, condition, content }) => {
        console.log(`   第${lineNumber}行: ${condition}`);
        const isPendingPaymentOnly = condition.includes('PENDING_PAYMENT') && !condition.includes('PAID');
        const status = isPendingPaymentOnly ? '✅ 正确 (仅PENDING_PAYMENT状态)' : '❌ 错误 (包含其他状态)';
        console.log(`   ${status}`);
      });
    } else {
      console.log('   未找到相关条件');
    }
    
    console.log('\n=== 预期行为 ===');
    console.log('📊 各状态下的显示情况:');
    console.log('');
    console.log('DRAFT 状态:');
    console.log('  ✅ 显示: 完善订单信息');
    console.log('  ❌ 不显示: 订单支付');
    console.log('');
    console.log('PENDING_PAYMENT 状态:');
    console.log('  ❌ 不显示: 完善订单信息');
    console.log('  ✅ 显示: 订单支付');
    console.log('');
    console.log('PAID 及后续状态:');
    console.log('  ❌ 不显示: 完善订单信息');
    console.log('  ❌ 不显示: 订单支付');
    console.log('  ✅ 显示: 支付状态/发货状态等');
    
  } else {
    console.log(`⚠️  文件不存在: ${filePath}`);
  }
} catch (error) {
  console.log(`❌ 检查文件失败: ${filePath} - ${error.message}`);
}

console.log('\n=== 检查完成 ===');
