#!/usr/bin/env node

/**
 * BitMarket 清理测试商品脚本
 * 删除演示和测试商品数据
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 清理测试商品
async function cleanTestProducts() {
  log('🗑️ 开始清理测试商品...', 'cyan');
  
  try {
    // 获取测试用户
    const testUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });

    if (testUsers.length === 0) {
      log('ℹ️ 未找到测试用户，跳过清理', 'yellow');
      return;
    }

    const testUserIds = testUsers.map(user => user.id);
    
    // 获取要删除的商品
    const productsToDelete = await prisma.product.findMany({
      where: {
        sellerId: {
          in: testUserIds
        }
      },
      select: {
        id: true,
        title: true,
        seller: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (productsToDelete.length === 0) {
      log('ℹ️ 未找到需要清理的测试商品', 'yellow');
      return;
    }

    log(`📋 找到 ${productsToDelete.length} 个测试商品需要清理:`, 'blue');
    productsToDelete.forEach(product => {
      log(`  - ${product.title} (卖家: ${product.seller.name})`, 'reset');
    });

    // 确认删除
    log('\n⚠️ 即将删除以上商品，包括相关的订单、评价、收藏等数据', 'yellow');
    
    // 在事务中删除相关数据
    const result = await prisma.$transaction(async (tx) => {
      const productIds = productsToDelete.map(p => p.id);
      
      // 删除商品相关的所有数据
      const deletedFavorites = await tx.favorite.deleteMany({
        where: { productId: { in: productIds } }
      });
      
      const deletedReviews = await tx.review.deleteMany({
        where: { productId: { in: productIds } }
      });
      
      const deletedOrderItems = await tx.orderItem.deleteMany({
        where: { productId: { in: productIds } }
      });
      
      const deletedVariantAttributes = await tx.variantAttribute.deleteMany({
        where: {
          variant: {
            productId: { in: productIds }
          }
        }
      });
      
      const deletedVariants = await tx.productVariant.deleteMany({
        where: { productId: { in: productIds } }
      });
      
      const deletedProducts = await tx.product.deleteMany({
        where: { id: { in: productIds } }
      });

      return {
        products: deletedProducts.count,
        favorites: deletedFavorites.count,
        reviews: deletedReviews.count,
        orderItems: deletedOrderItems.count,
        variants: deletedVariants.count,
        variantAttributes: deletedVariantAttributes.count
      };
    });

    log('\n✅ 清理完成！删除统计:', 'green');
    log(`  商品: ${result.products} 个`, 'reset');
    log(`  收藏: ${result.favorites} 个`, 'reset');
    log(`  评价: ${result.reviews} 个`, 'reset');
    log(`  订单项: ${result.orderItems} 个`, 'reset');
    log(`  商品变体: ${result.variants} 个`, 'reset');
    log(`  变体属性: ${result.variantAttributes} 个`, 'reset');

  } catch (error) {
    log(`❌ 清理失败: ${error.message}`, 'red');
    throw error;
  }
}

// 清理空的订单
async function cleanEmptyOrders() {
  log('\n🧹 清理空订单...', 'cyan');
  
  try {
    // 查找没有订单项的订单
    const emptyOrders = await prisma.order.findMany({
      where: {
        orderItems: {
          none: {}
        }
      },
      select: {
        id: true,
        orderNumber: true
      }
    });

    if (emptyOrders.length > 0) {
      const deletedOrders = await prisma.order.deleteMany({
        where: {
          id: {
            in: emptyOrders.map(order => order.id)
          }
        }
      });

      log(`✅ 删除了 ${deletedOrders.count} 个空订单`, 'green');
    } else {
      log('ℹ️ 未找到空订单', 'yellow');
    }

  } catch (error) {
    log(`❌ 清理空订单失败: ${error.message}`, 'red');
  }
}

// 显示清理后的统计
async function showCleanupStats() {
  log('\n📊 清理后的数据统计:', 'cyan');
  
  try {
    const productCount = await prisma.product.count();
    const userCount = await prisma.user.count();
    const orderCount = await prisma.order.count();
    
    log(`👥 用户总数: ${userCount}`, 'blue');
    log(`🛍️ 商品总数: ${productCount}`, 'blue');
    log(`📦 订单总数: ${orderCount}`, 'blue');

    if (productCount === 0) {
      log('\n✨ 数据库已清理干净，可以开始使用新的功能！', 'green');
    }

  } catch (error) {
    log(`❌ 获取统计数据失败: ${error.message}`, 'red');
  }
}

// 主函数
async function main() {
  try {
    log('🧹 开始 BitMarket 测试数据清理...', 'cyan');
    log('', 'reset');

    // 1. 清理测试商品
    await cleanTestProducts();

    // 2. 清理空订单
    await cleanEmptyOrders();

    // 3. 显示清理后统计
    await showCleanupStats();

    log('\n🎉 数据清理完成！', 'green');
    log('', 'reset');
    log('💡 提示:', 'yellow');
    log('  - 测试商品已全部删除', 'reset');
    log('  - 测试用户账号保留，可继续使用', 'reset');
    log('  - 数据库结构完整，功能正常', 'reset');

  } catch (error) {
    log('❌ 清理脚本执行失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main, cleanTestProducts, cleanEmptyOrders };
