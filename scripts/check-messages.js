const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkMessages() {
  try {
    console.log('📨 检查聊天消息...')
    
    // 获取所有消息
    const messages = await prisma.message.findMany({
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        receiver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        order: {
          select: {
            id: true,
            orderNumber: true,
            product: {
              select: {
                title: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`✅ 找到 ${messages.length} 条消息`)
    console.log('')

    messages.forEach((message, index) => {
      console.log(`${index + 1}. 消息ID: ${message.id}`)
      console.log(`   类型: ${message.messageType}`)
      console.log(`   内容: ${message.content || '(文件消息)'}`)
      console.log(`   发送者: ${message.sender.name || message.sender.email}`)
      console.log(`   接收者: ${message.receiver.name || message.receiver.email}`)
      console.log(`   订单: ${message.order.orderNumber} - ${message.order.product.title}`)
      console.log(`   状态: ${message.status}`)
      console.log(`   时间: ${message.createdAt}`)
      
      if (message.fileUrl) {
        console.log(`   📎 文件信息:`)
        console.log(`      URL: ${message.fileUrl}`)
        console.log(`      文件名: ${message.fileName}`)
        console.log(`      大小: ${message.fileSize} bytes`)
        console.log(`      类型: ${message.fileMimeType}`)
        if (message.fileMetadata) {
          console.log(`      元数据: ${JSON.stringify(message.fileMetadata)}`)
        }
      }
      console.log('')
    })

    // 统计不同类型的消息
    const messageStats = messages.reduce((stats, msg) => {
      stats[msg.messageType] = (stats[msg.messageType] || 0) + 1
      return stats
    }, {})

    console.log('📊 消息类型统计:')
    Object.entries(messageStats).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} 条`)
    })

    // 检查文件消息
    const fileMessages = messages.filter(msg => msg.fileUrl)
    console.log(`\n📎 文件消息: ${fileMessages.length} 条`)
    
    if (fileMessages.length > 0) {
      console.log('文件消息详情:')
      fileMessages.forEach((msg, index) => {
        console.log(`   ${index + 1}. ${msg.messageType} - ${msg.fileName}`)
        console.log(`      大小: ${(msg.fileSize / 1024).toFixed(2)} KB`)
        console.log(`      路径: ${msg.fileUrl}`)
      })
    }

  } catch (error) {
    console.error('❌ 检查消息失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkMessages()
