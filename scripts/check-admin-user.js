const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkAdminUser() {
  try {
    console.log('🔍 检查管理员用户...')

    // 查找管理员用户
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (adminUser) {
      console.log('✅ 找到管理员用户:')
      console.log(`   ID: ${adminUser.id}`)
      console.log(`   邮箱: ${adminUser.email}`)
      console.log(`   姓名: ${adminUser.name}`)
      console.log(`   角色: ${adminUser.role}`)
      console.log(`   状态: ${adminUser.status}`)
      console.log(`   创建时间: ${adminUser.createdAt}`)
    } else {
      console.log('❌ 未找到管理员用户')
      
      // 查找所有用户
      const allUsers = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          status: true
        }
      })
      
      console.log('\n📋 所有用户列表:')
      allUsers.forEach(user => {
        console.log(`   ${user.email} - ${user.name} - ${user.role} - ${user.status}`)
      })
    }

    // 检查是否有其他管理员
    const allAdmins = await prisma.user.findMany({
      where: { role: 'ADMIN' }
    })

    console.log(`\n👥 管理员总数: ${allAdmins.length}`)
    allAdmins.forEach(admin => {
      console.log(`   ${admin.email} - ${admin.name}`)
    })

  } catch (error) {
    console.error('❌ 检查失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

checkAdminUser()
