#!/usr/bin/env node

const { performance } = require('perf_hooks')

class MemoryAnalyzer {
  constructor() {
    this.snapshots = []
    this.baseline = null
  }

  // 获取详细的内存使用情况
  getMemoryUsage() {
    const usage = process.memoryUsage()
    return {
      timestamp: Date.now(),
      rss: usage.rss, // 常驻集大小
      heapTotal: usage.heapTotal, // 堆总大小
      heapUsed: usage.heapUsed, // 已使用堆大小
      external: usage.external, // 外部内存
      arrayBuffers: usage.arrayBuffers || 0, // ArrayBuffer内存
      heapUsagePercent: (usage.heapUsed / usage.heapTotal) * 100,
      totalMemoryMB: usage.rss / 1024 / 1024,
      heapUsedMB: usage.heapUsed / 1024 / 1024,
      heapTotalMB: usage.heapTotal / 1024 / 1024,
      externalMB: usage.external / 1024 / 1024
    }
  }

  // 创建内存快照
  takeSnapshot(label = 'snapshot') {
    const snapshot = {
      label,
      ...this.getMemoryUsage()
    }
    this.snapshots.push(snapshot)
    return snapshot
  }

  // 设置基线
  setBaseline(label = 'baseline') {
    this.baseline = this.takeSnapshot(label)
    return this.baseline
  }

  // 比较内存使用
  compareWithBaseline(currentSnapshot = null) {
    if (!this.baseline) {
      throw new Error('No baseline set. Call setBaseline() first.')
    }

    const current = currentSnapshot || this.getMemoryUsage()
    
    return {
      rssDelta: current.rss - this.baseline.rss,
      heapTotalDelta: current.heapTotal - this.baseline.heapTotal,
      heapUsedDelta: current.heapUsed - this.baseline.heapUsed,
      externalDelta: current.external - this.baseline.external,
      rssDeltaMB: (current.rss - this.baseline.rss) / 1024 / 1024,
      heapUsedDeltaMB: (current.heapUsed - this.baseline.heapUsed) / 1024 / 1024,
      heapTotalDeltaMB: (current.heapTotal - this.baseline.heapTotal) / 1024 / 1024,
      externalDeltaMB: (current.external - this.baseline.external) / 1024 / 1024,
      baseline: this.baseline,
      current
    }
  }

  // 强制垃圾回收（如果可用）
  forceGC() {
    if (global.gc) {
      global.gc()
      return true
    } else {
      console.warn('垃圾回收不可用。请使用 --expose-gc 标志启动 Node.js')
      return false
    }
  }

  // 内存泄漏检测
  detectMemoryLeak(threshold = 50) { // 50MB阈值
    if (this.snapshots.length < 2) {
      return { hasLeak: false, message: '需要至少2个快照来检测内存泄漏' }
    }

    const recent = this.snapshots.slice(-5) // 最近5个快照
    const growthTrend = []

    for (let i = 1; i < recent.length; i++) {
      const growth = recent[i].heapUsed - recent[i-1].heapUsed
      growthTrend.push(growth)
    }

    const avgGrowth = growthTrend.reduce((a, b) => a + b, 0) / growthTrend.length
    const avgGrowthMB = avgGrowth / 1024 / 1024

    return {
      hasLeak: avgGrowthMB > threshold,
      avgGrowthMB,
      threshold,
      message: avgGrowthMB > threshold 
        ? `检测到潜在内存泄漏：平均增长 ${avgGrowthMB.toFixed(2)}MB`
        : `内存使用正常：平均增长 ${avgGrowthMB.toFixed(2)}MB`
    }
  }

  // 生成内存报告
  generateReport() {
    const current = this.getMemoryUsage()
    const report = {
      timestamp: new Date().toISOString(),
      current,
      snapshots: this.snapshots,
      analysis: {
        totalSnapshots: this.snapshots.length,
        memoryLeak: this.detectMemoryLeak(),
        recommendations: this.getRecommendations(current)
      }
    }

    if (this.baseline) {
      report.comparison = this.compareWithBaseline(current)
    }

    return report
  }

  // 获取优化建议
  getRecommendations(usage) {
    const recommendations = []

    // 堆使用率过高
    if (usage.heapUsagePercent > 80) {
      recommendations.push({
        type: 'critical',
        message: `堆使用率过高 (${usage.heapUsagePercent.toFixed(1)}%)，建议立即优化`,
        actions: ['减少对象创建', '清理未使用的引用', '强制垃圾回收']
      })
    } else if (usage.heapUsagePercent > 60) {
      recommendations.push({
        type: 'warning',
        message: `堆使用率较高 (${usage.heapUsagePercent.toFixed(1)}%)，建议优化`,
        actions: ['检查大对象', '优化数据结构', '实现对象池']
      })
    }

    // 总内存使用过高
    if (usage.totalMemoryMB > 500) {
      recommendations.push({
        type: 'critical',
        message: `总内存使用过高 (${usage.totalMemoryMB.toFixed(1)}MB)`,
        actions: ['检查内存泄漏', '优化数据缓存', '分批处理数据']
      })
    } else if (usage.totalMemoryMB > 200) {
      recommendations.push({
        type: 'warning',
        message: `总内存使用较高 (${usage.totalMemoryMB.toFixed(1)}MB)`,
        actions: ['监控内存增长', '优化数据结构', '清理临时数据']
      })
    }

    // 外部内存过高
    if (usage.externalMB > 100) {
      recommendations.push({
        type: 'warning',
        message: `外部内存使用较高 (${usage.externalMB.toFixed(1)}MB)`,
        actions: ['检查Buffer使用', '优化文件操作', '清理外部资源']
      })
    }

    return recommendations
  }

  // 打印内存使用情况
  printMemoryUsage(usage = null) {
    const mem = usage || this.getMemoryUsage()
    
    console.log('\n📊 内存使用情况:')
    console.log('─'.repeat(50))
    console.log(`🔹 RSS (常驻集): ${mem.totalMemoryMB.toFixed(2)}MB`)
    console.log(`🔹 堆总大小: ${mem.heapTotalMB.toFixed(2)}MB`)
    console.log(`🔹 堆已使用: ${mem.heapUsedMB.toFixed(2)}MB (${mem.heapUsagePercent.toFixed(1)}%)`)
    console.log(`🔹 外部内存: ${mem.externalMB.toFixed(2)}MB`)
    
    if (mem.arrayBuffers > 0) {
      console.log(`🔹 ArrayBuffers: ${(mem.arrayBuffers / 1024 / 1024).toFixed(2)}MB`)
    }
  }

  // 清理快照历史
  clearSnapshots() {
    this.snapshots = []
    this.baseline = null
  }
}

// 内存优化工具
class MemoryOptimizer {
  constructor() {
    this.analyzer = new MemoryAnalyzer()
  }

  // 运行内存优化分析
  async runOptimizationAnalysis() {
    console.log('🔍 开始内存优化分析...')
    console.log('='.repeat(50))

    // 设置基线
    this.analyzer.setBaseline('启动基线')
    this.analyzer.printMemoryUsage()

    // 模拟一些内存使用场景
    await this.simulateMemoryUsage()

    // 生成报告
    const report = this.analyzer.generateReport()
    this.printOptimizationReport(report)

    return report
  }

  // 模拟内存使用场景
  async simulateMemoryUsage() {
    console.log('\n🧪 模拟内存使用场景...')

    // 场景1: 大量小对象创建
    console.log('\n📦 场景1: 创建大量小对象')
    const objects = []
    for (let i = 0; i < 100000; i++) {
      objects.push({
        id: i,
        name: `object-${i}`,
        data: new Array(10).fill(i),
        timestamp: Date.now()
      })
    }
    this.analyzer.takeSnapshot('大量小对象')
    this.analyzer.printMemoryUsage()

    // 场景2: 大对象创建
    console.log('\n📦 场景2: 创建大对象')
    const largeArrays = []
    for (let i = 0; i < 10; i++) {
      largeArrays.push(new Array(1000000).fill(i))
    }
    this.analyzer.takeSnapshot('大对象')
    this.analyzer.printMemoryUsage()

    // 场景3: 字符串操作
    console.log('\n📦 场景3: 大量字符串操作')
    let largeString = ''
    for (let i = 0; i < 100000; i++) {
      largeString += `这是第${i}个字符串片段，包含一些中文内容。`
    }
    this.analyzer.takeSnapshot('字符串操作')
    this.analyzer.printMemoryUsage()

    // 尝试垃圾回收
    console.log('\n🗑️  尝试垃圾回收...')
    if (this.analyzer.forceGC()) {
      await new Promise(resolve => setTimeout(resolve, 100))
      this.analyzer.takeSnapshot('垃圾回收后')
      this.analyzer.printMemoryUsage()
    }

    // 清理引用
    console.log('\n🧹 清理对象引用...')
    objects.length = 0
    largeArrays.length = 0
    largeString = null
    
    if (this.analyzer.forceGC()) {
      await new Promise(resolve => setTimeout(resolve, 100))
      this.analyzer.takeSnapshot('清理后')
      this.analyzer.printMemoryUsage()
    }
  }

  // 打印优化报告
  printOptimizationReport(report) {
    console.log('\n📊 内存优化分析报告')
    console.log('='.repeat(50))

    // 当前状态
    const current = report.current
    console.log(`\n🔍 当前内存状态:`)
    console.log(`   总内存: ${current.totalMemoryMB.toFixed(2)}MB`)
    console.log(`   堆使用: ${current.heapUsedMB.toFixed(2)}MB / ${current.heapTotalMB.toFixed(2)}MB (${current.heapUsagePercent.toFixed(1)}%)`)
    console.log(`   外部内存: ${current.externalMB.toFixed(2)}MB`)

    // 与基线对比
    if (report.comparison) {
      const comp = report.comparison
      console.log(`\n📈 与基线对比:`)
      console.log(`   总内存变化: ${comp.rssDeltaMB > 0 ? '+' : ''}${comp.rssDeltaMB.toFixed(2)}MB`)
      console.log(`   堆使用变化: ${comp.heapUsedDeltaMB > 0 ? '+' : ''}${comp.heapUsedDeltaMB.toFixed(2)}MB`)
      console.log(`   外部内存变化: ${comp.externalDeltaMB > 0 ? '+' : ''}${comp.externalDeltaMB.toFixed(2)}MB`)
    }

    // 内存泄漏检测
    const leak = report.analysis.memoryLeak
    console.log(`\n🔍 内存泄漏检测:`)
    console.log(`   ${leak.hasLeak ? '⚠️' : '✅'} ${leak.message}`)

    // 优化建议
    const recommendations = report.analysis.recommendations
    if (recommendations.length > 0) {
      console.log(`\n💡 优化建议:`)
      recommendations.forEach((rec, index) => {
        const icon = rec.type === 'critical' ? '🚨' : '⚠️'
        console.log(`   ${icon} ${rec.message}`)
        rec.actions.forEach(action => {
          console.log(`      - ${action}`)
        })
      })
    } else {
      console.log(`\n✅ 内存使用正常，无需特殊优化`)
    }
  }
}

// 运行内存分析
async function runMemoryAnalysis() {
  const optimizer = new MemoryOptimizer()
  
  try {
    const report = await optimizer.runOptimizationAnalysis()
    
    // 保存报告
    const fs = require('fs').promises
    await fs.mkdir('test-results/memory', { recursive: true })
    await fs.writeFile(
      'test-results/memory/memory-analysis-report.json',
      JSON.stringify(report, null, 2)
    )
    
    console.log('\n💾 内存分析报告已保存: test-results/memory/memory-analysis-report.json')
    
    return report
  } catch (error) {
    console.error('❌ 内存分析失败:', error)
    throw error
  }
}

// 命令行运行
if (require.main === module) {
  runMemoryAnalysis()
    .then(() => {
      console.log('\n✅ 内存分析完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 内存分析失败:', error)
      process.exit(1)
    })
}

module.exports = { MemoryAnalyzer, MemoryOptimizer }
