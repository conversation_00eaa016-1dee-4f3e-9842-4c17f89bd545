const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixMediatorBalance() {
  try {
    console.log('=== 修复中间人保证金余额问题 ===\n');
    
    // 1. 查询所有中间人
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        depositBalance: true,
        frozenBalance: true,
        availableBalance: true,
        mediatorDeposit: true
      }
    });
    
    console.log(`找到 ${mediators.length} 个中间人需要检查`);
    
    for (const mediator of mediators) {
      console.log(`\n处理中间人: ${mediator.name}`);
      console.log(`  当前状态:`);
      console.log(`    depositBalance: ${mediator.depositBalance}`);
      console.log(`    frozenBalance: ${mediator.frozenBalance}`);
      console.log(`    availableBalance: ${mediator.availableBalance}`);
      console.log(`    mediatorDeposit: ${mediator.mediatorDeposit}`);
      
      // 计算正确的可用余额
      const correctAvailableBalance = Math.max(0, mediator.depositBalance - mediator.frozenBalance);
      
      console.log(`  计算的正确可用余额: ${correctAvailableBalance}`);
      
      // 如果可用余额不正确，则修复
      if (Math.abs(mediator.availableBalance - correctAvailableBalance) > 0.01) {
        console.log(`  ❌ 可用余额不正确，需要修复`);
        
        await prisma.user.update({
          where: { id: mediator.id },
          data: {
            availableBalance: correctAvailableBalance
          }
        });
        
        console.log(`  ✅ 已修复可用余额: ${mediator.availableBalance} -> ${correctAvailableBalance}`);
      } else {
        console.log(`  ✅ 可用余额正确，无需修复`);
      }
      
      // 如果 mediatorDeposit 为 0 但 depositBalance 有值，同步 mediatorDeposit
      if (mediator.mediatorDeposit === 0 && mediator.depositBalance > 0) {
        console.log(`  ❌ mediatorDeposit 为 0 但 depositBalance 有值，需要同步`);
        
        await prisma.user.update({
          where: { id: mediator.id },
          data: {
            mediatorDeposit: mediator.depositBalance
          }
        });
        
        console.log(`  ✅ 已同步 mediatorDeposit: 0 -> ${mediator.depositBalance}`);
      }
    }
    
    console.log('\n=== 验证修复结果 ===');
    
    // 重新查询验证
    const updatedMediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        depositBalance: true,
        frozenBalance: true,
        availableBalance: true,
        mediatorDeposit: true,
        mediatorStatus: true
      }
    });
    
    console.log('\n修复后的中间人状态:');
    updatedMediators.forEach(m => {
      const expectedAvailable = Math.max(0, m.depositBalance - m.frozenBalance);
      const isCorrect = Math.abs(m.availableBalance - expectedAvailable) < 0.01;
      
      console.log(`${m.name}:`);
      console.log(`  状态: ${m.mediatorStatus}`);
      console.log(`  depositBalance: ${m.depositBalance}`);
      console.log(`  frozenBalance: ${m.frozenBalance}`);
      console.log(`  availableBalance: ${m.availableBalance} ${isCorrect ? '✅' : '❌'}`);
      console.log(`  mediatorDeposit: ${m.mediatorDeposit}`);
      console.log('');
    });
    
    // 测试自动分配逻辑
    console.log('=== 测试自动分配逻辑 ===');
    const testOrderAmount = 1000;
    
    const activeMediators = updatedMediators.filter(m => m.mediatorStatus === 'ACTIVE');
    console.log(`活跃中间人数量: ${activeMediators.length}`);
    
    const qualifiedMediators = activeMediators.filter(m => m.availableBalance >= testOrderAmount);
    console.log(`保证金充足的中间人数量 (>= ${testOrderAmount} USDT): ${qualifiedMediators.length}`);
    
    if (qualifiedMediators.length > 0) {
      console.log('✅ 修复成功！现在有保证金充足的中间人可用');
      qualifiedMediators.forEach(m => {
        console.log(`  - ${m.name}: 可用余额 ${m.availableBalance} USDT`);
      });
    } else {
      console.log('❌ 仍然没有保证金充足的中间人');
      
      // 分析原因
      if (activeMediators.length === 0) {
        console.log('  原因: 没有活跃状态的中间人');
      } else {
        console.log('  原因: 活跃中间人的可用余额都不足');
        activeMediators.forEach(m => {
          console.log(`    - ${m.name}: 可用余额 ${m.availableBalance} USDT (需要 ${testOrderAmount} USDT)`);
        });
      }
    }
    
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixMediatorBalance();
