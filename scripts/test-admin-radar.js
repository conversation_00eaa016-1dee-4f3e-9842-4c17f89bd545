const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testAdminRadar() {
  console.log('🔍 测试管理员反馈系统...\n')

  try {
    // 1. 检查管理员账号
    console.log('1. 检查管理员账号...')
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true
      }
    })

    if (!admin) {
      console.log('❌ 没有找到管理员账号')
      return
    }

    console.log(`✅ 找到管理员: ${admin.name} (${admin.email})`)

    // 2. 检查反馈数据
    console.log('\n2. 检查反馈数据...')
    const feedbackCount = await prisma.userFeedback.count()
    console.log(`✅ 反馈总数: ${feedbackCount}`)

    if (feedbackCount === 0) {
      console.log('⚠️  没有反馈数据，创建测试数据...')
      // 这里可以创建测试数据的代码
    }

    // 3. 测试反馈统计
    console.log('\n3. 测试反馈统计...')
    const stats = await getAdminFeedbackStats()
    console.log('✅ 统计数据:')
    console.log(`   - 总反馈: ${stats.total}`)
    console.log(`   - 待处理: ${stats.pending}`)
    console.log(`   - 处理中: ${stats.inProgress}`)
    console.log(`   - 已解决: ${stats.resolved}`)
    console.log(`   - 解决率: ${stats.resolutionRate}%`)

    // 4. 测试反馈列表
    console.log('\n4. 测试反馈列表...')
    const feedbacks = await getAdminFeedbackList()
    console.log(`✅ 获取到 ${feedbacks.length} 条反馈`)
    
    feedbacks.slice(0, 3).forEach((feedback, index) => {
      console.log(`   ${index + 1}. ${feedback.title} - ${feedback.status} (${feedback.priority})`)
      console.log(`      用户: ${feedback.user.name}`)
      console.log(`      时间: ${feedback.createdAt.toLocaleString('zh-CN')}`)
    })

    // 5. 测试分类统计
    console.log('\n5. 测试分类统计...')
    const categoryStats = await prisma.userFeedback.groupBy({
      by: ['category'],
      _count: { category: true }
    })

    console.log('✅ 分类统计:')
    categoryStats.forEach(stat => {
      const categoryNames = {
        'BUG_REPORT': '错误报告',
        'IMPROVEMENT': '改进建议',
        'APPEAL': '申诉'
      }
      console.log(`   - ${categoryNames[stat.category] || stat.category}: ${stat._count.category}`)
    })

    // 6. 测试状态统计
    console.log('\n6. 测试状态统计...')
    const statusStats = await prisma.userFeedback.groupBy({
      by: ['status'],
      _count: { status: true }
    })

    console.log('✅ 状态统计:')
    statusStats.forEach(stat => {
      const statusNames = {
        'PENDING': '待处理',
        'IN_PROGRESS': '处理中',
        'RESOLVED': '已解决'
      }
      console.log(`   - ${statusNames[stat.status] || stat.status}: ${stat._count.status}`)
    })

    console.log('\n🎉 管理员反馈系统测试完成！')
    console.log('\n📝 访问信息:')
    console.log('   管理员反馈页面: http://localhost:3000/admin/radar')
    console.log('   登录页面: http://localhost:3000/auth/signin')
    console.log(`   管理员邮箱: ${admin.email}`)

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 获取管理员反馈统计
async function getAdminFeedbackStats() {
  const [
    total,
    pending,
    inProgress,
    resolved
  ] = await Promise.all([
    prisma.userFeedback.count(),
    prisma.userFeedback.count({ where: { status: 'PENDING' } }),
    prisma.userFeedback.count({ where: { status: 'IN_PROGRESS' } }),
    prisma.userFeedback.count({ where: { status: 'RESOLVED' } })
  ])

  const resolutionRate = total > 0 ? Math.round((resolved / total) * 100) : 0

  return {
    total,
    pending,
    inProgress,
    resolved,
    resolutionRate
  }
}

// 获取管理员反馈列表
async function getAdminFeedbackList() {
  return await prisma.userFeedback.findMany({
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          userId: true
        }
      },
      assignedTo: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: [
      { priority: 'desc' },
      { createdAt: 'desc' }
    ],
    take: 10
  })
}

// 运行测试
if (require.main === module) {
  testAdminRadar().catch(console.error)
}

module.exports = { testAdminRadar }
