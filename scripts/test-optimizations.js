#!/usr/bin/env node

/**
 * BitMarket 优化功能测试脚本
 * 验证所有优化功能是否正常工作
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试文件是否存在
function testFileExists(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    log(`✅ ${description}: 文件存在`, 'green');
    return true;
  } else {
    log(`❌ ${description}: 文件不存在 (${filePath})`, 'red');
    return false;
  }
}

// 测试文件内容
function testFileContent(filePath, description, testFn) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    const result = testFn(content);
    
    if (result.success) {
      log(`✅ ${description}: ${result.message}`, 'green');
      return true;
    } else {
      log(`❌ ${description}: ${result.message}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ ${description}: 读取文件失败 - ${error.message}`, 'red');
    return false;
  }
}

// 测试增强的商品常量
function testEnhancedProductConstants() {
  log('\n📱 测试增强的商品分类系统...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  // 测试文件存在
  total++;
  if (testFileExists('lib/product-constants-enhanced.ts', '增强商品常量文件')) {
    passed++;
  }
  
  // 测试内容
  total++;
  if (testFileContent('lib/product-constants-enhanced.ts', '商品分类枚举', (content) => {
    const hasMainCategories = content.includes('export enum ProductCategory');
    const hasSubcategories = content.includes('export enum ProductSubcategory');
    const hasElectronics = content.includes('ELECTRONICS = \'ELECTRONICS\'');
    const hasMobilePhone = content.includes('MOBILE_PHONE = \'MOBILE_PHONE\'');
    
    if (hasMainCategories && hasSubcategories && hasElectronics && hasMobilePhone) {
      return { success: true, message: '包含完整的分类枚举定义' };
    } else {
      return { success: false, message: '分类枚举定义不完整' };
    }
  })) {
    passed++;
  }
  
  total++;
  if (testFileContent('lib/product-constants-enhanced.ts', '分类选项配置', (content) => {
    const hasCategoryOptions = content.includes('export const categoryOptions');
    const hasSubcategories = content.includes('subcategories:');
    const hasIcons = content.includes('icon:');
    const hasKeywords = content.includes('keywords:');
    
    if (hasCategoryOptions && hasSubcategories && hasIcons && hasKeywords) {
      return { success: true, message: '包含完整的分类选项配置' };
    } else {
      return { success: false, message: '分类选项配置不完整' };
    }
  })) {
    passed++;
  }
  
  log(`📊 商品分类系统测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 测试中国城市数据
function testChinaLocationData() {
  log('\n🗺️ 测试中国城市区县数据...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  // 测试基础数据文件
  total++;
  if (testFileExists('lib/china-location-data.ts', '基础城市数据文件')) {
    passed++;
  }
  
  // 测试完整数据文件
  total++;
  if (testFileExists('lib/china-location-complete.ts', '完整城市数据文件')) {
    passed++;
  }
  
  // 测试数据结构
  total++;
  if (testFileContent('lib/china-location-complete.ts', '城市数据结构', (content) => {
    const hasProvinceInterface = content.includes('interface Province');
    const hasCityInterface = content.includes('interface City');
    const hasDistrictInterface = content.includes('interface District');
    const hasLocationData = content.includes('export const chinaLocationData');
    
    if (hasProvinceInterface && hasCityInterface && hasDistrictInterface && hasLocationData) {
      return { success: true, message: '包含完整的数据结构定义' };
    } else {
      return { success: false, message: '数据结构定义不完整' };
    }
  })) {
    passed++;
  }
  
  // 测试功能函数
  total++;
  if (testFileContent('lib/china-location-complete.ts', '功能函数', (content) => {
    const hasGetAllProvinces = content.includes('export const getAllProvinces');
    const hasGetAllCities = content.includes('export const getAllCities');
    const hasSearchCities = content.includes('export const searchCities');
    const hasValidateLocation = content.includes('export const validateLocation');
    
    if (hasGetAllProvinces && hasGetAllCities && hasSearchCities && hasValidateLocation) {
      return { success: true, message: '包含完整的功能函数' };
    } else {
      return { success: false, message: '功能函数不完整' };
    }
  })) {
    passed++;
  }
  
  log(`📊 城市数据系统测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 测试增强组件
function testEnhancedComponents() {
  log('\n🧩 测试增强组件...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  // 测试增强分类选择器
  total++;
  if (testFileExists('components/product/EnhancedCategorySelector.tsx', '增强分类选择器')) {
    passed++;
  }
  
  // 测试增强地理位置选择器
  total++;
  if (testFileExists('components/location/EnhancedLocationSelector.tsx', '增强地理位置选择器')) {
    passed++;
  }
  
  // 测试组件功能
  total++;
  if (testFileContent('components/product/EnhancedCategorySelector.tsx', '分类选择器功能', (content) => {
    const hasMainCategory = content.includes('selectedCategory');
    const hasSubcategory = content.includes('selectedSubcategory');
    const hasSearch = content.includes('searchQuery');
    const hasLayouts = content.includes('layout');
    
    if (hasMainCategory && hasSubcategory && hasSearch && hasLayouts) {
      return { success: true, message: '包含完整的选择器功能' };
    } else {
      return { success: false, message: '选择器功能不完整' };
    }
  })) {
    passed++;
  }
  
  total++;
  if (testFileContent('components/location/EnhancedLocationSelector.tsx', '地理位置选择器功能', (content) => {
    const hasProvince = content.includes('selectedProvince');
    const hasCity = content.includes('selectedCity');
    const hasDistrict = content.includes('selectedDistrict');
    const hasSearch = content.includes('searchQuery');
    
    if (hasProvince && hasCity && hasDistrict && hasSearch) {
      return { success: true, message: '包含完整的地理位置功能' };
    } else {
      return { success: false, message: '地理位置功能不完整' };
    }
  })) {
    passed++;
  }
  
  log(`📊 增强组件测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 测试脚本文件
function testScripts() {
  log('\n📜 测试脚本文件...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  // 测试清理脚本
  total++;
  if (testFileExists('scripts/clean-test-products.js', '测试数据清理脚本')) {
    passed++;
  }
  
  // 测试城市数据生成脚本
  total++;
  if (testFileExists('scripts/fetch-china-cities.js', '城市数据生成脚本')) {
    passed++;
  }
  
  // 测试本测试脚本
  total++;
  if (testFileExists('scripts/test-optimizations.js', '优化测试脚本')) {
    passed++;
  }
  
  log(`📊 脚本文件测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 测试文档文件
function testDocumentation() {
  log('\n📚 测试文档文件...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  // 测试优化指南
  total++;
  if (testFileExists('docs/OPTIMIZATION_COMPLETE_GUIDE.md', '优化完成指南')) {
    passed++;
  }
  
  // 测试产品增强指南
  total++;
  if (testFileExists('docs/PRODUCT_ENHANCEMENT_GUIDE.md', '产品增强指南')) {
    passed++;
  }
  
  log(`📊 文档文件测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 测试 package.json 脚本
function testPackageScripts() {
  log('\n📦 测试 package.json 脚本...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  total++;
  if (testFileContent('package.json', 'npm 脚本', (content) => {
    const packageJson = JSON.parse(content);
    const scripts = packageJson.scripts || {};
    
    const hasCleanScript = scripts['clean:test-products'];
    const hasGenerateScript = scripts['generate:china-cities'];
    const hasTestScript = scripts['test:login'];
    
    if (hasCleanScript && hasGenerateScript && hasTestScript) {
      return { success: true, message: '包含所有必要的 npm 脚本' };
    } else {
      return { success: false, message: 'npm 脚本不完整' };
    }
  })) {
    passed++;
  }
  
  log(`📊 package.json 脚本测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 主函数
async function main() {
  try {
    log('🧪 开始 BitMarket 优化功能测试...', 'cyan');
    log('', 'reset');

    const results = [];
    
    // 运行所有测试
    results.push(testEnhancedProductConstants());
    results.push(testChinaLocationData());
    results.push(testEnhancedComponents());
    results.push(testScripts());
    results.push(testDocumentation());
    results.push(testPackageScripts());

    // 计算总体结果
    const totalPassed = results.reduce((sum, result) => sum + result.passed, 0);
    const totalTests = results.reduce((sum, result) => sum + result.total, 0);
    const passRate = ((totalPassed / totalTests) * 100).toFixed(1);

    log('\n📊 测试结果汇总:', 'cyan');
    log('=' * 50, 'cyan');
    log(`✅ 通过测试: ${totalPassed}/${totalTests} (${passRate}%)`, totalPassed === totalTests ? 'green' : 'yellow');
    
    if (totalPassed === totalTests) {
      log('\n🎉 所有优化功能测试通过！', 'green');
      log('', 'reset');
      log('💡 系统状态:', 'cyan');
      log('  ✅ 增强分类系统正常', 'reset');
      log('  ✅ 城市数据系统正常', 'reset');
      log('  ✅ 增强组件正常', 'reset');
      log('  ✅ 脚本文件完整', 'reset');
      log('  ✅ 文档完整', 'reset');
      log('', 'reset');
      log('🚀 可以开始使用优化后的功能！', 'green');
    } else {
      log('\n⚠️  部分测试未通过，请检查相关文件', 'yellow');
      log('', 'reset');
      log('🔧 建议操作:', 'cyan');
      log('  1. 检查未通过的测试项目', 'reset');
      log('  2. 重新运行相关生成脚本', 'reset');
      log('  3. 确认文件路径和内容正确', 'reset');
    }

    log('\n🌐 测试完成后可访问:', 'cyan');
    log('  商品发布: http://localhost:3000/products/create', 'reset');
    log('  商品列表: http://localhost:3000/products', 'reset');
    log('  用户登录: http://localhost:3000/auth/signin', 'reset');

  } catch (error) {
    log('❌ 测试执行失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main };
