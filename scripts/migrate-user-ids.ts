import { PrismaClient } from '@prisma/client'
import { updateExistingUsersWithUserId } from '../lib/userIdGenerator'

const prisma = new PrismaClient()

async function main() {
  console.log('开始为现有用户生成userId...')
  
  try {
    await updateExistingUsersWithUserId()
    console.log('用户ID迁移完成！')
  } catch (error) {
    console.error('迁移失败:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
