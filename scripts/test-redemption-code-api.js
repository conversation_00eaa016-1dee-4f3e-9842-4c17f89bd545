const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testRedemptionCodeAPI() {
  try {
    console.log('🧪 开始测试兑换码API功能...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('❌ 未找到管理员用户')
      return
    }

    console.log('👤 管理员信息:')
    console.log('  - ID:', admin.id)
    console.log('  - 邮箱:', admin.email)
    console.log('  - 当前余额:', admin.depositBalance, 'USDT')

    // 查找可用的兑换码
    const availableCode = await prisma.redemptionCode.findFirst({
      where: {
        status: 'ACTIVE',
        usedCount: {
          lt: prisma.redemptionCode.fields.maxUses
        },
        validUntil: {
          gt: new Date()
        }
      }
    })

    if (!availableCode) {
      console.error('❌ 没有可用的兑换码')
      return
    }

    console.log('\n🎫 找到可用兑换码:')
    console.log('  - 代码:', availableCode.codeValue)
    console.log('  - 奖励类型:', availableCode.rewardType)
    console.log('  - 奖励值:', availableCode.rewardValue, availableCode.rewardUnit)
    console.log('  - 状态:', availableCode.status)
    console.log('  - 使用次数:', availableCode.usedCount, '/', availableCode.maxUses)

    // 模拟API调用过程
    console.log('\n💰 开始使用兑换码...')
    
    const result = await prisma.$transaction(async (tx) => {
      // 1. 更新兑换码状态
      const updatedCode = await tx.redemptionCode.update({
        where: { id: availableCode.id },
        data: {
          status: 'USED',
          firstUsedAt: availableCode.firstUsedAt || new Date(),
          lastUsedAt: new Date(),
          usedCount: {
            increment: 1
          }
        }
      })

      // 2. 创建兑换交易记录
      const transaction = await tx.redemptionTransaction.create({
        data: {
          userId: admin.id,
          redemptionCodeId: availableCode.id,
          transactionType: 'REDEMPTION',
          rewardValue: availableCode.rewardValue,
          rewardUnit: availableCode.rewardUnit,
          description: `兑换码使用: ${availableCode.codeValue}`,
          metadata: {
            rewardType: availableCode.rewardType,
            status: 'COMPLETED'
          }
        }
      })

      // 3. 根据奖励类型处理奖励
      if (availableCode.rewardType === 'CASH_CREDIT') {
        // 现金奖励：直接增加用户余额
        await tx.user.update({
          where: { id: admin.id },
          data: {
            depositBalance: {
              increment: availableCode.rewardValue
            }
          }
        })

        // 创建资金交易记录
        await tx.fundTransaction.create({
          data: {
            userId: admin.id,
            type: 'REDEMPTION_REWARD',
            amount: availableCode.rewardValue,
            description: `兑换码奖励: ${availableCode.rewardValue} ${availableCode.rewardUnit}`,
            relatedId: availableCode.id,
            metadata: {
              relatedType: 'REDEMPTION_CODE',
              codeValue: availableCode.codeValue,
              rewardType: availableCode.rewardType
            }
          }
        })
      }

      return {
        redemptionCode: updatedCode,
        transaction,
        rewardType: availableCode.rewardType,
        rewardValue: availableCode.rewardValue,
        rewardUnit: availableCode.rewardUnit
      }
    })

    console.log('✅ 兑换成功!')
    console.log('  - 奖励类型:', result.rewardType)
    console.log('  - 奖励金额:', result.rewardValue, result.rewardUnit)
    console.log('  - 兑换时间:', result.redemptionCode.lastUsedAt)

    // 验证结果
    const updatedAdmin = await prisma.user.findUnique({
      where: { id: admin.id },
      select: { depositBalance: true }
    })

    console.log('\n📊 兑换后状态:')
    console.log('  - 原余额:', admin.depositBalance, 'USDT')
    console.log('  - 新余额:', updatedAdmin.depositBalance, 'USDT')
    console.log('  - 余额增加:', updatedAdmin.depositBalance - admin.depositBalance, 'USDT')

    // 检查兑换码状态
    const updatedCode = await prisma.redemptionCode.findUnique({
      where: { id: availableCode.id }
    })

    console.log('\n🎫 兑换码更新状态:')
    console.log('  - 状态:', updatedCode.status)
    console.log('  - 使用次数:', updatedCode.usedCount, '/', updatedCode.maxUses)
    console.log('  - 首次使用:', updatedCode.firstUsedAt)
    console.log('  - 最后使用:', updatedCode.lastUsedAt)

    console.log('\n🎉 兑换码API功能测试完成！')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testRedemptionCodeAPI()
