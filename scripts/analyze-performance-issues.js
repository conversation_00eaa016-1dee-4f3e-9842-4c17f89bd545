#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')
const { performance } = require('perf_hooks')

const prisma = new PrismaClient()

class PerformanceAnalyzer {
  constructor() {
    this.results = {
      timestamp: Date.now(),
      issues: [],
      recommendations: [],
      optimizations: []
    }
  }

  // 分析数据库性能
  async analyzeDatabasePerformance() {
    console.log('🗄️  分析数据库性能...')
    
    const tests = [
      {
        name: '用户查询性能',
        test: async () => {
          const start = performance.now()
          await prisma.user.findMany({ take: 10 })
          return performance.now() - start
        }
      },
      {
        name: '会话查询性能',
        test: async () => {
          const start = performance.now()
          await prisma.user.findUnique({
            where: { email: '<EMAIL>' },
            select: { id: true, email: true, name: true, role: true }
          })
          return performance.now() - start
        }
      },
      {
        name: '安全日志查询性能',
        test: async () => {
          const start = performance.now()
          await prisma.securityLog.findMany({
            take: 20,
            orderBy: { createdAt: 'desc' }
          })
          return performance.now() - start
        }
      },
      {
        name: '产品查询性能',
        test: async () => {
          const start = performance.now()
          await prisma.product.findMany({
            where: { status: 'AVAILABLE' },
            take: 10,
            include: { seller: true }
          })
          return performance.now() - start
        }
      }
    ]

    for (const test of tests) {
      try {
        const time = await test.test()
        console.log(`  ${test.name}: ${time.toFixed(2)}ms`)
        
        if (time > 100) {
          this.results.issues.push({
            type: 'database',
            severity: time > 500 ? 'critical' : 'warning',
            message: `${test.name}响应时间过长: ${time.toFixed(2)}ms`,
            recommendation: '考虑添加数据库索引或优化查询'
          })
        }
      } catch (error) {
        console.log(`  ${test.name}: 错误 - ${error.message}`)
        this.results.issues.push({
          type: 'database',
          severity: 'critical',
          message: `${test.name}查询失败: ${error.message}`,
          recommendation: '检查数据库连接和表结构'
        })
      }
    }
  }

  // 分析API性能
  async analyzeAPIPerformance() {
    console.log('\n🌐 分析API性能...')
    
    const apiTests = [
      {
        name: '用户资料API',
        endpoint: '/api/user/profile',
        expectedTime: 200
      },
      {
        name: '安全概览API',
        endpoint: '/api/user/security/overview',
        expectedTime: 300
      },
      {
        name: '会话API',
        endpoint: '/api/auth/session',
        expectedTime: 100
      }
    ]

    // 模拟API调用性能测试
    for (const test of apiTests) {
      console.log(`  测试 ${test.name}...`)
      
      // 这里我们分析可能的性能瓶颈
      if (test.endpoint === '/api/auth/session') {
        this.results.issues.push({
          type: 'api',
          severity: 'warning',
          message: '会话API被频繁调用，可能影响性能',
          recommendation: '实现会话缓存或减少调用频率'
        })
      }
      
      if (test.endpoint === '/api/user/security/overview') {
        this.results.issues.push({
          type: 'api',
          severity: 'warning',
          message: '安全概览API可能包含复杂查询',
          recommendation: '优化数据聚合逻辑，考虑使用缓存'
        })
      }
    }
  }

  // 分析编译性能
  analyzeCompilationPerformance() {
    console.log('\n⚡ 分析编译性能...')
    
    // 基于日志分析编译问题
    this.results.issues.push({
      type: 'compilation',
      severity: 'critical',
      message: '设置页面首次编译时间过长 (23.3秒)',
      recommendation: '优化组件导入，减少依赖，考虑代码分割'
    })

    this.results.issues.push({
      type: 'compilation',
      severity: 'warning',
      message: 'API路由编译时间较长 (694ms-947ms)',
      recommendation: '优化API路由的依赖导入'
    })

    // 分析模块数量
    this.results.issues.push({
      type: 'compilation',
      severity: 'warning',
      message: '编译模块数量过多 (887-1163个模块)',
      recommendation: '实现动态导入和代码分割'
    })
  }

  // 分析缓存问题
  analyzeCacheIssues() {
    console.log('\n💾 分析缓存问题...')
    
    this.results.issues.push({
      type: 'cache',
      severity: 'critical',
      message: 'Redis适配器连接失败，降级到默认适配器',
      recommendation: '修复Redis连接或配置备用缓存策略'
    })

    this.results.issues.push({
      type: 'cache',
      severity: 'warning',
      message: '缺少API响应缓存',
      recommendation: '为频繁调用的API实现缓存机制'
    })
  }

  // 生成优化建议
  generateOptimizations() {
    console.log('\n🚀 生成优化建议...')
    
    this.results.optimizations = [
      {
        category: '数据库优化',
        priority: 'high',
        items: [
          '为频繁查询的字段添加数据库索引',
          '优化复杂查询，减少JOIN操作',
          '实现查询结果缓存',
          '使用数据库连接池'
        ]
      },
      {
        category: 'API优化',
        priority: 'high',
        items: [
          '实现API响应缓存',
          '减少会话API调用频率',
          '优化数据序列化',
          '实现API请求去重'
        ]
      },
      {
        category: '编译优化',
        priority: 'medium',
        items: [
          '实现代码分割和懒加载',
          '优化组件导入结构',
          '减少不必要的依赖',
          '使用动态导入'
        ]
      },
      {
        category: '缓存优化',
        priority: 'high',
        items: [
          '修复Redis连接问题',
          '实现多层缓存策略',
          '配置适当的缓存TTL',
          '实现缓存预热'
        ]
      },
      {
        category: '前端优化',
        priority: 'medium',
        items: [
          '实现组件级缓存',
          '优化重新渲染',
          '使用React.memo和useMemo',
          '实现虚拟滚动'
        ]
      }
    ]
  }

  // 生成性能报告
  generateReport() {
    console.log('\n📊 性能分析报告')
    console.log('='.repeat(60))
    
    // 问题统计
    const criticalIssues = this.results.issues.filter(i => i.severity === 'critical').length
    const warningIssues = this.results.issues.filter(i => i.severity === 'warning').length
    
    console.log(`🚨 严重问题: ${criticalIssues}个`)
    console.log(`⚠️  警告问题: ${warningIssues}个`)
    console.log(`💡 优化建议: ${this.results.optimizations.length}类`)
    
    // 详细问题列表
    console.log('\n🔍 发现的问题:')
    this.results.issues.forEach((issue, index) => {
      const icon = issue.severity === 'critical' ? '🚨' : '⚠️'
      console.log(`${index + 1}. ${icon} [${issue.type.toUpperCase()}] ${issue.message}`)
      console.log(`   💡 建议: ${issue.recommendation}`)
      console.log('')
    })
    
    // 优化建议
    console.log('🚀 优化建议:')
    this.results.optimizations.forEach(opt => {
      const priorityIcon = opt.priority === 'high' ? '🔥' : opt.priority === 'medium' ? '⚡' : '💡'
      console.log(`\n${priorityIcon} ${opt.category} (优先级: ${opt.priority})`)
      opt.items.forEach(item => {
        console.log(`   • ${item}`)
      })
    })
    
    // 性能评分
    const score = Math.max(0, 100 - (criticalIssues * 20) - (warningIssues * 10))
    console.log(`\n🏆 性能评分: ${score}/100`)
    
    let grade, description
    if (score >= 90) {
      grade = 'A'
      description = '性能优秀'
    } else if (score >= 80) {
      grade = 'B'
      description = '性能良好'
    } else if (score >= 70) {
      grade = 'C'
      description = '性能一般'
    } else if (score >= 60) {
      grade = 'D'
      description = '性能较差'
    } else {
      grade = 'F'
      description = '性能很差'
    }
    
    console.log(`📈 性能等级: ${grade} - ${description}`)
    
    return {
      ...this.results,
      score,
      grade,
      description
    }
  }

  // 保存分析结果
  async saveResults(report) {
    try {
      const fs = require('fs').promises
      await fs.mkdir('test-results/performance', { recursive: true })
      
      const filename = `performance-analysis-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.writeFile(
        `test-results/performance/${filename}`,
        JSON.stringify(report, null, 2)
      )
      
      console.log(`\n💾 分析结果已保存: test-results/performance/${filename}`)
    } catch (error) {
      console.error('保存分析结果失败:', error)
    }
  }
}

// 运行性能分析
async function runPerformanceAnalysis() {
  const analyzer = new PerformanceAnalyzer()
  
  try {
    console.log('🔍 开始性能问题分析')
    console.log('='.repeat(60))
    
    await analyzer.analyzeDatabasePerformance()
    await analyzer.analyzeAPIPerformance()
    analyzer.analyzeCompilationPerformance()
    analyzer.analyzeCacheIssues()
    analyzer.generateOptimizations()
    
    const report = analyzer.generateReport()
    await analyzer.saveResults(report)
    
    console.log('\n✅ 性能分析完成')
    return report
    
  } catch (error) {
    console.error('❌ 性能分析失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 命令行运行
if (require.main === module) {
  runPerformanceAnalysis()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { PerformanceAnalyzer, runPerformanceAnalysis }
