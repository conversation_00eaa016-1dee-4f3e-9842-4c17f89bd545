const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkOrders() {
  try {
    const orders = await prisma.order.findMany({
      select: {
        id: true,
        status: true,
        paymentConfirmed: true,
        paymentTxHash: true,
        totalAmount: true,
        createdAt: true,
        updatedAt: true,
        product: {
          select: {
            title: true
          }
        },
        buyer: {
          select: {
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })
    
    console.log('📋 最近的订单状态:')
    console.log('=' .repeat(60))
    
    orders.forEach((order, index) => {
      console.log(`${index + 1}. 订单ID: ${order.id}`)
      console.log(`   商品: ${order.product.title}`)
      console.log(`   买家: ${order.buyer.email}`)
      console.log(`   金额: ${order.totalAmount} USDT`)
      console.log(`   状态: ${order.status}`)
      console.log(`   支付确认: ${order.paymentConfirmed ? '✅ 已确认' : '❌ 未确认'}`)
      console.log(`   交易哈希: ${order.paymentTxHash || '无'}`)
      console.log(`   创建时间: ${order.createdAt.toLocaleString()}`)
      console.log(`   更新时间: ${order.updatedAt.toLocaleString()}`)
      console.log('-'.repeat(40))
    })
    
    // 检查支付状态统计
    const statusStats = await prisma.order.groupBy({
      by: ['status', 'paymentConfirmed'],
      _count: {
        id: true
      }
    })
    
    console.log('\n📊 订单状态统计:')
    console.log('=' .repeat(40))
    statusStats.forEach(stat => {
      console.log(`状态: ${stat.status}, 支付确认: ${stat.paymentConfirmed ? '是' : '否'}, 数量: ${stat._count.id}`)
    })
    
  } catch (error) {
    console.error('查询失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkOrders()
  .then(() => {
    console.log('\n🎉 查询完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error)
    process.exit(1)
  })
