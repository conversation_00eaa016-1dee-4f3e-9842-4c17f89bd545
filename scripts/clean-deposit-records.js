const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanDepositRecords() {
  try {
    console.log('🧹 开始清理充值申请记录...')
    console.log('='.repeat(50))

    // 1. 查看当前充值记录统计
    const currentStats = await prisma.depositRecord.groupBy({
      by: ['status'],
      _count: { id: true },
      _sum: { amount: true }
    })

    console.log('📊 当前充值记录统计:')
    currentStats.forEach(stat => {
      console.log(`   ${stat.status}: ${stat._count.id} 笔, 总额 ${stat._sum.amount?.toFixed(2) || 0} USDT`)
    })

    const totalRecords = await prisma.depositRecord.count()
    console.log(`   总计: ${totalRecords} 笔充值记录`)
    console.log('')

    // 2. 显示最近的充值记录
    const recentRecords = await prisma.depositRecord.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    console.log('📋 最近的充值记录:')
    recentRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.user.name} - ${record.amount} USDT (${record.method}) - ${record.status}`)
      console.log(`      时间: ${record.createdAt.toLocaleString()}`)
      if (record.notes) {
        console.log(`      备注: ${record.notes}`)
      }
      console.log('')
    })

    // 3. 询问用户确认
    console.log('⚠️  警告: 即将删除所有充值申请记录!')
    console.log('这个操作不可逆转，请确认您真的要删除所有充值记录。')
    console.log('')
    console.log('如果您只想删除测试数据，请考虑使用更精确的条件。')
    console.log('')

    // 4. 提供不同的清理选项
    console.log('🔧 可用的清理选项:')
    console.log('1. 删除所有充值记录')
    console.log('2. 只删除测试充值记录 (包含"测试"关键词的)')
    console.log('3. 只删除待处理的充值记录')
    console.log('4. 只删除今天创建的充值记录')
    console.log('5. 取消操作')
    console.log('')

    // 由于这是一个脚本，我们提供几个预设的清理函数
    // 用户可以根据需要调用不同的函数

  } catch (error) {
    console.error('❌ 查询充值记录失败:', error.message)
  }
}

// 清理选项 1: 删除所有充值记录
async function deleteAllDepositRecords() {
  try {
    console.log('🗑️  删除所有充值记录...')
    
    const deleteResult = await prisma.depositRecord.deleteMany({})
    
    console.log(`✅ 成功删除 ${deleteResult.count} 条充值记录`)
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 清理选项 2: 只删除测试充值记录
async function deleteTestDepositRecords() {
  try {
    console.log('🧪 删除测试充值记录...')
    
    const deleteResult = await prisma.depositRecord.deleteMany({
      where: {
        OR: [
          { notes: { contains: '测试' } },
          { notes: { contains: 'test' } },
          { notes: { contains: 'Test' } },
          { notes: { contains: '需要核实' } },
          { notes: { contains: '需要确认' } }
        ]
      }
    })
    
    console.log(`✅ 成功删除 ${deleteResult.count} 条测试充值记录`)
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 清理选项 3: 只删除待处理的充值记录
async function deletePendingDepositRecords() {
  try {
    console.log('⏳ 删除待处理的充值记录...')
    
    const deleteResult = await prisma.depositRecord.deleteMany({
      where: {
        status: 'PENDING'
      }
    })
    
    console.log(`✅ 成功删除 ${deleteResult.count} 条待处理充值记录`)
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 清理选项 4: 只删除今天创建的充值记录
async function deleteTodayDepositRecords() {
  try {
    console.log('📅 删除今天创建的充值记录...')
    
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const deleteResult = await prisma.depositRecord.deleteMany({
      where: {
        createdAt: {
          gte: today
        }
      }
    })
    
    console.log(`✅ 成功删除 ${deleteResult.count} 条今天创建的充值记录`)
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 清理选项 5: 删除特定用户的充值记录
async function deleteUserDepositRecords(userEmail) {
  try {
    console.log(`👤 删除用户 ${userEmail} 的充值记录...`)
    
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    })
    
    if (!user) {
      console.log('❌ 用户不存在')
      return
    }
    
    const deleteResult = await prisma.depositRecord.deleteMany({
      where: {
        userId: user.id
      }
    })
    
    console.log(`✅ 成功删除用户 ${userEmail} 的 ${deleteResult.count} 条充值记录`)
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

// 主函数
async function main() {
  console.log('🚀 BitMarket 充值记录清理工具')
  console.log('='.repeat(50))
  
  try {
    // 显示当前状态
    await cleanDepositRecords()
    
    console.log('💡 使用方法:')
    console.log('1. 删除所有充值记录:')
    console.log('   node scripts/clean-deposit-records.js all')
    console.log('')
    console.log('2. 删除测试充值记录:')
    console.log('   node scripts/clean-deposit-records.js test')
    console.log('')
    console.log('3. 删除待处理充值记录:')
    console.log('   node scripts/clean-deposit-records.js pending')
    console.log('')
    console.log('4. 删除今天的充值记录:')
    console.log('   node scripts/clean-deposit-records.js today')
    console.log('')
    console.log('5. 删除特定用户的充值记录:')
    console.log('   node scripts/clean-deposit-records.<NAME_EMAIL>')
    
    // 根据命令行参数执行相应操作
    const args = process.argv.slice(2)
    const action = args[0]
    
    if (action === 'all') {
      console.log('\n⚠️  确认删除所有充值记录...')
      await deleteAllDepositRecords()
    } else if (action === 'test') {
      console.log('\n🧪 删除测试充值记录...')
      await deleteTestDepositRecords()
    } else if (action === 'pending') {
      console.log('\n⏳ 删除待处理充值记录...')
      await deletePendingDepositRecords()
    } else if (action === 'today') {
      console.log('\n📅 删除今天的充值记录...')
      await deleteTodayDepositRecords()
    } else if (action === 'user' && args[1]) {
      console.log(`\n👤 删除用户 ${args[1]} 的充值记录...`)
      await deleteUserDepositRecords(args[1])
    } else if (action) {
      console.log(`\n❌ 未知操作: ${action}`)
    }
    
    // 显示清理后的状态
    if (action) {
      console.log('\n📊 清理后的统计:')
      const finalStats = await prisma.depositRecord.groupBy({
        by: ['status'],
        _count: { id: true },
        _sum: { amount: true }
      })
      
      if (finalStats.length > 0) {
        finalStats.forEach(stat => {
          console.log(`   ${stat.status}: ${stat._count.id} 笔, 总额 ${stat._sum.amount?.toFixed(2) || 0} USDT`)
        })
      } else {
        console.log('   无充值记录')
      }
      
      const finalTotal = await prisma.depositRecord.count()
      console.log(`   总计: ${finalTotal} 笔充值记录`)
    }
    
  } catch (error) {
    console.error('\n💥 操作失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  deleteAllDepositRecords,
  deleteTestDepositRecords,
  deletePendingDepositRecords,
  deleteTodayDepositRecords,
  deleteUserDepositRecords
}
