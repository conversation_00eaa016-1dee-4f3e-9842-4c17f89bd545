const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createCreditTestData() {
  try {
    console.log('🎯 开始创建信用记录测试数据...')

    // 获取第一个用户
    const user = await prisma.user.findFirst({
      where: {
        role: 'USER'
      }
    })

    if (!user) {
      console.log('❌ 没有找到用户，请先创建用户账户')
      return
    }

    console.log(`📋 为用户 ${user.email} (ID: ${user.id}) 创建信用记录`)

    // 创建一些信用变动记录
    const creditHistories = [
      {
        userId: user.id,
        changeType: 'FIRST_ORDER',
        changeScore: 5,
        reason: '首次交易奖励',
        beforeScore: 30,
        afterScore: 35,
        metadata: { welcome: true }
      },
      {
        userId: user.id,
        changeType: 'ORDER_COMPLETE',
        changeScore: 2,
        reason: '完成订单交易',
        beforeScore: 35,
        afterScore: 37,
        metadata: { orderNumber: 'ORD-2024-001' }
      },
      {
        userId: user.id,
        changeType: 'REVIEW_RECEIVED',
        changeScore: 3,
        reason: '收到5星好评',
        beforeScore: 37,
        afterScore: 40,
        metadata: { rating: 5, reviewerName: '满意的买家' }
      },
      {
        userId: user.id,
        changeType: 'ORDER_COMPLETE',
        changeScore: 2,
        reason: '完成订单交易',
        beforeScore: 40,
        afterScore: 42,
        metadata: { orderNumber: 'ORD-2024-002' }
      },
      {
        userId: user.id,
        changeType: 'REVIEW_RECEIVED',
        changeScore: 2,
        reason: '收到4星好评',
        beforeScore: 42,
        afterScore: 44,
        metadata: { rating: 4, reviewerName: '不错的买家' }
      },
      {
        userId: user.id,
        changeType: 'BONUS',
        changeScore: 3,
        reason: '月度活跃用户奖励',
        beforeScore: 44,
        afterScore: 47,
        metadata: { month: '2024-01', activeLevel: 'high' }
      },
      {
        userId: user.id,
        changeType: 'ORDER_COMPLETE',
        changeScore: 2,
        reason: '完成订单交易',
        beforeScore: 47,
        afterScore: 49,
        metadata: { orderNumber: 'ORD-2024-003' }
      },
      {
        userId: user.id,
        changeType: 'REVIEW_RECEIVED',
        changeScore: 1,
        reason: '收到3星评价',
        beforeScore: 49,
        afterScore: 50,
        metadata: { rating: 3, reviewerName: '一般的买家' }
      },
      {
        userId: user.id,
        changeType: 'ORDER_COMPLETE',
        changeScore: 2,
        reason: '完成订单交易',
        beforeScore: 50,
        afterScore: 52,
        metadata: { orderNumber: 'ORD-2024-004' }
      },
      {
        userId: user.id,
        changeType: 'REVIEW_RECEIVED',
        changeScore: 3,
        reason: '收到5星好评',
        beforeScore: 52,
        afterScore: 55,
        metadata: { rating: 5, reviewerName: '非常满意的买家' }
      }
    ]

    // 批量创建信用记录
    for (const history of creditHistories) {
      await prisma.creditHistory.create({
        data: {
          ...history,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // 随机过去30天内的时间
        }
      })
      
      // 添加延迟以确保时间顺序
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // 更新用户的信用分数到最终值
    await prisma.user.update({
      where: { id: user.id },
      data: { creditScore: 55 }
    })

    console.log('✅ 信用记录测试数据创建完成！')
    console.log(`📊 用户当前信用分数: 55`)
    console.log(`📝 创建了 ${creditHistories.length} 条信用变动记录`)
    console.log('')
    console.log('🎯 测试数据包含:')
    console.log('  • 首次交易奖励 (+5分)')
    console.log('  • 订单完成奖励 (+2分 x4)')
    console.log('  • 好评奖励 (5星+3分, 4星+2分, 3星+1分)')
    console.log('  • 月度活跃奖励 (+3分)')
    console.log('')
    console.log('🌐 现在可以访问 http://localhost:3000/credit 查看信用记录页面')

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createCreditTestData()
