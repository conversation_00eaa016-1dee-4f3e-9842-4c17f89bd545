import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 担保金等级配置
const guaranteeLevels = [
  {
    level: 'BRONZE',
    minBalance: 0,
    maxDailyWithdrawal: 1000,
    withdrawalFeeRate: 0.02, // 2%
    tradingFeeDiscount: 0,   // 无折扣
    priorityLevel: 1,
    benefits: {
      description: '青铜等级用户',
      features: [
        '基础交易功能',
        '标准客服支持',
        '每日提现限额 1,000 USDT'
      ],
      withdrawalProcessTime: '24小时内',
      customerServiceLevel: '标准'
    },
    requirements: {
      minBalance: 0,
      description: '新用户默认等级'
    }
  },
  {
    level: 'SILVER',
    minBalance: 1000,
    maxDailyWithdrawal: 5000,
    withdrawalFeeRate: 0.015, // 1.5%
    tradingFeeDiscount: 0.1,  // 10%折扣
    priorityLevel: 2,
    benefits: {
      description: '白银等级用户',
      features: [
        '优先交易撮合',
        '优先客服支持',
        '每日提现限额 5,000 USDT',
        '交易手续费9折优惠',
        '商品优先展示权重+10%'
      ],
      withdrawalProcessTime: '12小时内',
      customerServiceLevel: '优先'
    },
    requirements: {
      minBalance: 1000,
      description: '担保金余额达到 1,000 USDT'
    }
  },
  {
    level: 'GOLD',
    minBalance: 5000,
    maxDailyWithdrawal: 20000,
    withdrawalFeeRate: 0.01, // 1%
    tradingFeeDiscount: 0.2, // 20%折扣
    priorityLevel: 3,
    benefits: {
      description: '黄金等级用户',
      features: [
        '高优先级交易撮合',
        'VIP客服专线',
        '每日提现限额 20,000 USDT',
        '交易手续费8折优惠',
        '商品优先展示权重+25%',
        '专属黄金标识',
        '月度奖励资格'
      ],
      withdrawalProcessTime: '6小时内',
      customerServiceLevel: 'VIP'
    },
    requirements: {
      minBalance: 5000,
      description: '担保金余额达到 5,000 USDT'
    }
  },
  {
    level: 'DIAMOND',
    minBalance: 20000,
    maxDailyWithdrawal: 50000,
    withdrawalFeeRate: 0.005, // 0.5%
    tradingFeeDiscount: 0.3,  // 30%折扣
    priorityLevel: 4,
    benefits: {
      description: '钻石等级用户',
      features: [
        '最高优先级交易撮合',
        '专属客户经理',
        '每日提现限额 50,000 USDT',
        '交易手续费7折优惠',
        '商品优先展示权重+50%',
        '专属钻石标识',
        '月度高级奖励',
        '平台重大活动优先参与权',
        '新功能内测资格'
      ],
      withdrawalProcessTime: '2小时内',
      customerServiceLevel: '专属客户经理'
    },
    requirements: {
      minBalance: 20000,
      description: '担保金余额达到 20,000 USDT'
    }
  },
  {
    level: 'PLATINUM',
    minBalance: 100000,
    maxDailyWithdrawal: 200000,
    withdrawalFeeRate: 0.002, // 0.2%
    tradingFeeDiscount: 0.5,  // 50%折扣
    priorityLevel: 5,
    benefits: {
      description: '铂金等级用户',
      features: [
        '超级优先级交易撮合',
        '专属VIP客户经理',
        '每日提现限额 200,000 USDT',
        '交易手续费5折优惠',
        '商品优先展示权重+100%',
        '专属铂金标识',
        '月度顶级奖励',
        '平台决策咨询权',
        '专属活动和福利',
        '一对一定制服务',
        '优先新币种交易权'
      ],
      withdrawalProcessTime: '1小时内',
      customerServiceLevel: '专属VIP客户经理'
    },
    requirements: {
      minBalance: 100000,
      description: '担保金余额达到 100,000 USDT'
    }
  }
]

async function initGuaranteeLevels() {
  console.log('开始初始化担保金等级配置...')

  try {
    // 清除现有配置
    await prisma.guaranteeLevel.deleteMany({})
    console.log('已清除现有担保金等级配置')

    // 插入新配置
    for (const level of guaranteeLevels) {
      await prisma.guaranteeLevel.create({
        data: level
      })
      console.log(`已创建 ${level.level} 等级配置`)
    }

    console.log('担保金等级配置初始化完成！')

    // 显示配置摘要
    console.log('\n=== 担保金等级配置摘要 ===')
    const levels = await prisma.guaranteeLevel.findMany({
      orderBy: { minBalance: 'asc' }
    })

    levels.forEach(level => {
      console.log(`${level.level}:`)
      console.log(`  最低余额: ${level.minBalance.toLocaleString()} USDT`)
      console.log(`  日提现限额: ${level.maxDailyWithdrawal.toLocaleString()} USDT`)
      console.log(`  提现手续费: ${(level.withdrawalFeeRate * 100).toFixed(2)}%`)
      console.log(`  交易费折扣: ${(level.tradingFeeDiscount * 100).toFixed(0)}%`)
      console.log('')
    })

  } catch (error) {
    console.error('初始化担保金等级配置失败:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  initGuaranteeLevels()
    .then(() => {
      console.log('脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('脚本执行失败:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}

export { initGuaranteeLevels }
