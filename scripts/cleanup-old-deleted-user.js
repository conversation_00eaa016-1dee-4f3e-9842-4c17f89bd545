const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanupOldDeletedUser() {
  try {
    console.log('🧹 清理旧的已删除用户记录...')
    
    // 1. 查找旧的已删除用户
    console.log('\n1. 查找旧的已删除用户...')
    const oldDeletedUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        products: true,
        ordersAsBuyer: true,
        ordersAsSeller: true,
        addresses: true,
        userSessions: true,
        favorites: true
      }
    })
    
    if (!oldDeletedUser) {
      console.log('❌ 没有找到指定的旧删除用户')
      return
    }
    
    console.log(`✅ 找到旧删除用户:`)
    console.log(`   ID: ${oldDeletedUser.id}`)
    console.log(`   姓名: ${oldDeletedUser.name}`)
    console.log(`   邮箱: ${oldDeletedUser.email}`)
    console.log(`   用户ID: ${oldDeletedUser.userId}`)
    console.log(`   状态: ${oldDeletedUser.status}`)
    console.log(`   商品数量: ${oldDeletedUser.products.length}`)
    console.log(`   买单数量: ${oldDeletedUser.ordersAsBuyer.length}`)
    console.log(`   卖单数量: ${oldDeletedUser.ordersAsSeller.length}`)
    
    // 2. 检查是否有关联的订单
    console.log('\n2. 检查关联数据...')
    const hasActiveOrders = oldDeletedUser.ordersAsBuyer.some(order => 
      ['PENDING_PAYMENT', 'PAID', 'SHIPPED'].includes(order.status)
    ) || oldDeletedUser.ordersAsSeller.some(order => 
      ['PENDING_PAYMENT', 'PAID', 'SHIPPED'].includes(order.status)
    )
    
    if (hasActiveOrders) {
      console.log('⚠️ 用户有未完成的订单，无法删除')
      return
    }
    
    console.log('✅ 用户没有未完成订单，可以安全删除')
    
    // 3. 使用我们的物理删除API来删除这个用户
    console.log('\n3. 执行物理删除...')
    
    // 模拟API调用
    const deleteResult = await prisma.$transaction(async (tx) => {
      // 3.1 创建匿名用户
      const timestamp = Date.now()
      const userIdSuffix = oldDeletedUser.userId ? oldDeletedUser.userId.slice(-8) : oldDeletedUser.id.slice(-8)
      const anonymizedName = `已删除用户#${userIdSuffix}`
      const anonymizedEmail = `deleted_user_${timestamp}@anonymous.local`
      const anonymizedUserId = `deleted-${timestamp}-${Math.random().toString(36).substring(2, 8)}`
      
      const anonymousUser = await tx.user.create({
        data: {
          userId: anonymizedUserId,
          name: anonymizedName,
          email: anonymizedEmail,
          password: 'deleted',
          role: 'USER',
          status: 'DELETED',
          creditScore: 0,
          city: '已删除',
          district: '已删除'
        }
      })
      
      console.log(`   ✅ 创建匿名用户: ${anonymousUser.name} (${anonymousUser.email})`)
      
      // 3.2 更新订单引用
      const buyerOrdersUpdated = await tx.order.updateMany({
        where: { buyerId: oldDeletedUser.id },
        data: { buyerId: anonymousUser.id }
      })
      
      const sellerOrdersUpdated = await tx.order.updateMany({
        where: { sellerId: oldDeletedUser.id },
        data: { sellerId: anonymousUser.id }
      })
      
      console.log(`   ✅ 更新买家订单: ${buyerOrdersUpdated.count} 个`)
      console.log(`   ✅ 更新卖家订单: ${sellerOrdersUpdated.count} 个`)
      
      // 3.3 处理商品
      const productsUpdated = await tx.product.updateMany({
        where: { sellerId: oldDeletedUser.id },
        data: {
          sellerId: anonymousUser.id,
          status: 'UNAVAILABLE',
          title: `[已删除用户商品] ${anonymizedName}的商品`
        }
      })
      
      console.log(`   ✅ 更新商品: ${productsUpdated.count} 个`)
      
      // 3.4 删除关联数据
      const addressesDeleted = await tx.address.deleteMany({
        where: { userId: oldDeletedUser.id }
      })
      
      const sessionsDeleted = await tx.userSession.deleteMany({
        where: { userId: oldDeletedUser.id }
      })
      
      const favoritesDeleted = await tx.favorite.deleteMany({
        where: { userId: oldDeletedUser.id }
      })
      
      console.log(`   ✅ 删除地址: ${addressesDeleted.count} 个`)
      console.log(`   ✅ 删除会话: ${sessionsDeleted.count} 个`)
      console.log(`   ✅ 删除收藏: ${favoritesDeleted.count} 个`)
      
      // 3.5 删除用户记录
      const deletedUser = await tx.user.delete({
        where: { id: oldDeletedUser.id },
        select: {
          id: true,
          name: true,
          email: true
        }
      })
      
      console.log(`   ✅ 删除用户记录: ${deletedUser.name} (${deletedUser.email})`)
      
      return {
        deletedUser,
        anonymousUser,
        stats: {
          buyerOrders: buyerOrdersUpdated.count,
          sellerOrders: sellerOrdersUpdated.count,
          products: productsUpdated.count,
          addresses: addressesDeleted.count,
          sessions: sessionsDeleted.count,
          favorites: favoritesDeleted.count
        }
      }
    })
    
    console.log('\n✅ 删除完成！')
    console.log(`   原用户: ${deleteResult.deletedUser.name} (${deleteResult.deletedUser.email})`)
    console.log(`   匿名用户: ${deleteResult.anonymousUser.name} (${deleteResult.anonymousUser.email})`)
    console.log(`   处理统计:`)
    console.log(`     - 买家订单: ${deleteResult.stats.buyerOrders}`)
    console.log(`     - 卖家订单: ${deleteResult.stats.sellerOrders}`)
    console.log(`     - 商品: ${deleteResult.stats.products}`)
    console.log(`     - 地址: ${deleteResult.stats.addresses}`)
    console.log(`     - 会话: ${deleteResult.stats.sessions}`)
    console.log(`     - 收藏: ${deleteResult.stats.favorites}`)
    
    console.log('\n🎉 旧的已删除用户已成功清理并转换为新的匿名用户格式！')
    
  } catch (error) {
    console.error('清理失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

cleanupOldDeletedUser()
  .then(() => {
    console.log('\n✨ 清理操作完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 清理失败:', error)
    process.exit(1)
  })
