const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testAPIStatus() {
  try {
    const userId = 'cmd8desog0002v9rwq6iekv1c';
    
    console.log('🔍 测试不同状态的商品查询');
    console.log('='.repeat(50));
    
    const statuses = ['AVAILABLE', 'SOLD', 'INACTIVE', 'SOLD_OUT'];
    
    for (const status of statuses) {
      console.log(`\n📊 查询状态: ${status}`);
      
      let whereCondition = {
        sellerId: userId,
        reviewStatus: 'APPROVED'
      };
      
      if (status === 'INACTIVE') {
        // INACTIVE状态包括SOLD_OUT和INACTIVE
        whereCondition.status = {
          in: ['INACTIVE', 'SOLD_OUT']
        };
      } else {
        whereCondition.status = status;
      }
      
      const products = await prisma.product.findMany({
        where: whereCondition,
        select: {
          id: true,
          title: true,
          stock: true,
          status: true,
          reviewStatus: true
        }
      });
      
      console.log(`找到 ${products.length} 个商品`);
      products.forEach(p => {
        console.log(`  - ${p.title} (库存: ${p.stock}, 状态: ${p.status})`);
      });
    }
    
    // 测试所有商品
    console.log('\n📋 该用户所有商品:');
    const allProducts = await prisma.product.findMany({
      where: { sellerId: userId },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        reviewStatus: true
      }
    });
    
    allProducts.forEach(p => {
      console.log(`  - ${p.title}`);
      console.log(`    库存: ${p.stock}, 状态: ${p.status}, 审核: ${p.reviewStatus}`);
    });
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('错误:', error);
    await prisma.$disconnect();
  }
}

testAPIStatus();
