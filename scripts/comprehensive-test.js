const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function comprehensiveTest() {
  try {
    console.log('🎯 开始综合功能测试...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('❌ 未找到管理员用户')
      return
    }

    console.log('👤 管理员信息:')
    console.log('  - ID:', admin.id)
    console.log('  - 邮箱:', admin.email)
    console.log('  - 当前余额:', admin.depositBalance, 'USDT')

    // 1. 测试礼品卡商品管理
    console.log('\n📦 测试礼品卡商品管理...')
    const giftCardProducts = await prisma.giftCardProduct.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            giftCards: true,
            orders: true
          }
        }
      }
    })

    console.log(`✅ 找到 ${giftCardProducts.length} 个活跃的礼品卡商品`)
    giftCardProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} (${product.productType})`)
      console.log(`     面值: ${product.faceValue} USDT, 售价: ${product.salePrice} USDT`)
      console.log(`     库存: ${product.stock}, 关联礼品卡: ${product._count.giftCards} 张`)
    })

    // 2. 测试礼品卡兑换
    console.log('\n🎁 测试礼品卡兑换...')
    const availableGiftCard = await prisma.giftCard.findFirst({
      where: {
        status: 'GENERATED',
        validUntil: {
          gt: new Date()
        }
      },
      include: {
        product: true
      }
    })

    if (availableGiftCard) {
      console.log(`✅ 找到可兑换礼品卡: ${availableGiftCard.cardCode}`)
      console.log(`   面值: ${availableGiftCard.faceValue} USDT`)
      console.log(`   商品: ${availableGiftCard.product?.name || '无'}`)
      console.log(`   有效期: ${availableGiftCard.validUntil}`)
    } else {
      console.log('⚠️  没有可兑换的礼品卡')
    }

    // 3. 测试兑换码功能
    console.log('\n🎫 测试兑换码功能...')
    const availableRedemptionCodes = await prisma.redemptionCode.findMany({
      where: {
        status: 'ACTIVE',
        usedCount: {
          lt: prisma.redemptionCode.fields.maxUses
        },
        validUntil: {
          gt: new Date()
        }
      },
      take: 3
    })

    console.log(`✅ 找到 ${availableRedemptionCodes.length} 个可用兑换码`)
    availableRedemptionCodes.forEach((code, index) => {
      console.log(`  ${index + 1}. ${code.codeValue}`)
      console.log(`     奖励: ${code.rewardValue} ${code.rewardUnit} (${code.rewardType})`)
      console.log(`     使用次数: ${code.usedCount}/${code.maxUses}`)
    })

    // 4. 检查交易记录
    console.log('\n📊 检查最近的交易记录...')
    
    // 资金交易记录
    const fundTransactions = await prisma.fundTransaction.findMany({
      where: {
        userId: admin.id
      },
      orderBy: { createdAt: 'desc' },
      take: 3,
      include: {
        user: { select: { name: true, email: true } }
      }
    })

    console.log(`✅ 最近 ${fundTransactions.length} 条资金交易:`)
    fundTransactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. ${tx.type} - ${tx.amount} USDT`)
      console.log(`     描述: ${tx.description}`)
      console.log(`     时间: ${tx.createdAt}`)
    })

    // 礼品卡交易记录
    const giftCardTransactions = await prisma.giftCardTransaction.findMany({
      where: {
        userId: admin.id
      },
      orderBy: { createdAt: 'desc' },
      take: 3,
      include: {
        giftCard: { select: { cardCode: true } }
      }
    })

    console.log(`\n🎁 最近 ${giftCardTransactions.length} 条礼品卡交易:`)
    giftCardTransactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. ${tx.transactionType} - ${tx.amount} USDT`)
      console.log(`     礼品卡: ${tx.giftCard.cardCode}`)
      console.log(`     时间: ${tx.createdAt}`)
    })

    // 兑换交易记录
    const redemptionTransactions = await prisma.redemptionTransaction.findMany({
      where: {
        userId: admin.id
      },
      orderBy: { createdAt: 'desc' },
      take: 3,
      include: {
        redemptionCode: { select: { codeValue: true } }
      }
    })

    console.log(`\n🎫 最近 ${redemptionTransactions.length} 条兑换交易:`)
    redemptionTransactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. ${tx.transactionType} - ${tx.rewardValue} ${tx.rewardUnit}`)
      console.log(`     兑换码: ${tx.redemptionCode.codeValue}`)
      console.log(`     时间: ${tx.createdAt}`)
    })

    // 5. 系统状态总结
    console.log('\n📈 系统状态总结:')
    
    const stats = {
      totalGiftCardProducts: await prisma.giftCardProduct.count(),
      activeGiftCardProducts: await prisma.giftCardProduct.count({ where: { isActive: true } }),
      totalGiftCards: await prisma.giftCard.count(),
      availableGiftCards: await prisma.giftCard.count({ where: { status: 'GENERATED' } }),
      redeemedGiftCards: await prisma.giftCard.count({ where: { status: 'REDEEMED' } }),
      totalRedemptionCodes: await prisma.redemptionCode.count(),
      activeRedemptionCodes: await prisma.redemptionCode.count({ where: { status: 'ACTIVE' } }),
      usedRedemptionCodes: await prisma.redemptionCode.count({ where: { status: 'USED' } }),
      totalFundTransactions: await prisma.fundTransaction.count(),
      totalGiftCardTransactions: await prisma.giftCardTransaction.count(),
      totalRedemptionTransactions: await prisma.redemptionTransaction.count()
    }

    console.log('✅ 数据统计:')
    console.log(`  - 礼品卡商品: ${stats.activeGiftCardProducts}/${stats.totalGiftCardProducts} (活跃/总数)`)
    console.log(`  - 礼品卡: ${stats.availableGiftCards}/${stats.totalGiftCards} (可用/总数)`)
    console.log(`  - 已兑换礼品卡: ${stats.redeemedGiftCards} 张`)
    console.log(`  - 兑换码: ${stats.activeRedemptionCodes}/${stats.totalRedemptionCodes} (活跃/总数)`)
    console.log(`  - 已使用兑换码: ${stats.usedRedemptionCodes} 个`)
    console.log(`  - 资金交易记录: ${stats.totalFundTransactions} 条`)
    console.log(`  - 礼品卡交易记录: ${stats.totalGiftCardTransactions} 条`)
    console.log(`  - 兑换交易记录: ${stats.totalRedemptionTransactions} 条`)

    console.log('\n🎉 综合功能测试完成！')
    console.log('\n💡 测试建议:')
    console.log('1. 访问 http://localhost:3000/deposit 测试用户端兑换功能')
    console.log('2. 访问 http://localhost:3000/admin/giftcard-products 管理礼品卡商品')
    console.log('3. 访问 http://localhost:3000/admin/giftcards 管理礼品卡')
    
    if (availableGiftCard) {
      console.log(`4. 使用礼品卡码测试: ${availableGiftCard.cardCode}`)
    }
    
    if (availableRedemptionCodes.length > 0) {
      console.log(`5. 使用兑换码测试: ${availableRedemptionCodes[0].codeValue}`)
    }

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
comprehensiveTest()
