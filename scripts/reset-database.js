const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function resetDatabase() {
  try {
    console.log('🗑️  开始重置数据库...')

    // 定义要删除的表，按照外键依赖关系的逆序
    const tablesToReset = [
      // 最后删除的表（依赖最少）
      { name: 'notification', model: 'notification' },
      { name: 'fundTransaction', model: 'fundTransaction' },
      { name: 'guaranteeTransaction', model: 'guaranteeTransaction' },
      { name: 'blockchainTransaction', model: 'blockchainTransaction' },
      { name: 'mediatorReward', model: 'mediatorReward' },
      { name: 'withdrawalVoucher', model: 'withdrawalVoucher' },
      { name: 'redemptionTransaction', model: 'redemptionTransaction' },
      { name: 'giftCardTransaction', model: 'giftCardTransaction' },
      { name: 'escrowChatMessage', model: 'escrowChatMessage' },
      { name: 'escrowChatRoom', model: 'escrowChatRoom' },
      { name: 'escrowDispute', model: 'escrowDispute' },
      { name: 'arbitrationVote', model: 'arbitrationVote' },
      { name: 'arbitrationCase', model: 'arbitrationCase' },
      { name: 'escrowTransaction', model: 'escrowTransaction' },
      { name: 'depositOperation', model: 'depositOperation' },
      { name: 'settlementItem', model: 'settlementItem' },
      { name: 'batchSettlement', model: 'batchSettlement' },
      { name: 'fundFreeze', model: 'fundFreeze' },
      { name: 'withdrawal', model: 'withdrawal' },
      { name: 'depositRecord', model: 'depositRecord' },
      { name: 'escrowPayment', model: 'escrowPayment' },
      { name: 'paymentPinVerification', model: 'paymentPinVerification' },
      { name: 'disputeReport', model: 'disputeReport' },
      { name: 'escrowOrder', model: 'escrowOrder' },
      { name: 'mediatorApplication', model: 'mediatorApplication' },
      { name: 'mediatorVerification', model: 'mediatorVerification' },
      { name: 'mediatorCommittee', model: 'mediatorCommittee' },
      { name: 'guarantorApplication', model: 'guarantorApplication' },
      { name: 'adminNote', model: 'adminNote' },
      { name: 'orderLog', model: 'orderLog' },
      { name: 'orderItem', model: 'orderItem' },
      { name: 'order', model: 'order' },
      { name: 'demandOffer', model: 'demandOffer' },
      { name: 'demand', model: 'demand' },
      { name: 'message', model: 'message' },
      { name: 'review', model: 'review' },
      { name: 'favorite', model: 'favorite' },
      { name: 'variantAttribute', model: 'variantAttribute' },
      { name: 'productVariant', model: 'productVariant' },
      { name: 'product', model: 'product' },
      { name: 'giftCard', model: 'giftCard' },
      { name: 'giftCardOrder', model: 'giftCardOrder' },
      { name: 'giftCardProduct', model: 'giftCardProduct' },
      { name: 'redemptionCode', model: 'redemptionCode' },
      { name: 'rewardCoupon', model: 'rewardCoupon' },
      { name: 'helpMediaFile', model: 'helpMediaFile' },
      { name: 'helpArticle', model: 'helpArticle' },
      { name: 'announcement', model: 'announcement' },
      { name: 'userFeedback', model: 'userFeedback' },
      { name: 'creditHistory', model: 'creditHistory' },
      { name: 'securityLog', model: 'securityLog' },
      { name: 'userSession', model: 'userSession' },
      { name: 'address', model: 'address' },
      { name: 'account', model: 'account' },
      { name: 'session', model: 'session' },
      { name: 'user', model: 'user' },
      // 系统配置表
      { name: 'systemSetting', model: 'systemSetting' },
      { name: 'systemConfig', model: 'systemConfig' },
      { name: 'feeConfig', model: 'feeConfig' },
      { name: 'guaranteeLevel', model: 'guaranteeLevel' },
      { name: 'walletConfig', model: 'walletConfig' },
      { name: 'blacklistedWallet', model: 'blacklistedWallet' },
      { name: 'riskAssessmentLog', model: 'riskAssessmentLog' },
      { name: 'searchKeyword', model: 'searchKeyword' },
      { name: 'fundPoolStats', model: 'fundPoolStats' }
    ]

    // 逐个删除表数据
    for (const table of tablesToReset) {
      try {
        console.log(`删除 ${table.name} 数据...`)
        if (prisma[table.model] && typeof prisma[table.model].deleteMany === 'function') {
          await prisma[table.model].deleteMany({})
        } else {
          console.log(`⚠️  跳过 ${table.name}（表不存在或无deleteMany方法）`)
        }
      } catch (error) {
        console.log(`⚠️  删除 ${table.name} 失败: ${error.message}`)
        // 继续删除其他表，不中断整个过程
      }
    }

    console.log('✅ 数据库重置完成！')
    console.log('📊 所有数据已清空，表结构保持不变')

  } catch (error) {
    console.error('❌ 重置数据库失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log('🎉 数据库重置成功完成！')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 数据库重置失败:', error)
      process.exit(1)
    })
}

module.exports = { resetDatabase }
