const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixMediatorConsoleDisplay() {
  console.log('🔧 修复中间人控制台显示问题...\n')

  try {
    // 1. 检查并修复数据库中的中间人数据
    console.log('1. 检查并修复数据库中的中间人数据...')
    
    const users = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true
      }
    })

    console.log(`✅ 找到 ${users.length} 个中间人用户`)
    
    for (const user of users) {
      console.log(`\n   检查用户: ${user.name} (${user.email})`)
      
      let needsUpdate = false
      const updateData = {}
      
      // 确保必要字段有默认值
      if (user.mediatorReputation === null || user.mediatorReputation === undefined) {
        updateData.mediatorReputation = 0
        needsUpdate = true
        console.log('     - 设置默认信誉值: 0')
      }
      
      if (user.mediatorSuccessRate === null || user.mediatorSuccessRate === undefined) {
        updateData.mediatorSuccessRate = 0
        needsUpdate = true
        console.log('     - 设置默认成功率: 0')
      }
      
      if (user.mediatorTotalOrders === null || user.mediatorTotalOrders === undefined) {
        updateData.mediatorTotalOrders = 0
        needsUpdate = true
        console.log('     - 设置默认订单数: 0')
      }
      
      if (user.mediatorFeeRate === null || user.mediatorFeeRate === undefined) {
        updateData.mediatorFeeRate = 0.025 // 2.5%
        needsUpdate = true
        console.log('     - 设置默认费率: 2.5%')
      }
      
      if (needsUpdate) {
        await prisma.user.update({
          where: { id: user.id },
          data: updateData
        })
        console.log('     ✅ 数据已更新')
      } else {
        console.log('     ✅ 数据完整，无需更新')
      }
    }

    // 2. 确保有测试用户
    console.log('\n2. 确保有测试用户...')
    
    let testMediatorUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testMediatorUser) {
      console.log('   创建测试中间人用户...')
      testMediatorUser = await prisma.user.create({
        data: {
          name: '测试中间人',
          email: '<EMAIL>',
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.025,
          mediatorReputation: 95.5,
          mediatorVerifiedAt: new Date(),
          bnbWalletVerified: true,
          mediatorExperience: '具有丰富的交易调解经验',
          mediatorIntroduction: '专业的交易调解员',
          mediatorSuccessRate: 98.5,
          mediatorTotalOrders: 156,
          depositBalance: 10000,
          creditScore: 95,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testMediatorUser.name}`)
    } else {
      // 确保测试用户数据完整
      await prisma.user.update({
        where: { id: testMediatorUser.id },
        data: {
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.025,
          mediatorReputation: 95.5,
          mediatorSuccessRate: 98.5,
          mediatorTotalOrders: 156,
          bnbWalletVerified: true
        }
      })
      console.log(`   ✅ 更新测试用户: ${testMediatorUser.name}`)
    }

    // 3. 创建普通用户用于对比测试
    let testNormalUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testNormalUser) {
      console.log('   创建测试普通用户...')
      testNormalUser = await prisma.user.create({
        data: {
          name: '测试普通用户',
          email: '<EMAIL>',
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          depositBalance: 1000,
          creditScore: 80,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testNormalUser.name}`)
    } else {
      await prisma.user.update({
        where: { id: testNormalUser.id },
        data: {
          isMediator: false,
          mediatorStatus: 'INACTIVE'
        }
      })
      console.log(`   ✅ 更新普通用户: ${testNormalUser.name}`)
    }

    // 4. 验证API接口
    console.log('\n3. 验证API接口...')
    
    try {
      const fetch = require('node-fetch')
      
      // 模拟API调用（需要实际的session token，这里只是检查接口存在）
      const response = await fetch('http://localhost:3000/api/user/profile')
      console.log(`   API接口状态: ${response.status}`)
      
      if (response.status === 401) {
        console.log('   ✅ API权限控制正常 (需要登录)')
      } else {
        console.log('   ⚠️  API可能有问题，请检查')
      }
    } catch (error) {
      console.log(`   ⚠️  API测试跳过: ${error.message}`)
    }

    // 5. 生成测试报告
    console.log('\n4. 生成测试报告...')
    
    const finalUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      select: {
        id: true,
        name: true,
        email: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true
      }
    })

    console.log('✅ 测试用户状态:')
    finalUsers.forEach(user => {
      console.log(`\n   ${user.name} (${user.email}):`)
      console.log(`     - isMediator: ${user.isMediator}`)
      console.log(`     - mediatorStatus: ${user.mediatorStatus}`)
      console.log(`     - mediatorReputation: ${user.mediatorReputation}`)
      console.log(`     - mediatorSuccessRate: ${user.mediatorSuccessRate}`)
      console.log(`     - mediatorTotalOrders: ${user.mediatorTotalOrders}`)
      console.log(`     - 应显示控制台: ${user.isMediator ? '是' : '否'}`)
    })

    console.log('\n🎉 修复完成！')
    console.log('\n📝 测试步骤:')
    console.log('\n【步骤1: 测试中间人用户】')
    console.log('1. 使用中间人账户登录:')
    console.log('   - 访问: http://localhost:3000/auth/signin')
    console.log('   - 邮箱: <EMAIL>')
    console.log('   - 密码: 123456 (或您设置的密码)')
    
    console.log('\n2. 访问调试页面验证数据:')
    console.log('   - 访问: http://localhost:3000/debug-profile')
    console.log('   - 检查 isMediator 字段是否为 true')
    console.log('   - 检查中间人相关字段是否有值')
    console.log('   - 查看"条件渲染测试"部分是否显示中间人控制台')
    
    console.log('\n3. 访问个人资料页面:')
    console.log('   - 访问: http://localhost:3000/profile')
    console.log('   - 查看是否显示橙色边框的中间人控制台卡片')
    console.log('   - 检查浏览器控制台的调试日志')
    
    console.log('\n【步骤2: 测试普通用户】')
    console.log('1. 使用普通用户账户登录:')
    console.log('   - 邮箱: <EMAIL>')
    console.log('   - 密码: 123456')
    
    console.log('\n2. 访问个人资料页面:')
    console.log('   - 确认不显示中间人控制台卡片')
    
    console.log('\n💡 故障排除:')
    console.log('   - 如果仍然不显示，请检查浏览器控制台的JavaScript错误')
    console.log('   - 确认网络请求成功返回用户数据')
    console.log('   - 检查条件渲染逻辑的调试日志')
    console.log('   - 清除浏览器缓存并刷新页面')

  } catch (error) {
    console.error('❌ 修复失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行修复
if (require.main === module) {
  fixMediatorConsoleDisplay().catch(console.error)
}

module.exports = { fixMediatorConsoleDisplay }
