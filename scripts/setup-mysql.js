/**
 * MySQL 数据库设置脚本
 * 帮助用户安装和配置MySQL数据库
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

function showMySQLSetupGuide() {
  console.log('🐬 MySQL 数据库设置指南')
  console.log('='.repeat(50))
  
  console.log('\n📋 当前配置:')
  console.log('  数据库类型: MySQL')
  console.log('  主机地址: localhost')
  console.log('  端口: 3306')
  console.log('  用户名: root')
  console.log('  数据库名: bitmarket')
  
  console.log('\n🔧 安装 MySQL:')
  console.log('\n1️⃣ Windows 安装:')
  console.log('   • 下载 MySQL Installer: https://dev.mysql.com/downloads/installer/')
  console.log('   • 运行安装程序，选择 "Developer Default"')
  console.log('   • 设置 root 密码 (建议使用: password)')
  console.log('   • 完成安装后启动 MySQL 服务')
  
  console.log('\n2️⃣ macOS 安装 (使用 Homebrew):')
  console.log('   brew install mysql')
  console.log('   brew services start mysql')
  console.log('   mysql_secure_installation')
  
  console.log('\n3️⃣ Ubuntu/Debian 安装:')
  console.log('   sudo apt update')
  console.log('   sudo apt install mysql-server')
  console.log('   sudo mysql_secure_installation')
  
  console.log('\n🗃️ 创建数据库:')
  console.log('   mysql -u root -p')
  console.log('   CREATE DATABASE bitmarket;')
  console.log('   SHOW DATABASES;')
  console.log('   EXIT;')
  
  console.log('\n⚡ 下一步操作:')
  console.log('   1. 安装 MySQL 服务器')
  console.log('   2. 创建 bitmarket 数据库')
  console.log('   3. 运行: npm install mysql2')
  console.log('   4. 运行: npx prisma migrate dev')
  console.log('   5. 运行: node scripts/setup-mysql.js test')
}

async function testMySQLConnection() {
  console.log('\n🔍 测试 MySQL 连接...')
  
  try {
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()
    
    console.log('正在连接 MySQL 数据库...')
    
    await prisma.$connect()
    console.log('✅ MySQL 连接成功!')
    
    // 尝试查询
    const result = await prisma.$queryRaw`SELECT VERSION() as version`
    console.log(`📊 MySQL 版本: ${result[0].version}`)
    
    await prisma.$disconnect()
    
  } catch (error) {
    console.error('❌ MySQL 连接失败:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 解决方案:')
      console.log('1. 确保 MySQL 服务正在运行')
      console.log('   Windows: 服务管理器中启动 MySQL80')
      console.log('   macOS: brew services start mysql')
      console.log('   Linux: sudo systemctl start mysql')
      
    } else if (error.message.includes('Access denied')) {
      console.log('\n💡 解决方案:')
      console.log('1. 检查用户名和密码是否正确')
      console.log('2. 确保用户有访问权限')
      console.log('3. 尝试重置 root 密码')
      
    } else if (error.message.includes('Unknown database')) {
      console.log('\n💡 解决方案:')
      console.log('1. 创建 bitmarket 数据库:')
      console.log('   mysql -u root -p')
      console.log('   CREATE DATABASE bitmarket;')
      
    } else if (error.message.includes('mysql2')) {
      console.log('\n💡 解决方案:')
      console.log('1. 安装 MySQL 驱动:')
      console.log('   npm install mysql2')
    }
  }
}

async function installMySQLDriver() {
  console.log('\n📦 安装 MySQL 驱动...')
  
  return new Promise((resolve, reject) => {
    const install = spawn('npm', ['install', 'mysql2'], {
      stdio: 'inherit',
      shell: true
    })
    
    install.on('close', (code) => {
      if (code === 0) {
        console.log('✅ MySQL 驱动安装成功!')
        resolve()
      } else {
        console.log('❌ MySQL 驱动安装失败')
        reject(new Error('安装失败'))
      }
    })
  })
}

async function runMigration() {
  console.log('\n🔄 运行数据库迁移...')
  
  return new Promise((resolve, reject) => {
    const migrate = spawn('npx', ['prisma', 'migrate', 'dev', '--name', 'switch-to-mysql'], {
      stdio: 'inherit',
      shell: true
    })
    
    migrate.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 数据库迁移完成!')
        resolve()
      } else {
        console.log('❌ 数据库迁移失败')
        reject(new Error('迁移失败'))
      }
    })
  })
}

async function generatePrismaClient() {
  console.log('\n🔧 生成 Prisma 客户端...')
  
  return new Promise((resolve, reject) => {
    const generate = spawn('npx', ['prisma', 'generate'], {
      stdio: 'inherit',
      shell: true
    })
    
    generate.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Prisma 客户端生成完成!')
        resolve()
      } else {
        console.log('❌ Prisma 客户端生成失败')
        reject(new Error('生成失败'))
      }
    })
  })
}

async function setupMySQL() {
  console.log('\n🚀 自动设置 MySQL...')
  
  try {
    // 1. 安装 MySQL 驱动
    await installMySQLDriver()
    
    // 2. 生成 Prisma 客户端
    await generatePrismaClient()
    
    // 3. 运行迁移
    await runMigration()
    
    // 4. 测试连接
    await testMySQLConnection()
    
    console.log('\n🎉 MySQL 设置完成!')
    console.log('\n📋 配置总结:')
    console.log('  ✅ MySQL 驱动已安装')
    console.log('  ✅ Prisma 客户端已生成')
    console.log('  ✅ 数据库迁移已完成')
    console.log('  ✅ 数据库连接正常')
    
  } catch (error) {
    console.error('\n❌ 设置过程中出现错误:', error.message)
    console.log('\n💡 请手动执行以下步骤:')
    console.log('1. npm install mysql2')
    console.log('2. npx prisma generate')
    console.log('3. npx prisma migrate dev')
    console.log('4. node scripts/setup-mysql.js test')
  }
}

function createMySQLCommands() {
  const commands = `
-- MySQL 数据库设置命令

-- 1. 连接到 MySQL
mysql -u root -p

-- 2. 创建数据库
CREATE DATABASE bitmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 创建专用用户 (可选)
CREATE USER 'bitmarket_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON bitmarket.* TO 'bitmarket_user'@'localhost';
FLUSH PRIVILEGES;

-- 4. 查看数据库
SHOW DATABASES;

-- 5. 使用数据库
USE bitmarket;

-- 6. 查看表
SHOW TABLES;

-- 7. 退出
EXIT;
`

  const sqlFile = path.join(process.cwd(), 'setup-mysql.sql')
  fs.writeFileSync(sqlFile, commands)
  console.log(`\n📄 MySQL 命令已保存到: ${sqlFile}`)
}

async function main() {
  const command = process.argv[2]
  
  switch (command) {
    case 'guide':
      showMySQLSetupGuide()
      break
      
    case 'test':
      await testMySQLConnection()
      break
      
    case 'install':
      await installMySQLDriver()
      break
      
    case 'migrate':
      await runMigration()
      break
      
    case 'setup':
      await setupMySQL()
      break
      
    case 'commands':
      createMySQLCommands()
      break
      
    default:
      console.log('🐬 MySQL 设置工具')
      console.log('='.repeat(30))
      console.log('使用方法:')
      console.log('  node setup-mysql.js guide     # 显示设置指南')
      console.log('  node setup-mysql.js test      # 测试连接')
      console.log('  node setup-mysql.js install   # 安装驱动')
      console.log('  node setup-mysql.js migrate   # 运行迁移')
      console.log('  node setup-mysql.js setup     # 自动设置')
      console.log('  node setup-mysql.js commands  # 生成SQL命令')
      console.log('')
      console.log('推荐步骤:')
      console.log('1. node setup-mysql.js guide    # 查看安装指南')
      console.log('2. 安装 MySQL 服务器')
      console.log('3. node setup-mysql.js setup    # 自动配置')
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  testMySQLConnection,
  installMySQLDriver,
  runMigration,
  setupMySQL
}
