#!/usr/bin/env node

const { spawn } = require('child_process')
const { performance } = require('perf_hooks')
const os = require('os')

function monitorCompilation() {
  console.log('📊 监控编译性能...')

  const startTime = performance.now()

  // Windows兼容性处理
  const isWindows = os.platform() === 'win32'
  const npmCommand = isWindows ? 'npm.cmd' : 'npm'

  const dev = spawn(npmCommand, ['run', 'dev'], {
    stdio: 'pipe',
    shell: isWindows
  })
  
  let compilationTimes = []
  
  dev.stdout.on('data', (data) => {
    const output = data.toString()
    
    // 检测编译时间
    const compileMatch = output.match(/✓ Compiled .+ in (\d+(?:\.\d+)?)([ms]+)/g)
    if (compileMatch) {
      compileMatch.forEach(match => {
        const timeMatch = match.match(/(\d+(?:\.\d+)?)([ms]+)/)
        if (timeMatch) {
          const time = parseFloat(timeMatch[1])
          const unit = timeMatch[2]
          const timeInMs = unit === 's' ? time * 1000 : time
          
          compilationTimes.push(timeInMs)
          
          if (timeInMs > 5000) {
            console.log(`⚠️  慢编译检测: ${timeInMs}ms`)
          }
        }
      })
    }
    
    // 输出原始日志
    process.stdout.write(data)
  })
  
  dev.stderr.on('data', (data) => {
    process.stderr.write(data)
  })
  
  // 定期报告编译性能
  setInterval(() => {
    if (compilationTimes.length > 0) {
      const avgTime = compilationTimes.reduce((a, b) => a + b, 0) / compilationTimes.length
      const maxTime = Math.max(...compilationTimes)
      
      console.log(`\n📊 编译性能统计:`)
      console.log(`   平均编译时间: ${avgTime.toFixed(2)}ms`)
      console.log(`   最长编译时间: ${maxTime.toFixed(2)}ms`)
      console.log(`   编译次数: ${compilationTimes.length}`)
      
      // 清空统计
      compilationTimes = []
    }
  }, 60000) // 每分钟报告一次
}

if (require.main === module) {
  monitorCompilation()
}
