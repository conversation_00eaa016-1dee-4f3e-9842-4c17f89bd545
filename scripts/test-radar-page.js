const fetch = require('node-fetch')

async function testRadarPage() {
  console.log('🔍 测试 /admin/radar 页面功能...\n')

  const baseUrl = 'http://localhost:3000'

  try {
    // 1. 测试反馈统计API
    console.log('1. 测试反馈统计API...')
    try {
      const statsResponse = await fetch(`${baseUrl}/api/admin/feedback/stats`)
      console.log(`   状态码: ${statsResponse.status}`)
      
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        console.log('✅ 统计API正常')
        console.log(`   - 总反馈: ${statsData.stats?.total || 0}`)
        console.log(`   - 待处理: ${statsData.stats?.pending || 0}`)
        console.log(`   - 已解决: ${statsData.stats?.resolved || 0}`)
      } else {
        const errorData = await statsResponse.json()
        console.log(`❌ 统计API错误: ${errorData.error}`)
      }
    } catch (error) {
      console.log(`❌ 统计API请求失败: ${error.message}`)
    }

    // 2. 测试反馈列表API
    console.log('\n2. 测试反馈列表API...')
    try {
      const listResponse = await fetch(`${baseUrl}/api/admin/feedback?page=1&limit=10`)
      console.log(`   状态码: ${listResponse.status}`)
      
      if (listResponse.ok) {
        const listData = await listResponse.json()
        console.log('✅ 列表API正常')
        console.log(`   - 反馈数量: ${listData.feedbacks?.length || 0}`)
        console.log(`   - 总数: ${listData.pagination?.total || 0}`)
        
        if (listData.feedbacks && listData.feedbacks.length > 0) {
          console.log('   - 前3条反馈:')
          listData.feedbacks.slice(0, 3).forEach((feedback, index) => {
            console.log(`     ${index + 1}. ${feedback.title} - ${feedback.status}`)
          })
        }
      } else {
        const errorData = await listResponse.json()
        console.log(`❌ 列表API错误: ${errorData.error}`)
      }
    } catch (error) {
      console.log(`❌ 列表API请求失败: ${error.message}`)
    }

    // 3. 测试页面访问
    console.log('\n3. 测试页面访问...')
    try {
      const pageResponse = await fetch(`${baseUrl}/admin/radar`)
      console.log(`   状态码: ${pageResponse.status}`)
      
      if (pageResponse.ok) {
        const pageContent = await pageResponse.text()
        if (pageContent.includes('反馈管理') || pageContent.includes('radar')) {
          console.log('✅ 页面可以访问')
        } else {
          console.log('⚠️  页面内容可能有问题')
        }
      } else {
        console.log(`❌ 页面访问失败: ${pageResponse.status}`)
      }
    } catch (error) {
      console.log(`❌ 页面访问请求失败: ${error.message}`)
    }

    // 4. 检查数据库连接
    console.log('\n4. 检查数据库状态...')
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()
    
    try {
      const feedbackCount = await prisma.userFeedback.count()
      const adminCount = await prisma.user.count({ where: { role: 'ADMIN' } })
      
      console.log('✅ 数据库连接正常')
      console.log(`   - 反馈总数: ${feedbackCount}`)
      console.log(`   - 管理员数: ${adminCount}`)
      
      await prisma.$disconnect()
    } catch (error) {
      console.log(`❌ 数据库连接失败: ${error.message}`)
    }

    console.log('\n🎉 测试完成！')
    console.log('\n📝 访问信息:')
    console.log('   1. 先登录管理员账号: http://localhost:3000/auth/signin')
    console.log('      邮箱: <EMAIL> 或 <EMAIL>')
    console.log('      密码: admin123456 或 123456')
    console.log('   2. 然后访问反馈管理: http://localhost:3000/admin/radar')
    console.log('\n💡 如果页面仍然无法显示，请检查:')
    console.log('   - 是否已登录管理员账号')
    console.log('   - 浏览器控制台是否有错误信息')
    console.log('   - 网络请求是否正常')

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

// 运行测试
if (require.main === module) {
  testRadarPage().catch(console.error)
}

module.exports = { testRadarPage }
