#!/usr/bin/env node

const http = require('http')
const { performance } = require('perf_hooks')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()
const PORT = 3001

// 性能监控数据
const performanceData = {
  startTime: Date.now(),
  metrics: {
    memory: { rss: 0, heapUsed: 0, heapTotal: 0 },
    requests: { total: 0, success: 0, error: 0, avgTime: 0 },
    database: { queries: 0, avgTime: 0, errors: 0 },
    alerts: []
  },
  history: []
}

// 收集系统指标
function collectMetrics() {
  const memUsage = process.memoryUsage()
  performanceData.metrics.memory = {
    rss: Math.round(memUsage.rss / 1024 / 1024),
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
    timestamp: Date.now()
  }
  
  // 保存历史数据
  performanceData.history.push({
    timestamp: Date.now(),
    memory: performanceData.metrics.memory,
    requests: { ...performanceData.metrics.requests }
  })
  
  // 只保留最近100条记录
  if (performanceData.history.length > 100) {
    performanceData.history = performanceData.history.slice(-100)
  }
  
  // 检查告警
  checkAlerts()
}

// 检查告警
function checkAlerts() {
  const alerts = []
  const memory = performanceData.metrics.memory
  
  if (memory.rss > 500) {
    alerts.push({
      type: 'memory',
      level: 'warning',
      message: `内存使用过高: ${memory.rss}MB`,
      timestamp: Date.now()
    })
  }
  
  if (performanceData.metrics.requests.avgTime > 1000) {
    alerts.push({
      type: 'response_time',
      level: 'warning',
      message: `响应时间过长: ${performanceData.metrics.requests.avgTime}ms`,
      timestamp: Date.now()
    })
  }
  
  // 添加新告警
  alerts.forEach(alert => {
    performanceData.metrics.alerts.unshift(alert)
    console.log(`🚨 ${alert.level.toUpperCase()}: ${alert.message}`)
  })
  
  // 只保留最近20条告警
  if (performanceData.metrics.alerts.length > 20) {
    performanceData.metrics.alerts = performanceData.metrics.alerts.slice(0, 20)
  }
}

// 记录请求
function recordRequest(responseTime, statusCode) {
  const requests = performanceData.metrics.requests
  requests.total++
  
  if (statusCode >= 200 && statusCode < 400) {
    requests.success++
  } else {
    requests.error++
  }
  
  // 更新平均响应时间
  requests.avgTime = Math.round(
    (requests.avgTime * (requests.total - 1) + responseTime) / requests.total
  )
}

// 生成HTML仪表板
function generateDashboard() {
  const uptime = Date.now() - performanceData.startTime
  const uptimeStr = formatUptime(uptime)
  const memory = performanceData.metrics.memory
  const requests = performanceData.metrics.requests
  const alerts = performanceData.metrics.alerts
  
  const errorRate = requests.total > 0 ? 
    Math.round((requests.error / requests.total) * 100) : 0
  
  const status = getSystemStatus()
  const statusColor = {
    'healthy': '#10b981',
    'warning': '#f59e0b', 
    'critical': '#ef4444'
  }[status]
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 性能监控</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .header { background: #2563eb; color: white; padding: 1rem; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; }
        .card { background: white; border-radius: 8px; padding: 1.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card h3 { color: #1f2937; margin-bottom: 1rem; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 0.5rem; }
        .metric-value { font-weight: bold; }
        .status { padding: 0.5rem 1rem; border-radius: 20px; color: white; font-weight: bold; }
        .alert { padding: 0.75rem; margin-bottom: 0.5rem; border-radius: 4px; background: #fef3c7; border-left: 4px solid #f59e0b; }
        .refresh-btn { background: #2563eb; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
        .chart { height: 100px; background: #f9fafb; border-radius: 4px; margin-top: 1rem; position: relative; overflow: hidden; }
        .chart-bar { position: absolute; bottom: 0; background: #2563eb; width: 2px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 BitMarket 性能监控仪表板</h1>
        <p>实时监控系统性能和健康状态</p>
    </div>
    
    <div class="container">
        <div style="text-align: center; margin-bottom: 2rem;">
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>
            <span style="margin-left: 1rem; color: #6b7280;">最后更新: ${new Date().toLocaleTimeString()}</span>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🏥 系统状态</h3>
                <div class="status" style="background-color: ${statusColor}; text-align: center;">
                    ${status === 'healthy' ? '健康 ✅' : status === 'warning' ? '警告 ⚠️' : '严重 🚨'}
                </div>
                <div class="metric">
                    <span>运行时间:</span>
                    <span class="metric-value">${uptimeStr}</span>
                </div>
                <div class="metric">
                    <span>启动时间:</span>
                    <span class="metric-value">${new Date(performanceData.startTime).toLocaleString()}</span>
                </div>
            </div>
            
            <div class="card">
                <h3>💾 内存使用</h3>
                <div class="metric">
                    <span>RSS内存:</span>
                    <span class="metric-value">${memory.rss}MB</span>
                </div>
                <div class="metric">
                    <span>堆内存:</span>
                    <span class="metric-value">${memory.heapUsed}MB / ${memory.heapTotal}MB</span>
                </div>
                <div class="metric">
                    <span>堆使用率:</span>
                    <span class="metric-value">${Math.round((memory.heapUsed / memory.heapTotal) * 100)}%</span>
                </div>
                <div class="chart">
                    ${generateMemoryChart()}
                </div>
            </div>
            
            <div class="card">
                <h3>🌐 请求统计</h3>
                <div class="metric">
                    <span>总请求数:</span>
                    <span class="metric-value">${requests.total}</span>
                </div>
                <div class="metric">
                    <span>成功/失败:</span>
                    <span class="metric-value">${requests.success} / ${requests.error}</span>
                </div>
                <div class="metric">
                    <span>错误率:</span>
                    <span class="metric-value">${errorRate}%</span>
                </div>
                <div class="metric">
                    <span>平均响应时间:</span>
                    <span class="metric-value">${requests.avgTime}ms</span>
                </div>
            </div>
            
            <div class="card">
                <h3>🗄️ 数据库</h3>
                <div class="metric">
                    <span>查询总数:</span>
                    <span class="metric-value">${performanceData.metrics.database.queries}</span>
                </div>
                <div class="metric">
                    <span>平均查询时间:</span>
                    <span class="metric-value">${performanceData.metrics.database.avgTime}ms</span>
                </div>
                <div class="metric">
                    <span>数据库错误:</span>
                    <span class="metric-value">${performanceData.metrics.database.errors}</span>
                </div>
                <div class="metric">
                    <span>连接状态:</span>
                    <span class="metric-value">正常 ✅</span>
                </div>
            </div>
            
            <div class="card">
                <h3>🚨 系统告警</h3>
                ${alerts.length > 0 ? 
                  alerts.slice(0, 5).map(alert => `
                    <div class="alert">
                      <strong>${alert.type.toUpperCase()}:</strong> ${alert.message}
                      <br><small>${new Date(alert.timestamp).toLocaleString()}</small>
                    </div>
                  `).join('') : 
                  '<p style="color: #10b981;">✅ 无活跃告警</p>'
                }
            </div>
            
            <div class="card">
                <h3>📊 性能指标</h3>
                <div class="metric">
                    <span>CPU使用率:</span>
                    <span class="metric-value">监控中...</span>
                </div>
                <div class="metric">
                    <span>磁盘I/O:</span>
                    <span class="metric-value">正常</span>
                </div>
                <div class="metric">
                    <span>网络延迟:</span>
                    <span class="metric-value">${requests.avgTime}ms</span>
                </div>
                <div class="metric">
                    <span>系统负载:</span>
                    <span class="metric-value">${status === 'healthy' ? '低' : status === 'warning' ? '中' : '高'}</span>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 2rem; color: #6b7280;">
            <p>🔄 页面每30秒自动刷新 | 📊 数据每5秒更新</p>
        </div>
    </div>
    
    <script>
        // 自动刷新页面
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>
  `
}

// 生成内存使用图表
function generateMemoryChart() {
  const history = performanceData.history.slice(-50) // 最近50个数据点
  if (history.length === 0) return ''
  
  const maxMemory = Math.max(...history.map(h => h.memory.rss))
  const chartBars = history.map((h, i) => {
    const height = (h.memory.rss / maxMemory) * 100
    const left = (i / history.length) * 100
    return `<div class="chart-bar" style="left: ${left}%; height: ${height}%;"></div>`
  }).join('')
  
  return chartBars
}

// 获取系统状态
function getSystemStatus() {
  const memory = performanceData.metrics.memory
  const requests = performanceData.metrics.requests
  
  const memoryHigh = memory.rss > 500
  const responseTimeSlow = requests.avgTime > 1000
  const errorRateHigh = requests.total > 0 && (requests.error / requests.total) > 0.05
  
  if (memoryHigh && responseTimeSlow) return 'critical'
  if (memoryHigh || responseTimeSlow || errorRateHigh) return 'warning'
  return 'healthy'
}

// 格式化运行时间
function formatUptime(ms) {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天 ${hours % 24}小时`
  if (hours > 0) return `${hours}小时 ${minutes % 60}分钟`
  if (minutes > 0) return `${minutes}分钟 ${seconds % 60}秒`
  return `${seconds}秒`
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const startTime = performance.now()
  
  res.on('finish', () => {
    const responseTime = performance.now() - startTime
    recordRequest(responseTime, res.statusCode)
  })
  
  if (req.url === '/api/metrics') {
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({
      ...performanceData,
      timestamp: Date.now(),
      status: getSystemStatus()
    }))
  } else if (req.url === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({
      status: getSystemStatus(),
      timestamp: Date.now(),
      uptime: Date.now() - performanceData.startTime
    }))
  } else {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' })
    res.end(generateDashboard())
  }
})

// 启动监控
setInterval(collectMetrics, 5000) // 每5秒收集指标
collectMetrics() // 立即收集一次

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 性能监控仪表板已启动: http://localhost:${PORT}`)
  console.log('📊 实时监控系统性能和健康状态')
  console.log('🔍 监控指标: 内存使用、请求统计、响应时间、系统告警')
  console.log('🚨 自动告警: 内存过高(>500MB)、响应时间过长(>1000ms)')
  console.log('🔄 数据更新: 每5秒收集指标，页面每30秒自动刷新')
})

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭性能监控系统...')
  server.close()
  await prisma.$disconnect()
  console.log('✅ 性能监控系统已关闭')
  process.exit(0)
})
