const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdmin() {
  try {
    console.log('👤 创建管理员账户...')
    
    // 检查是否已存在管理员
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })
    
    if (existingAdmin) {
      console.log('⚠️  管理员账户已存在:', existingAdmin.email)
      return existingAdmin
    }
    
    // 创建管理员账户
    const hashedPassword = await bcrypt.hash('123456', 12)
    
    const admin = await prisma.user.create({
      data: {
        name: '系统管理员',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
        depositBalance: 100000, // 给管理员一些初始余额
        creditScore: 100,
        city: '北京',
        district: '朝阳区',
        status: 'ACTIVE'
      }
    })
    
    console.log('✅ 管理员账户创建成功!')
    console.log('📧 邮箱:', admin.email)
    console.log('🔑 密码: 123456')
    console.log('👤 姓名:', admin.name)
    console.log('🆔 ID:', admin.id)
    
    return admin
    
  } catch (error) {
    console.error('❌ 创建管理员账户失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createAdmin()
    .then(() => {
      console.log('🎉 管理员账户创建完成！')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 创建管理员账户失败:', error)
      process.exit(1)
    })
}

module.exports = { createAdmin }
