#!/usr/bin/env node

const { performance } = require('perf_hooks')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

class PerformanceStressTest {
  constructor() {
    this.results = {
      startTime: Date.now(),
      tests: {},
      summary: {}
    }
  }

  // 数据库性能测试
  async testDatabasePerformance() {
    console.log('🗄️  开始数据库性能测试...')
    
    const tests = {
      simpleQuery: async () => {
        const start = performance.now()
        await prisma.user.count()
        return performance.now() - start
      },
      
      complexQuery: async () => {
        const start = performance.now()
        await prisma.product.findMany({
          where: { status: 'AVAILABLE' },
          include: { seller: true },
          take: 10
        })
        return performance.now() - start
      },
      
      aggregateQuery: async () => {
        const start = performance.now()
        await prisma.product.groupBy({
          by: ['status'],
          _count: { id: true }
        })
        return performance.now() - start
      },
      
      insertTest: async () => {
        const start = performance.now()
        const testUser = await prisma.user.create({
          data: {
            id: `test-${Date.now()}`,
            name: 'Test User',
            email: `test-${Date.now()}@example.com`,
            city: '测试城市',
            creditScore: 100
          }
        })
        const time = performance.now() - start
        
        // 清理测试数据
        await prisma.user.delete({ where: { id: testUser.id } })
        return time
      }
    }

    const dbResults = {}
    
    for (const [testName, testFn] of Object.entries(tests)) {
      console.log(`  📊 执行 ${testName} 测试...`)
      
      const times = []
      const iterations = 10
      
      for (let i = 0; i < iterations; i++) {
        try {
          const time = await testFn()
          times.push(time)
        } catch (error) {
          console.error(`    ❌ 测试失败: ${error.message}`)
          times.push(null)
        }
      }
      
      const validTimes = times.filter(t => t !== null)
      dbResults[testName] = {
        iterations,
        successCount: validTimes.length,
        failureCount: times.length - validTimes.length,
        avgTime: validTimes.length > 0 ? validTimes.reduce((a, b) => a + b, 0) / validTimes.length : 0,
        minTime: validTimes.length > 0 ? Math.min(...validTimes) : 0,
        maxTime: validTimes.length > 0 ? Math.max(...validTimes) : 0,
        times: validTimes
      }
      
      console.log(`    ✅ 平均时间: ${dbResults[testName].avgTime.toFixed(2)}ms`)
    }
    
    this.results.tests.database = dbResults
    return dbResults
  }

  // 内存压力测试
  async testMemoryPerformance() {
    console.log('💾 开始内存性能测试...')
    
    const getMemoryUsage = () => {
      const usage = process.memoryUsage()
      return {
        rss: usage.rss / 1024 / 1024,
        heapTotal: usage.heapTotal / 1024 / 1024,
        heapUsed: usage.heapUsed / 1024 / 1024,
        external: usage.external / 1024 / 1024
      }
    }

    const memoryTests = {
      largeObjectCreation: async () => {
        const before = getMemoryUsage()
        const start = performance.now()
        
        // 创建大量对象
        const objects = []
        for (let i = 0; i < 100000; i++) {
          objects.push({
            id: i,
            data: new Array(100).fill(i),
            timestamp: Date.now()
          })
        }
        
        const after = getMemoryUsage()
        const time = performance.now() - start
        
        // 清理
        objects.length = 0
        if (global.gc) global.gc()
        
        const afterCleanup = getMemoryUsage()
        
        return {
          time,
          memoryBefore: before,
          memoryAfter: after,
          memoryAfterCleanup: afterCleanup,
          memoryGrowth: after.heapUsed - before.heapUsed,
          memoryRecovered: after.heapUsed - afterCleanup.heapUsed
        }
      },
      
      stringConcatenation: async () => {
        const before = getMemoryUsage()
        const start = performance.now()
        
        let largeString = ''
        for (let i = 0; i < 50000; i++) {
          largeString += `这是第${i}个字符串片段，用于测试内存使用情况。`
        }
        
        const after = getMemoryUsage()
        const time = performance.now() - start
        
        // 清理
        largeString = null
        if (global.gc) global.gc()
        
        const afterCleanup = getMemoryUsage()
        
        return {
          time,
          memoryBefore: before,
          memoryAfter: after,
          memoryAfterCleanup: afterCleanup,
          memoryGrowth: after.heapUsed - before.heapUsed,
          memoryRecovered: after.heapUsed - afterCleanup.heapUsed
        }
      }
    }

    const memoryResults = {}
    
    for (const [testName, testFn] of Object.entries(memoryTests)) {
      console.log(`  📊 执行 ${testName} 测试...`)
      
      try {
        const result = await testFn()
        memoryResults[testName] = result
        
        console.log(`    ✅ 执行时间: ${result.time.toFixed(2)}ms`)
        console.log(`    📈 内存增长: ${result.memoryGrowth.toFixed(2)}MB`)
        console.log(`    🧹 内存回收: ${result.memoryRecovered.toFixed(2)}MB`)
      } catch (error) {
        console.error(`    ❌ 测试失败: ${error.message}`)
        memoryResults[testName] = { error: error.message }
      }
    }
    
    this.results.tests.memory = memoryResults
    return memoryResults
  }

  // 并发性能测试
  async testConcurrentPerformance() {
    console.log('🔄 开始并发性能测试...')
    
    const concurrentTests = {
      databaseConcurrency: async (concurrency) => {
        const start = performance.now()
        const promises = []
        
        for (let i = 0; i < concurrency; i++) {
          promises.push(
            prisma.product.findMany({
              where: { status: 'AVAILABLE' },
              take: 5
            })
          )
        }
        
        const results = await Promise.allSettled(promises)
        const time = performance.now() - start
        
        const successful = results.filter(r => r.status === 'fulfilled').length
        const failed = results.filter(r => r.status === 'rejected').length
        
        return {
          time,
          concurrency,
          successful,
          failed,
          avgTimePerRequest: time / concurrency
        }
      },
      
      memoryAllocation: async (concurrency) => {
        const start = performance.now()
        const promises = []
        
        for (let i = 0; i < concurrency; i++) {
          promises.push(
            new Promise(resolve => {
              const data = new Array(10000).fill(i)
              setTimeout(() => {
                data.length = 0
                resolve(data)
              }, 10)
            })
          )
        }
        
        await Promise.all(promises)
        const time = performance.now() - start
        
        return {
          time,
          concurrency,
          avgTimePerTask: time / concurrency
        }
      }
    }

    const concurrentResults = {}
    const concurrencyLevels = [1, 5, 10, 20, 50]
    
    for (const [testName, testFn] of Object.entries(concurrentTests)) {
      console.log(`  📊 执行 ${testName} 测试...`)
      concurrentResults[testName] = {}
      
      for (const concurrency of concurrencyLevels) {
        console.log(`    🔄 并发级别: ${concurrency}`)
        
        try {
          const result = await testFn(concurrency)
          concurrentResults[testName][concurrency] = result
          
          console.log(`      ✅ 总时间: ${result.time.toFixed(2)}ms`)
          console.log(`      📊 平均时间: ${result.avgTimePerRequest?.toFixed(2) || result.avgTimePerTask?.toFixed(2)}ms`)
        } catch (error) {
          console.error(`      ❌ 测试失败: ${error.message}`)
          concurrentResults[testName][concurrency] = { error: error.message }
        }
      }
    }
    
    this.results.tests.concurrent = concurrentResults
    return concurrentResults
  }

  // 生成性能报告
  generateReport() {
    const endTime = Date.now()
    const totalTime = endTime - this.results.startTime
    
    console.log('\n📊 性能测试报告')
    console.log('='.repeat(60))
    console.log(`测试开始时间: ${new Date(this.results.startTime).toLocaleString()}`)
    console.log(`测试结束时间: ${new Date(endTime).toLocaleString()}`)
    console.log(`总测试时间: ${(totalTime / 1000).toFixed(2)}秒`)
    
    // 数据库性能总结
    if (this.results.tests.database) {
      console.log('\n🗄️  数据库性能总结:')
      Object.entries(this.results.tests.database).forEach(([testName, result]) => {
        console.log(`  ${testName}: 平均 ${result.avgTime.toFixed(2)}ms (${result.successCount}/${result.iterations} 成功)`)
      })
    }
    
    // 内存性能总结
    if (this.results.tests.memory) {
      console.log('\n💾 内存性能总结:')
      Object.entries(this.results.tests.memory).forEach(([testName, result]) => {
        if (!result.error) {
          console.log(`  ${testName}: ${result.time.toFixed(2)}ms, 内存增长 ${result.memoryGrowth.toFixed(2)}MB`)
        }
      })
    }
    
    // 并发性能总结
    if (this.results.tests.concurrent) {
      console.log('\n🔄 并发性能总结:')
      Object.entries(this.results.tests.concurrent).forEach(([testName, results]) => {
        console.log(`  ${testName}:`)
        Object.entries(results).forEach(([concurrency, result]) => {
          if (!result.error) {
            const avgTime = result.avgTimePerRequest || result.avgTimePerTask
            console.log(`    并发${concurrency}: 总时间 ${result.time.toFixed(2)}ms, 平均 ${avgTime.toFixed(2)}ms`)
          }
        })
      })
    }
    
    // 性能评级
    const rating = this.calculatePerformanceRating()
    console.log(`\n🏆 性能评级: ${rating.grade} (${rating.score}/100)`)
    console.log(`📝 评估说明: ${rating.description}`)
    
    this.results.summary = {
      totalTime,
      endTime,
      rating
    }
    
    return this.results
  }

  // 计算性能评级
  calculatePerformanceRating() {
    let score = 100
    let issues = []
    
    // 数据库性能评估
    if (this.results.tests.database) {
      Object.entries(this.results.tests.database).forEach(([testName, result]) => {
        if (result.avgTime > 100) {
          score -= 10
          issues.push(`${testName} 响应时间过长 (${result.avgTime.toFixed(2)}ms)`)
        }
        if (result.failureCount > 0) {
          score -= 15
          issues.push(`${testName} 有失败的查询`)
        }
      })
    }
    
    // 内存性能评估
    if (this.results.tests.memory) {
      Object.entries(this.results.tests.memory).forEach(([testName, result]) => {
        if (!result.error && result.memoryGrowth > 100) {
          score -= 10
          issues.push(`${testName} 内存使用过高 (${result.memoryGrowth.toFixed(2)}MB)`)
        }
        if (!result.error && result.memoryRecovered < result.memoryGrowth * 0.8) {
          score -= 5
          issues.push(`${testName} 内存回收不充分`)
        }
      })
    }
    
    // 确定等级
    let grade, description
    if (score >= 90) {
      grade = 'A+'
      description = '性能优秀，系统运行非常稳定'
    } else if (score >= 80) {
      grade = 'A'
      description = '性能良好，系统运行稳定'
    } else if (score >= 70) {
      grade = 'B'
      description = '性能一般，建议优化'
    } else if (score >= 60) {
      grade = 'C'
      description = '性能较差，需要优化'
    } else {
      grade = 'D'
      description = '性能很差，急需优化'
    }
    
    return {
      score: Math.max(0, score),
      grade,
      description,
      issues
    }
  }

  // 保存测试结果
  async saveResults() {
    try {
      const fs = require('fs').promises
      await fs.mkdir('test-results/performance', { recursive: true })
      
      const filename = `performance-test-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.writeFile(
        `test-results/performance/${filename}`,
        JSON.stringify(this.results, null, 2)
      )
      
      console.log(`\n💾 测试结果已保存: test-results/performance/${filename}`)
    } catch (error) {
      console.error('保存测试结果失败:', error)
    }
  }
}

// 运行性能压力测试
async function runPerformanceStressTest() {
  const tester = new PerformanceStressTest()
  
  try {
    console.log('🚀 开始性能压力测试')
    console.log('='.repeat(60))
    
    // 执行各项测试
    await tester.testDatabasePerformance()
    await tester.testMemoryPerformance()
    await tester.testConcurrentPerformance()
    
    // 生成报告
    const report = tester.generateReport()
    
    // 保存结果
    await tester.saveResults()
    
    console.log('\n✅ 性能压力测试完成')
    return report
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 命令行运行
if (require.main === module) {
  runPerformanceStressTest()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { PerformanceStressTest, runPerformanceStressTest }
