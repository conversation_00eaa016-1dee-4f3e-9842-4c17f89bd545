/**
 * 用户同步脚本
 * 确保NextAuth会话中的用户在数据库中存在
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function syncUsers() {
  console.log('🔄 开始用户同步...')
  console.log('='.repeat(40))

  try {
    // 1. 检查数据库连接
    await prisma.$connect()
    console.log('✅ 数据库连接成功')

    // 2. 检查现有用户
    const existingUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        password: true
      }
    })

    console.log(`📊 现有用户数量: ${existingUsers.length}`)

    // 3. 确保有测试用户存在
    const testUsers = [
      {
        email: '<EMAIL>',
        name: '测试用户',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.HL9S.' // password: "password"
      },
      {
        email: '<EMAIL>',
        name: '演示用户',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.HL9S.' // password: "password"
      }
    ]

    for (const testUser of testUsers) {
      const existingUser = existingUsers.find(u => u.email === testUser.email)
      
      if (!existingUser) {
        console.log(`🔧 创建测试用户: ${testUser.email}`)
        
        const newUser = await prisma.user.create({
          data: {
            email: testUser.email,
            name: testUser.name,
            password: testUser.password,
            depositBalance: 1000,
            creditScore: 750,
            status: 'ACTIVE'
          }
        })
        
        console.log(`   ✅ 用户创建成功: ${newUser.id}`)
      } else {
        console.log(`   ℹ️  用户已存在: ${testUser.email}`)
        
        // 确保用户有密码
        if (!existingUser.password) {
          await prisma.user.update({
            where: { id: existingUser.id },
            data: { password: testUser.password }
          })
          console.log(`   🔑 已为用户设置密码: ${testUser.email}`)
        }
      }
    }

    // 4. 检查所有用户是否有必需字段
    console.log('\n🔍 检查用户数据完整性...')
    
    const allUsers = await prisma.user.findMany()
    let fixedCount = 0

    for (const user of allUsers) {
      const updates = {}
      
      if (user.depositBalance === null || user.depositBalance === undefined) {
        updates.depositBalance = 0
      }
      
      if (user.creditScore === null || user.creditScore === undefined) {
        updates.creditScore = 30
      }
      
      if (!user.status) {
        updates.status = 'ACTIVE'
      }

      if (Object.keys(updates).length > 0) {
        await prisma.user.update({
          where: { id: user.id },
          data: updates
        })
        fixedCount++
        console.log(`   🔧 修复用户数据: ${user.email}`)
      }
    }

    if (fixedCount > 0) {
      console.log(`✅ 修复了 ${fixedCount} 个用户的数据`)
    } else {
      console.log('✅ 所有用户数据完整')
    }

    // 5. 显示最终统计
    const finalUserCount = await prisma.user.count()
    console.log(`\n📊 最终用户统计:`)
    console.log(`   总用户数: ${finalUserCount}`)
    
    const activeUsers = await prisma.user.count({
      where: { status: 'ACTIVE' }
    })
    console.log(`   活跃用户: ${activeUsers}`)

    const usersWithPassword = await prisma.user.count({
      where: { password: { not: null } }
    })
    console.log(`   有密码用户: ${usersWithPassword}`)

    // 6. 生成登录信息
    console.log('\n🔑 可用的测试账户:')
    console.log('─'.repeat(40))
    
    const testAccounts = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      },
      select: {
        email: true,
        name: true,
        id: true
      }
    })

    testAccounts.forEach(account => {
      console.log(`📧 邮箱: ${account.email}`)
      console.log(`👤 姓名: ${account.name}`)
      console.log(`🆔 ID: ${account.id}`)
      console.log(`🔐 密码: password`)
      console.log('')
    })

    console.log('🎉 用户同步完成!')

  } catch (error) {
    console.error('❌ 用户同步失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 创建用户修复API
async function createUserFixAPI() {
  console.log('\n🔧 创建用户修复API...')
  
  const apiContent = `/**
 * 用户修复API
 * 当用户在会话中存在但数据库中不存在时，自动创建用户记录
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (existingUser) {
      return NextResponse.json({
        message: '用户已存在',
        user: existingUser
      })
    }

    // 创建新用户记录
    const newUser = await prisma.user.create({
      data: {
        id: session.user.id,
        email: session.user.email || '',
        name: session.user.name || '新用户',
        depositBalance: 0,
        creditScore: 30,
        status: 'ACTIVE'
      }
    })

    return NextResponse.json({
      message: '用户记录已创建',
      user: newUser
    })

  } catch (error) {
    console.error('User fix error:', error)
    return NextResponse.json(
      { error: '修复用户记录失败' },
      { status: 500 }
    )
  }
}
`

  const fs = require('fs')
  const path = require('path')
  
  const apiDir = path.join(process.cwd(), 'app/api/user/fix')
  if (!fs.existsSync(apiDir)) {
    fs.mkdirSync(apiDir, { recursive: true })
  }
  
  const apiFile = path.join(apiDir, 'route.ts')
  fs.writeFileSync(apiFile, apiContent)
  
  console.log('✅ 用户修复API已创建: /api/user/fix')
}

// 运行同步
if (require.main === module) {
  syncUsers()
    .then(() => {
      return createUserFixAPI()
    })
    .then(() => {
      console.log('\n🎯 所有任务完成!')
      console.log('现在可以使用以下账户登录:')
      console.log('• <EMAIL> / password')
      console.log('• <EMAIL> / password')
    })
    .catch(error => {
      console.error('\n💥 同步失败:', error)
      process.exit(1)
    })
}

module.exports = { syncUsers, createUserFixAPI }
