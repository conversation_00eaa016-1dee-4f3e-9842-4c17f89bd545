#!/usr/bin/env ts-node

/**
 * 自动清理脚本
 * 用于定时清理过期的礼品卡和兑换券记录
 * 
 * 使用方法:
 * 1. 直接运行: npx ts-node scripts/cleanup-cron.ts
 * 2. 添加到 crontab: 0 2 * * * cd /path/to/project && npx ts-node scripts/cleanup-cron.ts
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

interface CleanupResult {
  type: string
  deletedRecords: number
  deletedTransactions: number
  expiredItems: any[]
}

async function cleanupGiftCardRecords(): Promise<CleanupResult> {
  console.log('开始清理礼品卡记录...')
  
  const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)

  // 查找需要清理的已兑换礼品卡
  const expiredGiftCards = await prisma.giftCard.findMany({
    where: {
      status: 'REDEEMED',
      redeemedAt: {
        lt: fifteenDaysAgo
      }
    },
    select: {
      id: true,
      cardCode: true,
      redeemedAt: true,
      faceValue: true
    }
  })

  console.log(`找到 ${expiredGiftCards.length} 张过期的已兑换礼品卡`)

  let deletedRecords = 0
  let deletedTransactions = 0

  if (expiredGiftCards.length > 0) {
    const giftCardIds = expiredGiftCards.map(card => card.id)

    // 删除相关的交易记录
    const deletedTransactionResult = await prisma.giftCardTransaction.deleteMany({
      where: {
        giftCardId: {
          in: giftCardIds
        }
      }
    })
    deletedTransactions = deletedTransactionResult.count
    console.log(`删除了 ${deletedTransactions} 条礼品卡交易记录`)

    // 删除礼品卡记录
    const deletedGiftCardResult = await prisma.giftCard.deleteMany({
      where: {
        id: {
          in: giftCardIds
        }
      }
    })
    deletedRecords = deletedGiftCardResult.count
    console.log(`删除了 ${deletedRecords} 张礼品卡记录`)
  }

  return {
    type: 'giftcard',
    deletedRecords,
    deletedTransactions,
    expiredItems: expiredGiftCards.map(card => ({
      id: card.id,
      cardCode: card.cardCode,
      faceValue: card.faceValue,
      redeemedAt: card.redeemedAt
    }))
  }
}

async function cleanupRedemptionRecords(): Promise<CleanupResult> {
  console.log('开始清理兑换券记录...')
  
  const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)

  // 查找需要清理的已使用完毕的兑换券
  const expiredRedemptionCodes = await prisma.redemptionCode.findMany({
    where: {
      status: 'USED',
      lastUsedAt: {
        lt: fifteenDaysAgo
      }
    },
    select: {
      id: true,
      codeValue: true,
      title: true,
      lastUsedAt: true,
      rewardValue: true,
      rewardUnit: true
    }
  })

  console.log(`找到 ${expiredRedemptionCodes.length} 个过期的已使用兑换券`)

  let deletedRecords = 0
  let deletedTransactions = 0

  if (expiredRedemptionCodes.length > 0) {
    const redemptionCodeIds = expiredRedemptionCodes.map(code => code.id)

    // 删除相关的使用记录
    const deletedTransactionResult = await prisma.redemptionTransaction.deleteMany({
      where: {
        redemptionCodeId: {
          in: redemptionCodeIds
        }
      }
    })
    deletedTransactions = deletedTransactionResult.count
    console.log(`删除了 ${deletedTransactions} 条兑换券使用记录`)

    // 删除兑换券记录
    const deletedRedemptionResult = await prisma.redemptionCode.deleteMany({
      where: {
        id: {
          in: redemptionCodeIds
        }
      }
    })
    deletedRecords = deletedRedemptionResult.count
    console.log(`删除了 ${deletedRecords} 个兑换券记录`)
  }

  return {
    type: 'redemption',
    deletedRecords,
    deletedTransactions,
    expiredItems: expiredRedemptionCodes.map(code => ({
      id: code.id,
      codeValue: code.codeValue,
      title: code.title,
      rewardValue: code.rewardValue,
      rewardUnit: code.rewardUnit,
      lastUsedAt: code.lastUsedAt
    }))
  }
}

async function createCleanupLog(results: CleanupResult[]) {
  try {
    // 记录系统清理日志到控制台
    const logData = {
      type: 'AUTOMATED_CLEANUP',
      message: '自动数据清理任务执行完成',
      timestamp: new Date().toISOString(),
      results,
      summary: {
        totalDeletedRecords: results.reduce((sum, r) => sum + r.deletedRecords, 0),
        totalDeletedTransactions: results.reduce((sum, r) => sum + r.deletedTransactions, 0)
      }
    }

    console.log('=== 清理日志记录 ===')
    console.log(JSON.stringify(logData, null, 2))
    console.log('清理日志已记录到控制台')
  } catch (error) {
    console.error('记录清理日志失败:', error)
  }
}

async function main() {
  console.log('=== 自动数据清理任务开始 ===')
  console.log(`执行时间: ${new Date().toLocaleString('zh-CN')}`)
  
  try {
    const results: CleanupResult[] = []

    // 清理礼品卡记录
    const giftCardResult = await cleanupGiftCardRecords()
    results.push(giftCardResult)

    // 清理兑换券记录
    const redemptionResult = await cleanupRedemptionRecords()
    results.push(redemptionResult)

    // 记录清理日志
    await createCleanupLog(results)

    // 输出总结
    console.log('\n=== 清理任务完成 ===')
    console.log('清理结果汇总:')
    console.log(`- 礼品卡: 删除 ${giftCardResult.deletedRecords} 条记录, ${giftCardResult.deletedTransactions} 条交易`)
    console.log(`- 兑换券: 删除 ${redemptionResult.deletedRecords} 条记录, ${redemptionResult.deletedTransactions} 条交易`)
    console.log(`- 总计: 删除 ${giftCardResult.deletedRecords + redemptionResult.deletedRecords} 条记录, ${giftCardResult.deletedTransactions + redemptionResult.deletedTransactions} 条交易`)

    // 如果有删除的记录，输出详细信息
    if (giftCardResult.expiredItems.length > 0) {
      console.log('\n已删除的礼品卡:')
      giftCardResult.expiredItems.forEach(item => {
        console.log(`  - ${item.cardCode} (${item.faceValue} USDT, 兑换于 ${new Date(item.redeemedAt).toLocaleString('zh-CN')})`)
      })
    }

    if (redemptionResult.expiredItems.length > 0) {
      console.log('\n已删除的兑换券:')
      redemptionResult.expiredItems.forEach(item => {
        console.log(`  - ${item.title} (${item.rewardValue} ${item.rewardUnit}, 使用于 ${new Date(item.lastUsedAt).toLocaleString('zh-CN')})`)
      })
    }

    console.log('\n清理任务执行成功!')

  } catch (error) {
    console.error('清理任务执行失败:', error)
    
    // 记录错误日志到控制台
    try {
      const errorLogData = {
        type: 'AUTOMATED_CLEANUP_ERROR',
        message: '自动数据清理任务执行失败',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        level: 'ERROR'
      }

      console.log('=== 错误日志记录 ===')
      console.log(JSON.stringify(errorLogData, null, 2))
      console.log('错误日志已记录到控制台')
    } catch (logError) {
      console.error('记录错误日志失败:', logError)
    }

    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error)
}

export { main as runCleanup }
