const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAutoAssignMediator() {
  console.log('🤖 测试中间人自动分配系统...\n')

  try {
    // 1. 检查现有中间人数据
    console.log('1. 检查现有中间人数据...')
    
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        email: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        mediatorDeposit: true
      }
    })

    console.log(`✅ 找到 ${mediators.length} 个活跃中间人`)
    
    if (mediators.length === 0) {
      console.log('❌ 没有活跃的中间人，无法测试自动分配功能')
      return
    }

    // 2. 为每个中间人计算详细信息
    console.log('\n2. 计算中间人详细信息...')
    
    const mediatorDetails = []
    
    for (const mediator of mediators) {
      // 计算冻结资金
      const guaranteeDeposits = await prisma.fundTransaction.aggregate({
        where: {
          userId: mediator.id,
          type: 'GUARANTEE_DEPOSIT'
        },
        _sum: { amount: true }
      })

      const guaranteeWithdrawals = await prisma.fundTransaction.aggregate({
        where: {
          userId: mediator.id,
          type: 'GUARANTEE_WITHDRAWAL'
        },
        _sum: { amount: true }
      })

      const totalFrozenFunds = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0)

      // 计算活跃订单
      const activeOrders = await prisma.order.findMany({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        },
        select: {
          escrowAmount: true
        }
      })

      const lockedAmount = activeOrders.reduce((sum, order) => sum + (order.escrowAmount || 0), 0)
      const availableAmount = Math.max(0, totalFrozenFunds - lockedAmount)

      mediatorDetails.push({
        ...mediator,
        totalFrozenFunds,
        lockedAmount,
        availableAmount,
        activeOrderCount: activeOrders.length
      })

      console.log(`   ${mediator.name}:`)
      console.log(`     总冻结资金: ${totalFrozenFunds} USDT`)
      console.log(`     锁定金额: ${lockedAmount} USDT`)
      console.log(`     可用金额: ${availableAmount} USDT`)
      console.log(`     活跃订单: ${activeOrders.length} 个`)
      console.log(`     信誉度: ${mediator.mediatorReputation || 0} 分`)
      console.log(`     成功率: ${mediator.mediatorSuccessRate || 0}%`)
    }

    // 3. 测试不同订单金额的自动分配
    console.log('\n3. 测试不同订单金额的自动分配...')
    
    const testAmounts = [500, 1000, 2000, 5000, 10000]
    
    for (const orderAmount of testAmounts) {
      console.log(`\n   测试订单金额: ${orderAmount} USDT`)
      
      // 筛选符合条件的中间人
      const candidates = mediatorDetails.filter(m => m.availableAmount >= orderAmount)
      
      if (candidates.length === 0) {
        console.log(`     ❌ 无符合条件的中间人 (需要可用资金 ≥ ${orderAmount} USDT)`)
        continue
      }
      
      console.log(`     ✅ 符合条件的中间人: ${candidates.length} 个`)
      
      // 计算每个候选人的评分
      const scoredCandidates = candidates.map(mediator => {
        let score = 0
        
        // 信誉度权重 (40%)
        const reputationScore = (mediator.mediatorReputation || 0) / 100 * 40
        
        // 成功率权重 (30%)
        const successRateScore = (mediator.mediatorSuccessRate || 0) / 100 * 30
        
        // 负载均衡权重 (20%)
        const maxOrders = 50
        const loadBalanceScore = Math.max(0, (maxOrders - mediator.activeOrderCount) / maxOrders) * 20
        
        // 资金充足度权重 (10%)
        const fundsSufficiencyScore = Math.min(mediator.availableAmount / (orderAmount * 5), 1) * 10
        
        score = reputationScore + successRateScore + loadBalanceScore + fundsSufficiencyScore
        
        return {
          ...mediator,
          score,
          reputationScore,
          successRateScore,
          loadBalanceScore,
          fundsSufficiencyScore
        }
      })
      
      // 按评分排序
      scoredCandidates.sort((a, b) => b.score - a.score)
      
      const winner = scoredCandidates[0]
      
      console.log(`     🏆 最优中间人: ${winner.name}`)
      console.log(`       综合评分: ${winner.score.toFixed(2)} 分`)
      console.log(`       - 信誉度得分: ${winner.reputationScore.toFixed(2)} (${winner.mediatorReputation || 0}分)`)
      console.log(`       - 成功率得分: ${winner.successRateScore.toFixed(2)} (${winner.mediatorSuccessRate || 0}%)`)
      console.log(`       - 负载均衡得分: ${winner.loadBalanceScore.toFixed(2)} (${winner.activeOrderCount}个活跃订单)`)
      console.log(`       - 资金充足度得分: ${winner.fundsSufficiencyScore.toFixed(2)} (${winner.availableAmount} USDT可用)`)
      console.log(`       托管费用: ${(orderAmount * (winner.mediatorFeeRate || 0.02)).toFixed(2)} USDT`)
    }

    // 4. 模拟API调用测试
    console.log('\n4. 模拟API调用测试...')
    
    const testOrderAmount = 1000
    console.log(`   测试订单金额: ${testOrderAmount} USDT`)
    
    // 模拟API逻辑
    const apiCandidates = []
    
    for (const mediator of mediatorDetails) {
      if (mediator.availableAmount >= testOrderAmount) {
        const score = calculateMediatorScore(mediator, testOrderAmount)
        apiCandidates.push({
          ...mediator,
          score
        })
      }
    }
    
    if (apiCandidates.length > 0) {
      apiCandidates.sort((a, b) => b.score - a.score)
      const selectedMediator = apiCandidates[0]
      
      console.log('   ✅ API分配结果:')
      console.log(`     中间人: ${selectedMediator.name}`)
      console.log(`     费率: ${((selectedMediator.mediatorFeeRate || 0) * 100).toFixed(1)}%`)
      console.log(`     托管费用: ${(testOrderAmount * (selectedMediator.mediatorFeeRate || 0.02)).toFixed(2)} USDT`)
      console.log(`     总费用: ${(testOrderAmount + testOrderAmount * (selectedMediator.mediatorFeeRate || 0.02)).toFixed(2)} USDT`)
    } else {
      console.log('   ❌ 无可用中间人')
    }

    // 5. 负载均衡测试
    console.log('\n5. 负载均衡测试...')
    
    console.log('   模拟连续分配10个订单 (每个1000 USDT):')
    const assignmentCounts = {}
    
    for (let i = 0; i < 10; i++) {
      const availableCandidates = mediatorDetails.filter(m => m.availableAmount >= 1000)
      
      if (availableCandidates.length > 0) {
        // 按当前活跃订单数排序 (负载均衡)
        availableCandidates.sort((a, b) => {
          const aCount = assignmentCounts[a.id] || 0
          const bCount = assignmentCounts[b.id] || 0
          return aCount - bCount
        })
        
        const selected = availableCandidates[0]
        assignmentCounts[selected.id] = (assignmentCounts[selected.id] || 0) + 1
        
        console.log(`     订单 ${i + 1}: 分配给 ${selected.name} (当前分配: ${assignmentCounts[selected.id]}个)`)
      }
    }
    
    console.log('\n   最终分配统计:')
    Object.entries(assignmentCounts).forEach(([mediatorId, count]) => {
      const mediator = mediatorDetails.find(m => m.id === mediatorId)
      console.log(`     ${mediator.name}: ${count} 个订单`)
    })

    console.log('\n🎉 自动分配系统测试完成！')
    
    console.log('\n📝 测试结果总结:')
    console.log(`   - 活跃中间人数量: ${mediators.length}`)
    console.log(`   - 自动分配算法: 综合评分制`)
    console.log(`   - 评分因子: 信誉度(40%) + 成功率(30%) + 负载均衡(20%) + 资金充足度(10%)`)
    console.log(`   - 负载均衡: 优先分配给活跃订单少的中间人`)
    console.log(`   - 资金验证: 确保中间人可用资金充足`)

    console.log('\n🔧 手动测试步骤:')
    console.log('1. 访问任意商品页面')
    console.log('2. 选择数量，确保订单金额 ≥ 100 USDT')
    console.log('3. 勾选"使用中间人托管服务"')
    console.log('4. 观察系统自动分配中间人的过程')
    console.log('5. 查看分配结果和中间人信息')
    console.log('6. 完成订单创建流程')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 计算中间人评分的辅助函数
function calculateMediatorScore(mediator, orderAmount) {
  let score = 0

  // 信誉度权重 (40%)
  const reputationScore = (mediator.mediatorReputation || 0) / 100 * 40

  // 成功率权重 (30%)
  const successRateScore = (mediator.mediatorSuccessRate || 0) / 100 * 30

  // 负载均衡权重 (20%)
  const maxOrders = 50
  const loadBalanceScore = Math.max(0, (maxOrders - mediator.activeOrderCount) / maxOrders) * 20

  // 资金充足度权重 (10%)
  const fundsSufficiencyScore = Math.min(mediator.availableAmount / (orderAmount * 5), 1) * 10

  score = reputationScore + successRateScore + loadBalanceScore + fundsSufficiencyScore

  return score
}

// 运行测试
if (require.main === module) {
  testAutoAssignMediator().catch(console.error)
}

module.exports = { testAutoAssignMediator }
