const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('设置管理员权限...')
    
    const email = '<EMAIL>'
    
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      console.log('用户不存在，请先注册该用户')
      return
    }
    
    // 更新为管理员
    const updatedUser = await prisma.user.update({
      where: { email },
      data: { role: 'ADMIN' }
    })
    
    console.log('成功设置管理员权限:')
    console.log('邮箱:', updatedUser.email)
    console.log('角色:', updatedUser.role)
    console.log('状态:', updatedUser.status)
    
  } catch (error) {
    console.error('错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
