#!/usr/bin/env node

// 使用 Node.js 18+ 内置的 fetch，或者回退到 https 模块
const https = require('https')
const http = require('http')
const { URL } = require('url')

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const isHttps = urlObj.protocol === 'https:'
    const client = isHttps ? https : http

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    }

    const req = client.request(requestOptions, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          headers: new Map(Object.entries(res.headers)),
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        })
      })
    })

    req.on('error', reject)

    if (options.body) {
      req.write(options.body)
    }

    req.end()
  })
}

async function testAfterSalesAPI() {
  try {
    console.log('🔄 测试售后申请 API...')

    // 测试获取售后申请列表 API
    const response = await fetch('http://localhost:3000/api/after-sales?role=buyer', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    console.log('📊 API 响应状态:', response.status)
    console.log('📊 API 响应头:', Object.fromEntries(response.headers.entries()))

    if (response.ok) {
      const data = await response.json()
      console.log('✅ API 响应成功:')
      console.log('- 售后申请数量:', data.requests?.length || 0)
      console.log('- 分页信息:', data.pagination)
      
      if (data.requests && data.requests.length > 0) {
        console.log('- 第一个申请:', {
          id: data.requests[0].id,
          type: data.requests[0].type,
          status: data.requests[0].status,
          reason: data.requests[0].reason
        })
      }
    } else {
      const errorData = await response.json()
      console.log('❌ API 响应失败:', errorData)
    }

    // 测试获取统计信息 API
    console.log('\n🔄 测试统计信息 API...')
    const statsResponse = await fetch('http://localhost:3000/api/after-sales?role=buyer', {
      method: 'HEAD',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    console.log('📊 统计 API 响应状态:', statsResponse.status)
    if (statsResponse.ok) {
      console.log('✅ 统计信息:')
      console.log('- 总数:', statsResponse.headers.get('X-Total-Count'))
      console.log('- 待处理:', statsResponse.headers.get('X-Pending-Count'))
      console.log('- 处理中:', statsResponse.headers.get('X-Processing-Count'))
      console.log('- 已完成:', statsResponse.headers.get('X-Completed-Count'))
    } else {
      console.log('❌ 统计 API 响应失败')
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

testAfterSalesAPI()
