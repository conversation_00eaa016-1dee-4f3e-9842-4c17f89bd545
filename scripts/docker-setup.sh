#!/bin/bash

# ============================================================================
# BitMarket Docker 数据库环境设置脚本
# Docker Database Environment Setup Script for BitMarket
# ============================================================================

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 停止现有容器
stop_containers() {
    log_info "停止现有容器..."
    docker-compose down --remove-orphans || true
    log_success "容器已停止"
}

# 启动数据库服务
start_database() {
    log_info "启动 MySQL 数据库服务..."
    
    # 使用 Docker 环境变量文件
    if [ -f ".env.docker" ]; then
        log_info "使用 .env.docker 配置文件"
        docker-compose --env-file .env.docker up -d mysql
    else
        log_warning ".env.docker 文件不存在，使用默认配置"
        docker-compose up -d mysql
    fi
    
    log_success "MySQL 数据库服务已启动"
}

# 启动 Redis 服务 (可选)
start_redis() {
    log_info "启动 Redis 缓存服务..."
    
    if [ -f ".env.docker" ]; then
        docker-compose --env-file .env.docker --profile redis up -d redis
    else
        docker-compose --profile redis up -d redis
    fi
    
    log_success "Redis 缓存服务已启动"
}

# 等待数据库就绪
wait_for_database() {
    log_info "等待数据库就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            log_success "数据库已就绪"
            return 0
        fi
        
        log_info "等待数据库启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "数据库启动超时"
    return 1
}

# 运行 Prisma 迁移
run_prisma_migration() {
    log_info "运行 Prisma 数据库迁移..."
    
    # 设置环境变量
    export DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"
    
    # 生成 Prisma Client
    npx prisma generate
    
    # 运行迁移
    npx prisma migrate dev --name docker-init
    
    log_success "Prisma 迁移完成"
}

# 验证数据库连接
verify_connection() {
    log_info "验证数据库连接..."
    
    if docker-compose exec -T mysql mysql -u bitmarket_user -pbitmarket_pass_2024 -e "SELECT 'Connection successful' AS status;"; then
        log_success "数据库连接验证成功"
    else
        log_error "数据库连接验证失败"
        return 1
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    docker-compose ps
    
    echo ""
    log_info "数据库连接信息："
    echo "  Host: localhost"
    echo "  Port: 3306"
    echo "  Database: bitmarket"
    echo "  Username: bitmarket_user"
    echo "  Password: bitmarket_pass_2024"
    echo ""
    echo "  连接字符串: mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"
}

# 主函数
main() {
    echo "============================================================================"
    echo "BitMarket Docker 数据库环境设置"
    echo "============================================================================"
    
    # 解析命令行参数
    INCLUDE_REDIS=false
    SKIP_MIGRATION=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --redis)
                INCLUDE_REDIS=true
                shift
                ;;
            --skip-migration)
                SKIP_MIGRATION=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --redis           同时启动 Redis 服务"
                echo "  --skip-migration  跳过 Prisma 迁移"
                echo "  --help           显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行设置步骤
    check_docker
    stop_containers
    start_database
    
    if [ "$INCLUDE_REDIS" = true ]; then
        start_redis
    fi
    
    wait_for_database
    verify_connection
    
    if [ "$SKIP_MIGRATION" = false ]; then
        run_prisma_migration
    fi
    
    show_status
    
    echo ""
    log_success "Docker 数据库环境设置完成！"
    echo ""
    log_info "下一步："
    echo "  1. 复制 .env.docker 到 .env.local: cp .env.docker .env.local"
    echo "  2. 启动应用程序: npm run dev"
    echo ""
    log_info "管理命令："
    echo "  停止服务: docker-compose down"
    echo "  查看日志: docker-compose logs -f mysql"
    echo "  进入数据库: docker-compose exec mysql mysql -u bitmarket_user -p"
}

# 运行主函数
main "$@"
