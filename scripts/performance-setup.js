#!/usr/bin/env node

/**
 * 性能优化设置脚本
 * 用于初始化性能监控、缓存预热等
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始性能优化设置...')

// 1. 检查环境变量
function checkEnvironment() {
  console.log('📋 检查环境配置...')
  
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'JWT_SECRET'
  ]
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    console.warn('⚠️  缺少以下环境变量:', missingVars.join(', '))
    console.log('请参考 .env.example 文件配置环境变量')
  } else {
    console.log('✅ 环境变量检查通过')
  }
}

// 2. 检查Redis连接
async function checkRedis() {
  console.log('🔍 检查Redis连接...')

  try {
    // 检查Redis配置
    if (!process.env.REDIS_URL && !process.env.REDIS_HOST) {
      console.warn('⚠️  Redis未配置，将使用内存缓存')
      return false
    }

    const fs = require('fs')
    const path = require('path')
    const cachePath = path.join(__dirname, '../lib/cache.ts')

    if (!fs.existsSync(cachePath)) {
      console.warn('⚠️  缓存模块不存在，跳过Redis检查')
      return false
    }

    // 尝试连接Redis（如果配置了的话）
    const { cache } = require('../lib/cache-fallback')
    const isHealthy = await cache.healthCheck()

    if (isHealthy) {
      console.log('✅ Redis连接正常')
      return true
    } else {
      console.warn('⚠️  Redis连接失败，将使用内存缓存')
      return false
    }
  } catch (error) {
    console.warn('⚠️  Redis检查失败，将使用内存缓存:', error.message)
    return false
  }
}

// 3. 检查数据库连接
async function checkDatabase() {
  console.log('🗄️  检查数据库连接...')

  try {
    // 检查是否存在优化的prisma文件
    const fs = require('fs')
    const path = require('path')
    const optimizedPrismaPath = path.join(__dirname, '../lib/prisma-optimized.ts')

    if (fs.existsSync(optimizedPrismaPath)) {
      // 如果存在优化版本，尝试使用它
      try {
        const { prismaConnection } = require('../lib/prisma-optimized')
        const isHealthy = await prismaConnection.healthCheck()

        if (isHealthy) {
          console.log('✅ 数据库连接正常（使用优化版本）')
          return true
        }
      } catch (optimizedError) {
        console.log('⚠️  优化版本加载失败，尝试标准版本')
      }
    }

    // 回退到标准prisma客户端
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()

    await prisma.$queryRaw`SELECT 1`
    await prisma.$disconnect()

    console.log('✅ 数据库连接正常（使用标准版本）')
    return true

  } catch (error) {
    console.error('❌ 数据库连接错误:', error.message)
    return false
  }
}

// 4. 缓存预热
async function warmupCache() {
  console.log('🔥 开始缓存预热...')

  try {
    const fs = require('fs')
    const path = require('path')
    const cachePath = path.join(__dirname, '../lib/cache.ts')

    if (!fs.existsSync(cachePath)) {
      console.warn('⚠️  缓存模块不存在，跳过预热')
      return
    }

    const { cache } = require('../lib/cache-fallback')
    await cache.warmupCache()
    console.log('✅ 缓存预热完成')
  } catch (error) {
    console.warn('⚠️  缓存预热失败:', error.message)
  }
}

// 5. 创建性能监控目录
function setupMonitoring() {
  console.log('📊 设置性能监控...')
  
  const logsDir = path.join(process.cwd(), 'logs')
  const metricsDir = path.join(logsDir, 'metrics')
  
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true })
  }
  
  if (!fs.existsSync(metricsDir)) {
    fs.mkdirSync(metricsDir, { recursive: true })
  }
  
  console.log('✅ 监控目录创建完成')
}

// 6. 优化Node.js设置
function optimizeNodeSettings() {
  console.log('⚙️  优化Node.js设置...')
  
  // 设置内存限制
  const memoryLimit = process.env.NODE_MEMORY_LIMIT || '2048'
  process.env.NODE_OPTIONS = `--max-old-space-size=${memoryLimit}`
  
  // 启用性能优化
  if (process.env.NODE_ENV === 'production') {
    process.env.NODE_OPTIONS += ' --optimize-for-size'
  }
  
  console.log('✅ Node.js设置优化完成')
}

// 7. 生成性能报告
function generatePerformanceReport() {
  console.log('📈 生成性能基准报告...')
  
  const report = {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    memory: process.memoryUsage(),
    cpus: require('os').cpus().length,
    environment: process.env.NODE_ENV || 'development',
    optimizations: {
      redis: !!process.env.REDIS_URL,
      cluster: process.env.USE_CLUSTER === 'true',
      monitoring: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
    }
  }
  
  const reportPath = path.join(process.cwd(), 'logs', 'performance-baseline.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  
  console.log('✅ 性能基准报告已生成:', reportPath)
}

// 8. 数据库索引优化建议
function suggestDatabaseOptimizations() {
  console.log('💡 数据库优化建议:')
  
  const suggestions = [
    '考虑为高频查询字段添加索引',
    '定期分析慢查询日志',
    '考虑升级到PostgreSQL以获得更好的性能',
    '实施数据库连接池',
    '考虑读写分离架构'
  ]
  
  suggestions.forEach((suggestion, index) => {
    console.log(`   ${index + 1}. ${suggestion}`)
  })
}

// 主函数
async function main() {
  try {
    checkEnvironment()
    optimizeNodeSettings()
    setupMonitoring()
    
    const dbHealthy = await checkDatabase()
    if (!dbHealthy) {
      console.error('❌ 数据库连接失败，请检查配置')
      process.exit(1)
    }
    
    const redisHealthy = await checkRedis()
    
    if (redisHealthy) {
      await warmupCache()
    }
    
    generatePerformanceReport()
    suggestDatabaseOptimizations()
    
    console.log('\n🎉 性能优化设置完成!')
    console.log('💡 建议:')
    console.log('   - 在生产环境中启用集群模式 (USE_CLUSTER=true)')
    console.log('   - 配置Redis以获得更好的缓存性能')
    console.log('   - 定期监控性能指标')
    console.log('   - 考虑使用CDN加速静态资源')
    
  } catch (error) {
    console.error('❌ 设置过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  checkEnvironment,
  checkRedis,
  checkDatabase,
  warmupCache,
  setupMonitoring,
  optimizeNodeSettings,
  generatePerformanceReport,
  suggestDatabaseOptimizations
}
