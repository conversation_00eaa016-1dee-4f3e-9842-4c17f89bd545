const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestMediatorApplications() {
  try {
    console.log('开始创建测试中间人申请数据...')

    // 先清理现有的中间人申请
    await prisma.mediatorApplication.deleteMany({})
    console.log('清理了现有的中间人申请数据')

    // 首先检查是否有用户
    const users = await prisma.user.findMany({
      where: {
        role: 'USER',
        isMediator: false
      },
      take: 3
    })

    if (users.length === 0) {
      console.log('没有找到普通用户，先创建测试用户...')
      
      // 创建测试用户
      const testUsers = await Promise.all([
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: '张三',
            role: 'USER',
            depositBalance: 1000,
            creditScore: 85,
            emailVerified: new Date(),
            password: '$2a$10$example.hash.for.testing'
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: '李四',
            role: 'USER',
            depositBalance: 1500,
            creditScore: 92,
            emailVerified: new Date(),
            password: '$2a$10$example.hash.for.testing'
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: '王五',
            role: 'USER',
            depositBalance: 800,
            creditScore: 78,
            emailVerified: new Date(),
            password: '$2a$10$example.hash.for.testing'
          }
        })
      ])
      
      console.log(`创建了 ${testUsers.length} 个测试用户`)
      users.push(...testUsers)
    }

    // 获取管理员用户ID
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!adminUser) {
      throw new Error('没有找到管理员用户')
    }

    // 创建中间人申请
    const applications = []
    
    // 申请1 - 待审核
    applications.push(await prisma.mediatorApplication.create({
      data: {
        userId: users[0].id,
        bnbWalletAddress: '******************************************',
        depositAmount: 500,
        feeRate: 0.025, // 2.5%
        experience: '我有3年的数字货币交易经验，熟悉各种交易平台和钱包操作。曾经帮助朋友处理过多次交易纠纷，具备良好的沟通能力和公正的判断力。',
        introduction: '大家好，我是张三，希望成为平台的中间人，为大家提供安全可靠的托管服务。我承诺会公正处理每一笔交易，保护买卖双方的利益。',
        status: 'PENDING'
      }
    }))

    // 申请2 - 待审核
    applications.push(await prisma.mediatorApplication.create({
      data: {
        userId: users[1].id,
        bnbWalletAddress: '******************************************',
        depositAmount: 1000,
        feeRate: 0.02, // 2%
        experience: '我是一名资深的区块链从业者，有5年的数字货币交易和投资经验。曾在多个交易平台担任客服和争议调解员，处理过上百起交易纠纷。',
        introduction: '我是李四，拥有丰富的数字货币交易经验和争议处理能力。我相信公平、透明、高效的服务理念，愿意为平台用户提供专业的托管服务。',
        status: 'PENDING'
      }
    }))

    // 申请3 - 已批准（用于测试已处理状态）
    const approvedApplication = await prisma.mediatorApplication.create({
      data: {
        userId: users[2].id,
        bnbWalletAddress: '******************************************',
        depositAmount: 800,
        feeRate: 0.03, // 3%
        experience: '我有2年的P2P交易经验，熟悉各种支付方式和风险控制。曾经在其他平台担任过中间人，获得了用户的一致好评。',
        introduction: '我是王五，虽然经验相对较少，但我有强烈的责任心和学习能力。我会认真对待每一笔托管交易，确保交易的安全和顺利进行。',
        status: 'APPROVED',
        reviewedBy: adminUser.id,
        reviewNotes: '申请材料完整，经验符合要求，批准成为中间人。',
        approvedAt: new Date()
      }
    })

    // 更新已批准用户为中间人
    await prisma.user.update({
      where: { id: users[2].id },
      data: {
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        mediatorFeeRate: 0.03,
        mediatorDeposit: 800,
        mediatorVerifiedAt: new Date(),
        bnbWalletAddress: '******************************************',
        bnbWalletVerified: true,
        bnbWalletVerifiedAt: new Date()
      }
    })

    console.log(`创建了 ${applications.length} 个中间人申请:`)
    applications.forEach((app, index) => {
      console.log(`  ${index + 1}. 用户: ${users[index].name}, 状态: ${app.status}, 保证金: ${app.depositAmount} USDT`)
    })

    console.log('测试数据创建完成！')

  } catch (error) {
    console.error('创建测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestMediatorApplications()
