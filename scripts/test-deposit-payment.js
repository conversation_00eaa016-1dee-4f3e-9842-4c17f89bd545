/**
 * 测试保证金支付功能
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDepositPayment() {
  console.log('🧪 测试保证金支付功能...')
  console.log('='.repeat(50))

  try {
    // 1. 查找测试用户
    console.log('\n1️⃣ 查找测试用户...')
    
    const user1 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        name: true,
        email: true,
        depositBalance: true
      }
    })

    if (!user1) {
      console.log('❌ 测试用户不存在，请先运行初始化脚本')
      return
    }

    console.log(`✅ 找到测试用户: ${user1.name} (${user1.email})`)
    console.log(`   保证金余额: ${user1.depositBalance} USDT`)

    // 2. 确保用户有足够的保证金
    console.log('\n2️⃣ 检查保证金余额...')
    
    const minBalance = 500 // 最少需要500 USDT
    if (user1.depositBalance < minBalance) {
      console.log(`⚠️ 保证金不足，正在充值到 ${minBalance} USDT...`)
      
      await prisma.user.update({
        where: { id: user1.id },
        data: {
          depositBalance: minBalance
        }
      })

      // 记录充值交易
      await prisma.fundTransaction.create({
        data: {
          userId: user1.id,
          type: 'DEPOSIT',
          amount: minBalance - user1.depositBalance,
          description: '测试充值 - 保证金支付测试',
          metadata: {
            test: true,
            purpose: 'deposit_payment_test'
          }
        }
      })

      console.log(`✅ 充值完成，当前余额: ${minBalance} USDT`)
    } else {
      console.log(`✅ 保证金充足: ${user1.depositBalance} USDT`)
    }

    // 3. 查找或创建测试订单
    console.log('\n3️⃣ 查找测试订单...')
    
    let testOrder = await prisma.order.findFirst({
      where: {
        buyerId: user1.id,
        status: 'PENDING_PAYMENT'
      },
      include: {
        product: true,
        seller: true
      }
    })

    if (!testOrder) {
      console.log('⚠️ 没有找到待支付订单，正在创建测试订单...')
      
      // 查找测试商品
      const testProduct = await prisma.product.findFirst({
        where: {
          status: 'ACTIVE',
          reviewStatus: 'APPROVED'
        }
      })

      if (!testProduct) {
        console.log('❌ 没有可用的测试商品')
        return
      }

      // 创建测试订单
      testOrder = await prisma.order.create({
        data: {
          orderNumber: `TEST_${Date.now()}`,
          buyerId: user1.id,
          sellerId: testProduct.sellerId,
          productId: testProduct.id,
          quantity: 1,
          productPrice: testProduct.price,
          shippingFee: 10,
          platformFee: testProduct.price * 0.05,
          totalAmount: testProduct.price + 10 + (testProduct.price * 0.05),
          status: 'PENDING_PAYMENT',
          escrowStatus: 'PENDING',
          shippingAddress: {
            name: '测试用户',
            phone: '13800138000',
            address: '测试地址',
            city: '测试城市',
            district: '测试区域',
            postalCode: '100000'
          }
        },
        include: {
          product: true,
          seller: true
        }
      })

      console.log(`✅ 创建测试订单: ${testOrder.orderNumber}`)
    } else {
      console.log(`✅ 找到测试订单: ${testOrder.orderNumber}`)
    }

    console.log(`   订单金额: ${testOrder.totalAmount} USDT`)
    console.log(`   商品: ${testOrder.product.title}`)

    // 4. 测试保证金支付场景
    console.log('\n4️⃣ 测试保证金支付场景...')
    
    const currentBalance = await prisma.user.findUnique({
      where: { id: user1.id },
      select: { depositBalance: true }
    })

    const availableBalance = currentBalance.depositBalance * 0.9 // 假设90%可用
    const orderAmount = testOrder.totalAmount

    console.log(`   可用保证金: ${availableBalance.toFixed(2)} USDT`)
    console.log(`   订单金额: ${orderAmount.toFixed(2)} USDT`)

    if (availableBalance >= orderAmount) {
      console.log('✅ 余额充足，可以使用保证金支付')
      
      // 模拟保证金支付
      console.log('\n📝 模拟保证金支付流程:')
      console.log('   1. 用户选择保证金支付')
      console.log('   2. 系统检查可用余额')
      console.log('   3. 扣除保证金并创建托管')
      console.log('   4. 订单状态更新为已支付')
      
    } else {
      const shortage = orderAmount - availableBalance
      console.log(`⚠️ 余额不足，缺少: ${shortage.toFixed(2)} USDT`)
      
      console.log('\n📝 模拟补充支付流程:')
      console.log(`   1. 扣除可用保证金: ${availableBalance.toFixed(2)} USDT`)
      console.log(`   2. 需要补充支付: ${shortage.toFixed(2)} USDT`)
      console.log('   3. 用户选择补充支付方式 (币安支付/BNB链)')
      console.log('   4. 完成补充支付后订单确认')
    }

    // 5. 测试支付方式优先级
    console.log('\n5️⃣ 支付方式优先级...')
    
    const paymentMethods = [
      {
        id: 'deposit_balance',
        name: '保证金支付',
        priority: 1,
        available: availableBalance >= orderAmount,
        description: '即时到账，无手续费'
      },
      {
        id: 'binance_pay',
        name: 'Binance Pay',
        priority: 2,
        available: true,
        description: '扫码支付，低手续费'
      },
      {
        id: 'bnb_chain',
        name: 'BNB Smart Chain',
        priority: 3,
        available: true,
        description: '链上交易，去中心化'
      }
    ]

    console.log('💳 可用支付方式:')
    paymentMethods.forEach(method => {
      const status = method.available ? '✅ 可用' : '❌ 不可用'
      const recommended = method.id === 'deposit_balance' && method.available ? ' (推荐)' : ''
      console.log(`   ${method.priority}. ${method.name}: ${status}${recommended}`)
      console.log(`      ${method.description}`)
    })

    // 6. 测试数据统计
    console.log('\n6️⃣ 测试数据统计...')
    
    const orderStats = await prisma.order.groupBy({
      by: ['status', 'paymentMethod'],
      _count: {
        id: true
      },
      _sum: {
        totalAmount: true
      }
    })

    console.log('📊 订单统计:')
    orderStats.forEach(stat => {
      const method = stat.paymentMethod || '未设置'
      console.log(`   ${stat.status} (${method}): ${stat._count.id}笔, 总额${stat._sum.totalAmount || 0} USDT`)
    })

    // 7. 清理提示
    console.log('\n7️⃣ 测试数据清理...')
    
    const testTransactions = await prisma.fundTransaction.count({
      where: {
        metadata: {
          path: ['test'],
          equals: true
        }
      }
    })

    console.log(`📋 测试交易记录: ${testTransactions} 条`)
    
    if (testTransactions > 0) {
      console.log('💡 提示: 可以使用以下命令清理测试数据:')
      console.log('   DELETE FROM FundTransaction WHERE JSON_EXTRACT(metadata, "$.test") = true;')
    }

    console.log('\n🎉 保证金支付功能测试完成!')

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

async function showDepositPaymentSummary() {
  console.log('\n📋 保证金支付功能总结:')
  console.log('='.repeat(50))
  
  console.log('\n🔄 主要功能:')
  console.log('✅ 保证金余额支付')
  console.log('✅ 余额不足时补充支付')
  console.log('✅ 支付方式优先级排序')
  console.log('✅ 即时到账和托管机制')
  
  console.log('\n💰 支付流程:')
  console.log('1. 余额充足:')
  console.log('   • 直接扣除保证金')
  console.log('   • 创建托管记录')
  console.log('   • 订单状态更新为已支付')
  console.log('   • 即时确认，无需等待')
  
  console.log('\n2. 余额不足:')
  console.log('   • 扣除可用保证金')
  console.log('   • 显示补充支付选项')
  console.log('   • 支持币安支付/BNB链补充')
  console.log('   • 补充支付完成后订单确认')
  
  console.log('\n🛡️ 安全特性:')
  console.log('• 只能使用可用保证金 (排除冻结和待提现)')
  console.log('• 托管机制保护买卖双方')
  console.log('• 完整的资金流水记录')
  console.log('• 支付状态实时更新')
  
  console.log('\n🎯 用户体验:')
  console.log('• 保证金支付优先推荐')
  console.log('• 余额实时显示')
  console.log('• 一键支付，无需跳转')
  console.log('• 支付失败可选择其他方式')
  
  console.log('\n🧪 测试地址:')
  console.log('订单支付页面: http://localhost:3000/orders/[orderId]/payment')
  console.log('测试账户: <EMAIL> / 123456')
}

async function main() {
  console.log('🚀 BitMarket 保证金支付功能测试')
  console.log('='.repeat(50))
  
  try {
    await testDepositPayment()
    showDepositPaymentSummary()
    
    console.log('\n🎯 测试完成!')
    console.log('现在可以访问订单支付页面体验保证金支付功能')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  testDepositPayment
}
