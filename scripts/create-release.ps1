# BitMarket v1.3.0 发布包创建脚本

param(
    [string]$OutputDir = "release"
)

Write-Host "🚀 开始创建 BitMarket v1.3.0 发布包..." -ForegroundColor Green

# 读取版本信息
$packageJson = Get-Content "package.json" | ConvertFrom-Json
$version = $packageJson.version
$packageName = "bitmarket-v$version-source"

Write-Host "📋 项目信息:" -ForegroundColor Cyan
Write-Host "   名称: BitMarket" -ForegroundColor White
Write-Host "   版本: v$version" -ForegroundColor White
Write-Host "   包名: $packageName" -ForegroundColor White

# 创建发布目录
Write-Host "`n📁 创建发布目录..." -ForegroundColor Yellow
if (Test-Path $OutputDir) {
    Remove-Item $OutputDir -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
New-Item -ItemType Directory -Path "$OutputDir\$packageName" -Force | Out-Null

# 定义需要包含的文件和目录
$includeItems = @(
    "app",
    "components", 
    "lib",
    "prisma",
    "public",
    "scripts",
    "package.json",
    "package-lock.json",
    "next.config.ts",
    "tsconfig.json", 
    "tailwind.config.js",
    "postcss.config.js",
    "server.js",
    ".env.example",
    "README.md",
    "CHANGELOG.md",
    "RELEASE_SUMMARY_v1.3.0.md",
    "RELEASE_CONFIRMATION_v1.3.0.md",
    ".gitignore",
    ".eslintrc.json",
    "vitest.config.ts"
)

# 复制文件
Write-Host "📋 复制源码文件..." -ForegroundColor Yellow
$copiedCount = 0

foreach ($item in $includeItems) {
    if (Test-Path $item) {
        $targetPath = "$OutputDir\$packageName\$item"
        
        if (Test-Path $item -PathType Container) {
            # 复制目录（排除不需要的文件）
            Write-Host "  📁 $item" -ForegroundColor Gray
            robocopy $item $targetPath /E /XD node_modules .next dist build .cache tmp temp logs .vscode .idea coverage /XF *.log .env .env.local .env.production *.swp *.swo .DS_Store Thumbs.db *.tgz *.tar.gz /NFL /NDL /NJH /NJS | Out-Null
        } else {
            # 复制文件
            Write-Host "  📄 $item" -ForegroundColor Gray
            $targetDir = Split-Path $targetPath -Parent
            if (!(Test-Path $targetDir)) {
                New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
            }
            Copy-Item $item $targetPath -Force
            $copiedCount++
        }
    } else {
        Write-Host "  ⚠️  跳过不存在的文件: $item" -ForegroundColor DarkYellow
    }
}

Write-Host "✅ 源码文件复制完成，共处理 $copiedCount 个文件" -ForegroundColor Green

# 创建发布信息文件
Write-Host "`n📝 创建发布信息文件..." -ForegroundColor Yellow

$releaseInfo = @{
    name = "BitMarket"
    version = $version
    releaseDate = (Get-Date).ToString("yyyy-MM-dd")
    description = "BitMarket - 基于USDT的去中心化C2C交易平台"
    features = @(
        "保证金机制更新 - 优化中间人担保系统",
        "页面系统更新 - 全面改善用户界面", 
        "导航栏重构 - logo图片+BitMarket文字组合",
        "UI组件库 - 完整的可复用组件系统"
    )
    technicalImprovements = @(
        "构建系统优化",
        "依赖管理完善", 
        "代码质量提升",
        "性能优化"
    )
    requirements = @{
        node = ">=18.0.0"
        npm = ">=8.0.0"
    }
    installation = @(
        "1. 解压源码包",
        "2. 运行 npm install 安装依赖",
        "3. 配置环境变量（参考.env.example）", 
        "4. 运行 npm run build 构建项目",
        "5. 运行 npm start 启动生产服务器"
    )
}

$releaseInfo | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\$packageName\RELEASE_INFO.json" -Encoding UTF8

# 创建安装说明文件
$installGuide = @"
# BitMarket v$version 安装指南

## 📋 系统要求

- Node.js >= 18.0.0
- npm >= 8.0.0  
- 数据库: MySQL 8.0+ 或 PostgreSQL 13+

## 🚀 快速安装

### 1. 解压源码包
``````bash
# Windows
Expand-Archive bitmarket-v$version-source.zip

# Linux/macOS  
tar -xzf bitmarket-v$version-source.tar.gz
cd bitmarket-v$version-source
``````

### 2. 安装依赖
``````bash
npm install
``````

### 3. 环境配置
``````bash
# Windows
copy .env.example .env

# Linux/macOS
cp .env.example .env

# 编辑 .env 文件，配置数据库连接等信息
``````

### 4. 数据库设置
``````bash
npx prisma generate
npx prisma db push
``````

### 5. 构建项目
``````bash
npm run build
``````

### 6. 启动服务
``````bash
# 开发环境
npm run dev

# 生产环境
npm start
``````

## 🔧 配置说明

### 环境变量配置
请参考 ``.env.example`` 文件中的配置项说明。

### 数据库配置
支持 MySQL 和 PostgreSQL，请在 ``.env`` 文件中配置 ``DATABASE_URL``。

## 📚 更多信息

- 详细文档: 查看 README.md
- 更新日志: 查看 CHANGELOG.md  
- 发布说明: 查看 RELEASE_SUMMARY_v$version.md

## 🆘 获取帮助

如果遇到问题，请查看：
1. README.md 中的常见问题解答
2. GitHub Issues: https://github.com/liusu-ally/bitmarket/issues
3. 邮箱支持: <EMAIL>
"@

$installGuide | Out-File "$OutputDir\$packageName\INSTALL.md" -Encoding UTF8

Write-Host "✅ 发布信息文件已创建" -ForegroundColor Green

# 创建压缩包
Write-Host "`n📦 创建压缩包..." -ForegroundColor Yellow

$archiveName = "$packageName.zip"
$archivePath = "$OutputDir\$archiveName"

try {
    Compress-Archive -Path "$OutputDir\$packageName" -DestinationPath $archivePath -Force
    
    $archiveSize = (Get-Item $archivePath).Length
    $archiveSizeMB = [math]::Round($archiveSize / 1MB, 2)
    
    Write-Host "✅ 压缩包已创建: $archivePath" -ForegroundColor Green
    Write-Host "📊 压缩包大小: $archiveSizeMB MB" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 创建压缩包失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 生成校验和
Write-Host "`n🔐 生成校验和..." -ForegroundColor Yellow

try {
    $hash = Get-FileHash $archivePath -Algorithm SHA256
    $checksumPath = "$archivePath.sha256"
    "$($hash.Hash.ToLower())  $archiveName" | Out-File $checksumPath -Encoding ASCII
    
    Write-Host "✅ 校验和已生成: $checksumPath" -ForegroundColor Green
    Write-Host "🔐 SHA256: $($hash.Hash.ToLower())" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 生成校验和失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 显示完成信息
Write-Host "`n🎉 发布包创建完成!" -ForegroundColor Green
Write-Host "📦 发布包信息:" -ForegroundColor Cyan
Write-Host "   版本: v$version" -ForegroundColor White
Write-Host "   文件: $archiveName" -ForegroundColor White  
Write-Host "   路径: $archivePath" -ForegroundColor White
Write-Host "   大小: $archiveSizeMB MB" -ForegroundColor White
Write-Host "   校验: $($hash.Hash.ToLower())" -ForegroundColor White

Write-Host "`n📋 下一步操作:" -ForegroundColor Cyan
Write-Host "1. 测试压缩包完整性" -ForegroundColor White
Write-Host "2. 上传到发布平台" -ForegroundColor White  
Write-Host "3. 创建 GitHub Release" -ForegroundColor White
Write-Host "4. 通知用户更新" -ForegroundColor White
