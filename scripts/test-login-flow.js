const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testLoginFlow() {
  try {
    console.log('=== 测试登录流程 ===\n');
    
    // 1. 检查买家用户是否存在
    console.log('1. 检查买家用户...');
    const buyer = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        password: true
      }
    });
    
    if (!buyer) {
      console.log('❌ 买家用户不存在');
      return;
    }
    
    console.log(`✅ 买家用户: ${buyer.name} (${buyer.email})`);
    console.log(`   角色: ${buyer.role}, 状态: ${buyer.status}`);
    console.log(`   有密码: ${!!buyer.password}`);
    
    // 2. 测试登录API
    console.log('\n2. 测试登录API...');
    
    // 创建axios实例来保持cookies
    const client = axios.create({
      baseURL: 'http://localhost:3000',
      timeout: 10000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    try {
      // 尝试登录
      const loginResponse = await client.post('/api/auth/signin/credentials', {
        email: '<EMAIL>',
        password: '123456',
        redirect: false
      });
      
      console.log('登录响应状态:', loginResponse.status);
      console.log('登录响应数据:', JSON.stringify(loginResponse.data, null, 2));
      
      // 检查响应头中的cookies
      const setCookieHeaders = loginResponse.headers['set-cookie'];
      if (setCookieHeaders) {
        console.log('设置的Cookies:');
        setCookieHeaders.forEach((cookie, index) => {
          console.log(`${index + 1}. ${cookie}`);
        });
      } else {
        console.log('❌ 没有设置cookies');
      }
      
    } catch (loginError) {
      console.log('❌ 登录失败');
      if (loginError.response) {
        console.log(`状态码: ${loginError.response.status}`);
        console.log(`错误信息: ${JSON.stringify(loginError.response.data, null, 2)}`);
      } else {
        console.log(`错误: ${loginError.message}`);
      }
    }
    
    // 3. 测试NextAuth session端点
    console.log('\n3. 测试NextAuth session端点...');
    try {
      const sessionResponse = await client.get('/api/auth/session');
      
      console.log('Session响应状态:', sessionResponse.status);
      console.log('Session数据:', JSON.stringify(sessionResponse.data, null, 2));
      
    } catch (sessionError) {
      console.log('❌ 获取session失败');
      if (sessionError.response) {
        console.log(`状态码: ${sessionError.response.status}`);
        console.log(`错误信息: ${JSON.stringify(sessionError.response.data, null, 2)}`);
      } else {
        console.log(`错误: ${sessionError.message}`);
      }
    }
    
    // 4. 测试我们的认证API
    console.log('\n4. 测试我们的认证API...');
    try {
      const authTestResponse = await client.get('/api/test-auth');
      
      console.log('认证测试响应状态:', authTestResponse.status);
      console.log('认证测试数据:', JSON.stringify(authTestResponse.data, null, 2));
      
    } catch (authError) {
      console.log('❌ 认证测试失败');
      if (authError.response) {
        console.log(`状态码: ${authError.response.status}`);
        console.log(`错误信息: ${JSON.stringify(authError.response.data, null, 2)}`);
      } else {
        console.log(`错误: ${authError.message}`);
      }
    }
    
    // 5. 测试中间人分配API
    console.log('\n5. 测试中间人分配API...');
    try {
      const mediatorResponse = await client.post('/api/mediator/auto-assign', {
        orderAmount: 999
      });
      
      console.log('中间人分配响应状态:', mediatorResponse.status);
      console.log('中间人分配数据:', JSON.stringify(mediatorResponse.data, null, 2));
      
    } catch (mediatorError) {
      console.log('❌ 中间人分配失败');
      if (mediatorError.response) {
        console.log(`状态码: ${mediatorError.response.status}`);
        console.log(`错误信息: ${JSON.stringify(mediatorError.response.data, null, 2)}`);
      } else {
        console.log(`错误: ${mediatorError.message}`);
      }
    }
    
    // 6. 生成调试建议
    console.log('\n=== 调试建议 ===');
    console.log('基于测试结果:');
    console.log('');
    console.log('1. 如果登录失败:');
    console.log('   - 检查用户密码是否正确');
    console.log('   - 验证NextAuth配置');
    console.log('   - 检查数据库连接');
    console.log('');
    console.log('2. 如果登录成功但session为空:');
    console.log('   - 检查JWT配置');
    console.log('   - 验证cookie设置');
    console.log('   - 检查域名和路径配置');
    console.log('');
    console.log('3. 如果session存在但API认证失败:');
    console.log('   - 检查getServerSession调用');
    console.log('   - 验证authOptions导入');
    console.log('   - 检查cookie传递');
    console.log('');
    console.log('4. 浏览器测试建议:');
    console.log('   - 清除所有cookies和缓存');
    console.log('   - 重新登录');
    console.log('   - 使用开发者工具检查Network请求');
    console.log('   - 检查Application标签页的cookies');
    
  } catch (error) {
    console.error('测试登录流程失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLoginFlow();
