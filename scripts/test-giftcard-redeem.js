const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testGiftCardRedeem() {
  try {
    console.log('🧪 开始测试礼品卡兑换功能...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('❌ 未找到管理员用户')
      return
    }

    console.log('👤 管理员信息:')
    console.log('  - ID:', admin.id)
    console.log('  - 邮箱:', admin.email)
    console.log('  - 当前余额:', admin.depositBalance, 'USDT')

    // 查找测试礼品卡
    const testGiftCardCode = 'RQ9V1KY6U4ZS7J5C'
    const giftCard = await prisma.giftCard.findUnique({
      where: { cardCode: testGiftCardCode },
      include: {
        product: true,
        soldTo: { select: { name: true } },
        redeemedBy: { select: { name: true } },
        assignedTo: { select: { name: true, email: true } }
      }
    })

    if (!giftCard) {
      console.error('❌ 礼品卡不存在:', testGiftCardCode)
      return
    }

    console.log('\n🎁 礼品卡信息:')
    console.log('  - 卡号:', giftCard.cardCode)
    console.log('  - 面值:', giftCard.faceValue, 'USDT')
    console.log('  - 状态:', giftCard.status)
    console.log('  - 有效期:', giftCard.validUntil)
    console.log('  - 商品:', giftCard.product?.name || '无关联商品')
    console.log('  - 已兑换:', giftCard.redeemedAt ? '是' : '否')

    if (giftCard.status !== 'GENERATED') {
      console.log('⚠️  礼品卡状态不是 GENERATED，无法兑换')
      return
    }

    // 模拟兑换过程
    console.log('\n💰 开始兑换礼品卡...')
    
    const result = await prisma.$transaction(async (tx) => {
      // 1. 更新礼品卡状态
      const updatedGiftCard = await tx.giftCard.update({
        where: { id: giftCard.id },
        data: {
          status: 'REDEEMED',
          redeemedAt: new Date(),
          redeemedById: admin.id,
          redeemedValue: giftCard.faceValue
        }
      })

      // 2. 增加用户余额
      await tx.user.update({
        where: { id: admin.id },
        data: {
          depositBalance: {
            increment: giftCard.faceValue
          }
        }
      })

      // 3. 创建交易记录
      await tx.giftCardTransaction.create({
        data: {
          giftCardId: giftCard.id,
          transactionType: 'REDEMPTION',
          amount: giftCard.faceValue,
          userId: admin.id,
          description: `用户兑换礼品卡，获得 ${giftCard.faceValue} USDT`,
          metadata: {
            cardCode: giftCard.cardCode,
            originalFaceValue: giftCard.faceValue,
            redeemedAt: new Date().toISOString()
          }
        }
      })

      // 4. 创建用户余额变动记录
      await tx.fundTransaction.create({
        data: {
          userId: admin.id,
          type: 'GIFT_CARD_REDEMPTION',
          amount: giftCard.faceValue,
          description: `兑换礼品卡: ${giftCard.cardCode}`,
          relatedId: giftCard.id,
          metadata: {
            relatedType: 'GIFT_CARD',
            cardCode: giftCard.cardCode,
            faceValue: giftCard.faceValue
          }
        }
      })

      return {
        giftCard: updatedGiftCard,
        redeemedAmount: giftCard.faceValue
      }
    })

    console.log('✅ 兑换成功!')
    console.log('  - 兑换金额:', result.redeemedAmount, 'USDT')
    console.log('  - 兑换时间:', result.giftCard.redeemedAt)

    // 验证结果
    const updatedAdmin = await prisma.user.findUnique({
      where: { id: admin.id },
      select: { depositBalance: true }
    })

    console.log('\n📊 兑换后状态:')
    console.log('  - 新余额:', updatedAdmin.depositBalance, 'USDT')
    console.log('  - 余额增加:', updatedAdmin.depositBalance - admin.depositBalance, 'USDT')

    // 检查交易记录
    const transactions = await prisma.fundTransaction.findMany({
      where: {
        userId: admin.id,
        type: 'GIFT_CARD_REDEMPTION'
      },
      orderBy: { createdAt: 'desc' },
      take: 1
    })

    if (transactions.length > 0) {
      console.log('\n📝 交易记录:')
      console.log('  - 类型:', transactions[0].type)
      console.log('  - 金额:', transactions[0].amount, 'USDT')
      console.log('  - 描述:', transactions[0].description)
      console.log('  - 时间:', transactions[0].createdAt)
    }

    console.log('\n🎉 礼品卡兑换功能测试完成！')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testGiftCardRedeem()
