#!/usr/bin/env node

/**
 * BitMarket 全面数据清理脚本
 * 清理所有测试数据、演示数据和临时文件
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`  ${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

// 清理数据库数据
async function cleanDatabaseData() {
  logHeader('清理数据库数据');

  try {
    // 使用事务来确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 按正确的依赖关系顺序删除数据

      // 1. 删除仲裁投票
      const arbitrationVotes = await tx.arbitrationVote.deleteMany({});
      log(`✅ 清理 arbitrationVote: ${arbitrationVotes.count} 条记录`, 'green');

      // 2. 删除托管争议
      const escrowDisputes = await tx.escrowDispute.deleteMany({});
      log(`✅ 清理 escrowDispute: ${escrowDisputes.count} 条记录`, 'green');

      // 3. 删除中间人奖励
      const mediatorRewards = await tx.mediatorReward.deleteMany({});
      log(`✅ 清理 mediatorReward: ${mediatorRewards.count} 条记录`, 'green');

      // 4. 删除提现券
      const withdrawalVouchers = await tx.withdrawalVoucher.deleteMany({});
      log(`✅ 清理 withdrawalVoucher: ${withdrawalVouchers.count} 条记录`, 'green');

      // 5. 删除托管订单
      const escrowOrders = await tx.escrowOrder.deleteMany({});
      log(`✅ 清理 escrowOrder: ${escrowOrders.count} 条记录`, 'green');

      // 6. 删除区块链交易
      const blockchainTransactions = await tx.blockchainTransaction.deleteMany({});
      log(`✅ 清理 blockchainTransaction: ${blockchainTransactions.count} 条记录`, 'green');

      // 7. 删除消息
      const messages = await tx.message.deleteMany({});
      log(`✅ 清理 message: ${messages.count} 条记录`, 'green');

      // 8. 删除订单
      const orders = await tx.order.deleteMany({});
      log(`✅ 清理 order: ${orders.count} 条记录`, 'green');

      // 9. 删除商品
      const products = await tx.product.deleteMany({});
      log(`✅ 清理 product: ${products.count} 条记录`, 'green');

      // 10. 最后删除用户（保留管理员）
      const users = await tx.user.deleteMany({
        where: {
          role: {
            not: 'ADMIN'
          }
        }
      });
      log(`✅ 清理 user: ${users.count} 条记录 (保留管理员)`, 'green');
    });

    log('✅ 数据库数据清理完成', 'green');
  } catch (error) {
    log(`❌ 数据库清理失败: ${error.message}`, 'red');
    // 如果事务失败，尝试逐个删除
    await fallbackCleanup();
  }
}

// 备用清理方法
async function fallbackCleanup() {
  log('🔄 尝试备用清理方法...', 'yellow');

  try {
    // 禁用外键检查（SQLite）
    await prisma.$executeRaw`PRAGMA foreign_keys = OFF;`;

    const tables = [
      'arbitrationVote',
      'escrowDispute',
      'mediatorReward',
      'withdrawalVoucher',
      'escrowOrder',
      'blockchainTransaction',
      'message',
      'order',
      'product'
    ];

    for (const table of tables) {
      try {
        const result = await prisma[table].deleteMany({});
        log(`✅ 清理 ${table}: ${result.count} 条记录`, 'green');
      } catch (error) {
        log(`⚠️ 跳过 ${table}: ${error.message}`, 'yellow');
      }
    }

    // 删除非管理员用户
    const users = await prisma.user.deleteMany({
      where: {
        role: {
          not: 'ADMIN'
        }
      }
    });
    log(`✅ 清理 user: ${users.count} 条记录 (保留管理员)`, 'green');

    // 重新启用外键检查
    await prisma.$executeRaw`PRAGMA foreign_keys = ON;`;

  } catch (error) {
    log(`❌ 备用清理也失败: ${error.message}`, 'red');
  }
}

// 清理上传文件
async function cleanUploadedFiles() {
  logHeader('清理上传文件');
  
  const uploadDirs = [
    'public/uploads',
    'public/images/products',
    'public/images/avatars',
    'public/images/payments'
  ];

  for (const dir of uploadDirs) {
    try {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        let deletedCount = 0;
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isFile() && file !== '.gitkeep') {
            fs.unlinkSync(filePath);
            deletedCount++;
          }
        }
        
        log(`✅ 清理 ${dir}: ${deletedCount} 个文件`, 'green');
      } else {
        log(`ℹ️ 目录不存在: ${dir}`, 'yellow');
      }
    } catch (error) {
      log(`❌ 清理 ${dir} 失败: ${error.message}`, 'red');
    }
  }
}

// 清理临时文件和缓存
async function cleanTempFiles() {
  logHeader('清理临时文件和缓存');
  
  const tempDirs = [
    '.next',
    'node_modules/.cache',
    'test-results',
    'coverage',
    'logs'
  ];

  for (const dir of tempDirs) {
    try {
      if (fs.existsSync(dir)) {
        if (dir === '.next') {
          // 对于 .next 目录，只清理内容，保留目录结构
          const files = fs.readdirSync(dir);
          for (const file of files) {
            const filePath = path.join(dir, file);
            fs.rmSync(filePath, { recursive: true, force: true });
          }
          log(`✅ 清理 ${dir} 内容`, 'green');
        } else {
          fs.rmSync(dir, { recursive: true, force: true });
          log(`✅ 删除 ${dir}`, 'green');
        }
      } else {
        log(`ℹ️ 目录不存在: ${dir}`, 'yellow');
      }
    } catch (error) {
      log(`❌ 清理 ${dir} 失败: ${error.message}`, 'red');
    }
  }
}

// 清理日志文件
async function cleanLogFiles() {
  logHeader('清理日志文件');
  
  const logFiles = [
    'test.db',
    'test.db-journal',
    'error.log',
    'access.log',
    'debug.log'
  ];

  for (const file of logFiles) {
    try {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        log(`✅ 删除 ${file}`, 'green');
      }
    } catch (error) {
      log(`❌ 删除 ${file} 失败: ${error.message}`, 'red');
    }
  }
}

// 重置数据库架构（可选）
async function resetDatabaseSchema() {
  logHeader('重置数据库架构');
  
  try {
    log('ℹ️ 如需重置数据库架构，请手动运行:', 'yellow');
    log('   npx prisma migrate reset --force', 'yellow');
    log('   npx prisma db push', 'yellow');
  } catch (error) {
    log(`❌ 数据库架构重置失败: ${error.message}`, 'red');
  }
}

// 主函数
async function main() {
  log('🗑️ BitMarket 数据清理工具', 'magenta');
  log('⚠️ 警告：此操作将删除所有数据，请确认后继续', 'red');
  
  // 检查是否在生产环境
  if (process.env.NODE_ENV === 'production') {
    log('❌ 禁止在生产环境运行此脚本', 'red');
    process.exit(1);
  }

  try {
    await cleanDatabaseData();
    await cleanUploadedFiles();
    await cleanTempFiles();
    await cleanLogFiles();
    await resetDatabaseSchema();
    
    logHeader('清理完成');
    log('✅ 所有数据已清理完成', 'green');
    log('ℹ️ 建议重新运行 npm run build 重新构建项目', 'yellow');
    
  } catch (error) {
    log(`❌ 清理过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
