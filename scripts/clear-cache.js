#!/usr/bin/env node

/**
 * 缓存清理脚本
 * 用于清理Redis缓存或特定模式的缓存
 */

const readline = require('readline')

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 询问用户确认
function askConfirmation(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes')
    })
  })
}

// 清理所有缓存
async function clearAllCache() {
  console.log('🧹 清理所有缓存...')
  
  try {
    const { cache } = require('../lib/cache-fallback')
    const result = await cache.clearCache()
    
    if (result) {
      console.log('✅ 所有缓存已清理')
    } else {
      console.log('❌ 缓存清理失败')
    }
  } catch (error) {
    console.error('❌ 缓存清理错误:', error.message)
  }
}

// 清理特定模式的缓存
async function clearPatternCache(pattern) {
  console.log(`🧹 清理模式 "${pattern}" 的缓存...`)
  
  try {
    const { cache } = require('../lib/cache-fallback')
    const count = await cache.delPattern(pattern)
    
    console.log(`✅ 清理了 ${count} 个缓存项`)
  } catch (error) {
    console.error('❌ 缓存清理错误:', error.message)
  }
}

// 清理用户相关缓存
async function clearUserCache(userId) {
  console.log(`🧹 清理用户 ${userId} 的缓存...`)
  
  try {
    const { CacheInvalidator } = require('../lib/api-cache-middleware')
    await CacheInvalidator.invalidateUser(userId)
    
    console.log('✅ 用户缓存已清理')
  } catch (error) {
    console.error('❌ 用户缓存清理错误:', error.message)
  }
}

// 清理商品相关缓存
async function clearProductCache(productId) {
  console.log(`🧹 清理商品 ${productId} 的缓存...`)
  
  try {
    const { CacheInvalidator } = require('../lib/api-cache-middleware')
    await CacheInvalidator.invalidateProduct(productId)
    
    console.log('✅ 商品缓存已清理')
  } catch (error) {
    console.error('❌ 商品缓存清理错误:', error.message)
  }
}

// 清理API缓存
async function clearApiCache() {
  console.log('🧹 清理API缓存...')
  
  try {
    const { cache } = require('../lib/cache-fallback')
    const count = await cache.delPattern('api:*')
    
    console.log(`✅ 清理了 ${count} 个API缓存项`)
  } catch (error) {
    console.error('❌ API缓存清理错误:', error.message)
  }
}

// 清理搜索缓存
async function clearSearchCache() {
  console.log('🧹 清理搜索缓存...')
  
  try {
    const { CacheInvalidator } = require('../lib/api-cache-middleware')
    await CacheInvalidator.invalidateSearch()
    
    console.log('✅ 搜索缓存已清理')
  } catch (error) {
    console.error('❌ 搜索缓存清理错误:', error.message)
  }
}

// 显示缓存统计
async function showCacheStats() {
  console.log('📊 缓存统计信息:')
  
  try {
    const { cache } = require('../lib/cache-fallback')
    
    // 检查Redis连接
    const isHealthy = await cache.healthCheck()
    if (!isHealthy) {
      console.log('❌ Redis连接失败')
      return
    }
    
    console.log('✅ Redis连接正常')
    
    // 获取缓存键统计
    const patterns = [
      'user:*',
      'product:*',
      'order:*',
      'api:*',
      'search:*',
      'session:*',
      'stats:*'
    ]
    
    for (const pattern of patterns) {
      try {
        const keys = await cache.redis.keys(pattern)
        console.log(`   ${pattern}: ${keys.length} 个键`)
      } catch (error) {
        console.log(`   ${pattern}: 获取失败`)
      }
    }
    
  } catch (error) {
    console.error('❌ 获取缓存统计失败:', error.message)
  }
}

// 缓存预热
async function warmupCache() {
  console.log('🔥 开始缓存预热...')
  
  try {
    const { cache } = require('../lib/cache-fallback')
    await cache.warmupCache()
    
    console.log('✅ 缓存预热完成')
  } catch (error) {
    console.error('❌ 缓存预热失败:', error.message)
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
🧹 缓存管理工具

用法:
  node scripts/clear-cache.js [选项]

选项:
  --all                清理所有缓存
  --pattern <pattern>  清理匹配模式的缓存
  --user <userId>      清理特定用户的缓存
  --product <id>       清理特定商品的缓存
  --api                清理API缓存
  --search             清理搜索缓存
  --stats              显示缓存统计
  --warmup             缓存预热
  --help               显示帮助信息

示例:
  node scripts/clear-cache.js --all
  node scripts/clear-cache.js --pattern "user:*"
  node scripts/clear-cache.js --user user123
  node scripts/clear-cache.js --stats
`)
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0 || args.includes('--help')) {
    showHelp()
    rl.close()
    return
  }
  
  try {
    if (args.includes('--stats')) {
      await showCacheStats()
    } else if (args.includes('--warmup')) {
      await warmupCache()
    } else if (args.includes('--all')) {
      const confirmed = await askConfirmation('确定要清理所有缓存吗? (y/N): ')
      if (confirmed) {
        await clearAllCache()
      } else {
        console.log('操作已取消')
      }
    } else if (args.includes('--api')) {
      await clearApiCache()
    } else if (args.includes('--search')) {
      await clearSearchCache()
    } else if (args.includes('--pattern')) {
      const patternIndex = args.indexOf('--pattern')
      const pattern = args[patternIndex + 1]
      if (pattern) {
        const confirmed = await askConfirmation(`确定要清理模式 "${pattern}" 的缓存吗? (y/N): `)
        if (confirmed) {
          await clearPatternCache(pattern)
        } else {
          console.log('操作已取消')
        }
      } else {
        console.error('❌ 请提供缓存模式')
      }
    } else if (args.includes('--user')) {
      const userIndex = args.indexOf('--user')
      const userId = args[userIndex + 1]
      if (userId) {
        await clearUserCache(userId)
      } else {
        console.error('❌ 请提供用户ID')
      }
    } else if (args.includes('--product')) {
      const productIndex = args.indexOf('--product')
      const productId = args[productIndex + 1]
      if (productId) {
        await clearProductCache(productId)
      } else {
        console.error('❌ 请提供商品ID')
      }
    } else {
      console.error('❌ 未知选项，使用 --help 查看帮助')
    }
    
  } catch (error) {
    console.error('❌ 执行过程中出现错误:', error.message)
    process.exit(1)
  } finally {
    rl.close()
    
    // 断开Redis连接
    try {
      const { cache } = require('../lib/cache-fallback')
      await cache.disconnect()
    } catch (error) {
      // 忽略断开连接的错误
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  clearAllCache,
  clearPatternCache,
  clearUserCache,
  clearProductCache,
  clearApiCache,
  clearSearchCache,
  showCacheStats,
  warmupCache
}
