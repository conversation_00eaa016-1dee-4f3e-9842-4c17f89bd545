#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testSessionProvider() {
  try {
    console.log('🧪 测试SessionProvider配置')
    console.log('='.repeat(50))

    // 1. 检查数据库连接
    console.log('\n📊 1. 检查数据库连接...')
    const userCount = await prisma.user.count()
    console.log(`✅ 数据库连接正常，用户总数: ${userCount}`)

    // 2. 检查是否有测试用户
    console.log('\n👤 2. 检查测试用户...')
    let testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!testUser) {
      console.log('⚠️  测试用户不存在，正在创建...')
      const hashedPassword = await bcrypt.hash('123456', 12)
      
      testUser = await prisma.user.create({
        data: {
          id: `test-user-${Date.now()}`,
          userId: `test-${Date.now()}`,
          name: '测试用户',
          email: '<EMAIL>',
          password: hashedPassword,
          city: '测试城市',
          creditScore: 100,
          role: 'USER'
        }
      })
      console.log('✅ 测试用户创建成功')
    } else {
      console.log('✅ 测试用户已存在')
    }

    console.log(`   用户ID: ${testUser.id}`)
    console.log(`   自定义ID: ${testUser.userId}`)
    console.log(`   邮箱: ${testUser.email}`)
    console.log(`   姓名: ${testUser.name}`)

    // 3. 检查NextAuth配置文件
    console.log('\n🔧 3. 检查NextAuth配置文件...')
    const fs = require('fs')
    const path = require('path')

    const configFiles = [
      'lib/auth.ts',
      'app/api/auth/[...nextauth]/route.ts',
      'components/providers/session-provider.tsx',
      'app/layout.tsx'
    ]

    configFiles.forEach(file => {
      const fullPath = path.join(process.cwd(), file)
      if (fs.existsSync(fullPath)) {
        console.log(`✅ ${file}`)
        
        const content = fs.readFileSync(fullPath, 'utf8')
        
        if (file === 'lib/auth.ts') {
          if (content.includes('NextAuthOptions') && content.includes('CredentialsProvider')) {
            console.log('   - NextAuth配置正确 ✅')
          } else {
            console.log('   - NextAuth配置可能有问题 ⚠️')
          }
        }
        
        if (file === 'components/providers/session-provider.tsx') {
          if (content.includes('SessionProvider') && content.includes("'use client'")) {
            console.log('   - SessionProvider配置正确 ✅')
          } else {
            console.log('   - SessionProvider配置可能有问题 ⚠️')
          }
        }
        
        if (file === 'app/layout.tsx') {
          if (content.includes('AuthSessionProvider')) {
            console.log('   - Layout中包含SessionProvider ✅')
          } else {
            console.log('   - Layout中缺少SessionProvider ⚠️')
          }
        }
      } else {
        console.log(`❌ ${file} (文件不存在)`)
      }
    })

    // 4. 检查环境变量
    console.log('\n🌍 4. 检查环境变量...')
    const requiredEnvVars = [
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL',
      'DATABASE_URL'
    ]

    requiredEnvVars.forEach(envVar => {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar}: 已设置`)
      } else {
        console.log(`❌ ${envVar}: 未设置`)
      }
    })

    // 5. 生成测试建议
    console.log('\n💡 5. 测试建议...')
    console.log('如果仍然遇到SessionProvider错误，请尝试以下步骤:')
    console.log('')
    console.log('1. 重启开发服务器:')
    console.log('   npm run dev')
    console.log('')
    console.log('2. 清除Next.js缓存:')
    console.log('   rm -rf .next')
    console.log('   npm run dev')
    console.log('')
    console.log('3. 检查浏览器控制台是否有其他错误')
    console.log('')
    console.log('4. 尝试访问测试页面:')
    console.log('   http://localhost:3000/test-session')
    console.log('')
    console.log('5. 使用测试用户登录:')
    console.log('   邮箱: <EMAIL>')
    console.log('   密码: 123456')

    // 6. 检查页面文件
    console.log('\n📄 6. 检查问题页面...')
    const problemFile = 'app/settings/page.tsx'
    const problemPath = path.join(process.cwd(), problemFile)
    
    if (fs.existsSync(problemPath)) {
      console.log(`✅ ${problemFile} 存在`)
      
      const content = fs.readFileSync(problemPath, 'utf8')
      if (content.includes("'use client'")) {
        console.log('   - 包含 use client 指令 ✅')
      } else {
        console.log('   - 缺少 use client 指令 ⚠️')
      }
      
      if (content.includes('useSession')) {
        console.log('   - 使用 useSession hook ✅')
      } else {
        console.log('   - 未使用 useSession hook ❓')
      }
    } else {
      console.log(`❌ ${problemFile} 不存在`)
    }

    console.log('\n✅ SessionProvider配置检查完成')
    console.log('\n🔍 如果问题仍然存在，可能的原因:')
    console.log('1. 浏览器缓存问题 - 尝试硬刷新 (Ctrl+Shift+R)')
    console.log('2. Next.js缓存问题 - 删除 .next 文件夹并重启')
    console.log('3. 组件渲染时机问题 - 检查是否在服务端渲染时调用了 useSession')
    console.log('4. 依赖版本问题 - 检查 next-auth 版本是否兼容')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testSessionProvider()
    .then(() => {
      console.log('\n✅ 测试完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 测试失败:', error)
      process.exit(1)
    })
}

module.exports = { testSessionProvider }
