const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestPayment() {
  try {
    console.log('🔧 创建测试支付订单...')
    
    // 查找现有的商品和用户
    const product = await prisma.product.findFirst({
      where: { status: 'AVAILABLE' }
    })
    
    const buyer = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })
    
    const seller = await prisma.user.findFirst({
      where: { 
        email: { not: '<EMAIL>' }
      }
    })
    
    if (!product || !buyer || !seller) {
      console.log('❌ 缺少必要的测试数据（商品、买家或卖家）')
      return
    }
    
    // 创建测试订单
    const orderId = 'test-order-' + Math.random().toString(36).substring(2, 14)
    
    const order = await prisma.order.create({
      data: {
        id: orderId,
        productId: product.id,
        buyerId: buyer.id,
        sellerId: seller.id,
        totalAmount: product.price,
        productPrice: product.price,
        status: 'PENDING_PAYMENT',
        paymentConfirmed: false,
        paymentTxHash: 'test-tx-' + Math.random().toString(36).substring(2, 10),
        shippingAddress: {
          address: '测试地址',
          note: '测试订单，用于验证支付确认功能'
        }
      },
      include: {
        product: {
          select: {
            title: true,
            price: true
          }
        },
        buyer: {
          select: {
            email: true,
            name: true
          }
        },
        seller: {
          select: {
            email: true,
            name: true
          }
        }
      }
    })
    
    console.log('✅ 测试订单创建成功！')
    console.log('=' .repeat(50))
    console.log(`订单ID: ${order.id}`)
    console.log(`商品: ${order.product.title}`)
    console.log(`价格: ${order.product.price} USDT`)
    console.log(`买家: ${order.buyer.email} (${order.buyer.name || '未设置'})`)
    console.log(`卖家: ${order.seller.email} (${order.seller.name || '未设置'})`)
    console.log(`状态: ${order.status}`)
    console.log(`支付确认: ${order.paymentConfirmed ? '是' : '否'}`)
    console.log(`交易哈希: ${order.paymentTxHash}`)
    
    console.log('\n💡 现在可以在管理员支付页面测试确认功能：')
    console.log('   http://localhost:3000/admin/payments')
    
  } catch (error) {
    console.error('创建测试订单失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestPayment()
  .then(() => {
    console.log('\n🎉 测试订单创建完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error)
    process.exit(1)
  })
