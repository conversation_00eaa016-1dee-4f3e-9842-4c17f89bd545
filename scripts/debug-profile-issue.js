/**
 * 调试用户资料404问题
 * 检查数据库中的用户数据和会话状态
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugProfileIssue() {
  console.log('🔍 开始调试用户资料404问题...')
  console.log('='.repeat(50))

  try {
    // 1. 检查数据库连接
    console.log('1. 检查数据库连接...')
    await prisma.$connect()
    console.log('   ✅ 数据库连接成功')

    // 2. 检查用户表结构
    console.log('\n2. 检查用户表结构...')
    const userCount = await prisma.user.count()
    console.log(`   📊 用户总数: ${userCount}`)

    if (userCount === 0) {
      console.log('   ⚠️  数据库中没有用户数据')
      console.log('   💡 建议: 创建测试用户')
      await createTestUser()
    } else {
      console.log('   ✅ 数据库中有用户数据')
    }

    // 3. 列出所有用户
    console.log('\n3. 列出现有用户...')
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true
      },
      take: 10
    })

    if (users.length > 0) {
      console.log('   📋 用户列表:')
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ID: ${user.id}`)
        console.log(`      邮箱: ${user.email}`)
        console.log(`      姓名: ${user.name || '未设置'}`)
        console.log(`      创建时间: ${user.createdAt.toISOString()}`)
        console.log('')
      })
    }

    // 4. 检查会话表
    console.log('4. 检查会话数据...')
    const sessionCount = await prisma.session.count()
    console.log(`   📊 会话总数: ${sessionCount}`)

    if (sessionCount > 0) {
      const recentSessions = await prisma.session.findMany({
        select: {
          id: true,
          userId: true,
          expires: true
        },
        orderBy: {
          expires: 'desc'
        },
        take: 5
      })

      console.log('   📋 最近会话:')
      recentSessions.forEach((session, index) => {
        const isExpired = new Date() > session.expires
        console.log(`   ${index + 1}. 会话ID: ${session.id.substring(0, 8)}...`)
        console.log(`      用户ID: ${session.userId}`)
        console.log(`      过期时间: ${session.expires.toISOString()}`)
        console.log(`      状态: ${isExpired ? '已过期' : '有效'}`)
        console.log('')
      })
    }

    // 5. 检查账户表
    console.log('5. 检查账户绑定...')
    const accountCount = await prisma.account.count()
    console.log(`   📊 账户绑定总数: ${accountCount}`)

    if (accountCount > 0) {
      const accounts = await prisma.account.findMany({
        select: {
          id: true,
          userId: true,
          provider: true,
          type: true
        },
        take: 5
      })

      console.log('   📋 账户绑定:')
      accounts.forEach((account, index) => {
        console.log(`   ${index + 1}. 用户ID: ${account.userId}`)
        console.log(`      提供商: ${account.provider}`)
        console.log(`      类型: ${account.type}`)
        console.log('')
      })
    }

    // 6. 测试API路径
    console.log('6. 检查API路径...')
    const fs = require('fs')
    const path = require('path')
    
    const apiPath = path.join(process.cwd(), 'app/api/user/profile/route.ts')
    const apiExists = fs.existsSync(apiPath)
    console.log(`   📁 API文件存在: ${apiExists ? '是' : '否'}`)
    
    if (apiExists) {
      const content = fs.readFileSync(apiPath, 'utf8')
      const hasGetMethod = content.includes('export async function GET')
      const hasPutMethod = content.includes('export async function PUT')
      console.log(`   🔧 GET方法: ${hasGetMethod ? '存在' : '缺失'}`)
      console.log(`   🔧 PUT方法: ${hasPutMethod ? '存在' : '缺失'}`)
    }

    // 7. 生成修复建议
    console.log('\n7. 生成修复建议...')
    await generateFixSuggestions(userCount, sessionCount, accountCount)

  } catch (error) {
    console.error('❌ 调试过程中出现错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

async function createTestUser() {
  console.log('\n🔧 创建测试用户...')
  
  try {
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '测试用户',
        depositBalance: 1000,
        creditScore: 750
      }
    })

    console.log('   ✅ 测试用户创建成功:')
    console.log(`   ID: ${testUser.id}`)
    console.log(`   邮箱: ${testUser.email}`)
    console.log(`   姓名: ${testUser.name}`)

    // 创建对应的账户记录
    const testAccount = await prisma.account.create({
      data: {
        userId: testUser.id,
        type: 'credentials',
        provider: 'credentials',
        providerAccountId: testUser.id
      }
    })

    console.log('   ✅ 测试账户创建成功')

    return testUser

  } catch (error) {
    console.error('   ❌ 创建测试用户失败:', error.message)
  }
}

async function generateFixSuggestions(userCount, sessionCount, accountCount) {
  const suggestions = []

  if (userCount === 0) {
    suggestions.push({
      issue: '数据库中没有用户',
      solution: '需要注册用户或导入用户数据',
      action: '访问 /auth/register 注册新用户'
    })
  }

  if (sessionCount === 0) {
    suggestions.push({
      issue: '没有活跃会话',
      solution: '用户需要重新登录',
      action: '访问 /auth/signin 重新登录'
    })
  }

  if (accountCount === 0) {
    suggestions.push({
      issue: '没有账户绑定记录',
      solution: '检查NextAuth配置',
      action: '确保认证提供商配置正确'
    })
  }

  if (suggestions.length === 0) {
    suggestions.push({
      issue: '数据看起来正常',
      solution: '可能是会话过期或权限问题',
      action: '检查浏览器控制台和网络请求'
    })
  }

  console.log('💡 修复建议:')
  suggestions.forEach((suggestion, index) => {
    console.log(`${index + 1}. 问题: ${suggestion.issue}`)
    console.log(`   解决方案: ${suggestion.solution}`)
    console.log(`   操作: ${suggestion.action}`)
    console.log('')
  })
}

// 运行调试
if (require.main === module) {
  debugProfileIssue()
    .then(() => {
      console.log('🎉 调试完成!')
    })
    .catch(error => {
      console.error('💥 调试失败:', error)
      process.exit(1)
    })
}

module.exports = { debugProfileIssue, createTestUser }
