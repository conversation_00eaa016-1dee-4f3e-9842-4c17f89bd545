const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugEscrowCreationDetailed() {
  try {
    console.log('=== 详细调试中间人订单创建问题 ===\n');
    
    const productId = 'cmdr1z4vv00078oqqm2k4gq7v';
    
    // 1. 检查商品和用户信息
    console.log('1. 检查商品和用户信息...');
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    });
    
    if (!product) {
      console.log('❌ 商品不存在');
      return;
    }
    
    console.log(`商品: ${product.title}`);
    console.log(`价格: ${product.price} USDT`);
    console.log(`卖家: ${product.seller.name} (角色: ${product.seller.role})`);
    
    // 2. 检查最近的订单
    console.log('\n2. 检查最近的订单...');
    const recentOrders = await prisma.order.findMany({
      where: {
        productId: productId
      },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });
    
    console.log(`最近的订单数量: ${recentOrders.length}`);
    recentOrders.forEach((order, index) => {
      console.log(`${index + 1}. 订单ID: ${order.id}`);
      console.log(`   状态: ${order.status}`);
      console.log(`   金额: ${order.totalAmount} USDT`);
      console.log(`   买家: ${order.buyer.name} (${order.buyer.email})`);
      console.log(`   创建时间: ${order.createdAt.toISOString()}`);
      console.log('');
    });
    
    // 3. 检查托管订单
    console.log('3. 检查托管订单...');
    const escrowOrders = await prisma.escrowOrder.findMany({
      where: {
        orderId: {
          in: recentOrders.map(o => o.id)
        }
      },
      include: {
        mediator: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    console.log(`托管订单数量: ${escrowOrders.length}`);
    escrowOrders.forEach((escrow, index) => {
      console.log(`${index + 1}. 托管订单ID: ${escrow.id}`);
      console.log(`   关联订单ID: ${escrow.orderId}`);
      console.log(`   状态: ${escrow.status}`);
      console.log(`   金额: ${escrow.amount} USDT`);
      console.log(`   中间人: ${escrow.mediator.name}`);
      console.log('');
    });
    
    // 4. 检查中间人状态
    console.log('4. 检查中间人状态...');
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        mediatorStatus: true,
        availableBalance: true,
        mediatorFeeRate: true
      }
    });
    
    console.log(`中间人总数: ${mediators.length}`);
    mediators.forEach((mediator, index) => {
      console.log(`${index + 1}. ${mediator.name}`);
      console.log(`   状态: ${mediator.mediatorStatus}`);
      console.log(`   可用余额: ${mediator.availableBalance} USDT`);
      console.log(`   费率: ${(mediator.mediatorFeeRate * 100).toFixed(2)}%`);
      console.log('');
    });
    
    // 5. 模拟订单创建流程
    console.log('5. 模拟订单创建流程...');
    
    // 检查是否有测试用户
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          contains: 'test'
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      },
      take: 3
    });
    
    console.log(`测试用户数量: ${testUsers.length}`);
    testUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   角色: ${user.role}`);
      console.log(`   ID: ${user.id}`);
      console.log('');
    });
    
    // 6. 检查可能的错误原因
    console.log('6. 检查可能的错误原因...');
    
    const issues = [];
    
    // 检查商品价格
    if (product.price < 100) {
      issues.push(`商品价格 ${product.price} USDT 低于最低要求 100 USDT`);
    }
    
    // 检查活跃中间人
    const activeMediators = mediators.filter(m => m.mediatorStatus === 'ACTIVE');
    if (activeMediators.length === 0) {
      issues.push('没有活跃的中间人');
    }
    
    // 检查保证金充足的中间人
    const qualifiedMediators = activeMediators.filter(m => m.availableBalance >= product.price);
    if (qualifiedMediators.length === 0) {
      issues.push('没有保证金充足的中间人');
    }
    
    // 检查最近的失败订单
    const failedOrders = recentOrders.filter(o => o.status === 'CANCELLED');
    if (failedOrders.length > 0) {
      issues.push(`有 ${failedOrders.length} 个失败的订单`);
    }
    
    if (issues.length > 0) {
      console.log('发现的问题:');
      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    } else {
      console.log('✅ 基本条件都满足');
    }
    
    // 7. 生成调试建议
    console.log('\n=== 调试建议 ===');
    console.log('请检查以下几点:');
    console.log('');
    console.log('1. 浏览器开发者工具:');
    console.log('   - 打开 F12 开发者工具');
    console.log('   - 查看 Network 标签页');
    console.log('   - 重新尝试购买，观察 API 请求');
    console.log('   - 检查是否有 401、403、500 等错误');
    console.log('');
    console.log('2. 控制台错误:');
    console.log('   - 查看 Console 标签页');
    console.log('   - 查找 JavaScript 错误信息');
    console.log('   - 特别注意认证相关错误');
    console.log('');
    console.log('3. API 请求详情:');
    console.log('   - 检查 /api/orders 请求是否成功');
    console.log('   - 检查 /api/mediator/auto-assign 请求');
    console.log('   - 检查 /api/escrow/create 请求');
    console.log('');
    console.log('4. 用户登录状态:');
    console.log('   - 确认用户已正确登录');
    console.log('   - 检查 session 是否有效');
    console.log('   - 尝试刷新页面重新登录');
    
    // 8. 提供测试命令
    console.log('\n=== 测试命令 ===');
    console.log('可以使用以下命令进行进一步测试:');
    console.log('');
    console.log('# 检查服务器日志');
    console.log('tail -f logs/*.log');
    console.log('');
    console.log('# 测试 API 端点');
    console.log('curl -X POST http://localhost:3000/api/mediator/auto-assign \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"orderAmount": 999}\'');
    
  } catch (error) {
    console.error('调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugEscrowCreationDetailed();
