#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

async function fixNextAuthError() {
  try {
    console.log('🔧 修复NextAuth SessionProvider错误')
    console.log('='.repeat(50))

    // 1. 清理Next.js缓存
    console.log('\n🧹 1. 清理Next.js缓存...')
    const nextDir = path.join(process.cwd(), '.next')
    
    if (fs.existsSync(nextDir)) {
      console.log('正在删除 .next 目录...')
      fs.rmSync(nextDir, { recursive: true, force: true })
      console.log('✅ .next 目录已删除')
    } else {
      console.log('✅ .next 目录不存在，无需删除')
    }

    // 2. 检查并修复SessionProvider
    console.log('\n🔧 2. 检查并修复SessionProvider...')
    const sessionProviderPath = path.join(process.cwd(), 'components/providers/session-provider.tsx')
    
    if (fs.existsSync(sessionProviderPath)) {
      const content = fs.readFileSync(sessionProviderPath, 'utf8')
      
      // 检查是否包含必要的配置
      if (!content.includes('refetchInterval') || !content.includes('refetchOnWindowFocus')) {
        console.log('⚠️  SessionProvider配置不完整，正在修复...')
        
        const fixedContent = `'use client'

import { SessionProvider } from 'next-auth/react'
import { Session } from 'next-auth'
import { ReactNode } from 'react'

interface AuthSessionProviderProps {
  children: ReactNode
  session?: Session | null
}

export default function AuthSessionProvider({
  children,
  session
}: AuthSessionProviderProps) {
  return (
    <SessionProvider 
      session={session} 
      refetchInterval={5 * 60} // 5分钟刷新一次
      refetchOnWindowFocus={true}
      refetchWhenOffline={false}
    >
      {children}
    </SessionProvider>
  )
}
`
        
        fs.writeFileSync(sessionProviderPath, fixedContent)
        console.log('✅ SessionProvider已修复')
      } else {
        console.log('✅ SessionProvider配置正确')
      }
    } else {
      console.log('❌ SessionProvider文件不存在')
    }

    // 3. 检查layout.tsx
    console.log('\n📄 3. 检查layout.tsx...')
    const layoutPath = path.join(process.cwd(), 'app/layout.tsx')
    
    if (fs.existsSync(layoutPath)) {
      const content = fs.readFileSync(layoutPath, 'utf8')
      
      if (content.includes('AuthSessionProvider')) {
        console.log('✅ layout.tsx包含AuthSessionProvider')
      } else {
        console.log('⚠️  layout.tsx缺少AuthSessionProvider')
      }
    } else {
      console.log('❌ layout.tsx不存在')
    }

    // 4. 检查next-auth版本
    console.log('\n📦 4. 检查next-auth版本...')
    const packageJsonPath = path.join(process.cwd(), 'package.json')
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      const nextAuthVersion = packageJson.dependencies?.['next-auth'] || packageJson.devDependencies?.['next-auth']
      
      if (nextAuthVersion) {
        console.log(`✅ next-auth版本: ${nextAuthVersion}`)
        
        // 检查是否是兼容版本
        if (nextAuthVersion.includes('4.')) {
          console.log('✅ next-auth版本兼容')
        } else {
          console.log('⚠️  next-auth版本可能不兼容，建议使用4.x版本')
        }
      } else {
        console.log('❌ 未找到next-auth依赖')
      }
    }

    // 5. 创建测试页面
    console.log('\n🧪 5. 创建测试页面...')
    const testPagePath = path.join(process.cwd(), 'app/test-session-fix/page.tsx')
    const testPageDir = path.dirname(testPagePath)
    
    if (!fs.existsSync(testPageDir)) {
      fs.mkdirSync(testPageDir, { recursive: true })
    }
    
    const testPageContent = `'use client'

import { useSession, signIn, signOut } from 'next-auth/react'
import { useEffect, useState } from 'react'

export default function TestSessionFixPage() {
  const { data: session, status } = useSession()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 测试useSession是否正常工作
    try {
      console.log('Session status:', status)
      console.log('Session data:', session)
      setError(null)
    } catch (err) {
      console.error('Session error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }, [session, status])

  if (error) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">SessionProvider错误</h1>
          <p className="text-red-700 mb-4">{error}</p>
          <p className="text-sm text-gray-600">
            请检查SessionProvider是否正确配置在app/layout.tsx中
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">SessionProvider修复测试</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">会话状态</h2>
          <div className="space-y-2">
            <div><strong>状态:</strong> {status}</div>
            <div><strong>会话存在:</strong> {session ? '是' : '否'}</div>
            {session && (
              <div className="mt-4 space-y-2">
                <div><strong>用户ID:</strong> {session.user?.id}</div>
                <div><strong>邮箱:</strong> {session.user?.email}</div>
                <div><strong>姓名:</strong> {session.user?.name}</div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">操作</h2>
          <div className="space-x-4">
            {!session ? (
              <button
                onClick={() => signIn()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                登录
              </button>
            ) : (
              <button
                onClick={() => signOut()}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
              >
                退出登录
              </button>
            )}
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-green-800 mb-4">✅ SessionProvider修复成功</h2>
          <p className="text-green-700">
            如果您能看到这个页面并且没有错误，说明SessionProvider已经正确配置。
          </p>
          <div className="mt-4">
            <a 
              href="/settings" 
              className="text-blue-600 hover:text-blue-800 underline"
            >
              返回设置页面测试
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
`
    
    fs.writeFileSync(testPagePath, testPageContent)
    console.log('✅ 测试页面已创建: /test-session-fix')

    // 6. 生成修复报告
    console.log('\n📊 6. 修复完成报告...')
    console.log('='.repeat(50))
    console.log('✅ Next.js缓存已清理')
    console.log('✅ SessionProvider配置已检查/修复')
    console.log('✅ 测试页面已创建')
    console.log('')
    console.log('🔄 下一步操作:')
    console.log('1. 重启开发服务器: npm run dev')
    console.log('2. 访问测试页面: http://localhost:3000/test-session-fix')
    console.log('3. 如果测试页面正常，再访问: http://localhost:3000/settings')
    console.log('')
    console.log('💡 如果问题仍然存在:')
    console.log('1. 检查浏览器控制台是否有其他错误')
    console.log('2. 尝试在无痕模式下访问')
    console.log('3. 检查是否有其他中间件或组件干扰')
    console.log('4. 确保所有使用useSession的组件都有"use client"指令')

  } catch (error) {
    console.error('❌ 修复失败:', error)
    throw error
  }
}

// 运行修复
if (require.main === module) {
  fixNextAuthError()
    .then(() => {
      console.log('\n✅ NextAuth错误修复完成')
      console.log('请重启开发服务器并测试')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 修复失败:', error)
      process.exit(1)
    })
}

module.exports = { fixNextAuthError }
