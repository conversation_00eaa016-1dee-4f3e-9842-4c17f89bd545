const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testMediatorApplicationFlow() {
  console.log('🔍 测试中间人申请流程...\n')

  try {
    // 1. 检查现有用户
    console.log('1. 检查现有用户...')
    const users = await prisma.user.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        email: true,
        isMediator: true,
        mediatorStatus: true,
        depositBalance: true
      }
    })

    console.log(`✅ 找到 ${users.length} 个用户`)
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (${user.email})`)
      console.log(`      中间人: ${user.isMediator ? '是' : '否'}`)
      console.log(`      状态: ${user.mediatorStatus || '无'}`)
      console.log(`      余额: ${user.depositBalance} USDT`)
    })

    if (users.length === 0) {
      console.log('❌ 没有用户，请先创建用户')
      return
    }

    // 2. 为测试用户设置足够的保证金
    console.log('\n2. 为测试用户设置保证金...')
    const testUser = users[0]
    
    if (testUser.depositBalance < 5000) {
      await prisma.user.update({
        where: { id: testUser.id },
        data: { depositBalance: 10000 }
      })
      console.log(`✅ 为用户 ${testUser.name} 设置保证金: 10000 USDT`)
    } else {
      console.log(`✅ 用户 ${testUser.name} 已有足够保证金: ${testUser.depositBalance} USDT`)
    }

    // 3. 检查现有申请
    console.log('\n3. 检查现有中间人申请...')
    const applications = await prisma.mediatorApplication.findMany({
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    console.log(`✅ 找到 ${applications.length} 个申请`)
    applications.forEach((app, index) => {
      console.log(`   ${index + 1}. ${app.user.name} - ${app.status}`)
      console.log(`      保证金: ${app.depositAmount} USDT`)
      console.log(`      费率: ${(app.feeRate * 100).toFixed(1)}%`)
      console.log(`      申请时间: ${new Date(app.createdAt).toLocaleDateString('zh-CN')}`)
    })

    // 4. 创建测试申请（如果没有待审核的申请）
    const pendingApplications = applications.filter(app => app.status === 'PENDING')
    
    if (pendingApplications.length === 0) {
      console.log('\n4. 创建测试中间人申请...')
      
      // 找一个非中间人用户
      const nonMediatorUser = users.find(user => !user.isMediator)
      
      if (nonMediatorUser) {
        try {
          // 确保用户有足够余额
          await prisma.user.update({
            where: { id: nonMediatorUser.id },
            data: { depositBalance: 15000 }
          })

          const testApplication = await prisma.mediatorApplication.create({
            data: {
              userId: nonMediatorUser.id,
              bnbWalletAddress: '0x' + Math.random().toString(16).substr(2, 40),
              depositAmount: 5000,
              feeRate: 0.025, // 2.5%
              experience: '具有3年电商平台交易经验，熟悉各类交易纠纷处理流程，能够公正客观地处理买卖双方争议。',
              introduction: '我希望成为平台的中间人，为用户提供专业的交易调解服务，确保交易的公平性和安全性。',
              status: 'PENDING'
            }
          })

          console.log(`✅ 为用户 ${nonMediatorUser.name} 创建测试申请`)
          console.log(`   申请ID: ${testApplication.id}`)
        } catch (error) {
          console.log(`❌ 创建申请失败: ${error.message}`)
        }
      } else {
        console.log('⚠️  所有用户都已是中间人，无法创建新申请')
      }
    } else {
      console.log(`\n4. 已有 ${pendingApplications.length} 个待审核申请`)
    }

    // 5. 检查管理员用户
    console.log('\n5. 检查管理员用户...')
    const adminUsers = await prisma.user.findMany({
      where: {
        role: 'ADMIN'
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    })

    console.log(`✅ 找到 ${adminUsers.length} 个管理员`)
    adminUsers.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.name} (${admin.email})`)
    })

    if (adminUsers.length === 0) {
      console.log('⚠️  没有管理员用户，创建一个测试管理员...')
      
      try {
        const adminUser = await prisma.user.create({
          data: {
            name: '系统管理员',
            email: '<EMAIL>',
            role: 'ADMIN',
            status: 'ACTIVE',
            creditScore: 100,
            depositBalance: 0
          }
        })
        console.log(`✅ 创建管理员: ${adminUser.name} (${adminUser.email})`)
      } catch (error) {
        console.log(`❌ 创建管理员失败: ${error.message}`)
      }
    }

    // 6. 验证API接口
    console.log('\n6. 验证相关API接口...')
    
    try {
      const fetch = require('node-fetch')
      
      // 测试获取申请列表API
      console.log('   测试申请列表API...')
      const listResponse = await fetch('http://localhost:3000/api/admin/mediator/applications')
      
      if (listResponse.status === 403) {
        console.log('   ✅ API权限控制正常 (需要管理员权限)')
      } else if (listResponse.ok) {
        console.log('   ✅ API接口正常')
      } else {
        console.log(`   ⚠️  API响应异常: ${listResponse.status}`)
      }

    } catch (error) {
      console.log(`   ⚠️  API测试跳过: ${error.message}`)
    }

    // 7. 功能测试清单
    console.log('\n7. 功能测试清单...')
    console.log('✅ 需要测试的功能:')
    console.log('   □ 用户在保证金页面提交中间人申请')
    console.log('   □ 管理员在管理后台查看申请列表')
    console.log('   □ 管理员审核申请 (批准/拒绝)')
    console.log('   □ 批准后用户成为中间人')
    console.log('   □ 中间人用户在个人资料页面看到控制台')
    console.log('   □ 普通用户不显示中间人控制台')

    console.log('\n🎉 测试准备完成！')
    console.log('\n📝 手动测试步骤:')
    
    console.log('\n【用户申请流程】')
    console.log('1. 用户登录: http://localhost:3000/auth/signin')
    console.log('2. 访问保证金页面: http://localhost:3000/deposit')
    console.log('3. 切换到"中间人"标签页')
    console.log('4. 填写申请表单并提交')
    
    console.log('\n【管理员审核流程】')
    console.log('1. 管理员登录: <EMAIL>')
    console.log('2. 访问管理后台: http://localhost:3000/admin')
    console.log('3. 点击"中间人管理"')
    console.log('4. 查看申请列表并进行审核')
    
    console.log('\n【中间人控制台验证】')
    console.log('1. 被批准的用户登录')
    console.log('2. 访问个人资料: http://localhost:3000/profile')
    console.log('3. 查看是否显示中间人控制台卡片')
    console.log('4. 点击进入中间人控制台')

    console.log('\n💡 测试要点:')
    console.log('   - 申请需要足够的保证金余额')
    console.log('   - 管理员审核后用户状态会更新')
    console.log('   - 只有中间人用户才显示控制台')
    console.log('   - 权限控制正确工作')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testMediatorApplicationFlow().catch(console.error)
}

module.exports = { testMediatorApplicationFlow }
