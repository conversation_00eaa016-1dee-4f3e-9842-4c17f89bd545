/**
 * 数据库清理脚本
 * 提供多种数据删除选项
 */

const { PrismaClient } = require('@prisma/client')
const readline = require('readline')

const prisma = new PrismaClient()

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

async function showDatabaseStats() {
  console.log('📊 当前数据库统计:')
  console.log('='.repeat(40))
  
  try {
    const stats = {
      users: await prisma.user.count(),
      products: await prisma.product.count(),
      orders: await prisma.order.count(),
      demands: await prisma.demand.count(),
      reviews: await prisma.review.count(),
      messages: await prisma.message.count(),
      accounts: await prisma.account.count()
    }

    Object.entries(stats).forEach(([table, count]) => {
      console.log(`${table.padEnd(15)}: ${count} 条记录`)
    })
    
    return stats
  } catch (error) {
    console.error('获取统计信息失败:', error.message)
    return {}
  }
}

async function deleteAllData() {
  console.log('\n⚠️  即将删除所有数据...')
  
  const confirm = await askQuestion('确认删除所有数据? (输入 "DELETE ALL" 确认): ')
  
  if (confirm !== 'DELETE ALL') {
    console.log('❌ 操作已取消')
    return
  }

  try {
    console.log('🗑️  开始删除数据...')
    
    // 按依赖关系顺序删除
    await prisma.review.deleteMany()
    console.log('✅ 已删除所有评价')
    
    await prisma.message.deleteMany()
    console.log('✅ 已删除所有消息')
    
    await prisma.demandOffer.deleteMany()
    console.log('✅ 已删除所有需求报价')
    
    await prisma.demand.deleteMany()
    console.log('✅ 已删除所有需求单')
    
    await prisma.orderItem.deleteMany()
    console.log('✅ 已删除所有订单项')
    
    await prisma.order.deleteMany()
    console.log('✅ 已删除所有订单')
    
    await prisma.product.deleteMany()
    console.log('✅ 已删除所有商品')
    
    await prisma.account.deleteMany()
    console.log('✅ 已删除所有账户')
    
    await prisma.user.deleteMany()
    console.log('✅ 已删除所有用户')
    
    console.log('\n🎉 所有数据已删除完成!')
    
  } catch (error) {
    console.error('❌ 删除数据失败:', error.message)
  }
}

async function deleteTestData() {
  console.log('\n🧪 删除测试数据...')
  
  try {
    // 删除测试用户及相关数据
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
    
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          in: testEmails
        }
      }
    })
    
    console.log(`找到 ${testUsers.length} 个测试用户`)
    
    for (const user of testUsers) {
      // 删除用户相关的所有数据
      await prisma.review.deleteMany({
        where: {
          OR: [
            { reviewerId: user.id },
            { revieweeId: user.id }
          ]
        }
      })
      
      await prisma.message.deleteMany({
        where: {
          OR: [
            { senderId: user.id },
            { receiverId: user.id }
          ]
        }
      })
      
      await prisma.demandOffer.deleteMany({
        where: {
          demand: {
            userId: user.id
          }
        }
      })
      
      await prisma.demand.deleteMany({
        where: { userId: user.id }
      })
      
      await prisma.orderItem.deleteMany({
        where: {
          order: {
            OR: [
              { buyerId: user.id },
              { sellerId: user.id }
            ]
          }
        }
      })
      
      await prisma.order.deleteMany({
        where: {
          OR: [
            { buyerId: user.id },
            { sellerId: user.id }
          ]
        }
      })
      
      await prisma.product.deleteMany({
        where: { sellerId: user.id }
      })
      
      await prisma.account.deleteMany({
        where: { userId: user.id }
      })
      
      await prisma.user.delete({
        where: { id: user.id }
      })
      
      console.log(`✅ 已删除测试用户: ${user.email}`)
    }
    
    console.log('\n🎉 测试数据删除完成!')
    
  } catch (error) {
    console.error('❌ 删除测试数据失败:', error.message)
  }
}

async function deleteByTable() {
  console.log('\n📋 选择要删除的表:')
  console.log('1. 用户 (users)')
  console.log('2. 商品 (products)')
  console.log('3. 订单 (orders)')
  console.log('4. 需求单 (demands)')
  console.log('5. 评价 (reviews)')
  console.log('6. 消息 (messages)')
  console.log('7. 账户 (accounts)')
  
  const choice = await askQuestion('请选择 (1-7): ')
  
  try {
    switch (choice) {
      case '1':
        await prisma.user.deleteMany()
        console.log('✅ 已删除所有用户')
        break
      case '2':
        await prisma.product.deleteMany()
        console.log('✅ 已删除所有商品')
        break
      case '3':
        await prisma.orderItem.deleteMany()
        await prisma.order.deleteMany()
        console.log('✅ 已删除所有订单')
        break
      case '4':
        await prisma.demandOffer.deleteMany()
        await prisma.demand.deleteMany()
        console.log('✅ 已删除所有需求单')
        break
      case '5':
        await prisma.review.deleteMany()
        console.log('✅ 已删除所有评价')
        break
      case '6':
        await prisma.message.deleteMany()
        console.log('✅ 已删除所有消息')
        break
      case '7':
        await prisma.account.deleteMany()
        console.log('✅ 已删除所有账户')
        break
      default:
        console.log('❌ 无效选择')
    }
  } catch (error) {
    console.error('❌ 删除失败:', error.message)
  }
}

async function resetDatabase() {
  console.log('\n🔄 重置数据库 (删除所有数据并重新创建表结构)...')
  
  const confirm = await askQuestion('确认重置数据库? (输入 "RESET" 确认): ')
  
  if (confirm !== 'RESET') {
    console.log('❌ 操作已取消')
    return
  }

  try {
    // 删除所有数据
    await deleteAllData()
    
    // 重新应用数据库迁移
    console.log('\n🔧 重新应用数据库迁移...')
    const { spawn } = require('child_process')
    
    const migrate = spawn('npx', ['prisma', 'migrate', 'reset', '--force'], {
      stdio: 'inherit',
      shell: true
    })
    
    migrate.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 数据库重置完成!')
      } else {
        console.log('❌ 数据库重置失败')
      }
    })
    
  } catch (error) {
    console.error('❌ 重置数据库失败:', error.message)
  }
}

async function main() {
  console.log('🗑️  BitMarket 数据库清理工具')
  console.log('='.repeat(50))
  
  await showDatabaseStats()
  
  console.log('\n请选择操作:')
  console.log('1. 删除所有数据')
  console.log('2. 删除测试数据')
  console.log('3. 按表删除数据')
  console.log('4. 重置数据库')
  console.log('5. 查看统计信息')
  console.log('6. 退出')
  
  const choice = await askQuestion('\n请选择 (1-6): ')
  
  switch (choice) {
    case '1':
      await deleteAllData()
      break
    case '2':
      await deleteTestData()
      break
    case '3':
      await deleteByTable()
      break
    case '4':
      await resetDatabase()
      break
    case '5':
      await showDatabaseStats()
      break
    case '6':
      console.log('👋 再见!')
      break
    default:
      console.log('❌ 无效选择')
  }
  
  rl.close()
  await prisma.$disconnect()
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  deleteAllData,
  deleteTestData,
  deleteByTable,
  resetDatabase,
  showDatabaseStats
}
