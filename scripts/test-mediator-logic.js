const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testMediatorLogic() {
  try {
    console.log('=== 测试中间人分配逻辑 ===\n');
    
    // 模拟 auto-assign API 的逻辑
    const orderAmount = 1000;
    console.log(`测试订单金额: ${orderAmount} USDT\n`);
    
    // 1. 获取所有活跃的中间人
    console.log('1. 查询活跃中间人...');
    const activeMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        mediatorDeposit: true
      }
    });
    
    console.log(`找到 ${activeMediators.length} 个活跃中间人`);
    
    if (activeMediators.length === 0) {
      console.log('❌ 没有活跃的中间人');
      return;
    }
    
    // 2. 为每个中间人计算可用保证金
    console.log('\n2. 计算每个中间人的可用保证金...');
    const mediatorCandidates = [];
    
    for (const mediator of activeMediators) {
      console.log(`\n检查中间人: ${mediator.name}`);
      
      // 获取中间人的完整余额信息
      const mediatorBalance = await prisma.user.findUnique({
        where: { id: mediator.id },
        select: {
          depositBalance: true,
          frozenBalance: true,
          availableBalance: true
        }
      });

      if (!mediatorBalance) {
        console.log('  ❌ 无法获取余额信息');
        continue;
      }

      console.log(`  余额信息:`);
      console.log(`    depositBalance: ${mediatorBalance.depositBalance}`);
      console.log(`    frozenBalance: ${mediatorBalance.frozenBalance}`);
      console.log(`    availableBalance: ${mediatorBalance.availableBalance}`);

      // 计算当前锁定金额（活跃担保订单）
      const activeOrders = await prisma.order.findMany({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        },
        select: {
          escrowAmount: true
        }
      });

      const lockedAmount = activeOrders.reduce((sum, order) => sum + (order.escrowAmount || 0), 0);
      console.log(`  活跃订单锁定金额: ${lockedAmount}`);
      
      // 使用正确的可用余额计算
      const availableAmount = Math.max(0, mediatorBalance.availableBalance - lockedAmount);
      console.log(`  最终可用金额: ${availableAmount}`);

      // 检查是否足够
      const isQualified = availableAmount >= orderAmount;
      console.log(`  是否符合条件 (>= ${orderAmount}): ${isQualified ? '✅' : '❌'}`);

      if (isQualified) {
        // 计算综合评分
        const score = calculateMediatorScore(mediator, activeOrders.length, availableAmount, orderAmount);
        
        mediatorCandidates.push({
          ...mediator,
          totalDeposit: mediatorBalance.depositBalance,
          frozenBalance: mediatorBalance.frozenBalance,
          lockedAmount,
          availableAmount,
          activeOrderCount: activeOrders.length,
          score
        });
        
        console.log(`  综合评分: ${score.toFixed(2)}`);
      }
    }
    
    // 3. 检查结果
    console.log(`\n3. 分配结果:`);
    console.log(`符合条件的中间人数量: ${mediatorCandidates.length}`);
    
    if (mediatorCandidates.length === 0) {
      console.log('❌ 暂无保证金充足的中间人可用');
      console.log('   建议: 请稍后再试或联系客服');
    } else {
      // 按综合评分排序
      mediatorCandidates.sort((a, b) => b.score - a.score);
      
      const selectedMediator = mediatorCandidates[0];
      const escrowFee = orderAmount * (selectedMediator.mediatorFeeRate || 0.02);
      
      console.log('✅ 自动分配成功');
      console.log(`   选中的中间人: ${selectedMediator.name}`);
      console.log(`   可用金额: ${selectedMediator.availableAmount} USDT`);
      console.log(`   活跃订单数: ${selectedMediator.activeOrderCount}`);
      console.log(`   综合评分: ${selectedMediator.score.toFixed(2)}`);
      console.log(`   托管费用: ${escrowFee} USDT`);
      console.log(`   总费用: ${orderAmount + escrowFee} USDT`);
      
      // 显示所有候选人
      if (mediatorCandidates.length > 1) {
        console.log('\n   其他候选人:');
        mediatorCandidates.slice(1).forEach((candidate, index) => {
          console.log(`   ${index + 2}. ${candidate.name} - 评分: ${candidate.score.toFixed(2)}, 可用: ${candidate.availableAmount} USDT`);
        });
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 计算中间人综合评分（复制自 auto-assign API）
function calculateMediatorScore(mediator, activeOrderCount, availableAmount, orderAmount) {
  let score = 0;

  // 信誉度权重 (40%)
  const reputationScore = (mediator.mediatorReputation || 0) / 100 * 40;

  // 成功率权重 (30%)
  const successRateScore = (mediator.mediatorSuccessRate || 0) / 100 * 30;

  // 负载均衡权重 (20%) - 活跃订单越少分数越高
  const maxOrders = 50; // 假设最大活跃订单数为50
  const loadBalanceScore = Math.max(0, (maxOrders - activeOrderCount) / maxOrders) * 20;

  // 资金充足度权重 (10%) - 可用资金越多分数越高
  const fundsSufficiencyScore = Math.min(availableAmount / (orderAmount * 5), 1) * 10;

  score = reputationScore + successRateScore + loadBalanceScore + fundsSufficiencyScore;

  return score;
}

testMediatorLogic();
