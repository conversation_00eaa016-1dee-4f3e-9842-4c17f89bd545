/**
 * 测试profile页面修复
 * 检查代码语法和变量使用
 */

const fs = require('fs')
const path = require('path')

function testProfilePageFix() {
  console.log('🔍 测试 Profile 页面修复...')
  console.log('='.repeat(40))

  try {
    const profilePath = path.join(process.cwd(), 'app/profile/page.tsx')
    const content = fs.readFileSync(profilePath, 'utf8')

    // 检查是否还有错误的变量名
    const issues = []

    // 1. 检查setLoading使用
    if (content.includes('setLoading(')) {
      const matches = content.match(/setLoading\(/g)
      issues.push(`发现 ${matches.length} 处使用了 setLoading，应该使用 setIsLoading`)
    }

    // 2. 检查loading变量使用（除了NextAuth的status loading）
    const loadingMatches = content.match(/(?<!status === ')loading(?!\s*\|\|)/g)
    if (loadingMatches && loadingMatches.length > 0) {
      // 过滤掉合法的使用
      const lines = content.split('\n')
      lines.forEach((line, index) => {
        if (line.includes('loading') && 
            !line.includes('status === \'loading\'') && 
            !line.includes('isLoading') &&
            !line.includes('setIsLoading') &&
            !line.includes('加载中') &&
            !line.includes('Loading')) {
          issues.push(`第 ${index + 1} 行可能有问题: ${line.trim()}`)
        }
      })
    }

    // 3. 检查state定义
    const stateDefMatch = content.match(/const \[(\w+), set(\w+)\] = useState<boolean>\(false\)/)
    if (stateDefMatch) {
      const stateName = stateDefMatch[1]
      const setterName = stateDefMatch[2]
      console.log(`✅ 找到状态定义: ${stateName}, ${setterName}`)
      
      if (stateName !== 'isLoading' || setterName !== 'IsLoading') {
        issues.push(`状态变量名不一致: 期望 isLoading/setIsLoading，实际 ${stateName}/set${setterName}`)
      }
    }

    // 4. 检查函数中的使用 - 改进的匹配
    const lines = content.split('\n')
    let inFetchProfile = false
    let inHandleSubmit = false
    let fetchProfileHasStart = false
    let fetchProfileHasEnd = false
    let handleSubmitHasStart = false
    let handleSubmitHasEnd = false
    let braceCount = 0

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      if (line.includes('const fetchProfile = async () => {')) {
        inFetchProfile = true
        braceCount = 1
        continue
      }

      if (line.includes('const handleSubmit = async (e: React.FormEvent) => {')) {
        inHandleSubmit = true
        braceCount = 1
        continue
      }

      if (inFetchProfile || inHandleSubmit) {
        // 计算大括号
        braceCount += (line.match(/\{/g) || []).length
        braceCount -= (line.match(/\}/g) || []).length

        if (line.includes('setIsLoading(true)')) {
          if (inFetchProfile) fetchProfileHasStart = true
          if (inHandleSubmit) handleSubmitHasStart = true
        }

        if (line.includes('setIsLoading(false)')) {
          if (inFetchProfile) fetchProfileHasEnd = true
          if (inHandleSubmit) handleSubmitHasEnd = true
        }

        // 函数结束
        if (braceCount === 0) {
          inFetchProfile = false
          inHandleSubmit = false
        }
      }
    }

    if (!fetchProfileHasStart) {
      issues.push('fetchProfile 函数开始时未设置 loading 状态')
    }
    if (!fetchProfileHasEnd) {
      issues.push('fetchProfile 函数结束时未重置 loading 状态')
    }
    if (!handleSubmitHasStart) {
      issues.push('handleSubmit 函数开始时未设置 loading 状态')
    }
    if (!handleSubmitHasEnd) {
      issues.push('handleSubmit 函数结束时未重置 loading 状态')
    }

    // 显示结果
    if (issues.length === 0) {
      console.log('✅ Profile 页面检查通过!')
      console.log('   所有 loading 状态变量使用正确')
      console.log('   函数中正确设置和重置了 loading 状态')
      
      // 显示修复总结
      console.log('\n📋 修复总结:')
      console.log('   ✅ 修复了 setLoading -> setIsLoading')
      console.log('   ✅ 在 fetchProfile 开始时添加了 setIsLoading(true)')
      console.log('   ✅ 在 fetchProfile 结束时使用 setIsLoading(false)')
      console.log('   ✅ handleSubmit 函数中正确使用 setIsLoading')
      
    } else {
      console.log('❌ 发现问题:')
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`)
      })
    }

    // 显示状态变量使用统计
    console.log('\n📊 状态变量使用统计:')
    const isLoadingCount = (content.match(/isLoading/g) || []).length
    const setIsLoadingCount = (content.match(/setIsLoading/g) || []).length
    console.log(`   isLoading: ${isLoadingCount} 次`)
    console.log(`   setIsLoading: ${setIsLoadingCount} 次`)

    return issues.length === 0

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    return false
  }
}

// 检查其他可能的loading相关问题
function checkOtherLoadingIssues() {
  console.log('\n🔍 检查其他可能的 loading 问题...')
  
  const filesToCheck = [
    'app/auth/signin/page.tsx',
    'app/auth/register/page.tsx',
    'components/ui/loading.tsx'
  ]

  filesToCheck.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath)
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8')
      const loadingIssues = []
      
      // 检查常见的loading问题
      if (content.includes('setLoading(') && !content.includes('setIsLoading(')) {
        loadingIssues.push('使用了 setLoading 而不是 setIsLoading')
      }
      
      if (loadingIssues.length > 0) {
        console.log(`⚠️  ${filePath}:`)
        loadingIssues.forEach(issue => {
          console.log(`   - ${issue}`)
        })
      } else {
        console.log(`✅ ${filePath}: 无问题`)
      }
    } else {
      console.log(`ℹ️  ${filePath}: 文件不存在`)
    }
  })
}

// 主函数
function main() {
  console.log('🔧 Profile 页面修复验证工具')
  console.log('='.repeat(50))
  
  const profileOk = testProfilePageFix()
  checkOtherLoadingIssues()
  
  console.log('\n🎯 总结:')
  if (profileOk) {
    console.log('✅ Profile 页面修复成功!')
    console.log('   现在可以正常访问用户资料页面')
    console.log('   loading 状态管理已修复')
  } else {
    console.log('❌ Profile 页面仍有问题')
    console.log('   请检查上述问题并修复')
  }
  
  console.log('\n🔗 测试建议:')
  console.log('1. 启动应用: npm run dev')
  console.log('2. 登录账户: <EMAIL> / 123456')
  console.log('3. 访问资料页面: http://localhost:3000/profile')
  console.log('4. 检查页面是否正常加载')
}

if (require.main === module) {
  main()
}

module.exports = {
  testProfilePageFix,
  checkOtherLoadingIssues
}
