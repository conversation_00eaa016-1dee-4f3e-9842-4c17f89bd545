const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function listOrders() {
  try {
    console.log('📋 查看现有订单...')
    
    const orders = await prisma.order.findMany({
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        product: {
          select: {
            id: true,
            title: true,
            price: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (orders.length === 0) {
      console.log('❌ 没有找到任何订单')
      return
    }

    console.log(`✅ 找到 ${orders.length} 个订单:`)
    console.log('')

    orders.forEach((order, index) => {
      console.log(`${index + 1}. 订单ID: ${order.id}`)
      console.log(`   订单号: ${order.orderNumber}`)
      console.log(`   商品: ${order.product.title}`)
      console.log(`   价格: ${order.totalAmount} USDT`)
      console.log(`   状态: ${order.status}`)
      console.log(`   买家: ${order.buyer.name || order.buyer.email}`)
      console.log(`   卖家: ${order.seller.name || order.seller.email}`)
      console.log(`   创建时间: ${order.createdAt}`)
      console.log('')
    })

  } catch (error) {
    console.error('❌ 查询订单失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

listOrders()
