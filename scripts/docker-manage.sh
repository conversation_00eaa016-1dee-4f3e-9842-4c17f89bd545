#!/bin/bash

# ============================================================================
# BitMarket Docker 数据库管理脚本
# Docker Database Management Script for BitMarket
# ============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    echo "BitMarket Docker 数据库管理工具"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start     启动数据库服务"
    echo "  stop      停止数据库服务"
    echo "  restart   重启数据库服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  backup    备份数据库"
    echo "  restore   恢复数据库"
    echo "  reset     重置数据库 (删除所有数据)"
    echo "  shell     进入数据库 shell"
    echo "  migrate   运行 Prisma 迁移"
    echo "  clean     清理未使用的 Docker 资源"
    echo ""
    echo "选项:"
    echo "  --redis   包含 Redis 服务"
    echo "  --help    显示帮助信息"
}

# 启动服务
start_services() {
    local include_redis=$1
    
    log_info "启动 BitMarket 数据库服务..."
    
    if [ "$include_redis" = true ]; then
        docker-compose --env-file .env.docker --profile redis up -d
        log_success "MySQL 和 Redis 服务已启动"
    else
        docker-compose --env-file .env.docker up -d mysql
        log_success "MySQL 服务已启动"
    fi
    
    # 等待服务就绪
    log_info "等待服务就绪..."
    sleep 5
    
    # 检查服务状态
    docker-compose ps
}

# 停止服务
stop_services() {
    log_info "停止 BitMarket 数据库服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    local include_redis=$1
    log_info "重启 BitMarket 数据库服务..."
    stop_services
    start_services $include_redis
}

# 查看服务状态
show_status() {
    log_info "BitMarket 数据库服务状态："
    docker-compose ps
    
    echo ""
    log_info "容器资源使用情况："
    docker stats --no-stream bitmarket-mysql 2>/dev/null || log_warning "MySQL 容器未运行"
    docker stats --no-stream bitmarket-redis 2>/dev/null || log_warning "Redis 容器未运行"
}

# 查看日志
show_logs() {
    local service=${1:-mysql}
    log_info "查看 $service 服务日志..."
    docker-compose logs -f $service
}

# 备份数据库
backup_database() {
    local backup_file="backup/bitmarket_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "备份数据库到 $backup_file..."
    
    # 创建备份目录
    mkdir -p backup
    
    # 执行备份
    docker-compose exec -T mysql mysqldump \
        -u bitmarket_user \
        -pbitmarket_pass_2024 \
        --single-transaction \
        --routines \
        --triggers \
        bitmarket > $backup_file
    
    log_success "数据库备份完成: $backup_file"
}

# 恢复数据库
restore_database() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件路径"
        echo "用法: $0 restore <backup_file>"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_warning "这将覆盖现有数据库，确认继续吗？(y/N)"
    read -r confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "操作已取消"
        exit 0
    fi
    
    log_info "从 $backup_file 恢复数据库..."
    
    # 恢复数据库
    docker-compose exec -T mysql mysql \
        -u bitmarket_user \
        -pbitmarket_pass_2024 \
        bitmarket < $backup_file
    
    log_success "数据库恢复完成"
}

# 重置数据库
reset_database() {
    log_warning "这将删除所有数据库数据，确认继续吗？(y/N)"
    read -r confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "操作已取消"
        exit 0
    fi
    
    log_info "重置数据库..."
    
    # 停止服务
    docker-compose down
    
    # 删除数据卷
    docker volume rm bitmarket_mysql_data 2>/dev/null || true
    
    # 重新启动服务
    docker-compose --env-file .env.docker up -d mysql
    
    # 等待服务就绪
    sleep 10
    
    # 运行 Prisma 迁移
    export DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"
    npx prisma migrate dev --name reset-init
    
    log_success "数据库重置完成"
}

# 进入数据库 shell
enter_shell() {
    log_info "进入 MySQL shell..."
    docker-compose exec mysql mysql -u bitmarket_user -pbitmarket_pass_2024 bitmarket
}

# 运行 Prisma 迁移
run_migration() {
    log_info "运行 Prisma 迁移..."
    
    export DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"
    
    npx prisma generate
    npx prisma migrate dev
    
    log_success "Prisma 迁移完成"
}

# 清理 Docker 资源
clean_docker() {
    log_info "清理未使用的 Docker 资源..."
    
    docker system prune -f
    docker volume prune -f
    
    log_success "Docker 资源清理完成"
}

# 主函数
main() {
    local command=$1
    local include_redis=false
    
    # 解析选项
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --redis)
                include_redis=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
    
    # 执行命令
    case $command in
        start)
            start_services $include_redis
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services $include_redis
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs $1
            ;;
        backup)
            backup_database
            ;;
        restore)
            restore_database $1
            ;;
        reset)
            reset_database
            ;;
        shell)
            enter_shell
            ;;
        migrate)
            run_migration
            ;;
        clean)
            clean_docker
            ;;
        help|--help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查是否提供了命令
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 运行主函数
main "$@"
