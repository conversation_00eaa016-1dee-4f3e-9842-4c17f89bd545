const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args))

const BASE_URL = 'http://localhost:3000'
let authTokens = {}

// 测试结果统计
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
}

// 辅助函数：发送HTTP请求
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  }
  
  const response = await fetch(url, { ...defaultOptions, ...options })
  const data = await response.json()
  
  return {
    status: response.status,
    data,
    ok: response.ok
  }
}

// 测试函数
async function testAPI(name, testFn) {
  try {
    console.log(`🧪 测试: ${name}`)
    await testFn()
    console.log(`✅ ${name} - 通过`)
    testResults.passed++
  } catch (error) {
    console.log(`❌ ${name} - 失败: ${error.message}`)
    testResults.failed++
    testResults.errors.push({ test: name, error: error.message })
  }
}

// 1. 认证API测试
async function testAuth() {
  await testAPI('NextAuth登录API', async () => {
    // NextAuth使用不同的登录方式，这里测试credentials provider
    const response = await makeRequest('/api/auth/callback/credentials', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: '<EMAIL>',
        password: '123456',
        csrfToken: 'test-token',
        callbackUrl: 'http://localhost:3000',
        json: 'true'
      }).toString()
    })

    // NextAuth可能返回重定向或其他响应，这里只检查不是错误页面
    if (response.data && typeof response.data === 'string' && response.data.includes('<!DOCTYPE')) {
      // 如果返回HTML页面，可能是重定向到登录页面，这是正常的
      console.log('  📝 NextAuth登录API响应正常（可能需要CSRF token）')
    } else {
      console.log('  📝 NextAuth登录API响应格式正确')
    }
  })
  
  await testAPI('获取用户信息API', async () => {
    const response = await makeRequest('/api/user/profile')
    
    if (!response.ok) {
      throw new Error(`获取用户信息失败: ${response.data.message || '未知错误'}`)
    }
    
    if (!response.data.id || !response.data.email) {
      throw new Error('用户信息响应格式错误')
    }

    console.log(`  📝 获取用户信息成功: ${response.data.email}`)
  })
}

// 2. 商品API测试
async function testProducts() {
  await testAPI('获取商品列表API', async () => {
    const response = await makeRequest('/api/products')
    
    if (!response.ok) {
      throw new Error(`获取商品列表失败: ${response.data.message || '未知错误'}`)
    }
    
    if (!Array.isArray(response.data.products)) {
      throw new Error('商品列表响应格式错误')
    }
    
    if (response.data.products.length === 0) {
      throw new Error('商品列表为空')
    }
    
    console.log(`  📝 获取到 ${response.data.products.length} 个商品`)
  })
  
  await testAPI('获取商品详情API', async () => {
    // 先获取商品列表
    const listResponse = await makeRequest('/api/products')
    const firstProduct = listResponse.data.products[0]
    
    const response = await makeRequest(`/api/products/${firstProduct.id}`)
    
    if (!response.ok) {
      throw new Error(`获取商品详情失败: ${response.data.message || '未知错误'}`)
    }
    
    if (!response.data.product || !response.data.product.id) {
      throw new Error('商品详情响应格式错误')
    }
    
    console.log(`  📝 获取商品详情成功: ${response.data.product.title}`)
  })
}

// 3. 收藏API测试
async function testFavorites() {
  let productId = null

  await testAPI('添加商品到收藏API', async () => {
    // 先获取一个商品ID
    const productsResponse = await makeRequest('/api/products')
    productId = productsResponse.data.products[0].id

    const response = await makeRequest('/api/user/favorites', {
      method: 'POST',
      body: JSON.stringify({
        productId: productId
      })
    })

    if (!response.ok) {
      throw new Error(`添加到收藏失败: ${response.data.message || '未知错误'}`)
    }

    console.log('  📝 商品添加到收藏成功')
  })

  await testAPI('获取收藏列表API', async () => {
    const response = await makeRequest('/api/user/favorites')

    if (!response.ok) {
      throw new Error(`获取收藏列表失败: ${response.data.message || '未知错误'}`)
    }

    if (!Array.isArray(response.data.favorites)) {
      throw new Error('收藏列表响应格式错误')
    }

    console.log(`  📝 收藏列表中有 ${response.data.favorites.length} 个商品`)
  })
}

// 4. 管理员API测试
async function testAdminAPIs() {
  await testAPI('管理员权限检查API', async () => {
    const response = await makeRequest('/api/admin/check')

    if (!response.ok) {
      throw new Error(`管理员权限检查失败: ${response.data.message || '未知错误'}`)
    }

    console.log(`  📝 管理员权限检查: ${response.data.isAdmin ? '通过' : '失败'}`)
  })

  await testAPI('管理员导航API', async () => {
    const response = await makeRequest('/api/admin/navigation')

    if (!response.ok) {
      throw new Error(`获取管理员导航失败: ${response.data.message || '未知错误'}`)
    }

    if (!Array.isArray(response.data.navigation)) {
      throw new Error('导航响应格式错误')
    }

    console.log(`  📝 获取到 ${response.data.navigation.length} 个导航项`)
  })
}

// 主测试函数
async function runAPITests() {
  console.log('🚀 开始API功能测试...\n')
  
  try {
    console.log('📋 1. 认证API测试')
    await testAuth()
    
    console.log('\n📋 2. 商品API测试')
    await testProducts()
    
    console.log('\n📋 3. 收藏API测试')
    await testFavorites()
    
    console.log('\n📋 4. 管理员API测试')
    await testAdminAPIs()
    
  } catch (error) {
    console.error('❌ API测试过程中发生错误:', error)
  }
  
  console.log('\n📊 API测试结果统计:')
  console.log(`✅ 通过: ${testResults.passed}`)
  console.log(`❌ 失败: ${testResults.failed}`)
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试详情:')
    testResults.errors.forEach(error => {
      console.log(`  - ${error.test}: ${error.error}`)
    })
  }
  
  console.log('\n🎉 API测试完成!')
  return testResults
}

// 运行测试
runAPITests()
