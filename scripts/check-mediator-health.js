const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkMediatorHealth() {
  try {
    console.log('=== 中间人系统健康检查 ===\n');
    
    const issues = [];
    const warnings = [];
    
    // 1. 检查中间人数量
    const totalMediators = await prisma.user.count({
      where: { isMediator: true }
    });
    
    const activeMediators = await prisma.user.count({
      where: { isMediator: true, mediatorStatus: 'ACTIVE' }
    });
    
    console.log(`中间人总数: ${totalMediators}`);
    console.log(`活跃中间人数: ${activeMediators}`);
    
    if (activeMediators === 0) {
      issues.push('没有活跃的中间人');
    } else if (activeMediators < 3) {
      warnings.push(`活跃中间人数量较少 (${activeMediators})，建议增加更多中间人`);
    }
    
    // 2. 检查数据一致性
    console.log('\n检查数据一致性...');
    const mediators = await prisma.user.findMany({
      where: { isMediator: true },
      select: {
        id: true,
        name: true,
        depositBalance: true,
        frozenBalance: true,
        availableBalance: true
      }
    });
    
    let inconsistentCount = 0;
    mediators.forEach(m => {
      const expectedAvailable = Math.max(0, m.depositBalance - m.frozenBalance);
      const isConsistent = Math.abs(m.availableBalance - expectedAvailable) < 0.01;
      
      if (!isConsistent) {
        issues.push(`${m.name}: availableBalance 不一致 (${m.availableBalance} vs 期望 ${expectedAvailable})`);
        inconsistentCount++;
      }
    });
    
    if (inconsistentCount === 0) {
      console.log('✅ 所有中间人余额数据一致');
    } else {
      console.log(`❌ 发现 ${inconsistentCount} 个数据一致性问题`);
    }
    
    // 3. 检查保证金充足性
    console.log('\n检查保证金充足性...');
    const testAmounts = [100, 500, 1000, 5000];
    
    for (const amount of testAmounts) {
      const qualifiedCount = await countQualifiedMediators(amount);
      console.log(`能处理 ${amount} USDT 订单的中间人: ${qualifiedCount}`);
      
      if (amount <= 1000 && qualifiedCount === 0) {
        issues.push(`没有中间人能处理 ${amount} USDT 的订单`);
      } else if (amount <= 1000 && qualifiedCount < 2) {
        warnings.push(`能处理 ${amount} USDT 订单的中间人较少 (${qualifiedCount})`);
      }
    }
    
    // 4. 检查中间人负载
    console.log('\n检查中间人负载...');
    const activeMediator = await prisma.user.findMany({
      where: { isMediator: true, mediatorStatus: 'ACTIVE' },
      select: { id: true, name: true }
    });
    
    for (const mediator of activeMediator) {
      const activeOrderCount = await prisma.order.count({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        }
      });
      
      console.log(`${mediator.name}: ${activeOrderCount} 个活跃订单`);
      
      if (activeOrderCount > 20) {
        warnings.push(`${mediator.name} 负载较高 (${activeOrderCount} 个活跃订单)`);
      }
    }
    
    // 5. 生成报告
    console.log('\n=== 健康检查报告 ===');
    
    if (issues.length === 0 && warnings.length === 0) {
      console.log('✅ 中间人系统运行正常');
    } else {
      if (issues.length > 0) {
        console.log('\n❌ 发现以下问题需要立即解决:');
        issues.forEach((issue, index) => {
          console.log(`${index + 1}. ${issue}`);
        });
      }
      
      if (warnings.length > 0) {
        console.log('\n⚠️  发现以下警告:');
        warnings.forEach((warning, index) => {
          console.log(`${index + 1}. ${warning}`);
        });
      }
    }
    
    // 6. 建议
    console.log('\n=== 建议 ===');
    
    if (activeMediators < 3) {
      console.log('- 考虑招募更多中间人以提高系统可用性');
    }
    
    if (inconsistentCount > 0) {
      console.log('- 运行 scripts/fix-mediator-balance.js 修复数据一致性问题');
    }
    
    const lowBalanceMediators = mediators.filter(m => m.availableBalance < 1000).length;
    if (lowBalanceMediators > 0) {
      console.log(`- ${lowBalanceMediators} 个中间人的可用余额较低，建议提醒充值`);
    }
    
    console.log('- 定期运行此健康检查脚本以监控系统状态');
    
  } catch (error) {
    console.error('健康检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 计算能处理指定金额订单的中间人数量
async function countQualifiedMediators(orderAmount) {
  const activeMediators = await prisma.user.findMany({
    where: { isMediator: true, mediatorStatus: 'ACTIVE' },
    select: { id: true, availableBalance: true }
  });
  
  let qualifiedCount = 0;
  
  for (const mediator of activeMediators) {
    const activeOrdersSum = await prisma.order.aggregate({
      where: {
        mediatorId: mediator.id,
        status: {
          in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
        }
      },
      _sum: { escrowAmount: true }
    });
    
    const lockedAmount = activeOrdersSum._sum.escrowAmount || 0;
    const availableAmount = Math.max(0, mediator.availableBalance - lockedAmount);
    
    if (availableAmount >= orderAmount) {
      qualifiedCount++;
    }
  }
  
  return qualifiedCount;
}

// 如果直接运行此脚本
if (require.main === module) {
  checkMediatorHealth();
}

module.exports = { checkMediatorHealth };
