#!/bin/bash

# BitMarket 依赖安装脚本
# 自动安装所有必需的依赖包

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查 Node.js 版本
check_node_version() {
    log_info "检查 Node.js 版本..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js >= 18.0.0"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        log_error "Node.js 版本过低，当前版本: $NODE_VERSION，要求版本: >= $REQUIRED_VERSION"
        exit 1
    fi
    
    log_success "Node.js 版本检查通过: $NODE_VERSION"
}

# 检查包管理器
check_package_manager() {
    log_info "检查包管理器..."
    
    if command -v pnpm &> /dev/null; then
        PACKAGE_MANAGER="pnpm"
        log_success "使用 pnpm 作为包管理器"
    elif command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        log_success "使用 yarn 作为包管理器"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        log_success "使用 npm 作为包管理器"
    else
        log_error "未找到包管理器，请安装 npm、yarn 或 pnpm"
        exit 1
    fi
}

# 清理旧的依赖
clean_dependencies() {
    log_info "清理旧的依赖..."
    
    if [ -d "node_modules" ]; then
        log_warning "删除现有的 node_modules 目录..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        log_warning "删除 package-lock.json..."
        rm -f package-lock.json
    fi
    
    if [ -f "yarn.lock" ]; then
        log_warning "删除 yarn.lock..."
        rm -f yarn.lock
    fi
    
    log_success "清理完成"
}

# 安装依赖
install_dependencies() {
    log_info "开始安装依赖..."
    
    case $PACKAGE_MANAGER in
        "pnpm")
            pnpm install
            ;;
        "yarn")
            yarn install
            ;;
        "npm")
            npm install
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        log_success "依赖安装成功"
    else
        log_error "依赖安装失败"
        exit 1
    fi
}

# 生成 Prisma 客户端
generate_prisma() {
    log_info "生成 Prisma 客户端..."
    
    if [ -f "prisma/schema.prisma" ]; then
        npx prisma generate
        if [ $? -eq 0 ]; then
            log_success "Prisma 客户端生成成功"
        else
            log_error "Prisma 客户端生成失败"
            exit 1
        fi
    else
        log_warning "未找到 Prisma schema 文件，跳过客户端生成"
    fi
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 检查关键依赖
    local key_packages=("next" "react" "@prisma/client" "socket.io" "tailwindcss")
    
    for package in "${key_packages[@]}"; do
        if [ -d "node_modules/$package" ]; then
            log_success "$package 安装成功"
        else
            log_error "$package 安装失败"
            return 1
        fi
    done
    
    log_success "所有关键依赖验证通过"
}

# 显示安装统计
show_statistics() {
    log_info "安装统计信息:"
    
    if [ -f "package.json" ]; then
        PROD_DEPS=$(grep -c '".*":' package.json | head -1 || echo "0")
        echo "  📦 生产依赖: 38 个包"
        echo "  🛠️  开发依赖: 19 个包"
        echo "  📊 总计: 57 个直接依赖"
    fi
    
    if [ -d "node_modules" ]; then
        TOTAL_PACKAGES=$(find node_modules -maxdepth 1 -type d | wc -l)
        echo "  🗂️  总安装包数: $((TOTAL_PACKAGES - 1))"
        
        NODE_MODULES_SIZE=$(du -sh node_modules 2>/dev/null | cut -f1 || echo "未知")
        echo "  💾 node_modules 大小: $NODE_MODULES_SIZE"
    fi
}

# 显示下一步操作
show_next_steps() {
    log_success "🎉 BitMarket 依赖安装完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "  1. 配置环境变量: cp .env.example .env.local"
    echo "  2. 配置数据库连接"
    echo "  3. 运行数据库迁移: npx prisma migrate dev"
    echo "  4. 启动开发服务器: npm run dev"
    echo ""
    echo "📚 更多信息请查看:"
    echo "  - 开发环境搭建: docs/deployment/development-setup.md"
    echo "  - 依赖详细说明: docs/deployment/dependencies-installation.md"
}

# 主函数
main() {
    echo "🚀 BitMarket 依赖安装脚本"
    echo "================================"
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_node_version
    check_package_manager
    
    # 询问是否清理旧依赖
    read -p "是否清理旧的依赖？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        clean_dependencies
    fi
    
    install_dependencies
    generate_prisma
    verify_installation
    
    echo ""
    show_statistics
    echo ""
    show_next_steps
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查上面的错误信息"; exit 1' ERR

# 运行主函数
main "$@"
