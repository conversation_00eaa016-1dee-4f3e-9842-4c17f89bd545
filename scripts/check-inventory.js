const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkInventory() {
  try {
    console.log('开始检查商品库存状态...')
    
    // 查找所有库存不足但仍在售的商品
    const outOfStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lte: 0
        },
        status: 'AVAILABLE',
        isDemandGenerated: false
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (outOfStockProducts.length === 0) {
      console.log('没有发现库存不足的商品')
      return
    }

    console.log(`发现 ${outOfStockProducts.length} 个库存不足的商品，开始自动下架...`)

    // 批量更新商品状态为SOLD_OUT
    const productIds = outOfStockProducts.map(p => p.id)
    
    const updateResult = await prisma.product.updateMany({
      where: {
        id: {
          in: productIds
        }
      },
      data: {
        status: 'SOLD_OUT'
      }
    })

    console.log(`成功下架了 ${updateResult.count} 个商品`)

    // 输出下架的商品信息
    outOfStockProducts.forEach(product => {
      console.log(`- 商品: ${product.title} (ID: ${product.id})`)
      console.log(`  卖家: ${product.seller.name || product.seller.email}`)
      console.log(`  库存: ${product.stock}`)
      console.log(`  状态: ${product.status} -> SOLD_OUT`)
      console.log('')
    })

    // 查找低库存商品（库存 <= 5）
    const lowStockProducts = await prisma.product.findMany({
      where: {
        stock: {
          lte: 5,
          gt: 0
        },
        status: 'AVAILABLE',
        isDemandGenerated: false
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        stock: 'asc'
      }
    })

    if (lowStockProducts.length > 0) {
      console.log(`发现 ${lowStockProducts.length} 个低库存商品:`)
      lowStockProducts.forEach(product => {
        console.log(`- ${product.title}: 库存 ${product.stock} 件 (卖家: ${product.seller.name || product.seller.email})`)
      })
    }

    // 生成库存报告
    const stats = await prisma.product.groupBy({
      by: ['status'],
      _count: {
        id: true
      },
      where: {
        isDemandGenerated: false
      }
    })

    console.log('\n库存统计报告:')
    stats.forEach(stat => {
      console.log(`${stat.status}: ${stat._count.id} 个商品`)
    })

    console.log('\n库存检查完成!')

  } catch (error) {
    console.error('库存检查失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkInventory()
}

module.exports = { checkInventory }
