#!/usr/bin/env node

/**
 * 下拉列表修复验证脚本
 * 检查修复文件是否正确创建和配置
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试文件是否存在
function testFileExists(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    log(`✅ ${description}: 文件存在`, 'green');
    return true;
  } else {
    log(`❌ ${description}: 文件不存在 (${filePath})`, 'red');
    return false;
  }
}

// 测试文件内容
function testFileContent(filePath, description, testFn) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    const result = testFn(content);
    
    if (result.success) {
      log(`✅ ${description}: ${result.message}`, 'green');
      return true;
    } else {
      log(`❌ ${description}: ${result.message}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ ${description}: 读取文件失败 - ${error.message}`, 'red');
    return false;
  }
}

// 测试修复文件
function testDropdownFix() {
  log('\n🔧 测试下拉列表修复...', 'cyan');
  
  let passed = 0;
  let total = 0;
  
  // 测试修复版LocationSelector
  total++;
  if (testFileExists('components/location/FixedLocationSelector.tsx', '修复版地理位置选择器')) {
    passed++;
  }
  
  // 测试CSS修复文件
  total++;
  if (testFileExists('styles/dropdown-fix.css', '下拉列表修复样式')) {
    passed++;
  }
  
  // 测试FixedLocationSelector内容
  total++;
  if (testFileContent('components/location/FixedLocationSelector.tsx', '修复版选择器功能', (content) => {
    const hasZIndex = content.includes('zIndex: 9999');
    const hasDropUp = content.includes('dropUp');
    const hasSmartPosition = content.includes('getDropdownPosition');
    const hasBoxShadow = content.includes('boxShadow');
    
    if (hasZIndex && hasDropUp && hasSmartPosition && hasBoxShadow) {
      return { success: true, message: '包含所有修复功能' };
    } else {
      return { success: false, message: '修复功能不完整' };
    }
  })) {
    passed++;
  }
  
  // 测试CSS样式内容
  total++;
  if (testFileContent('styles/dropdown-fix.css', 'CSS修复样式', (content) => {
    const hasZIndex = content.includes('z-index: 9999');
    const hasDropUp = content.includes('drop-up');
    const hasDropDown = content.includes('drop-down');
    const hasOverlay = content.includes('dropdown-overlay');
    
    if (hasZIndex && hasDropUp && hasDropDown && hasOverlay) {
      return { success: true, message: '包含所有必要的CSS修复' };
    } else {
      return { success: false, message: 'CSS修复不完整' };
    }
  })) {
    passed++;
  }
  
  // 测试全局CSS导入
  total++;
  if (testFileContent('app/globals.css', '全局CSS导入', (content) => {
    const hasImport = content.includes('dropdown-fix.css');
    
    if (hasImport) {
      return { success: true, message: '已正确导入修复样式' };
    } else {
      return { success: false, message: '未导入修复样式' };
    }
  })) {
    passed++;
  }
  
  // 测试商品创建页面更新
  total++;
  if (testFileContent('app/products/create/page.tsx', '商品创建页面更新', (content) => {
    const hasFixedImport = content.includes('FixedLocationSelector');
    const hasFixedUsage = content.includes('<FixedLocationSelector');
    
    if (hasFixedImport && hasFixedUsage) {
      return { success: true, message: '已使用修复版选择器' };
    } else {
      return { success: false, message: '未使用修复版选择器' };
    }
  })) {
    passed++;
  }
  
  log(`📊 下拉列表修复测试: ${passed}/${total} 通过`, passed === total ? 'green' : 'yellow');
  return { passed, total };
}

// 主函数
async function main() {
  try {
    log('🧪 开始下拉列表修复验证...', 'cyan');
    log('', 'reset');

    const result = testDropdownFix();
    
    const passRate = ((result.passed / result.total) * 100).toFixed(1);

    log('\n📊 修复验证结果:', 'cyan');
    log('=' * 50, 'cyan');
    log(`✅ 通过测试: ${result.passed}/${result.total} (${passRate}%)`, result.passed === result.total ? 'green' : 'yellow');
    
    if (result.passed === result.total) {
      log('\n🎉 下拉列表修复验证通过！', 'green');
      log('', 'reset');
      log('🔧 修复内容:', 'cyan');
      log('  ✅ 创建了修复版地理位置选择器', 'reset');
      log('  ✅ 添加了智能定位逻辑（向上/向下展开）', 'reset');
      log('  ✅ 提高了z-index层级（9999）', 'reset');
      log('  ✅ 增强了阴影效果', 'reset');
      log('  ✅ 添加了CSS修复样式', 'reset');
      log('  ✅ 更新了商品创建页面', 'reset');
      log('', 'reset');
      log('🌐 现在可以测试修复效果:', 'green');
      log('  访问: http://localhost:3000/products/create', 'reset');
      log('  测试城市和区县选择器的下拉显示', 'reset');
    } else {
      log('\n⚠️  部分修复验证未通过', 'yellow');
      log('', 'reset');
      log('🔧 建议操作:', 'cyan');
      log('  1. 检查未通过的测试项目', 'reset');
      log('  2. 确认文件路径和内容正确', 'reset');
      log('  3. 重新运行修复脚本', 'reset');
    }

    log('\n💡 修复说明:', 'cyan');
    log('  - 下拉列表现在会智能检测可用空间', 'reset');
    log('  - 空间不足时会向上展开', 'reset');
    log('  - 使用了更高的z-index确保显示在最前面', 'reset');
    log('  - 添加了更好的阴影效果', 'reset');
    log('  - 优化了滚动条样式', 'reset');

  } catch (error) {
    log('❌ 验证执行失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { main };
