const sharp = require('sharp')
const fs = require('fs')
const path = require('path')

async function createTestAvatar() {
  try {
    console.log('🎨 创建测试头像图片...')
    
    // 创建一个简单的测试头像 (200x200 像素)
    const testAvatarBuffer = await sharp({
      create: {
        width: 200,
        height: 200,
        channels: 3,
        background: { r: 100, g: 150, b: 200 }
      }
    })
    .png()
    .toBuffer()

    // 保存到头像目录
    const avatarDir = path.join(process.cwd(), 'public', 'uploads', 'avatar')
    const testAvatarPath = path.join(avatarDir, 'test-avatar-200x200.png')
    
    // 确保目录存在
    if (!fs.existsSync(avatarDir)) {
      fs.mkdirSync(avatarDir, { recursive: true })
    }
    
    fs.writeFileSync(testAvatarPath, testAvatarBuffer)
    
    console.log('✅ 测试头像创建成功')
    console.log(`   文件路径: ${testAvatarPath}`)
    console.log(`   文件大小: ${(testAvatarBuffer.length / 1024).toFixed(2)} KB`)
    console.log(`   访问URL: http://localhost:3000/uploads/avatar/test-avatar-200x200.png`)

    // 创建一个大尺寸的测试图片 (用于测试尺寸限制)
    const largeSizeBuffer = await sharp({
      create: {
        width: 600,
        height: 600,
        channels: 3,
        background: { r: 200, g: 100, b: 150 }
      }
    })
    .png()
    .toBuffer()

    const largeTestPath = path.join(avatarDir, 'test-avatar-600x600.png')
    fs.writeFileSync(largeTestPath, largeSizeBuffer)
    
    console.log('✅ 大尺寸测试头像创建成功')
    console.log(`   文件路径: ${largeTestPath}`)
    console.log(`   文件大小: ${(largeSizeBuffer.length / 1024).toFixed(2)} KB`)
    console.log(`   尺寸: 600x600 (用于测试尺寸限制)`)

    // 创建一个小尺寸的测试图片 (用于测试最小尺寸限制)
    const smallSizeBuffer = await sharp({
      create: {
        width: 150,
        height: 150,
        channels: 3,
        background: { r: 150, g: 200, b: 100 }
      }
    })
    .png()
    .toBuffer()

    const smallTestPath = path.join(avatarDir, 'test-avatar-150x150.png')
    fs.writeFileSync(smallTestPath, smallSizeBuffer)
    
    console.log('✅ 小尺寸测试头像创建成功')
    console.log(`   文件路径: ${smallTestPath}`)
    console.log(`   文件大小: ${(smallSizeBuffer.length / 1024).toFixed(2)} KB`)
    console.log(`   尺寸: 150x150 (用于测试最小尺寸限制)`)

    console.log('\n📋 测试建议:')
    console.log('1. 使用 test-avatar-200x200.png 测试正常头像上传')
    console.log('2. 使用 test-avatar-600x600.png 测试大尺寸图片处理')
    console.log('3. 使用 test-avatar-150x150.png 测试最小尺寸限制')
    console.log('4. 在浏览器中访问 http://localhost:3000/profile 进行测试')

  } catch (error) {
    console.error('❌ 创建测试头像失败:', error)
  }
}

createTestAvatar()
