const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testPaymentStatusCheck() {
  try {
    console.log('🧪 测试支付页面状态检查功能')
    console.log('=' .repeat(50))
    
    // 1. 查找不同状态的订单
    console.log('\n1. 查找不同状态的订单...')
    
    const orderStatuses = ['PENDING_PAYMENT', 'PAID', 'SHIPPED', 'COMPLETED', 'CANCELLED', 'REFUND_REQUESTED']
    
    for (const status of orderStatuses) {
      const orders = await prisma.order.findMany({
        where: { status },
        select: {
          id: true,
          orderNumber: true,
          status: true,
          totalAmount: true,
          buyer: {
            select: { name: true, email: true }
          },
          product: {
            select: { title: true }
          }
        },
        take: 2
      })
      
      console.log(`\n   ${status} 状态订单: ${orders.length} 个`)
      orders.forEach((order, index) => {
        console.log(`     ${index + 1}. 订单 ${order.orderNumber}`)
        console.log(`        商品: ${order.product.title}`)
        console.log(`        买家: ${order.buyer.name || order.buyer.email}`)
        console.log(`        金额: ${order.totalAmount} USDT`)
        console.log(`        支付页面: http://localhost:3000/orders/${order.id}/payment`)
      })
    }
    
    // 2. 测试场景说明
    console.log('\n2. 测试场景说明...')
    console.log('=' .repeat(40))
    
    console.log('\n✅ 应该允许支付的订单:')
    console.log('   • 状态: PENDING_PAYMENT')
    console.log('   • 显示: 正常的支付页面')
    console.log('   • 行为: 可以选择支付方式并提交支付')
    
    console.log('\n❌ 应该拒绝支付的订单:')
    console.log('   • 状态: PAID - 显示"此订单已支付，正在等待发货"')
    console.log('   • 状态: SHIPPED - 显示"此订单已发货，正在配送中"')
    console.log('   • 状态: COMPLETED - 显示"此订单已完成，无需再次支付"')
    console.log('   • 状态: CANCELLED - 显示"此订单已取消，无法支付"')
    console.log('   • 状态: REFUND_REQUESTED - 显示"此订单正在申请退款，无法支付"')
    
    // 3. 检查支付页面的改进
    console.log('\n3. 支付页面改进内容...')
    console.log('=' .repeat(40))
    
    console.log('\n🔧 状态检查逻辑改进:')
    console.log('   • 详细的状态判断逻辑')
    console.log('   • 针对不同状态的具体错误信息')
    console.log('   • 更友好的用户提示')
    
    console.log('\n🎨 错误页面改进:')
    console.log('   • 更美观的错误页面设计')
    console.log('   • 清晰的图标和提示信息')
    console.log('   • 多个返回选项（订单详情、订单列表、首页）')
    
    console.log('\n🛡️ 安全性改进:')
    console.log('   • 防止重复支付')
    console.log('   • 状态验证更加严格')
    console.log('   • 明确的错误信息，避免用户困惑')
    
    // 4. 测试步骤
    console.log('\n4. 手动测试步骤...')
    console.log('=' .repeat(40))
    
    console.log('\n步骤 1: 测试已支付订单')
    console.log('   1. 找一个状态为 PAID 的订单')
    console.log('   2. 访问其支付页面: /orders/{orderId}/payment')
    console.log('   3. 应该看到错误信息: "此订单已支付，正在等待发货"')
    console.log('   4. 页面应该显示返回按钮，不显示支付表单')
    
    console.log('\n步骤 2: 测试已完成订单')
    console.log('   1. 找一个状态为 COMPLETED 的订单')
    console.log('   2. 访问其支付页面: /orders/{orderId}/payment')
    console.log('   3. 应该看到错误信息: "此订单已完成，无需再次支付"')
    console.log('   4. 页面应该显示返回按钮，不显示支付表单')
    
    console.log('\n步骤 3: 测试待支付订单')
    console.log('   1. 找一个状态为 PENDING_PAYMENT 的订单')
    console.log('   2. 访问其支付页面: /orders/{orderId}/payment')
    console.log('   3. 应该看到正常的支付页面')
    console.log('   4. 可以选择支付方式并进行支付')
    
    // 5. 预期结果
    console.log('\n5. 预期结果...')
    console.log('=' .repeat(40))
    
    console.log('\n✅ 功能正常的表现:')
    console.log('   • 已支付/完成的订单无法再次支付')
    console.log('   • 错误信息清晰明确')
    console.log('   • 页面设计美观友好')
    console.log('   • 提供合适的返回选项')
    console.log('   • 只有 PENDING_PAYMENT 状态的订单可以支付')
    
    console.log('\n❌ 需要注意的问题:')
    console.log('   • 如果任何已支付订单仍能访问支付表单')
    console.log('   • 如果错误信息不够清晰')
    console.log('   • 如果页面设计不够友好')
    console.log('   • 如果缺少返回选项')
    
    console.log('\n🔗 快速测试链接:')
    const testOrder = await prisma.order.findFirst({
      where: { status: { not: 'PENDING_PAYMENT' } },
      select: { id: true, status: true, orderNumber: true }
    })
    
    if (testOrder) {
      console.log(`   测试订单: ${testOrder.orderNumber} (状态: ${testOrder.status})`)
      console.log(`   支付页面: http://localhost:3000/orders/${testOrder.id}/payment`)
      console.log(`   预期结果: 应该显示错误信息，不允许支付`)
    } else {
      console.log('   没有找到非待支付状态的订单用于测试')
    }
    
  } catch (error) {
    console.error('测试准备失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testPaymentStatusCheck()
  .then(() => {
    console.log('\n🎉 支付状态检查功能测试准备完成！')
    console.log('现在可以手动测试支付页面的状态检查功能了。')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 测试准备失败:', error)
    process.exit(1)
  })
