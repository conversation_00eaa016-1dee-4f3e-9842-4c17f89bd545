/**
 * 资金流动系统测试脚本
 * 用于验证"冻结 → 状态确认 → 划扣"机制
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// 模拟用户数据
const testUsers = [
  {
    email: '<EMAIL>',
    name: '测试买家',
    depositBalance: 1000, // 1000 USDT担保金
    role: 'USER'
  },
  {
    email: '<EMAIL>',
    name: '测试卖家',
    depositBalance: 500,
    role: 'USER'
  },
  {
    email: '<EMAIL>',
    name: '测试管理员',
    depositBalance: 0,
    role: 'ADMIN'
  }
]

async function createTestUsers() {
  console.log('🔧 创建测试用户...')
  
  const users = {}
  
  for (const userData of testUsers) {
    try {
      const user = await prisma.user.upsert({
        where: { email: userData.email },
        update: userData,
        create: userData
      })
      users[userData.email] = user
      console.log(`✅ 创建用户: ${user.name} (${user.email})`)
    } catch (error) {
      console.error(`❌ 创建用户失败: ${userData.email}`, error.message)
    }
  }
  
  return users
}

async function testPurchaseFlow(buyerId, sellerId) {
  console.log('\n📦 测试商品购买流程...')
  
  try {
    // 1. 创建测试商品
    const product = await prisma.product.create({
      data: {
        title: '测试商品',
        description: '这是一个测试商品',
        price: 100,
        sellerId: sellerId,
        status: 'AVAILABLE',
        city: '北京',
        district: '朝阳区'
      }
    })
    console.log(`✅ 创建商品: ${product.title} - ${product.price} USDT`)

    // 2. 创建订单
    const order = await prisma.order.create({
      data: {
        orderNumber: `TEST-${Date.now()}`,
        productId: product.id,
        buyerId: buyerId,
        sellerId: sellerId,
        status: 'PENDING_PAYMENT',
        totalAmount: 105, // 商品价格 + 运费
        productPrice: 100,
        shippingFee: 5,
        platformFee: 5.25, // 5% 平台费
        shippingAddress: '测试地址'
      }
    })
    console.log(`✅ 创建订单: ${order.orderNumber}`)

    // 3. 测试资金冻结
    const path = require('path')
    const fundFlowPath = path.join(__dirname, '../lib/fund-flow.ts')

    // 由于是TypeScript文件，我们需要直接使用数据库操作
    // 创建冻结记录
    const freezeRecord = await prisma.fundFreeze.create({
      data: {
        userId: buyerId,
        amount: order.totalAmount,
        purpose: 'PURCHASE',
        relatedId: order.id,
        relatedType: 'ORDER',
        status: 'FROZEN',
        notes: `测试商品购买: ${order.orderNumber}`
      }
    })
    console.log(`✅ 资金冻结成功: ${freezeRecord.id}`)

    // 4. 确认支付状态
    await prisma.fundFreeze.update({
      where: { id: freezeRecord.id },
      data: {
        status: 'CONFIRMED',
        confirmedBy: buyerId,
        confirmedAt: new Date()
      }
    })
    console.log(`✅ 支付状态确认成功`)

    // 5. 模拟发货和收货，然后执行划扣
    const platformFee = order.totalAmount * 0.05
    const actualAmount = order.totalAmount - platformFee

    await prisma.$transaction(async (tx) => {
      // 更新冻结记录
      await tx.fundFreeze.update({
        where: { id: freezeRecord.id },
        data: {
          status: 'SETTLED',
          toUserId: sellerId,
          platformFee,
          actualAmount,
          settledBy: buyerId,
          settledAt: new Date()
        }
      })

      // 从买家扣除担保金
      await tx.user.update({
        where: { id: buyerId },
        data: {
          depositBalance: {
            decrement: order.totalAmount
          }
        }
      })

      // 给卖家增加担保金
      await tx.user.update({
        where: { id: sellerId },
        data: {
          depositBalance: {
            increment: actualAmount
          }
        }
      })
    })
    console.log(`✅ 资金划扣成功，交易完成`)

    return { product, order, freezeId: freezeRecord.id }

  } catch (error) {
    console.error('❌ 商品购买流程测试失败:', error.message)
    throw error
  }
}

async function testWithdrawalFlow(userId) {
  console.log('\n💰 测试提现流程...')

  try {
    const amount = 50
    const withdrawalFee = amount * 0.01
    const actualAmount = amount - withdrawalFee

    // 1. 创建提现记录
    const withdrawal = await prisma.withdrawal.create({
      data: {
        userId: userId,
        amount: amount,
        walletAddress: '******************************************',
        withdrawalFee: withdrawalFee,
        actualAmount: actualAmount,
        status: 'PENDING',
        notes: '测试提现申请'
      }
    })

    // 2. 创建冻结记录
    const freezeRecord = await prisma.fundFreeze.create({
      data: {
        userId: userId,
        amount: amount,
        purpose: 'WITHDRAWAL',
        relatedId: withdrawal.id,
        relatedType: 'WITHDRAWAL',
        status: 'FROZEN',
        notes: `提现申请冻结资金: ${withdrawal.id}`
      }
    })

    console.log(`✅ 提现申请创建成功: ${withdrawal.id}`)
    console.log(`✅ 资金冻结成功: ${freezeRecord.id}`)

    return { withdrawalId: withdrawal.id, freezeId: freezeRecord.id }

  } catch (error) {
    console.error('❌ 提现流程测试失败:', error.message)
    throw error
  }
}

async function testBalanceQueries(userId) {
  console.log('\n💳 测试余额查询...')

  try {
    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { depositBalance: true, name: true }
    })

    // 计算冻结金额
    const frozenAmount = await prisma.fundFreeze.aggregate({
      where: {
        userId,
        status: {
          in: ['FROZEN', 'CONFIRMED']
        }
      },
      _sum: {
        amount: true
      }
    })

    const totalFrozen = frozenAmount._sum.amount || 0
    const availableBalance = Math.max(0, user.depositBalance - totalFrozen)

    console.log(`✅ 用户: ${user.name}`)
    console.log(`✅ 总担保金: ${user.depositBalance} USDT`)
    console.log(`✅ 冻结金额: ${totalFrozen} USDT`)
    console.log(`✅ 可用余额: ${availableBalance} USDT`)

    // 查询冻结记录
    const freezeRecords = await prisma.fundFreeze.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    console.log(`✅ 冻结记录数量: ${freezeRecords.length}`)

    // 显示最近的记录
    if (freezeRecords.length > 0) {
      console.log('📋 最近的冻结记录:')
      freezeRecords.slice(0, 3).forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.purpose} - ${record.amount} USDT - ${record.status}`)
      })
    }

    return { availableBalance, freezeRecords }

  } catch (error) {
    console.error('❌ 余额查询测试失败:', error.message)
    throw error
  }
}

async function runTests() {
  console.log('🚀 开始资金流动系统测试\n')
  
  try {
    // 创建测试用户
    const users = await createTestUsers()
    const buyer = users['<EMAIL>']
    const seller = users['<EMAIL>']
    const admin = users['<EMAIL>']
    
    // 测试购买流程
    await testPurchaseFlow(buyer.id, seller.id)
    
    // 测试提现流程
    await testWithdrawalFlow(seller.id)
    
    // 测试余额查询
    await testBalanceQueries(buyer.id)
    await testBalanceQueries(seller.id)
    
    console.log('\n🎉 所有测试完成！')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  runTests()
}

module.exports = {
  createTestUsers,
  testPurchaseFlow,
  testWithdrawalFlow,
  testBalanceQueries,
  runTests
}
