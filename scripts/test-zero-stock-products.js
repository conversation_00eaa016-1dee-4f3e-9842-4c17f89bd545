#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testZeroStockProducts() {
  try {
    console.log('🔍 测试0库存商品状态管理')
    console.log('='.repeat(50))

    // 1. 查找所有0库存但状态为AVAILABLE的商品
    console.log('\n📊 查找0库存但仍在售的商品...')
    const zeroStockAvailable = await prisma.product.findMany({
      where: {
        stock: {
          lte: 0
        },
        status: 'AVAILABLE'
      },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        seller: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    console.log(`发现 ${zeroStockAvailable.length} 个0库存但仍在售的商品`)
    
    if (zeroStockAvailable.length > 0) {
      console.log('\n📋 商品列表:')
      zeroStockAvailable.forEach((product, index) => {
        console.log(`${index + 1}. ${product.title} (ID: ${product.id})`)
        console.log(`   库存: ${product.stock}, 状态: ${product.status}`)
        console.log(`   卖家: ${product.seller.name} (${product.seller.id})`)
        console.log('')
      })

      // 2. 自动修复这些商品的状态
      console.log('🔧 开始修复商品状态...')
      const updateResult = await prisma.product.updateMany({
        where: {
          id: {
            in: zeroStockAvailable.map(p => p.id)
          }
        },
        data: {
          status: 'SOLD_OUT'
        }
      })

      console.log(`✅ 已修复 ${updateResult.count} 个商品的状态为 SOLD_OUT`)
    }

    // 3. 统计各状态商品数量
    console.log('\n📈 商品状态统计:')
    const statusStats = await prisma.product.groupBy({
      by: ['status'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    statusStats.forEach(stat => {
      console.log(`${stat.status}: ${stat._count.id} 个商品`)
    })

    // 4. 查找SOLD_OUT状态的商品
    console.log('\n🔍 查找SOLD_OUT状态的商品...')
    const soldOutProducts = await prisma.product.findMany({
      where: {
        status: 'SOLD_OUT'
      },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        seller: {
          select: {
            id: true,
            name: true
          }
        }
      },
      take: 10 // 只显示前10个
    })

    console.log(`找到 ${soldOutProducts.length} 个SOLD_OUT状态的商品`)
    
    if (soldOutProducts.length > 0) {
      console.log('\n📋 SOLD_OUT商品列表 (前10个):')
      soldOutProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.title} (ID: ${product.id})`)
        console.log(`   库存: ${product.stock}, 状态: ${product.status}`)
        console.log(`   卖家: ${product.seller.name} (${product.seller.id})`)
        console.log('')
      })
    }

    // 5. 检查特定用户的商品状态分布
    const testUserId = 'cmd8desog0002v9rwq6iekv1c'
    console.log(`\n👤 检查用户 ${testUserId} 的商品状态分布...`)
    
    const userProducts = await prisma.product.findMany({
      where: {
        sellerId: testUserId
      },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true
      }
    })

    if (userProducts.length > 0) {
      console.log(`用户共有 ${userProducts.length} 个商品`)
      
      const userStatusStats = {}
      userProducts.forEach(product => {
        userStatusStats[product.status] = (userStatusStats[product.status] || 0) + 1
      })

      console.log('状态分布:')
      Object.entries(userStatusStats).forEach(([status, count]) => {
        console.log(`  ${status}: ${count} 个`)
      })

      // 显示0库存的商品
      const zeroStockUserProducts = userProducts.filter(p => p.stock <= 0)
      if (zeroStockUserProducts.length > 0) {
        console.log(`\n📦 该用户的0库存商品 (${zeroStockUserProducts.length}个):`)
        zeroStockUserProducts.forEach(product => {
          console.log(`  - ${product.title} (库存: ${product.stock}, 状态: ${product.status})`)
        })
      }
    } else {
      console.log('该用户没有商品')
    }

    // 6. 测试库存管理功能
    console.log('\n🧪 测试库存管理功能...')

    try {
      // 手动实现批量检查功能
      const outOfStockProducts = await prisma.product.findMany({
        where: {
          stock: {
            lte: 0
          },
          status: 'AVAILABLE'
        }
      })

      if (outOfStockProducts.length > 0) {
        const updateResult = await prisma.product.updateMany({
          where: {
            id: {
              in: outOfStockProducts.map(p => p.id)
            }
          },
          data: {
            status: 'SOLD_OUT'
          }
        })
        console.log(`批量检查结果: 下架了 ${updateResult.count} 个库存不足的商品`)
      } else {
        console.log('批量检查结果: 没有需要下架的商品')
      }
    } catch (error) {
      console.log('库存管理功能测试跳过:', error.message)
    }

    // 7. 验证修复结果
    console.log('\n✅ 验证修复结果...')
    const finalZeroStockAvailable = await prisma.product.count({
      where: {
        stock: {
          lte: 0
        },
        status: 'AVAILABLE'
      }
    })

    console.log(`修复后仍有 ${finalZeroStockAvailable} 个0库存但在售的商品`)
    
    if (finalZeroStockAvailable === 0) {
      console.log('🎉 所有0库存商品状态已正确修复！')
    } else {
      console.log('⚠️  仍有商品需要修复')
    }

    console.log('\n📊 最终状态统计:')
    const finalStats = await prisma.product.groupBy({
      by: ['status'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    finalStats.forEach(stat => {
      console.log(`${stat.status}: ${stat._count.id} 个商品`)
    })

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testZeroStockProducts()
    .then(() => {
      console.log('\n✅ 测试完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 测试失败:', error)
      process.exit(1)
    })
}

module.exports = { testZeroStockProducts }
