const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
const readline = require('readline')

const prisma = new PrismaClient()

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 提示用户输入
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// 验证邮箱格式
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证密码强度
function isValidPassword(password) {
  return password.length >= 6
}

// 生成用户ID
function generateUserId() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = 'admin-'
  for (let i = 0; i < 10; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

async function createCustomAdmin() {
  console.log('🔧 创建自定义管理员账号\n')
  console.log('请按照提示输入管理员信息：\n')

  try {
    // 获取邮箱
    let email = ''
    while (!email || !isValidEmail(email)) {
      email = await askQuestion('请输入管理员邮箱: ')
      if (!isValidEmail(email)) {
        console.log('❌ 邮箱格式不正确，请重新输入')
      }
    }

    // 获取密码
    let password = ''
    while (!password || !isValidPassword(password)) {
      password = await askQuestion('请输入管理员密码 (至少6位): ')
      if (!isValidPassword(password)) {
        console.log('❌ 密码至少需要6位字符，请重新输入')
      }
    }

    // 获取姓名
    let name = await askQuestion('请输入管理员姓名 (可选，按回车跳过): ')
    if (!name.trim()) {
      name = '系统管理员'
    }

    console.log('\n📋 确认信息:')
    console.log(`   邮箱: ${email}`)
    console.log(`   密码: ${'*'.repeat(password.length)}`)
    console.log(`   姓名: ${name}`)

    const confirm = await askQuestion('\n确认创建管理员账号吗？(y/n): ')
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ 操作已取消')
      return
    }

    console.log('\n🔄 正在创建管理员账号...\n')

    // 检查用户是否已存在
    console.log('1. 检查用户是否已存在...')
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      console.log(`⚠️  用户 ${email} 已存在`)
      
      const updateConfirm = await askQuestion('是否将现有用户更新为管理员？(y/n): ')
      if (updateConfirm.toLowerCase() !== 'y' && updateConfirm.toLowerCase() !== 'yes') {
        console.log('❌ 操作已取消')
        return
      }

      // 更新为管理员权限
      console.log('2. 更新用户为管理员权限...')
      const hashedPassword = await bcrypt.hash(password, 12)
      
      const updatedUser = await prisma.user.update({
        where: { email },
        data: {
          role: 'ADMIN',
          name: name,
          password: hashedPassword,
          emailVerified: new Date()
        }
      })
      
      console.log(`✅ 用户 ${email} 已更新为管理员`)
      console.log(`📋 用户信息:`)
      console.log(`   - ID: ${updatedUser.id}`)
      console.log(`   - 用户ID: ${updatedUser.userId}`)
      console.log(`   - 邮箱: ${updatedUser.email}`)
      console.log(`   - 姓名: ${updatedUser.name}`)
      console.log(`   - 角色: ${updatedUser.role}`)
      console.log(`   - 信用分: ${updatedUser.creditScore}`)
      
      return updatedUser
    }

    // 生成唯一用户ID
    console.log('2. 生成用户ID...')
    let userId = generateUserId()
    
    let existingUserId = await prisma.user.findUnique({
      where: { userId }
    })
    
    while (existingUserId) {
      userId = generateUserId()
      existingUserId = await prisma.user.findUnique({
        where: { userId }
      })
    }
    
    console.log(`✅ 生成用户ID: ${userId}`)

    // 加密密码
    console.log('3. 加密密码...')
    const hashedPassword = await bcrypt.hash(password, 12)
    console.log('✅ 密码加密完成')

    // 创建用户
    console.log('4. 创建管理员用户...')
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        userId,
        role: 'ADMIN',
        creditScore: 100, // 管理员默认高信用分
        emailVerified: new Date(), // 管理员账号默认已验证
        status: 'ACTIVE',
        depositBalance: 0
      }
    })

    console.log(`✅ 管理员账号创建成功！`)
    console.log(`📋 账号信息:`)
    console.log(`   - ID: ${newUser.id}`)
    console.log(`   - 用户ID: ${newUser.userId}`)
    console.log(`   - 邮箱: ${newUser.email}`)
    console.log(`   - 姓名: ${newUser.name}`)
    console.log(`   - 角色: ${newUser.role}`)
    console.log(`   - 信用分: ${newUser.creditScore}`)
    console.log(`   - 邮箱验证: ${newUser.emailVerified ? '已验证' : '未验证'}`)

    // 验证登录
    console.log('\n5. 验证登录功能...')
    const loginTest = await bcrypt.compare(password, newUser.password)
    console.log(`✅ 密码验证: ${loginTest ? '成功' : '失败'}`)

    // 检查管理员权限
    console.log('\n6. 验证管理员权限...')
    const adminUser = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        userId: true,
        creditScore: true
      }
    })

    if (adminUser && adminUser.role === 'ADMIN') {
      console.log('✅ 管理员权限验证成功')
    } else {
      console.log('❌ 管理员权限验证失败')
    }

    console.log('\n🎉 管理员账号创建完成！')
    console.log('\n📝 登录信息:')
    console.log(`   邮箱: ${email}`)
    console.log(`   密码: ${password}`)
    console.log('\n🔗 可以使用以下链接登录:')
    console.log('   登录页面: http://localhost:3000/auth/signin')
    console.log('   管理后台: http://localhost:3000/admin')

    return newUser

  } catch (error) {
    console.error('❌ 创建管理员账号失败:', error)
    
    if (error.code === 'P2002') {
      console.log('💡 提示: 该邮箱或用户ID已被使用')
    }
    
    throw error
  } finally {
    rl.close()
    await prisma.$disconnect()
  }
}

// 运行脚本
if (require.main === module) {
  createCustomAdmin().catch(console.error)
}

module.exports = { createCustomAdmin }
