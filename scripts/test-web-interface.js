const axios = require('axios')

const BASE_URL = 'http://localhost:3000'

// 模拟浏览器请求的配置
const axiosConfig = {
  timeout: 10000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
  }
}

async function testWebInterface() {
  try {
    console.log('🌐 开始测试Web界面和API...')
    console.log('=' .repeat(60))

    // 1. 测试首页加载
    console.log('\n🏠 1. 测试首页加载')
    await testHomePage()

    // 2. 测试公开API
    console.log('\n🔓 2. 测试公开API')
    await testPublicAPIs()

    // 3. 测试商品搜索
    console.log('\n🔍 3. 测试商品搜索')
    await testProductSearch()

    // 4. 测试热门关键词
    console.log('\n🔥 4. 测试热门关键词')
    await testHotKeywords()

    // 5. 测试公告系统
    console.log('\n📢 5. 测试公告系统')
    await testAnnouncements()

    // 6. 测试礼品卡验证
    console.log('\n🎁 6. 测试礼品卡验证')
    await testGiftCardValidation()

    // 7. 测试兑换码验证
    console.log('\n🎫 7. 测试兑换码验证')
    await testRedemptionCodeValidation()

    // 8. 测试系统状态
    console.log('\n📊 8. 测试系统状态')
    await testSystemStatus()

    console.log('\n🎉 Web界面测试完成！')

  } catch (error) {
    console.error('❌ Web界面测试失败:', error.message)
  }
}

// 1. 测试首页加载
async function testHomePage() {
  try {
    const response = await axios.get(`${BASE_URL}`, axiosConfig)
    
    if (response.status === 200) {
      console.log('✅ 首页加载成功')
      console.log(`  - 状态码: ${response.status}`)
      console.log(`  - 内容类型: ${response.headers['content-type']}`)
      console.log(`  - 响应大小: ${response.data.length} 字符`)
    }
  } catch (error) {
    console.log('⚠️  首页加载失败:', error.message)
  }
}

// 2. 测试公开API
async function testPublicAPIs() {
  try {
    // 测试公开设置API
    const settingsResponse = await axios.get(`${BASE_URL}/api/settings/public`, axiosConfig)
    
    if (settingsResponse.status === 200) {
      console.log('✅ 公开设置API正常')
      console.log(`  - 平台名称: ${settingsResponse.data.settings.platform?.name || '未设置'}`)
      console.log(`  - 数据源: ${settingsResponse.data.source}`)
    }

    // 测试平台基本信息
    const platformResponse = await axios.post(`${BASE_URL}/api/settings/public`, 
      { type: 'basic' }, 
      axiosConfig
    )
    
    if (platformResponse.status === 200) {
      console.log('✅ 平台信息API正常')
      const data = platformResponse.data.data
      console.log(`  - 平台名称: ${data.name}`)
      console.log(`  - 平台描述: ${data.description}`)
      console.log(`  - 总用户数: ${data.stats?.totalUsers || 0}`)
      console.log(`  - 总商品数: ${data.stats?.totalProducts || 0}`)
    }

  } catch (error) {
    console.log('⚠️  公开API测试失败:', error.message)
  }
}

// 3. 测试商品搜索
async function testProductSearch() {
  try {
    // 测试商品列表API
    const productsResponse = await axios.get(`${BASE_URL}/api/products?page=1&limit=5`, axiosConfig)
    
    if (productsResponse.status === 200) {
      console.log('✅ 商品列表API正常')
      const data = productsResponse.data
      console.log(`  - 找到商品: ${data.products?.length || 0} 个`)
      console.log(`  - 总数: ${data.total || 0}`)
      console.log(`  - 当前页: ${data.pagination?.page || 1}`)
      
      if (data.products && data.products.length > 0) {
        const product = data.products[0]
        console.log(`  - 示例商品: ${product.title}`)
        console.log(`  - 价格: ${product.price} USDT`)
      }
    }

    // 测试商品搜索
    const searchResponse = await axios.get(
      `${BASE_URL}/api/products?keyword=手机&page=1&limit=3`, 
      axiosConfig
    )
    
    if (searchResponse.status === 200) {
      console.log('✅ 商品搜索API正常')
      const data = searchResponse.data
      console.log(`  - 搜索结果: ${data.products?.length || 0} 个`)
    }

  } catch (error) {
    console.log('⚠️  商品搜索测试失败:', error.message)
  }
}

// 4. 测试热门关键词
async function testHotKeywords() {
  try {
    const response = await axios.post(`${BASE_URL}/api/products/search`, 
      { keyword: '电脑', resultCount: 3 }, 
      axiosConfig
    )
    
    if (response.status === 200) {
      console.log('✅ 热门关键词API正常')
      const data = response.data
      console.log(`  - 热门关键词数量: ${data.hotKeywords?.length || 0}`)
      
      if (data.hotKeywords && data.hotKeywords.length > 0) {
        console.log('  - 热门关键词:')
        data.hotKeywords.slice(0, 5).forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.keyword} (${item.category}) - ${item.count} 次`)
        })
      }
    }

  } catch (error) {
    console.log('⚠️  热门关键词测试失败:', error.message)
  }
}

// 5. 测试公告系统
async function testAnnouncements() {
  try {
    const response = await axios.get(`${BASE_URL}/api/announcements/home`, axiosConfig)
    
    if (response.status === 200) {
      console.log('✅ 公告系统API正常')
      const data = response.data
      console.log(`  - 轮播公告: ${data.carouselItems?.length || 0} 条`)
      console.log(`  - 置顶公告: ${data.stickyAnnouncements?.length || 0} 条`)
      console.log(`  - 最新公告: ${data.latestAnnouncements?.length || 0} 条`)
      
      if (data.carouselItems && data.carouselItems.length > 0) {
        const announcement = data.carouselItems[0]
        console.log(`  - 最新公告: ${announcement.title}`)
        console.log(`    类别: ${announcement.category}`)
        console.log(`    优先级: ${announcement.priority}`)
      }
    }

  } catch (error) {
    console.log('⚠️  公告系统测试失败:', error.message)
  }
}

// 6. 测试礼品卡验证
async function testGiftCardValidation() {
  try {
    // 测试有效的礼品卡
    const validResponse = await axios.post(`${BASE_URL}/api/giftcards/validate`, 
      { cardCode: 'M45XX2MZK5WV9E7R' }, 
      axiosConfig
    )
    
    if (validResponse.status === 200) {
      console.log('✅ 礼品卡验证API正常')
      const data = validResponse.data
      if (data.valid) {
        console.log(`  - 礼品卡有效: ${data.giftCard.cardCode}`)
        console.log(`  - 面值: ${data.giftCard.faceValue} USDT`)
        console.log(`  - 状态: ${data.giftCard.status}`)
      } else {
        console.log(`  - 礼品卡无效或已使用`)
      }
    }

    // 测试无效的礼品卡
    const invalidResponse = await axios.post(`${BASE_URL}/api/giftcards/validate`, 
      { cardCode: 'INVALID_CODE' }, 
      axiosConfig
    )
    
    if (invalidResponse.status === 200) {
      const data = invalidResponse.data
      console.log(`  - 无效礼品卡测试: ${data.valid ? '失败' : '通过'}`)
    }

  } catch (error) {
    console.log('⚠️  礼品卡验证测试失败:', error.message)
  }
}

// 7. 测试兑换码验证
async function testRedemptionCodeValidation() {
  try {
    // 测试有效的兑换码
    const validResponse = await axios.post(`${BASE_URL}/api/redemption-codes/validate`, 
      { code: '57XSKSNBEJOM' }, 
      axiosConfig
    )
    
    if (validResponse.status === 200) {
      console.log('✅ 兑换码验证API正常')
      const data = validResponse.data
      if (data.valid) {
        console.log(`  - 兑换码有效: ${data.redemptionCode.codeValue}`)
        console.log(`  - 奖励: ${data.redemptionCode.rewardValue} ${data.redemptionCode.rewardUnit}`)
        console.log(`  - 类型: ${data.redemptionCode.rewardType}`)
        console.log(`  - 使用次数: ${data.redemptionCode.usedCount}/${data.redemptionCode.maxUses}`)
      } else {
        console.log(`  - 兑换码无效或已用完`)
      }
    }

    // 测试无效的兑换码
    const invalidResponse = await axios.post(`${BASE_URL}/api/redemption-codes/validate`, 
      { code: 'INVALID_CODE' }, 
      axiosConfig
    )
    
    if (invalidResponse.status === 200) {
      const data = invalidResponse.data
      console.log(`  - 无效兑换码测试: ${data.valid ? '失败' : '通过'}`)
    }

  } catch (error) {
    console.log('⚠️  兑换码验证测试失败:', error.message)
  }
}

// 8. 测试系统状态
async function testSystemStatus() {
  try {
    // 测试API响应时间
    const startTime = Date.now()
    const response = await axios.get(`${BASE_URL}/api/settings/public`, axiosConfig)
    const responseTime = Date.now() - startTime
    
    console.log('✅ 系统性能测试')
    console.log(`  - API响应时间: ${responseTime}ms`)
    console.log(`  - 服务器状态: ${response.status === 200 ? '正常' : '异常'}`)
    
    // 测试并发请求
    console.log('  - 测试并发请求...')
    const concurrentRequests = Array(5).fill().map(() => 
      axios.get(`${BASE_URL}/api/settings/public`, axiosConfig)
    )
    
    const concurrentStart = Date.now()
    const results = await Promise.allSettled(concurrentRequests)
    const concurrentTime = Date.now() - concurrentStart
    
    const successCount = results.filter(r => r.status === 'fulfilled').length
    console.log(`  - 并发请求结果: ${successCount}/5 成功`)
    console.log(`  - 并发处理时间: ${concurrentTime}ms`)

  } catch (error) {
    console.log('⚠️  系统状态测试失败:', error.message)
  }
}

// 运行测试
testWebInterface()
