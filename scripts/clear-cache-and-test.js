const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function clearCacheAndTest() {
  try {
    console.log('🧹 清除缓存并测试商品显示');
    console.log('='.repeat(50));
    
    const userId = 'cmd8desog0002v9rwq6iekv1c';
    
    // 1. 检查当前商品状态
    console.log('\n📊 检查当前商品状态:');
    const products = await prisma.product.findMany({
      where: { sellerId: userId },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        reviewStatus: true,
        updatedAt: true
      }
    });
    
    console.log(`找到 ${products.length} 个商品:`);
    products.forEach(p => {
      console.log(`  - ${p.title}`);
      console.log(`    状态: ${p.status}, 库存: ${p.stock}, 审核: ${p.reviewStatus}`);
      console.log(`    更新时间: ${p.updatedAt}`);
    });
    
    // 2. 模拟清除缓存（通过添加nocache参数）
    console.log('\n🔄 测试带nocache参数的查询:');
    
    const testStatuses = ['AVAILABLE', 'SOLD', 'INACTIVE'];
    
    for (const status of testStatuses) {
      console.log(`\n📋 测试 ${status} 状态:`);
      
      const where = {
        sellerId: userId,
        reviewStatus: 'APPROVED'
      };
      
      if (status === 'INACTIVE') {
        where.status = {
          in: ['INACTIVE', 'SOLD_OUT']
        };
      } else {
        where.status = status;
      }
      
      const statusProducts = await prisma.product.findMany({
        where,
        select: {
          id: true,
          title: true,
          status: true,
          stock: true
        },
        orderBy: { createdAt: 'desc' }
      });
      
      console.log(`  找到 ${statusProducts.length} 个商品`);
      statusProducts.forEach(p => {
        console.log(`    - ${p.title} (状态: ${p.status}, 库存: ${p.stock})`);
      });
    }
    
    // 3. 检查是否有其他状态的商品
    console.log('\n🔍 检查所有可能的商品状态:');
    const allStatuses = await prisma.product.groupBy({
      by: ['status'],
      where: { sellerId: userId },
      _count: { id: true }
    });
    
    console.log('用户商品状态分布:');
    allStatuses.forEach(s => {
      console.log(`  ${s.status}: ${s._count.id} 个`);
    });
    
    // 4. 检查审核状态
    console.log('\n📝 检查审核状态:');
    const reviewStatuses = await prisma.product.groupBy({
      by: ['reviewStatus'],
      where: { sellerId: userId },
      _count: { id: true }
    });
    
    console.log('用户商品审核状态分布:');
    reviewStatuses.forEach(s => {
      console.log(`  ${s.reviewStatus}: ${s._count.id} 个`);
    });
    
    // 5. 如果有SOLD_OUT商品但审核状态不是APPROVED，修复它
    const soldOutProducts = await prisma.product.findMany({
      where: {
        sellerId: userId,
        status: 'SOLD_OUT'
      },
      select: {
        id: true,
        title: true,
        reviewStatus: true
      }
    });
    
    if (soldOutProducts.length > 0) {
      console.log('\n🔧 检查SOLD_OUT商品的审核状态:');
      for (const product of soldOutProducts) {
        console.log(`  - ${product.title}: 审核状态 ${product.reviewStatus}`);
        
        if (product.reviewStatus !== 'APPROVED') {
          console.log(`    ⚠️  商品审核状态不是APPROVED，正在修复...`);
          await prisma.product.update({
            where: { id: product.id },
            data: { reviewStatus: 'APPROVED' }
          });
          console.log(`    ✅ 已修复商品 ${product.title} 的审核状态`);
        }
      }
    }
    
    // 6. 最终验证
    console.log('\n✅ 最终验证 - INACTIVE标签页应该显示的商品:');
    const finalCheck = await prisma.product.findMany({
      where: {
        sellerId: userId,
        reviewStatus: 'APPROVED',
        status: {
          in: ['INACTIVE', 'SOLD_OUT']
        }
      },
      select: {
        id: true,
        title: true,
        status: true,
        stock: true,
        reviewStatus: true
      }
    });
    
    console.log(`INACTIVE标签页应该显示 ${finalCheck.length} 个商品:`);
    finalCheck.forEach(p => {
      console.log(`  - ${p.title}`);
      console.log(`    状态: ${p.status}, 库存: ${p.stock}, 审核: ${p.reviewStatus}`);
    });
    
    if (finalCheck.length === 0) {
      console.log('\n❌ 没有找到应该在INACTIVE标签页显示的商品');
      console.log('这可能是前端缓存问题，建议：');
      console.log('1. 刷新浏览器页面');
      console.log('2. 清除浏览器缓存');
      console.log('3. 在URL后添加 ?nocache=1 参数');
    } else {
      console.log('\n✅ 找到了应该显示的商品，如果前端没显示可能是缓存问题');
    }
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ 错误:', error);
    await prisma.$disconnect();
  }
}

clearCacheAndTest();
