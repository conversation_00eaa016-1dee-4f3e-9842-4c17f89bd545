const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testUserProductsAndDemands() {
  console.log('🔍 测试用户商品和需求管理页面...\n')

  try {
    // 1. 获取测试用户
    console.log('1. 获取测试用户...')
    const users = await prisma.user.findMany({
      take: 2,
      select: {
        id: true,
        name: true,
        email: true,
        userId: true
      }
    })

    if (users.length === 0) {
      console.log('❌ 没有找到测试用户')
      return
    }

    const testUser = users[0]
    console.log(`✅ 测试用户: ${testUser.name} (${testUser.email})`)

    // 2. 检查用户的商品
    console.log('\n2. 检查用户商品...')
    const products = await prisma.product.findMany({
      where: { sellerId: testUser.id },
      select: {
        id: true,
        title: true,
        status: true,
        price: true,
        createdAt: true
      },
      take: 5
    })

    console.log(`✅ 用户商品数量: ${products.length}`)
    products.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.title} - ${product.status} - $${product.price}`)
    })

    // 3. 检查用户的需求
    console.log('\n3. 检查用户需求...')
    const demands = await prisma.demand.findMany({
      where: { userId: testUser.id },
      select: {
        id: true,
        title: true,
        status: true,
        budget: true,
        demandType: true,
        createdAt: true,
        expirationTime: true,
        _count: {
          select: {
            offers: true
          }
        }
      },
      take: 5
    })

    console.log(`✅ 用户需求数量: ${demands.length}`)
    demands.forEach((demand, index) => {
      console.log(`   ${index + 1}. ${demand.title} - ${demand.status} - $${demand.budget} - ${demand._count.offers}个报价`)
    })

    // 4. 如果需求数量少，创建一些测试需求
    if (demands.length < 3) {
      console.log('\n4. 创建测试需求...')
      
      const testDemands = [
        {
          title: '寻找二手iPhone 14',
          description: '需要一台成色较好的iPhone 14，颜色不限，要求无维修记录，电池健康度80%以上。',
          demandType: 'buy_goods',
          subcategory: '手机数码',
          budget: 4500,
          deliveryMethod: 'delivery',
          userId: testUser.id,
          expirationTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
        },
        {
          title: '需要网站设计服务',
          description: '为我的小企业设计一个简洁的企业官网，包含首页、产品展示、关于我们、联系我们等页面。',
          demandType: 'hire_service',
          subcategory: '网站开发',
          budget: 2000,
          deliveryMethod: 'online',
          userId: testUser.id,
          expirationTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // 14天后过期
        },
        {
          title: '求助：如何学习React开发',
          description: '刚开始学习前端开发，想了解React的学习路径和推荐资源，有经验的朋友请指教。',
          demandType: 'request_info',
          subcategory: '技术咨询',
          budget: 100,
          deliveryMethod: 'online',
          userId: testUser.id,
          expirationTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3天后过期
        }
      ]

      for (const demandData of testDemands) {
        try {
          const newDemand = await prisma.demand.create({
            data: demandData,
            select: {
              id: true,
              title: true,
              demandType: true,
              budget: true
            }
          })
          console.log(`   ✅ 创建需求: ${newDemand.title} - ${newDemand.demandType} - $${newDemand.budget}`)
        } catch (error) {
          console.log(`   ❌ 创建需求失败: ${demandData.title}`)
        }
      }
    }

    // 5. 测试API接口
    console.log('\n5. 测试用户需求API接口...')
    try {
      const fetch = require('node-fetch')
      const response = await fetch(`http://localhost:3000/api/users/${testUser.id}/demands?page=1&limit=10`)
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ API接口正常，返回 ${data.demands?.length || 0} 个需求`)
        console.log(`   分页信息: 第${data.pagination?.page}页，共${data.pagination?.total}条`)
      } else {
        console.log(`❌ API接口错误: ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ API接口测试失败: ${error.message}`)
    }

    // 6. 统计信息
    console.log('\n6. 统计信息...')
    const stats = await Promise.all([
      prisma.product.count({ where: { sellerId: testUser.id } }),
      prisma.demand.count({ where: { userId: testUser.id } }),
      prisma.demand.count({ where: { userId: testUser.id, status: 'OPEN' } }),
      prisma.demand.count({ where: { userId: testUser.id, status: 'CLOSED' } }),
      prisma.demandOffer.count({ 
        where: { 
          demand: { userId: testUser.id } 
        } 
      })
    ])

    console.log(`✅ 用户统计:`)
    console.log(`   - 商品总数: ${stats[0]}`)
    console.log(`   - 需求总数: ${stats[1]}`)
    console.log(`   - 开放需求: ${stats[2]}`)
    console.log(`   - 已关闭需求: ${stats[3]}`)
    console.log(`   - 收到报价: ${stats[4]}`)

    console.log('\n🎉 测试完成！')
    console.log('\n📝 访问信息:')
    console.log(`   用户商品和需求页面: http://localhost:3000/products/user/${testUser.id}`)
    console.log(`   需求列表页面: http://localhost:3000/demands`)
    console.log(`   创建需求页面: http://localhost:3000/demands/create`)

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testUserProductsAndDemands().catch(console.error)
}

module.exports = { testUserProductsAndDemands }
