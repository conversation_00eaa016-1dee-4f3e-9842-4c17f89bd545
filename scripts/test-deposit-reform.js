/**
 * 保证金改革方案测试脚本
 * 测试新的保证金页面和改革功能
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testReformFeatures() {
  console.log('🚀 测试保证金改革方案...')
  console.log('='.repeat(50))

  try {
    // 1. 测试用户数据
    console.log('\n1️⃣ 检查用户数据...')
    const users = await prisma.user.findMany({
      select: {
        email: true,
        name: true,
        depositBalance: true,
        creditScore: true,
        isGuarantor: true
      }
    })

    users.forEach(user => {
      const trustLevel = Math.min(5, Math.floor(user.creditScore / 20))
      const stars = '⭐'.repeat(trustLevel) + '☆'.repeat(5 - trustLevel)
      
      console.log(`👤 ${user.name} (${user.email}):`)
      console.log(`   保证金: ${user.depositBalance.toFixed(2)} USDT`)
      console.log(`   信用分: ${user.creditScore}`)
      console.log(`   信誉等级: ${stars} (${trustLevel}/5)`)
      console.log(`   中间人: ${user.isGuarantor ? '是' : '否'}`)
      console.log('')
    })

    // 2. 测试阶梯手续费计算
    console.log('\n2️⃣ 测试阶梯手续费计算...')
    const testAmounts = [50, 150, 800, 2500]
    
    testAmounts.forEach(amount => {
      const fee = calculateWithdrawalFee(amount)
      console.log(`💰 提现 ${amount} USDT:`)
      console.log(`   手续费: ${fee.fee.toFixed(2)} USDT (${fee.rate})`)
      console.log(`   实际到账: ${(amount - fee.fee).toFixed(2)} USDT`)
      console.log(`   说明: ${fee.description}`)
      console.log('')
    })

    // 3. 测试数据库表
    console.log('\n3️⃣ 检查数据库表...')
    
    const tableChecks = [
      { name: 'User', model: prisma.user },
      { name: 'DepositRecord', model: prisma.depositRecord },
      { name: 'FundTransaction', model: prisma.fundTransaction },
      { name: 'GuarantorApplication', model: prisma.guarantorApplication },
      { name: 'Withdrawal', model: prisma.withdrawal }
    ]

    for (const table of tableChecks) {
      try {
        const count = await table.model.count()
        console.log(`✅ ${table.name}表: ${count} 条记录`)
      } catch (error) {
        console.log(`❌ ${table.name}表: 检查失败 - ${error.message}`)
      }
    }

    // 4. 创建测试充值记录
    console.log('\n4️⃣ 创建测试充值记录...')
    
    const user1 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (user1) {
      const testDeposits = [
        { amount: 100, method: 'usdt', status: 'COMPLETED' },
        { amount: 0.5, originalAmount: 0.5, method: 'bnb', status: 'PENDING' },
        { amount: 200, method: 'binance', status: 'COMPLETED' }
      ]

      for (const deposit of testDeposits) {
        await prisma.depositRecord.create({
          data: {
            userId: user1.id,
            amount: deposit.amount,
            originalAmount: deposit.originalAmount || deposit.amount,
            method: deposit.method,
            status: deposit.status,
            notes: `测试${deposit.method}充值`,
            metadata: {
              test: true,
              createdBy: 'test-script'
            }
          }
        })
      }
      
      console.log(`✅ 为用户 ${user1.name} 创建了 ${testDeposits.length} 条测试充值记录`)
    }

    // 5. 创建测试资金交易记录
    console.log('\n5️⃣ 创建测试资金交易记录...')
    
    if (user1) {
      const testTransactions = [
        { type: 'DEPOSIT', amount: 100, description: 'USDT充值' },
        { type: 'FREEZE', amount: -50, description: '商品购买担保' },
        { type: 'COMMISSION', amount: 5, description: '中间人佣金' },
        { type: 'UNFREEZE', amount: 50, description: '交易完成解冻' }
      ]

      for (const transaction of testTransactions) {
        await prisma.fundTransaction.create({
          data: {
            userId: user1.id,
            type: transaction.type,
            amount: transaction.amount,
            description: transaction.description,
            metadata: {
              test: true,
              createdBy: 'test-script'
            }
          }
        })
      }
      
      console.log(`✅ 为用户 ${user1.name} 创建了 ${testTransactions.length} 条测试交易记录`)
    }

    console.log('\n🎉 改革方案测试数据创建完成!')

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

// 阶梯手续费计算函数
function calculateWithdrawalFee(amount) {
  if (amount <= 100) {
    return { fee: 0.5, rate: 'fixed', description: '固定手续费' }
  } else if (amount <= 500) {
    return { fee: amount * 0.005, rate: '0.5%', description: '按比例收费' }
  } else if (amount <= 2000) {
    return { fee: amount * 0.003, rate: '0.3%', description: '按比例收费' }
  } else {
    return { fee: 0, rate: '0%', description: '免手续费' }
  }
}

async function showReformSummary() {
  console.log('\n📊 改革方案总结:')
  console.log('='.repeat(50))
  
  console.log('\n🆓 零交易手续费:')
  console.log('   • 所有商品交易免手续费')
  console.log('   • 担保服务免手续费')
  console.log('   • 提升用户交易体验')
  
  console.log('\n🏦 担保池机制:')
  console.log('   • 担保金进入记账池，不立即扣除')
  console.log('   • 提现时统一结算')
  console.log('   • 提升资金使用效率')
  
  console.log('\n📊 阶梯提现手续费:')
  console.log('   • ≤100 USDT: 固定0.5 USDT')
  console.log('   • 101-500 USDT: 0.5%手续费')
  console.log('   • 501-2000 USDT: 0.3%手续费')
  console.log('   • >2000 USDT: 免手续费')
  
  console.log('\n⭐ 信誉体系:')
  console.log('   • 可视化信誉等级展示')
  console.log('   • 担保次数和履约率统计')
  console.log('   • 信任分数综合评估')
  
  console.log('\n💰 多种支付方式:')
  console.log('   • USDT直接转账')
  console.log('   • 币安支付 (Binance Pay)')
  console.log('   • BNB支付')
  
  console.log('\n🛡️ 中间人机制:')
  console.log('   • 信用分80+，保证金1000+可申请')
  console.log('   • 0.5%-2%佣金比例')
  console.log('   • 完善的申请和审核流程')
}

async function testPageFeatures() {
  console.log('\n🧪 页面功能测试清单:')
  console.log('='.repeat(50))
  
  console.log('\n📋 测试步骤:')
  console.log('1. 启动应用: npm run dev')
  console.log('2. 访问页面: http://localhost:3000/deposit')
  console.log('3. 登录测试账户:')
  console.log('   • <EMAIL> / 123456')
  console.log('   • <EMAIL> / 123456')
  console.log('   • <EMAIL> / 123456')
  
  console.log('\n✅ 功能验证:')
  console.log('   • 概览页面显示新的余额结构')
  console.log('   • 信誉等级星级显示')
  console.log('   • 担保池和待提现金额')
  console.log('   • 多种充值方式选择')
  console.log('   • 阶梯手续费计算')
  console.log('   • 中间人申请功能')
  
  console.log('\n🔗 API测试:')
  console.log('   • GET /api/funds/balance - 新的余额结构')
  console.log('   • POST /api/funds/deposit - 多种支付方式')
  console.log('   • GET /api/funds/deposit - 充值记录')
  console.log('   • POST /api/guarantor/apply - 中间人申请')
}

async function main() {
  console.log('🚀 BitMarket 保证金改革方案测试')
  console.log('='.repeat(50))
  
  try {
    await testReformFeatures()
    showReformSummary()
    testPageFeatures()
    
    console.log('\n🎉 改革方案测试完成!')
    console.log('现在可以访问 http://localhost:3000/deposit 体验新功能')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  testReformFeatures,
  calculateWithdrawalFee
}
