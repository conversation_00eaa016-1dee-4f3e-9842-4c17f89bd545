const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUserProducts() {
  try {
    const userId = 'cmd8desog0002v9rwq6iekv1c';
    console.log('检查用户商品:', userId);
    
    const products = await prisma.product.findMany({
      where: { sellerId: userId },
      select: {
        id: true,
        title: true,
        stock: true,
        status: true,
        reviewStatus: true,
        createdAt: true
      }
    });
    
    console.log(`用户商品总数: ${products.length}`);
    console.log('用户商品列表:');
    products.forEach(p => {
      console.log(`- ${p.title} (ID: ${p.id})`);
      console.log(`  库存: ${p.stock}, 状态: ${p.status}, 审核: ${p.reviewStatus}`);
      console.log(`  创建时间: ${p.createdAt}`);
      console.log('');
    });
    
    // 按状态分组统计
    const statusCount = {};
    products.forEach(p => {
      statusCount[p.status] = (statusCount[p.status] || 0) + 1;
    });
    
    console.log('状态统计:');
    Object.entries(statusCount).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}个`);
    });
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('错误:', error);
    await prisma.$disconnect();
  }
}

checkUserProducts();
