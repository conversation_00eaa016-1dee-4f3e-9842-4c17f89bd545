const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugMediatorDeposit() {
  try {
    console.log('=== 调试中间人保证金问题 ===\n');
    
    // 1. 查询所有中间人
    console.log('1. 查询所有中间人:');
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorDeposit: true,
        depositBalance: true,
        frozenBalance: true,
        availableBalance: true
      }
    });
    
    console.log(`找到 ${mediators.length} 个中间人:`);
    mediators.forEach((m, index) => {
      console.log(`${index + 1}. ${m.name} (ID: ${m.id})`);
      console.log(`   状态: ${m.mediatorStatus}`);
      console.log(`   mediatorDeposit: ${m.mediatorDeposit} USDT`);
      console.log(`   depositBalance: ${m.depositBalance} USDT`);
      console.log(`   frozenBalance: ${m.frozenBalance} USDT`);
      console.log(`   availableBalance: ${m.availableBalance} USDT`);
      console.log('');
    });
    
    // 2. 检查活跃中间人
    console.log('2. 检查活跃中间人:');
    const activeMediators = mediators.filter(m => m.mediatorStatus === 'ACTIVE');
    console.log(`活跃中间人数量: ${activeMediators.length}`);
    
    if (activeMediators.length === 0) {
      console.log('❌ 没有活跃的中间人！这是问题的原因之一。');
    }
    
    // 3. 模拟 auto-assign API 的逻辑
    console.log('\n3. 模拟 auto-assign API 的保证金计算:');
    const testOrderAmount = 1000; // 测试订单金额
    
    for (const mediator of activeMediators) {
      console.log(`\n检查中间人: ${mediator.name}`);
      
      // 原始的错误逻辑：使用 fundTransaction 表
      console.log('  原始逻辑 (fundTransaction):');
      const guaranteeDeposits = await prisma.fundTransaction.aggregate({
        where: {
          userId: mediator.id,
          type: 'GUARANTEE_DEPOSIT'
        },
        _sum: { amount: true }
      });

      const guaranteeWithdrawals = await prisma.fundTransaction.aggregate({
        where: {
          userId: mediator.id,
          type: 'GUARANTEE_WITHDRAWAL'
        },
        _sum: { amount: true }
      });

      const totalFrozenFunds = Math.abs(guaranteeDeposits._sum.amount || 0) - Math.abs(guaranteeWithdrawals._sum.amount || 0);
      
      // 计算活跃订单锁定金额
      const activeOrders = await prisma.order.findMany({
        where: {
          mediatorId: mediator.id,
          status: {
            in: ['PENDING', 'CONFIRMED', 'SHIPPED', 'IN_PROGRESS', 'PENDING_PAYMENT']
          }
        },
        select: {
          escrowAmount: true
        }
      });

      const lockedAmount = activeOrders.reduce((sum, order) => sum + (order.escrowAmount || 0), 0);
      const availableAmountOld = Math.max(0, totalFrozenFunds - lockedAmount);
      
      console.log(`    GUARANTEE_DEPOSIT 总额: ${guaranteeDeposits._sum.amount || 0}`);
      console.log(`    GUARANTEE_WITHDRAWAL 总额: ${guaranteeWithdrawals._sum.amount || 0}`);
      console.log(`    计算的冻结资金: ${totalFrozenFunds}`);
      console.log(`    活跃订单锁定: ${lockedAmount}`);
      console.log(`    可用金额: ${availableAmountOld}`);
      console.log(`    是否足够 ${testOrderAmount} USDT: ${availableAmountOld >= testOrderAmount ? '✅' : '❌'}`);
      
      // 正确的逻辑：使用 User 表字段
      console.log('  正确逻辑 (User 表字段):');
      console.log(`    mediatorDeposit: ${mediator.mediatorDeposit}`);
      console.log(`    depositBalance: ${mediator.depositBalance}`);
      console.log(`    availableBalance: ${mediator.availableBalance}`);
      console.log(`    是否足够 ${testOrderAmount} USDT (mediatorDeposit): ${mediator.mediatorDeposit >= testOrderAmount ? '✅' : '❌'}`);
      console.log(`    是否足够 ${testOrderAmount} USDT (availableBalance): ${mediator.availableBalance >= testOrderAmount ? '✅' : '❌'}`);
    }
    
    // 4. 检查 GuaranteeTransaction 表
    console.log('\n4. 检查 GuaranteeTransaction 表:');
    const guaranteeTransactionCount = await prisma.guaranteeTransaction.count();
    console.log(`GuaranteeTransaction 记录数: ${guaranteeTransactionCount}`);
    
    if (guaranteeTransactionCount > 0) {
      const sampleTransactions = await prisma.guaranteeTransaction.findMany({
        take: 5,
        select: {
          userId: true,
          type: true,
          amount: true,
          user: {
            select: { name: true }
          }
        }
      });
      
      console.log('样本记录:');
      sampleTransactions.forEach(gt => {
        console.log(`  - ${gt.user.name}: ${gt.type} = ${gt.amount}`);
      });
    }
    
    // 5. 总结问题
    console.log('\n=== 问题总结 ===');
    if (activeMediators.length === 0) {
      console.log('❌ 主要问题：没有活跃状态的中间人');
    } else {
      console.log('✅ 有活跃的中间人');
      
      const hasDepositInMediatorField = activeMediators.some(m => m.mediatorDeposit > 0);
      const hasDepositInBalanceField = activeMediators.some(m => m.availableBalance > 0);
      
      if (!hasDepositInMediatorField && !hasDepositInBalanceField) {
        console.log('❌ 问题：中间人没有保证金（mediatorDeposit 和 availableBalance 都为0）');
      } else if (hasDepositInMediatorField) {
        console.log('✅ 中间人在 mediatorDeposit 字段有保证金');
      } else if (hasDepositInBalanceField) {
        console.log('✅ 中间人在 availableBalance 字段有保证金');
      }
      
      console.log('❌ API 问题：auto-assign API 使用了错误的保证金计算逻辑（fundTransaction 表）');
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMediatorDeposit();
