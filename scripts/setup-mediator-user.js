#!/usr/bin/env node

/**
 * 设置测试中间人用户
 * 确保有一个可以登录的中间人用户用于测试
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function setupMediatorUser() {
  console.log('🔧 设置测试中间人用户...\n')

  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功\n')

    // 1. 检查是否已有测试用户
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (testUser) {
      console.log('📝 更新现有测试用户为中间人...')
      
      // 更新为中间人
      testUser = await prisma.user.update({
        where: { id: testUser.id },
        data: {
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.05, // 5%
          mediatorDeposit: 10000,
          mediatorReputation: 4.8,
          bnbWalletVerified: true,
          mediatorExperience: '具有5年电商经验，专业可靠',
          mediatorIntroduction: '专业的中间人服务提供者，致力于为用户提供安全的交易保障',
          mediatorSuccessRate: 98.5,
          mediatorTotalOrders: 150,
          mediatorVerifiedAt: new Date()
        }
      })
    } else {
      console.log('🆕 创建新的测试中间人用户...')
      
      // 创建密码哈希
      const hashedPassword = await bcrypt.hash('123456', 12)
      
      // 创建新用户
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '测试中间人',
          password: hashedPassword,
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.05, // 5%
          mediatorDeposit: 10000,
          mediatorReputation: 4.8,
          bnbWalletVerified: true,
          mediatorExperience: '具有5年电商经验，专业可靠',
          mediatorIntroduction: '专业的中间人服务提供者，致力于为用户提供安全的交易保障',
          mediatorSuccessRate: 98.5,
          mediatorTotalOrders: 150,
          mediatorVerifiedAt: new Date(),
          creditScore: 85,
          depositBalance: 1000,
          status: 'ACTIVE'
        }
      })
    }

    console.log('✅ 中间人用户设置成功!')
    console.log(`   邮箱: ${testUser.email}`)
    console.log(`   密码: 123456`)
    console.log(`   ID: ${testUser.id}`)
    console.log(`   中间人状态: ${testUser.mediatorStatus}`)
    console.log(`   费率: ${(testUser.mediatorFeeRate * 100).toFixed(1)}%`)
    console.log(`   保证金: ${testUser.mediatorDeposit.toLocaleString()} USDT`)
    console.log()

    // 2. 创建一些测试数据
    console.log('📊 创建测试托管订单数据...')

    // 创建测试买家
    let buyer = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })
    if (!buyer) {
      const buyerPassword = await bcrypt.hash('123456', 12)
      buyer = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '测试买家',
          password: buyerPassword,
          creditScore: 70,
          status: 'ACTIVE'
        }
      })
    }

    // 创建测试卖家
    let seller = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })
    if (!seller) {
      const sellerPassword = await bcrypt.hash('123456', 12)
      seller = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '测试卖家',
          password: sellerPassword,
          creditScore: 75,
          status: 'ACTIVE'
        }
      })
    }

    // 创建测试商品
    let product = await prisma.product.findFirst({
      where: { sellerId: seller.id }
    })
    if (!product) {
      product = await prisma.product.create({
        data: {
          title: '测试商品 - iPhone 15 Pro',
          description: '全新未拆封的iPhone 15 Pro，用于测试托管服务',
          price: 8999,
          sellerId: seller.id,
          status: 'ACTIVE'
        }
      })
    }

    // 创建测试订单
    const existingOrder = await prisma.order.findFirst({
      where: { 
        buyerId: buyer.id,
        sellerId: seller.id,
        mediatorId: testUser.id
      }
    })

    if (!existingOrder) {
      const order = await prisma.order.create({
        data: {
          orderNumber: `TEST-${Date.now()}`,
          status: 'PAID',
          totalAmount: 8999,
          productPrice: 8999,
          useEscrow: true,
          escrowFee: 449.95, // 5% 费率
          escrowFeeRate: 0.05,
          productId: product.id,
          buyerId: buyer.id,
          sellerId: seller.id,
          mediatorId: testUser.id
        }
      })

      // 创建托管订单
      await prisma.escrowOrder.create({
        data: {
          orderId: order.id,
          mediatorId: testUser.id,
          buyerId: buyer.id,
          sellerId: seller.id,
          amount: 8999,
          mediatorFee: 449.95,
          platformFee: 134.99, // 30% 平台费
          status: 'FUNDED',
          mediatorWalletAddress: '******************************************'
        }
      })

      console.log('✅ 创建测试托管订单成功')
    } else {
      console.log('✅ 测试托管订单已存在')
    }

    // 3. 验证数据
    console.log('\n📋 验证中间人数据...')
    
    const mediatorStats = await Promise.all([
      prisma.escrowOrder.count({
        where: { mediatorId: testUser.id }
      }),
      prisma.escrowOrder.count({
        where: {
          mediatorId: testUser.id,
          status: { in: ['PENDING', 'FUNDED', 'SHIPPED'] }
        }
      }),
      prisma.escrowOrder.aggregate({
        where: { 
          mediatorId: testUser.id,
          status: 'COMPLETED'
        },
        _sum: { mediatorFee: true }
      })
    ])

    console.log(`   总托管订单: ${mediatorStats[0]}`)
    console.log(`   活跃订单: ${mediatorStats[1]}`)
    console.log(`   总收益: ${mediatorStats[2]._sum.mediatorFee || 0} USDT`)

    console.log('\n🎉 设置完成!')
    console.log('\n📝 登录信息:')
    console.log('   邮箱: <EMAIL>')
    console.log('   密码: 123456')
    console.log('\n🔗 测试步骤:')
    console.log('   1. 访问 http://localhost:3000/auth/signin')
    console.log('   2. 使用上述账号登录')
    console.log('   3. 访问 http://localhost:3000/profile')
    console.log('   4. 查看中间人控制器面板')

  } catch (error) {
    console.error('❌ 设置过程中出现错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行设置
setupMediatorUser().catch(console.error)
