const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAdminMediatorSetting() {
  console.log('🔧 测试管理员中间人设置功能...\n')

  try {
    // 1. 检查现有用户状态
    console.log('1. 检查现有用户状态...')
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        isGuarantor: true,
        isMediator: true,
        mediatorStatus: true,
        role: true
      },
      take: 10
    })

    console.log(`✅ 找到 ${users.length} 个用户`)
    users.forEach((user, index) => {
      console.log(`\n   ${index + 1}. ${user.name} (${user.email})`)
      console.log(`      角色: ${user.role}`)
      console.log(`      担保人: ${user.isGuarantor}`)
      console.log(`      中间人: ${user.isMediator}`)
      console.log(`      中间人状态: ${user.mediatorStatus}`)
    })

    // 2. 确保有测试用户
    console.log('\n2. 确保有测试用户...')
    
    let testUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })
    
    if (!testUser) {
      console.log('   创建测试用户...')
      testUser = await prisma.user.create({
        data: {
          name: '测试用户-管理员设置',
          email: '<EMAIL>',
          isGuarantor: false,
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          depositBalance: 5000,
          creditScore: 85,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testUser.name}`)
    } else {
      // 重置测试用户状态
      await prisma.user.update({
        where: { id: testUser.id },
        data: {
          isGuarantor: false,
          isMediator: false,
          mediatorStatus: 'INACTIVE'
        }
      })
      console.log(`   ✅ 重置测试用户: ${testUser.name}`)
    }

    // 3. 模拟管理员设置中间人
    console.log('\n3. 模拟管理员设置中间人...')
    
    console.log('   设置前状态:')
    console.log(`     isGuarantor: ${testUser.isGuarantor}`)
    console.log(`     isMediator: ${testUser.isMediator}`)
    console.log(`     mediatorStatus: ${testUser.mediatorStatus}`)
    
    // 模拟API调用设置为中间人
    const updatedUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        mediatorFeeRate: 0.025,
        mediatorReputation: 0,
        mediatorSuccessRate: 0,
        mediatorTotalOrders: 0,
        mediatorVerifiedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        isGuarantor: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true
      }
    })
    
    console.log('   设置后状态:')
    console.log(`     isGuarantor: ${updatedUser.isGuarantor}`)
    console.log(`     isMediator: ${updatedUser.isMediator}`)
    console.log(`     mediatorStatus: ${updatedUser.mediatorStatus}`)
    console.log(`     mediatorFeeRate: ${updatedUser.mediatorFeeRate}`)
    console.log(`     mediatorReputation: ${updatedUser.mediatorReputation}`)
    console.log(`     mediatorSuccessRate: ${updatedUser.mediatorSuccessRate}`)
    console.log(`     mediatorTotalOrders: ${updatedUser.mediatorTotalOrders}`)

    // 4. 验证个人资料页面应该显示中间人控制台
    console.log('\n4. 验证个人资料页面显示逻辑...')
    
    const shouldShowConsole = updatedUser.isMediator
    console.log(`   条件检查: profile?.isMediator = ${shouldShowConsole}`)
    console.log(`   应显示中间人控制台: ${shouldShowConsole ? '是' : '否'}`)
    
    if (shouldShowConsole) {
      console.log('   ✅ 中间人控制台应该显示')
      console.log('   状态标签应显示:', updatedUser.mediatorStatus === 'ACTIVE' ? '已认证' : '待认证')
      console.log('   统计信息:')
      console.log(`     - 调解订单: ${updatedUser.mediatorTotalOrders}`)
      console.log(`     - 成功率: ${updatedUser.mediatorSuccessRate}%`)
      console.log(`     - 信誉值: ${updatedUser.mediatorReputation}`)
    } else {
      console.log('   ❌ 中间人控制台不应显示')
    }

    // 5. 测试取消中间人
    console.log('\n5. 测试取消中间人...')
    
    const canceledUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        isMediator: false,
        mediatorStatus: 'INACTIVE'
      },
      select: {
        id: true,
        name: true,
        isMediator: true,
        mediatorStatus: true
      }
    })
    
    console.log('   取消后状态:')
    console.log(`     isMediator: ${canceledUser.isMediator}`)
    console.log(`     mediatorStatus: ${canceledUser.mediatorStatus}`)
    console.log(`     应显示中间人控制台: ${canceledUser.isMediator ? '是' : '否'}`)

    // 6. 检查管理员用户
    console.log('\n6. 检查管理员用户...')
    const adminUsers = await prisma.user.findMany({
      where: {
        role: 'ADMIN'
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    })

    console.log(`✅ 找到 ${adminUsers.length} 个管理员`)
    adminUsers.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.name} (${admin.email})`)
    })

    console.log('\n🎉 测试完成！')
    console.log('\n📝 手动测试步骤:')
    
    console.log('\n【步骤1: 管理员设置中间人】')
    console.log('1. 使用管理员账户登录')
    console.log('2. 访问用户管理页面: http://localhost:3000/admin/users')
    console.log('3. 找到测试用户，点击"设为中间人"按钮')
    console.log('4. 确认用户显示"中间人"标签')
    
    console.log('\n【步骤2: 验证个人资料页面】')
    console.log(`1. 使用测试用户登录: ${testUser.email}`)
    console.log('2. 访问个人资料页面: http://localhost:3000/profile')
    console.log('3. 查看是否显示中间人控制台卡片')
    console.log('4. 验证统计信息和状态标签')
    
    console.log('\n【步骤3: 测试取消中间人】')
    console.log('1. 管理员点击"取消中间人"按钮')
    console.log('2. 用户个人资料页面应不再显示中间人控制台')
    
    console.log('\n💡 关键区别:')
    console.log('   - isGuarantor: 担保人身份（旧功能）')
    console.log('   - isMediator: 中间人身份（新功能）')
    console.log('   - 个人资料页面检查的是 isMediator 字段')
    console.log('   - 管理员现在可以分别设置这两个身份')

    console.log('\n🔧 如果仍有问题:')
    console.log('   1. 检查管理员页面是否正确显示中间人标签')
    console.log('   2. 确认API调用成功更新了 isMediator 字段')
    console.log('   3. 清除浏览器缓存并刷新页面')
    console.log('   4. 检查浏览器控制台的错误信息')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testAdminMediatorSetting().catch(console.error)
}

module.exports = { testAdminMediatorSetting }
