const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testMediatorConsole() {
  console.log('🔍 测试中间人控制台功能...\n')

  try {
    // 1. 检查中间人用户
    console.log('1. 检查中间人用户...')
    const mediators = await prisma.user.findMany({
      where: {
        isMediator: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        depositBalance: true,
        mediatorVerifiedAt: true
      }
    })

    console.log(`✅ 找到 ${mediators.length} 个中间人用户`)
    
    if (mediators.length === 0) {
      console.log('❌ 没有中间人用户，请先运行 setup-mediator-user.js')
      return
    }

    mediators.forEach((mediator, index) => {
      console.log(`   ${index + 1}. ${mediator.name} (${mediator.email})`)
      console.log(`      状态: ${mediator.mediatorStatus}`)
      console.log(`      费率: ${(mediator.mediatorFeeRate * 100).toFixed(1)}%`)
      console.log(`      信誉: ${mediator.mediatorReputation}`)
      console.log(`      成功率: ${mediator.mediatorSuccessRate}%`)
      console.log(`      总订单: ${mediator.mediatorTotalOrders}`)
      console.log(`      保证金: ${mediator.depositBalance} USDT`)
      console.log(`      认证时间: ${mediator.mediatorVerifiedAt ? '已认证' : '未认证'}`)
    })

    // 2. 检查个人资料页面功能
    console.log('\n2. 检查个人资料页面功能...')
    console.log('✅ 个人资料页面应该显示的功能:')
    console.log('   - 中间人控制台卡片 (仅对中间人显示)')
    console.log('   - 橙色边框突出显示')
    console.log('   - 显示中间人状态标签')
    console.log('   - 显示统计信息: 调解订单、成功率、信誉值')
    console.log('   - 点击跳转到 /mediator/dashboard')

    // 3. 检查中间人控制台页面
    console.log('\n3. 检查中间人控制台页面...')
    console.log('✅ 中间人控制台应该包含:')
    console.log('   - 页面标题: "中间人控制台"')
    console.log('   - 状态标签显示')
    console.log('   - 统计卡片: 总订单、进行中、成功率、信誉值')
    console.log('   - 标签页导航: 概览、调解订单、设置')
    console.log('   - 最近调解订单列表')

    // 4. 测试API接口
    console.log('\n4. 测试相关API接口...')
    
    try {
      const fetch = require('node-fetch')
      
      // 测试用户资料API
      console.log('   测试用户资料API...')
      const profileResponse = await fetch('http://localhost:3000/api/user/profile', {
        headers: {
          'Cookie': 'next-auth.session-token=test' // 这里需要实际的session token
        }
      })
      
      if (profileResponse.status === 401) {
        console.log('   ⚠️  需要登录才能测试API')
      } else if (profileResponse.ok) {
        console.log('   ✅ 用户资料API正常')
      } else {
        console.log(`   ❌ 用户资料API错误: ${profileResponse.status}`)
      }

    } catch (error) {
      console.log(`   ⚠️  API测试跳过: ${error.message}`)
    }

    // 5. 功能验证清单
    console.log('\n5. 功能验证清单...')
    console.log('✅ 需要验证的功能:')
    console.log('   □ 中间人用户登录后，个人资料页面显示中间人控制台')
    console.log('   □ 非中间人用户不显示中间人控制台')
    console.log('   □ 中间人控制台卡片显示正确的统计信息')
    console.log('   □ 点击中间人控制台跳转到正确页面')
    console.log('   □ 中间人控制台页面正确显示用户信息')
    console.log('   □ 状态标签正确显示 (已认证/待认证)')

    // 6. 测试数据总结
    console.log('\n6. 测试数据总结...')
    const activeMediators = mediators.filter(m => m.mediatorStatus === 'ACTIVE')
    const pendingMediators = mediators.filter(m => m.mediatorStatus === 'PENDING')
    
    console.log(`✅ 测试数据统计:`)
    console.log(`   - 总中间人数: ${mediators.length}`)
    console.log(`   - 活跃中间人: ${activeMediators.length}`)
    console.log(`   - 待审核中间人: ${pendingMediators.length}`)
    console.log(`   - 平均信誉值: ${(mediators.reduce((sum, m) => sum + m.mediatorReputation, 0) / mediators.length).toFixed(1)}`)
    console.log(`   - 平均成功率: ${(mediators.reduce((sum, m) => sum + m.mediatorSuccessRate, 0) / mediators.length).toFixed(1)}%`)

    console.log('\n🎉 测试准备完成！')
    console.log('\n📝 手动测试步骤:')
    console.log('1. 使用中间人账户登录:')
    console.log('   - 访问: http://localhost:3000/auth/signin')
    console.log('   - 邮箱: <EMAIL>')
    console.log('   - 密码: 123456')
    
    console.log('\n2. 验证个人资料页面:')
    console.log('   - 访问: http://localhost:3000/profile')
    console.log('   - 查看是否显示中间人控制台卡片')
    console.log('   - 验证统计信息是否正确')
    
    console.log('\n3. 验证中间人控制台:')
    console.log('   - 点击中间人控制台卡片')
    console.log('   - 或直接访问: http://localhost:3000/mediator/dashboard')
    console.log('   - 验证页面功能是否正常')

    console.log('\n4. 验证权限控制:')
    console.log('   - 使用非中间人账户登录')
    console.log('   - 验证个人资料页面不显示中间人控制台')
    console.log('   - 直接访问中间人控制台应该显示注册表单')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
if (require.main === module) {
  testMediatorConsole().catch(console.error)
}

module.exports = { testMediatorConsole }
