const axios = require('axios');

async function testMediatorAutoAssign() {
  try {
    console.log('=== 测试中间人自动分配功能 ===\n');
    
    const baseURL = 'http://localhost:3000';
    
    // 测试不同的订单金额
    const testCases = [
      { orderAmount: 100, description: '小额订单' },
      { orderAmount: 1000, description: '中等订单' },
      { orderAmount: 5000, description: '大额订单' },
      { orderAmount: 10000, description: '超大额订单' }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n--- 测试 ${testCase.description} (${testCase.orderAmount} USDT) ---`);
      
      try {
        const response = await axios.post(`${baseURL}/api/mediator/auto-assign`, {
          orderAmount: testCase.orderAmount,
          orderId: `test-order-${Date.now()}`
        }, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });
        
        if (response.data.success) {
          console.log('✅ 自动分配成功');
          console.log(`   分配的中间人: ${response.data.data.mediator.name}`);
          console.log(`   中间人ID: ${response.data.data.mediator.id}`);
          console.log(`   可用金额: ${response.data.data.mediator.availableAmount} USDT`);
          console.log(`   活跃订单数: ${response.data.data.mediator.activeOrderCount}`);
          console.log(`   托管费用: ${response.data.data.escrowFee} USDT`);
          console.log(`   总费用: ${response.data.data.totalCost} USDT`);
          console.log(`   分配原因: ${response.data.data.assignmentReason}`);
        } else {
          console.log('❌ 自动分配失败');
          console.log(`   错误信息: ${response.data.error}`);
        }
        
      } catch (error) {
        if (error.response) {
          console.log('❌ API 请求失败');
          console.log(`   状态码: ${error.response.status}`);
          console.log(`   错误信息: ${error.response.data.error || error.response.data}`);
          
          if (error.response.data.suggestion) {
            console.log(`   建议: ${error.response.data.suggestion}`);
          }
        } else if (error.code === 'ECONNREFUSED') {
          console.log('❌ 无法连接到服务器，请确保应用正在运行');
          break;
        } else {
          console.log('❌ 请求错误:', error.message);
        }
      }
    }
    
    // 测试边界情况
    console.log('\n--- 测试边界情况 ---');
    
    // 测试金额过小
    console.log('\n测试金额过小 (50 USDT):');
    try {
      const response = await axios.post(`${baseURL}/api/mediator/auto-assign`, {
        orderAmount: 50,
        orderId: `test-order-small-${Date.now()}`
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('❌ 应该失败但成功了');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 正确拒绝了过小的订单金额');
        console.log(`   错误信息: ${error.response.data.error}`);
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }
    
    // 测试缺少参数
    console.log('\n测试缺少参数:');
    try {
      const response = await axios.post(`${baseURL}/api/mediator/auto-assign`, {
        orderId: `test-order-no-amount-${Date.now()}`
        // 缺少 orderAmount
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('❌ 应该失败但成功了');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 正确拒绝了缺少参数的请求');
        console.log(`   错误信息: ${error.response.data.error}`);
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testMediatorAutoAssign();
}

module.exports = { testMediatorAutoAssign };
