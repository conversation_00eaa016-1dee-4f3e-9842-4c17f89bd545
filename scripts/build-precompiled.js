#!/usr/bin/env node

// BitMarket 预编译构建脚本
const { spawn } = require('child_process')
const fs = require('fs')

async function buildWithPrecompilation() {
  console.log('🚀 开始预编译构建')
  console.log('='.repeat(50))
  
  try {
    // 1. 清理构建缓存
    console.log('🧹 清理构建缓存...')
    if (fs.existsSync('.next')) {
      fs.rmSync('.next', { recursive: true, force: true })
      console.log('  ✅ .next 目录已清理')
    }
    
    // 2. 运行构建
    console.log('\n📦 开始Next.js构建...')
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'inherit',
      shell: process.platform === 'win32'
    })
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ 预编译构建完成')
        console.log('\n📊 构建结果:')
        console.log('  🟢 静态页面: 已预编译')
        console.log('  🟡 ISR页面: 已配置增量更新')
        console.log('  🔴 动态页面: 保持服务端渲染')
        console.log('\n🚀 启动生产服务器: npm start')
      } else {
        console.error('\n❌ 构建失败，退出码:', code)
        process.exit(1)
      }
    })
    
    buildProcess.on('error', (error) => {
      console.error('\n❌ 构建过程出错:', error)
      process.exit(1)
    })
    
  } catch (error) {
    console.error('❌ 构建失败:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  buildWithPrecompilation()
}

module.exports = { buildWithPrecompilation }
