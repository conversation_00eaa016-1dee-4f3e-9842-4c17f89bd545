#!/usr/bin/env node

const express = require('express')
const { performance } = require('perf_hooks')
const { PrismaClient } = require('@prisma/client')
const path = require('path')
const fs = require('fs').promises

const app = express()
const prisma = new PrismaClient()
const PORT = 3001

// 性能监控数据存储
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      system: {
        startTime: Date.now(),
        uptime: 0,
        memory: {},
        cpu: {},
        requests: {
          total: 0,
          success: 0,
          error: 0,
          avgResponseTime: 0
        }
      },
      database: {
        connections: 0,
        queries: {
          total: 0,
          slow: 0,
          avgTime: 0
        },
        errors: 0
      },
      api: {
        endpoints: {},
        slowRequests: [],
        errorRequests: []
      },
      alerts: []
    }
    
    this.thresholds = {
      responseTime: 1000, // 1秒
      memoryUsage: 500, // 500MB
      dbQueryTime: 500, // 500ms
      errorRate: 0.05 // 5%
    }
    
    this.startMonitoring()
  }

  // 开始监控
  startMonitoring() {
    // 每5秒收集系统指标
    setInterval(() => {
      this.collectSystemMetrics()
    }, 5000)

    // 每30秒检查告警
    setInterval(() => {
      this.checkAlerts()
    }, 30000)

    // 每分钟清理旧数据
    setInterval(() => {
      this.cleanupOldData()
    }, 60000)

    console.log('🚀 性能监控系统已启动')
  }

  // 收集系统指标
  collectSystemMetrics() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    this.metrics.system.uptime = Date.now() - this.metrics.system.startTime
    this.metrics.system.memory = {
      rss: memUsage.rss / 1024 / 1024, // MB
      heapTotal: memUsage.heapTotal / 1024 / 1024,
      heapUsed: memUsage.heapUsed / 1024 / 1024,
      external: memUsage.external / 1024 / 1024,
      timestamp: Date.now()
    }
    
    this.metrics.system.cpu = {
      user: cpuUsage.user,
      system: cpuUsage.system,
      timestamp: Date.now()
    }
  }

  // 记录API请求
  recordRequest(endpoint, method, responseTime, statusCode) {
    const key = `${method} ${endpoint}`
    
    if (!this.metrics.api.endpoints[key]) {
      this.metrics.api.endpoints[key] = {
        count: 0,
        totalTime: 0,
        avgTime: 0,
        errors: 0,
        lastAccess: Date.now()
      }
    }
    
    const endpointMetrics = this.metrics.api.endpoints[key]
    endpointMetrics.count++
    endpointMetrics.totalTime += responseTime
    endpointMetrics.avgTime = endpointMetrics.totalTime / endpointMetrics.count
    endpointMetrics.lastAccess = Date.now()
    
    // 更新全局请求统计
    this.metrics.system.requests.total++
    
    if (statusCode >= 200 && statusCode < 400) {
      this.metrics.system.requests.success++
    } else {
      this.metrics.system.requests.error++
      endpointMetrics.errors++
      
      // 记录错误请求
      this.metrics.api.errorRequests.push({
        endpoint: key,
        statusCode,
        responseTime,
        timestamp: Date.now()
      })
    }
    
    // 记录慢请求
    if (responseTime > this.thresholds.responseTime) {
      this.metrics.api.slowRequests.push({
        endpoint: key,
        responseTime,
        timestamp: Date.now()
      })
    }
    
    // 更新平均响应时间
    const totalRequests = this.metrics.system.requests.total
    const currentAvg = this.metrics.system.requests.avgResponseTime
    this.metrics.system.requests.avgResponseTime = 
      (currentAvg * (totalRequests - 1) + responseTime) / totalRequests
  }

  // 记录数据库查询
  recordDbQuery(queryTime, isError = false) {
    this.metrics.database.queries.total++
    
    if (isError) {
      this.metrics.database.errors++
    }
    
    if (queryTime > this.thresholds.dbQueryTime) {
      this.metrics.database.queries.slow++
    }
    
    // 更新平均查询时间
    const total = this.metrics.database.queries.total
    const currentAvg = this.metrics.database.queries.avgTime
    this.metrics.database.queries.avgTime = 
      (currentAvg * (total - 1) + queryTime) / total
  }

  // 检查告警
  checkAlerts() {
    const alerts = []
    
    // 内存使用告警
    const memUsage = this.metrics.system.memory.rss
    if (memUsage > this.thresholds.memoryUsage) {
      alerts.push({
        type: 'memory',
        level: 'warning',
        message: `内存使用过高: ${memUsage.toFixed(1)}MB`,
        timestamp: Date.now()
      })
    }
    
    // 响应时间告警
    const avgResponseTime = this.metrics.system.requests.avgResponseTime
    if (avgResponseTime > this.thresholds.responseTime) {
      alerts.push({
        type: 'response_time',
        level: 'warning',
        message: `平均响应时间过长: ${avgResponseTime.toFixed(0)}ms`,
        timestamp: Date.now()
      })
    }
    
    // 错误率告警
    const errorRate = this.metrics.system.requests.error / this.metrics.system.requests.total
    if (errorRate > this.thresholds.errorRate) {
      alerts.push({
        type: 'error_rate',
        level: 'critical',
        message: `错误率过高: ${(errorRate * 100).toFixed(1)}%`,
        timestamp: Date.now()
      })
    }
    
    // 数据库查询告警
    const dbAvgTime = this.metrics.database.queries.avgTime
    if (dbAvgTime > this.thresholds.dbQueryTime) {
      alerts.push({
        type: 'database',
        level: 'warning',
        message: `数据库查询时间过长: ${dbAvgTime.toFixed(0)}ms`,
        timestamp: Date.now()
      })
    }
    
    // 添加新告警
    alerts.forEach(alert => {
      this.metrics.alerts.unshift(alert)
      console.log(`🚨 ${alert.level.toUpperCase()}: ${alert.message}`)
    })
  }

  // 清理旧数据
  cleanupOldData() {
    const now = Date.now()
    const oneHour = 60 * 60 * 1000
    
    // 清理旧的慢请求记录
    this.metrics.api.slowRequests = this.metrics.api.slowRequests.filter(
      req => now - req.timestamp < oneHour
    )
    
    // 清理旧的错误请求记录
    this.metrics.api.errorRequests = this.metrics.api.errorRequests.filter(
      req => now - req.timestamp < oneHour
    )
    
    // 清理旧告警（保留最近100条）
    if (this.metrics.alerts.length > 100) {
      this.metrics.alerts = this.metrics.alerts.slice(0, 100)
    }
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      status: this.getSystemStatus()
    }
  }

  // 获取系统状态
  getSystemStatus() {
    const memUsage = this.metrics.system.memory.rss || 0
    const avgResponseTime = this.metrics.system.requests.avgResponseTime || 0
    const errorRate = this.metrics.system.requests.total > 0 
      ? this.metrics.system.requests.error / this.metrics.system.requests.total 
      : 0
    
    if (memUsage > this.thresholds.memoryUsage * 1.5 || 
        avgResponseTime > this.thresholds.responseTime * 2 ||
        errorRate > this.thresholds.errorRate * 2) {
      return 'critical'
    } else if (memUsage > this.thresholds.memoryUsage || 
               avgResponseTime > this.thresholds.responseTime ||
               errorRate > this.thresholds.errorRate) {
      return 'warning'
    } else {
      return 'healthy'
    }
  }
}

// 创建监控实例
const monitor = new PerformanceMonitor()

// 中间件：记录请求性能
app.use((req, res, next) => {
  const startTime = performance.now()
  
  res.on('finish', () => {
    const endTime = performance.now()
    const responseTime = endTime - startTime
    monitor.recordRequest(req.path, req.method, responseTime, res.statusCode)
  })
  
  next()
})

// 静态文件服务
app.use(express.static(path.join(__dirname, '../public')))

// API路由
app.get('/api/metrics', (req, res) => {
  res.json(monitor.getPerformanceReport())
})

app.get('/api/health', (req, res) => {
  const status = monitor.getSystemStatus()
  res.json({
    status,
    timestamp: Date.now(),
    uptime: Date.now() - monitor.metrics.system.startTime
  })
})

// 性能监控仪表板页面
app.get('/', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 性能监控仪表板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .header { background: #2563eb; color: white; padding: 1rem; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; }
        .card { background: white; border-radius: 8px; padding: 1.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card h3 { color: #1f2937; margin-bottom: 1rem; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 0.5rem; }
        .metric-value { font-weight: bold; }
        .status-healthy { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-critical { color: #ef4444; }
        .chart { height: 200px; background: #f9fafb; border-radius: 4px; margin-top: 1rem; }
        .alert { padding: 0.75rem; margin-bottom: 0.5rem; border-radius: 4px; }
        .alert-warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
        .alert-critical { background: #fee2e2; border-left: 4px solid #ef4444; }
        .refresh-btn { background: #2563eb; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 BitMarket 性能监控仪表板</h1>
        <p>实时监控系统性能和健康状态</p>
    </div>
    
    <div class="container">
        <div style="text-align: center; margin-bottom: 2rem;">
            <button class="refresh-btn" onclick="loadMetrics()">🔄 刷新数据</button>
            <span id="last-update" style="margin-left: 1rem; color: #6b7280;"></span>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🏥 系统健康状态</h3>
                <div id="system-status"></div>
            </div>
            
            <div class="card">
                <h3>💾 内存使用情况</h3>
                <div id="memory-metrics"></div>
            </div>
            
            <div class="card">
                <h3>🌐 API请求统计</h3>
                <div id="request-metrics"></div>
            </div>
            
            <div class="card">
                <h3>🗄️ 数据库性能</h3>
                <div id="database-metrics"></div>
            </div>
            
            <div class="card">
                <h3>🚨 系统告警</h3>
                <div id="alerts"></div>
            </div>
            
            <div class="card">
                <h3>🐌 慢请求监控</h3>
                <div id="slow-requests"></div>
            </div>
        </div>
    </div>

    <script>
        function loadMetrics() {
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    updateDashboard(data);
                    document.getElementById('last-update').textContent = 
                        '最后更新: ' + new Date().toLocaleTimeString();
                })
                .catch(error => {
                    console.error('加载指标失败:', error);
                });
        }

        function updateDashboard(data) {
            // 系统状态
            const statusElement = document.getElementById('system-status');
            const statusClass = 'status-' + data.status;
            const statusText = {
                'healthy': '健康 ✅',
                'warning': '警告 ⚠️',
                'critical': '严重 🚨'
            }[data.status];
            
            statusElement.innerHTML = \`
                <div class="metric">
                    <span>状态:</span>
                    <span class="metric-value \${statusClass}">\${statusText}</span>
                </div>
                <div class="metric">
                    <span>运行时间:</span>
                    <span class="metric-value">\${formatUptime(data.system.uptime)}</span>
                </div>
            \`;

            // 内存指标
            const memoryElement = document.getElementById('memory-metrics');
            const memory = data.system.memory;
            memoryElement.innerHTML = \`
                <div class="metric">
                    <span>RSS内存:</span>
                    <span class="metric-value">\${memory.rss?.toFixed(1) || 0}MB</span>
                </div>
                <div class="metric">
                    <span>堆内存:</span>
                    <span class="metric-value">\${memory.heapUsed?.toFixed(1) || 0}MB / \${memory.heapTotal?.toFixed(1) || 0}MB</span>
                </div>
                <div class="metric">
                    <span>外部内存:</span>
                    <span class="metric-value">\${memory.external?.toFixed(1) || 0}MB</span>
                </div>
            \`;

            // 请求指标
            const requestElement = document.getElementById('request-metrics');
            const requests = data.system.requests;
            const errorRate = requests.total > 0 ? (requests.error / requests.total * 100).toFixed(1) : 0;
            requestElement.innerHTML = \`
                <div class="metric">
                    <span>总请求数:</span>
                    <span class="metric-value">\${requests.total}</span>
                </div>
                <div class="metric">
                    <span>成功/失败:</span>
                    <span class="metric-value">\${requests.success} / \${requests.error}</span>
                </div>
                <div class="metric">
                    <span>错误率:</span>
                    <span class="metric-value">\${errorRate}%</span>
                </div>
                <div class="metric">
                    <span>平均响应时间:</span>
                    <span class="metric-value">\${requests.avgResponseTime.toFixed(0)}ms</span>
                </div>
            \`;

            // 数据库指标
            const dbElement = document.getElementById('database-metrics');
            const db = data.database;
            dbElement.innerHTML = \`
                <div class="metric">
                    <span>查询总数:</span>
                    <span class="metric-value">\${db.queries.total}</span>
                </div>
                <div class="metric">
                    <span>慢查询:</span>
                    <span class="metric-value">\${db.queries.slow}</span>
                </div>
                <div class="metric">
                    <span>平均查询时间:</span>
                    <span class="metric-value">\${db.queries.avgTime.toFixed(0)}ms</span>
                </div>
                <div class="metric">
                    <span>数据库错误:</span>
                    <span class="metric-value">\${db.errors}</span>
                </div>
            \`;

            // 告警
            const alertsElement = document.getElementById('alerts');
            if (data.alerts.length > 0) {
                alertsElement.innerHTML = data.alerts.slice(0, 5).map(alert => \`
                    <div class="alert alert-\${alert.level}">
                        <strong>\${alert.type.toUpperCase()}:</strong> \${alert.message}
                        <br><small>\${new Date(alert.timestamp).toLocaleString()}</small>
                    </div>
                \`).join('');
            } else {
                alertsElement.innerHTML = '<p style="color: #10b981;">✅ 无活跃告警</p>';
            }

            // 慢请求
            const slowElement = document.getElementById('slow-requests');
            if (data.api.slowRequests.length > 0) {
                slowElement.innerHTML = data.api.slowRequests.slice(0, 5).map(req => \`
                    <div class="metric">
                        <span>\${req.endpoint}:</span>
                        <span class="metric-value">\${req.responseTime.toFixed(0)}ms</span>
                    </div>
                \`).join('');
            } else {
                slowElement.innerHTML = '<p style="color: #10b981;">✅ 无慢请求</p>';
            }
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) return \`\${days}天 \${hours % 24}小时\`;
            if (hours > 0) return \`\${hours}小时 \${minutes % 60}分钟\`;
            if (minutes > 0) return \`\${minutes}分钟 \${seconds % 60}秒\`;
            return \`\${seconds}秒\`;
        }

        // 初始加载和自动刷新
        loadMetrics();
        setInterval(loadMetrics, 10000); // 每10秒刷新
    </script>
</body>
</html>
  `)
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 性能监控仪表板已启动: http://localhost:${PORT}`)
  console.log('📊 实时监控系统性能和健康状态')
  console.log('🔍 监控指标包括: 内存、CPU、API响应时间、数据库性能')
  console.log('🚨 自动告警: 内存过高、响应时间过长、错误率过高')
})

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭性能监控系统...')
  await prisma.$disconnect()
  process.exit(0)
})

module.exports = { PerformanceMonitor }
