#!/usr/bin/env node

const http = require('http')

async function testPerformanceMonitor() {
  console.log('🧪 测试性能监控器')
  console.log('='.repeat(50))
  
  const baseUrl = 'http://localhost:3001'
  
  // 测试健康检查端点
  console.log('\n📊 测试健康检查端点...')
  try {
    const healthResponse = await fetch(`${baseUrl}/api/health`)
    const healthData = await healthResponse.json()
    
    console.log('✅ 健康检查响应:')
    console.log(`   状态: ${healthData.status}`)
    console.log(`   运行时间: ${Math.round(healthData.uptime / 1000)}秒`)
    console.log(`   时间戳: ${new Date(healthData.timestamp).toLocaleString()}`)
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message)
  }
  
  // 测试指标端点
  console.log('\n📈 测试指标端点...')
  try {
    const metricsResponse = await fetch(`${baseUrl}/api/metrics`)
    const metricsData = await metricsResponse.json()
    
    console.log('✅ 指标数据响应:')
    console.log(`   系统状态: ${metricsData.status}`)
    console.log(`   内存使用: ${metricsData.metrics.memory.rss}MB RSS, ${metricsData.metrics.memory.heapUsed}MB 堆`)
    console.log(`   请求统计: ${metricsData.metrics.requests.total} 总计, ${metricsData.metrics.requests.success} 成功, ${metricsData.metrics.requests.error} 失败`)
    console.log(`   平均响应时间: ${metricsData.metrics.requests.avgTime}ms`)
    console.log(`   告警数量: ${metricsData.metrics.alerts.length}`)
    console.log(`   历史数据点: ${metricsData.history.length}`)
  } catch (error) {
    console.log('❌ 指标获取失败:', error.message)
  }
  
  // 测试仪表板页面
  console.log('\n🖥️  测试仪表板页面...')
  try {
    const dashboardResponse = await fetch(baseUrl)
    const dashboardHtml = await dashboardResponse.text()
    
    if (dashboardHtml.includes('BitMarket 性能监控仪表板')) {
      console.log('✅ 仪表板页面加载成功')
      console.log(`   页面大小: ${Math.round(dashboardHtml.length / 1024)}KB`)
      console.log(`   包含监控组件: ${dashboardHtml.includes('系统状态') ? '✅' : '❌'}`)
    } else {
      console.log('❌ 仪表板页面内容异常')
    }
  } catch (error) {
    console.log('❌ 仪表板加载失败:', error.message)
  }
  
  // 生成一些测试请求来触发监控
  console.log('\n🔄 生成测试请求...')
  const testRequests = []
  
  for (let i = 0; i < 10; i++) {
    testRequests.push(
      fetch(`${baseUrl}/api/health`).catch(err => ({ error: err.message }))
    )
  }
  
  const results = await Promise.allSettled(testRequests)
  const successful = results.filter(r => r.status === 'fulfilled').length
  const failed = results.filter(r => r.status === 'rejected').length
  
  console.log(`✅ 测试请求完成: ${successful} 成功, ${failed} 失败`)
  
  // 等待一下让监控器处理请求
  console.log('\n⏳ 等待监控器处理请求...')
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // 再次检查指标
  console.log('\n📊 检查更新后的指标...')
  try {
    const updatedMetricsResponse = await fetch(`${baseUrl}/api/metrics`)
    const updatedMetricsData = await updatedMetricsResponse.json()
    
    console.log('✅ 更新后的指标:')
    console.log(`   总请求数: ${updatedMetricsData.metrics.requests.total}`)
    console.log(`   成功请求: ${updatedMetricsData.metrics.requests.success}`)
    console.log(`   失败请求: ${updatedMetricsData.metrics.requests.error}`)
    console.log(`   平均响应时间: ${updatedMetricsData.metrics.requests.avgTime}ms`)
  } catch (error) {
    console.log('❌ 更新指标获取失败:', error.message)
  }
  
  console.log('\n🎯 性能监控器测试完成')
  console.log('📊 访问仪表板: http://localhost:3001')
  console.log('🔍 API端点:')
  console.log('   - 健康检查: http://localhost:3001/api/health')
  console.log('   - 指标数据: http://localhost:3001/api/metrics')
}

// 简单的fetch实现（如果没有node-fetch）
function fetch(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: 'GET'
    }
    
    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        })
      })
    })
    
    req.on('error', reject)
    req.end()
  })
}

// 运行测试
if (require.main === module) {
  testPerformanceMonitor()
    .then(() => {
      console.log('\n✅ 测试完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 测试失败:', error)
      process.exit(1)
    })
}

module.exports = { testPerformanceMonitor }
