#!/usr/bin/env node

/**
 * 停止占用指定端口的进程
 */

const { execSync } = require('child_process')
const os = require('os')

const port = process.argv[2] || '3000'

console.log(`🔍 检查端口 ${port} 的使用情况...`)

try {
  let command
  let killCommand
  
  if (os.platform() === 'win32') {
    // Windows命令
    command = `netstat -ano | findstr :${port}`
    
    try {
      const output = execSync(command, { encoding: 'utf8' })
      
      if (output.trim()) {
        console.log('📋 发现以下进程占用端口:')
        console.log(output)
        
        // 提取PID
        const lines = output.trim().split('\n')
        const pids = new Set()
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/)
          const pid = parts[parts.length - 1]
          if (pid && pid !== '0' && !isNaN(pid)) {
            pids.add(pid)
          }
        })
        
        if (pids.size > 0) {
          console.log(`\n🔪 停止进程 (PIDs: ${Array.from(pids).join(', ')})...`)
          
          pids.forEach(pid => {
            try {
              execSync(`taskkill /F /PID ${pid}`, { stdio: 'inherit' })
              console.log(`✅ 已停止进程 ${pid}`)
            } catch (error) {
              console.log(`⚠️  无法停止进程 ${pid}: ${error.message}`)
            }
          })
        }
      } else {
        console.log(`✅ 端口 ${port} 未被占用`)
      }
    } catch (error) {
      console.log(`✅ 端口 ${port} 未被占用`)
    }
  } else {
    // Unix/Linux/macOS命令
    command = `lsof -ti:${port}`
    
    try {
      const output = execSync(command, { encoding: 'utf8' })
      
      if (output.trim()) {
        const pids = output.trim().split('\n').filter(pid => pid)
        console.log(`📋 发现进程占用端口 ${port}: ${pids.join(', ')}`)
        
        pids.forEach(pid => {
          try {
            execSync(`kill -9 ${pid}`)
            console.log(`✅ 已停止进程 ${pid}`)
          } catch (error) {
            console.log(`⚠️  无法停止进程 ${pid}: ${error.message}`)
          }
        })
      } else {
        console.log(`✅ 端口 ${port} 未被占用`)
      }
    } catch (error) {
      console.log(`✅ 端口 ${port} 未被占用`)
    }
  }
  
  console.log(`\n🎉 端口 ${port} 现在可用！`)
  console.log(`💡 现在可以运行: npm run dev`)
  
} catch (error) {
  console.error('❌ 检查端口时出错:', error.message)
  process.exit(1)
}
