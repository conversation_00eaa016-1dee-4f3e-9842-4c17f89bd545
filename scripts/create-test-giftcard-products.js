const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestGiftCardProducts() {
  try {
    console.log('🔍 查找管理员用户...')
    
    // 查找管理员用户
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!adminUser) {
      console.log('❌ 未找到管理员用户，请先运行 create-fresh-test-data.js')
      return
    }
    
    console.log('✅ 找到管理员用户:', adminUser.email)
    
    console.log('🎁 创建测试礼品卡商品...')
    
    // 删除现有的礼品卡商品
    try {
      await prisma.giftCardProduct.deleteMany({})
      console.log('  ✓ 清理现有礼品卡商品')
    } catch (e) {
      console.log('  - 跳过清理礼品卡商品表')
    }
    
    // 创建测试礼品卡商品
    const testProducts = [
      {
        name: 'USDT充值卡 - 10元',
        description: '10 USDT充值卡，可用于平台充值或转赠他人',
        productType: 'RECHARGE_CARD',
        faceValue: 10.00,
        salePrice: 9.50,
        stock: 100,
        isActive: true,
        terms: '1. 有效期365天\n2. 可转赠他人使用\n3. 不可退款',
        validDays: 365,
        supportedPayments: ['BALANCE', 'BINANCE_PAY'],
        createdById: adminUser.id
      },
      {
        name: 'USDT充值卡 - 50元',
        description: '50 USDT充值卡，享受更多优惠',
        productType: 'RECHARGE_CARD',
        faceValue: 50.00,
        salePrice: 47.50,
        stock: 50,
        isActive: true,
        terms: '1. 有效期365天\n2. 可转赠他人使用\n3. 不可退款\n4. 享受5%折扣',
        validDays: 365,
        supportedPayments: ['BALANCE', 'BINANCE_PAY', 'CRYPTO'],
        createdById: adminUser.id
      },
      {
        name: 'USDT充值卡 - 100元',
        description: '100 USDT充值卡，大额充值首选',
        productType: 'RECHARGE_CARD',
        faceValue: 100.00,
        salePrice: 92.00,
        stock: 20,
        isActive: true,
        terms: '1. 有效期365天\n2. 可转赠他人使用\n3. 不可退款\n4. 享受8%折扣',
        validDays: 365,
        supportedPayments: ['BALANCE', 'BINANCE_PAY', 'CRYPTO'],
        createdById: adminUser.id
      },
      {
        name: '平台礼品卡 - 25元',
        description: '25 USDT平台礼品卡，送礼首选',
        productType: 'GIFT_CARD',
        faceValue: 25.00,
        salePrice: 24.00,
        stock: 30,
        isActive: true,
        terms: '1. 有效期180天\n2. 仅限平台内使用\n3. 可转赠他人\n4. 不可退款',
        validDays: 180,
        supportedPayments: ['BALANCE'],
        createdById: adminUser.id
      },
      {
        name: '限时特惠卡 - 200元',
        description: '200 USDT限时特惠卡，超值优惠',
        productType: 'GIFT_CARD',
        faceValue: 200.00,
        salePrice: 180.00,
        stock: 5,
        isActive: true,
        terms: '1. 有效期90天\n2. 限时特惠商品\n3. 享受10%折扣\n4. 不可退款',
        validDays: 90,
        supportedPayments: ['BALANCE', 'BINANCE_PAY'],
        createdById: adminUser.id
      },
      {
        name: '测试商品 - 已下架',
        description: '这是一个已下架的测试商品',
        productType: 'GIFT_CARD',
        faceValue: 5.00,
        salePrice: 5.00,
        stock: 0,
        isActive: false,
        terms: '测试商品，已下架',
        validDays: 30,
        supportedPayments: ['BALANCE'],
        createdById: adminUser.id
      }
    ]
    
    // 创建礼品卡商品
    for (const product of testProducts) {
      const giftCardProduct = await prisma.giftCardProduct.create({
        data: product
      })
      console.log(`✅ 创建礼品卡商品: ${product.name} (面值: ${product.faceValue} USDT, 售价: ${product.salePrice} USDT)`)
    }
    
    console.log('✅ 测试礼品卡商品创建完成!')
    console.log('\n📋 商品摘要:')
    console.log('- USDT充值卡 10元: 面值10, 售价9.5 (5%折扣)')
    console.log('- USDT充值卡 50元: 面值50, 售价47.5 (5%折扣)')
    console.log('- USDT充值卡 100元: 面值100, 售价92 (8%折扣)')
    console.log('- 平台礼品卡 25元: 面值25, 售价24 (4%折扣)')
    console.log('- 限时特惠卡 200元: 面值200, 售价180 (10%折扣)')
    console.log('- 测试商品: 已下架')
    
    console.log('\n🔗 测试链接:')
    console.log('1. 管理员登录: <EMAIL> / admin123')
    console.log('2. 访问充值页面: http://localhost:3001/deposit')
    console.log('3. 点击"礼品卡"标签页')
    console.log('4. 管理员可以看到商品管理功能')
    console.log('5. 用户可以选择发货方式购买礼品卡')
    
    console.log('\n💡 发货方式说明:')
    console.log('- 兑换码: 生成兑换码，可分享给他人')
    console.log('- 礼品卡: 生成实体礼品卡，可作为礼品')
    console.log('- 直充账号: 直接充值到购买者账户余额')
    
  } catch (error) {
    console.error('❌ 创建测试礼品卡商品失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
createTestGiftCardProducts()
