const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    console.log('👤 创建测试用户用于演示标记功能...')
    
    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (existingUser) {
      console.log('⚠️ 测试用户已存在，跳过创建')
      console.log(`用户ID: ${existingUser.id}`)
      console.log(`用户名: ${existingUser.name}`)
      console.log(`邮箱: ${existingUser.email}`)
      return
    }
    
    // 创建密码哈希
    const hashedPassword = await bcrypt.hash('123456', 12)
    
    // 创建测试用户
    const testUser = await prisma.user.create({
      data: {
        userId: 'user-risky-' + Math.random().toString(36).substring(2, 10),
        name: '可疑用户',
        email: '<EMAIL>',
        password: hashedPassword,
        emailVerified: new Date(),
        role: 'USER',
        status: 'ACTIVE',
        creditScore: 15, // 较低的信用分数
        riskLevel: 'NORMAL', // 初始为正常状态
        city: '北京',
        district: '朝阳区'
      }
    })
    
    console.log('✅ 测试用户创建成功！')
    console.log('=' .repeat(50))
    console.log(`用户ID: ${testUser.id}`)
    console.log(`用户名: ${testUser.name}`)
    console.log(`邮箱: ${testUser.email}`)
    console.log(`信用分数: ${testUser.creditScore}`)
    console.log(`风险等级: ${testUser.riskLevel}`)
    console.log(`状态: ${testUser.status}`)
    
    console.log('\n💡 现在可以在管理员页面测试标记功能：')
    console.log('1. 访问: http://localhost:3000/admin/users')
    console.log('2. 找到用户"可疑用户 (<EMAIL>)"')
    console.log('3. 点击"🚩 标记风险"按钮')
    console.log('4. 选择标记类型和风险等级')
    console.log('5. 填写标记原因')
    console.log('6. 确认标记并查看结果')
    
    // 创建一些模拟的可疑活动记录
    console.log('\n📝 为演示目的，创建一些模拟活动...')
    
    // 创建一个商品
    const product = await prisma.product.create({
      data: {
        title: '可疑商品 - 价格异常低',
        description: '这是一个价格异常低的商品，可能存在问题',
        price: 1.0, // 异常低价
        images: JSON.stringify(['https://example.com/suspicious.jpg']),
        category: 'ELECTRONICS',
        condition: 'NEW',
        stock: 1,
        sellerId: testUser.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        city: '北京',
        district: '朝阳区'
      }
    })
    
    console.log(`✅ 创建了可疑商品: ${product.title}`)
    console.log(`   价格: ${product.price} USDT (异常低价)`)
    
  } catch (error) {
    console.error('创建测试用户失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
  .then(() => {
    console.log('\n🎉 测试用户创建完成！')
    console.log('现在可以在管理员用户管理页面测试风险标记功能了。')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 脚本执行失败:', error)
    process.exit(1)
  })
