/**
 * 创建演示账户脚本
 * 创建1个管理员账户和2个普通用户账户，密码都是123456
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createDemoAccounts() {
  console.log('👥 创建演示账户...')
  console.log('='.repeat(40))

  try {
    // 密码哈希
    const hashedPassword = await bcrypt.hash('123456', 12)
    console.log('🔐 密码已加密')

    // 1. 创建管理员账户
    console.log('\n👑 创建管理员账户...')
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword,
        role: 'ADMIN'
      },
      create: {
        email: '<EMAIL>',
        name: '系统管理员',
        password: hashedPassword,
        role: 'ADMIN',
        depositBalance: 10000,
        creditScore: 1000,
        status: 'ACTIVE',
        isGuarantor: true,
        city: '北京市',
        district: '朝阳区'
      }
    })
    console.log(`✅ 管理员账户: ${admin.email}`)
    console.log(`   ID: ${admin.id}`)
    console.log(`   姓名: ${admin.name}`)
    console.log(`   角色: ${admin.role}`)

    // 2. 创建用户1
    console.log('\n👤 创建用户1...')
    const user1 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword
      },
      create: {
        email: '<EMAIL>',
        name: '张三',
        password: hashedPassword,
        role: 'USER',
        depositBalance: 1000,
        creditScore: 750,
        status: 'ACTIVE',
        city: '上海市',
        district: '黄浦区'
      }
    })
    console.log(`✅ 用户1: ${user1.email}`)
    console.log(`   ID: ${user1.id}`)
    console.log(`   姓名: ${user1.name}`)
    console.log(`   信用分: ${user1.creditScore}`)

    // 3. 创建用户2
    console.log('\n👤 创建用户2...')
    const user2 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword
      },
      create: {
        email: '<EMAIL>',
        name: '李四',
        password: hashedPassword,
        role: 'USER',
        depositBalance: 1500,
        creditScore: 680,
        status: 'ACTIVE',
        city: '广州市',
        district: '天河区'
      }
    })
    console.log(`✅ 用户2: ${user2.email}`)
    console.log(`   ID: ${user2.id}`)
    console.log(`   姓名: ${user2.name}`)
    console.log(`   信用分: ${user2.creditScore}`)

    // 显示账户总结
    console.log('\n📊 账户创建总结:')
    console.log('='.repeat(50))
    console.log('👑 管理员账户:')
    console.log(`   邮箱: <EMAIL>`)
    console.log(`   密码: 123456`)
    console.log(`   姓名: 系统管理员`)
    console.log(`   角色: 管理员`)
    console.log(`   保证金: 10,000 USDT`)
    console.log(`   信用分: 1,000`)
    
    console.log('\n👤 用户账户1:')
    console.log(`   邮箱: <EMAIL>`)
    console.log(`   密码: 123456`)
    console.log(`   姓名: 张三`)
    console.log(`   角色: 普通用户`)
    console.log(`   保证金: 1,000 USDT`)
    console.log(`   信用分: 750`)
    console.log(`   位置: 上海市黄浦区`)
    
    console.log('\n👤 用户账户2:')
    console.log(`   邮箱: <EMAIL>`)
    console.log(`   密码: 123456`)
    console.log(`   姓名: 李四`)
    console.log(`   角色: 普通用户`)
    console.log(`   保证金: ¥1,500`)
    console.log(`   信用分: 680`)
    console.log(`   位置: 广州市天河区`)

    console.log('\n🎉 演示账户创建完成!')
    console.log('\n🔗 登录地址: http://localhost:3000/auth/signin')

    return {
      admin,
      user1,
      user2
    }

  } catch (error) {
    console.error('❌ 创建账户失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 主函数
async function main() {
  console.log('🚀 BitMarket 演示账户创建工具')
  console.log('='.repeat(50))
  
  try {
    // 创建演示账户
    const result = await createDemoAccounts()
    
    console.log('\n✨ 账户创建完成，可以开始登录测试!')
    
    return result

  } catch (error) {
    console.error('\n💥 创建失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  createDemoAccounts
}
