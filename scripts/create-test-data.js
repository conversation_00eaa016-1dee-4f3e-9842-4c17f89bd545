const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestData() {
  try {
    console.log('开始创建测试数据...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('未找到管理员用户')
      return
    }

    console.log('找到管理员:', admin.email)

    // 1. 创建礼品卡商品
    const giftCardProduct = await prisma.giftCardProduct.create({
      data: {
        name: '测试礼品卡',
        description: '用于测试的礼品卡商品',
        productType: 'GIFT_CARD',
        faceValue: 10,
        salePrice: 10,
        stock: 100,
        isActive: true,
        validDays: 365,
        supportedPayments: ['BALANCE', 'BINANCE_PAY'],
        createdById: admin.id
      }
    })

    console.log('创建礼品卡商品:', giftCardProduct.name)

    // 2. 创建充值卡商品
    const rechargeCardProduct = await prisma.giftCardProduct.create({
      data: {
        name: '测试充值卡',
        description: '用于测试的充值卡商品',
        productType: 'RECHARGE_CARD',
        faceValue: 20,
        salePrice: 20,
        stock: 50,
        isActive: true,
        validDays: 365,
        supportedPayments: ['BALANCE'],
        createdById: admin.id
      }
    })

    console.log('创建充值卡商品:', rechargeCardProduct.name)

    // 3. 生成一些测试礼品卡
    const testGiftCards = []
    for (let i = 0; i < 5; i++) {
      const cardCode = generateGiftCardCode()
      const giftCard = await prisma.giftCard.create({
        data: {
          cardCode,
          faceValue: 10,
          status: 'GENERATED',
          validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
          productId: giftCardProduct.id,
          createdById: admin.id,
          batchId: 'TEST_BATCH_001'
        }
      })
      testGiftCards.push(giftCard)
      console.log(`创建测试礼品卡 ${i + 1}: ${cardCode}`)
    }

    // 4. 创建一些测试兑换码
    const testRedemptionCodes = []
    for (let i = 0; i < 3; i++) {
      const codeValue = generateRedemptionCode()
      const redemptionCode = await prisma.redemptionCode.create({
        data: {
          codeValue,
          codeType: 'REDEMPTION_CODE',
          rewardType: 'CASH_CREDIT',
          rewardValue: 5,
          rewardUnit: 'USDT',
          distributionType: 'PUBLIC',
          maxUses: 1,
          validFrom: new Date(),
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
          status: 'ACTIVE',
          createdById: admin.id,
          title: `测试兑换码 ${i + 1}`,
          description: '用于测试的兑换码'
        }
      })
      testRedemptionCodes.push(redemptionCode)
      console.log(`创建测试兑换码 ${i + 1}: ${codeValue}`)
    }

    console.log('\n✅ 测试数据创建完成!')
    console.log('\n📋 测试礼品卡码:')
    testGiftCards.forEach((card, index) => {
      console.log(`${index + 1}. ${card.cardCode}`)
    })

    console.log('\n🎫 测试兑换码:')
    testRedemptionCodes.forEach((code, index) => {
      console.log(`${index + 1}. ${code.codeValue}`)
    })

    console.log('\n💡 使用说明:')
    console.log('1. 访问 http://localhost:3000/deposit')
    console.log('2. 点击"兑换码"标签页')
    console.log('3. 切换到"礼品卡"模式测试礼品卡兑换')
    console.log('4. 切换到"兑换码"模式测试兑换码使用')

  } catch (error) {
    console.error('创建测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 生成礼品卡码
function generateGiftCardCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成兑换码
function generateRedemptionCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 运行脚本
createTestData()
