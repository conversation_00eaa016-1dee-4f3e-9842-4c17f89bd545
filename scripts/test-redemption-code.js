const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testRedemptionCode() {
  try {
    console.log('🧪 开始测试兑换码功能...')

    // 查找管理员用户
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      console.error('❌ 未找到管理员用户')
      return
    }

    console.log('👤 管理员信息:')
    console.log('  - ID:', admin.id)
    console.log('  - 邮箱:', admin.email)
    console.log('  - 当前余额:', admin.depositBalance, 'USDT')

    // 查找测试兑换码
    const testRedemptionCode = 'HRCE5Z9CSOW8'
    const redemptionCode = await prisma.redemptionCode.findUnique({
      where: { codeValue: testRedemptionCode },
      include: {
        createdBy: { select: { name: true } },
        targetUser: { select: { name: true, email: true } }
      }
    })

    if (!redemptionCode) {
      console.error('❌ 兑换码不存在:', testRedemptionCode)
      return
    }

    console.log('\n🎫 兑换码信息:')
    console.log('  - 代码:', redemptionCode.codeValue)
    console.log('  - 奖励类型:', redemptionCode.rewardType)
    console.log('  - 奖励值:', redemptionCode.rewardValue, redemptionCode.rewardUnit)
    console.log('  - 状态:', redemptionCode.status)
    console.log('  - 有效期:', redemptionCode.validUntil)
    console.log('  - 最大使用次数:', redemptionCode.maxUses)
    console.log('  - 已使用次数:', redemptionCode.usedCount)

    if (redemptionCode.status !== 'ACTIVE') {
      console.log('⚠️  兑换码状态不是 ACTIVE，无法使用')
      return
    }

    if (redemptionCode.usedCount >= redemptionCode.maxUses) {
      console.log('⚠️  兑换码已达到最大使用次数')
      return
    }

    // 模拟兑换过程
    console.log('\n💰 开始使用兑换码...')
    
    const result = await prisma.$transaction(async (tx) => {
      // 1. 更新兑换码使用次数
      const updatedCode = await tx.redemptionCode.update({
        where: { id: redemptionCode.id },
        data: {
          usedCount: {
            increment: 1
          },
          lastUsedAt: new Date()
        }
      })

      // 2. 创建兑换交易记录
      const transaction = await tx.redemptionTransaction.create({
        data: {
          userId: admin.id,
          redemptionCodeId: redemptionCode.id,
          transactionType: 'REDEMPTION',
          rewardValue: redemptionCode.rewardValue,
          rewardUnit: redemptionCode.rewardUnit,
          description: `兑换码使用: ${redemptionCode.codeValue}`,
          metadata: {
            rewardType: redemptionCode.rewardType,
            status: 'COMPLETED'
          }
        }
      })

      // 3. 根据奖励类型处理奖励
      if (redemptionCode.rewardType === 'CASH_CREDIT') {
        // 现金奖励：直接增加用户余额
        await tx.user.update({
          where: { id: admin.id },
          data: {
            depositBalance: {
              increment: redemptionCode.rewardValue
            }
          }
        })

        // 创建资金交易记录
        await tx.fundTransaction.create({
          data: {
            userId: admin.id,
            type: 'REDEMPTION_REWARD',
            amount: redemptionCode.rewardValue,
            description: `兑换码奖励: ${redemptionCode.rewardValue} ${redemptionCode.rewardUnit}`,
            relatedId: redemptionCode.id,
            metadata: {
              relatedType: 'REDEMPTION_CODE',
              codeValue: redemptionCode.codeValue,
              rewardType: redemptionCode.rewardType
            }
          }
        })
      }

      return {
        code: updatedCode,
        transaction,
        rewardAmount: redemptionCode.rewardValue
      }
    })

    console.log('✅ 兑换成功!')
    console.log('  - 奖励金额:', result.rewardAmount, redemptionCode.rewardUnit)
    console.log('  - 兑换时间:', result.transaction.createdAt)

    // 验证结果
    const updatedAdmin = await prisma.user.findUnique({
      where: { id: admin.id },
      select: { depositBalance: true }
    })

    console.log('\n📊 兑换后状态:')
    console.log('  - 新余额:', updatedAdmin.depositBalance, 'USDT')
    console.log('  - 余额增加:', updatedAdmin.depositBalance - admin.depositBalance, 'USDT')

    // 检查交易记录
    const transactions = await prisma.fundTransaction.findMany({
      where: {
        userId: admin.id,
        type: 'REDEMPTION_REWARD'
      },
      orderBy: { createdAt: 'desc' },
      take: 1
    })

    if (transactions.length > 0) {
      console.log('\n📝 交易记录:')
      console.log('  - 类型:', transactions[0].type)
      console.log('  - 金额:', transactions[0].amount, 'USDT')
      console.log('  - 描述:', transactions[0].description)
      console.log('  - 时间:', transactions[0].createdAt)
    }

    console.log('\n🎉 兑换码功能测试完成！')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
testRedemptionCode()
