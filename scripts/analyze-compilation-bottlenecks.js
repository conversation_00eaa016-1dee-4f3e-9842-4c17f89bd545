#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

class CompilationAnalyzer {
  constructor() {
    this.results = {
      timestamp: Date.now(),
      issues: [],
      optimizations: [],
      dependencies: {},
      components: {},
      imports: {}
    }
  }

  // 分析依赖结构
  async analyzeDependencies() {
    console.log('📦 分析依赖结构...')
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      const deps = { ...packageJson.dependencies, ...packageJson.devDependencies }
      
      // 大型依赖库检查
      const heavyDeps = [
        '@heroicons/react',
        'next-auth',
        '@prisma/client',
        'bcryptjs',
        'socket.io',
        'tailwindcss'
      ]

      console.log('  检查大型依赖:')
      heavyDeps.forEach(dep => {
        if (deps[dep]) {
          console.log(`    ✅ ${dep}: ${deps[dep]}`)
          this.results.dependencies[dep] = deps[dep]
        }
      })

      // 检查可能的重复依赖
      const duplicateChecks = [
        ['react', 'react-dom'],
        ['@types/react', '@types/react-dom'],
        ['typescript', '@types/node']
      ]

      console.log('  检查依赖一致性:')
      duplicateChecks.forEach(([dep1, dep2]) => {
        if (deps[dep1] && deps[dep2]) {
          console.log(`    ✅ ${dep1} & ${dep2} 都存在`)
        }
      })

    } catch (error) {
      console.log('  ❌ 无法读取package.json')
      this.results.issues.push({
        type: 'dependency',
        message: '无法分析依赖结构',
        severity: 'warning'
      })
    }
  }

  // 分析组件导入结构
  async analyzeComponentImports() {
    console.log('\n🧩 分析组件导入结构...')
    
    const componentDirs = ['app', 'components', 'lib']
    let totalFiles = 0
    let heavyImports = 0
    let circularImports = []

    for (const dir of componentDirs) {
      if (fs.existsSync(dir)) {
        const files = this.getAllFiles(dir, ['.tsx', '.ts', '.js', '.jsx'])
        totalFiles += files.length
        
        console.log(`  📁 ${dir}: ${files.length} 文件`)
        
        // 分析每个文件的导入
        for (const file of files.slice(0, 10)) { // 限制分析前10个文件
          try {
            const content = fs.readFileSync(file, 'utf8')
            const imports = this.extractImports(content)
            
            if (imports.length > 15) {
              heavyImports++
              console.log(`    ⚠️  ${path.relative(process.cwd(), file)}: ${imports.length} 个导入`)
              
              this.results.issues.push({
                type: 'import',
                file: file,
                message: `过多导入 (${imports.length}个)`,
                severity: 'warning'
              })
            }

            // 检查大型库的导入
            const heavyLibImports = imports.filter(imp => 
              imp.includes('@heroicons/react') ||
              imp.includes('lucide-react') ||
              imp.includes('react-icons')
            )

            if (heavyLibImports.length > 5) {
              console.log(`    🔍 ${path.relative(process.cwd(), file)}: ${heavyLibImports.length} 个图标导入`)
            }

          } catch (error) {
            // 忽略读取错误
          }
        }
      }
    }

    console.log(`  📊 总计: ${totalFiles} 文件, ${heavyImports} 个重导入文件`)
    
    this.results.components = {
      totalFiles,
      heavyImports,
      circularImports: circularImports.length
    }
  }

  // 分析Next.js配置问题
  analyzeNextConfig() {
    console.log('\n⚙️  分析Next.js配置...')
    
    try {
      const nextConfigPath = 'next.config.js'
      if (fs.existsSync(nextConfigPath)) {
        const content = fs.readFileSync(nextConfigPath, 'utf8')
        console.log('  ✅ next.config.js 存在')
        
        // 检查已知的配置问题
        if (content.includes('swcMinify')) {
          console.log('  ⚠️  检测到过时的 swcMinify 配置')
          this.results.issues.push({
            type: 'config',
            message: 'swcMinify 配置已过时，在新版本Next.js中默认启用',
            severity: 'warning'
          })
        }

        if (content.includes('incrementalCacheHandlerPath')) {
          console.log('  ⚠️  检测到实验性缓存配置')
          this.results.issues.push({
            type: 'config',
            message: 'incrementalCacheHandlerPath 可能不兼容当前Next.js版本',
            severity: 'warning'
          })
        }

      } else {
        console.log('  ❌ next.config.js 不存在')
      }
    } catch (error) {
      console.log('  ❌ 无法分析Next.js配置')
    }
  }

  // 生成优化建议
  generateOptimizations() {
    console.log('\n🚀 生成优化建议...')
    
    this.results.optimizations = [
      {
        category: '依赖优化',
        priority: 'high',
        items: [
          '使用动态导入 (dynamic import) 延迟加载大型组件',
          '优化图标库导入，只导入需要的图标',
          '检查并移除未使用的依赖',
          '使用 bundle analyzer 分析包大小'
        ]
      },
      {
        category: 'Next.js配置优化',
        priority: 'high',
        items: [
          '修复过时的配置选项',
          '启用正确的编译器优化',
          '配置适当的缓存策略',
          '优化webpack配置'
        ]
      },
      {
        category: '代码结构优化',
        priority: 'medium',
        items: [
          '减少单个文件的导入数量',
          '实现组件懒加载',
          '优化路由结构',
          '使用React.memo优化重渲染'
        ]
      },
      {
        category: '开发环境优化',
        priority: 'medium',
        items: [
          '启用Fast Refresh优化',
          '配置适当的文件监听',
          '优化TypeScript编译',
          '使用SWC替代Babel'
        ]
      }
    ]

    console.log('优化建议生成完成:')
    this.results.optimizations.forEach(opt => {
      console.log(`\n${opt.category} (优先级: ${opt.priority}):`)
      opt.items.forEach(item => {
        console.log(`  • ${item}`)
      })
    })
  }

  // 辅助函数：获取所有文件
  getAllFiles(dir, extensions) {
    let files = []
    
    try {
      const items = fs.readdirSync(dir)
      
      for (const item of items) {
        const fullPath = path.join(dir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files = files.concat(this.getAllFiles(fullPath, extensions))
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath)
        }
      }
    } catch (error) {
      // 忽略访问错误
    }
    
    return files
  }

  // 辅助函数：提取导入语句
  extractImports(content) {
    const imports = []
    const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g
    let match

    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1])
    }

    return imports
  }

  // 生成报告
  generateReport() {
    console.log('\n📊 编译瓶颈分析报告')
    console.log('='.repeat(60))
    
    const criticalIssues = this.results.issues.filter(i => i.severity === 'critical').length
    const warningIssues = this.results.issues.filter(i => i.severity === 'warning').length
    
    console.log(`🚨 严重问题: ${criticalIssues}个`)
    console.log(`⚠️  警告问题: ${warningIssues}个`)
    console.log(`💡 优化建议: ${this.results.optimizations.length}类`)
    
    if (this.results.issues.length > 0) {
      console.log('\n🔍 发现的问题:')
      this.results.issues.forEach((issue, index) => {
        const icon = issue.severity === 'critical' ? '🚨' : '⚠️'
        console.log(`${index + 1}. ${icon} [${issue.type.toUpperCase()}] ${issue.message}`)
        if (issue.file) {
          console.log(`   📁 文件: ${path.relative(process.cwd(), issue.file)}`)
        }
      })
    }

    // 计算优化潜力
    const optimizationPotential = this.calculateOptimizationPotential()
    console.log(`\n🎯 预期编译时间改善: ${optimizationPotential.improvement}`)
    console.log(`📈 优化潜力评级: ${optimizationPotential.grade}`)

    return {
      ...this.results,
      optimizationPotential
    }
  }

  // 计算优化潜力
  calculateOptimizationPotential() {
    let potentialImprovement = 0
    
    // 基于发现的问题计算改善潜力
    this.results.issues.forEach(issue => {
      if (issue.type === 'config') potentialImprovement += 30
      if (issue.type === 'import') potentialImprovement += 15
      if (issue.type === 'dependency') potentialImprovement += 10
    })

    // 基于组件复杂度
    if (this.results.components.heavyImports > 5) {
      potentialImprovement += 25
    }

    potentialImprovement = Math.min(potentialImprovement, 70) // 最大70%改善

    let grade, improvement
    if (potentialImprovement >= 50) {
      grade = 'A'
      improvement = '50-70% (显著改善)'
    } else if (potentialImprovement >= 30) {
      grade = 'B'
      improvement = '30-50% (良好改善)'
    } else if (potentialImprovement >= 15) {
      grade = 'C'
      improvement = '15-30% (一般改善)'
    } else {
      grade = 'D'
      improvement = '< 15% (有限改善)'
    }

    return {
      potential: potentialImprovement,
      grade,
      improvement
    }
  }

  // 保存分析结果
  async saveResults(report) {
    try {
      await fs.promises.mkdir('test-results/performance', { recursive: true })
      
      const filename = `compilation-analysis-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await fs.promises.writeFile(
        `test-results/performance/${filename}`,
        JSON.stringify(report, null, 2)
      )
      
      console.log(`\n💾 分析结果已保存: test-results/performance/${filename}`)
    } catch (error) {
      console.error('保存分析结果失败:', error)
    }
  }
}

// 运行分析
async function runCompilationAnalysis() {
  const analyzer = new CompilationAnalyzer()
  
  try {
    console.log('🔍 开始编译瓶颈分析')
    console.log('='.repeat(60))
    
    await analyzer.analyzeDependencies()
    await analyzer.analyzeComponentImports()
    analyzer.analyzeNextConfig()
    analyzer.generateOptimizations()
    
    const report = analyzer.generateReport()
    await analyzer.saveResults(report)
    
    console.log('\n✅ 编译瓶颈分析完成')
    return report
    
  } catch (error) {
    console.error('❌ 编译分析失败:', error)
    throw error
  }
}

// 命令行运行
if (require.main === module) {
  runCompilationAnalysis()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { CompilationAnalyzer, runCompilationAnalysis }
