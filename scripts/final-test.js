const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function finalTest() {
  try {
    console.log('🎯 开始最终功能测试...')

    // 1. 检查礼品卡商品
    console.log('\n📦 检查礼品卡商品...')
    const giftCardProducts = await prisma.giftCardProduct.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            giftCards: true,
            orders: true
          }
        }
      }
    })

    console.log(`✅ 找到 ${giftCardProducts.length} 个活跃的礼品卡商品`)
    giftCardProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} (${product.productType})`)
      console.log(`     面值: ${product.faceValue} USDT, 库存: ${product.stock}`)
      console.log(`     关联礼品卡: ${product._count.giftCards} 张`)
    })

    // 2. 检查礼品卡
    console.log('\n🎁 检查礼品卡状态...')
    const giftCards = await prisma.giftCard.findMany({
      include: {
        product: { select: { name: true } },
        redeemedBy: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    console.log(`✅ 找到 ${giftCards.length} 张礼品卡`)
    giftCards.forEach((card, index) => {
      console.log(`  ${index + 1}. ${card.cardCode} - ${card.status}`)
      console.log(`     面值: ${card.faceValue} USDT, 商品: ${card.product?.name || '无'}`)
      if (card.redeemedBy) {
        console.log(`     已兑换，兑换人: ${card.redeemedBy.name}`)
      }
    })

    // 3. 检查兑换码
    console.log('\n🎫 检查兑换码状态...')
    const redemptionCodes = await prisma.redemptionCode.findMany({
      include: {
        createdBy: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`✅ 找到 ${redemptionCodes.length} 个兑换码`)
    redemptionCodes.forEach((code, index) => {
      console.log(`  ${index + 1}. ${code.codeValue} - ${code.status}`)
      console.log(`     奖励: ${code.rewardValue} ${code.rewardUnit} (${code.rewardType})`)
      console.log(`     使用次数: ${code.usedCount}/${code.maxUses}`)
    })

    // 4. 检查交易记录
    console.log('\n📊 检查最近的交易记录...')
    const fundTransactions = await prisma.fundTransaction.findMany({
      include: {
        user: { select: { name: true, email: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`✅ 找到 ${fundTransactions.length} 条资金交易记录`)
    fundTransactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. ${tx.type} - ${tx.amount} USDT`)
      console.log(`     用户: ${tx.user.name || tx.user.email}`)
      console.log(`     描述: ${tx.description}`)
      console.log(`     时间: ${tx.createdAt}`)
    })

    // 5. 检查礼品卡交易记录
    console.log('\n🎁 检查礼品卡交易记录...')
    const giftCardTransactions = await prisma.giftCardTransaction.findMany({
      include: {
        user: { select: { name: true, email: true } },
        giftCard: { select: { cardCode: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`✅ 找到 ${giftCardTransactions.length} 条礼品卡交易记录`)
    giftCardTransactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. ${tx.transactionType} - ${tx.amount} USDT`)
      console.log(`     礼品卡: ${tx.giftCard.cardCode}`)
      console.log(`     用户: ${tx.user.name || tx.user.email}`)
      console.log(`     时间: ${tx.createdAt}`)
    })

    // 6. 检查兑换交易记录
    console.log('\n🎫 检查兑换交易记录...')
    const redemptionTransactions = await prisma.redemptionTransaction.findMany({
      include: {
        user: { select: { name: true, email: true } },
        redemptionCode: { select: { codeValue: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`✅ 找到 ${redemptionTransactions.length} 条兑换交易记录`)
    redemptionTransactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. ${tx.transactionType} - ${tx.rewardValue} ${tx.rewardUnit}`)
      console.log(`     兑换码: ${tx.redemptionCode.codeValue}`)
      console.log(`     用户: ${tx.user.name || tx.user.email}`)
      console.log(`     时间: ${tx.createdAt}`)
    })

    // 7. 生成可用的测试数据
    console.log('\n🧪 可用的测试数据:')
    
    const availableGiftCards = giftCards.filter(card => card.status === 'GENERATED')
    const availableRedemptionCodes = redemptionCodes.filter(code => 
      code.status === 'ACTIVE' && code.usedCount < code.maxUses
    )

    console.log('\n🎁 可兑换的礼品卡:')
    if (availableGiftCards.length > 0) {
      availableGiftCards.slice(0, 3).forEach((card, index) => {
        console.log(`  ${index + 1}. ${card.cardCode} (${card.faceValue} USDT)`)
      })
    } else {
      console.log('  暂无可兑换的礼品卡')
    }

    console.log('\n🎫 可使用的兑换码:')
    if (availableRedemptionCodes.length > 0) {
      availableRedemptionCodes.slice(0, 3).forEach((code, index) => {
        console.log(`  ${index + 1}. ${code.codeValue} (${code.rewardValue} ${code.rewardUnit})`)
      })
    } else {
      console.log('  暂无可使用的兑换码')
    }

    console.log('\n🎉 最终测试完成！')
    console.log('\n💡 测试说明:')
    console.log('1. 访问 http://localhost:3000/deposit')
    console.log('2. 点击"兑换码"标签页')
    console.log('3. 切换到"礼品卡"模式测试礼品卡兑换')
    console.log('4. 切换到"兑换码"模式测试兑换码使用')
    console.log('5. 访问 http://localhost:3000/admin/giftcard-products 管理礼品卡商品')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行测试
finalTest()
