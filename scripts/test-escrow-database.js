const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testEscrowDatabase() {
  try {
    console.log('🧪 开始测试托管系统数据库功能...')
    console.log('=' .repeat(80))

    // 1. 创建测试用户
    console.log('\n👥 1. 创建测试用户')
    const testUsers = await createTestUsers()

    // 2. 测试中间人申请流程
    console.log('\n📝 2. 测试中间人申请流程')
    const mediatorApplication = await testMediatorApplication(testUsers.mediator)

    // 3. 批准中间人申请
    console.log('\n✅ 3. 批准中间人申请')
    await approveMediatorApplication(mediatorApplication.id, testUsers.admin.id)

    // 4. 创建测试商品和订单
    console.log('\n📦 4. 创建测试商品和订单')
    const { product, order } = await createTestProductAndOrder(testUsers)

    // 5. 测试托管订单创建
    console.log('\n🛡️ 5. 测试托管订单创建')
    const escrowOrder = await testEscrowOrderCreation(order.id, testUsers.mediator.id, testUsers.buyer.id)

    // 6. 测试托管流程
    console.log('\n🔄 6. 测试托管流程')
    await testEscrowWorkflow(escrowOrder.id)

    // 7. 测试争议举报
    console.log('\n⚖️ 7. 测试争议举报')
    const dispute = await testDisputeCreation(escrowOrder.id, testUsers.buyer.id, testUsers.seller.id)

    // 8. 测试提现券系统
    console.log('\n🎫 8. 测试提现券系统')
    await testWithdrawalVoucherSystem(testUsers.admin.id, testUsers.mediator.id)

    // 9. 生成测试报告
    console.log('\n📊 9. 生成测试报告')
    await generateTestReport()

    console.log('\n🎉 托管系统数据库测试完成！')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 1. 创建测试用户
async function createTestUsers() {
  const timestamp = Date.now()
  
  const buyer = await prisma.user.create({
    data: {
      email: `escrow_buyer_${timestamp}@test.com`,
      name: `托管测试买家${timestamp}`,
      password: 'password123',
      role: 'USER',
      emailVerified: new Date(),
      depositBalance: 2000,
      creditPoints: 100
    }
  })

  const seller = await prisma.user.create({
    data: {
      email: `escrow_seller_${timestamp}@test.com`,
      name: `托管测试卖家${timestamp}`,
      password: 'password123',
      role: 'USER',
      emailVerified: new Date(),
      depositBalance: 500,
      creditPoints: 150
    }
  })

  const mediator = await prisma.user.create({
    data: {
      email: `escrow_mediator_${timestamp}@test.com`,
      name: `托管测试中间人${timestamp}`,
      password: 'password123',
      role: 'USER',
      emailVerified: new Date(),
      depositBalance: 5000,
      creditPoints: 200
    }
  })

  const admin = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  }) || await prisma.user.create({
    data: {
      email: `escrow_admin_${timestamp}@test.com`,
      name: `托管测试管理员${timestamp}`,
      password: 'password123',
      role: 'ADMIN',
      emailVerified: new Date(),
      depositBalance: 10000,
      creditPoints: 500
    }
  })

  console.log('✅ 测试用户创建成功:')
  console.log(`  - 买家: ${buyer.name} (余额: ${buyer.depositBalance} USDT)`)
  console.log(`  - 卖家: ${seller.name} (余额: ${seller.depositBalance} USDT)`)
  console.log(`  - 中间人: ${mediator.name} (余额: ${mediator.depositBalance} USDT)`)
  console.log(`  - 管理员: ${admin.name}`)

  return { buyer, seller, mediator, admin }
}

// 2. 测试中间人申请
async function testMediatorApplication(mediator) {
  const application = await prisma.mediatorApplication.create({
    data: {
      userId: mediator.id,
      bnbWalletAddress: `0x${Date.now().toString(16).padStart(40, '0')}`,
      depositAmount: 2000,
      feeRate: 0.025, // 2.5%
      experience: '拥有5年数字货币交易经验，熟悉各种托管服务流程，具备良好的沟通能力和风险控制意识。',
      introduction: '专业的数字货币交易中间人，致力于为用户提供安全、高效的托管服务。',
      status: 'PENDING'
    }
  })

  // 冻结保证金
  await prisma.fundFreeze.create({
    data: {
      userId: mediator.id,
      amount: 2000,
      purpose: '中间人保证金',
      status: 'FROZEN',
      relatedId: application.id,
      relatedType: 'MEDIATOR_APPLICATION'
    }
  })

  await prisma.user.update({
    where: { id: mediator.id },
    data: { depositBalance: { decrement: 2000 } }
  })

  console.log('✅ 中间人申请创建成功:')
  console.log(`  - 申请ID: ${application.id}`)
  console.log(`  - 保证金: ${application.depositAmount} USDT`)
  console.log(`  - 手续费率: ${application.feeRate * 100}%`)

  return application
}

// 3. 批准中间人申请
async function approveMediatorApplication(applicationId, adminId) {
  const application = await prisma.mediatorApplication.findUnique({
    where: { id: applicationId },
    include: { user: true }
  })

  await prisma.$transaction(async (tx) => {
    // 更新申请状态
    await tx.mediatorApplication.update({
      where: { id: applicationId },
      data: {
        status: 'APPROVED',
        reviewedBy: adminId,
        reviewNotes: '申请材料完整，符合中间人要求',
        approvedAt: new Date()
      }
    })

    // 更新用户为中间人
    await tx.user.update({
      where: { id: application.userId },
      data: {
        isMediator: true,
        mediatorStatus: 'ACTIVE',
        mediatorFeeRate: application.feeRate,
        mediatorDeposit: application.depositAmount,
        mediatorVerifiedAt: new Date(),
        bnbWalletAddress: application.bnbWalletAddress,
        bnbWalletVerified: true,
        bnbWalletVerifiedAt: new Date(),
        mediatorReputation: 85.0,
        mediatorSuccessRate: 0.95
      }
    })

    // 更新保证金冻结记录
    await tx.fundFreeze.updateMany({
      where: {
        userId: application.userId,
        relatedId: applicationId,
        relatedType: 'MEDIATOR_APPLICATION',
        status: 'FROZEN'
      },
      data: {
        purpose: '中间人保证金',
        relatedType: 'MEDIATOR_DEPOSIT'
      }
    })
  })

  console.log('✅ 中间人申请已批准')
}

// 4. 创建测试商品和订单
async function createTestProductAndOrder(testUsers) {
  const product = await prisma.product.create({
    data: {
      title: '托管测试商品 - MacBook Pro M3',
      description: '全新MacBook Pro M3，16GB内存，512GB存储，深空灰色。',
      price: 150.00,
      category: 'ELECTRONICS',
      condition: 'NEW',
      city: '北京市',
      district: '朝阳区',
      address: '北京市朝阳区测试街道',
      sellerId: testUsers.seller.id,
      status: 'AVAILABLE',
      reviewStatus: 'APPROVED'
    }
  })

  const order = await prisma.order.create({
    data: {
      orderNumber: `ESCROW_TEST_${Date.now()}`,
      buyerId: testUsers.buyer.id,
      sellerId: testUsers.seller.id,
      productId: product.id,
      totalAmount: product.price,
      productPrice: product.price,
      status: 'PENDING_PAYMENT',
      paymentMethod: 'BALANCE'
    }
  })

  console.log('✅ 测试商品和订单创建成功:')
  console.log(`  - 商品: ${product.title} (${product.price} USDT)`)
  console.log(`  - 订单: ${order.orderNumber}`)

  return { product, order }
}

// 5. 测试托管订单创建
async function testEscrowOrderCreation(orderId, mediatorId, buyerId) {
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    include: { product: true }
  })

  const mediator = await prisma.user.findUnique({
    where: { id: mediatorId }
  })

  const mediatorFee = order.totalAmount * mediator.mediatorFeeRate
  const platformFee = mediatorFee * 0.3
  const totalAmount = order.totalAmount + mediatorFee

  const escrowOrder = await prisma.$transaction(async (tx) => {
    // 创建托管记录
    const escrow = await tx.escrowOrder.create({
      data: {
        orderId,
        mediatorId,
        buyerId: order.buyerId,
        sellerId: order.sellerId,
        amount: order.totalAmount,
        mediatorFee,
        platformFee,
        status: 'PENDING',
        mediatorWalletAddress: mediator.bnbWalletAddress
      }
    })

    // 扣除买家余额
    await tx.user.update({
      where: { id: buyerId },
      data: { depositBalance: { decrement: totalAmount } }
    })

    // 创建资金冻结记录
    await tx.fundFreeze.create({
      data: {
        userId: buyerId,
        amount: totalAmount,
        purpose: '托管订单资金',
        status: 'FROZEN',
        relatedId: escrow.id,
        relatedType: 'ESCROW_ORDER'
      }
    })

    // 更新订单状态
    await tx.order.update({
      where: { id: orderId },
      data: {
        status: 'PAID',
        paymentConfirmed: true,
        useEscrow: true,
        mediatorId,
        escrowFee: mediatorFee,
        escrowFeeRate: mediator.mediatorFeeRate
      }
    })

    // 创建聊天室
    await tx.escrowChatRoom.create({
      data: {
        escrowOrderId: escrow.id,
        roomCode: `ESCROW_${escrow.id}_${Date.now()}`,
        isActive: true
      }
    })

    return escrow
  })

  console.log('✅ 托管订单创建成功:')
  console.log(`  - 托管ID: ${escrowOrder.id}`)
  console.log(`  - 订单金额: ${order.totalAmount} USDT`)
  console.log(`  - 中间人费用: ${mediatorFee.toFixed(2)} USDT`)
  console.log(`  - 平台费用: ${platformFee.toFixed(2)} USDT`)

  return escrowOrder
}

// 6. 测试托管流程
async function testEscrowWorkflow(escrowOrderId) {
  // 6.1 中间人确认资金到账
  await prisma.escrowOrder.update({
    where: { id: escrowOrderId },
    data: {
      status: 'FUNDED',
      fundedAt: new Date(),
      bnbTransactionHash: `0x${Date.now().toString(16)}`
    }
  })
  console.log('  ✅ 中间人确认资金到账')

  // 6.2 卖家发货
  await prisma.escrowOrder.update({
    where: { id: escrowOrderId },
    data: {
      status: 'SHIPPED',
      shippedAt: new Date()
    }
  })
  console.log('  ✅ 卖家确认发货')

  // 6.3 买家确认收货
  await prisma.escrowOrder.update({
    where: { id: escrowOrderId },
    data: {
      status: 'DELIVERED',
      deliveredAt: new Date()
    }
  })
  console.log('  ✅ 买家确认收货')

  // 6.4 中间人完成托管
  const escrowOrder = await prisma.escrowOrder.findUnique({
    where: { id: escrowOrderId }
  })

  await prisma.$transaction(async (tx) => {
    // 更新托管状态
    await tx.escrowOrder.update({
      where: { id: escrowOrderId },
      data: {
        status: 'COMPLETED',
        completedAt: new Date()
      }
    })

    // 给卖家打款
    await tx.user.update({
      where: { id: escrowOrder.sellerId },
      data: { depositBalance: { increment: escrowOrder.amount } }
    })

    // 给中间人支付费用
    const mediatorFee = escrowOrder.mediatorFee - escrowOrder.platformFee
    await tx.user.update({
      where: { id: escrowOrder.mediatorId },
      data: { depositBalance: { increment: mediatorFee } }
    })

    // 释放买家冻结资金
    await tx.fundFreeze.updateMany({
      where: {
        userId: escrowOrder.buyerId,
        relatedId: escrowOrderId,
        relatedType: 'ESCROW_ORDER',
        status: 'FROZEN'
      },
      data: {
        status: 'RELEASED'
      }
    })
  })

  console.log('  ✅ 中间人完成托管，资金已分配')
  console.log('✅ 托管流程测试完成')
}

// 7. 测试争议举报
async function testDisputeCreation(escrowOrderId, reporterId, reportedId) {
  const dispute = await prisma.escrowDispute.create({
    data: {
      escrowOrderId,
      reporterId,
      reportedId,
      reason: 'PRODUCT_MISMATCH',
      description: '收到的商品与描述不符，颜色和配置都不对',
      evidence: {
        images: ['evidence1.jpg', 'evidence2.jpg'],
        description: '商品实物照片'
      },
      status: 'PENDING',
      priority: 'HIGH'
    }
  })

  console.log('✅ 争议举报创建成功:')
  console.log(`  - 争议ID: ${dispute.id}`)
  console.log(`  - 举报原因: ${dispute.reason}`)
  console.log(`  - 优先级: ${dispute.priority}`)

  return dispute
}

// 8. 测试提现券系统
async function testWithdrawalVoucherSystem(adminId, mediatorId) {
  // 8.1 创建提现券
  const voucher = await prisma.withdrawalVoucher.create({
    data: {
      code: `TEST_VOUCHER_${Date.now()}`,
      amount: 10,
      description: '仲裁投票参与奖励',
      validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      issuedBy: adminId
    }
  })

  console.log('✅ 提现券创建成功:')
  console.log(`  - 券码: ${voucher.code}`)
  console.log(`  - 面值: ${voucher.amount} USDT`)

  // 8.2 分配给中间人
  await prisma.withdrawalVoucher.update({
    where: { id: voucher.id },
    data: { usedBy: mediatorId }
  })

  // 8.3 创建奖励记录
  await prisma.mediatorReward.create({
    data: {
      mediatorId,
      rewardType: 'VOTING_PARTICIPATION',
      voucherId: voucher.id,
      description: '参与仲裁投票奖励',
      earnedAt: new Date()
    }
  })

  console.log('✅ 提现券分配给中间人成功')
}

// 9. 生成测试报告
async function generateTestReport() {
  const stats = {
    users: await prisma.user.count(),
    mediatorApplications: await prisma.mediatorApplication.count(),
    escrowOrders: await prisma.escrowOrder.count(),
    escrowDisputes: await prisma.escrowDispute.count(),
    withdrawalVouchers: await prisma.withdrawalVoucher.count(),
    mediatorRewards: await prisma.mediatorReward.count(),
    escrowChatRooms: await prisma.escrowChatRoom.count()
  }

  console.log('📊 测试报告:')
  console.log('=' .repeat(40))
  console.log(`  - 用户总数: ${stats.users}`)
  console.log(`  - 中间人申请: ${stats.mediatorApplications}`)
  console.log(`  - 托管订单: ${stats.escrowOrders}`)
  console.log(`  - 争议举报: ${stats.escrowDisputes}`)
  console.log(`  - 提现券: ${stats.withdrawalVouchers}`)
  console.log(`  - 中间人奖励: ${stats.mediatorRewards}`)
  console.log(`  - 托管聊天室: ${stats.escrowChatRooms}`)

  console.log('\n✅ 测试功能覆盖:')
  console.log('  ✅ 中间人申请和审核')
  console.log('  ✅ 托管订单创建和管理')
  console.log('  ✅ 托管流程完整执行')
  console.log('  ✅ 争议举报系统')
  console.log('  ✅ 提现券奖励系统')
  console.log('  ✅ 资金冻结和释放')
  console.log('  ✅ 多方聊天室创建')
}

// 运行测试
testEscrowDatabase().catch(console.error)
