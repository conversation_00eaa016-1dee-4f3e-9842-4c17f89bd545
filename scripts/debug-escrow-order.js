const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugEscrowOrder() {
  try {
    const productId = 'cmdr1z4vv00078oqqm2k4gq7v';
    
    console.log('=== 调试中间人订单问题 ===\n');
    
    // 1. 检查商品信息
    console.log('1. 检查商品信息...');
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            creditScore: true
          }
        }
      }
    });
    
    if (!product) {
      console.log('❌ 商品不存在');
      return;
    }
    
    console.log('商品信息:');
    console.log(`- ID: ${product.id}`);
    console.log(`- 标题: ${product.title}`);
    console.log(`- 价格: ${product.price} USDT`);
    console.log(`- 库存: ${product.stock}`);
    console.log(`- 状态: ${product.status}`);
    console.log(`- 卖家: ${product.seller.name} (ID: ${product.seller.id})`);
    
    // 2. 检查中间人订单条件
    console.log('\n2. 检查中间人订单条件...');
    console.log(`商品价格: ${product.price} USDT`);
    console.log(`是否满足最低金额 (>= 100 USDT): ${product.price >= 100 ? '✅' : '❌'}`);
    
    if (product.price < 100) {
      console.log('❌ 商品价格低于100 USDT，无法使用中间人托管服务');
      console.log('   解决方案: 商品价格需要至少100 USDT才能使用托管服务');
      return;
    }
    
    // 3. 检查活跃中间人
    console.log('\n3. 检查活跃中间人...');
    const activeMediators = await prisma.user.findMany({
      where: {
        isMediator: true,
        mediatorStatus: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        availableBalance: true,
        mediatorFeeRate: true,
        mediatorReputation: true
      }
    });
    
    console.log(`活跃中间人数量: ${activeMediators.length}`);
    
    if (activeMediators.length === 0) {
      console.log('❌ 没有活跃的中间人');
      return;
    }
    
    // 4. 测试自动分配中间人API
    console.log('\n4. 测试自动分配中间人API...');
    const orderAmount = product.price;
    
    try {
      const response = await fetch('http://localhost:3000/api/mediator/auto-assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderAmount: orderAmount,
          orderId: `test-${Date.now()}`
        })
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log('✅ 自动分配中间人成功');
        console.log(`   分配的中间人: ${data.data.mediator.name}`);
        console.log(`   托管费用: ${data.data.escrowFee} USDT`);
        console.log(`   总费用: ${data.data.totalCost} USDT`);
      } else {
        console.log('❌ 自动分配中间人失败');
        console.log(`   状态码: ${response.status}`);
        console.log(`   错误信息: ${data.error || JSON.stringify(data)}`);
        
        if (data.suggestion) {
          console.log(`   建议: ${data.suggestion}`);
        }
      }
    } catch (error) {
      console.log('❌ API 请求失败');
      console.log(`   错误: ${error.message}`);
      
      if (error.code === 'ECONNREFUSED') {
        console.log('   原因: 无法连接到服务器，请确保应用正在运行');
      }
    }
    
    // 5. 检查订单创建API
    console.log('\n5. 检查订单创建相关...');
    
    // 检查是否有现有订单
    const existingOrders = await prisma.order.findMany({
      where: {
        productId: productId
      },
      select: {
        id: true,
        status: true,
        totalAmount: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });
    
    console.log(`该商品的历史订单数量: ${existingOrders.length}`);
    if (existingOrders.length > 0) {
      console.log('最近的订单:');
      existingOrders.forEach((order, index) => {
        console.log(`  ${index + 1}. 订单ID: ${order.id}`);
        console.log(`     状态: ${order.status}`);
        console.log(`     金额: ${order.totalAmount} USDT`);
        console.log(`     创建时间: ${order.createdAt.toISOString()}`);
      });
    }
    
    // 6. 检查托管订单
    console.log('\n6. 检查托管订单...');
    const escrowOrders = await prisma.escrowOrder.findMany({
      where: {
        orderId: {
          in: existingOrders.map(o => o.id)
        }
      },
      select: {
        id: true,
        orderId: true,
        status: true,
        amount: true,
        mediatorFee: true
      }
    });
    
    console.log(`该商品相关的托管订单数量: ${escrowOrders.length}`);
    if (escrowOrders.length > 0) {
      escrowOrders.forEach((escrow, index) => {
        console.log(`  ${index + 1}. 托管订单ID: ${escrow.id}`);
        console.log(`     关联订单ID: ${escrow.orderId}`);
        console.log(`     状态: ${escrow.status}`);
        console.log(`     金额: ${escrow.amount} USDT`);
        console.log(`     托管费: ${escrow.mediatorFee} USDT`);
      });
    }
    
    // 7. 问题诊断
    console.log('\n=== 问题诊断 ===');
    
    if (product.price < 100) {
      console.log('❌ 主要问题: 商品价格过低');
      console.log('   解决方案: 将商品价格调整至100 USDT以上');
    } else if (activeMediators.length === 0) {
      console.log('❌ 主要问题: 没有活跃的中间人');
      console.log('   解决方案: 需要激活中间人账户');
    } else {
      console.log('✅ 基本条件满足，可能是前端或API问题');
      console.log('   建议: 检查浏览器控制台错误信息');
      console.log('   建议: 检查网络请求是否正常');
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugEscrowOrder();
