const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    console.log('开始创建测试用户...')

    // 检查是否已存在测试用户
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('测试用户已存在:', existingUser.email)
      console.log('用户ID:', existingUser.userId)
      console.log('用户名:', existingUser.name)
      return existingUser
    }

    // 创建密码哈希
    const hashedPassword = await bcrypt.hash('123456', 12)

    // 生成用户ID
    const generateUserId = () => {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let result = 'user-'
      for (let i = 0; i < 12; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }

    // 创建测试用户
    const user = await prisma.user.create({
      data: {
        userId: generateUserId(),
        name: '测试用户',
        email: '<EMAIL>',
        password: hashedPassword,
        city: '北京',
        district: '朝阳区',
        creditScore: 30,
        depositBalance: 0,
        status: 'ACTIVE',
        isGuarantor: false,
        role: 'USER'
      }
    })

    console.log('测试用户创建成功:', {
      id: user.id,
      userId: user.userId,
      email: user.email,
      name: user.name
    })

    return user
  } catch (error) {
    console.error('创建测试用户失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
