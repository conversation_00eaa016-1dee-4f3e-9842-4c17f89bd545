#!/usr/bin/env node

/**
 * 历史点管理工具
 * 用于创建、管理和回档代码历史点
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

class HistoryManager {
  constructor() {
    this.historyFile = path.join(process.cwd(), '.history-points.json')
    this.loadHistory()
  }

  // 加载历史记录
  loadHistory() {
    try {
      if (fs.existsSync(this.historyFile)) {
        this.history = JSON.parse(fs.readFileSync(this.historyFile, 'utf8'))
      } else {
        this.history = { points: [] }
      }
    } catch (error) {
      console.error('❌ 加载历史记录失败:', error.message)
      this.history = { points: [] }
    }
  }

  // 保存历史记录
  saveHistory() {
    try {
      fs.writeFileSync(this.historyFile, JSON.stringify(this.history, null, 2))
    } catch (error) {
      console.error('❌ 保存历史记录失败:', error.message)
    }
  }

  // 创建历史点
  createPoint(description, type = 'manual') {
    try {
      // 检查是否有未提交的更改
      const status = execSync('git status --porcelain', { encoding: 'utf8' })
      
      if (status.trim()) {
        console.log('📝 发现未提交的更改，正在创建提交...')
        execSync('git add .', { stdio: 'inherit' })
        
        const commitMessage = `checkpoint: ${description}`
        execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' })
      }

      // 获取当前提交信息
      const commitHash = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim()
      const branch = execSync('git branch --show-current', { encoding: 'utf8' }).trim()
      const timestamp = new Date().toISOString()

      // 创建历史点记录
      const historyPoint = {
        id: Date.now().toString(),
        hash: commitHash,
        branch: branch,
        description: description,
        type: type,
        timestamp: timestamp,
        files: this.getChangedFiles()
      }

      this.history.points.unshift(historyPoint)
      this.saveHistory()

      console.log('✅ 历史点创建成功!')
      console.log(`   📍 ID: ${historyPoint.id}`)
      console.log(`   🔗 Hash: ${commitHash.substring(0, 8)}`)
      console.log(`   🌿 Branch: ${branch}`)
      console.log(`   📝 Description: ${description}`)
      console.log(`   ⏰ Time: ${new Date(timestamp).toLocaleString()}`)

      return historyPoint

    } catch (error) {
      console.error('❌ 创建历史点失败:', error.message)
      return null
    }
  }

  // 获取更改的文件列表
  getChangedFiles() {
    try {
      const diff = execSync('git diff --name-only HEAD~1 HEAD', { encoding: 'utf8' })
      return diff.trim().split('\n').filter(file => file.length > 0)
    } catch (error) {
      return []
    }
  }

  // 列出所有历史点
  listPoints() {
    console.log('📚 历史点列表:\n')
    
    if (this.history.points.length === 0) {
      console.log('   暂无历史点')
      return
    }

    this.history.points.forEach((point, index) => {
      const date = new Date(point.timestamp).toLocaleString()
      const typeIcon = point.type === 'auto' ? '🤖' : '👤'
      
      console.log(`${index + 1}. ${typeIcon} ${point.description}`)
      console.log(`   📍 ID: ${point.id}`)
      console.log(`   🔗 Hash: ${point.hash.substring(0, 8)}`)
      console.log(`   🌿 Branch: ${point.branch}`)
      console.log(`   ⏰ Time: ${date}`)
      
      if (point.files && point.files.length > 0) {
        console.log(`   📁 Files: ${point.files.slice(0, 3).join(', ')}${point.files.length > 3 ? '...' : ''}`)
      }
      console.log('')
    })
  }

  // 回档到指定历史点
  rollback(pointId) {
    const point = this.history.points.find(p => p.id === pointId)
    
    if (!point) {
      console.error('❌ 未找到指定的历史点')
      return false
    }

    try {
      console.log(`🔄 正在回档到历史点: ${point.description}`)
      console.log(`   🔗 Hash: ${point.hash.substring(0, 8)}`)
      console.log(`   ⏰ Time: ${new Date(point.timestamp).toLocaleString()}`)

      // 检查当前是否有未提交的更改
      const status = execSync('git status --porcelain', { encoding: 'utf8' })
      if (status.trim()) {
        console.log('⚠️  发现未提交的更改，建议先创建历史点')
        return false
      }

      // 创建回档分支
      const rollbackBranch = `rollback-${point.id}-${Date.now()}`
      execSync(`git checkout -b ${rollbackBranch} ${point.hash}`, { stdio: 'inherit' })

      console.log('✅ 回档成功!')
      console.log(`   🌿 新分支: ${rollbackBranch}`)
      console.log(`   💡 提示: 如需返回原分支，使用: git checkout ${point.branch}`)

      return true

    } catch (error) {
      console.error('❌ 回档失败:', error.message)
      return false
    }
  }

  // 删除历史点
  deletePoint(pointId) {
    const index = this.history.points.findIndex(p => p.id === pointId)
    
    if (index === -1) {
      console.error('❌ 未找到指定的历史点')
      return false
    }

    const point = this.history.points[index]
    this.history.points.splice(index, 1)
    this.saveHistory()

    console.log('✅ 历史点已删除!')
    console.log(`   📝 Description: ${point.description}`)
    
    return true
  }

  // 自动创建历史点（在重要操作前）
  autoCheckpoint(operation) {
    const description = `自动检查点 - ${operation}`
    return this.createPoint(description, 'auto')
  }
}

// 命令行接口
function main() {
  const manager = new HistoryManager()
  const args = process.argv.slice(2)
  const command = args[0]

  switch (command) {
    case 'create':
    case 'c':
      const description = args[1] || '手动创建的历史点'
      manager.createPoint(description)
      break

    case 'list':
    case 'l':
      manager.listPoints()
      break

    case 'rollback':
    case 'r':
      const pointId = args[1]
      if (!pointId) {
        console.error('❌ 请指定历史点ID')
        process.exit(1)
      }
      manager.rollback(pointId)
      break

    case 'delete':
    case 'd':
      const deleteId = args[1]
      if (!deleteId) {
        console.error('❌ 请指定要删除的历史点ID')
        process.exit(1)
      }
      manager.deletePoint(deleteId)
      break

    case 'auto':
      const operation = args[1] || '重要操作'
      manager.autoCheckpoint(operation)
      break

    default:
      console.log('📚 历史点管理工具使用说明:')
      console.log('')
      console.log('创建历史点:')
      console.log('  node scripts/history-manager.js create "描述信息"')
      console.log('  node scripts/history-manager.js c "描述信息"')
      console.log('')
      console.log('列出历史点:')
      console.log('  node scripts/history-manager.js list')
      console.log('  node scripts/history-manager.js l')
      console.log('')
      console.log('回档到历史点:')
      console.log('  node scripts/history-manager.js rollback <历史点ID>')
      console.log('  node scripts/history-manager.js r <历史点ID>')
      console.log('')
      console.log('删除历史点:')
      console.log('  node scripts/history-manager.js delete <历史点ID>')
      console.log('  node scripts/history-manager.js d <历史点ID>')
      console.log('')
      console.log('自动检查点:')
      console.log('  node scripts/history-manager.js auto "操作描述"')
      break
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = HistoryManager
