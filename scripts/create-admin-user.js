const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdminUser(email, password) {
  try {
    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: email }
    })

    if (existingUser) {
      console.log(`用户 ${email} 已存在，正在更新为管理员...`)
      
      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 12)
      
      // 更新现有用户
      const updatedUser = await prisma.user.update({
        where: { email: email },
        data: { 
          role: 'ADMIN',
          password: hashedPassword,
          emailVerified: new Date()
        }
      })

      console.log(`✅ 用户 ${email} 已更新为管理员`)
      console.log(`用户信息:`)
      console.log(`- ID: ${updatedUser.id}`)
      console.log(`- 姓名: ${updatedUser.name || '未设置'}`)
      console.log(`- 邮箱: ${updatedUser.email}`)
      console.log(`- 角色: ${updatedUser.role}`)
      console.log(`- 邮箱验证: ${updatedUser.emailVerified ? '已验证' : '未验证'}`)
      
    } else {
      console.log(`创建新用户 ${email}...`)
      
      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 12)
      
      // 创建新用户
      const newUser = await prisma.user.create({
        data: {
          email: email,
          password: hashedPassword,
          role: 'ADMIN',
          name: 'Admin User',
          emailVerified: new Date(),
          creditScore: 100, // 管理员给高信誉分
          status: 'ACTIVE'
        }
      })

      console.log(`✅ 管理员用户 ${email} 创建成功`)
      console.log(`用户信息:`)
      console.log(`- ID: ${newUser.id}`)
      console.log(`- 姓名: ${newUser.name}`)
      console.log(`- 邮箱: ${newUser.email}`)
      console.log(`- 角色: ${newUser.role}`)
      console.log(`- 信誉分: ${newUser.creditScore}`)
      console.log(`- 邮箱验证: ${newUser.emailVerified ? '已验证' : '未验证'}`)
    }
    
  } catch (error) {
    console.error('❌ 创建/更新管理员用户失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 获取命令行参数或使用默认值
const email = process.argv[2] || '<EMAIL>'
const password = process.argv[3] || '123456'

console.log(`正在为邮箱 ${email} 创建/更新管理员账户...`)
createAdminUser(email, password)
