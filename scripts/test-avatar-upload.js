const fs = require('fs')
const path = require('path')
const FormData = require('form-data')
const fetch = require('node-fetch')

async function testAvatarUpload() {
  try {
    console.log('🧪 测试头像上传API...')
    console.log('=' .repeat(50))

    // 测试文件路径
    const testFiles = [
      {
        name: 'test-avatar-200x200.png',
        description: '正常尺寸头像 (200x200)',
        shouldPass: true
      },
      {
        name: 'test-avatar-600x600.png', 
        description: '大尺寸头像 (600x600)',
        shouldPass: true // 应该被自动缩放
      },
      {
        name: 'test-avatar-150x150.png',
        description: '小尺寸头像 (150x150)', 
        shouldPass: false // 应该被拒绝
      }
    ]

    for (const testFile of testFiles) {
      console.log(`\n📤 测试上传: ${testFile.description}`)
      
      const filePath = path.join(process.cwd(), 'public', 'uploads', 'avatar', testFile.name)
      
      if (!fs.existsSync(filePath)) {
        console.log(`❌ 测试文件不存在: ${filePath}`)
        continue
      }

      try {
        // 创建FormData
        const formData = new FormData()
        formData.append('file', fs.createReadStream(filePath))
        formData.append('type', 'avatar')

        // 模拟上传请求
        const response = await fetch('http://localhost:3000/api/upload', {
          method: 'POST',
          body: formData,
          headers: {
            // 注意：不要手动设置Content-Type，让FormData自动设置
            ...formData.getHeaders()
          }
        })

        const result = await response.json()

        if (response.ok) {
          console.log('✅ 上传成功')
          console.log(`   返回URL: ${result.url}`)
          console.log(`   文件名: ${result.fileName}`)
          console.log(`   处理后大小: ${(result.size / 1024).toFixed(2)} KB`)
          console.log(`   原始大小: ${(result.originalSize / 1024).toFixed(2)} KB`)
          
          if (testFile.shouldPass) {
            console.log('✅ 符合预期（应该成功）')
          } else {
            console.log('⚠️  意外成功（预期应该失败）')
          }
        } else {
          console.log('❌ 上传失败')
          console.log(`   错误信息: ${result.error}`)
          console.log(`   状态码: ${response.status}`)
          
          if (!testFile.shouldPass) {
            console.log('✅ 符合预期（应该失败）')
          } else {
            console.log('⚠️  意外失败（预期应该成功）')
          }
        }

      } catch (error) {
        console.log('❌ 请求失败:', error.message)
      }
    }

    console.log('\n🎉 头像上传API测试完成！')
    
    console.log('\n📋 手动测试建议:')
    console.log('1. 在浏览器中访问: http://localhost:3000/profile')
    console.log('2. 点击头像上传区域')
    console.log('3. 选择测试图片进行上传')
    console.log('4. 检查上传后的头像是否在导航栏正确显示')
    console.log('5. 刷新页面验证头像是否持久保存')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/session')
    return response.ok
  } catch (error) {
    return false
  }
}

async function main() {
  const serverRunning = await checkServer()
  
  if (!serverRunning) {
    console.log('❌ 开发服务器未运行')
    console.log('请先运行: npm run dev')
    return
  }
  
  console.log('✅ 开发服务器正在运行')
  await testAvatarUpload()
}

main()
