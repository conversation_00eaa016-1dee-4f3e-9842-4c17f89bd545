#!/usr/bin/env node

/**
 * BitMarket Release 示例脚本
 * 演示如何使用release自动化功能
 */

const ReleaseManager = require('./release.js')

async function runExamples() {
  console.log('🚀 BitMarket Release 示例演示\n')
  
  console.log('📋 可用的release命令示例：\n')
  
  // 示例1：基本patch release
  console.log('1️⃣ 基本patch版本release：')
  console.log('   npm run release')
  console.log('   或: node scripts/release.js')
  console.log('   或: .\\scripts\\release.ps1\n')
  
  // 示例2：minor版本release
  console.log('2️⃣ Minor版本release：')
  console.log('   npm run release:minor')
  console.log('   或: node scripts/release.js --version-type minor')
  console.log('   或: .\\scripts\\release.ps1 -VersionType minor\n')
  
  // 示例3：带消息的release
  console.log('3️⃣ 带release消息的版本发布：')
  console.log('   node scripts/release.js --version-type minor --message "新增用户管理功能"')
  console.log('   或: .\\scripts\\release.ps1 -VersionType minor -ReleaseMessage "新增用户管理功能"\n')
  
  // 示例4：演练模式
  console.log('4️⃣ 演练模式（查看将要执行的操作）：')
  console.log('   npm run release:dry')
  console.log('   或: node scripts/release.js --dry-run')
  console.log('   或: .\\scripts\\release.ps1 -DryRun\n')
  
  // 示例5：跳过测试
  console.log('5️⃣ 跳过测试的快速release：')
  console.log('   node scripts/release.js --skip-tests')
  console.log('   或: .\\scripts\\release.ps1 -SkipTests\n')
  
  // 示例6：只创建包不做git操作
  console.log('6️⃣ 只创建发布包（不做git操作）：')
  console.log('   node scripts/release.js --skip-git')
  console.log('   或: .\\scripts\\release.ps1 -SkipGit\n')
  
  // 示例7：当前版本重新打包
  console.log('7️⃣ 当前版本重新打包：')
  console.log('   npm run release:current')
  console.log('   或: node scripts/release.js --version-type current\n')
  
  console.log('📚 更多信息请查看 RELEASE_GUIDE.md\n')
  
  // 交互式演示
  const readline = require('readline')
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  const response = await new Promise(resolve => {
    rl.question('是否要运行演练模式演示？(y/N): ', resolve)
  })
  
  if (response.toLowerCase() === 'y') {
    console.log('\n🎭 运行演练模式演示...\n')
    
    const releaseManager = new ReleaseManager({
      versionType: 'patch',
      dryRun: true,
      releaseMessage: '演示release功能'
    })
    
    await releaseManager.run()
  } else {
    console.log('\n✅ 演示完成！')
    console.log('💡 提示：使用 npm run release:dry 可以安全地查看release流程')
  }
  
  rl.close()
}

// 显示当前版本信息
function showCurrentVersion() {
  const fs = require('fs')
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  
  console.log('📋 当前项目信息：')
  console.log(`   项目名称: ${packageJson.name}`)
  console.log(`   当前版本: v${packageJson.version}`)
  console.log(`   描述: BitMarket - 基于USDT的去中心化C2C交易平台`)
  console.log('')
}

// 主函数
async function main() {
  try {
    showCurrentVersion()
    await runExamples()
  } catch (error) {
    console.error('❌ 演示失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = { runExamples, showCurrentVersion }
