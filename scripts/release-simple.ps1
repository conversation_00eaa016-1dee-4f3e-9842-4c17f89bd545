# BitMarket 简化Release脚本

param(
    [string]$VersionType = "patch",
    [switch]$DryRun,
    [string]$Message = ""
)

Write-Host "🚀 BitMarket Release 脚本" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# 检查是否在项目根目录
if (!(Test-Path "package.json")) {
    Write-Host "❌ 请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 读取当前版本
$packageJson = Get-Content "package.json" | ConvertFrom-Json
$currentVersion = $packageJson.version

Write-Host "`n📋 当前版本: v$currentVersion" -ForegroundColor Cyan

# 计算新版本
$versionParts = $currentVersion.Split('.')
$major = [int]$versionParts[0]
$minor = [int]$versionParts[1]
$patch = [int]$versionParts[2]

switch ($VersionType) {
    "major" { 
        $major++
        $minor = 0
        $patch = 0
    }
    "minor" { 
        $minor++
        $patch = 0
    }
    "patch" { 
        $patch++
    }
    "current" {
        # 保持当前版本
    }
}

$newVersion = "$major.$minor.$patch"

Write-Host "📋 目标版本: v$newVersion" -ForegroundColor Cyan
Write-Host "📋 版本类型: $VersionType" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "`n🎭 演练模式 - 将要执行的操作：" -ForegroundColor Yellow
    Write-Host "1. 检查Git状态" -ForegroundColor White
    if ($VersionType -ne "current") {
        Write-Host "2. 更新版本号到 v$newVersion" -ForegroundColor White
    }
    Write-Host "3. 运行测试" -ForegroundColor White
    Write-Host "4. 构建项目" -ForegroundColor White
    Write-Host "5. 创建发布包" -ForegroundColor White
    Write-Host "6. 提交Git更改" -ForegroundColor White
    Write-Host "7. 创建版本标签" -ForegroundColor White
    Write-Host "8. 推送到远程仓库" -ForegroundColor White
    Write-Host "9. 生成Release Notes" -ForegroundColor White
    Write-Host "`n✅ 演练完成！使用不带 -DryRun 参数执行实际操作" -ForegroundColor Green
    exit 0
}

# 确认继续
$response = Read-Host "`n是否继续执行release？(y/N)"
if ($response -ne "y" -and $response -ne "Y") {
    Write-Host "❌ Release已取消" -ForegroundColor Yellow
    exit 0
}

try {
    # 1. 检查Git状态
    Write-Host "`n🔄 检查Git状态..." -ForegroundColor Cyan
    $gitStatus = git status --porcelain 2>$null
    if ($gitStatus) {
        Write-Host "⚠️  工作目录有未提交的更改" -ForegroundColor Yellow
        git status --short
    } else {
        Write-Host "✅ 工作目录干净" -ForegroundColor Green
    }

    # 2. 更新版本号
    if ($VersionType -ne "current") {
        Write-Host "`n🔄 更新版本号到 v$newVersion..." -ForegroundColor Cyan
        $packageJson.version = $newVersion
        $packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json" -Encoding UTF8
        Write-Host "✅ 版本号已更新" -ForegroundColor Green
    }

    # 3. 运行测试
    Write-Host "`n🔄 运行测试..." -ForegroundColor Cyan
    try {
        npm test 2>$null
        Write-Host "✅ 测试通过" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  测试失败，但继续执行" -ForegroundColor Yellow
    }

    # 4. 构建项目
    Write-Host "`n🔄 构建项目..." -ForegroundColor Cyan
    npm run build
    Write-Host "✅ 构建完成" -ForegroundColor Green

    # 5. 创建发布包
    Write-Host "`n🔄 创建发布包..." -ForegroundColor Cyan
    node scripts/create-release-package.js
    Write-Host "✅ 发布包创建完成" -ForegroundColor Green

    # 6. Git操作
    Write-Host "`n🔄 执行Git操作..." -ForegroundColor Cyan
    git add .
    git commit -m "chore: release v$newVersion"
    git tag -a "v$newVersion" -m "Release v$newVersion"
    
    Write-Host "🔄 推送到远程仓库..." -ForegroundColor Cyan
    git push origin main
    git push origin "v$newVersion"
    Write-Host "✅ Git操作完成" -ForegroundColor Green

    # 7. 生成Release Notes
    Write-Host "`n🔄 生成Release Notes..." -ForegroundColor Cyan
    $date = Get-Date -Format "yyyy-MM-dd"
    $releaseNotes = @"
# BitMarket v$newVersion Release Notes

## 📋 版本信息
- **版本号**: v$newVersion
- **发布日期**: $date
- **构建状态**: ✅ 通过

## 🚀 主要更新
$Message

## 📦 下载
- [源码包](https://github.com/liusu-ally/bitmarket/releases/download/v$newVersion/bitmarket-v$newVersion-source.zip)

## 🔧 安装说明
1. 下载并解压源码包
2. 运行 ``npm install`` 安装依赖
3. 配置环境变量（参考.env.example）
4. 运行 ``npm run build`` 构建项目
5. 运行 ``npm start`` 启动服务

## 🆘 支持
如有问题，请访问 [GitHub Issues](https://github.com/liusu-ally/bitmarket/issues)
"@

    if (!(Test-Path "release")) {
        New-Item -ItemType Directory -Path "release" -Force | Out-Null
    }
    
    $releaseNotesPath = "release\RELEASE_NOTES_v$newVersion.md"
    $releaseNotes | Out-File $releaseNotesPath -Encoding UTF8
    Write-Host "✅ Release Notes已生成: $releaseNotesPath" -ForegroundColor Green

    # 完成
    Write-Host "`n🎉 Release v$newVersion 完成！" -ForegroundColor Green
    Write-Host "📦 发布包位置: .\release\" -ForegroundColor Cyan
    Write-Host "📝 Release Notes: $releaseNotesPath" -ForegroundColor Cyan
    
    Write-Host "`n📋 下一步操作：" -ForegroundColor Cyan
    Write-Host "1. 检查发布包完整性" -ForegroundColor White
    Write-Host "2. 上传到GitHub Releases" -ForegroundColor White
    Write-Host "3. 通知用户更新" -ForegroundColor White

} catch {
    Write-Host "`n❌ Release失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
