/**
 * 保证金页面测试脚本
 * 测试保证金页面的功能和API接口
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDepositPageAPIs() {
  console.log('🧪 测试保证金页面API接口...')
  console.log('='.repeat(50))

  try {
    // 1. 测试资金余额API
    console.log('\n1️⃣ 测试资金余额API...')
    const balanceResponse = await fetch('http://localhost:3000/api/funds/balance', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (balanceResponse.ok) {
      console.log('✅ 资金余额API响应正常')
    } else {
      console.log('❌ 资金余额API响应异常:', balanceResponse.status)
    }

    // 2. 测试提现记录API
    console.log('\n2️⃣ 测试提现记录API...')
    const withdrawalResponse = await fetch('http://localhost:3000/api/funds/withdrawal', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (withdrawalResponse.ok) {
      console.log('✅ 提现记录API响应正常')
    } else {
      console.log('❌ 提现记录API响应异常:', withdrawalResponse.status)
    }

    // 3. 测试中间人申请API
    console.log('\n3️⃣ 测试中间人申请API...')
    const guarantorResponse = await fetch('http://localhost:3000/api/guarantor/apply', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (guarantorResponse.ok) {
      console.log('✅ 中间人申请API响应正常')
    } else {
      console.log('❌ 中间人申请API响应异常:', guarantorResponse.status)
    }

  } catch (error) {
    console.error('❌ API测试失败:', error.message)
  }
}

async function checkDatabaseTables() {
  console.log('\n🗃️ 检查数据库表结构...')
  console.log('='.repeat(40))

  try {
    // 检查用户表
    const userCount = await prisma.user.count()
    console.log(`✅ User表: ${userCount} 条记录`)

    // 检查资金冻结表
    const freezeCount = await prisma.fundFreeze.count()
    console.log(`✅ FundFreeze表: ${freezeCount} 条记录`)

    // 检查提现表
    const withdrawalCount = await prisma.withdrawal.count()
    console.log(`✅ Withdrawal表: ${withdrawalCount} 条记录`)

    // 检查中间人申请表
    try {
      const guarantorCount = await prisma.guarantorApplication.count()
      console.log(`✅ GuarantorApplication表: ${guarantorCount} 条记录`)
    } catch (error) {
      console.log('⚠️  GuarantorApplication表不存在，需要创建')
    }

  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message)
  }
}

async function testUserBalances() {
  console.log('\n💰 检查用户保证金余额...')
  console.log('='.repeat(40))

  try {
    const users = await prisma.user.findMany({
      select: {
        email: true,
        name: true,
        depositBalance: true,
        creditScore: true,
        isGuarantor: true
      }
    })

    users.forEach(user => {
      console.log(`👤 ${user.name} (${user.email}):`)
      console.log(`   保证金: ${user.depositBalance.toFixed(2)} USDT`)
      console.log(`   信用分: ${user.creditScore}`)
      console.log(`   中间人: ${user.isGuarantor ? '是' : '否'}`)
      console.log('')
    })

    // 统计信息
    const totalBalance = users.reduce((sum, user) => sum + user.depositBalance, 0)
    const guarantorCount = users.filter(user => user.isGuarantor).length
    
    console.log('📊 统计信息:')
    console.log(`   总用户数: ${users.length}`)
    console.log(`   总保证金: ${totalBalance.toFixed(2)} USDT`)
    console.log(`   中间人数: ${guarantorCount}`)

  } catch (error) {
    console.error('❌ 用户余额检查失败:', error.message)
  }
}

async function createTestData() {
  console.log('\n🔧 创建测试数据...')
  console.log('='.repeat(40))

  try {
    // 为用户1增加保证金，使其满足中间人申请条件
    const user1 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (user1) {
      await prisma.user.update({
        where: { id: user1.id },
        data: {
          depositBalance: 1500,
          creditScore: 85
        }
      })
      console.log('✅ 已更新user1的保证金和信用分数')
    }

    // 为用户2设置不同的条件
    const user2 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (user2) {
      await prisma.user.update({
        where: { id: user2.id },
        data: {
          depositBalance: 800,
          creditScore: 75
        }
      })
      console.log('✅ 已更新user2的保证金和信用分数')
    }

    console.log('\n📋 测试场景设置:')
    console.log('   user1: 满足中间人申请条件 (1500 USDT, 85分)')
    console.log('   user2: 不满足申请条件 (800 USDT, 75分)')

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error.message)
  }
}

function showPageFeatures() {
  console.log('\n🎯 保证金页面功能清单:')
  console.log('='.repeat(50))
  
  console.log('\n📊 概览功能:')
  console.log('   ✅ 余额显示 (总余额、可用、冻结、信用分)')
  console.log('   ✅ 中间人状态显示')
  console.log('   ✅ 资金使用情况统计')
  console.log('   ✅ 保证金规则说明')

  console.log('\n💰 充值功能:')
  console.log('   ✅ 充值金额输入')
  console.log('   ✅ 交易哈希记录')
  console.log('   ✅ 备注信息')
  console.log('   ✅ 充值流程说明')

  console.log('\n💸 提现功能:')
  console.log('   ✅ 提现金额输入')
  console.log('   ✅ 钱包地址输入')
  console.log('   ✅ 手续费计算')
  console.log('   ✅ 提现记录查看')

  console.log('\n🛡️ 中间人功能:')
  console.log('   ✅ 申请条件检查')
  console.log('   ✅ 申请表单')
  console.log('   ✅ 中间人权益说明')
  console.log('   ✅ 服务规则说明')

  console.log('\n🔗 API接口:')
  console.log('   ✅ GET /api/funds/balance - 查询余额')
  console.log('   ✅ POST /api/funds/balance - 充值保证金')
  console.log('   ✅ GET /api/funds/withdrawal - 查询提现记录')
  console.log('   ✅ POST /api/funds/withdrawal - 申请提现')
  console.log('   ✅ GET /api/guarantor/apply - 查询申请状态')
  console.log('   ✅ POST /api/guarantor/apply - 申请中间人')
}

function showTestInstructions() {
  console.log('\n🧪 测试指南:')
  console.log('='.repeat(50))
  
  console.log('\n1️⃣ 启动应用:')
  console.log('   npm run dev')
  
  console.log('\n2️⃣ 访问保证金页面:')
  console.log('   http://localhost:3000/deposit')
  
  console.log('\n3️⃣ 登录测试账户:')
  console.log('   <EMAIL> / 123456 (满足中间人条件)')
  console.log('   <EMAIL> / 123456 (不满足条件)')
  console.log('   <EMAIL> / 123456 (管理员)')
  
  console.log('\n4️⃣ 测试功能:')
  console.log('   • 查看余额概览')
  console.log('   • 测试充值功能')
  console.log('   • 测试提现功能')
  console.log('   • 测试中间人申请')
  
  console.log('\n5️⃣ 验证结果:')
  console.log('   • 检查数据库记录')
  console.log('   • 验证API响应')
  console.log('   • 确认UI交互')
}

async function main() {
  console.log('🚀 保证金页面测试工具')
  console.log('='.repeat(50))
  
  try {
    await checkDatabaseTables()
    await testUserBalances()
    await createTestData()
    await testDepositPageAPIs()
    
    showPageFeatures()
    showTestInstructions()
    
    console.log('\n🎉 保证金页面测试准备完成!')
    console.log('现在可以访问 http://localhost:3000/deposit 进行测试')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  testDepositPageAPIs,
  checkDatabaseTables,
  testUserBalances,
  createTestData
}
