/**
 * 测试管理后台新功能
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAdminFeatures() {
  console.log('🧪 测试管理后台新功能...')
  console.log('='.repeat(50))

  try {
    // 1. 测试手续费配置
    console.log('\n1️⃣ 测试手续费配置...')
    
    // 创建交易手续费配置
    const transactionFeeConfig = await prisma.feeConfig.create({
      data: {
        type: 'TRANSACTION',
        name: '标准交易手续费',
        description: '适用于所有交易的标准手续费',
        enabled: true,
        feeType: 'PERCENTAGE',
        feeValue: 0.02, // 2%
        minFee: 1.0,
        maxFee: 100.0,
        createdBy: 'admin-test',
        updatedBy: 'admin-test'
      }
    })

    console.log(`✅ 创建交易手续费配置: ${transactionFeeConfig.id}`)
    console.log(`   费率: ${transactionFeeConfig.feeValue * 100}%`)
    console.log(`   最低: ${transactionFeeConfig.minFee} USDT`)
    console.log(`   最高: ${transactionFeeConfig.maxFee} USDT`)

    // 创建分段提现手续费配置
    const withdrawalFeeConfig = await prisma.feeConfig.create({
      data: {
        type: 'WITHDRAWAL',
        name: '分段提现手续费',
        description: '根据提现金额分段收费',
        enabled: true,
        feeType: 'TIERED',
        tiers: [
          { min: 0, max: 100, rate: 0.01, type: 'PERCENTAGE' },
          { min: 100, max: 1000, rate: 0.008, type: 'PERCENTAGE' },
          { min: 1000, max: 999999, rate: 0.005, type: 'PERCENTAGE' }
        ],
        minFee: 0.5,
        maxFee: 50.0,
        createdBy: 'admin-test',
        updatedBy: 'admin-test'
      }
    })

    console.log(`✅ 创建分段提现手续费配置: ${withdrawalFeeConfig.id}`)
    console.log(`   分段数: ${withdrawalFeeConfig.tiers.length}`)

    // 2. 测试保证金操作记录
    console.log('\n2️⃣ 测试保证金操作记录...')
    
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (testUser) {
      // 创建保证金操作记录
      const depositOperation = await prisma.depositOperation.create({
        data: {
          userId: testUser.id,
          operationType: 'MANUAL_ADD',
          amount: 100,
          balanceBefore: testUser.depositBalance,
          balanceAfter: testUser.depositBalance + 100,
          reason: '测试手动增加保证金',
          notes: '管理后台功能测试',
          operatorId: testUser.id // 临时使用同一用户作为操作员
        }
      })

      console.log(`✅ 创建保证金操作记录: ${depositOperation.id}`)
      console.log(`   操作类型: ${depositOperation.operationType}`)
      console.log(`   操作金额: ${depositOperation.amount} USDT`)
      console.log(`   余额变化: ${depositOperation.balanceBefore} → ${depositOperation.balanceAfter}`)
    }

    // 3. 测试手续费计算
    console.log('\n3️⃣ 测试手续费计算...')
    
    const testAmounts = [50, 150, 500, 1500]
    
    console.log('💰 交易手续费计算:')
    for (const amount of testAmounts) {
      const fee = Math.max(
        transactionFeeConfig.minFee,
        Math.min(
          transactionFeeConfig.maxFee,
          amount * transactionFeeConfig.feeValue
        )
      )
      console.log(`   ${amount} USDT → 手续费: ${fee.toFixed(2)} USDT`)
    }

    console.log('\n💸 提现手续费计算 (分段):')
    for (const amount of testAmounts) {
      let fee = 0
      const tiers = withdrawalFeeConfig.tiers
      
      // 找到适用的分段
      const tier = tiers.find(t => amount >= t.min && amount < t.max)
      if (tier) {
        fee = amount * tier.rate
      }
      
      // 应用最低和最高限制
      fee = Math.max(withdrawalFeeConfig.minFee, Math.min(withdrawalFeeConfig.maxFee, fee))
      
      console.log(`   ${amount} USDT → 手续费: ${fee.toFixed(2)} USDT`)
    }

    // 4. 测试数据统计
    console.log('\n4️⃣ 测试数据统计...')
    
    const stats = await Promise.all([
      prisma.feeConfig.count(),
      prisma.depositOperation.count(),
      prisma.user.aggregate({
        _sum: { depositBalance: true },
        _avg: { depositBalance: true },
        _count: { id: true }
      }),
      prisma.depositRecord.groupBy({
        by: ['status'],
        _count: { id: true },
        _sum: { amount: true }
      }),
      prisma.withdrawal.groupBy({
        by: ['status'],
        _count: { id: true },
        _sum: { amount: true }
      })
    ])

    console.log('📊 系统统计:')
    console.log(`   手续费配置: ${stats[0]} 个`)
    console.log(`   保证金操作记录: ${stats[1]} 条`)
    console.log(`   用户总数: ${stats[2]._count.id}`)
    console.log(`   保证金总额: ${stats[2]._sum.depositBalance?.toFixed(2) || 0} USDT`)
    console.log(`   平均保证金: ${stats[2]._avg.depositBalance?.toFixed(2) || 0} USDT`)

    console.log('\n📈 充值统计:')
    stats[3].forEach(stat => {
      console.log(`   ${stat.status}: ${stat._count.id} 笔, 总额 ${stat._sum.amount?.toFixed(2) || 0} USDT`)
    })

    console.log('\n📉 提现统计:')
    stats[4].forEach(stat => {
      console.log(`   ${stat.status}: ${stat._count.id} 笔, 总额 ${stat._sum.amount?.toFixed(2) || 0} USDT`)
    })

    console.log('\n🎉 管理后台新功能测试完成!')

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

async function showAdminFeaturesSummary() {
  console.log('\n📋 管理后台新功能总结:')
  console.log('='.repeat(50))
  
  console.log('\n🔄 新增功能:')
  console.log('✅ 用户保证金管理页面 (/admin/deposits)')
  console.log('✅ 平台手续费管理页面 (/admin/fees)')
  console.log('✅ 保证金手动调整功能')
  console.log('✅ 充值/提现申请审核功能')
  console.log('✅ 手续费配置管理功能')
  console.log('✅ 分段式手续费设置')
  
  console.log('\n💰 保证金管理功能:')
  console.log('• 用户保证金余额查看和管理')
  console.log('• 手动增加/减少保证金')
  console.log('• 充值申请审核 (批准/拒绝)')
  console.log('• 提现申请审核 (批准/拒绝/完成)')
  console.log('• 完整的操作历史记录')
  console.log('• 搜索和筛选功能')
  
  console.log('\n⚙️ 手续费管理功能:')
  console.log('• 交易手续费配置')
  console.log('• 提现手续费配置')
  console.log('• 支持百分比、固定金额、分段计费')
  console.log('• 最低/最高手续费限制')
  console.log('• 支付方式和用户类型差异化设置')
  console.log('• 配置版本控制和历史追踪')
  console.log('• 配置导入/导出功能')
  
  console.log('\n🛡️ 安全特性:')
  console.log('• 管理员权限验证')
  console.log('• 操作日志记录')
  console.log('• 数据完整性检查')
  console.log('• 原子性事务操作')
  
  console.log('\n🧪 测试地址:')
  console.log('管理后台首页: http://localhost:3000/admin')
  console.log('保证金管理: http://localhost:3000/admin/deposits')
  console.log('手续费管理: http://localhost:3000/admin/fees')
  console.log('测试账户: <EMAIL> / 123456')
}

async function main() {
  console.log('🚀 BitMarket 管理后台新功能测试')
  console.log('='.repeat(50))
  
  try {
    await testAdminFeatures()
    showAdminFeaturesSummary()
    
    console.log('\n🎯 测试完成!')
    console.log('现在可以访问管理后台体验新的保证金和手续费管理功能')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  testAdminFeatures
}
