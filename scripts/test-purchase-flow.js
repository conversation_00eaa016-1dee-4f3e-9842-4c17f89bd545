const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestData() {
  console.log('🚀 创建购买流程测试数据...')
  
  try {
    // 1. 确保用户存在并设置余额
    console.log('👤 设置测试用户...')
    
    // 买家用户
    const buyer = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        depositBalance: 2000 // 设置足够的余额
      },
      create: {
        email: '<EMAIL>',
        name: '测试买家',
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        role: 'USER',
        depositBalance: 2000,
        creditScore: 50
      }
    })
    
    // 卖家用户（管理员）
    const seller = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        depositBalance: 1000
      },
      create: {
        email: '<EMAIL>',
        name: '管理员',
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        role: 'ADMIN',
        depositBalance: 1000,
        creditScore: 100
      }
    })
    
    console.log(`✅ 买家用户: ${buyer.email}, 余额: ${buyer.depositBalance} USDT`)
    console.log(`✅ 卖家用户: ${seller.email}, 余额: ${seller.depositBalance} USDT`)
    
    // 2. 创建测试商品
    console.log('📦 创建测试商品...')
    
    const testProducts = [
      {
        title: '测试商品 - iPhone 15 Pro',
        description: '全新iPhone 15 Pro，256GB，深空黑色。包装完整，配件齐全。',
        price: 899.99,
        stock: 3,
        category: '数码产品',
        condition: 'NEW',
        city: '北京市',
        district: '朝阳区',
        sellerId: seller.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED'
      },
      {
        title: '测试商品 - MacBook Air M2',
        description: '苹果MacBook Air M2芯片，13英寸，8GB内存，256GB存储。',
        price: 1199.99,
        stock: 2,
        category: '数码产品',
        condition: 'NEW',
        city: '上海市',
        district: '浦东新区',
        sellerId: seller.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED'
      },
      {
        title: '测试商品 - AirPods Pro 2',
        description: '苹果AirPods Pro 第二代，主动降噪，无线充电盒。',
        price: 249.99,
        stock: 5,
        category: '数码产品',
        condition: 'NEW',
        city: '广州市',
        district: '天河区',
        sellerId: seller.id,
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED'
      }
    ]
    
    const createdProducts = []
    for (const productData of testProducts) {
      // 检查商品是否已存在
      const existingProduct = await prisma.product.findFirst({
        where: {
          title: productData.title,
          sellerId: productData.sellerId
        }
      })

      let product
      if (existingProduct) {
        product = await prisma.product.update({
          where: { id: existingProduct.id },
          data: productData
        })
      } else {
        product = await prisma.product.create({
          data: productData
        })
      }
      createdProducts.push(product)
    }
    
    console.log(`✅ 创建了 ${createdProducts.length} 个测试商品`)
    
    // 3. 为买家创建测试地址
    console.log('📍 创建测试地址...')
    
    // 检查地址是否已存在
    const existingAddress = await prisma.address.findFirst({
      where: {
        userId: buyer.id,
        name: '张三'
      }
    })

    let testAddress
    if (existingAddress) {
      testAddress = await prisma.address.update({
        where: { id: existingAddress.id },
        data: {
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '三里屯街道工体北路1号',
          remark: '家',
          isDefault: true
        }
      })
    } else {
      testAddress = await prisma.address.create({
        data: {
          userId: buyer.id,
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '三里屯街道工体北路1号',
          remark: '家',
          isDefault: true
        }
      })
    }
    
    console.log(`✅ 创建测试地址: ${testAddress.name} - ${testAddress.province}${testAddress.city}${testAddress.district}`)
    
    console.log('\n🎉 测试数据创建完成！')
    console.log('\n📋 测试数据摘要:')
    console.log(`- 买家用户: ${buyer.email} (余额: ${buyer.depositBalance} USDT)`)
    console.log(`- 卖家用户: ${seller.email} (余额: ${seller.depositBalance} USDT)`)
    console.log(`- 测试商品: ${createdProducts.length} 个`)
    console.log(`- 测试地址: 1 个`)
    console.log('\n🛒 现在可以开始测试购买流程了！')
    
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
createTestData()
