const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testPaymentsAPI() {
  try {
    console.log('🔍 测试支付管理API数据格式...')
    
    // 模拟API查询逻辑
    const orderWhere = {}
    
    const orders = await prisma.order.findMany({
      where: orderWhere,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            price: true
          }
        },
        buyer: {
          select: {
            id: true,
            userId: true,
            name: true,
            email: true,
            creditScore: true
          }
        },
        seller: {
          select: {
            id: true,
            userId: true,
            name: true,
            email: true,
            creditScore: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    // 转换为支付记录格式（模拟API逻辑）
    const payments = orders.map(order => ({
      id: order.id,
      amount: order.totalAmount,
      paymentMethod: 'USDT_TRC20', // 默认支付方式
      status: order.paymentConfirmed ? 'CONFIRMED' :
              order.status === 'CANCELLED' ? 'REJECTED' :
              order.status === 'REFUND_REQUESTED' ? 'REFUNDED' : 'PENDING',
      txHash: order.paymentTxHash,
      refundAmount: null,
      refundTxHash: null,
      adminNotes: null,
      createdAt: order.createdAt,
      confirmedAt: order.paymentConfirmed ? order.updatedAt : null,
      refundedAt: order.status === 'REFUND_REQUESTED' ? order.updatedAt : null,
      order: order
    }))

    console.log('\n📋 API返回的支付记录格式:')
    console.log('=' .repeat(60))
    
    payments.forEach((payment, index) => {
      console.log(`${index + 1}. 支付记录:`)
      console.log(`   ID: ${payment.id}`)
      console.log(`   金额: ${payment.amount} USDT`)
      console.log(`   支付方式: ${payment.paymentMethod}`)
      console.log(`   状态: ${payment.status}`)
      console.log(`   交易哈希: ${payment.txHash || '无'}`)
      console.log(`   创建时间: ${payment.createdAt.toLocaleString()}`)
      console.log(`   确认时间: ${payment.confirmedAt ? payment.confirmedAt.toLocaleString() : '未确认'}`)
      console.log(`   订单状态: ${payment.order.status}`)
      console.log(`   支付确认: ${payment.order.paymentConfirmed ? '是' : '否'}`)
      console.log(`   商品: ${payment.order.product.title}`)
      console.log(`   买家: ${payment.order.buyer.email}`)
      console.log('-'.repeat(40))
    })

    // 测试状态映射
    console.log('\n🔍 状态映射测试:')
    console.log('=' .repeat(40))
    
    const statusMap = {
      'PENDING': '待确认',
      'CONFIRMED': '已确认',
      'REJECTED': '已拒绝',
      'REFUNDED': '已退款'
    }
    
    payments.forEach((payment, index) => {
      const statusText = statusMap[payment.status] || payment.status
      console.log(`${index + 1}. ${payment.id} -> ${payment.status} (${statusText})`)
    })

  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testPaymentsAPI()
  .then(() => {
    console.log('\n🎉 测试完成！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 测试失败:', error)
    process.exit(1)
  })
