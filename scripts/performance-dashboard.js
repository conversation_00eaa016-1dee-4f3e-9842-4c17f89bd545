#!/usr/bin/env node

const fs = require('fs/promises')
const path = require('path')
const http = require('http')
const { performance } = require('perf_hooks')

class PerformanceDashboard {
  constructor(port = 3001) {
    this.port = port
    this.metrics = new Map()
    this.alerts = []
    this.thresholds = {
      responseTime: 100, // ms
      memoryUsage: 80, // %
      errorRate: 5, // %
      cacheHitRate: 80 // %
    }
    this.server = null
  }

  // 启动仪表板服务器
  async start() {
    this.server = http.createServer((req, res) => {
      this.handleRequest(req, res)
    })

    this.server.listen(this.port, () => {
      console.log(`🚀 性能监控仪表板启动: http://localhost:${this.port}`)
    })

    // 定期收集性能数据
    setInterval(() => this.collectMetrics(), 5000)
    
    // 定期检查告警
    setInterval(() => this.checkAlerts(), 10000)
  }

  // 停止仪表板
  async stop() {
    if (this.server) {
      this.server.close()
      console.log('📊 性能监控仪表板已停止')
    }
  }

  // 处理HTTP请求
  async handleRequest(req, res) {
    const url = new URL(req.url, `http://localhost:${this.port}`)
    
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Content-Type', 'application/json')

    try {
      switch (url.pathname) {
        case '/':
          res.setHeader('Content-Type', 'text/html')
          res.end(await this.generateDashboardHTML())
          break
        case '/api/metrics':
          res.end(JSON.stringify(this.getMetricsData()))
          break
        case '/api/alerts':
          res.end(JSON.stringify(this.alerts))
          break
        case '/api/performance-report':
          res.end(JSON.stringify(await this.loadPerformanceReport()))
          break
        case '/api/system-stats':
          res.end(JSON.stringify(this.getSystemStats()))
          break
        default:
          res.statusCode = 404
          res.end(JSON.stringify({ error: 'Not found' }))
      }
    } catch (error) {
      res.statusCode = 500
      res.end(JSON.stringify({ error: error.message }))
    }
  }

  // 收集性能指标
  collectMetrics() {
    const timestamp = Date.now()
    const memUsage = process.memoryUsage()
    
    const metrics = {
      timestamp,
      memory: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        usage: (memUsage.heapUsed / memUsage.heapTotal) * 100
      },
      cpu: {
        usage: process.cpuUsage()
      },
      uptime: process.uptime(),
      eventLoop: {
        delay: this.measureEventLoopDelay()
      }
    }

    this.metrics.set(timestamp, metrics)
    
    // 只保留最近1小时的数据
    const oneHourAgo = timestamp - 60 * 60 * 1000
    for (const [time] of this.metrics) {
      if (time < oneHourAgo) {
        this.metrics.delete(time)
      }
    }
  }

  // 测量事件循环延迟
  measureEventLoopDelay() {
    const start = performance.now()
    setImmediate(() => {
      const delay = performance.now() - start
      return delay
    })
    return 0 // 简化实现
  }

  // 检查告警
  checkAlerts() {
    const latestMetrics = Array.from(this.metrics.values()).slice(-1)[0]
    if (!latestMetrics) return

    const alerts = []

    // 内存使用告警
    if (latestMetrics.memory.usage > this.thresholds.memoryUsage) {
      alerts.push({
        type: 'warning',
        message: `内存使用率过高: ${latestMetrics.memory.usage.toFixed(1)}%`,
        timestamp: Date.now(),
        threshold: this.thresholds.memoryUsage
      })
    }

    // 添加新告警
    alerts.forEach(alert => {
      this.alerts.unshift(alert)
    })

    // 只保留最近50条告警
    this.alerts = this.alerts.slice(0, 50)
  }

  // 获取指标数据
  getMetricsData() {
    const recentMetrics = Array.from(this.metrics.entries())
      .slice(-60) // 最近60个数据点
      .map(([timestamp, data]) => ({
        timestamp,
        ...data
      }))

    return {
      current: recentMetrics[recentMetrics.length - 1] || null,
      history: recentMetrics,
      summary: this.calculateSummary(recentMetrics)
    }
  }

  // 计算摘要统计
  calculateSummary(metrics) {
    if (metrics.length === 0) return null

    const memoryUsages = metrics.map(m => m.memory.usage)
    const heapUsed = metrics.map(m => m.memory.heapUsed)

    return {
      avgMemoryUsage: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
      maxMemoryUsage: Math.max(...memoryUsages),
      avgHeapUsed: heapUsed.reduce((a, b) => a + b, 0) / heapUsed.length,
      maxHeapUsed: Math.max(...heapUsed),
      dataPoints: metrics.length
    }
  }

  // 获取系统统计
  getSystemStats() {
    return {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      pid: process.pid,
      cwd: process.cwd(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    }
  }

  // 加载性能报告
  async loadPerformanceReport() {
    try {
      const reportPath = path.join(process.cwd(), 'test-results/performance/performance-report.json')
      const data = await fs.readFile(reportPath, 'utf-8')
      return JSON.parse(data)
    } catch (error) {
      return { error: '无法加载性能报告', message: error.message }
    }
  }

  // 生成仪表板HTML
  async generateDashboardHTML() {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 性能监控仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: #f5f5f5; 
            color: #333;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 20px; 
            text-align: center; 
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { 
            background: white; 
            border-radius: 8px; 
            padding: 20px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .metric { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 10px 0; 
            border-bottom: 1px solid #eee; 
        }
        .metric:last-child { border-bottom: none; }
        .metric-value { font-weight: bold; color: #667eea; }
        .alert { 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 4px; 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
        }
        .alert.warning { background: #f8d7da; border-color: #f5c6cb; }
        .chart-container { height: 300px; margin: 20px 0; }
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 8px; 
        }
        .status-good { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .refresh-btn { 
            background: #667eea; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 10px 0; 
        }
        .refresh-btn:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ BitMarket 性能监控仪表板</h1>
        <p>实时性能监控和分析</p>
    </div>
    
    <div class="container">
        <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
        
        <div class="grid">
            <div class="card">
                <h3>📊 系统状态</h3>
                <div id="system-status">
                    <div class="metric">
                        <span>系统状态</span>
                        <span><span class="status-indicator status-good"></span>运行正常</span>
                    </div>
                    <div class="metric">
                        <span>运行时间</span>
                        <span class="metric-value" id="uptime">-</span>
                    </div>
                    <div class="metric">
                        <span>Node.js版本</span>
                        <span class="metric-value" id="node-version">-</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>💾 内存使用</h3>
                <div id="memory-stats">
                    <div class="metric">
                        <span>堆内存使用</span>
                        <span class="metric-value" id="heap-used">-</span>
                    </div>
                    <div class="metric">
                        <span>堆内存总量</span>
                        <span class="metric-value" id="heap-total">-</span>
                    </div>
                    <div class="metric">
                        <span>使用率</span>
                        <span class="metric-value" id="memory-usage">-</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🚨 告警信息</h3>
                <div id="alerts-container">
                    <p>暂无告警</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📈 性能趋势</h3>
            <div class="chart-container">
                <canvas id="performance-chart"></canvas>
            </div>
        </div>
        
        <div class="card">
            <h3>🎯 性能基准</h3>
            <div id="benchmark-results">
                <p>加载中...</p>
            </div>
        </div>
    </div>

    <script>
        let performanceChart = null;
        
        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                return await response.json();
            } catch (error) {
                console.error('获取数据失败:', error);
                return null;
            }
        }
        
        async function updateSystemStatus() {
            const stats = await fetchData('/api/system-stats');
            if (!stats) return;
            
            document.getElementById('uptime').textContent = formatUptime(stats.uptime);
            document.getElementById('node-version').textContent = stats.nodeVersion;
            
            const memUsage = stats.memoryUsage;
            document.getElementById('heap-used').textContent = formatBytes(memUsage.heapUsed);
            document.getElementById('heap-total').textContent = formatBytes(memUsage.heapTotal);
            document.getElementById('memory-usage').textContent = 
                ((memUsage.heapUsed / memUsage.heapTotal) * 100).toFixed(1) + '%';
        }
        
        async function updateAlerts() {
            const alerts = await fetchData('/api/alerts');
            if (!alerts) return;
            
            const container = document.getElementById('alerts-container');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p>暂无告警</p>';
                return;
            }
            
            container.innerHTML = alerts.slice(0, 5).map(alert => 
                \`<div class="alert \${alert.type}">
                    <strong>\${alert.type.toUpperCase()}</strong>: \${alert.message}
                    <br><small>\${new Date(alert.timestamp).toLocaleString()}</small>
                </div>\`
            ).join('');
        }
        
        async function updatePerformanceChart() {
            const metrics = await fetchData('/api/metrics');
            if (!metrics || !metrics.history) return;
            
            const ctx = document.getElementById('performance-chart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }
            
            const labels = metrics.history.map(m => new Date(m.timestamp).toLocaleTimeString());
            const memoryData = metrics.history.map(m => m.memory.usage);
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '内存使用率 (%)',
                        data: memoryData,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        async function updateBenchmarkResults() {
            const report = await fetchData('/api/performance-report');
            const container = document.getElementById('benchmark-results');
            
            if (!report || report.error) {
                container.innerHTML = '<p>暂无性能基准数据</p>';
                return;
            }
            
            if (report.benchmarks) {
                const benchmarks = Object.entries(report.benchmarks).slice(0, 5);
                container.innerHTML = benchmarks.map(([name, data]) => 
                    \`<div class="metric">
                        <span>\${name}</span>
                        <span class="metric-value">\${data.summary.avgDuration.toFixed(2)}ms</span>
                    </div>\`
                ).join('');
            }
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return \`\${hours}h \${minutes}m\`;
        }
        
        async function refreshData() {
            await Promise.all([
                updateSystemStatus(),
                updateAlerts(),
                updatePerformanceChart(),
                updateBenchmarkResults()
            ]);
        }
        
        // 初始化
        refreshData();
        
        // 定期刷新
        setInterval(refreshData, 10000);
    </script>
</body>
</html>
    `
  }
}

// 启动仪表板
if (require.main === module) {
  const dashboard = new PerformanceDashboard()
  
  dashboard.start().catch(error => {
    console.error('启动仪表板失败:', error)
    process.exit(1)
  })
  
  // 优雅关闭
  process.on('SIGINT', async () => {
    console.log('\n正在关闭性能监控仪表板...')
    await dashboard.stop()
    process.exit(0)
  })
}

module.exports = { PerformanceDashboard }
