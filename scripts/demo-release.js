#!/usr/bin/env node

/**
 * BitMarket Release 演示脚本
 * 展示完整的release流程，包括版本更新、打包、git操作等
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class ReleaseDemo {
  constructor() {
    this.projectRoot = process.cwd()
  }

  log(message, color = 'white') {
    const colors = {
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      cyan: '\x1b[36m',
      white: '\x1b[37m',
      reset: '\x1b[0m'
    }
    console.log(`${colors[color]}${message}${colors.reset}`)
  }

  step(message) {
    this.log(`\n🔄 ${message}`, 'cyan')
  }

  success(message) {
    this.log(`✅ ${message}`, 'green')
  }

  warning(message) {
    this.log(`⚠️  ${message}`, 'yellow')
  }

  error(message) {
    this.log(`❌ ${message}`, 'red')
  }

  // 显示当前项目状态
  showProjectStatus() {
    this.log('🚀 BitMarket Release 演示', 'green')
    this.log('==========================', 'green')
    
    // 读取项目信息
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    
    this.log('\n📋 项目信息:', 'cyan')
    this.log(`   项目名称: ${packageJson.name}`, 'white')
    this.log(`   当前版本: v${packageJson.version}`, 'white')
    this.log(`   描述: BitMarket - 基于USDT的去中心化C2C交易平台`, 'white')
    
    // 检查Git状态
    try {
      const gitBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim()
      const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' }).trim()
      
      this.log('\n📋 Git状态:', 'cyan')
      this.log(`   当前分支: ${gitBranch}`, 'white')
      this.log(`   工作目录: ${gitStatus ? '有未提交更改' : '干净'}`, 'white')
    } catch (error) {
      this.warning('无法获取Git状态')
    }
  }

  // 演示版本号计算
  demonstrateVersioning() {
    this.step('演示版本号管理')
    
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const currentVersion = packageJson.version
    
    const versionTypes = ['patch', 'minor', 'major']
    
    this.log(`\n当前版本: v${currentVersion}`, 'white')
    this.log('可选的版本升级:', 'white')
    
    versionTypes.forEach(type => {
      const newVersion = this.calculateNextVersion(currentVersion, type)
      this.log(`   ${type}: v${currentVersion} → v${newVersion}`, 'white')
    })
  }

  // 计算下一个版本号
  calculateNextVersion(currentVersion, type) {
    const parts = currentVersion.split('.').map(Number)
    let [major, minor, patch] = parts

    switch (type) {
      case 'major':
        major++
        minor = 0
        patch = 0
        break
      case 'minor':
        minor++
        patch = 0
        break
      case 'patch':
        patch++
        break
    }

    return `${major}.${minor}.${patch}`
  }

  // 演示打包功能
  async demonstratePackaging() {
    this.step('演示项目打包功能')
    
    this.log('正在创建发布包...', 'white')
    
    try {
      // 运行打包脚本
      execSync('node scripts/create-release-package.js', { 
        stdio: 'inherit',
        cwd: this.projectRoot 
      })
      
      this.success('发布包创建成功')
      
      // 显示生成的文件
      const releaseDir = path.join(this.projectRoot, 'release')
      if (fs.existsSync(releaseDir)) {
        const files = fs.readdirSync(releaseDir)
        this.log('\n📦 生成的文件:', 'cyan')
        files.forEach(file => {
          const filePath = path.join(releaseDir, file)
          const stats = fs.statSync(filePath)
          if (stats.isFile()) {
            const sizeMB = (stats.size / (1024 * 1024)).toFixed(2)
            this.log(`   📄 ${file} (${sizeMB} MB)`, 'white')
          } else {
            this.log(`   📁 ${file}/`, 'white')
          }
        })
      }
      
    } catch (error) {
      this.error(`打包失败: ${error.message}`)
    }
  }

  // 演示Git操作（模拟）
  demonstrateGitOperations() {
    this.step('演示Git操作（模拟）')
    
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const version = packageJson.version
    
    this.log('将要执行的Git操作:', 'white')
    this.log(`   1. git add .`, 'white')
    this.log(`   2. git commit -m "chore: release v${version}"`, 'white')
    this.log(`   3. git tag -a "v${version}" -m "Release v${version}"`, 'white')
    this.log(`   4. git push origin main`, 'white')
    this.log(`   5. git push origin "v${version}"`, 'white')
    
    this.warning('注意：这只是演示，实际的Git操作需要在真实的release中执行')
  }

  // 演示Release Notes生成
  demonstrateReleaseNotes() {
    this.step('演示Release Notes生成')
    
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const version = packageJson.version
    const date = new Date().toISOString().split('T')[0]
    
    const releaseNotes = `# BitMarket v${version} Release Notes

## 📋 版本信息
- **版本号**: v${version}
- **发布日期**: ${date}
- **构建状态**: ✅ 通过

## 🚀 主要更新
- 保证金机制更新 - 优化中间人担保系统
- 页面系统更新 - 全面改善用户界面
- 导航栏重构 - logo图片+BitMarket文字组合
- UI组件库 - 完整的可复用组件系统

## 📦 下载
- [源码包](https://github.com/liusu-ally/bitmarket/releases/download/v${version}/bitmarket-v${version}-source.zip)

## 🔧 安装说明
1. 下载并解压源码包
2. 运行 \`npm install\` 安装依赖
3. 配置环境变量（参考.env.example）
4. 运行 \`npm run build\` 构建项目
5. 运行 \`npm start\` 启动服务`

    // 保存到文件
    const releaseDir = path.join(this.projectRoot, 'release')
    if (!fs.existsSync(releaseDir)) {
      fs.mkdirSync(releaseDir, { recursive: true })
    }
    
    const releaseNotesPath = path.join(releaseDir, `RELEASE_NOTES_v${version}_demo.md`)
    fs.writeFileSync(releaseNotesPath, releaseNotes)
    
    this.success(`Release Notes已生成: ${releaseNotesPath}`)
    this.log('\n📝 Release Notes预览:', 'cyan')
    this.log(releaseNotes.split('\n').slice(0, 10).join('\n'), 'white')
    this.log('...', 'white')
  }

  // 显示可用的release命令
  showAvailableCommands() {
    this.step('可用的Release命令')
    
    this.log('📋 npm scripts:', 'cyan')
    this.log('   npm run release              # 基本patch版本release', 'white')
    this.log('   npm run release:minor        # minor版本release', 'white')
    this.log('   npm run release:major        # major版本release', 'white')
    this.log('   npm run release:current      # 当前版本重新打包', 'white')
    this.log('   npm run release:dry          # 演练模式', 'white')
    this.log('   npm run package:create       # 只创建发布包', 'white')
    
    this.log('\n📋 直接命令:', 'cyan')
    this.log('   node scripts/release.js --version-type minor', 'white')
    this.log('   node scripts/release.js --dry-run', 'white')
    this.log('   node scripts/release.js --skip-tests', 'white')
    this.log('   node scripts/release.js --skip-git', 'white')
    
    this.log('\n📋 PowerShell命令 (Windows):', 'cyan')
    this.log('   .\\scripts\\release-simple.ps1 -VersionType minor', 'white')
    this.log('   .\\scripts\\release-simple.ps1 -DryRun', 'white')
  }

  // 主演示函数
  async run() {
    try {
      this.showProjectStatus()
      this.demonstrateVersioning()
      await this.demonstratePackaging()
      this.demonstrateGitOperations()
      this.demonstrateReleaseNotes()
      this.showAvailableCommands()
      
      this.log('\n🎉 Release演示完成！', 'green')
      this.log('💡 提示：使用 npm run release:dry 可以安全地测试完整的release流程', 'cyan')
      this.log('📚 更多信息请查看 RELEASE_GUIDE.md', 'cyan')
      
    } catch (error) {
      this.error(`演示失败: ${error.message}`)
      process.exit(1)
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const demo = new ReleaseDemo()
  demo.run()
}

module.exports = ReleaseDemo
