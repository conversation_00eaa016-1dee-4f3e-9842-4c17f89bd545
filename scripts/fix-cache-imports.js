#!/usr/bin/env node

/**
 * 批量修复缓存导入，使用回退版本
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 修复缓存导入...')

// 需要修复的文件列表
const filesToFix = [
  'scripts/clear-cache.js',
  'scripts/health-check.js',
  'scripts/performance-setup.js',
]

// 替换函数
function replaceInFile(filePath, searchPattern, replacement) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`)
      return false
    }

    let content = fs.readFileSync(filePath, 'utf8')
    const originalContent = content
    
    // 执行替换
    content = content.replace(searchPattern, replacement)
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content)
      console.log(`✅ 已修复: ${filePath}`)
      return true
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`)
      return false
    }
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message)
    return false
  }
}

// 主修复函数
function fixCacheImports() {
  let fixedCount = 0
  
  // 修复 require('../lib/cache') 为 require('../lib/cache-fallback')
  const searchPattern = /require\(['"]\.\.\/lib\/cache['"]\)/g
  const replacement = "require('../lib/cache-fallback')"
  
  filesToFix.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath)
    if (replaceInFile(fullPath, searchPattern, replacement)) {
      fixedCount++
    }
  })
  
  console.log(`\n📊 修复完成: ${fixedCount}/${filesToFix.length} 个文件`)
  
  if (fixedCount > 0) {
    console.log('\n✅ 所有缓存导入已修复为使用回退版本')
    console.log('ℹ️  现在应用将使用内存缓存而不是Redis')
  }
}

// 运行修复
if (require.main === module) {
  fixCacheImports()
}

module.exports = { fixCacheImports }
