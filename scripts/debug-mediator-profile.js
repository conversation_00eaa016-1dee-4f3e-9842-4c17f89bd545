const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugMediatorProfile() {
  console.log('🔍 调试中间人个人资料显示问题...\n')

  try {
    // 1. 检查所有用户的中间人状态
    console.log('1. 检查所有用户的中间人状态...')
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        isMediator: true,
        mediatorStatus: true,
        mediatorFeeRate: true,
        mediatorReputation: true,
        mediatorVerifiedAt: true,
        bnbWalletVerified: true,
        mediatorExperience: true,
        mediatorIntroduction: true,
        mediatorSuccessRate: true,
        mediatorTotalOrders: true,
        depositBalance: true
      }
    })

    console.log(`✅ 找到 ${users.length} 个用户`)
    users.forEach((user, index) => {
      console.log(`\n   ${index + 1}. ${user.name} (${user.email})`)
      console.log(`      ID: ${user.id}`)
      console.log(`      isMediator: ${user.isMediator}`)
      console.log(`      mediatorStatus: ${user.mediatorStatus}`)
      console.log(`      mediatorFeeRate: ${user.mediatorFeeRate}`)
      console.log(`      mediatorReputation: ${user.mediatorReputation}`)
      console.log(`      mediatorSuccessRate: ${user.mediatorSuccessRate}`)
      console.log(`      mediatorTotalOrders: ${user.mediatorTotalOrders}`)
      console.log(`      bnbWalletVerified: ${user.bnbWalletVerified}`)
      console.log(`      mediatorVerifiedAt: ${user.mediatorVerifiedAt}`)
      console.log(`      depositBalance: ${user.depositBalance}`)
    })

    // 2. 模拟API调用测试
    console.log('\n2. 模拟API调用测试...')
    
    const mediatorUsers = users.filter(user => user.isMediator)
    const nonMediatorUsers = users.filter(user => !user.isMediator)
    
    console.log(`✅ 中间人用户: ${mediatorUsers.length} 个`)
    mediatorUsers.forEach(user => {
      console.log(`   - ${user.name}: ${user.mediatorStatus}`)
    })
    
    console.log(`✅ 非中间人用户: ${nonMediatorUsers.length} 个`)
    nonMediatorUsers.forEach(user => {
      console.log(`   - ${user.name}`)
    })

    // 3. 检查条件渲染逻辑
    console.log('\n3. 检查条件渲染逻辑...')
    
    users.forEach(user => {
      const shouldShowConsole = user.isMediator
      const statusText = user.mediatorStatus === 'ACTIVE' ? '已认证' : '待认证'
      
      console.log(`\n   用户: ${user.name}`)
      console.log(`   isMediator: ${user.isMediator}`)
      console.log(`   应显示控制台: ${shouldShowConsole}`)
      if (shouldShowConsole) {
        console.log(`   状态标签: ${statusText}`)
        console.log(`   统计数据:`)
        console.log(`     - 调解订单: ${user.mediatorTotalOrders || 0}`)
        console.log(`     - 成功率: ${user.mediatorSuccessRate || 0}%`)
        console.log(`     - 信誉值: ${user.mediatorReputation || 0}`)
      }
    })

    // 4. 创建测试用户（如果需要）
    console.log('\n4. 确保有测试用户...')
    
    let testMediatorUser = users.find(user => user.isMediator && user.mediatorStatus === 'ACTIVE')
    let testNormalUser = users.find(user => !user.isMediator)
    
    if (!testMediatorUser) {
      console.log('   创建测试中间人用户...')
      testMediatorUser = await prisma.user.create({
        data: {
          name: '测试中间人用户',
          email: '<EMAIL>',
          isMediator: true,
          mediatorStatus: 'ACTIVE',
          mediatorFeeRate: 0.025,
          mediatorReputation: 95.5,
          mediatorVerifiedAt: new Date(),
          bnbWalletVerified: true,
          mediatorExperience: '具有丰富的交易调解经验',
          mediatorIntroduction: '专业的交易调解员',
          mediatorSuccessRate: 98.5,
          mediatorTotalOrders: 156,
          depositBalance: 10000,
          creditScore: 95,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testMediatorUser.name} (${testMediatorUser.email})`)
    } else {
      console.log(`   ✅ 已有测试中间人: ${testMediatorUser.name}`)
    }
    
    if (!testNormalUser) {
      console.log('   创建测试普通用户...')
      testNormalUser = await prisma.user.create({
        data: {
          name: '测试普通用户',
          email: '<EMAIL>',
          isMediator: false,
          mediatorStatus: 'INACTIVE',
          depositBalance: 1000,
          creditScore: 80,
          status: 'ACTIVE'
        }
      })
      console.log(`   ✅ 创建成功: ${testNormalUser.name} (${testNormalUser.email})`)
    } else {
      console.log(`   ✅ 已有测试普通用户: ${testNormalUser.name}`)
    }

    // 5. 生成测试报告
    console.log('\n5. 生成测试报告...')
    
    console.log('✅ 数据库状态检查:')
    console.log(`   - 总用户数: ${users.length}`)
    console.log(`   - 中间人用户数: ${mediatorUsers.length}`)
    console.log(`   - 活跃中间人数: ${mediatorUsers.filter(u => u.mediatorStatus === 'ACTIVE').length}`)
    console.log(`   - 待审核中间人数: ${mediatorUsers.filter(u => u.mediatorStatus === 'PENDING').length}`)
    
    console.log('\n✅ 前端显示逻辑:')
    console.log('   条件: profile?.isMediator')
    console.log('   说明: 只要 isMediator 为 true 就显示控制台')
    console.log('   状态标签: 根据 mediatorStatus 显示"已认证"或"待认证"')
    
    console.log('\n🎉 调试完成！')
    console.log('\n📝 测试步骤:')
    console.log(`1. 使用中间人账户登录: ${testMediatorUser.email}`)
    console.log('2. 访问个人资料页面: http://localhost:3000/profile')
    console.log('3. 查看是否显示橙色边框的中间人控制台卡片')
    console.log('4. 验证统计信息是否正确显示')
    
    console.log(`\n5. 使用普通用户账户登录: ${testNormalUser.email}`)
    console.log('6. 访问个人资料页面，确认不显示中间人控制台')
    
    console.log('\n💡 如果仍然不显示，请检查:')
    console.log('   - 浏览器控制台是否有JavaScript错误')
    console.log('   - 网络请求是否成功返回用户数据')
    console.log('   - profile 对象是否正确设置')
    console.log('   - 条件渲染逻辑是否正确执行')

    // 6. 输出具体的用户数据用于前端调试
    console.log('\n6. 前端调试数据:')
    console.log('中间人用户数据示例:')
    console.log(JSON.stringify({
      id: testMediatorUser.id,
      name: testMediatorUser.name,
      email: testMediatorUser.email,
      isMediator: testMediatorUser.isMediator,
      mediatorStatus: testMediatorUser.mediatorStatus,
      mediatorReputation: testMediatorUser.mediatorReputation,
      mediatorSuccessRate: testMediatorUser.mediatorSuccessRate,
      mediatorTotalOrders: testMediatorUser.mediatorTotalOrders
    }, null, 2))

  } catch (error) {
    console.error('❌ 调试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行调试
if (require.main === module) {
  debugMediatorProfile().catch(console.error)
}

module.exports = { debugMediatorProfile }
