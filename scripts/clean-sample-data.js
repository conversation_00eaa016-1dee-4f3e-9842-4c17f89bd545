/**
 * 清理示例数据脚本
 * 删除示例商品、订单、需求单等，保留用户账户
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanSampleData() {
  console.log('🧹 清理示例数据...')
  console.log('='.repeat(40))

  try {
    // 1. 删除评价记录
    console.log('\n⭐ 删除评价记录...')
    const reviewsDeleted = await prisma.review.deleteMany()
    console.log(`✅ 删除了 ${reviewsDeleted.count} 条评价记录`)

    // 2. 删除消息记录
    console.log('\n💬 删除消息记录...')
    const messagesDeleted = await prisma.message.deleteMany()
    console.log(`✅ 删除了 ${messagesDeleted.count} 条消息记录`)

    // 3. 删除需求报价
    console.log('\n💰 删除需求报价...')
    const demandOffersDeleted = await prisma.demandOffer.deleteMany()
    console.log(`✅ 删除了 ${demandOffersDeleted.count} 条需求报价`)

    // 4. 删除需求单
    console.log('\n📋 删除需求单...')
    const demandsDeleted = await prisma.demand.deleteMany()
    console.log(`✅ 删除了 ${demandsDeleted.count} 个需求单`)

    // 5. 删除订单项
    console.log('\n📦 删除订单项...')
    const orderItemsDeleted = await prisma.orderItem.deleteMany()
    console.log(`✅ 删除了 ${orderItemsDeleted.count} 个订单项`)

    // 6. 删除订单
    console.log('\n🛒 删除订单...')
    const ordersDeleted = await prisma.order.deleteMany()
    console.log(`✅ 删除了 ${ordersDeleted.count} 个订单`)

    // 7. 删除商品
    console.log('\n🛍️ 删除商品...')
    const productsDeleted = await prisma.product.deleteMany()
    console.log(`✅ 删除了 ${productsDeleted.count} 个商品`)

    // 8. 显示保留的用户账户
    console.log('\n👥 检查保留的用户账户...')
    const users = await prisma.user.findMany({
      select: {
        email: true,
        name: true,
        role: true,
        depositBalance: true,
        creditScore: true
      }
    })

    console.log(`✅ 保留了 ${users.length} 个用户账户:`)
    users.forEach(user => {
      console.log(`   ${user.email} - ${user.name} (${user.role})`)
      console.log(`     保证金: ¥${user.depositBalance}, 信用分: ${user.creditScore}`)
    })

    // 9. 显示清理总结
    console.log('\n📊 清理总结:')
    console.log('='.repeat(50))
    console.log(`✅ 删除的数据:`)
    console.log(`   评价记录: ${reviewsDeleted.count} 条`)
    console.log(`   消息记录: ${messagesDeleted.count} 条`)
    console.log(`   需求报价: ${demandOffersDeleted.count} 条`)
    console.log(`   需求单: ${demandsDeleted.count} 个`)
    console.log(`   订单项: ${orderItemsDeleted.count} 个`)
    console.log(`   订单: ${ordersDeleted.count} 个`)
    console.log(`   商品: ${productsDeleted.count} 个`)
    
    console.log(`\n✅ 保留的数据:`)
    console.log(`   用户账户: ${users.length} 个`)

    console.log('\n🎉 示例数据清理完成!')
    console.log('现在数据库只保留用户账户，可以开始正常使用')

    return {
      deleted: {
        reviews: reviewsDeleted.count,
        messages: messagesDeleted.count,
        demandOffers: demandOffersDeleted.count,
        demands: demandsDeleted.count,
        orderItems: orderItemsDeleted.count,
        orders: ordersDeleted.count,
        products: productsDeleted.count
      },
      preserved: {
        users: users.length
      }
    }

  } catch (error) {
    console.error('❌ 清理数据失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 显示当前数据统计
async function showCurrentData() {
  console.log('📊 当前数据统计:')
  console.log('='.repeat(40))

  try {
    const stats = {
      users: await prisma.user.count(),
      products: await prisma.product.count(),
      orders: await prisma.order.count(),
      demands: await prisma.demand.count(),
      reviews: await prisma.review.count(),
      messages: await prisma.message.count()
    }

    Object.entries(stats).forEach(([table, count]) => {
      const emoji = {
        users: '👥',
        products: '🛍️',
        orders: '🛒',
        demands: '📋',
        reviews: '⭐',
        messages: '💬'
      }[table] || '📊'
      
      console.log(`${emoji} ${table.padEnd(15)}: ${count} 条`)
    })

    return stats

  } catch (error) {
    console.error('❌ 获取统计失败:', error)
    return {}
  }
}

// 主函数
async function main() {
  const command = process.argv[2]

  if (command === 'stats') {
    console.log('📊 数据库统计信息')
    console.log('='.repeat(30))
    await showCurrentData()
    return
  }

  console.log('🧹 BitMarket 示例数据清理工具')
  console.log('='.repeat(50))
  
  try {
    // 显示清理前的数据统计
    console.log('清理前:')
    await showCurrentData()
    
    // 执行清理
    const result = await cleanSampleData()
    
    console.log('\n✨ 数据库已清理完成!')
    console.log('现在可以开始正常使用BitMarket平台')
    
    return result

  } catch (error) {
    console.error('\n💥 清理失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  cleanSampleData,
  showCurrentData
}
