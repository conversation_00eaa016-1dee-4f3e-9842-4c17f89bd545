const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function makeAdmin() {
  try {
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (user) {
      // 更新为管理员
      await prisma.user.update({
        where: { id: user.id },
        data: { role: 'ADMIN' }
      })
      console.log(`✅ 用户 ${user.email} 已更新为管理员`)
    } else {
      console.log('❌ 用户不存在')
    }
  } catch (error) {
    console.error('❌ 更新失败:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

makeAdmin()
