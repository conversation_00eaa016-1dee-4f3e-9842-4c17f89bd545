#!/usr/bin/env node

/**
 * BitMarket 数据清理验证脚本
 * 验证数据是否已被正确清理
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${'='.repeat(50)}`, 'cyan');
  log(`  ${message}`, 'cyan');
  log(`${'='.repeat(50)}`, 'cyan');
}

// 验证数据库清理状态
async function verifyDatabaseClean() {
  logHeader('验证数据库清理状态');
  
  try {
    const tables = [
      'arbitrationVote',
      'escrowDispute', 
      'mediatorReward',
      'withdrawalVoucher',
      'escrowOrder',
      'blockchainTransaction',
      'message',
      'order',
      'product',
      'user'
    ];

    let totalRecords = 0;
    
    for (const table of tables) {
      try {
        const count = await prisma[table].count();
        totalRecords += count;
        
        if (count === 0) {
          log(`✅ ${table}: ${count} 条记录`, 'green');
        } else {
          log(`ℹ️ ${table}: ${count} 条记录`, 'yellow');
        }
      } catch (error) {
        log(`❌ 检查 ${table} 失败: ${error.message}`, 'red');
      }
    }
    
    log(`\n📊 总记录数: ${totalRecords}`, totalRecords === 0 ? 'green' : 'yellow');
    
    if (totalRecords === 0) {
      log('✅ 数据库已完全清理', 'green');
    } else {
      log('ℹ️ 数据库中仍有数据（可能包含管理员账户）', 'yellow');
    }
    
  } catch (error) {
    log(`❌ 数据库验证失败: ${error.message}`, 'red');
  }
}

// 验证文件清理状态
async function verifyFilesClean() {
  logHeader('验证文件清理状态');
  
  const fs = require('fs');
  const path = require('path');
  
  const checkDirs = [
    'public/uploads',
    '.next',
    'test-results',
    'coverage',
    'logs'
  ];
  
  for (const dir of checkDirs) {
    try {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        const fileCount = files.filter(f => f !== '.gitkeep').length;
        
        if (fileCount === 0) {
          log(`✅ ${dir}: 已清理`, 'green');
        } else {
          log(`ℹ️ ${dir}: ${fileCount} 个文件/目录`, 'yellow');
        }
      } else {
        log(`✅ ${dir}: 不存在`, 'green');
      }
    } catch (error) {
      log(`❌ 检查 ${dir} 失败: ${error.message}`, 'red');
    }
  }
}

// 主函数
async function main() {
  log('🔍 BitMarket 数据清理验证工具', 'cyan');
  
  try {
    await verifyDatabaseClean();
    await verifyFilesClean();
    
    logHeader('验证完成');
    log('✅ 数据清理验证完成', 'green');
    
  } catch (error) {
    log(`❌ 验证过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
