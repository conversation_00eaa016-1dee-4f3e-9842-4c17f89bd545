#!/bin/bash

# BitMarket 测试环境设置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js版本
check_node_version() {
    log_info "检查Node.js版本..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装Node.js 18.x或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要18.x或更高版本，当前版本: $(node -v)"
        exit 1
    fi
    
    log_success "Node.js版本检查通过: $(node -v)"
}

# 检查npm版本
check_npm_version() {
    log_info "检查npm版本..."
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "npm版本: $(npm -v)"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 安装测试依赖
install_test_dependencies() {
    log_info "检查测试依赖..."
    
    # 检查是否需要安装额外的测试依赖
    MISSING_DEPS=()
    
    # 检查faker.js
    if ! npm list @faker-js/faker &> /dev/null; then
        MISSING_DEPS+=("@faker-js/faker")
    fi
    
    # 检查测试工具
    if ! npm list @vitest/coverage-v8 &> /dev/null; then
        MISSING_DEPS+=("@vitest/coverage-v8")
    fi
    
    if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
        log_info "安装缺失的测试依赖: ${MISSING_DEPS[*]}"
        npm install --save-dev "${MISSING_DEPS[@]}"
        log_success "测试依赖安装完成"
    else
        log_success "所有测试依赖已安装"
    fi
}

# 创建测试目录结构
create_test_directories() {
    log_info "创建测试目录结构..."
    
    DIRS=(
        "test-results"
        "coverage"
        "test/fixtures"
        "test/mocks"
        "test/snapshots"
    )
    
    for dir in "${DIRS[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        fi
    done
}

# 设置测试数据库
setup_test_database() {
    log_info "设置测试数据库..."
    
    # 创建测试数据库文件
    if [ -f "test.db" ]; then
        rm test.db
        log_info "删除旧的测试数据库"
    fi
    
    # 设置环境变量
    export NODE_ENV=test
    export DATABASE_URL="file:./test.db"
    
    # 运行数据库迁移（如果存在）
    if [ -f "prisma/schema.prisma" ]; then
        log_info "运行数据库迁移..."
        npx prisma migrate dev --name init --skip-generate || true
        npx prisma generate
        log_success "数据库迁移完成"
    fi
}

# 验证测试环境
verify_test_environment() {
    log_info "验证测试环境..."
    
    # 检查vitest配置
    if [ ! -f "vitest.config.ts" ]; then
        log_error "未找到vitest配置文件"
        exit 1
    fi
    
    # 检查测试设置文件
    if [ ! -f "test/setup.ts" ]; then
        log_error "未找到测试设置文件"
        exit 1
    fi
    
    # 运行简单的测试验证
    log_info "运行测试环境验证..."
    npx vitest --run --reporter=basic test/setup.ts || {
        log_warning "测试环境验证失败，但继续执行"
    }
    
    log_success "测试环境验证完成"
}

# 生成测试配置文件
generate_test_config() {
    log_info "生成测试配置文件..."
    
    # 创建Jest配置（如果需要）
    if [ ! -f "jest.config.js" ] && [ "$1" = "--jest" ]; then
        cat > jest.config.js << EOF
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/test/setup.ts'],
  testMatch: [
    '<rootDir>/test/**/*.test.ts',
    '<rootDir>/test/**/*.test.tsx'
  ],
  collectCoverageFrom: [
    'app/**/*.{ts,tsx}',
    'lib/**/*.{ts,tsx}',
    'components/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1'
  }
}
EOF
        log_success "生成Jest配置文件"
    fi
    
    # 创建测试环境变量文件
    if [ ! -f ".env.test" ]; then
        cat > .env.test << EOF
NODE_ENV=test
DATABASE_URL=file:./test.db
NEXTAUTH_SECRET=test-secret
NEXTAUTH_URL=http://localhost:3000
JWT_SECRET=test-jwt-secret
REDIS_URL=redis://localhost:6379/1
EOF
        log_success "生成测试环境变量文件"
    fi
}

# 清理测试环境
cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 删除测试数据库
    if [ -f "test.db" ]; then
        rm test.db
        log_success "删除测试数据库"
    fi
    
    # 清理测试结果
    if [ -d "test-results" ]; then
        rm -rf test-results/*
        log_success "清理测试结果"
    fi
    
    # 清理覆盖率报告
    if [ -d "coverage" ]; then
        rm -rf coverage/*
        log_success "清理覆盖率报告"
    fi
}

# 运行测试
run_tests() {
    local test_type=${1:-"all"}
    
    log_info "运行测试: $test_type"
    
    case $test_type in
        "unit")
            node scripts/test-runner.js unit
            ;;
        "integration")
            node scripts/test-runner.js integration
            ;;
        "e2e")
            node scripts/test-runner.js e2e
            ;;
        "performance")
            node scripts/test-runner.js performance
            ;;
        "all")
            node scripts/test-runner.js all
            ;;
        *)
            log_error "未知的测试类型: $test_type"
            log_info "可用的测试类型: unit, integration, e2e, performance, all"
            exit 1
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "BitMarket 测试环境设置脚本"
    echo ""
    echo "用法: $0 [选项] [测试类型]"
    echo ""
    echo "选项:"
    echo "  --setup          设置测试环境"
    echo "  --cleanup        清理测试环境"
    echo "  --verify         验证测试环境"
    echo "  --jest           生成Jest配置"
    echo "  --help           显示帮助信息"
    echo ""
    echo "测试类型:"
    echo "  unit             单元测试"
    echo "  integration      集成测试"
    echo "  e2e              端到端测试"
    echo "  performance      性能测试"
    echo "  all              所有测试 (默认)"
    echo ""
    echo "示例:"
    echo "  $0 --setup                    # 设置测试环境"
    echo "  $0 unit                       # 运行单元测试"
    echo "  $0 --cleanup                  # 清理测试环境"
}

# 主函数
main() {
    local setup_only=false
    local cleanup_only=false
    local verify_only=false
    local generate_jest=false
    local test_type="all"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --setup)
                setup_only=true
                shift
                ;;
            --cleanup)
                cleanup_only=true
                shift
                ;;
            --verify)
                verify_only=true
                shift
                ;;
            --jest)
                generate_jest=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            unit|integration|e2e|performance|all)
                test_type=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行相应的操作
    if [ "$cleanup_only" = true ]; then
        cleanup_test_environment
        exit 0
    fi
    
    if [ "$verify_only" = true ]; then
        verify_test_environment
        exit 0
    fi
    
    if [ "$setup_only" = true ]; then
        check_node_version
        check_npm_version
        install_dependencies
        install_test_dependencies
        create_test_directories
        setup_test_database
        if [ "$generate_jest" = true ]; then
            generate_test_config --jest
        else
            generate_test_config
        fi
        verify_test_environment
        log_success "测试环境设置完成！"
        exit 0
    fi
    
    # 默认：设置环境并运行测试
    check_node_version
    check_npm_version
    install_dependencies
    install_test_dependencies
    create_test_directories
    setup_test_database
    generate_test_config
    verify_test_environment
    run_tests "$test_type"
}

# 错误处理
trap 'log_error "脚本执行失败，退出码: $?"' ERR

# 运行主函数
main "$@"
